import java.security.MessageDigest

/**
 * 刷新base项目环境到本项目中，方便单独调试
 * 整理本项目资源，方便base项目读取打包
 */
def buildTmpDir = "${buildDir}/tmp"
def srcResourceDir = "src/main/resources"
def webRoot = "${rootDir}/WEB-INF/"
def configPath = "${webRoot}/config"
def resourcePath = "${rootDir}/${srcResourceDir}"
def tmpConfigPath = "${buildTmpDir}/resources/config"

// 生成文件sha256值
static String sha256(File file) {
    def messageDigest = MessageDigest.getInstance("SHA-256")
    FileInputStream fileInputStream = new FileInputStream(file)
    byte[] bytes = new byte[1024]
    int len = 0
    while ((len = fileInputStream.read(bytes)) != -1) {
        messageDigest.update(bytes, 0, len)
    }
    return messageDigest.digest().encodeHex().toString()
}


def downloadResource() {
    // 下载base资源文件
    download.run {
        src 'http://git.koal.com/api/v4/projects/8322/repository/files/src%2Fmain%2Fresources%2FbaseResources.zip/raw?ref=develop'
        dest "${buildDir}/tmp/resources/baseResources.zip"
        header 'PRIVATE-TOKEN', "${accessToken}"
    }
    download.run {
        src 'http://git.koal.com/api/v4/projects/8322/repository/files/src%2Fmain%2Fresources%2FclodResources.zip.sha256/raw?ref=develop'
        dest "${buildDir}/tmp/resources/clodResources.zip.sha256"
        header 'PRIVATE-TOKEN', "${accessToken}"
    }
    def clodResourcesSha256 = new File("${buildDir}/tmp/resources/clodResources.zip.sha256").text

    // 本地clodResources.zip文件不存在或者sha256值不一致则下载
    if ((new File("${buildDir}/tmp/resources/clodResources.zip").exists() && clodResourcesSha256 != sha256(new File("${buildDir}/tmp/resources/clodResources.zip")))
            || !new File("${buildDir}/tmp/resources/clodResources.zip").exists()) {
        download.run {
            src 'http://git.koal.com/api/v4/projects/8322/repository/files/src%2Fmain%2Fresources%2FclodResources.zip/raw?ref=develop'
            dest "${buildDir}/tmp/resources/clodResources.zip"
            header 'PRIVATE-TOKEN', "${accessToken}"
        }
    }
    // 下载application.properties
    download.run {
        src 'http://git.koal.com/api/v4/projects/8322/repository/files/src%2Fmain%2Fresources%2Fapplication.properties/raw?ref=develop'
        dest "${buildDir}/tmp/resources/application.properties"
        header 'PRIVATE-TOKEN', "${accessToken}"
    }

    // 下载KGS系统预置SQL
    download.run {
        src 'http://git.koal.com/api/v4/projects/10528/repository/files/src%2Fmain%2Fresources%2Fkgssql%2Fkm%2Fkgs.sql/raw?ref=develop'
        dest "${buildDir}/tmp/resources/kgssql/data/kgs.sql"
        header 'PRIVATE-TOKEN', "${accessToken}"
    }

    download.run {
        src 'http://git.koal.com/api/v4/projects/10528/repository/files/src%2Fmain%2Fresources%2Fkgssql%2Fdemo%2Fkgs.sql/raw?ref=develop'
        dest "${buildDir}/tmp/resources/kgssql/demo/kgs.sql"
        header 'PRIVATE-TOKEN', "${accessToken}"
    }

}

task cleanProductEnv {
    doLast {
        delete (
                //排除"${buildDir}/tmp/resources/clodResources.zip"
                fileTree(dir: "${buildDir}", exclude: "**/clodResources.zip")
        )
        delete "${configPath}"
        delete "${resourcePath}/sql"
        delete "${resourcePath}/logback-local.xml"
        delete "${resourcePath}/logback-server.xml"
        delete "${resourcePath}/static"
        delete "${resourcePath}/application.properties"
        delete "${rootDir}/db"
        delete "${rootDir}/keystore"
        delete "${rootDir}/logs"
        delete "${rootDir}/out"
        delete "${rootDir}/config"
        delete "${webRoot}"
    }
}

task buildProductEnv(dependsOn: [cleanProductEnv, jar, refreshStaticResource]) {
    doLast {
        // 下载要base的资源
        downloadResource()
        // 解压下载的产品
        fileTree(dir: "${buildTmpDir}/resources", includes: ['**/*.zip']).each {
            zipFile ->
                copy {
                    from zipTree(zipFile)
                    into buildTmpDir + "/resources"
                }
        }

        // classes路径
        def classesPath = "${buildDir}/classes/java/main"
        mkdir "${classesPath}/public/pkiclient"

        // 拷贝pkiclient到resources目录
        copy {
            from("${buildTmpDir}/resources/public/pkiclient")
            include "PKIClient*"
            into "${classesPath}/public/pkiclient"
        }

        // 拷贝logback等配置文件
        copy {
            from("${buildTmpDir}/resources/config")
            include "logback*", "jmx.yaml"
            into "${srcResourceDir}"
        }

        // 初始化开发环境（配置文件、数据库）
        // 复制产品的properties到统一目录中统一处理
        copy {
            from "${srcResourceDir}"
            include "npki-km.properties", "sharding-km.properties", "application-preset-km.properties"
            into "${buildTmpDir}/resources/config"
        }

        // 复制产品sql到统一目录中处理
        copy {
            from "${srcResourceDir}/kmsql"
            into "${buildTmpDir}/resources/sql"
        }

        // 初始化config目录，兼容打包目录
        delete fileTree(configPath)
        mkdir(configPath)

        // 读取各产品sharding配置，整合到application.properties中
        javaexec {
            classpath sourceSets.main.runtimeClasspath
            main "kl.npki.base.management.utils.PkiPropertiesBuildUtil"
            def argArr = [tmpConfigPath, "${buildDir}/tmp/resources/", configPath] as String[]
            args argArr
        }

        // 读取各产品数据库脚本，整合数据库脚本
        javaexec {
            classpath sourceSets.main.runtimeClasspath
            main "kl.npki.base.management.utils.PkiSqlBuildUtil"
            def tmpSqlPath = "${buildTmpDir}/resources/sql"
            def argArr = [tmpSqlPath, webRoot] as String[]
            args argArr
        }

        // 读取KGS数据库脚本，整合数据库脚本
        javaexec {
            classpath sourceSets.main.runtimeClasspath
            main "kl.ibc.kgs.management.utils.KgsSqlBuildUtil"
            def tmpSqlPath = "${buildTmpDir}/resources/kgssql"
            def argArr = [tmpSqlPath, webRoot] as String[]
            args argArr
        }

        // 初始化demo预置数据，包括配置文件npki-demo.properties和sql文件npki-demo.sql，并生成db文件
        javaexec {
            classpath sourceSets.main.runtimeClasspath
            main "kl.npki.base.management.utils.PkiDemoBuildUtil"
            def rootPath = "${rootDir}"
            // RSA_2048表示默认构建的站点证书算法
            def argArr = [rootPath, configPath, tmpConfigPath, "RSA_2048"] as String[]
            args argArr
        }

        // 修改config/npki-demo.properties中的kl.km.key.keyLimits[\d+].limit为100，减少CPU占用
        def demoProperties = new File("${configPath}/npki-demo.properties")
        def demoPropertiesContent = demoProperties.text
        demoPropertiesContent = demoPropertiesContent.replaceAll(/(kl.km.key.keyLimits\[\d+].limit=)(\d+)/, '$1100')
        demoProperties.write(demoPropertiesContent)

        // 将配置文件复制到config目录
        copy {
            from "${srcResourceDir}"
            include "logback*"
            include "bootstrap.yml"
            include "sql"
            into configPath
        }

    }
}



