import java.security.MessageDigest

//def resourcesDir = "${buildDir}/classes/java/main"
def buildTmpDir = "${buildDir}/tmp"
def srcResourceDir = "src/main/resources"
def configPath = "WEB-INF/config"
def resourcePath = "${rootDir}/${srcResourceDir}"

// 生成文件sha256值
static String sha256(File file) {
    def messageDigest = MessageDigest.getInstance("SHA-256")
    FileInputStream fileInputStream = new FileInputStream(file)
    byte[] bytes = new byte[1024]
    int len = 0
    while ((len = fileInputStream.read(bytes)) != -1) {
        messageDigest.update(bytes, 0, len)
    }
    return messageDigest.digest().encodeHex().toString()
}

// 打包base项目配置资源，供其他子系统拉取
task zipResources(type: Zip) {
    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)
    from ("${srcResourceDir}/basesql") {
        into "sql/"
    }
    from ("${srcResourceDir}/java-agent") {
        into "java-agent/"
    }
    from ("${srcResourceDir}") {
        include "npki-base.properties"
        include "logback-server.xml"
        include "jmx.yaml"
        into "config/"
    }
    archiveFileName = "baseResources.zip"
    destinationDirectory = file("${srcResourceDir}")

    doLast {
        // 生成checksum文件,用于校验
        def checksumFile = file("${srcResourceDir}/${archiveName}.sha256")

        // 生成文件sha256值
        checksumFile.text = sha256(file("${srcResourceDir}/${archiveName}"))
    }
}

// 打包base项目大的、冷的数据，不与配置资源一起打包，减小git仓库体积
task zipClodResources(type: Zip) {
    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)
    from ("${srcResourceDir}/public") {
        into "public/"
    }
    archiveFileName = "clodResources.zip"
    destinationDirectory = file("${srcResourceDir}")

    doLast {
        // 生成checksum文件,用于校验
        def checksumFile = file("${srcResourceDir}/${archiveName}.sha256")

        // 生成文件sha256值
        checksumFile.text = sha256(file("${srcResourceDir}/${archiveName}"))
    }
}

task cleanProductEnv {
    doLast {
        delete "${buildDir}"
        delete "${configPath}"
        delete "${resourcePath}/sql"
        delete "${resourcePath}/static"
        delete "${resourcePath}/public/helper"
        // 删除*.jks文件
        fileTree(dir: "${resourcePath}", includes: ['*.jks']).each {
            file ->
                delete file
        }

        delete "${rootDir}/db"
        delete "${rootDir}/keystore"
        delete "${rootDir}/logs"
    }
}

// 构建产品开发/打包环境
task buildProductEnv(dependsOn: [cleanProductEnv, jar, refreshStaticResource]) {
    doLast {
        // 初始化开发环境（配置文件、数据库）
        // 复制npki-base.properties到统一目录中统一处理
        copy {
            from "${srcResourceDir}"
            include "npki-base.properties"
            into "${buildTmpDir}/resources/config"
        }
        // 复制basesql到统一目录中处理
        copy {
            from "${srcResourceDir}/basesql"
            into "${buildTmpDir}/resources/sql"
        }

        // 复制jks到resources目录
        copy {
            from("${buildTmpDir}/resources/config/")
            include "*.jks"
            into "${srcResourceDir}"
        }
        // 复制公共文件内容
        copy {
            from("${buildTmpDir}/resources/public/")
            into "${srcResourceDir}/public/"
        }

        // 初始化config目录，兼容打包目录
        delete fileTree(configPath)
        mkdir(configPath)

        // 复制日志配置
        copy {
            from("${srcResourceDir}")
            include("logback*")
            into "${configPath}"
        }
        // 读取各产品sharding配置，整合到application.properties中
        javaexec {
            classpath sourceSets.main.runtimeClasspath
            main "kl.npki.base.management.utils.PkiPropertiesBuildUtil"
            def tmpConfigPath = "${buildTmpDir}/resources/config"
            def argArr = [tmpConfigPath, resourcePath, configPath] as String[]
            args argArr
        }

        // 读取各产品数据库脚本，整合数据库脚本
        javaexec {
            classpath sourceSets.main.runtimeClasspath
            main "kl.npki.base.management.utils.PkiSqlBuildUtil"
            def tmpSqlPath = "${buildTmpDir}/resources/sql"
            def argArr = [tmpSqlPath, resourcePath] as String[]
            args argArr
        }

        // 初始化demo预置数据，包括配置文件npki-demo.properties和sql文件npki-demo.sql，并生成db文件
        javaexec {
            classpath sourceSets.main.runtimeClasspath
            main "kl.npki.base.management.utils.PkiDemoBuildUtil"
            def rootPath = "${rootDir}"
            def argArr = [rootPath, configPath] as String[]
            args argArr
        }

        // 将配置文件复制到config目录
        copy {
            from "${buildTmpDir}/resources/config/"
            include "logback*"
            into configPath
        }
    }

}


