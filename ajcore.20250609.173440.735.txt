---- AspectJ Properties ---
<PERSON>pectJ Compiler 1.9.6 built on Tuesday Jul 21, 2020 at 13:30:08 PDT
---- Dump Properties ---
Dump file: ajcore.20250609.173440.735.txt
Dump reason: org.aspectj.weaver.BCException
Dump on exception: true
Dump at exit condition: abort
---- Exception Information ---
---- System Properties ---
java.runtime.name=OpenJDK Runtime Environment
sun.boot.library.path=D:\Java\openjdk-8u392-b08-windows-64\jre\bin
java.vm.version=25.392-b08
java.vm.vendor=Oracle Corporation
java.vendor.url=http://java.oracle.com/
path.separator=;
java.vm.name=OpenJDK 64-Bit Server VM
file.encoding.pkg=sun.io
user.script=
sun.java.launcher=SUN_STANDARD
user.country=CN
sun.os.patch.level=
java.vm.specification.name=Java Virtual Machine Specification
user.dir=D:\IdeaProjects\npki\npki-km-management
java.runtime.version=1.8.0_392-392-b08
java.awt.graphicsenv=sun.awt.Win32GraphicsEnvironment
java.endorsed.dirs=D:\Java\openjdk-8u392-b08-windows-64\jre\lib\endorsed
os.arch=amd64
java.io.tmpdir=C:\Users\<USER>\AppData\Local\Temp\
line.separator=

java.vm.specification.vendor=Oracle Corporation
user.variant=
os.name=Windows 11
sun.jnu.encoding=GBK
java.library.path=D:\Java\openjdk-8u392-b08-windows-64\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;D:\app\Administrator\product\11.2.0\dbhome_2\bin;D:\Java\jdk1.8\bin;D:\Java\jdk1.8\jre\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;D:\app\Administrator\product\11.2.0\dbhome_1\BIN;D:\apache-tomcat-9.0.104\bin;D:\apache-tomcat-9.0.104\lib;D:\Program Files\Git\cmd;D:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;D:\Program Files\JetBrains\IntelliJ IDEA 2025.1.1.1\bin;;D:\Program Files (x86)\Nmap;D:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2022.2.2\bin;;C:\Users\<USER>\AppData\Roaming\npm;.
java.specification.name=Java Platform API Specification
java.class.version=52.0
sun.management.compiler=HotSpot 64-Bit Tiered Compilers
os.version=10.0
user.home=C:\Users\<USER>\Users\Administrator\.gradle\caches\modules-2\files-2.1\org.aspectj\aspectjtools\1.9.6\f943d46e5995ad91b733777d27d545d10e5107ba\aspectjtools-1.9.6.jar
user.name=Administrator
java.vm.specification.version=1.8
sun.java.command=org.aspectj.tools.ajc.Main -argfile D:\IdeaProjects\npki\npki-km-management\build\tmp\compileJava\ajc.options
java.home=D:\Java\openjdk-8u392-b08-windows-64\jre
sun.arch.data.model=64
user.language=zh
java.specification.vendor=Oracle Corporation
awt.toolkit=sun.awt.windows.WToolkit
java.vm.info=mixed mode
java.version=1.8.0_392-392
java.ext.dirs=D:\Java\openjdk-8u392-b08-windows-64\jre\lib\ext;C:\WINDOWS\Sun\Java\lib\ext
sun.boot.class.path=D:\Java\openjdk-8u392-b08-windows-64\jre\lib\resources.jar;D:\Java\openjdk-8u392-b08-windows-64\jre\lib\rt.jar;D:\Java\openjdk-8u392-b08-windows-64\jre\lib\sunrsasign.jar;D:\Java\openjdk-8u392-b08-windows-64\jre\lib\jsse.jar;D:\Java\openjdk-8u392-b08-windows-64\jre\lib\jce.jar;D:\Java\openjdk-8u392-b08-windows-64\jre\lib\charsets.jar;D:\Java\openjdk-8u392-b08-windows-64\jre\lib\jfr.jar;D:\Java\openjdk-8u392-b08-windows-64\jre\classes
java.vendor=OpenLogic-OpenJDK
java.specification.maintenance.version=5
file.separator=\
java.vendor.url.bug=http://bugreport.sun.com/bugreport/
sun.io.unicode.encoding=UnicodeLittle
sun.cpu.endian=little
sun.desktop=windows
sun.cpu.isalist=amd64
---- Command Line ---
-argfile
D:\IdeaProjects\npki\npki-km-management\build\tmp\compileJava\ajc.options
---- Full Classpath ---
Empty
---- Compiler Messages ---
warning at <Unknown>::0 Found @DeclareAnnotation while current release does not support it (see 'org.aspectj.weaver.bcel.AtAjAttributes')
abort ABORT -- (BCException) malformed class file
when batch building BuildConfig[null] #Files=0 AopXmls=#0

malformed class file
when batch building BuildConfig[null] #Files=0 AopXmls=#0

org.aspectj.weaver.BCException: malformed class file
when batch building BuildConfig[null] #Files=0 AopXmls=#0

	at org.aspectj.weaver.bcel.Utility.makeJavaClass(Utility.java:476)
	at org.aspectj.weaver.bcel.UnwovenClassFile.getJavaClass(UnwovenClassFile.java:76)
	at org.aspectj.weaver.bcel.BcelWeaver.addClassFile(BcelWeaver.java:455)
	at org.aspectj.weaver.bcel.BcelWeaver.addClassFile(BcelWeaver.java:479)
	at org.aspectj.ajdt.internal.core.builder.AjBuildManager.initBcelWorld(AjBuildManager.java:908)
	at org.aspectj.ajdt.internal.core.builder.AjBuildManager.performBuild(AjBuildManager.java:253)
	at org.aspectj.ajdt.internal.core.builder.AjBuildManager.batchBuild(AjBuildManager.java:189)
	at org.aspectj.ajdt.ajc.AjdtCommand.doCommand(AjdtCommand.java:114)
	at org.aspectj.ajdt.ajc.AjdtCommand.runCommand(AjdtCommand.java:60)
	at org.aspectj.tools.ajc.Main.run(Main.java:371)
	at org.aspectj.tools.ajc.Main.runMain(Main.java:248)
	at org.aspectj.tools.ajc.Main.main(Main.java:84)

