# 需要先在宿主机执行gradle signedZip命令

#unpacks zipped file
FROM harbor.koal.com/hub/library/busybox:stable AS unpack
WORKDIR /unpack
COPY build/distributions/*.zip /
RUN rm -rf /*_unsigned.zip
RUN unzip /*.zip

FROM harbor.koal.com/java/java-builder/eclipse-temurin:8-jdk-license
COPY --from=unpack /unpack/SYT0901-KM/ /app/
ENV TZ=PRC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
# 定义工作目录
WORKDIR /app
# 输出当前目录下所有文件
RUN ls -al
# 赋权
RUN chmod 500 service.sh
# 修改配置文件/app/WEB-INF/config/application.properties中的配置kl.base.license.specifiedDeployEnv=*为kl.base.license.specifiedDeployEnv=SKS_GW
RUN sed -i 's/kl.base.license.specifiedDeployEnv=.*/kl.base.license.specifiedDeployEnv=SKS_GW/g' /app/WEB-INF/config/application.properties
# 声明需要暴露的端口
EXPOSE 11080 18800 18880 11443
# 配置容器启动后执行的命令tail
ENTRYPOINT ["/bin/sh", "-c", "./service.sh start && tail -f /dev/null"]