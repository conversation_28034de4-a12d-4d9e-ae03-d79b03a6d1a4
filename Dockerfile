# 需要先在宿主机执行gradle signZip命令

#unpacks zipped file
FROM harbor.koal.com/hub/library/busybox:stable AS unpack
WORKDIR /unpack
COPY build/distributions/*signed.zip /
RUN unzip /*signed.zip

FROM harbor.koal.com/hub/adoptopenjdk/openjdk8:jre8u392-b08
COPY --from=unpack /unpack/BASE-MGMT/ /app/
ENV TZ=PRC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
# 定义工作目录
WORKDIR /app
# 输出当前目录下所有文件
RUN ls -al
# 赋权
RUN chmod 500 service.sh
# 声明需要暴露的端口
EXPOSE 8080
# 配置容器启动后执行的命令tail
ENTRYPOINT ["/bin/sh", "-c", "./service.sh start && tail -f /dev/null"]
