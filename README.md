# npki-base-management

## 使用与维护

### 释放环境

- 调出gradle面板，双击 **buildProductEnv**
  - 或者在终端命令行执行 `gradle buildProductEnv`

### 运行工程

- 运行: `kl.npki.BaseManagementApplication#main()`
- 访问: [http://localhost:8085/mgmt/v1](http://localhost:8085/mgmt/v1)

### 资源文件更新

- sql脚本、配置文件等资源文件更新后，需要运行`gradle zipResource` 命令，会将资源文件打包成`xxxResources.zip`到[src/main/resources](src/main/resources)目录下，提交时将其一起提交到gitlab

### 打包工程

- 执行`gradle zip`，将工程打包成`xxx.zip`，上传到服务器，解压后运行`sh service.sh start`即可

## 开发手册

### 数据库数据完整性保护

#### 功能介绍
- 为了保证数据库数据不被窜改，增加了对应的完整性保护功能
- 完整性配置开关: kl.base.sys.openDataFull=true
#### 功能约束
- 在处理涉及完整性保护字段的对象时（无论是 Entity 实体还是 Data Object (DO)），必须确保对象字段与数据库记录之间保持严格的一致性。这种一致性包括字段名称、数据类型、精度和格式的完全匹配。特别是对于用于完整性校验的字段，任何不一致都可能导致数据完整性验证失败。
- 对于数据库默认值的处理，需开发者在对象模型中显式定义与数据库默认值相同的默认值，确保内存中的对象状态与数据库一致
#### 使用步骤: 
- 1.以管理员信息表为例，如果需要对管理员信息表增加完整性保护功能，请按如下步骤完成
- 2.管理员表DO对象中，在需要保护的字段上增加 @DataFullField 注解 （具体可查看TAdminInfoDO类。）
```
@DataFullField 作用: 完整性原文由被注解标识的字段值拼接而成
```
- 3.管理员信息表增加 'FULL_DATA_HASH' 字段, 如: TAdminInfoDO类：
```
// 完整性值
@TableField(value = "FULL_DATA_HASH", fill = FieldFill.INSERT_UPDATE)
@KlDbField(type = DataType.VARCHAR, size = 1024)
private String fullDataHash;
```
- 4.完整性值的新增 
  - 向管理员信息表插入数据时，拦截器可自动填充完整性值。具体原理查看 kl.npki.base.repository.interceptor.DataProtectAutoFillInterceptor 类.
- 5.完整性值的更新 （重点注意，使用不当会造成完整性原文不正确）
  - 管理员信息表更新数据时，也会自动重新计算完整性值，进行完整性更新
  - 注意，由于 更新数据时是通过拦截器实现，所以更新的对象必须是包含所有完整性字段值的对象，建议使用先查后更新的方式，如下:
  ```
  AdminEntity adminEntity = adminInfoRepository.getById(adminInfoId);
  adminEntity.setPasswordHash("xxxxxx")
  adminCertEntity.update();
  ```


- 6.完整性的验证
  - 系统不会自动验证数据库数据的完整性，需要开发人员自行确定校验时机
  - BaseRepository 接口已增强了完整性校验功能，方便了开发人员在数据访问层统一处理数据完整性，推荐直接在持久层进行校验。

- 6.1 持久层校验（推荐）

在继承 `BaseRepository` 的仓储实现类中，可以直接使用内置的校验方法：

```java
public interface IAdminInfoRepository extends BaseRepository {
    // any method
}
public class AdminInfoRepositoryImpl extends KlServiceImpl<TAdminInfoMapper, TAdminInfoDO> implements IAdminInfoRepository {
    
    public TAdminInfoDO getById(String adminId) {
      // 从数据库获取数据
      TAdminInfoDO infoDO = super.getById(id);
      CheckUtils.notNull(infoDO, ManagementInternalError.DB_QUERY_ERROR.toException("数据为空"));
      // 进行完整性校验
      verifyDataIntegrity(adminInfoDO);
      return convertService.convert(infoDO, AdminEntity.class);
    }
}
```

这种方式无需额外操作，直接利用了 `BaseRepository` 提供的默认实现。

- 6.2 业务层校验（需要额外处理）

如果业务层需要进行完整性校验，特别是当使用 Entity 对象而非 DO 对象时，需要在业务层重写校验方法：

1. 在业务层实现类中重写校验方法：

```java
public class AdminServiceImpl implements BaseRepository {

  @Autowired
  private ConvertService convertService;

  @Override
  public void verifyDataIntegrity(Object dataObj) {
    if (dataObj instanceof AdminEntity) {
      dataObj = convertService.convert(dataObj, TAdminInfoDO.class);
    }
    super.verifyDataIntegrity(dataObj);
  }

  @Override
  public boolean checkDataIntegrity(Object dataObj) {
    if (dataObj instanceof AdminEntity) {
      dataObj = convertService.convert(dataObj, TAdminInfoDO.class);
    }
    return super.checkDataIntegrity(dataObj);
  }
}
```

2. 在业务逻辑中使用：

- 使用 verifyDataIntegrity 方法（抛出异常）：
```java
public void someBusinessMethod(AdminEntity adminEntity) {
  try {
    RepositoryFactory.get(IAdminInfoRepository.class).verifyDataIntegrity(adminEntity);
    // 校验通过，继续处理
  } catch (BaseException e) {
    // 校验失败，处理异常
    LOGGER.error("管理员信息完整性校验失败 - ID: {}, 错误: {}", adminEntity.getId(), e.getMessage());
    // 根据业务需求进行后续处理
  }
}
```

- 使用 checkDataIntegrity 方法（返回布尔值）：
```java
public boolean someBusinessMethod(AdminEntity adminEntity) {
  boolean checkResult = RepositoryFactory.get(IAdminInfoRepository.class).checkDataIntegrity(adminEntity);
  if (checkResult) {
    // 校验通过，继续处理
    return true;
  } else {
    // 校验失败，进行相应处理
    LOGGER.warn("管理员信息完整性校验失败 - ID: {}", adminEntity.getId());
    // 根据业务需求进行后续处理
    return false;
  }
}
```

- 注意事项：
  - 完整性校验默认约束的输入对象应是DO对象实例（因为生成完整性数据设计是在DO对象上），如果需要支持Entity实体输入，需重写校验方法，在实现类将Entity实体转换为DO对象实例。
  - 完整性校验仅在系统配置开启（`kl.base.sys.openDataFull=true`）且处于部署环境时执行。
  - 确保 DO 对象正确实现了 `getFullDataHash` 方法。
  - 如使用自定义完整性原文生成逻辑，请正确配置 DO 类上的 `@DataProtect` 注解。
  - 校验失败时，`verifyDataIntegrity` 方法会抛出 `BaseValidationError.INTEGRITY_VERIFICATION_FAILED` 异常。
  - 使用 DEBUG 日志级别可查看详细校验信息。
  - 针对MYSQL、KINGBASE数据库时间字段的四舍五入问题，目前解决方案是在字段上使用注解@KlDbField(type = DataType.DATE, defaultValue = "CURRENT_TIMESTAMP", size = 6)，将时间精度调整为6位小数（微秒级别），以避免时间值被四舍五入。由于绝大多数数据库支持的时间精度均可达到微秒级，6位小数的精度足够满足实际需求，确保时间数据的准确性和完整性。
  - 完整性校验失败的排查重点应放在完整性原文的正确性上。大多数完整性校验失败的根本原因是完整性原文数据不正确或不一致，因此建议优先从完整性原文的生成和配置入手进行排查，确保其准确无误，从而有效避免校验失败。



- 补充: 完整性原文自定义生成
  - 在DO类上增加@DataProtect注解,以TRoleDO为例子
  ```
   @DataProtect(genOriDataClass = RoleCustomGenOriDataServiceImpl.class)
  ```
  - RoleCustomGenOriDataServiceImpl为自定义生成完整性原文的类
  - 注意: 验证时，不能再使用第6点中的完整性原文获取方式，请使用对应自定义类去获取完整性原文。