# npki-base-service

## 组件引用
```groovy
    implementation "kl.npki:npki-base-service:3.1.0-SNAPSHOT"
```
##  监控认证配置说明

### 功能说明
对监控接口(/metrics/*)进行基础认证保护，通过配置可以灵活开启或关闭认证功能。

### 认证接口范围
所有 `/metrics/*` 路径下的接口都会进行认证拦截，例如：
- /metrics/health
- /metrics/info
- /metrics/prometheus
  等

### 配置项

| 配置项 | 说明 | 默认值 | 必填 |
|-------|------|--------|-----|
| kl.base.metrics.auth.enabled | 是否启用监控认证 | false | 否 |
| kl.base.auth.user.name | 认证用户名 | - | 是(启用认证时) |
| kl.base.auth.user.password | 认证密码 | - | 是(启用认证时) |

### 使用说明

1. 启用监控认证（使用默认配置）
```yaml
# 只开启监控认证
kl.base.metrics.auth.enabled=true
# 不配置用户名密码则使用默认值：
# - 默认用户名：user
# - 默认密码：启动时自动生成，在启动日志中查看
# 日志格式如下：
# 基本认证使用生成的安全密码：xxxxxxxx
# 此生成的密码仅供开发使用。在将应用程序部署到生产环境之前，必须更新您的安全配置。
```

2. 启用监控认证（显式配置）
```yaml
# 开启监控认证
kl.base.metrics.auth.enabled=true
# 设置认证用户信息
kl.base.auth.user.name=admin
kl.base.auth.user.password=password123
```