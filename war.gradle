import java.nio.file.Files
import java.security.MessageDigest

def dateStr = new Date().format('yyyyMMdd')
def warStdArchiveFileName = "${packageName}-${project.version}-${dateStr}.war"
def warIcbcArchiveFileName = "${packageName}-6.8.9.1-for-ICBC-${dateStr}.war"
ext.cleanedLibsDir = "$buildDir/tmp/cleanedLibs"
ext.icbcWarType = 'icbc'
ext.stdWarType = 'standard'
// 默认标准版本
ext.warType = ext.stdWarType

// 计算并打印 SHA-256 哈希值
static def calculateSha256(File file) {
    MessageDigest digest = MessageDigest.getInstance("SHA-256")
    byte[] fileBytes = Files.readAllBytes(file.toPath())
    byte[] hashBytes = digest.digest(fileBytes)
    return hashBytes.collect { String.format("%02x", it) }.join()
}

// 把清理依赖的逻辑写成普通方法，传入warType判断排除
def cleanJarDependenciesAction(String warType) {
    println ">>> 当前 warType = ${warType}"
    file(ext.cleanedLibsDir).mkdirs()

    def excludedPrefixes = [
            "jetty",
            "spring-boot-starter-jetty",
            "websocket",
            "javax-websocket",
            "tongweb"
    ]

    // 标准版本排除npki-base-sso-icbc相关依赖
    if (warType != project.ext.icbcWarType) {
        println("标准版本排除npki-base-sso-icbc相关依赖")
        excludedPrefixes.add('npki-base-sso-icbc')
    }

    def isExcluded = { String name ->
        excludedPrefixes.any { prefix -> name.startsWith(prefix) }
    }

    configurations.runtimeClasspath.files.each { jarFile ->
        def jarName = jarFile.name
        if (isExcluded(jarName)) return

        def destFile = new File(cleanedLibsDir, jarName)

        // 这里演示特殊处理 h2 和 jackson 相关jar
        if (jarName.contains("h2") || jarName.contains("jackson")) {
            def tempDir = new File("$buildDir/tmp/unpack/${jarName.replaceAll(/\.jar$/, "")}")
            delete tempDir
            tempDir.mkdirs()

            copy {
                from zipTree(jarFile)
                into tempDir
                exclude 'META-INF/versions/**'
            }

            ant.zip(destfile: destFile) {
                fileset(dir: tempDir)
            }
        } else {
            copy {
                from jarFile
                into cleanedLibsDir
            }
        }
    }
}

// 注册两个具体清理任务，分别传入对应warType
tasks.register('cleanJarDependenciesStandard') {
    doLast {
        cleanJarDependenciesAction(project.ext.stdWarType)
    }
}

tasks.register('cleanJarDependenciesIcbc') {
    doLast {
        cleanJarDependenciesAction(project.ext.icbcWarType)
    }
}

/**
 * ICBC配置处理，将npki-base-sso-icbc-xxx.jar包中的ssic.properties文件拷贝到WEB-INF/config目录下
 */
def processSsicPropertiesIntoWar = {
    def icbcJars = configurations.runtimeClasspath.findAll {
        it.name.startsWith("npki-base-sso-icbc-") && it.name.endsWith(".jar")
    }
    if (icbcJars.isEmpty()) {
        throw new GradleException("未找到匹配的 npki-base-sso-icbc-*.jar，请检查 dependencies 是否正确")
    }

    def jarFile = icbcJars.first()
    def ssicFile = zipTree(jarFile).matching { include 'ssic.properties' }.singleFile

    copy {
        from ssicFile
        into("WEB-INF/config/")
    }
}

def processApplicationPropertiesIntoWar = {
    def appPropsFile = file("WEB-INF/config/application.properties")
    if (!appPropsFile.exists()) {
        throw new GradleException("未找到 WEB-INF/config/application.properties 文件")
    }
    def content = appPropsFile.getText("UTF-8")

    if (content.contains("kl.base.sysInfo.passwdSeparateDb=")) {
        content = content.replaceFirst(/(?m)^kl\.base\.sysInfo\.passwdSeparateDb=.*/, "kl.base.sysInfo.passwdSeparateDb=true")
        println "打开人机分离开关：kl.base.sysInfo.passwdSeparateDb=true"
    }

    def outputPropsFile = file("WEB-INF/config/application.properties")
    outputPropsFile.parentFile.mkdirs()
    outputPropsFile.text = content
}

def buildWarTask = { String taskName, String warType, String archiveFileName ->
    tasks.register(taskName, War) {
        group = "build"
        description = "打包 ${warType} 版本的 WAR"

        dependsOn buildProductEnv
        // 根据 warType 依赖对应的clean任务
        if (warType == project.ext.icbcWarType) {
            dependsOn 'cleanJarDependenciesIcbc'
        } else {
            dependsOn 'cleanJarDependenciesStandard'
        }

        // 清理默认 classpath，避免自动依赖冲突
        doFirst {
            classpath = files()
            classpath jar.archiveFile.get().asFile
            file(cleanedLibsDir).eachFileMatch(~/.+\.jar/) { jar ->
                classpath file(jar)
            }
        }

        setArchiveFileName(archiveFileName)

        into('static') {
            from('src/main/resources/static')
        }
        into('public') {
            from("${buildDir}/tmp/resources/public")
        }
        into('WEB-INF') {
            from 'WEB-INF'
        }

        if (warType == project.ext.icbcWarType) {
            doFirst {
                println ">>> 注入ssic.properties配置和打开人机分离开关"
                processSsicPropertiesIntoWar()
                processApplicationPropertiesIntoWar()
            }
        }

        // 拷贝
        doLast {
            copy {
                from archiveFile.get().asFile.parent
                include "*.war"
                into "${buildDir}/distributions/"
            }

            def hashValue = calculateSha256(archiveFile.get().asFile)
            println "WAR包的名称【未签名】: ${archiveFile.get().asFile.name}"
            println "WAR包的路径【未签名】: ${buildDir}/distributions/${archiveFile.get().asFile.name}"
            println "WAR包的 SHA-256 哈希值【未签名】: ${hashValue}"
        }
    }
}

tasks.named("war") {
    group = "build"
    description = "兼容默认 war 任务，实际执行 warStandard"
    dependsOn.clear()
    dependsOn "warStandard"
    doLast {
        println ">>> 默认 war 被重定向执行 warStandard 任务"
    }
}

// 标准版本WAR包
buildWarTask("warStandard", stdWarType, warStdArchiveFileName)

// ICBC版本WAR包
buildWarTask("warIcbc", icbcWarType, warIcbcArchiveFileName)