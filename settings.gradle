pluginManagement {
    repositories {
        mavenLocal()
        maven {
            url "http://nexus3.koal.com:8081/repository/maven-public/"
            allowInsecureProtocol = true
            metadataSources {
                mavenPom()
                artifact()
            }
        }
        mavenCentral()
    }
}

rootProject.name = 'npki-km-management'
includeBuild '../npki-km-core'
includeBuild '../npki-km-service'
includeBuild '../npki-base-core'
includeBuild '../npki-base-management-core'
includeBuild '../npki-base-management'
includeBuild '../npki-base-service'
//includeBuild '../common-base-timer'
includeBuild '../common-base-auth'
includeBuild '../../cbb/common-base-db'
