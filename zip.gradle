import java.security.MessageDigest
import java.util.regex.Pattern
import java.nio.file.Files
import java.nio.file.Paths
import java.nio.file.StandardOpenOption
// 打包产品
def preZipDir = "${buildDir}/preZip/"
def vmOptionsFilePath=preZipDir + "/service.vmoptions"
def projectDir = project.getProjectDir().getPath()
def standardZipType = 'standard'
def kcspZipType = 'kcsp'
// 在gradle.properties或通过-D参数设置
// 打包类型，默认为standard，可设置为kcsp
project.ext.zipType = project.hasProperty('zipType') ? project.zipType : standardZipType

task copyJarResources() {
    dependsOn buildProductEnv
    doLast {
        switch (project.zipType){
            case standardZipType:
                doStandardPreZip(preZipDir)
                break
            case kcspZipType:
                doKcspPreZip(preZipDir)
                break
        }
    }
}

// 预打包标准产品部署包
private void doStandardPreZip(preZipDir) {

    // 清除现有的lib目录
    delete {
        "$preZipDir\\lib"
    }

    // 清除配置文件
    delete {
        "$preZipDir\\config"
    }

    copy {
        from configurations.runtimeClasspath
        into "$preZipDir/WEB-INF/lib"
        exclude { fileTreeElement ->
            fileTreeElement.file.name.startsWith("npki-base-sso-icbc-")
        }
    }
    copy {
        from("$buildDir/libs")
        into "$preZipDir\\WEB-INF\\lib"
    }

    copy {
        from("src/main/resources/static")
        into "$preZipDir\\static"
    }

    copy {
        from("${buildDir}/classes/java/main/public/")
        into "$preZipDir\\public"
    }

    copy {
        from("WEB-INF")
        into "$preZipDir\\WEB-INF\\"
    }

    download.run {
        src "http://nexus3.koal.com:8081/repository/maven-releases/kl/pki/dpg/dpg-verifier/${dpgVerifierVersion}/dpg-verifier-${dpgVerifierVersion}.jar"
        dest "$preZipDir/dpg-verifier.jar"
    }
}

// 预打包kcsp产品部署包
private void doKcspPreZip(preZipDir) {
    copy {
        from('src/main/resources/kcsp')
        include ".env", "docker-compose.yaml", "install-list.xlsx"
        into "$preZipDir"
    }

    // 清除配置文件
    delete {
        "$preZipDir\\config"
    }
    // 复制配置文件
    copy {
        from("config")
        into "$preZipDir\\data\\config"
    }

    copy {
        from('src/main/resources/kcsp')
        include "kcsp-npki-*.properties"
        into "$preZipDir\\config"
    }

    copy {
        from("db")
        into "$preZipDir\\db"
    }

    copy {
        // 配置复制任务：处理数据库建表文件
        from('src/main/resources/sql')
        include '*.sql'
        exclude 'data/*.sql' // 排除data目录下的sql文件
        into "$preZipDir/db"
        eachFile { FileCopyDetails fcd ->
            // 截取文件名前缀作为目录名
            def dbType = fcd.name.split('\\.')[0]
            // 创建新的目录结构和文件名
            fcd.path = "${dbType}/init_table.sql"
        }

        // 获取所有数据库类型目录
        def dbTypes = file('src/main/resources/sql').listFiles().findAll { it.isFile() && it.name.endsWith('.sql') }.collect { it.name.split('\\.')[0] }

        // 配置复制任务：处理初始化数据文件
        dbTypes.each { dbType ->
            copy {
                from('src/main/resources/sql/data/npki.sql')
                into "$preZipDir/db/$dbType"
                rename { 'init_data.sql' }
            }
        }

        includeEmptyDirs = false
    }

    // 复制文件密码机数据
    copy {
        from("keystore")
        into "$preZipDir\\keystore"
    }

    // 清除现有的lib目录
    delete {
        "$preZipDir\\lib"
    }

    // 将依赖包复制到jar目录
    copy {
        from configurations.runtimeClasspath
        into "$preZipDir\\jar"
    }
    copy {
        from "$buildDir/libs"
        into "$preZipDir\\jar"
    }

    copy {
        from('src/main/resources')
        include "static/"
        into "$preZipDir\\front"
    }

    copy {
        from('src/main/resources/kcsp')
        include "kcsp-*-k8s.yaml"
        into "$preZipDir\\k8s"
    }

    download.run {
        src "http://nexus3.koal.com:8081/repository/maven-releases/kl/pki/dpg/dpg-verifier/${dpgVerifierForKcspVersion}/dpg-verifier-${dpgVerifierForKcspVersion}.jar"
        dest "$preZipDir/dpg-verifier.jar"
    }
}

def static setFileFormat(dirPath, suffixs, isDos2Unix) {
    def suffixs2Regex = ""
    suffixs.each() { suffix ->
        suffixs2Regex = suffixs2Regex + Pattern.quote(suffix) + "|"
    }
    new File(dirPath).eachFileMatch(~/.*\.($suffixs2Regex)/) { file ->
        if (file.size() > 0) {
            def lineList = file.readLines('UTF-8')
            def content = lineList.join(isDos2Unix ? '\n' : '\r\n')
            file.write(content, 'UTF-8')
        }
    }

}

task setFileFormat() {
    setFileFormat(projectDir + '/src/main/bin', ['sh'], true)
    setFileFormat(projectDir + '/src/main/bin', ['bat'], false)
}

// 复制启动脚本
task copyBin() {
    dependsOn setFileFormat
    doLast {
        copy {
            from("src/main/bin")
            into(preZipDir)
        }
        // 修改启动脚本启动类
        def scriptFile = file("$preZipDir/service.sh")
        def content = scriptFile.text

        // 使用正则表达式替换 START_CLASS 的值
        content = content.replaceAll(/(START_CLASS=)(\S+)/, "\$1$mainClass")
        // 将修改后的内容写回文件
        scriptFile.text = content

        if (project.zipType == kcspZipType) {
            // 写入环境变量到 service.vmoptions 文件
            try {
                // 读取现有的 service.vmoptions 文件内容
                List<String> lines = Files.readAllLines(Paths.get(vmOptionsFilePath));

                // 添加新的环境变量，用于对接KCSP 监控
                lines.add('${JMX_EXPORTER_JVM_OPTION}')
                lines.add('${SKYWALKING_JVM_OPTION}')

                // 将更新后的内容写回文件
                Files.write(Paths.get(vmOptionsFilePath), lines, StandardOpenOption.WRITE);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}

jar.mustRunAfter cleanProductEnv
copyBin.mustRunAfter copyJarResources

// 计算并打印 SHA-256 哈希值
static def calculateSha256(File file) {
    MessageDigest digest = MessageDigest.getInstance("SHA-256")
    byte[] fileBytes = Files.readAllBytes(file.toPath())
    byte[] hashBytes = digest.digest(fileBytes)
    return hashBytes.collect { String.format("%02x", it) }.join()
}

task createZip(type: Zip) {
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)
    archiveFileName = getPackageName(standardZipType, kcspZipType)

    from("${preZipDir}") {
        include "*.sh"
        fileMode 0755
    }

    from("${preZipDir}") {
        include "**/*"
    }

    if (zipType != kcspZipType) {
        into packageName
    }

    doLast {
        def archiveFile = archiveFile.get().asFile
        def hashValue = calculateSha256(archiveFile)
        println "ZIP包的名称: ${archiveFile.name}"
        println "ZIP包的路径: ${archiveFile}"
        println "ZIP包的 SHA-256 哈希值: ${hashValue}"
    }
}

def getPackageName(standardZipType, kcspZipType) {
    def dateStr = new Date().format('yyyyMMdd')
    switch (zipType) {
        case standardZipType:
            return "${packageName}-${project.version}-${dateStr}.zip"
        case kcspZipType:
            return "${packageName}-${project.version}-FOR-KCSP-${dateStr}.zip"
    }
}

task unsignedZip {
    dependsOn cleanProductEnv
    dependsOn copyJarResources
    dependsOn copyBin
    dependsOn createZip
}

copyBin.mustRunAfter copyJarResources
createZip.mustRunAfter copyBin

task signedZip {
    dependsOn unsignedZip

    doLast {
        // 定义原始和修改后的文件名
        def originalZip = file("${buildDir}/distributions/" + getPackageName(standardZipType, kcspZipType))
        def unsignedZip = file("${originalZip.parent}/${originalZip.name.replace('.zip', '_unsigned.zip')}")
        def signedZip = file("${originalZip.parent}/${originalZip.name.replace('.zip', '_signed.zip')}")

        // 下载 dpg-signer.jar
        download.run {
            src "http://nexus3.koal.com:8081/repository/maven-releases/kl/pki/dpg/dpg-signer/${dpgSignerVersion}/dpg-signer-${dpgSignerVersion}.jar"
            dest "${buildDir}/distributions/dpg-signer.jar"
        }

        // 执行签名过程
        javaexec {
            main = '-jar'
            args = ["${buildDir}/distributions/dpg-signer.jar", originalZip]
        }

        // 将原始 zip 重命名为 *_unsigned.zip
        if (originalZip.exists()) {
            originalZip.renameTo(unsignedZip)
            println "ZIP包重命名完成【未签名】: ${unsignedZip.name}"
            println "ZIP包的路径【未签名】: ${unsignedZip}"
            def hashValue = calculateSha256(unsignedZip)
            println "ZIP包的SHA-256 哈希值【未签名】: ${hashValue}"
        }

        // 如果有必要，移除签名 zip 文件名中的 _signed 后缀
        if (signedZip.exists()) {
            def finalSignedZip = file("${signedZip.parent}/${signedZip.name.replace('_signed.zip', '.zip')}")
            signedZip.renameTo(finalSignedZip)
            println "ZIP包重命名完成【已签名】: ${finalSignedZip.name}"
            println "ZIP包的路径【已签名】: ${finalSignedZip}"
            def hashValue = calculateSha256(finalSignedZip)
            println "ZIP包的SHA-256 哈希值【已签名】: ${hashValue}"
        }
    }
}