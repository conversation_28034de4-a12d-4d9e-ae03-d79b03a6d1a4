version: '3'

networks:
  app-web:
    driver: bridge

services:
  npki-km-management:
    build: .
    image: harbor.koal.com/npki/npki-km-management:latest
    container_name: npki-km-management
    ports:
      - "11080:11080"
      - "11443:11443"
      - "18800:18800"
      - "9443:9443"
      - "9096:9096"
    restart: always
    networks:
      - app-web
    environment:
      - APOLLO_APP_ID=nkm-k8s
      - APOLLO_ENV=DEV
      - APOLLO_CLUSTER=default
      - APOLLO_PORTAL_URL=http://10.0.244.80:31070
      - APOLLO_META=http://10.0.244.80:31080
      - APOLLO_TOKEN=a9435124aaf3488a208f3d1ed94dc79efafea4e4
      - CONFIG_STORE_TYPE=APOLLO
      - CONFIG_CLASS_LOCATION=kl
      - CONFIG_FILE_LOCATION=config
      - CONFIG_ENC_KEY=