plugins {
    id 'java'
    id 'java-library'
    id 'maven-publish'
    id "de.undercouch.download" version "5.3.0"
}

download.run {
    src 'http://git.koal.com/api/v4/projects/8613/repository/files/properties.gradle/raw?ref=develop'
    dest "${buildDir}/properties.gradle"
    header 'PRIVATE-TOKEN', "${accessToken}"
}

apply from: "${buildDir}/properties.gradle"

sourceCompatibility = 1.8
targetCompatibility = 1.8


repositories {
    mavenLocal()
    maven {
        url mvnPublicRepo
        allowInsecureProtocol = true
        metadataSources {
            mavenPom()
            artifact()
        }
    }
    mavenCentral()
}

configurations.configureEach {
    // check for updates every build
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
    exclude group: 'org.springframework.boot', module: 'spring-boot-starter-tomcat'
    exclude group: 'org.apache.tomcat.embed'
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    exclude group: 'log4j', module: 'log4j'
    exclude group: 'org.apache.logging.log4j'
    exclude group: "org.apache.commons", module: "commons-dbcp2"
    exclude group: "dm", module: "DmJdbcDriver"
}

ext {
    isReleaseVersion = !version.endsWith("SNAPSHOT")
}

configurations {
    testImplementation.extendsFrom compileOnly
}

dependencies {

    // spring基础组件
    compileOnly "org.springframework.boot:spring-boot-starter-web:${springBootVersion}"
    compileOnly "org.springframework.boot:spring-boot-starter-jetty:${springBootVersion}"
    compileOnly "org.springframework.boot:spring-boot-dependencies:${springBootVersion}"

    // springcloud组件
    compileOnly "com.tongweb.springboot:tongweb-spring-boot-starter-2.x:${tongWebVersion}"
    compileOnly "org.springframework.cloud:spring-cloud-starter-bootstrap:${springCloudBootstrapVersion}"

    // consul注册中心
    api "org.springframework.cloud:spring-cloud-starter-consul-discovery:${springCloudConsulVersion}"

    api "kl.npki:npki-base-core:${version}"
    api "kl.nbase:common-base-netty:${cbbNettyVersion}"
    api "kl.nbase:common-base-emengine-file-spi:${cbbKJCCEngineVersion}"
    implementation "kl.nbase:common-base-emengine-sdf-library-koal-windows:${cbbKJCCEngineVersion}"
    implementation "kl.nbase:common-base-emengine-sdf-library-koal-linux:${cbbKJCCEngineVersion}"
    implementation "kl.nbase:common-base-emengine-sdf-library-sjj1012a-linux:${cbbKJCCEngineVersion}"
    implementation "kl.nbase:common-base-emengine-sdf-library-sjj1012a-windows:${cbbKJCCEngineVersion}"
    implementation "kl.nbase:common-base-emengine-sdf-library-sansec-fips-linux:${cbbKJCCEngineVersion}"
    implementation "kl.nbase:common-base-emengine-sdf-library-sansec-fips-windows:${cbbKJCCEngineVersion}"
    // KCSP 3.0 SDF Engine
    api "kl.nbase:common-base-emengine-sdf-library-kcsp-linux:${cbbKJCCEngineVersion}"
    // apollo
    api "com.ctrip.framework.apollo:apollo-client:${apolloClientVersion}"
    api "com.ctrip.framework.apollo:apollo-openapi:${apolloClientVersion}"

    // redis
    api "org.redisson:redisson:${redissonVersion}"
    api "io.lettuce:lettuce-core:${lettuceVersion}"

    //traffic
    api "kl.nbase:common-base-traffic:${cbbTrafficVersion}"

    // 数据库
    api "com.alibaba:druid:${druidVersion}"
    api "com.baomidou:mybatis-plus-boot-starter:${mybatisPlusVersion}"

    api "org.apache.shardingsphere:shardingsphere-jdbc-core:${shardingSphereVersion}"

    api "kl.cloud:sql-generator-spring-boot-starter:${sqlGeneratorVersion}"
    api "kl.tools:dbtool:${klDbtoolVersion}"
    api "com.h2database:h2:${h2Version}"
    api "mysql:mysql-connector-java:${mysqlConnectorVersion}"
    api "com.oracle.database.jdbc:ojdbc8:${oracleOjdbc8Version}"
    api "cn.com.kingbase:kingbase8:${kingbaseJdbcVersion}"
    api "com.dameng:DmJdbcDriver18:${dmJdbcVersion}"
    api "gbase:gbase-connector-java:${gbaseJdbcVersion}"
    // 高斯DB数据库驱动包，官方获取：https://support.huaweicloud.com/centralized-devg-v3-gaussdb/gaussdb-42-0068.html
    api "org.opengauss:opengauss-jdbc:${gaussDBJdbcVersion}"
    api "com.mysql:mysql-connector-j:${mysqlConnectorJVersion}"

    api "org.antlr:ST4:${antlrST4Version}"

    // 通用基础组件
    annotationProcessor  "org.mapstruct:mapstruct-processor:${mapstructProcessor}"
    api "org.reflections:reflections:${reflectionsVersion}"
    api "org.ehcache:ehcache:${ehcacheVersion}"
    api "com.github.ben-manes.caffeine:caffeine:${caffeineVersion}"
    api "com.github.ben-manes.caffeine:jcache:${caffeineVersion}"
    api "io.netty:netty-all:${nettyVersion}"
    api "io.netty.incubator:netty-incubator-codec-classes-quic:${nettyIncubatorVersion}"

    // Swagger
    api "org.springdoc:springdoc-openapi-ui:${springdocOpenapiUiVersion}"
    api "org.glassfish:jakarta.el:${jakartaElVersion}"
    api "com.dooapp.fxform2:core:${fxform2CoreVersion}"
    // Swagger新UI
    api "com.github.xiaoymin:knife4j-springdoc-ui:${knife4jSpringdocUiVersion}"

    // 监控相关依赖
    api "kl.nbase:common-base-health-spring:${cbbHealthVersion}"
    runtimeOnly "kl.nbase:common-base-health-jvm:${cbbHealthVersion}"
    runtimeOnly ("kl.nbase:common-base-health-oshi:${cbbHealthVersion}") {
        exclude group: "org.slf4j", module: "slf4j-api"
    }
    runtimeOnly "kl.nbase:common-base-health-db:${cbbHealthVersion}"

    // 依赖漏洞修复，升级版本
    constraints{
        api "org.eclipse.jetty:jetty-servlets:${jettyWebappVersion}"
    }
    api "org.codehaus.janino:janino:${janinoVersion}"
    api ("org.yaml:snakeyaml:${snakeyamlVersion}"){
        force = true
    }
    api "com.alibaba.nacos:nacos-client:${nacosClientVersion}"
    api "com.google.protobuf:protobuf-java:${protobufJavaVersion}"
    api "com.google.guava:guava:${guavaVersion}"
    api "org.ow2.asm:asm:${asmVersion}"
    api "org.apache.calcite.avatica:avatica-core:${avaticaCoreVersion}"
    api "org.apache.calcite:calcite-core:${calciteCoreVersion}"
    api "commons-logging:commons-logging:${commonsLoggingVersion}"
    api "org.apache.groovy:groovy:${groovyVersion}"
    // RPC Feign
    api "kl.nbase:common-base-rpc-feign:${cbbRpcVersion}"

    compileOnly 'org.projectlombok:lombok:1.18.12'
    annotationProcessor 'org.projectlombok:lombok:1.18.12'
}

sourceSets {
    main {
        java { srcDir 'src/main/java' }
        resources {
            srcDirs 'src/main/java'
            srcDirs 'src/main/resources'
            exclude '**/*.java'
        }
        // 资源文件合并到 classes, 方便取其中的资源i18n等资源
        output.resourcesDir = java.classesDirectory
    }
    test {
        java { srcDir 'src/test/java' }
        resources {
            srcDirs 'src/test/java'
            srcDirs 'src/test/resources'
            exclude '**/*.java'
        }
        // 资源文件合并到 classes
        output.resourcesDir = java.classesDirectory
    }
}

jar {

    enabled = true

    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)

    archiveBaseName.set(artifactId)

    manifest {

        attributes "Implementation-Title": project.artifactId
        attributes "Implementation-Version": project.version

    }

}

task sourcesJar(type: Jar, dependsOn: classes) {
    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)

    archiveClassifier.set('sources')
    archiveBaseName.set(artifactId)

    from sourceSets.main.allSource
    from sourceSets.test.allSource
}

artifacts {

    archives jar
    archives sourcesJar
}

publishing {
    repositories {
        maven {
            if (isReleaseVersion) {
                url mvnReleaseRepo
            } else {
                url mvnSnapshotsRepo
            }
            credentials {
                username = mvnUsername
                password = mvnPassword
            }
        }
    }
    publications {
        mavenJava(MavenPublication) {
            groupId project.group
            artifactId project.artifactId
            version project.version
            from components.java
            artifact sourcesJar
        }
    }
}

test {
    useJUnitPlatform()
}
