plugins {
    id 'java'
    id 'war'
    id 'maven-publish'
    id "de.undercouch.download" version "5.3.0"
    id "io.freefair.aspectj.post-compile-weaving" version "5.1.1"
    id "org.owasp.dependencycheck" version "8.2.1"
}

// 下载通用gradle脚本
task downloadGradles() {
    // 版本控制脚本
    download.run {
        src 'http://git.koal.com/api/v4/projects/8613/repository/files/properties.gradle/raw?ref=develop'
        dest "${buildDir}/properties.gradle"
        header 'PRIVATE-TOKEN', "${accessToken}"
    }
    // 刷新静态资源脚本
    download.run {
        src 'http://git.koal.com/api/v4/projects/8613/repository/files/staticResource.gradle/raw?ref=develop'
        dest "${buildDir}/staticResource.gradle"
        header 'PRIVATE-TOKEN', "${accessToken}"
    }
    // 添加zip打包脚本
    download.run {
        src 'http://git.koal.com/api/v4/projects/8322/repository/files/zip.gradle/raw?ref=develop'
        dest "${buildDir}/zip.gradle"
        header 'PRIVATE-TOKEN', "${accessToken}"
    }
    // 添加war打包脚本
    download.run {
        src 'http://git.koal.com/api/v4/projects/8322/repository/files/war.gradle/raw?ref=develop'
        dest "${buildDir}/war.gradle"
        header 'PRIVATE-TOKEN', "${accessToken}"
    }
}

apply from: "${buildDir}/properties.gradle"
apply from: "${buildDir}/staticResource.gradle"
apply from: "buildNpkiEnv.gradle"
apply from: "${buildDir}/zip.gradle"
apply from: "${buildDir}/war.gradle"

sourceCompatibility = 1.8
targetCompatibility = 1.8


repositories {
    mavenLocal()
    maven {
        url mvnPublicRepo
        allowInsecureProtocol = true
        metadataSources {
            mavenPom()
            artifact()
        }
    }
    mavenCentral()
}

configurations.all {
    // check for updates every build
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
    exclude group: 'org.springframework.boot', module: 'spring-boot-starter-tomcat'
    exclude group: 'org.apache.tomcat.embed'
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    exclude group: 'log4j', module: 'log4j'
    exclude group: 'org.apache.shardingsphere.elasticjob'
    exclude group: 'koal.emengine3', module: 'koal_emengine'
    resolutionStrategy.eachDependency { DependencyResolveDetails details ->
        def requested = details.requested
        if (requested.group == 'org.yaml' && requested.name == 'snakeyaml') {
            details.useVersion "${snakeyamlVersion}"
        }
        if (requested.group == 'org.slf4j' && requested.name == 'slf4j-api') {
            details.useVersion "${slf4jVersion}"
        }
        if (requested.group == 'org.springframework.boot' && requested.name == 'spring-boot-actuator-autoconfigure') {
            details.useVersion "${springBootActuatorVersion}"
        }
    }
}

ext {
    isReleaseVersion = !version.endsWith("SNAPSHOT")
    ibcReleaseVersion = "1.0.0-SNAPSHOT"
}

configurations {
    testImplementation.extendsFrom compileOnly
}

dependencies {

    aspect "kl.nbase:common-base-log:${cbbLogVersion}"
    aspect "kl.nbase:common-base-traffic:${cbbTrafficVersion}"
    implementation "kl.npki:npki-km-service:${version}"
    // 引入base-management相关的依赖
    implementation "kl.npki:npki-base-management:${project.version}"
    // 引入SM9模块依赖
    implementation("kl.ibc:ibc-base-core:${ibcReleaseVersion}") {
        transitive = false
    }
    implementation("kl.ibc:ibc-base-service:${ibcReleaseVersion}") {
        transitive = false
    }
    implementation("kl.ibc:ibc-base-management:${ibcReleaseVersion}") {
        transitive = false
    }
    implementation("kl.ibc:ibc-base-management-core:${ibcReleaseVersion}") {
        transitive = false
    }
    implementation("kl.ibc:ibc-kgs-core:${ibcReleaseVersion}") {
        transitive = false
    }
    implementation("kl.ibc:ibc-kgs-service:${ibcReleaseVersion}") {
        transitive = false
    }
    implementation("kl.ibc:ibc-kgs-management:${ibcReleaseVersion}") {
        transitive = false
    }
    // 通用基础组件
    annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"

    // junit5
    testImplementation group: 'org.junit.jupiter', name: 'junit-jupiter-api', version: '5.9.2'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.9.2'
    testImplementation "org.springframework.boot:spring-boot-starter-test:${springBootVersion}"
    testAspect "kl.nbase:common-base-log:${cbbLogVersion}"
}

sourceSets {
    main {
        java { srcDir 'src/main/java' }
        resources {
            srcDirs 'src/main/java'
            srcDirs 'src/main/resources'
            exclude '**/*.java'
        }
        // 资源文件合并到 classes, 方便取其中的资源i18n等资源
        output.resourcesDir = java.classesDirectory
    }
    test {
        java { srcDir 'src/test/java' }
        resources {
            srcDirs 'src/test/java'
            srcDirs 'src/test/resources'
            exclude '**/*.java'
        }
        // 资源文件合并到 classes
        output.resourcesDir = java.classesDirectory
    }
}

jar {
    enabled = true
    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)
    // 排除不需要的文件
    exclude 'sharding-km.properties', 'npki*', 'logback*', 'application*', 'bootstrap.yml', '*.jks' , 'jmx.yaml'
    exclude 'static/', 'kmsql/', 'db/', 'sql/' , 'public/', 'kcsp/'
    archiveBaseName.set(artifactId)
    manifest {
        attributes "Implementation-Title": project.artifactId
        attributes "Implementation-Version": project.version

    }
}

task sourcesJar(type: Jar, dependsOn: classes) {
    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)
    exclude 'sharding-km.properties', 'npki*', 'logback*', 'application*', 'bootstrap.yml', '*.jks'
    exclude 'static/', 'kmsql/', 'db/', 'sql/' , 'public/'
    archiveClassifier.set('sources')
    archiveBaseName.set(artifactId)

    from sourceSets.main.allSource
    from sourceSets.test.allSource
}

task allPackages() {
    dependsOn signedZip
    dependsOn war
}


tasks.named('processResources') {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

tasks.named('processTestResources') {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}


artifacts {

    archives jar
    archives sourcesJar
}

publishing {
    repositories {
        maven {
            if (isReleaseVersion) {
                url mvnReleaseRepo
            } else {
                url mvnSnapshotsRepo
            }
            credentials {
                username = mvnUsername
                password = mvnPassword
            }
        }
    }
    publications {
        mavenJava(MavenPublication) {
            groupId project.group
            artifactId project.artifactId
            version project.version
            from components.java
            artifact sourcesJar
        }
    }
}

test {
    useJUnitPlatform()
}
dependencyCheck {
    analyzers {
        assemblyEnabled=false
        ossIndex {
            enabled = false
        }
    }
}
