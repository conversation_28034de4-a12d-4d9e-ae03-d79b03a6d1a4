plugins {
    id 'java'
    id 'java-library'
    id 'maven-publish'
    id "de.undercouch.download" version "5.3.0"
}

download.run {
    src 'http://git.koal.com/api/v4/projects/8613/repository/files/properties.gradle/raw?ref=develop'
    dest "${buildDir}/properties.gradle"
    header 'PRIVATE-TOKEN', "${accessToken}"
}

apply from: "${buildDir}/properties.gradle"

sourceCompatibility = 1.8
targetCompatibility = 1.8


repositories {
    mavenLocal()
    maven {
        url mvnPublicRepo
        allowInsecureProtocol = true
        metadataSources {
            mavenPom()
            artifact()
        }
    }
    mavenCentral()
}

configurations.all {
    // check for updates every build
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'

    // 强制使用特定版本的依赖
    resolutionStrategy {
        // 由于KJCC组件在很多组件中都有引用，为了保持版本一致性，强制指定为common-gradle-parent中设置的版本 {{
        force "kl.nbase:common-base-klcrypto:${cbbKJCCSecurityVersion}"
        force "kl.nbase:common-base-klpkix:${cbbKJCCSecurityVersion}"
        force "kl.nbase:common-base-klasn1:${cbbKJCCSecurityVersion}"
        force "kl.nbase:common-base-emengine:${cbbKJCCEngineVersion}"
        force "kl.nbase:encryption-fusion:${cbbKJCCEngineVersion}"
        force "kl.nbase:common-base-emengine-fusion:${cbbKJCCEngineVersion}"
        // }}
        // todo helper包升级到6.1.2-SNAPSHOT后，可删掉此配置
        force "kl.nbase:common-base-helper:${cbbHelperVersion}"
    }
}

ext {
    isReleaseVersion = !version.endsWith("SNAPSHOT")
}

configurations {
    testImplementation.extendsFrom compileOnly
}

dependencies {
    // 通用组件
    api "org.apache.commons:commons-lang3:${commonsLangVersion}"
    api "commons-io:commons-io:${commonsIOVersion}"
    api "org.apache.commons:commons-collections4:${commonsCollectionsVersion}"
    api "commons-codec:commons-codec:${commonsCodecVersion}"
    api "com.alibaba:transmittable-thread-local:${transmittableThreadVersion}"
    api "org.hibernate.validator:hibernate-validator:${hibernateValidatorVersion}"
    api "org.slf4j:slf4j-api:${slf4jVersion}"

    // kl核心包依赖组件
    api "kl.nbase:common-base-helper:${cbbHelperVersion}"
    api "kl.nbase:common-base-exception:${cbbExceptionVersion}"
    api "kl.nbase:common-base-config:${cbbConfigVersion}"
    api "kl.nbase:common-base-cache:${cbbCacheVersion}"
    api "kl.nbase:common-base-log:${cbbLogVersion}"
    api "kl.nbase:common-base-klcrypto:${cbbKJCCSecurityVersion}"
    api "kl.nbase:common-base-klasn1:${cbbKJCCSecurityVersion}"
    api "kl.nbase:common-base-klpkix:${cbbKJCCSecurityVersion}"
    api "kl.security:security-klext:${klextVersion}"
    // kl基础组件
    api "kl.nbase:common-base-db:${cbbDbVersion}"
    api "kl.nbase:common-base-bean:${cbbBeanVersion}"
    api "kl.nbase:common-base-timer:${cbbTimerVersion}"
    api "kl.nbase:common-base-auth:${cbbAuthVersion}"
    api "kl.nbase:common-base-i18n:${cbbI18nVersion}"
    // emengine依赖
    api "kl.nbase:common-base-emengine:${cbbKJCCEngineVersion}"
    api "kl.nbase:encryption-fusion:${cbbKJCCEngineVersion}"
    api "kl.nbase:common-base-emengine-fusion:${cbbKJCCEngineVersion}"
    // RPC
    api "kl.nbase:common-base-rpc-netty-tcp:${cbbRpcVersion}"
    api "kl.nbase:common-base-rpc-apachehttp:${cbbRpcVersion}"
    api "kl.nbase:common-base-rpc-okhttp:${cbbRpcVersion}"
    // mapper组件
    api "org.mapstruct:mapstruct:${mapstructVersion}"
    api "org.mapstruct:mapstruct-jdk8:${mapstructVersion}"

    // easy-excel
    api ("com.alibaba:easyexcel:${easyExcelVersion}"){
        exclude group: 'org.apache.commons', module: 'commons-compress'
    }
    api "org.apache.poi:poi-ooxml:${poiVersion}"
    api "org.apache.commons:commons-compress:${commonsCompressVersion}"
    api "org.apache.commons:commons-csv:${commonsCsvVersion}"
    api "org.apache.commons:commons-configuration2:${commonsConfigurationVersion}"
    api "commons-fileupload:commons-fileupload:${commonsFileUploadVersion}"

    //json数据转换依赖
    api 'net.sf.json-lib:json-lib:2.4:jdk15'
    api "com.fasterxml.jackson.core:jackson-annotations:${jacksonAnnotationsVersion}"
    api "com.fasterxml.jackson.core:jackson-databind:${jacksonDatabindVersion}"
    api "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:${jacksonDatabindVersion}"
    api "com.jayway.jsonpath:json-path:${jsonPathVersion}"
    api "net.minidev:json-smart:${jsonSmartVersion}"

    // 反射依赖
    api "org.reflections:reflections:${reflectionsVersion}"

    // Excel XAdES签名相关依赖
    implementation "org.apache.santuario:xmlsec:${xmlsecVersion}"

    // junit5
    testImplementation "org.junit.jupiter:junit-jupiter-api:${junitVersion}"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:${junitVersion}"
    testImplementation "koal.emengine3:koal_emengine:${koalEmengineVersion}"
    testImplementation "kl.nbase:common-base-emengine-file-spi:${cbbKJCCEngineVersion}"

    // logback日志
    api "ch.qos.logback:logback-classic:${logbackVersion}"
    api "ch.qos.logback:logback-core:${logbackVersion}"

}


jar {

    enabled = true

    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)

    archiveBaseName.set(artifactId)

    manifest {

        attributes "Implementation-Title": project.artifactId
        attributes "Implementation-Version": project.version

    }

}

task sourcesJar(type: Jar, dependsOn: classes) {
    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)

    archiveClassifier.set('sources')
    archiveBaseName.set(artifactId)

    from sourceSets.main.allSource
    from sourceSets.test.allSource
}

artifacts {

    archives jar
    archives sourcesJar
}

publishing {
    repositories {
        maven {
            if (isReleaseVersion) {
                url mvnReleaseRepo
            } else {
                url mvnSnapshotsRepo
            }
            credentials {
                username = mvnUsername
                password = mvnPassword
            }
        }
    }
    publications {
        mavenJava(MavenPublication) {
            groupId project.group
            artifactId project.artifactId
            version project.version
            from components.java
            artifact sourcesJar
        }
    }
}

test {
    useJUnitPlatform()
}
