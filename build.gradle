plugins {
    id 'java'
    id 'java-library'
    id 'maven-publish'
    id "de.undercouch.download" version "5.3.0"
}

download.run {
    src 'http://git.koal.com/api/v4/projects/8613/repository/files/properties.gradle/raw?ref=develop'
    dest "${buildDir}/properties.gradle"
    header 'PRIVATE-TOKEN', "${accessToken}"
}

apply from: "${buildDir}/properties.gradle"
sourceCompatibility = 1.8
targetCompatibility = 1.8


repositories {
    mavenLocal()
    maven {
        url mvnPublicRepo
        allowInsecureProtocol = true
        metadataSources {
            mavenPom()
            artifact()
        }
    }
    mavenCentral()
}

configurations.all {
    // check for updates every build
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
}

ext {
    isReleaseVersion = !version.endsWith("SNAPSHOT")
}

configurations {
    testImplementation.extendsFrom compileOnly
}

dependencies {
    api "kl.npki:npki-base-core:${project.version}"
    // 默认内置KCSP单点登录实现，其他平台单点登录需要提取SPI实现包，然后放入lib目录
    api "kl.npki:npki-base-sso-kcsp:${project.version}"
    // api "kl.npki:npki-base-sso-icbc:${project.version}"
    compileOnly "org.springframework.boot:spring-boot-starter-web:${springBootVersion}"
    compileOnly "com.baomidou:mybatis-plus:${mybatisPlusVersion}"
    api "com.alibaba:druid:${druidVersion}"
    api ("org.logback-extensions:logback-ext-loggly:0.1.5"){
        exclude group: 'ch.qos.logback', module: 'logback-classic'
    }
    api "io.swagger.core.v3:swagger-annotations:${swaggerAnnotationsVersion}"
    api "kl.nbase:common-base-log:${cbbLogVersion}"
    api "kl.nbase:common-base-netty:${cbbRpcVersion}"
}


jar {

    enabled = true

    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)

    archiveBaseName.set(artifactId)

    manifest {

        attributes "Implementation-Title": project.artifactId
        attributes "Implementation-Version": project.version

    }

}

task sourcesJar(type: Jar, dependsOn: classes) {
    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)

    archiveClassifier.set('sources')
    archiveBaseName.set(artifactId)

    from sourceSets.main.allSource
    from sourceSets.test.allSource
}

artifacts {

    archives jar
    archives sourcesJar
}

publishing {
    repositories {
        maven {
            if (isReleaseVersion) {
                url mvnReleaseRepo
            } else {
                url mvnSnapshotsRepo
            }
            credentials {
                username = mvnUsername
                password = mvnPassword
            }
        }
    }
    publications {
        mavenJava(MavenPublication) {
            groupId project.group
            artifactId project.artifactId
            version project.version
            from components.java
            artifact sourcesJar
        }
    }
}

test {
    useJUnitPlatform()
}
