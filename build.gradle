plugins {
    id 'java'
    id 'java-library'
    id 'maven-publish'
    id "de.undercouch.download" version "5.3.0"
    id "io.freefair.aspectj.post-compile-weaving" version "5.1.1"
}

download.run {
    src 'http://git.koal.com/api/v4/projects/8613/repository/files/properties.gradle/raw?ref=develop'
    dest "${buildDir}/properties.gradle"
    header 'PRIVATE-TOKEN', "${accessToken}"
}

apply from: "${buildDir}/properties.gradle"

sourceCompatibility = 1.8
targetCompatibility = 1.8


repositories {
    mavenLocal()
    maven {
        url mvnPublicRepo
        metadataSources {
            mavenPom()
            artifact()
        }
    }
    mavenCentral()
}

configurations.all {
    // check for updates every build
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
}

ext {
    isReleaseVersion = !version.endsWith("SNAPSHOT")
}

configurations {
    testImplementation.extendsFrom compileOnly
}

dependencies {
    aspect "kl.nbase:common-base-traffic:${cbbTrafficVersion}"

    api "kl.npki:npki-base-core:${project.version}"
    annotationProcessor  "org.mapstruct:mapstruct-processor:${mapstructProcessor}"
    compileOnly "io.swagger.core.v3:swagger-annotations:${swaggerAnnotationsVersion}"
    // junit5
    testImplementation group: 'org.junit.jupiter', name: 'junit-jupiter-api', version: '5.9.0-M1'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.9.0-M1'
}


jar {

    enabled = true

    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)

    archiveBaseName.set(artifactId)

    manifest {

        attributes "Implementation-Title": project.artifactId
        attributes "Implementation-Version": project.version

    }

}

task sourcesJar(type: Jar, dependsOn: classes) {
    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)

    archiveClassifier.set('sources')
    archiveBaseName.set(artifactId)

    from sourceSets.main.allSource
    from sourceSets.test.allSource
}

artifacts {

    archives jar
    archives sourcesJar
}

publishing {
    repositories {
        maven {
            if (isReleaseVersion) {
                url mvnReleaseRepo
            } else {
                url mvnSnapshotsRepo
            }
            credentials {
                username = mvnUsername
                password = mvnPassword
            }
        }
    }
    publications {
        mavenJava(MavenPublication) {
            groupId project.group
            artifactId project.artifactId
            version project.version
            from components.java
            artifact sourcesJar
        }
    }
}

test {
    useJUnitPlatform()
}
