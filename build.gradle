plugins {
    id 'java'
    id 'war'
    id 'java-library'
    id 'maven-publish'
    id 'application'
    id "de.undercouch.download" version "5.3.0"
    id "io.freefair.aspectj.post-compile-weaving" version "5.1.1"
    id "org.owasp.dependencycheck" version "8.2.1"
}

// 下载通用gradle脚本
task downloadGradles() {
    // 版本控制脚本
    download.run {
        src 'http://git.koal.com/api/v4/projects/8613/repository/files/properties.gradle/raw?ref=develop'
        dest "${buildDir}/properties.gradle"
        header 'PRIVATE-TOKEN', "${accessToken}"
    }
    // 刷新静态资源脚本
    download.run {
        src 'http://git.koal.com/api/v4/projects/8613/repository/files/staticResource.gradle/raw?ref=develop'
        dest "${buildDir}/staticResource.gradle"
        header 'PRIVATE-TOKEN', "${accessToken}"
    }
}

apply from: "${buildDir}/properties.gradle"
apply from: "${buildDir}/staticResource.gradle"
apply from: "buildNpkiEnv.gradle"
apply from: "zip.gradle"
apply from: "war.gradle"

sourceCompatibility = 1.8
targetCompatibility = 1.8

repositories {
    mavenLocal()
    maven {
        url mvnPublicRepo
        allowInsecureProtocol = true
        metadataSources {
            mavenPom()
            artifact()
        }
    }
    mavenCentral()
}

configurations.configureEach {
    // check for updates every build
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
    exclude group: 'org.springframework.boot', module: 'spring-boot-starter-tomcat'
    exclude group: 'org.apache.tomcat.embed'
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    exclude group: 'log4j', module: 'log4j'
    exclude group: "org.apache.commons", module: "commons-dbcp2"
    // lombok
    exclude group: 'org.projectlombok', module: 'lombok'

    resolutionStrategy.eachDependency { DependencyResolveDetails details ->
        def requested = details.requested
        if (requested.group == 'org.yaml' && requested.name == 'snakeyaml') {
            details.useVersion "${snakeyamlVersion}"
        }
    }
}

ext {
    isReleaseVersion = !version.endsWith("SNAPSHOT")
}

configurations {
    env
    testImplementation.extendsFrom compileOnly
}

dependencies {
    // spring基础组件
    api "org.springframework.boot:spring-boot-starter-web:${springBootVersion}"
    api "org.springframework.boot:spring-boot-starter-jetty:${springBootVersion}"
    api "org.springframework.boot:spring-boot-starter-security:${springBootVersion}"
    api "org.springframework.boot:spring-boot-actuator-autoconfigure:${springBootActuatorVersion}"
    api "org.springframework.boot:spring-boot-dependencies:${springBootVersion}"

    api "org.springframework.security:spring-security-config:${springSecurityVersion}"
    api "org.springframework.security:spring-security-web:${springSecurityVersion}"

    api "org.springframework:spring-webmvc:${springVersion}"
    api "org.springframework:spring-jdbc:${springVersion}"

    api ("com.tongweb.springboot:tongweb-spring-boot-starter-2.x:${tongWebVersion}"){
        exclude module: 'tongweb-dependencies-check'
    }
    api "org.springframework.cloud:spring-cloud-starter-bootstrap:${springCloudBootstrapVersion}"
    api "com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config:${nacosStarterVersion}"
    api "kl.cds:koal-consul-client:${koalConsulClientVersion}"

    api 'com.github.promeg:tinypinyin:2.0.3'

    // 领域层业务-通用
    api "kl.npki:npki-base-service:${project.version}"
    api "kl.npki:npki-base-management-core:${project.version}"

    // 引入 国密ssl中间件 相关依赖
    api "kl.nbase:common-base-gmssl-jetty:${cbbGmsslVersion}"
    api "kl.nbase:common-base-gmssl-tongweb:${cbbGmsslVersion}"
    api "org.eclipse.jetty:jetty-webapp:${jettyWebappVersion}"

    aspect "kl.nbase:common-base-log:${cbbLogVersion}"
    testAspect "kl.nbase:common-base-log:${cbbLogVersion}"
    annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"
    // junit5
    testImplementation group: 'org.junit.jupiter', name: 'junit-jupiter-api', version: '5.9.0-M1'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.9.0-M1'
    testImplementation "org.springframework.boot:spring-boot-starter-test:${springBootVersion}"

    // i18n
    implementation "kl.nbase:common-base-i18n:${cbbI18nVersion}"
}

sourceSets {
    main {
        java { srcDir 'src/main/java' }
        resources {
            srcDirs 'src/main/java'
            srcDirs 'src/main/resources'
            exclude '**/*.java'
        }
        // 资源文件合并到 classes, 方便取其中的资源i18n等资源
        output.resourcesDir = java.outputDir
    }
    test {
        java { srcDir 'src/test/java' }
        resources {
            srcDirs 'src/test/java'
            srcDirs 'src/test/resources'
            exclude '**/*.java'
        }
        // 资源文件合并到 classes
        output.resourcesDir = java.outputDir
    }
}

jar {

    enabled = true
    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)
    // 排除不需要的文件
    exclude 'baseResources.zip', 'npki*', 'logback*', 'application.properties', 'bootstrap.yml', '*.jks', '*.p12', "*.dat", 'jmx.yaml'
    exclude 'basesql/', 'static/', 'sql/', 'db/', 'public/', 'clodResources*'
    archiveBaseName.set(artifactId)
    manifest {
        attributes "Implementation-Title": project.artifactId
        attributes "Implementation-Version": project.version
//        attributes "Class-Path":
    }
}

task sourcesJar(type: Jar, dependsOn: classes) {
    // 忽略重复的文件
    setDuplicatesStrategy(DuplicatesStrategy.EXCLUDE)
    // 排除不需要的文件
    exclude 'baseResources.zip', 'npki*', 'logback*', 'application.properties', 'bootstrap.yml', '*.jks', '*.p12', "banner.txt", "*.dat"
    exclude 'basesql/', 'static/', 'sql/', 'db/', 'public/', 'clodResources*'

    archiveClassifier.set('sources')
    archiveBaseName.set(artifactId)

    from sourceSets.main.allSource
    from sourceSets.test.allSource
}

artifacts {

    archives jar
    archives sourcesJar
}

publishing {
    repositories {
        maven {
            if (isReleaseVersion) {
                url mvnReleaseRepo
            } else {
                url mvnSnapshotsRepo
            }
            credentials {
                username = mvnUsername
                password = mvnPassword
            }
        }
    }
    publications {
        mavenJava(MavenPublication) {
            groupId project.group
            artifactId project.artifactId
            version project.version
            from components.java
            artifact sourcesJar
        }
    }
}
dependencyCheck {
    // 检查报告路径: build/reports/dependency-check-report.html
    // 支持指定路径
    analyzers {
        assemblyEnabled=false
        ossIndex {
            enabled = false
        }
    }
}
