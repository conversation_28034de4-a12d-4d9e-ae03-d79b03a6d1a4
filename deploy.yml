apiVersion: v1
kind: Namespace
metadata:
  name: npki
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: npki-km-management
  namespace: npki
spec:
  replicas: 1
  selector:
    matchLabels:
      app: npki-km-management
  template:
    metadata:
      labels:
        app: npki-km-management
    spec:
      containers:
        - name: npki-km-management
          image: harbor.koal.com/npki/npki-km-management:latest
          # 优先使用远程镜像
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /metrics/health
              port: 11080
              scheme: HTTP
            initialDelaySeconds: 120
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 10
          ports:
            # containerPort是在pod控制器中定义的、pod中的容器需要暴露的端口。例如，mysql 服务需要暴露 3306 端口，redis 暴露 6379 端口
            - containerPort: 11080
            - containerPort: 11443
            - containerPort: 18800
            - containerPort: 9443
            - containerPort: 9096
          env:
            - name: APOLLO_APP_ID
              value: nkm-k8s
              # value: npki-km-management
            - name: APOLLO_ENV
              value: DEV
            - name: APOLLO_CLUSTER
              value: default
            - name: APOLLO_PORTAL_URL
              value: http://***********:31070
            - name: APOLLO_META
              value: http://***********:31080
            - name: APOLLO_TOKEN
              value: a9435124aaf3488a208f3d1ed94dc79efafea4e4
            - name: CONFIG_ENC_KEY
            - name: CONFIG_STORE_TYPE
              value: APOLLO
---
apiVersion: v1
kind: Service
metadata:
  name: npki-km-management
  labels:
    app: npki-km-management
  namespace: npki
spec:
  type: NodePort
  ports:
    # port是暴露在cluster ip上的端口，:port提供了集群内部客户端访问service的入口，即clusterIP:port
    - port: 11080
      # targetPort是pod上的端口，从port/nodePort上来的数据，经过kube-proxy流入到后端pod的targetPort上，最后进入容器。与制作容器时暴露的端口一致（使用DockerFile中的EXPOSE）
      targetPort: 11080
      # nodePort 提供了集群外部客户端访问 Service 的一种方式，nodePort 提供了集群外部客户端访问 Service 的端口，通过 nodeIP:nodePort 提供了外部流量访问k8s集群中service的入口。
      nodePort: 30100
      #protocol: TCP
      name: mgmt-http
    - port: 11443
      targetPort: 11443
      nodePort: 30101
      name: mgmt-https
    - port: 18800
      targetPort: 18800
      nodePort: 30102
      name: tcp
    - port: 9443
      targetPort: 9443
      nodePort: 30103
      name: service-https
    - port: 9096
      targetPort: 9096
      nodePort: 30104
      name: service-http
  selector:
    app: npki-km-management
