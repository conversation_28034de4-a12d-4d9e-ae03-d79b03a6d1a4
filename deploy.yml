apiVersion: v1
kind: Namespace
metadata:
  name: npki
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: npki-base-management
  namespace: npki
spec:
  replicas: 1
  selector:
    matchLabels:
      app: npki-base-management
  template:
    metadata:
      labels:
        app: npki-base-management
    spec:
      containers:
      - name: npki-base-management
        image: harbor.koal.com/npki/npki-base-management:latest
        # 优先使用远程镜像
        imagePullPolicy: Always
        ports:
        # containerPort是在pod控制器中定义的、pod中的容器需要暴露的端口。例如，mysql 服务需要暴露 3306 端口，redis 暴露 6379 端口
        - containerPort: 8085
        env:
        - name: PARAMS   # 定义变量，用来接收sql的用户/密码 mysql为k8s集群内的service名称，在k8s集群内部可以直接使用service名称，因为集群默认做了coredns解析
          # value: "--spring.datasource.url=********************************************************************************* --spring.datasource.username=root --spring.datasource.password=U2FtcGxlUGFzc3dvcmQ= --spring.mail.username=<EMAIL> --spring.mail.password=U2FtcGxlUGFzc3dvcmQ="
---
apiVersion: v1
kind: Service
metadata:
  name: npki-base-management
  labels:
    app: npki-base-management
  namespace: npki
spec:
  type: NodePort
  ports:
  # port是暴露在cluster ip上的端口，:port提供了集群内部客户端访问service的入口，即clusterIP:port
  - port: 8080
    # targetPort是pod上的端口，从port/nodePort上来的数据，经过kube-proxy流入到后端pod的targetPort上，最后进入容器。与制作容器时暴露的端口一致（使用DockerFile中的EXPOSE）
    targetPort: 8080
    # nodePort 提供了集群外部客户端访问 Service 的一种方式，nodePort 提供了集群外部客户端访问 Service 的端口，通过 nodeIP:nodePort 提供了外部流量访问k8s集群中service的入口。
    nodePort: 30110
    #protocol: TCP
    #name: http
  selector:
    app: npki-base-management
