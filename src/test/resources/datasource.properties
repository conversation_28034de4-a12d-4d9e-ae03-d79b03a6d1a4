kl.datasource.slotName=test

kl.datasource.datasource[0].name=ds0
kl.datasource.datasource[0].url=jdbc:mysql://10.0.244\
  .77:3306/003gqtest?useUnicode=true&characterEncoding=UTF-8&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode\
  =false&serverTimezone=Asia/Shanghai&useSSL=false
kl.datasource.datasource[0].username=root
kl.datasource.datasource[0].password=root123
kl.datasource.datasource[0].driverClassName=com.mysql.cj.jdbc.Driver
kl.datasource.datasource[0].initialSize=1
kl.datasource.datasource[0].minIdle=1
kl.datasource.datasource[0].maxActive=20
kl.datasource.datasource[0].maxWait=60000
kl.datasource.datasource[0].timeBetweenEvictionRunsMillis=60000
kl.datasource.datasource[0].minEvictableIdleTimeMillis=300000
kl.datasource.datasource[0].validationQuery=SELECT 1 FROM DUAL
kl.datasource.datasource[0].testWhileIdle=true
kl.datasource.datasource[0].testOnBorrow=false
kl.datasource.datasource[0].testOnReturn=false
kl.datasource.datasource[0].poolPreparedStatements=false
kl.datasource.datasource[0].filters=stat,slf4j
kl.datasource.datasource[0].logSlowSql=true
kl.datasource.datasource[0].slowSqlMillis=3000

kl.datasource.datasource[1].name=hds1
kl.datasource.datasource[1].url=jdbc:mysql://10.0.244\
  .77:3306/004gqtest?useUnicode=true&characterEncoding=UTF-8&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode\
  =false&serverTimezone=Asia/Shanghai&useSSL=false
kl.datasource.datasource[1].username=root
kl.datasource.datasource[1].password=root123
kl.datasource.datasource[1].driverClassName=com.mysql.cj.jdbc.Driver
kl.datasource.datasource[1].initialSize=1
kl.datasource.datasource[1].minIdle=1
kl.datasource.datasource[1].maxActive=20
kl.datasource.datasource[1].maxWait=60000
kl.datasource.datasource[1].timeBetweenEvictionRunsMillis=60000
kl.datasource.datasource[1].minEvictableIdleTimeMillis=300000
kl.datasource.datasource[1].validationQuery=SELECT 1 FROM DUAL
kl.datasource.datasource[1].testWhileIdle=true
kl.datasource.datasource[1].testOnBorrow=false
kl.datasource.datasource[1].testOnReturn=false
kl.datasource.datasource[1].poolPreparedStatements=false
kl.datasource.datasource[1].filters=stat,slf4j
kl.datasource.datasource[1].logSlowSql=true
kl.datasource.datasource[1].slowSqlMillis=3000