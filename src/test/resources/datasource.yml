switchable:
  - slot:
      slotName: test
      datasource:
        - driverClassName: com.mysql.cj.jdbc.Driver
          filters: stat,slf4j
          initialSize: 1
          logSlowSql: true
          maxActive: 20
          maxWait: 60000
          minEvictableIdleTimeMillis: 300000
          minIdle: 1
          name: ds0
          password: root123
          poolPreparedStatements: false
          slowSqlMillis: 3000
          testOnBorrow: false
          testOnReturn: false
          testWhileIdle: true
          timeBetweenEvictionRunsMillis: 60000
          url: ***************************************************************************************************************************************************************************************
          username: root
          validationQuery: SELECT 1 FROM DUAL
    sharding:
      databaseDiscoveryEnabled: false
      databaseShardingAlgorithm: kl.nbase.db.support.sharding.algorithm.DefaultDatabaseShardingAlgorithm
      databaseShardingColumn: id
      readwriteSplittingEnabled: false
      shardingEnabled: true
      tables:
        - dataNodes: ds0.t_key_current_$->{0..1}
          keyGenerateAlgorithm: kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
          keyGenerateColumn: id
          name: t_key_current
          shardingAlgorithm: kl.nbase.db.support.sharding.algorithm.HashModShardingAlgorithm
          shardingColumn: id
        - dataNodes: ds0.t_key_history_$->{0..1}
          keyGenerateAlgorithm: kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
          keyGenerateColumn: id
          name: t_key_history
          shardingAlgorithm: kl.nbase.db.support.sharding.algorithm.HashModShardingAlgorithm
          shardingColumn: id