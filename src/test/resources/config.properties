# cache
kl.cache.enabled=true
kl.cache.type=ehcache
kl.cache.ehcache.heapSize=256

# em
kl.em.emConf[0].enabled = true
kl.em.emConf[0].id = 0
kl.em.emConf[0].name = \u6587\u4EF6\u5BC6\u7801\u673A
kl.em.emConf[0].instanceName = RSA
kl.em.emConf[0].type = FILE2048
kl.em.emConf[0].ip = 127.0.0.1
kl.em.emConf[0].port = 8017
kl.em.emConf[0].username = user
kl.em.emConf[0].password = 12345678
kl.em.emConf[0].keyBeginIndex = 1
kl.em.emConf[0].keyEndIndex = 5
kl.em.emConf[0].sm3Uid =
kl.em.emConf[0].storePath = ./src/test/resources/
kl.em.emConf[1].enabled = true
kl.em.emConf[1].id = 1
kl.em.emConf[1].name = \u6587\u4EF6\u5BC6\u7801\u673A
kl.em.emConf[1].instanceName = SM2
kl.em.emConf[1].type = SM2FILE
kl.em.emConf[1].ip = 127.0.0.1
kl.em.emConf[1].port = 8017
kl.em.emConf[1].username = user
kl.em.emConf[1].password = 12345678
kl.em.emConf[1].keyBeginIndex = 1
kl.em.emConf[1].keyEndIndex = 5
kl.em.emConf[1].sm3Uid =
kl.em.emConf[1].storePath = ./src/test/resources/

# log
kl.log.file=logs/npki-base-management
kl.log.level=info
kl.log.engineLevel=info
kl.log.sysLog[0]=Console
kl.log.transactionLog[0]=Console
kl.log.maxCapacity=20mb
kl.log.auditServiceUrl=127.0.0.1

# login
kl.login.accessTokenLifecycle= 86400000
kl.login.accessControlEnabled= false
kl.login.apiPrefix=/mgmt/v1
kl.login.urlWhitelist[0]= /login/**
kl.login.urlWhitelist[1]= /getRandom
kl.login.urlWhitelist[2]= /**


# license
kl.license.license =
kl.license.device =
#\u6307\u5B9A\u7684\u90E8\u7F72\u73AF\u5883\u53C2\u6570,\u5F53\u8BBE\u7F6EUNSPECIFIED\u65F6\u7CFB\u7EDF\u81EA\u52A8\u5224\u65AD\u90E8\u7F72\u73AF\u5883
#\u53EF\u9009UNSPECIFIED/OLD/FILE/WINDOWS/LINUX/DOCKER/GATEWAY
kl.license.specifiedDeployEnv = UNSPECIFIED
#\u5907\u7528\u7684\u90E8\u7F72\u73AF\u5883\u53C2\u6570,\u5F53specifiedDeployEnv\u65E0\u6548\u65F6\u4F7F\u7528
kl.license.standbyDeployEnv = FILE

# sysInfo
## \u7CFB\u7EDF\u540D\u79F0
kl.sysInfo.name=
## \u7248\u672C
kl.sysInfo.version=
## \u7248\u672C\u8865\u4E01
kl.sysInfo.packVersion=
## \u5B9A\u5236\u7248\u672C
kl.sysInfo.customizedVersion=
## \u7535\u5B50\u90AE\u4EF6
kl.sysInfo.email=
## \u7CFB\u7EDFLOGO
kl.sysInfo.logo=
## \u662F\u5426\u662F\u767B\u5F55\u6A21\u5F0F
kl.sysInfo.loginMode=
## \u662F\u5426\u663E\u793A\u5FAE\u8F6FCSP\u8BBE\u5907
kl.sysInfo.showCSP=
## \u672C\u7EA7\u8EAB\u4EFD\u8BC1\u4E66
kl.sysInfo.cert.idCert=MIIBjjCCATKgAwIBAgIQMXA9kPVZLAKGvvV7aFMLlDAMBggqgRzPVQGDdQUAMCUxFjAUBgNVBAMMDVNtMl9DQV9pZENlcnQxCzAJBgNVBAYTAkNOMB4XDTIyMTExODA5MDQ0OFoXDTQyMTExMzA5MDQ0OFowJTEWMBQGA1UEAwwNU20yX0NBX2lkQ2VydDELMAkGA1UEBhMCQ04wWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAAQDgGi4XpOgSnAszOhtNaxsU971Ex1c5KOZQgYVa/dNfShdPIyP3+9+MYZ1kbYKj+d/ltexGm4jDoWry1N7CH87o0IwQDAPBgNVHRMBAf8EBTADAQH/MA4GA1UdDwEB/wQEAwIBhjAdBgNVHQ4EFgQU6G6IRcYXspoJgsYwsRhcafCLQuQwDAYIKoEcz1UBg3UFAANIADBFAiEAoCkLJHV6igk+5WCGXG1WV7j2DDkz0mpIbm4a7+9v8XsCIEIhqijvEfv7g0NFm5L81jccC7vy6IMVmzQZ6JmAe6Q7
## \u6839\u8EAB\u4EFD\u8BC1\u4E66
kl.sysInfo.cert.rootIdCert=
## \u4E00\u7EA7\u8EAB\u4EFD\u8BC1\u4E66
kl.sysInfo.cert.firstIdCert=
## \u8EAB\u4EFD\u8BC1\u4E66\u8BF7\u6C42
kl.sysInfo.cert.idCertReq=
## \u672C\u7EA7\u7AD9\u70B9\u8BC1\u4E66
kl.sysInfo.cert.siteCert=
## \u6839\u7AD9\u70B9\u8BC1\u4E66
kl.sysInfo.cert.rootSiteCert=
## \u4E00\u7EA7\u7AD9\u70B9\u8BC1\u4E66
kl.sysInfo.cert.firstSiteCert=
## \u7AD9\u70B9\u8BC1\u4E66\u8BF7\u6C42
kl.sysInfo.cert.siteCertReq=
kl.sysInfo.environmentSwitchId=test

# timer
kl.timer.enabled=true
kl.timer.type=spring
kl.timer.executor.coreSize=5
kl.timer.spring.coreSize=5
kl.timer.spring.awaitTerminationSeconds=60
kl.timer.xxlJob.adminAddresses=http://***********:30614/xxl-job-admin
kl.timer.xxlJob.accessToken=
kl.timer.xxlJob.appName=KM-Service
kl.timer.xxlJob.ip=
kl.timer.xxlJob.address=
kl.timer.xxlJob.port=58101
kl.timer.xxlJob.logPath=
kl.timer.xxlJob.logRetentionDays=30

kl.datasource.switchable[0].sharding.databaseDiscoveryEnabled=false
kl.datasource.switchable[0].sharding.databaseShardingAlgorithm=kl.nbase.db.support.sharding.algorithm.DefaultDatabaseShardingAlgorithm
kl.datasource.switchable[0].sharding.databaseShardingColumn=id
kl.datasource.switchable[0].sharding.readwriteSplittingEnabled=false
kl.datasource.switchable[0].sharding.shardingEnabled=true
kl.datasource.switchable[0].sharding.tables[0].dataNodes=ds0.t_key_current_$->{0..1}
kl.datasource.switchable[0].sharding.tables[0].keyGenerateAlgorithm=kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
kl.datasource.switchable[0].sharding.tables[0].keyGenerateColumn=id
kl.datasource.switchable[0].sharding.tables[0].name=t_key_current
kl.datasource.switchable[0].sharding.tables[0].shardingAlgorithm=kl.nbase.db.support.sharding.algorithm.HashModShardingAlgorithm
kl.datasource.switchable[0].sharding.tables[0].shardingColumn=id
kl.datasource.switchable[0].sharding.tables[1].dataNodes=ds1.t_key_history_$->{0..1}
kl.datasource.switchable[0].sharding.tables[1].keyGenerateAlgorithm=kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
kl.datasource.switchable[0].sharding.tables[1].keyGenerateColumn=id
kl.datasource.switchable[0].sharding.tables[1].name=t_key_history
kl.datasource.switchable[0].sharding.tables[1].shardingAlgorithm=kl.nbase.db.support.sharding.algorithm.HashModShardingAlgorithm
kl.datasource.switchable[0].sharding.tables[1].shardingColumn=id
kl.datasource.switchable[0].slot.datasource[0].driverClassName=com.mysql.cj.jdbc.Driver
kl.datasource.switchable[0].slot.datasource[0].filters=stat,slf4j
kl.datasource.switchable[0].slot.datasource[0].initialSize=1
kl.datasource.switchable[0].slot.datasource[0].logSlowSql=true
kl.datasource.switchable[0].slot.datasource[0].maxActive=20
kl.datasource.switchable[0].slot.datasource[0].maxWait=60000
kl.datasource.switchable[0].slot.datasource[0].minEvictableIdleTimeMillis=300000
kl.datasource.switchable[0].slot.datasource[0].minIdle=1
kl.datasource.switchable[0].slot.datasource[0].name=ds0
kl.datasource.switchable[0].slot.datasource[0].password=root123
kl.datasource.switchable[0].slot.datasource[0].poolPreparedStatements=false
kl.datasource.switchable[0].slot.datasource[0].slowSqlMillis=3000
kl.datasource.switchable[0].slot.datasource[0].testOnBorrow=false
kl.datasource.switchable[0].slot.datasource[0].testOnReturn=false
kl.datasource.switchable[0].slot.datasource[0].testWhileIdle=true
kl.datasource.switchable[0].slot.datasource[0].timeBetweenEvictionRunsMillis=60000
kl.datasource.switchable[0].slot.datasource[0].url=***************************************************************************************************************************************************************************************
kl.datasource.switchable[0].slot.datasource[0].username=root
kl.datasource.switchable[0].slot.datasource[0].validationQuery=SELECT 1 FROM DUAL
kl.datasource.switchable[0].slot.datasource[1].driverClassName=com.mysql.cj.jdbc.Driver
kl.datasource.switchable[0].slot.datasource[1].filters=stat,slf4j
kl.datasource.switchable[0].slot.datasource[1].initialSize=1
kl.datasource.switchable[0].slot.datasource[1].logSlowSql=true
kl.datasource.switchable[0].slot.datasource[1].maxActive=20
kl.datasource.switchable[0].slot.datasource[1].maxWait=60000
kl.datasource.switchable[0].slot.datasource[1].minEvictableIdleTimeMillis=300000
kl.datasource.switchable[0].slot.datasource[1].minIdle=1
kl.datasource.switchable[0].slot.datasource[1].name=ds1
kl.datasource.switchable[0].slot.datasource[1].password=root123
kl.datasource.switchable[0].slot.datasource[1].poolPreparedStatements=false
kl.datasource.switchable[0].slot.datasource[1].slowSqlMillis=3000
kl.datasource.switchable[0].slot.datasource[1].testOnBorrow=false
kl.datasource.switchable[0].slot.datasource[1].testOnReturn=false
kl.datasource.switchable[0].slot.datasource[1].testWhileIdle=true
kl.datasource.switchable[0].slot.datasource[1].timeBetweenEvictionRunsMillis=60000
kl.datasource.switchable[0].slot.datasource[1].url=*******************************************************************************************************************************************************************************************
kl.datasource.switchable[0].slot.datasource[1].username=root
kl.datasource.switchable[0].slot.datasource[1].validationQuery=SELECT 1 FROM DUAL
kl.datasource.switchable[0].slot.slotName=test
kl.datasource.switchable[1].sharding.databaseDiscoveryEnabled=false
kl.datasource.switchable[1].sharding.databaseShardingAlgorithm=kl.nbase.db.support.sharding.algorithm.DefaultDatabaseShardingAlgorithm
kl.datasource.switchable[1].sharding.databaseShardingColumn=id
kl.datasource.switchable[1].sharding.readwriteSplittingEnabled=false
kl.datasource.switchable[1].sharding.shardingEnabled=true
kl.datasource.switchable[1].sharding.tables[0].dataNodes=ds0.t_key_current_$->{0..1}
kl.datasource.switchable[1].sharding.tables[0].keyGenerateAlgorithm=kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
kl.datasource.switchable[1].sharding.tables[0].keyGenerateColumn=id
kl.datasource.switchable[1].sharding.tables[0].name=t_key_current
kl.datasource.switchable[1].sharding.tables[0].shardingAlgorithm=kl.nbase.db.support.sharding.algorithm.HashModShardingAlgorithm
kl.datasource.switchable[1].sharding.tables[0].shardingColumn=id
kl.datasource.switchable[1].sharding.tables[1].dataNodes=ds1.t_key_history_$->{0..1}
kl.datasource.switchable[1].sharding.tables[1].keyGenerateAlgorithm=kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
kl.datasource.switchable[1].sharding.tables[1].keyGenerateColumn=id
kl.datasource.switchable[1].sharding.tables[1].name=t_key_history
kl.datasource.switchable[1].sharding.tables[1].shardingAlgorithm=kl.nbase.db.support.sharding.algorithm.HashModShardingAlgorithm
kl.datasource.switchable[1].sharding.tables[1].shardingColumn=id
kl.datasource.switchable[1].slot.datasource[0].driverClassName=com.mysql.cj.jdbc.Driver
kl.datasource.switchable[1].slot.datasource[0].filters=stat,slf4j
kl.datasource.switchable[1].slot.datasource[0].initialSize=1
kl.datasource.switchable[1].slot.datasource[0].logSlowSql=true
kl.datasource.switchable[1].slot.datasource[0].maxActive=20
kl.datasource.switchable[1].slot.datasource[0].maxWait=60000
kl.datasource.switchable[1].slot.datasource[0].minEvictableIdleTimeMillis=300000
kl.datasource.switchable[1].slot.datasource[0].minIdle=1
kl.datasource.switchable[1].slot.datasource[0].name=ds0
kl.datasource.switchable[1].slot.datasource[0].password=root123
kl.datasource.switchable[1].slot.datasource[0].poolPreparedStatements=false
kl.datasource.switchable[1].slot.datasource[0].slowSqlMillis=3000
kl.datasource.switchable[1].slot.datasource[0].testOnBorrow=false
kl.datasource.switchable[1].slot.datasource[0].testOnReturn=false
kl.datasource.switchable[1].slot.datasource[0].testWhileIdle=true
kl.datasource.switchable[1].slot.datasource[0].timeBetweenEvictionRunsMillis=60000
kl.datasource.switchable[1].slot.datasource[0].url=****************************************************************************************************************************************************************************************
kl.datasource.switchable[1].slot.datasource[0].username=root
kl.datasource.switchable[1].slot.datasource[0].validationQuery=SELECT 1 FROM DUAL
kl.datasource.switchable[1].slot.datasource[1].driverClassName=com.mysql.cj.jdbc.Driver
kl.datasource.switchable[1].slot.datasource[1].filters=stat,slf4j
kl.datasource.switchable[1].slot.datasource[1].initialSize=1
kl.datasource.switchable[1].slot.datasource[1].logSlowSql=true
kl.datasource.switchable[1].slot.datasource[1].maxActive=20
kl.datasource.switchable[1].slot.datasource[1].maxWait=60000
kl.datasource.switchable[1].slot.datasource[1].minEvictableIdleTimeMillis=300000
kl.datasource.switchable[1].slot.datasource[1].minIdle=1
kl.datasource.switchable[1].slot.datasource[1].name=ds1
kl.datasource.switchable[1].slot.datasource[1].password=root123
kl.datasource.switchable[1].slot.datasource[1].poolPreparedStatements=false
kl.datasource.switchable[1].slot.datasource[1].slowSqlMillis=3000
kl.datasource.switchable[1].slot.datasource[1].testOnBorrow=false
kl.datasource.switchable[1].slot.datasource[1].testOnReturn=false
kl.datasource.switchable[1].slot.datasource[1].testWhileIdle=true
kl.datasource.switchable[1].slot.datasource[1].timeBetweenEvictionRunsMillis=60000
kl.datasource.switchable[1].slot.datasource[1].url=****************************************************************************************************************************************************************************************
kl.datasource.switchable[1].slot.datasource[1].username=root
kl.datasource.switchable[1].slot.datasource[1].validationQuery=SELECT 1 FROM DUAL
kl.datasource.switchable[1].slot.slotName=pord

