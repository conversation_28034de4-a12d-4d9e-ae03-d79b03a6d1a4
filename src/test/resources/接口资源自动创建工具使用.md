# 接口权限自动创建工具使用

## 1. 目的
- 自动创建将接口权限信息写入数据库，避免手动创建的繁琐。

## 2. 原理
- 通过读取swagger接口的属性和注释信息，自动生成请求方法、请求路径、接口描述、接口描述国际化键等信息。
- 只需简单的配置即可完成接口权限的自动创建。
- 创建的权限信息会写入数据库，并生成权限信息文件。
- 会跳过已经存在的权限信息，避免重复创建。

## 3. 使用步骤

1. 将`test`目录下的[PermissionToolsController.java](../java/kl/npki/base/management/PermissionToolsController.java)拷贝到[controller](../../main/java/kl/npki/base/management/controller)目录下

2. 暂时放行全部接口
    - 在[CustomAuthorizationManager.java](../../main/java/kl/npki/base/management/security/CustomAuthorizationManager.java)的`check`方法的开头添加以下代码：
    ```java
       if(true) {
           return new AuthorizationDecision(true);
       }
    ```
3. 访问接口
    - 访问[http://localhost:port/mgmt/v1/permissions/tools/import-from-swagger]
    - 系统会自动添加新增的接口权限信息到数据库中
    - 执行完之后可以选择删除上一步新增的代码重启项目

4. 将接口资源合理地分配到菜单下
    - 登录安全管理员
    - 进入【权限管理】
    - 新增的接口会显示在菜单的同一层级下，点击【编辑】，选择正确的`父级资源`
    - 如果一个接口同时属于多个菜单，可以点击【克隆】，只需要修改`父级资源`即可，`资源名称`和`资源URL`保持不变（通过资源名称和资源URL定位已经存在的接口资源，拷贝资源名称的国际化键，如果修改了资源名称可能需要添加修改国际化键）

5. 给角色分配权限
    - 进入【角色管理】
    - 点击【编辑权限】按钮，选择需要分配的接口资源
    - 点击【保存】按钮
6. 导出数据，更新预置sql
    - 以上三步的数据产生的数据都是增量添加的数据，可以根据时间线将数据导出，并更新预置sql
 
