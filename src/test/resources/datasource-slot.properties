
kl.slot[0].databaseName=test1

kl.slot[0].datasource[0].name=ds0
kl.slot[0].datasource[0].url=jdbc:mysql://10.0.244\
  .77:3306/001gqtest?useUnicode=true&characterEncoding=UTF-8&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode\
  =false&serverTimezone=Asia/Shanghai&useSSL=false
kl.slot[0].datasource[0].username=root
kl.slot[0].datasource[0].password=root123
kl.slot[0].datasource[0].driverClassName=com.mysql.cj.jdbc.Driver
kl.slot[0].datasource[0].initialSize=1
kl.slot[0].datasource[0].minIdle=1
kl.slot[0].datasource[0].maxActive=20
kl.slot[0].datasource[0].maxWait=60000
kl.slot[0].datasource[0].timeBetweenEvictionRunsMillis=60000
kl.slot[0].datasource[0].minEvictableIdleTimeMillis=300000
kl.slot[0].datasource[0].validationQuery=SELECT 1 FROM DUAL
kl.slot[0].datasource[0].testWhileIdle=true
kl.slot[0].datasource[0].testOnBorrow=false
kl.slot[0].datasource[0].testOnReturn=false
kl.slot[0].datasource[0].poolPreparedStatements=false
kl.slot[0].datasource[0].filters=stat,slf4j
kl.slot[0].datasource[0].logSlowSql=true
kl.slot[0].datasource[0].slowSqlMillis=3000

kl.slot[0].datasource[1].name=hds1
kl.slot[0].datasource[1].url=jdbc:mysql://10.0.244\
  .77:3306/002gqtest?useUnicode=true&characterEncoding=UTF-8&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode\
  =false&serverTimezone=Asia/Shanghai&useSSL=false
kl.slot[0].datasource[1].username=root
kl.slot[0].datasource[1].password=root123
kl.slot[0].datasource[1].driverClassName=com.mysql.cj.jdbc.Driver
kl.slot[0].datasource[1].initialSize=1
kl.slot[0].datasource[1].minIdle=1
kl.slot[0].datasource[1].maxActive=20
kl.slot[0].datasource[1].maxWait=60000
kl.slot[0].datasource[1].timeBetweenEvictionRunsMillis=60000
kl.slot[0].datasource[1].minEvictableIdleTimeMillis=300000
kl.slot[0].datasource[1].validationQuery=SELECT 1 FROM DUAL
kl.slot[0].datasource[1].testWhileIdle=true
kl.slot[0].datasource[1].testOnBorrow=false
kl.slot[0].datasource[1].testOnReturn=false
kl.slot[0].datasource[1].poolPreparedStatements=false
kl.slot[0].datasource[1].filters=stat,slf4j
kl.slot[0].datasource[1].logSlowSql=true
kl.slot[0].datasource[1].slowSqlMillis=3000

kl.slot[0].shardingEnabled=true
# 分库的分片键，全局统一，无法为单个表设置单独的键
kl.slot[0].databaseShardingColumn=id
# 对应shardingColumn的分片算法，默认为kl.nbase.db.support.sharding.algorithm.DefaultDatabaseShardingAlgorithm
kl.slot[0].databaseShardingAlgorithm=kl.nbase.db.support.sharding.algorithm.TenancyDatabaseShardingAlgorithm
# 需要分表的表名
kl.slot[0].tables[0].name=country
# 需要自动生成键值的字段
kl.slot[0].tables[0].keyGenerateColumn=id
# 对应keyGenerateColumn的键值生成算法，默认为kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
kl.slot[0].tables[0].keyGenerateAlgorithm=kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
# 分表的分片键
kl.slot[0].tables[0].shardingColumn=id
# 对应shardingColumn的分片算法，默认为kl.nbase.db.support.sharding.algorithm.DefaultTableShardingAlgorithm
kl.slot[0].tables[0].shardingAlgorithm=kl.nbase.db.support.sharding.algorithm.DefaultTableShardingAlgorithm
# 需要分片的完整分库分表节点表达式
kl.slot[0].tables[0].dataNodes=hds1.country$->{0..1}

# 需要分表的表名
kl.slot[0].tables[1].name=city
# 需要自动生成键值的字段
kl.slot[0].tables[1].keyGenerateColumn=id
# 对应keyGenerateColumn的键值生成算法，默认为kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
kl.slot[0].tables[1].keyGenerateAlgorithm=kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
# 分表的分片键
kl.slot[0].tables[1].shardingColumn=id
# 对应shardingColumn的分片算法，默认为kl.nbase.db.support.sharding.algorithm.DefaultTableShardingAlgorithm
kl.slot[0].tables[1].shardingAlgorithm=kl.nbase.db.support.sharding.algorithm.DefaultTableShardingAlgorithm
# 需要分片的完整分库分表节点表达式
kl.slot[0].tables[1].dataNodes=ds0.city$->{0..1}

kl.slot[0].readwriteSplittingEnabled=false

kl.slot[0].databaseDiscoveryEnabled=false



kl.slot[1].databaseName=test2
kl.slot[1].datasource[0].name=ds0
kl.slot[1].datasource[0].url=jdbc:mysql://10.0.244\
  .77:3306/001gqtest?useUnicode=true&characterEncoding=UTF-8&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode\
  =false&serverTimezone=Asia/Shanghai&useSSL=false
kl.slot[1].datasource[0].username=root
kl.slot[1].datasource[0].password=root123
kl.slot[1].datasource[0].driverClassName=com.mysql.cj.jdbc.Driver
kl.slot[1].datasource[0].initialSize=1
kl.slot[1].datasource[0].minIdle=1
kl.slot[1].datasource[0].maxActive=20
kl.slot[1].datasource[0].maxWait=60000
kl.slot[1].datasource[0].timeBetweenEvictionRunsMillis=60000
kl.slot[1].datasource[0].minEvictableIdleTimeMillis=300000
kl.slot[1].datasource[0].validationQuery=SELECT 1 FROM DUAL
kl.slot[1].datasource[0].testWhileIdle=true
kl.slot[1].datasource[0].testOnBorrow=false
kl.slot[1].datasource[0].testOnReturn=false
kl.slot[1].datasource[0].poolPreparedStatements=false
kl.slot[1].datasource[0].filters=stat,slf4j
kl.slot[1].datasource[0].logSlowSql=true
kl.slot[1].datasource[0].slowSqlMillis=3000

kl.slot[1].datasource[1].name=hds1
kl.slot[1].datasource[1].url=jdbc:mysql://10.0.244\
  .77:3306/002gqtest?useUnicode=true&characterEncoding=UTF-8&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode\
  =false&serverTimezone=Asia/Shanghai&useSSL=false
kl.slot[1].datasource[1].username=root
kl.slot[1].datasource[1].password=root123
kl.slot[1].datasource[1].driverClassName=com.mysql.cj.jdbc.Driver
kl.slot[1].datasource[1].initialSize=1
kl.slot[1].datasource[1].minIdle=1
kl.slot[1].datasource[1].maxActive=20
kl.slot[1].datasource[1].maxWait=60000
kl.slot[1].datasource[1].timeBetweenEvictionRunsMillis=60000
kl.slot[1].datasource[1].minEvictableIdleTimeMillis=300000
kl.slot[1].datasource[1].validationQuery=SELECT 1 FROM DUAL
kl.slot[1].datasource[1].testWhileIdle=true
kl.slot[1].datasource[1].testOnBorrow=false
kl.slot[1].datasource[1].testOnReturn=false
kl.slot[1].datasource[1].poolPreparedStatements=false
kl.slot[1].datasource[1].filters=stat,slf4j
kl.slot[1].datasource[1].logSlowSql=true
kl.slot[1].datasource[1].slowSqlMillis=3000

kl.slot[1].shardingEnabled=true
# 分库的分片键，全局统一，无法为单个表设置单独的键
kl.slot[1].databaseShardingColumn=id
# 对应shardingColumn的分片算法，默认为kl.nbase.db.support.sharding.algorithm.DefaultDatabaseShardingAlgorithm
kl.slot[1].databaseShardingAlgorithm=kl.nbase.db.support.sharding.algorithm.TenancyDatabaseShardingAlgorithm
# 需要分表的表名
kl.slot[1].tables[0].name=country
# 需要自动生成键值的字段
kl.slot[1].tables[0].keyGenerateColumn=id
# 对应keyGenerateColumn的键值生成算法，默认为kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
kl.slot[1].tables[0].keyGenerateAlgorithm=kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
# 分表的分片键
kl.slot[1].tables[0].shardingColumn=id
# 对应shardingColumn的分片算法，默认为kl.nbase.db.support.sharding.algorithm.DefaultTableShardingAlgorithm
kl.slot[1].tables[0].shardingAlgorithm=kl.nbase.db.support.sharding.algorithm.DefaultTableShardingAlgorithm
# 需要分片的完整分库分表节点表达式
kl.slot[1].tables[0].dataNodes=hds1.country$->{0..1}

# 需要分表的表名
kl.slot[1].tables[1].name=country
# 需要自动生成键值的字段
kl.slot[1].tables[1].keyGenerateColumn=id
# 对应keyGenerateColumn的键值生成算法，默认为kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
kl.slot[1].tables[1].keyGenerateAlgorithm=kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
# 分表的分片键
kl.slot[1].tables[1].shardingColumn=id
# 对应shardingColumn的分片算法，默认为kl.nbase.db.support.sharding.algorithm.DefaultTableShardingAlgorithm
kl.slot[1].tables[1].shardingAlgorithm=kl.nbase.db.support.sharding.algorithm.DefaultTableShardingAlgorithm
# 需要分片的完整分库分表节点表达式
kl.slot[1].tables[1].dataNodes=ds0.country$->{0..1}

kl.slot[1].readwriteSplittingEnabled=false

kl.slot[1].databaseDiscoveryEnabled=false

