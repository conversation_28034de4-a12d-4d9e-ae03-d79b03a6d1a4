# key
kl.km.key.cacheCheckFrequency = 60
kl.km.key.cacheSize = 2
kl.km.key.cacheSupplyThreshold = 1
kl.km.key.keyUniquenessCheck = true
kl.km.key.keyLimits[0].enable = true
kl.km.key.keyLimits[0].keyType = RSA_2048
kl.km.key.keyLimits[0].limit = 100
kl.km.key.keyLimits[0].keyGenCronExpression = 0 0 0/1 * * ?
kl.km.key.keyLimits[1].enable = true
kl.km.key.keyLimits[1].keyType = RSA_4096
kl.km.key.keyLimits[1].limit = 100
kl.km.key.keyLimits[1].keyGenCronExpression = 0 0 0/1 * * ?
kl.km.key.keyLimits[2].enable = true
kl.km.key.keyLimits[2].keyType = SM2
kl.km.key.keyLimits[2].limit = 100
kl.km.key.keyLimits[2].keyGenCronExpression = 0 0 0/1 * * ?

# archive
kl.km.archive.lateArchiveTime = 365
kl.km.archive.archivePageThreshold = 200
kl.km.archive.archiveCronExpression = 0 0 0 * * ?

# kmConf
kl.km.conf.signResponse=
kl.km.conf.checkSnExist=
kl.km.conf.restoreCA=
kl.km.conf.sksKeyValidityYears=
kl.km.conf.reduceRealTime=
kl.km.conf.reduceExecuteInterval=
kl.km.conf.performanceTest=
kl.km.conf.doRevokeCA=
kl.km.conf.b64RootCert=
kl.km.conf.verifyRequest=

# mainKey
kl.km.mainKey.mainKeyAlgo = SM4_ECB_P5
# \u6570\u636E\u5E93\u4E2D\u5185\u7F6E\u7684\u4E3B\u5BC6\u94A5ID
kl.km.mainKey.mainKeyId = 1628952377385156610

## \u672C\u7EA7\u8EAB\u4EFD\u8BC1\u4E66
kl.base.cert.idCert=MIICQDCCAeSgAwIBAgIQMetscJvxfs6SSSbQ4lRcMTAMBggqgRzPVQGDdQUAMFgxCzAJBgNVBAYTAkNOMQswCQYDVQQIDAJzaDELMAkGA1UEBwwCc2gxDTALBgNVBAoMBGtvYWwxDTALBgNVBAsMBGtvYWwxETAPBgNVBAMMCG5wa2l0ZXN0MCAXDTIzMDIxNjA4MzY0OFoYDzIyOTYxMjAxMDgzNjQ4WjBaMQswCQYDVQQGEwJDTjELMAkGA1UECAwCc2gxCzAJBgNVBAcMAnNoMQ0wCwYDVQQKDARrb2FsMQ0wCwYDVQQLDARrb2FsMRMwEQYDVQQDDApucGtpSWRDZXJ0MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAETW8AGUw/L8CVSDo/hvChVocTnw2RB+vsrqGZnb4SPCbv6sxvBc7yM+caSUxqA2AfrxRs0Bw202gZVAvRJqqXt6OBiTCBhjAMBgNVHRMBAf8EAjAAMA4GA1UdDwEB/wQEAwIGwDATBgNVHSUEDDAKBggrBgEFBQcDAjARBglghkgBhvhCAQEEBAMCB4AwHQYDVR0OBBYEFOSYauWM9O4muUTxFv0sCNkTnTN3MB8GA1UdIwQYMBaAFNWYLmKg9lLVhWtb6j9BLRkKDcOLMAwGCCqBHM9VAYN1BQADSAAwRQIgFM+T0cMV+TMDFoxCq6PgNRu2HPRfVnF7OqRTlM0eWQQCIQDxod2JPjxfyFBXvE1mhJHFHR8hr1nM3nfI/ozlIhqvDw==
## \u8EAB\u4EFD\u8BC1\u4E66\u8BF7\u6C42
kl.base.cert.idCertReq=
## \u672C\u7EA7\u7AD9\u70B9\u8BC1\u4E66
kl.base.cert.ssLCert=MIICWzCCAf+gAwIBAgIQMVZVdUnKcA1+vaAN+wm3nDAMBggqgRzPVQGDdQUAMFgxCzAJBgNVBAYTAkNOMQswCQYDVQQIDAJzaDELMAkGA1UEBwwCc2gxDTALBgNVBAoMBGtvYWwxDTALBgNVBAsMBGtvYWwxETAPBgNVBAMMCG5wa2l0ZXN0MCAXDTIzMDIxNjA4MzkyMVoYDzIwNTAwNzA0MDgzOTIxWjBcMQswCQYDVQQGEwJDTjELMAkGA1UECAwCc2gxCzAJBgNVBAcMAnNoMQ0wCwYDVQQKDARrb2FsMQ0wCwYDVQQLDARrb2FsMRUwEwYDVQQDDAxucGtpU2l0ZUNlcnQwWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAAQDgGi4XpOgSnAszOhtNaxsU971Ex1c5KOZQgYVa/dNfShdPIyP3+9+MYZ1kbYKj+d/ltexGm4jDoWry1N7CH87o4GiMIGfMAwGA1UdEwEB/wQCMAAwDgYDVR0PAQH/BAQDAgXgMBMGA1UdJQQMMAoGCCsGAQUFBwMBMBcGA1UdEQQQMA6CDG5wa2lTaXRlQ2VydDARBglghkgBhvhCAQEEBAMCBkAwHQYDVR0OBBYEFOhuiEXGF7KaCYLGMLEYXGnwi0LkMB8GA1UdIwQYMBaAFNWYLmKg9lLVhWtb6j9BLRkKDcOLMAwGCCqBHM9VAYN1BQADSAAwRQIhAJJszLref1WIhgjFiFMh41rKe1PUF6g2y5Cc6CLVUV/ZAiAwThNHQ0STCaoKGiKV8rMtBI80col5FgAd1XCjtujHIA==
## \u7AD9\u70B9\u8BC1\u4E66\u8BF7\u6C42
kl.base.cert.siteCertReq=

# em
kl.base.em.emConf[0].enabled = true
kl.base.em.emConf[0].id = 0
kl.base.em.emConf[0].name = \u6587\u4EF6\u5BC6\u7801\u673A
kl.base.em.emConf[0].instanceName = RSA
kl.base.em.emConf[0].type = FILE4096
kl.base.em.emConf[0].ip = 127.0.0.1
kl.base.em.emConf[0].port = 8017
kl.base.em.emConf[0].username = user
kl.base.em.emConf[0].password = 12345678
kl.base.em.emConf[0].keyBeginIndex = 1
kl.base.em.emConf[0].keyEndIndex = 5
kl.base.em.emConf[0].sm3Uid =
kl.base.em.emConf[0].storePath = ./src/test/resources/
kl.base.em.emConf[1].enabled = true
kl.base.em.emConf[1].id = 1
kl.base.em.emConf[1].name = \u6587\u4EF6\u5BC6\u7801\u673A
kl.base.em.emConf[1].instanceName = SM2
kl.base.em.emConf[1].type = SM2FILE
kl.base.em.emConf[1].ip = 127.0.0.1
kl.base.em.emConf[1].port = 8017
kl.base.em.emConf[1].username = user
kl.base.em.emConf[1].password = 12345678
kl.base.em.emConf[1].keyBeginIndex = 1
kl.base.em.emConf[1].keyEndIndex = 5
kl.base.em.emConf[1].sm3Uid =
kl.base.em.emConf[1].storePath = ./src/test/resources/


kl.base.datasource.sharding.databaseDiscoveryEnabled=true
# \u542F\u7528\u591A\u4E3B\u591A\u4ECE
kl.base.datasource.sharding.multiMastersAndSlavesEnabled=true
kl.base.datasource.sharding.databaseShardingAlgorithm=kl.nbase.db.support.sharding.algorithm.DefaultDatabaseShardingAlgorithm
kl.base.datasource.sharding.databaseShardingColumn=id
# \u5206\u5E93\u5206\u8868
kl.base.datasource.sharding.shardingEnabled=false
# \u5206\u8868\u4E0D\u5206\u5E93
kl.base.datasource.sharding.tableShardingEnabled=true
# \u8BFB\u5199\u5206\u79BB
kl.base.datasource.sharding.readWriteEnabled=true
kl.base.datasource.sharding.props.sql-show=true
# \u5C06\u8BFB\u5199\u5206\u79BB\u7684\u6570\u636E\u6E90\u770B\u4F5C\u4E00\u4E2A\u903B\u8F91\u6570\u636E\u6E90\uFF0C\u540D\u79F0\u4E3Ads0\uFF0C\u540E\u9762\u7684\u5206\u8868\u89C4\u5219\u90FD\u4EE5ds0\u4F5C\u4E3A\u5E93\u540D
kl.base.datasource.sharding.readWrite[0].name=ds0
kl.base.datasource.sharding.readWrite[0].props.write-data-source-name=master
kl.base.datasource.sharding.readWrite[0].props.read-data-source-names=slave
kl.base.datasource.sharding.readWrite[0].type=STATIC
kl.base.datasource.sharding.readWrite[0].transactionalReadQueryStrategy=PRIMARY
kl.base.datasource.sharding.readWrite[0].loadBalancerName=round_robin
kl.base.datasource.sharding.loadBalancers.round_robin.type=ROUND_ROBIN

kl.base.datasource.sharding.tables[0].dataNodes=ds0.t_key_current_$->{0..2}
kl.base.datasource.sharding.tables[0].keyGenerateAlgorithm=kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
kl.base.datasource.sharding.tables[0].keyGenerateColumn=id
kl.base.datasource.sharding.tables[0].name=t_key_current
kl.base.datasource.sharding.tables[0].shardingAlgorithm=kl.nbase.db.support.sharding.algorithm.HashModShardingAlgorithm
kl.base.datasource.sharding.tables[0].shardingColumn=id


kl.base.datasource.slot.datasource[0].driverClassName=com.mysql.cj.jdbc.Driver
kl.base.datasource.slot.datasource[0].filters=stat,slf4j
kl.base.datasource.slot.datasource[0].initialSize=1
kl.base.datasource.slot.datasource[0].logSlowSql=true
kl.base.datasource.slot.datasource[0].maxActive=20
kl.base.datasource.slot.datasource[0].maxWait=10000
kl.base.datasource.slot.datasource[0].minEvictableIdleTimeMillis=300000
kl.base.datasource.slot.datasource[0].minIdle=1
kl.base.datasource.slot.datasource[0].name=master_0
kl.base.datasource.slot.datasource[0].password=*()D1546040776q
kl.base.datasource.slot.datasource[0].poolPreparedStatements=false
kl.base.datasource.slot.datasource[0].slowSqlMillis=3000
kl.base.datasource.slot.datasource[0].testOnBorrow=false
kl.base.datasource.slot.datasource[0].testOnReturn=false
kl.base.datasource.slot.datasource[0].testWhileIdle=true
kl.base.datasource.slot.datasource[0].timeBetweenEvictionRunsMillis=60000
# \u5982\u679C\u9700\u8981\u8FDB\u884C\u521D\u59CB\u5316 \uFF0C\u52A0\u4E0A INIT=RUNSCRIPT FROM 'classpath:db/h2/h2_ddl.sql'
kl.base.datasource.slot.datasource[0].url=jdbc\:mysql\://**************\:3307/nkm0223?useUnicode\=true&characterEncoding\=UTF-8&useJDBCCompliantTimezoneShift\=true&useLegacyDatetimeCode\=false&serverTimezone\=GMT%2B8&useSSL\=false&allowPublicKeyRetrieval\=true
kl.base.datasource.slot.datasource[0].username=root
kl.base.datasource.slot.datasource[0].validationQuery=SELECT 1 FROM DUAL
kl.base.datasource.slot.datasource[0].maxEvictableIdleTimeMillis=300000
kl.base.datasource.slot.datasource[0].useUnfairLock=true
kl.base.datasource.slot.datasource[0].keepAlive=false
kl.base.datasource.slot.datasource[0].maxOpenPreparedStatements=0

kl.base.datasource.slot.datasource[1].driverClassName=com.mysql.cj.jdbc.Driver
kl.base.datasource.slot.datasource[1].filters=stat,slf4j
kl.base.datasource.slot.datasource[1].initialSize=1
kl.base.datasource.slot.datasource[1].logSlowSql=true
kl.base.datasource.slot.datasource[1].maxActive=20
kl.base.datasource.slot.datasource[1].maxWait=10000
kl.base.datasource.slot.datasource[1].minEvictableIdleTimeMillis=300000
kl.base.datasource.slot.datasource[1].minIdle=1
kl.base.datasource.slot.datasource[1].name=slave_1
kl.base.datasource.slot.datasource[1].password=*()D1546040776q
kl.base.datasource.slot.datasource[1].poolPreparedStatements=false
kl.base.datasource.slot.datasource[1].slowSqlMillis=3000
kl.base.datasource.slot.datasource[1].testOnBorrow=false
kl.base.datasource.slot.datasource[1].testOnReturn=false
kl.base.datasource.slot.datasource[1].testWhileIdle=true
kl.base.datasource.slot.datasource[1].timeBetweenEvictionRunsMillis=60000
# \u5982\u679C\u9700\u8981\u8FDB\u884C\u521D\u59CB\u5316 \uFF0C\u52A0\u4E0A INIT=RUNSCRIPT FROM 'classpath:db/h2/h2_ddl.sql'
kl.base.datasource.slot.datasource[1].url=jdbc\:mysql\://**************\:3308/nkm0223?useUnicode\=true&characterEncoding\=UTF-8&useJDBCCompliantTimezoneShift\=true&useLegacyDatetimeCode\=false&serverTimezone\=GMT%2B8&useSSL\=false&allowPublicKeyRetrieval\=true
kl.base.datasource.slot.datasource[1].username=root
kl.base.datasource.slot.datasource[1].validationQuery=SELECT 1 FROM DUAL
kl.base.datasource.slot.datasource[1].maxEvictableIdleTimeMillis=300000
kl.base.datasource.slot.datasource[1].useUnfairLock=true
kl.base.datasource.slot.datasource[1].keepAlive=false
kl.base.datasource.slot.datasource[1].maxOpenPreparedStatements=0

kl.base.datasource.slot.datasource[2].driverClassName=com.mysql.cj.jdbc.Driver
kl.base.datasource.slot.datasource[2].filters=stat,slf4j
kl.base.datasource.slot.datasource[2].initialSize=1
kl.base.datasource.slot.datasource[2].logSlowSql=true
kl.base.datasource.slot.datasource[2].maxActive=20
kl.base.datasource.slot.datasource[2].maxWait=10000
kl.base.datasource.slot.datasource[2].minEvictableIdleTimeMillis=300000
kl.base.datasource.slot.datasource[2].minIdle=1
kl.base.datasource.slot.datasource[2].name=slave_2
kl.base.datasource.slot.datasource[2].password=*()D1546040776q
kl.base.datasource.slot.datasource[2].poolPreparedStatements=false
kl.base.datasource.slot.datasource[2].slowSqlMillis=3000
kl.base.datasource.slot.datasource[2].testOnBorrow=false
kl.base.datasource.slot.datasource[2].testOnReturn=false
kl.base.datasource.slot.datasource[2].testWhileIdle=true
kl.base.datasource.slot.datasource[2].timeBetweenEvictionRunsMillis=60000
# \u5982\u679C\u9700\u8981\u8FDB\u884C\u521D\u59CB\u5316 \uFF0C\u52A0\u4E0A INIT=RUNSCRIPT FROM 'classpath:db/h2/h2_ddl.sql'
kl.base.datasource.slot.datasource[2].url=jdbc\:mysql\://**************\:3309/nkm0223?useUnicode\=true&characterEncoding\=UTF-8&useJDBCCompliantTimezoneShift\=true&useLegacyDatetimeCode\=false&serverTimezone\=GMT%2B8&useSSL\=false&allowPublicKeyRetrieval\=true
kl.base.datasource.slot.datasource[2].username=root
kl.base.datasource.slot.datasource[2].validationQuery=SELECT 1 FROM DUAL
kl.base.datasource.slot.datasource[2].maxEvictableIdleTimeMillis=300000
kl.base.datasource.slot.datasource[2].useUnfairLock=true
kl.base.datasource.slot.datasource[2].keepAlive=false
kl.base.datasource.slot.datasource[2].maxOpenPreparedStatements=0
kl.base.datasource.slot.slotName=demo

