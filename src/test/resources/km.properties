# key
km.key.allKeyGenCronExpression = 0 0 0/1 * * ?
km.key.cacheCheckFrequency = 60
km.key.cacheSize = 2
km.key.cacheSupplyThreshold = 1
km.key.keyUniquenessCheck = true
km.key.keyLimits[0].enable = true
km.key.keyLimits[0].keyType = RSA_2048
km.key.keyLimits[0].limit = 100
km.key.keyLimits[0].keyGenCronExpression = 0 0 0/1 * * ?
km.key.keyLimits[1].enable = true
km.key.keyLimits[1].keyType = RSA_4096
km.key.keyLimits[1].limit = 100
km.key.keyLimits[1].keyGenCronExpression = 0 0 0/1 * * ?
km.key.keyLimits[2].enable = true
km.key.keyLimits[2].keyType = SM2
km.key.keyLimits[2].limit = 100
km.key.keyLimits[2].keyGenCronExpression = 0 0 0/1 * * ?
km.key.lateArchiveTime = 365
km.key.archivePageThreshold = 200
km.key.archiveCronExpression = 0 0 0 * * ?

# kmConf
km.conf.signResponse=
km.conf.checkSnExist=
km.conf.restoreCA=
km.conf.sksKeyValidityYears=
km.conf.reduceRealTime=
km.conf.reduceExecuteInterval=
km.conf.performanceTest=
km.conf.doRevokeCA=
km.conf.b64RootCert=
km.conf.verifyRequest=
kl.km.conf.sksKeyCacheEnable=

# mainKey
km.mainKey.mainKeyAlgo = SM4_ECB
km.mainKey.mainKeyId = 1592728042865909762

# thread
km.thread.corePoolSize = 1
km.thread.maxPoolSize = 5
km.thread.keepAliveTime = 30
km.thread.queueCapacity = 1000
