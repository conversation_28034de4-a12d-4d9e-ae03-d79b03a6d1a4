package kl.npki.km.management.log;

import kl.nbase.log.collect.LogCollector;
import kl.nbase.log.resolver.ILogResolver;
import org.aspectj.lang.ProceedingJoinPoint;

/**
 * <AUTHOR>
 * @Date 2023/3/10 15:45
 */
public class AspectTestResolver implements ILogResolver {
    @Override
    public String getLogType() {
        return null;
    }

    @Override
    public String resolve(ProceedingJoinPoint joinPoint, Object returnValue, LogCollector logCollector) {
        String name = joinPoint.getSignature().getName();
        System.out.println("进入" + name + "方法解析器");
        return name;
    }

    @Override
    public String exceptionResolve(ProceedingJoinPoint joinPoint, Throwable t, LogCollector logCollector) {
        return null;
    }
}
