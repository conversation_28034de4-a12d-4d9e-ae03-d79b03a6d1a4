package kl.npki.km.management.repository.service.impl;

import kl.npki.km.service.repository.entity.CaResourceDO;
import kl.npki.km.service.repository.service.ICaResourceService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/10/24 15:21
 */
@SpringBootTest
@Transactional
public class CaResourceServiceTest {

    @Resource
    private ICaResourceService caResourceService;

    @Test
    void addCaResource() {
        CaResourceDO caResourceDO = new CaResourceDO();
        caResourceDO.setCaId(2L);
        caResourceDO.setKeyType("SM2");
        caResourceDO.setKeyNum(36);
        caResourceDO.setLimitNum(100);
        caResourceDO.setWarningNum(20);
        caResourceDO.setResourceStatus(1);
        caResourceDO.setCreateTime(LocalDateTime.of(2019, 8, 1, 10, 10));
        caResourceDO.setUpdateTime(LocalDateTime.of(2023, 10, 1, 10, 10));
        caResourceService.save(caResourceDO);
        Assertions.assertNotNull(caResourceDO.getId());
    }
}
