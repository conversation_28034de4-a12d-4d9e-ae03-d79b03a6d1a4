package kl.npki.km.management.repository.service.impl;

import kl.npki.km.core.common.constants.ArchiveReason;
import kl.npki.km.service.repository.entity.KeyHistoryDO;
import kl.npki.km.service.repository.service.IKeyHistoryService;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/10/19 11:46
 * @Description:
 */
@SpringBootTest
@Transactional
class KeyHistoryServiceImplTest {
    @Resource
    private IKeyHistoryService keyHistoryService;

    @Test
    void addKeyHistory() {
        for (int i = 0; i < 15; i++) {
            KeyHistoryDO keyHistoryDO = new KeyHistoryDO();
            keyHistoryDO.setPrivateKey(RandomStringUtils.randomAlphabetic(30));
            keyHistoryDO.setKeyIndex(RandomStringUtils.randomAlphabetic(20));
            keyHistoryDO.setPublicKey(RandomStringUtils.randomAlphabetic(40));
            keyHistoryDO.setKeyType("RSA_2048");
            keyHistoryDO.setMainKeyId(32443554665566L);
            keyHistoryDO.setCaId(32435456665663L);
            keyHistoryDO.setEncCertSn(RandomStringUtils.randomNumeric(16));
            keyHistoryDO.setSubject("CN=" + RandomStringUtils.randomAlphanumeric(5) + ",C=CN");
            keyHistoryDO.setCreateTime(LocalDateTime.now());
            keyHistoryDO.setValidStart(LocalDateTime.of(2018, 3, 1, 10, 10));
            keyHistoryDO.setValidEnd(LocalDateTime.of(2023, 10, 1, 10, 10));
            keyHistoryDO.setArchiveReason(ArchiveReason.EXPIRED.getDesc());
            keyHistoryDO.setEscrowTime(LocalDateTime.of(2018, 3, 1, 10, 10));
            keyHistoryService.save(keyHistoryDO);
            Assertions.assertNotNull(keyHistoryDO.getId());
        }
    }

    @Test
    void countKeyCurrent() {
        KeyHistoryDO keyHistoryDO = keyHistoryService.getById(1590163224569016321L);
        Assertions.assertNotNull(keyHistoryDO);
    }
}
