package kl.npki.km.management.repository.service.impl;

import kl.npki.km.service.repository.entity.CaInfoDO;
import kl.npki.km.service.repository.service.ICaInfoService;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/10/24 11:57
 */
@SpringBootTest
@Transactional
public class CaInfoServiceImplTest {

    @Resource
    private ICaInfoService caInfoService;

    @Test
    void addCaInfo() {
        CaInfoDO caInfoDO = new CaInfoDO();
        caInfoDO.setCaName("koalCA");
        caInfoDO.setCaVersion("1.0");
        caInfoDO.setIdCert(RandomStringUtils.random(40));
        caInfoDO.setCertSn("32435456665663");
        caInfoDO.setKeyIndex("32435456665663");
        caInfoDO.setUseMediumSn(32431);
        caInfoDO.setCaStatus(1);
        caInfoDO.setLimitYear(2);
        caInfoDO.setCreateTime(LocalDateTime.of(2019, 8, 1, 10, 10));
        caInfoDO.setUpdateTime(LocalDateTime.of(2023, 10, 1, 10, 10));
        caInfoService.save(caInfoDO);
        Assertions.assertNotNull(caInfoDO.getId());
//        FileEngine4096.getInstance().enumKeyType();
    }
}
