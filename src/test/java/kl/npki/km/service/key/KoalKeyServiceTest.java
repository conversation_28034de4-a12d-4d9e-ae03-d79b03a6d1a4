package kl.npki.km.service.key;

import com.koal.security.asn1.Null;
import com.koal.security.pki.x509.AlgorithmIdentifier;
import com.koal.security.pki.x509.CertificateSerialNumber;
import com.koal.security.pki.x509.SubjectPublicKeyInfo;
import com.koal.security.pki.x509.Validity;
import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.security.asn1.cms.EncryptedContentInfo;
import kl.nbase.security.asn1.custom.pkcs.RecipientInfo;
import kl.nbase.security.asn1.custom.pkcs.SignedAndEnvelopedData;
import kl.nbase.security.asn1.gb.km.response.RetKeyRespond;
import kl.nbase.security.asn1.gb.kmv2.response.KMRespond;
import kl.nbase.security.asn1.gb.kmv2.response.Respond;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.entity.algo.BlockSymAlgo;
import kl.npki.base.core.biz.cert.service.CertSignService;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.base.core.utils.KeyUtils;
import kl.npki.km.core.service.key.IkeyService;
import kl.npki.km.core.service.key.impl.KeyServiceImpl;
import koal.common.emengine.util.RandomGen;
import koal.security.gb.Identifiers;
import org.apache.commons.lang.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigInteger;
import java.security.Key;
import java.security.KeyPair;
import java.security.PublicKey;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/11/18 10:52
 * @Description:
 */
@SpringBootTest
class KoalKeyServiceTest {

    public static int caCertKeyIndex = 1;
    public IkeyService ikeyService = new KeyServiceImpl();

    @Test
    void genCaIdCert() {
        ClusterEngine engine = EngineHolder.get();

        KeyPair keyPair = engine.getSignKeyPair(AsymAlgo.SM2, caCertKeyIndex);
        String b64Cert = null;
        try {
            Certificate cert = CertSignService.issueSelfSignCert(engine, "CN=TestCa,C=CN", keyPair, 365L);

            b64Cert = Base64Util.base64Encode(cert.getEncoded());

        } catch (Exception e) {
            e.printStackTrace();
        }
        Assertions.assertNotNull(b64Cert);
    }


    @Test
    void keyApplyService() throws Exception {

        // 生成请求
        byte[] applyReq = generateApplyReq();

        // 发送请求，获取响应
        byte[] resultByte1 = ikeyService.processReq(applyReq);
        Assertions.assertNotNull(resultByte1);

        // 幂等测试
        // 由于 SignedAndEnvelopedData 中包含随机生成的对称密钥，直接对比必然有不同的部分，因此解析出内容物（即解密后的私钥）进行比较
        byte[] resultByte2 = ikeyService.processReq(applyReq);
        Assertions.assertNotNull(resultByte2);

        byte[] priKey1 = parseKeyApplyServiceResponse(resultByte1);
        byte[] priKey2 = parseKeyApplyServiceResponse(resultByte2);
        // 相同的请求，获取的私钥应相同
        Assertions.assertArrayEquals(priKey1, priKey2);

        // 生成另一个请求
        byte[] applyReq3 = generateApplyReq();
        // 发送请求，获取响应
        byte[] resultByte3 = ikeyService.processReq(applyReq3);
        Assertions.assertNotNull(resultByte1);
        byte[] priKey3 = parseKeyApplyServiceResponse(resultByte3);
        // 不同的请求，获取的私钥应不同
        Assertions.assertFalse(Objects.deepEquals(priKey1, priKey3));

    }

    @Test
    void keyRestoreService() throws Exception {
        ClusterEngine engine = EngineHolder.get();

        KeyPair keyPair = engine.genKeyPair(AsymAlgo.SM2);
        SubjectPublicKeyInfo pubKeyInfo = new SubjectPublicKeyInfo("pubKey");
        if (isRSAKey(keyPair.getPublic())) {
            pubKeyInfo.decode(keyPair.getPublic().getEncoded());
        } else {
            pubKeyInfo.setPublicKeyValue(keyPair.getPublic());
        }
        CertificateSerialNumber sn = new CertificateSerialNumber();
        BigInteger certificationSN = new BigInteger("9582948816525855");
        sn.setValue(certificationSN);

        AlgorithmIdentifier mSymmAlgo = new AlgorithmIdentifier();
        mSymmAlgo.getAlgorithm().setValue(Identifiers.id_cn_gmj_algo_sm4_ecb);
        mSymmAlgo.getParameters().setActual(new Null("Null"));


        // 密钥恢复
        String restoreNumeric = RandomGen.genRandomDec(8);
        byte[] restoreReq = new KoalGBV2Helper().createRestoreReq(sn, mSymmAlgo, Integer.parseInt(restoreNumeric),
            pubKeyInfo, engine.getSignKeyPair(AsymAlgo.SM2, caCertKeyIndex).getPrivate());
        System.out.println(Base64Util.base64Encode(restoreReq));

        byte[] bytes = ikeyService.processReq(restoreReq);
        Assertions.assertNotNull(bytes);
    }

    @Test
    void keyRevokeService() throws Exception {
        ClusterEngine engine = EngineHolder.get();

        CertificateSerialNumber sn = new CertificateSerialNumber();
        BigInteger certificationSN = new BigInteger("9582948816525855");
        sn.setValue(certificationSN);

        String revokeNumeric = RandomGen.genRandomDec(8);

        byte[] revokeReq = new KoalGBV2Helper().createRevokeReq(sn, Integer.parseInt(revokeNumeric),
            engine.getSignKeyPair(AsymAlgo.SM2, caCertKeyIndex).getPrivate());

        System.out.println(Base64Util.base64Encode(revokeReq));
        byte[] bytes = ikeyService.processReq(revokeReq);
        Assertions.assertNotNull(bytes);
    }

    private boolean isRSAKey(Key key) {
        if (key instanceof RSAPublicKey) {
            return true;
        } else if (key instanceof RSAPrivateKey) {
            return true;
        }
        return false;
    }

    /**
     * 生成密钥申请请求
     *
     * @return 密钥生成请求
     * @throws Exception
     */
    private byte[] generateApplyReq() throws Exception {
        ClusterEngine engine = EngineHolder.get();

        KeyPair keyPair = engine.genKeyPair(AsymAlgo.SM2);
        // 包含公钥
        SubjectPublicKeyInfo pubKeyInfo = new SubjectPublicKeyInfo("pubKey");
        if (isRSAKey(keyPair.getPublic())) {
            pubKeyInfo.decode(keyPair.getPublic().getEncoded());
        } else {
            pubKeyInfo.setPublicKeyValue(keyPair.getPublic());
        }
        // SN
        CertificateSerialNumber sn = new CertificateSerialNumber();
        BigInteger certificationSN = new BigInteger(RandomGen.genRandomDec(16));
        sn.setValue(certificationSN);
        // 算法
        AlgorithmIdentifier mSymmAlgo = new AlgorithmIdentifier();
        mSymmAlgo.getAlgorithm().setValue(Identifiers.id_cn_gmj_algo_sm4_ecb);
        mSymmAlgo.getParameters().setActual(new Null("Null"));

        // 有效期
        Validity validity = new Validity();
        long now = System.currentTimeMillis();
        Date notBefore = new Date(now);
        Date notAfter = new Date(now + 365 * 24 * 3600 * 1000L);
        validity.getNotBefore().setActual(
            validity.getNotBefore().getGeneralizedTime());
        validity.getNotBefore().getGeneralizedTime().setValue(notBefore);
        validity.getNotAfter().setActual(
            validity.getNotAfter().getGeneralizedTime());
        validity.getNotAfter().getGeneralizedTime().setValue(notAfter);
        // userName
        String userName = "TestUser" + RandomGen.genRandomDec(6);
        // taskNo
        String numeric = RandomGen.genRandomDec(8);
        // 密钥请求
        return new KoalGBV2Helper().createApplyReq(sn, pubKeyInfo, pubKeyInfo, userName, mSymmAlgo,
            Integer.valueOf(numeric), validity, RandomStringUtils.randomAlphabetic(16),
            engine.getSignKeyPair(AsymAlgo.SM2, caCertKeyIndex).getPrivate());
    }

    /**
     * 解析密钥申请响应
     *
     * @param response {@link KMRespond} 的 byte 数组
     * @return 私钥原文
     * @throws Exception
     */
    private byte[] parseKeyApplyServiceResponse(byte[] response) throws Exception {
        Respond respond =
            Respond.getInstance(KMRespond.getInstance(response).getKsRespond().getRespondList().getObjectAt(0));
        Assertions.assertEquals(0, respond.getTagNo());
        RetKeyRespond retKeyRespond = (RetKeyRespond) respond.getValue();

        // 获得公钥
        kl.nbase.security.asn1.x509.SubjectPublicKeyInfo publicKeyInfo = retKeyRespond.getRetPubKey();
        PublicKey publicKey = KeyUtils.getPublicKey(publicKeyInfo);
        ClusterEngine engine = EngineHolder.get();
        KeyPair keyPair = engine.getKeyPair(publicKey);

        // 获得封装后的私钥
        SignedAndEnvelopedData signedAndEnvelopedData = retKeyRespond.getRetPriKey();
        // 取出 RecipientInfo 中加密后的对称密钥
        RecipientInfo recipientInfo =
            RecipientInfo.getInstance(signedAndEnvelopedData.getRecipientInfos().getObjectAt(0));
        byte[] encryptedSymKey = recipientInfo.getEncryptedKey().getOctets();
        // 获得对称密钥原文
        byte[] decryptedSymKey = engine.priDec(keyPair.getPrivate(), encryptedSymKey);
        EncryptedContentInfo encryptedContentInfo = signedAndEnvelopedData.getEncryptedContentInfo();
        // 对称解密获得私钥原文
        BlockSymAlgo blockSymAlgo =
            BlockSymAlgo.valueOf(encryptedContentInfo.getContentEncryptionAlgorithm().getAlgorithm().getId());
        return engine.symmetricDec(blockSymAlgo, decryptedSymKey, null,
            encryptedContentInfo.getEncryptedContent().getOctets());
    }

}
