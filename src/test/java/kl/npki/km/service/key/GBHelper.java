package kl.npki.km.service.key;

import com.koal.security.pki.gb.km.EntName;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.security.asn1.Null;
import kl.security.asn1.ObjectIdentifier;
import kl.security.asn1.UTF8String;
import kl.security.ec.asn1.x962.Parameters;
import kl.security.pki.gb.km.EntName;
import kl.security.pki.gb.km.request.AppUserInfo;
import kl.security.pki.gb.km.request.ApplyKeyReq;
import kl.security.pki.gb.km.request.RestoreKeyReq;
import kl.security.pki.pkcs1.Identifiers;
import kl.security.pki.x509.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.Serializable;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.util.Date;

/**
 * Purpose:按照国家密码局标准协议，构造密钥分发申请，密钥恢复申请，密钥销毁申请的帮助类
 *
 * @see
 * @since 6.5.0
 */
public abstract class GBHelper implements Serializable {

    private static final long serialVersionUID = 1L;
    public static String caCert = "MIIBjjCCATKgAwIBAgIQMXA9kPVZLAKGvvV7aFMLlDAMBggqgRzPVQGDdQUAMCUxFjAUBgNVBAMMDVNtMl9DQV9pZENlcnQxCzAJBgNVBAYTAkNOMB4XDTIyMTExODA5MDQ0OFoXDTQyMTExMzA5MDQ0OFowJTEWMBQGA1UEAwwNU20yX0NBX2lkQ2VydDELMAkGA1UEBhMCQ04wWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAAQDgGi4XpOgSnAszOhtNaxsU971Ex1c5KOZQgYVa/dNfShdPIyP3+9+MYZ1kbYKj+d/ltexGm4jDoWry1N7CH87o0IwQDAPBgNVHRMBAf8EBTADAQH/MA4GA1UdDwEB/wQEAwIBhjAdBgNVHQ4EFgQU6G6IRcYXspoJgsYwsRhcafCLQuQwDAYIKoEcz1UBg3UFAANIADBFAiEAoCkLJHV6igk+5WCGXG1WV7j2DDkz0mpIbm4a7+9v8XsCIEIhqijvEfv7g0NFm5L81jccC7vy6IMVmzQZ6JmAe6Q7";

    private static Log mLog = LogFactory.getLog(GBHelper.class);

    public static EntName getEntName() throws Exception {

        EntName entName = null;

        Certificate cert = new Certificate();
        cert.decode(Base64.decodeBase64(caCert));
        if (cert == null)
            throw new Exception("CA不存在任何有效的身份证书，无法与KM通讯，请检查配置或重新签发身份证书");

        mLog.debug("CA-KM CAAuthCert Name: " + cert.getSubject().toString());
        MessageDigest md;
        md = MessageDigest.getInstance("SHA1");
        byte[] certPubKeyHash = md.digest(cert.getPublicKey().getEncoded());
        mLog.debug("CA-KM CAAuthCert Hash: "
                + new String(Base64.encodeBase64(certPubKeyHash)));
        entName = new kl.security.pki.gb.km.EntName();
        entName.getEntGeneralName().setActual(
                entName.getEntGeneralName().getDirectoryName());
        entName.getEntGeneralName().getDirectoryName().copy(cert.getSubject());
        entName.getEntPubKeyHash().setValue(certPubKeyHash);
        entName.getHashAlgorithm().getAlgorithm()
                .setValue(Identifiers.id_SHA1);
        entName.getHashAlgorithm().getParameters()
                .addComponent(new Null("null"));
        entName.getSerialNumber().copy(cert.getSerialNumber());
        entName.encode();

        return entName;
    }

    /**
     * V1 V2版本共享的构造申请请求
     *
     * @param sigPubKey
     * @param protectedPubKey
     * @param sn
     * @param userName
     * @param mKeyEncryptionAlgorithmIdentifier
     * @param validity
     * @param mediumSn
     * @return
     */
    public static ApplyKeyReq createApplyKeyReq(SubjectPublicKeyInfo sigPubKey,
                                                SubjectPublicKeyInfo protectedPubKey, CertificateSerialNumber sn,
                                                String userName,
                                                AlgorithmIdentifier mKeyEncryptionAlgorithmIdentifier,
                                                Validity validity, String mediumSn) {
        ApplyKeyReq akr = new ApplyKeyReq();

        AppUserInfo aui = new AppUserInfo();
        aui.getUserCertNo().copy(sn);
        aui.getUserPubKey().copy(protectedPubKey);
        Date notbefore = (Date) validity.getNotBefore().getValue();
        Date notafter = (Date) validity.getNotAfter().getValue();
        aui.getNotBefore().setValue(notbefore);
        aui.getNotAfter().setValue(notafter);
        aui.getUserName().setValue(userName.getBytes());
        // 上海CA中，增加的介质序列号
        if (mediumSn != null) {
            UTF8String extInfo = new UTF8String();
            extInfo.setValue(mediumSn);
            aui.getExtendInfo().addComponent(extInfo);
        }

        AsymAlgo protectAsymAlgo = AsymAlgo.valueOf(protectedPubKey.getPublicKey());
        if (AsymAlgo.SM2.getAlgoName().equals(protectAsymAlgo.getAlgoName())) {
            akr.getRetAsymAlg().getAlgorithm()
                    .setValue(kl.security.gb.Identifiers.id_cn_gmj_algo_sm2);
            akr.getRetAsymAlg().getParameters().addComponent(new Null("null"));
        } else {
            akr.getRetAsymAlg()
                    .getAlgorithm()
                    .setValue(
                            kl.security.pki.pkcs1.Identifiers.rsaEncryption);
            akr.getRetAsymAlg().getParameters().addComponent(new Null("null"));
        }

        AsymAlgo sigAsymAlgo = AsymAlgo.valueOf(sigPubKey.getPublicKey());
        if (AsymAlgo.SM2.getAlgoName().equals(sigAsymAlgo.getAlgoName())) {
            Parameters parameters = (Parameters) sigPubKey.getAlgorithm()
                    .getParameters().getActual();
            // 曲线ID，默认为SM2
            ObjectIdentifier ecCurveId = kl.security.gb.Identifiers.id_cn_gmj_algo_sm2;
            // 密钥长度，默认为256
            int nKeyLen = 256;
            if (parameters.getActual() == parameters.getNamedCurve()) {
                ecCurveId = (ObjectIdentifier) parameters
                        .getActual();
                // 可以根据曲线，设定密钥长度
            }
            akr.getAppKeyLen().setValue(BigInteger.valueOf(nKeyLen));
            akr.getAppKeyType().getAlgorithm().setValue(ecCurveId);
            akr.getAppKeyType().getParameters().addComponent(new Null("null"));
            akr.getRetHashAlg().getAlgorithm()
                    .setValue(kl.security.gb.Identifiers.id_cn_gmj_algo_sm3);
            akr.getRetHashAlg().getParameters().addComponent(new Null("null"));
        } else {
            java.security.interfaces.RSAPublicKey rsaKey = (java.security.interfaces.RSAPublicKey) protectedPubKey
                    .getPublicKey();
            if (rsaKey.getModulus().bitLength() > 1024) {
                akr.getAppKeyLen().setValue(BigInteger.valueOf(2048));
            } else {
                akr.getAppKeyLen().setValue(BigInteger.valueOf(1024));
            }
            akr.getAppKeyType()
                    .getAlgorithm()
                    .setValue(
                            kl.security.pki.pkcs1.Identifiers.rsaEncryption);
            akr.getAppKeyType().getParameters().addComponent(new Null("null"));
            akr.getRetHashAlg().getAlgorithm()
                    .setValue(kl.security.pki.pkcs1.Identifiers.id_SHA1);
            akr.getRetHashAlg().getParameters().addComponent(new Null("null"));
        }

        akr.getRetSymAlg().copy(mKeyEncryptionAlgorithmIdentifier);

        akr.getAppUserInfo().copy(aui);
        return akr;
    }

    /**
     * V1 V2版本共享的构造恢复请求
     *
     * @param sn
     * @param mKeyEncryptionAlgorithmIdentifier
     * @param protectedPubKey
     * @return
     * @throws Exception
     */
    public static RestoreKeyReq createRestoreKeyReq(CertificateSerialNumber sn,
                                                    AlgorithmIdentifier mKeyEncryptionAlgorithmIdentifier,
                                                    SubjectPublicKeyInfo protectedPubKey) throws Exception {

        RestoreKeyReq rkr = new RestoreKeyReq();
        AsymAlgo sigAsymAlgo = AsymAlgo.valueOf(protectedPubKey.getPublicKey());
        if (AsymAlgo.SM2.getAlgoName().equals(sigAsymAlgo.getAlgoName())) {
            rkr.getRetAsymAlg().getAlgorithm()
                    .setValue(kl.security.gb.Identifiers.id_cn_gmj_algo_sm2_enc);
            rkr.getRetAsymAlg().getParameters().addComponent(new Null("null"));
            rkr.getRetHashAlg().getAlgorithm()
                    .setValue(kl.security.gb.Identifiers.id_cn_gmj_algo_sm3);
            rkr.getRetHashAlg().getParameters().addComponent(new Null("null"));
        } else {
            rkr.getRetAsymAlg()
                    .getAlgorithm()
                    .setValue(
                            kl.security.pki.pkcs1.Identifiers.rsaEncryption);
            rkr.getRetAsymAlg().getParameters().addComponent(new Null("null"));
            rkr.getRetHashAlg().getAlgorithm()
                    .setValue(kl.security.pki.pkcs1.Identifiers.id_SHA1);
            rkr.getRetHashAlg().getParameters().addComponent(new Null("null"));
        }
        rkr.getRetSymAlg().copy(mKeyEncryptionAlgorithmIdentifier);

        rkr.getUserCertNo().copy(sn);
        rkr.getUserPubKey().copy(protectedPubKey);
        return rkr;
    }

}
