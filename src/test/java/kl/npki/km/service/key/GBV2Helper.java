package kl.npki.km.service.key;

import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.security.asn1.gb.km.response.RetKeyRespond;
import kl.nbase.security.asn1.gb.km.response.RetKeyResponse;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.security.asn1.Null;
import kl.security.asn1.ObjectIdentifier;
import kl.security.asn1.UTF8String;
import kl.security.pki.gb.km.request.ApplyKeyReq;
import kl.security.pki.gb.km.request.RestoreKeyReq;
import kl.security.pki.gb.km.request.RevokeKeyReq;
import kl.security.pki.gb.kmV2.request.CARequestV2;
import kl.security.pki.gb.kmV2.request.RequestV2;
import kl.security.pki.gb.kmV2.response.ErrorPkgRespondV2;
import kl.security.pki.gb.kmV2.response.KMRespondV2;
import kl.security.pki.gb.kmV2.response.RespondV2;
import kl.security.pki.pkcs1.Identifiers;
import kl.security.pki.x509.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.math.BigInteger;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.interfaces.ECPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.Date;

/**
 * 具体的国标V2接口显示，用于构造请求，解析相应
 *
 * <AUTHOR>
 */
public class GBV2Helper extends GBHelper {

    private static final long serialVersionUID = 1L;
    public static String kmCert = "MIIBjzCCATKgAwIBAgIQMQY2hd0mKue8bSjZodoahzAMBggqgRzPVQGDdQUAMCUxFjAUBgNVBAMMDVNtMl9LTV9pZENlcnQxCzAJBgNVBAYTAkNOMB4XDTIyMTExODA5MDQ0OFoXDTQyMTExMzA5MDQ0OFowJTEWMBQGA1UEAwwNU20yX0tNX2lkQ2VydDELMAkGA1UEBhMCQ04wWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAARNbwAZTD8vwJVIOj+G8KFWhxOfDZEH6+yuoZmdvhI8Ju/qzG8FzvIz5xpJTGoDYB+vFGzQHDbTaBlUC9Emqpe3o0IwQDAPBgNVHRMBAf8EBTADAQH/MA4GA1UdDwEB/wQEAwIBhjAdBgNVHQ4EFgQU5Jhq5Yz07ia5RPEW/SwI2ROdM3cwDAYIKoEcz1UBg3UFAANJADBGAiEAmo0GcklGcGZsjA6bKODid7eFGXrYbQhtRIiVn7hDGFECIQDiNkjTn1lfDEVs6xMf4OsZaHFGXE13UBG7Q1jXQ5zVuw==";
    private static Log mLog = LogFactory.getLog(GBV2Helper.class);

    /**
     * 设置请求时间和签名值
     *
     * @param cr
     * @param taskNo
     * @throws Exception
     */
    public static void setTimeTagAndsign(CARequestV2 cr, Integer taskNo, PrivateKey key)
            throws Exception {
        Date requestTime = new Date(System.currentTimeMillis());
        cr.getRequestTime().setValue(requestTime);
        cr.getTaskNo().setValue(BigInteger.valueOf(taskNo));

        if (key == null)
            throw new Exception("CA不存在任何有效的身份证书，无法与KM通讯，请检查配置或重新签发身份证书");
        ObjectIdentifier signAlg;
        HashAlgo hashAlgo;
        if (key instanceof ECPrivateKey) {
            hashAlgo = HashAlgo.SM3;
            signAlg = kl.security.gb.Identifiers.id_cn_gmj_algo_sm2_sm3;
        } else {
            hashAlgo = HashAlgo.SHA1;
            signAlg = Identifiers.sha1WithRSAEncryption;
        }
        cr.getAlgorithmIdentifier().getAlgorithm().setValue(signAlg);
        cr.getAlgorithmIdentifier().getParameters()
                .addComponent(new Null("null"));

        byte[] signBytes = EngineHolder.get().sign(key, cr.getToBeSigned().encode(), hashAlgo, null);
        // 可验证签名是否正确
        // boolean v = engine.verify(AuthKeyStoreMngr.getInstance()
        // .getCurrentCaAuthCert().getPublicKey(), cr.getToBeSigned()
        // .encode(), signBytes);
        cr.getSignature().setValue(signBytes);
    }

    private static void isError(RespondV2 r11) throws Exception {
        if (r11.getActual() == r11.getErrorPkgRepond()) {
            ErrorPkgRespondV2 err = (ErrorPkgRespondV2) r11.getActual();
            UTF8String str = (UTF8String) err.getErrDesc().getComponent(0);
            throw new Exception("对端返回错误[" + err.getErrNo().getIntValue()
                    + "],描述如下：" + str);
        }
    }

    public byte[] createApplyReq(CertificateSerialNumber sn,
                                 SubjectPublicKeyInfo sigPubKey,
                                 SubjectPublicKeyInfo protectedPubKey, String userName,
                                 AlgorithmIdentifier mKeyEncryptionAlgorithmIdentifier, Integer taskNo,
                                 Validity validity, String mediumSn, PrivateKey key) throws Exception {
        CARequestV2 cr = null;
        ApplyKeyReq akr = createApplyKeyReq(sigPubKey, protectedPubKey, sn,
                userName, mKeyEncryptionAlgorithmIdentifier, validity, mediumSn);
        cr = new CARequestV2();
        cr.getVersion().setValue(Version.v2);
        cr.getCaName().copy(getEntName());
        RequestV2 rq = new RequestV2("Request");
        rq.getApplykeyreq().copy(akr);
        rq.setActual(rq.getApplykeyreq());
        cr.getRequestList().addComponent(rq);
        setTimeTagAndsign(cr, taskNo, key);
        return cr.encode();
    }

    public RetKeyResponse parseApplyRep(byte[] result, int tagNo,
                                        boolean traceable, boolean isVerify) throws Exception {
        KMRespondV2 kmr1 = new KMRespondV2();
        kmr1.decode(result);
        verifyResponse(kmr1);
        RespondV2 applyRespond = (RespondV2) kmr1.getRespondList()
                .getComponent(0);
        isError(applyRespond);
        if (applyRespond.getActual() == applyRespond.getApplyKeyRespond()
                || applyRespond.getActual() == applyRespond.getRestoreKeyRespond()) {
            RetKeyResponse rkr = (RetKeyRespond) applyRespond.getActual();
            return rkr;
        } else {
            throw new Exception("响应类型错误");
        }
    }

    public byte[] createRestoreReq(CertificateSerialNumber sn,
                                   AlgorithmIdentifier mKeyEncryptionAlgorithmIdentifier, int taskNo,
                                   SubjectPublicKeyInfo protectedPubKey, PrivateKey key) throws Exception {
        CARequestV2 cr = new CARequestV2();
        cr.getVersion().setValue(Version.v2);
        cr.getCaName().copy(getEntName());
        RestoreKeyReq rkr = createRestoreKeyReq(sn,
                mKeyEncryptionAlgorithmIdentifier, protectedPubKey);
        RequestV2 rq = new RequestV2("Request");
        rq.getRestorekeyreq().copy(rkr);
        rq.setActual(rq.getRestorekeyreq());
        cr.getRequestList().addComponent(rq);
        setTimeTagAndsign(cr, taskNo, key);
        return cr.encode();
    }

    public RetKeyResponse parseRestoreRep(byte[] result, int tagNo,
                                          boolean traceable, boolean isVerify) throws Exception {
        KMRespondV2 kmrRestore = new KMRespondV2();
        kmrRestore.decode(result);
        verifyResponse(kmrRestore);
        RespondV2 r11 = (RespondV2) kmrRestore.getRespondList().getComponent(0);
        isError(r11);
        if (r11.getActual() == r11.getApplyKeyRespond()
                || r11.getActual() == r11.getRestoreKeyRespond()) {
            RetKeyResponse rkr = (RetKeyRespond) r11.getActual();

            return rkr;
        } else {
            throw new Exception("响应类型错误");
        }
    }

    public byte[] createRevokeReq(CertificateSerialNumber sn, int taskNo, PrivateKey key)
            throws Exception {
        CARequestV2 cr = null;
        cr = new CARequestV2();
        cr.getVersion().setValue(Version.v2);
        cr.getCaName().copy(getEntName());
        RevokeKeyReq revokeKeyReq = new RevokeKeyReq();
        revokeKeyReq.getUserCertNo().copy(sn);
        RequestV2 rq = new RequestV2("Request");
        rq.getRevokekeyreq().copy(revokeKeyReq);
        rq.setActual(rq.getRevokekeyreq());
        cr.getRequestList().addComponent(rq);
        setTimeTagAndsign(cr, taskNo, key);
        return cr.encode();
    }

    public void parseRevokeRep(byte[] result, int tagNo, boolean traceable,
                               boolean isVerify) throws Exception {
        KMRespondV2 kmr1 = new KMRespondV2();
        kmr1.decode(result);
        verifyResponse(kmr1);
        RespondV2 revokeRespond = (RespondV2) kmr1.getRespondList()
                .getComponent(0);
        isError(revokeRespond);
    }

    /**
     * 验证KM返回的相应，验证签名等，暂未实现
     *
     * @param kmr
     */
    public void verifyResponse(KMRespondV2 kmr) {
        // no implements
        String kmCertB64 = kmCert;
        Certificate kmCert = null;
        if (kmCertB64 != null && kmCertB64.trim().length() > 10) {
            try {
                kmCert = new Certificate();
                kmCert.decode(Base64.decodeBase64(kmCertB64));
            } catch (Exception e) {
                kmCert = null;
                mLog.error("KM身份证书配置不正确", e);
            }
        }
        if (kmCert != null) {
            HashAlgo hashAlgo;
            ClusterEngine engineAdapter = EngineHolder.get();
            PublicKey pubKey = kmCert.getPublicKey();
            if (pubKey instanceof RSAPublicKey) {
                hashAlgo = HashAlgo.SHA1;
            } else {
                hashAlgo = HashAlgo.SM3;
            }
            if (engineAdapter != null) {
                try {
                    boolean verify = engineAdapter.verify(pubKey,
                            kmr.getToBeSigned().encode(),
                            (byte[]) kmr.getSignature().getValue(),
                            hashAlgo,
                            null);
                    if (!verify) {
                        throw new Exception("验证签名失败");
                    }
                } catch (Exception e) {
                    mLog.error("签名验证失败：", e);
                }
            }
        }
    }
}
