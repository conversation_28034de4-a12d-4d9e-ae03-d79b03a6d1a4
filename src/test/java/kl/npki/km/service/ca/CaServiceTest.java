package kl.npki.km.service.ca;

import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.security.asn1.*;
import kl.nbase.security.asn1.custom.pkcs.EnvelopedKeyPairData;
import kl.nbase.security.asn1.pkcs.CertificationRequestInfo;
import kl.nbase.security.asn1.x500.X500Name;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.asn1.x509.SubjectPublicKeyInfo;
import kl.nbase.security.asn1.x509.V3TBSCertificateGenerator;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.entity.algo.BlockSymAlgo;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.nbase.security.utils.AsymKeyUtil;
import kl.nbase.security.utils.SignatureAlgoUtil;
import kl.npki.base.core.asn1.custom.*;
import kl.npki.base.core.biz.cert.model.template.SelfSignCertTemplate;
import kl.npki.base.core.biz.cert.service.CertSignService;
import kl.npki.base.core.biz.dataprotect.service.impl.DataProtectServiceHashMacSm3Impl;
import kl.npki.base.core.biz.cert.service.IdCertMgr;
import kl.npki.base.core.common.crypto.SignatureHelper;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.npki.base.core.utils.KeyUtils;
import kl.npki.km.core.biz.trans.service.IProtocolProcessor;
import kl.npki.km.core.biz.trans.service.ProtocolProcessorFactory;
import kl.npki.km.core.exception.KmValidationError;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Arrays;
import java.util.Date;

@SpringBootTest
class CaServiceTest {
    /**
     * ca身份证书的算法类型
     */
    private static final AsymAlgo DEFAULT_ALGO = AsymAlgo.SM2;
    /**
     * ca身份证书的dn项
     */
    private static final String CA_ID_CERT_DN = "C=CN,CN=idCert_ca";
    /**
     * ca站点证书的dn项
     */
    private static final String CA_SSL_CERT_DN = "C=CN,CN=sslCert_ca";
    /**
     * ca名称
     */
    private static final String CA_NAME = "ca for test";
    /**
     * ca版本
     */
    private static final String CA_VERSION = "2";
    /**
     * 密钥有效期，年
     */
    private static final Integer LIMIT_YEAR = 20;

    @Test
    Pair<Certificate, KeyPair> testCaFastAccess() throws Exception {
        // 生成ca证书的密钥对
        KeyPair keyPair = genKeyPair();
        // 构建ca身份证书
        Certificate caCert = buildCaIdCert(keyPair);
        // 生成快速接入请求
        byte[] req = buildRequest(caCert, keyPair);
        System.out.println(Base64.encodeBase64String(req));
        // 执行接入业务
        IProtocolProcessor protocolProcessor = ProtocolProcessorFactory.createProtocolProcessor(req);
        byte[] result = protocolProcessor.process(req);
        // 解析响应
        CaFastAccessResponse response = CaFastAccessResponse.getInstance(result);
        TBSCaFastResponse caFastResponse = response.getCaFastResponse();
        byte[] tobeSign = caFastResponse.getEncoded();
        assert SignatureHelper.verify(IdCertMgr.INSTANCE.getIdCert(), response.getSignedData().getOctets(), tobeSign);

        KmInfo kmInfo = caFastResponse.getKmInfo();

        Certificate idCert = kmInfo.getIdCert();
        String kmName = new String(kmInfo.getName().getOctets());
        String kmVersion = new String(kmInfo.getVersion().getOctets());

        SysInfoConfig sysInfoConfig = ConfigHolder.get().get(SysInfoConfig.class);

        assert idCert.equals(IdCertMgr.INSTANCE.getIdCert());
        assert kmName.equals(sysInfoConfig.getName());
        assert kmVersion.equals(sysInfoConfig.getVersion());

        // 返回的ca身份证书，用于测试站点证书快速签发
        return new ImmutablePair<>(caCert, keyPair);
    }


    @Test
    void testCaFastSign() throws Exception {
        // 快速接入，获得CA身份证书
        Pair<Certificate, KeyPair> certificateKeyPairPair = testCaFastAccess();
        Certificate caCert = certificateKeyPairPair.getLeft();
        KeyPair caKeyPair = certificateKeyPairPair.getRight();

        byte[] request = buildCaSiteCertRequest(caCert, true);
        // 执行签发业务
        IProtocolProcessor protocolProcessor = ProtocolProcessorFactory.createProtocolProcessor(request);
        byte[] result = protocolProcessor.process(request);


        KmResponse kmResponse = KmResponse.getInstance(result);
        assert !isErrorResponse(kmResponse);
        // 解析响应
        CaSiteCertResponse caSiteCertResponse = (CaSiteCertResponse) kmResponse.getValue();
        // 验签
        TBSCaSiteCertResponse tbsCaSiteCertResponse = caSiteCertResponse.getTBSCaSiteCertResponse();
        byte[] tobeSign = tbsCaSiteCertResponse.getEncoded();
        assert SignatureHelper.verify(IdCertMgr.INSTANCE.getIdCert(), caSiteCertResponse.getSignedData().getOctets(), tobeSign);

        // 从响应中获取站点证书
        Certificate signCert = tbsCaSiteCertResponse.getSignCert();
        Certificate encCert = tbsCaSiteCertResponse.getEncCert();
        EnvelopedKeyPairData encKeyPair = tbsCaSiteCertResponse.getEncKeyPair();

        System.out.println("站点签名证书:"+Base64.encodeBase64String(signCert.getEncoded()));
        System.out.println("站点加密证书:"+Base64.encodeBase64String(encCert.getEncoded()));
        // 解密拿到加密证书私钥
        PrivateKey privateKey = decryptEnvelopedKeyPairData(encKeyPair, caKeyPair);
        System.out.println("站点加密证书私钥:"+Base64.encodeBase64String(privateKey.getEncoded()));

    }

    private KeyPair genKeyPair() {
        ClusterEngine engine = EngineHolder.get();
        KeyPair keyPair = null;
        if (Arrays.stream(engine.getAllSignKeyPair(DEFAULT_ALGO)).findFirst().isPresent()) {
            keyPair = engine.getAllSignKeyPair(DEFAULT_ALGO)[0];
        }
        assert keyPair != null;
        return keyPair;
    }

    private byte[] buildRequest(Certificate caCert, KeyPair keyPair) throws Exception {

        CaInfo caInfo = buildCaInfo(caCert);
        ASN1Boolean assignDefaultResources = ASN1Boolean.getInstance(true);
        ASN1Integer limitYear = new ASN1Integer(LIMIT_YEAR);

        TBSCaFastRequest tbsCaFastRequest = new TBSCaFastRequest(caInfo, assignDefaultResources, limitYear);
        String signAlgoOid = CertUtils.getDefaultSignAlgoOid(keyPair.getPublic());
        ASN1ObjectIdentifier signAlgorithm = new ASN1ObjectIdentifier(signAlgoOid);

        byte[] sign = sign(caCert, keyPair, tbsCaFastRequest.getEncoded());
        ASN1BitString signedData = new DERBitString(sign);

        CaFastAccessRequest request = new CaFastAccessRequest(tbsCaFastRequest, signAlgorithm, signedData);

        return request.getEncoded();
    }

    private Certificate buildCaIdCert(KeyPair keyPair) throws Exception {
        X500Name subject = new X500Name(CA_ID_CERT_DN);
        Date[] validateDate = CertUtils.getCertValidTimeFromNow(365 * 40L);

        String defaultSignAlgoOid = CertUtils.getDefaultSignAlgoOid(keyPair.getPublic());
        SelfSignCertTemplate selfSignCertTemplate = new SelfSignCertTemplate(subject, keyPair.getPublic(),
            validateDate[0], validateDate[1], defaultSignAlgoOid);
        V3TBSCertificateGenerator tbsCertGen = new V3TBSCertificateGenerator();
        tbsCertGen.setIssuer(new X500Name(CA_ID_CERT_DN));
        selfSignCertTemplate.setIssuer(tbsCertGen);

        return CertSignService.certSignature(EngineHolder.get(), keyPair.getPrivate(), selfSignCertTemplate.create(),
            SignatureAlgoUtil.getHashAlgoByOid(defaultSignAlgoOid));
    }

    public static byte[] sign(Certificate cert, KeyPair keyPair, byte[] toBeSigned) {

        try {
            ClusterEngine engine = EngineHolder.get();
            HashAlgo hashAlgo = SignatureAlgoUtil.getHashAlgoByOid(cert.getSignatureAlgorithm().getAlgorithm());
            // 获取身份证书私钥签名
            return engine.sign(keyPair.getPrivate(), toBeSigned, hashAlgo, null);
        } catch (Exception e) {
            throw KmValidationError.SIGN_ERROR.toException(e);
        }

    }

    private byte[] buildCaSiteCertRequest(Certificate caCert, boolean needEncCert) throws Exception {
        // 公钥信息
        PublicKey publicKey = KeyUtils.getPublicKey(caCert.getSubjectPublicKeyInfo());
        ClusterEngine engine = EngineHolder.get();
        AsymAlgo asymAlgo = AsymAlgo.valueOf(publicKey);
        // 生成站点签名证书密钥对
        KeyPair keyPair = engine.genKeyPair(asymAlgo);
        // 构造证书请求
        SubjectPublicKeyInfo pkInfo = SubjectPublicKeyInfo.getInstance(keyPair.getPublic().getEncoded());
        CertificationRequestInfo certificationRequest = new CertificationRequestInfo(new X500Name(CA_SSL_CERT_DN), pkInfo, new DERSet());

        // CA基本信息
        CaInfo caInfo = buildCaInfo(caCert);
        // 是否需要签发加密站点证书
        ASN1Boolean needEncCertAsn = ASN1Boolean.getInstance(needEncCert);
        // 待签名数据
        TBSCaSiteCertRequest tbsCertSignRequest = new TBSCaSiteCertRequest(caInfo, needEncCertAsn, certificationRequest);
        // 签名信息
        String signAlgoOid = CertUtils.getDefaultSignAlgoOid(publicKey);
        ASN1ObjectIdentifier signAlgorithm = new ASN1ObjectIdentifier(signAlgoOid);
        // 生成签名值
        byte[] sign = SignatureHelper.sign(tbsCertSignRequest.getEncoded());
        ASN1BitString signedData = new DERBitString(sign);
        // 组装请求
        CaSiteCertRequest request = new CaSiteCertRequest(tbsCertSignRequest, signAlgorithm, signedData);
        return request.getEncoded();
    }

    private CaInfo buildCaInfo(Certificate caCert) throws Exception {
        ASN1OctetString caVersion = new DEROctetString(CA_VERSION.getBytes());
        ASN1OctetString caName = new DEROctetString(CA_NAME.getBytes());

        DataProtectServiceHashMacSm3Impl hmacService = new DataProtectServiceHashMacSm3Impl(BaseConstant.HMAC_KEY);
        byte[] hmac = hmacService.generateData(caName.getOctets());
        ASN1BitString hmacData = new DERBitString(hmac);

        return new CaInfo(caVersion, caName, caCert, hmacData);
    }

    private PrivateKey decryptEnvelopedKeyPairData(EnvelopedKeyPairData envelopedKeyPairData, KeyPair caKeyPair) throws Exception {
        ASN1OctetString encryptedKeyPairData = envelopedKeyPairData.getEncryptedKeyPairData();
        ASN1OctetString encryptedSymmKeyData = envelopedKeyPairData.getEncryptedSymmKeyData();
        ASN1OctetString symmetricCipherOID = envelopedKeyPairData.getSymmetricCipherOID();

        ClusterEngine engine = EngineHolder.get();

        // 私钥解密，得到对称加密密钥
        byte[] symmetricKey = engine.priDec(caKeyPair.getPrivate(), encryptedSymmKeyData.getOctets());
        // 获得对称算法类型
        BlockSymAlgo symAlgo = BlockSymAlgo.valueOf(new String(symmetricCipherOID.getOctets()));
        // 对称解密，得到站点加密证书私钥
        byte[] privateKeyByte = engine.symmetricDec(symAlgo, symmetricKey, null, encryptedKeyPairData.getOctets());
        return AsymKeyUtil.bytes2PrivateKey(privateKeyByte);
    }

    private boolean isErrorResponse(KmResponse kmResponse) {
        return KmResponse.Choice.ERROR_PKG_RESPOND.getTagNo() == kmResponse.getTagNo();
    }
}
