package kl.npki.base.management;

/**
 * <AUTHOR>
 * @Date 2023/3/1 15:05
 */
public class PropTest {
    public static void main(String[] args) throws Exception {
//        String name = "npki-application.properties";
//        String path = ClassLoader.getSystemResource(name).getPath();
//        path = java.net.URLDecoder.decode(path, "UTF-8");
//        System.out.println(path);
//        File file = new File(path);
//        properties.load(file);
//        properties.setProperty("kl.test.test", "tttttt", "测试测试");
//        properties.setProperty("kl.base.sysInfo.name", "sdfsfsfd");
//        FileOutputStream fos = new FileOutputStream(file);
//        properties.store(fos);
        String s = "sharding-km";
        String a = "sfsfd";
        boolean matches = s.matches("^sharding\\S*");


    }
}
