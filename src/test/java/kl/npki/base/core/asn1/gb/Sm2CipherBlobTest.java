package kl.npki.base.core.asn1.gb;

import kl.nbase.security.pkix.custom.skf.SM2CipherBlob;
import org.apache.commons.codec.binary.Base64;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;

/**
 * <AUTHOR>
 * @date 2023-01-31 17:50
 */
class Sm2CipherBlobTest {

    public static final String generatedByEngine16Bytes = "BBwqsj05pF+d/qwsewa3CVV4AuXtoKRi2kV+s4RxqD2HDlZA0kbSZs+5p6cDkqoCHL4cMgt+vdeMkpZLPE5cvfnZ9TgNmyfYlJW+2o/UQGU3HLlsIBF43Pr4DjiCao+FXwGaWtqVzzK+xj+75xA3zps=";
    public static final String generatedByEngine24Bytes = "BBLG0lQlagJpm0WnVVzZu93USPkTkDJWyd+67sZbEeIzfbK6pvFPLoHPrNCmzvfRqkjpiCfsxTDcpoIokPM09NMZ9hrMC7+8kZYXjChTdT4NLWcyc4GT2Im+5ELvmL/MNC+5P2EhfL+eq6ymtVNqJ9X/dWmaU5riGA==";
    public static final String generatedByEngine32Bytes = "BJCyFIiAh8NpzVyMNF4XOFGAt7rI6kD3DdLywUARoLJVrDgxnzPEFrA3WK271W2QAo5SpJh0+N6vFReTMvdPJ2ZM0BS3QgwvcxsqGzIUF1Pf+4dKM2jFWYY93xvH3Ujq1aE98QcJ/mk4HbDqAaWMqXlckqToHlQAtMER74g+yB3V";
    public static final String generatedByEngine64Bytes = "BDIQgGTzHBeiu3RjCTclQBHNm6Jsgr+4ZydMZmflDhdQilNw3ZTAx8ZQVKDsLjH2uL+g6M0HNATp3rUkXFws/lVeS03WgFhsXMTClxIftWFAjxpdW5VzdOkwE95gu55nHau9r/NvryrOi+5NavjJtBYM6Yso/YLqjydEhgkaAYSlr4xvFuL1GzK2m2DzmMBO3n1EaMQ7pR3aVwlJmEcQjDY=";
    public static final String generatedByEngine128Bytes = "BN0Tg2/KJyMe/cXILWvSXkis+Tyz7vfzOy3PpuZgpQOmeWb44SWgJuPU7ZDmnl61hpODbdgGrvCLD1JSpk+HjnjGxMN6c8l1gmkqpOK2S4SawtXv3aOMeCjM1i/V1HGHCc3HEUjZ4V8SsOlwX3lpIgN99L3OXspk66+NeiXHLtFFiF8uhgRphmcMTXyi/QH31TwLoxuisFSmpqlEgWKvF3Yuk1dPDTgtRGfAg/iI3kSKJ4heoIYbk3NKNXYPbaN/vnv6xkuIyIN5iUfMsD2Gxxegefl0Wc69P8a+o6NVU1Bm";

    @Test
    void generateAndCompare() {
        // 比较 cipher 长度不同的情况
        compare(generatedByEngine16Bytes);
        compare(generatedByEngine24Bytes);
        compare(generatedByEngine32Bytes);
        compare(generatedByEngine64Bytes);
        compare(generatedByEngine128Bytes);
    }

    private void compare(String generatedByEngine) {

        byte[] generatedByEngineBytes = Base64.decodeBase64(generatedByEngine);
        // 从 engine 加密结果获取 Sm2CipherBlob
        SM2CipherBlob blobFromEngine = SM2CipherBlob.valueOf(generatedByEngineBytes);

        byte[] toEMByteArray = blobFromEngine.toEMByteArray();
        byte[] toOsccaByteArray = blobFromEngine.toOsccaByteArray();

        // 比较从 EMByteArray 生成的 Sm2CipherBlob
        assertArrayEquals(toEMByteArray, SM2CipherBlob.valueOf(toEMByteArray).toEMByteArray());
        assertArrayEquals(toOsccaByteArray, SM2CipherBlob.valueOf(toEMByteArray).toOsccaByteArray());

        // 比较从 OsccaByteArray 生成的 Sm2CipherBlob
        assertArrayEquals(toEMByteArray, SM2CipherBlob.valueOf(toOsccaByteArray).toEMByteArray());
        assertArrayEquals(toOsccaByteArray, SM2CipherBlob.valueOf(toOsccaByteArray).toOsccaByteArray());
    }
}
