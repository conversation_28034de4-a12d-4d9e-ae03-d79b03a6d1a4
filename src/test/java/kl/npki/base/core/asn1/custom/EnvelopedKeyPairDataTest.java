package kl.npki.base.core.asn1.custom;

import kl.nbase.security.asn1.custom.pkcs.EnvelopedKeyPairData;
import org.apache.commons.codec.binary.Base64;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;

/**
 * <AUTHOR>
 * @date 2023-02-15 13:52
 */
class EnvelopedKeyPairDataTest {

    @Test
    void testRecoverData() throws Exception {
        // 测试环境生成的数据，即 EnvelopedKeyPairData.getEncoded() 后转为 base64
        String recoverData = "MIIBjAQABIIBhAEAAAABBAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA3bpgu3j9ekRRiiitOHZEmEaBaSy7jfoNxJxkf61Io3MAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABMEuvvl0kTKJly9R+59jB9u9hm2n0RYcNv/y9Y2UrLZQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALTRz2pqA7O0gH4eM1vgD6A+nsbc+aONLIhGLq+zhClYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQOY+ZtJ78ef1VjM3RyT0dv/Ip4xLYsEE74nEz3jjJAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABpZ1ENbZLXP3isIIm5D6yE9XGMvhrZgDTchJuvRUcNhALvueZzJHljQUYEcr9MqsTQBrXK+aoi9IV6W8Kl8hYIEAAAAHng5K8VgVigm0Wr0KksJt0EAA==";
        byte[] bytes = Base64.decodeBase64(recoverData);
        EnvelopedKeyPairData instance = EnvelopedKeyPairData.getInstance(bytes);
        // 获取 SkfEnvelopedKeyBlob 部分，由 kl.npki.base.core.asn1.gb.SkfEnvelopedKeyBlob 生成
        byte[] skfEnvelopedKeyBlobGeneratedByNewClass = instance.getEncryptedKeyPairData().getOctets();
        // 使用老KM的数据结构解析
        com.gzca.security.asn1.gm0016.SkfEnvelopedKeyBlob oldSkf = com.gzca.security.asn1.gm0016.SkfEnvelopedKeyBlob.getInstance(skfEnvelopedKeyBlobGeneratedByNewClass);
        // 使用老KM的数据结构再次解析
        com.gzca.security.asn1.gm0016.SkfEnvelopedKeyBlob oldSkf2 = com.gzca.security.asn1.gm0016.SkfEnvelopedKeyBlob.getInstance(oldSkf.getBytes());

        // 新数据结构、老数据结构、老数据结构再次解析后的数据结构，三者应该相等
        assertArrayEquals(skfEnvelopedKeyBlobGeneratedByNewClass, oldSkf.getBytes());
        assertArrayEquals(oldSkf.getBytes(), oldSkf2.getBytes());
    }
}