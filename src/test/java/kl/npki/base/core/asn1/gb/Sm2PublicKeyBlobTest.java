package kl.npki.base.core.asn1.gb;

import kl.nbase.security.asn1.ASN1Sequence;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.pkix.custom.skf.SM2PublicKeyBlob;
import kl.nbase.security.utils.PEMUtil;
import kl.npki.base.core.utils.CertUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.security.interfaces.ECPublicKey;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 * @date 2023-01-31 15:55
 */
class Sm2PublicKeyBlobTest {

    @Test
    void generateAndCompare() throws Exception {
        // 读取证书
        String sm2Cert = "src/test/resources/sm2/npki-base-core-test.cert.pem";
        byte[] bytes = FileUtils.readFileToByteArray(new File(sm2Cert));

        bytes = Base64.decodeBase64(PEMUtil.unFormatCert(new String(bytes)));
        Certificate certificate = Certificate.getInstance(
            ASN1Sequence.getInstance(bytes)
        );
        // 获取公钥
        ECPublicKey publicKey = (ECPublicKey) CertUtil.getPublicKey(certificate.getSubjectPublicKeyInfo());
        System.out.println(publicKey.getParams().getOrder());
        // 通过公钥构造 Sm2PublicKeyBlob
        SM2PublicKeyBlob sm2PublicKeyBlobByPubKey = SM2PublicKeyBlob.valueOf(
            publicKey,
            false
        );
        // 通过 byte 数组构造 Sm2PublicKeyBlob
        byte[] pubKeyBlobBytes = sm2PublicKeyBlobByPubKey.toByteArray();
        SM2PublicKeyBlob sm2PublicKeyBlobByBytes = SM2PublicKeyBlob.valueOf(pubKeyBlobBytes, false);

        // 比较两种方式生成的 Sm2PublicKeyBlob 内容是否一致
        assertArrayEquals(sm2PublicKeyBlobByBytes.toByteArray(), sm2PublicKeyBlobByPubKey.toByteArray());
        assertEquals(sm2PublicKeyBlobByBytes.getBitLength(), sm2PublicKeyBlobByPubKey.getBitLength());
        assertArrayEquals(sm2PublicKeyBlobByBytes.getX(), sm2PublicKeyBlobByPubKey.getX());
        assertArrayEquals(sm2PublicKeyBlobByBytes.getY(), sm2PublicKeyBlobByPubKey.getY());
    }


}
