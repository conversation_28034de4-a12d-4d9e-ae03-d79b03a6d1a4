package kl.npki.base.core.configs;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @since 2024/1/12
 */
class EmConfigTest {
    private static Pair<Integer, String> parseIndexWithCred(String indexWithCred) {
        // indexWithCred: index-私钥授权访问码，例如：1-12346578
        // 只分割一次，防止私钥授权访问码中包含“-”字符
        String[] indexWithCredArray = indexWithCred.split("-", 2);
        Integer index = Integer.parseInt(indexWithCredArray[0]);
        String cred = indexWithCredArray.length > 1 ? indexWithCredArray[1] : null;
        return Pair.of(index, cred);
    }

    @Test
    void testParseIndexWithCred() {
        // test
        String indexWithCred = "1-12346578";
        Pair<Integer, String> integerStringPair = parseIndexWithCred(indexWithCred);
        System.out.println(integerStringPair);
        assertEquals(1, integerStringPair.getLeft());
        assertEquals("12346578", integerStringPair.getRight());


        indexWithCred = "1-12346-578";
        Pair<Integer, String> integerStringPair2 = parseIndexWithCred(indexWithCred);
        System.out.println(integerStringPair2);
        assertEquals(1, integerStringPair2.getLeft());
        assertEquals("12346-578", integerStringPair2.getRight());
    }
}