package kl.npki.base.core.utils;

import kl.nbase.security.asn1.ASN1Encodable;
import kl.nbase.security.asn1.ASN1Sequence;
import kl.nbase.security.asn1.ASN1Set;
import kl.nbase.security.asn1.DLTaggedObject;
import kl.nbase.security.asn1.custom.cms.EncryptedContentInfo;
import kl.nbase.security.asn1.custom.pkcs.ExtendedCertificateOrCertificate;
import kl.nbase.security.asn1.custom.pkcs.RecipientInfo;
import kl.nbase.security.asn1.custom.pkcs.SignedAndEnvelopedData;
import kl.nbase.security.asn1.pkcs.SignerInfo;
import kl.nbase.security.asn1.x500.X500Name;
import kl.nbase.security.asn1.x509.AlgorithmIdentifier;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import kl.nbase.security.asn1.x509.Certificate;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;

/**
 * <AUTHOR> Yang
 * @date 2023-01-11 10:52
 */
class PkcsUtilsTest {

    private static SignedAndEnvelopedData signedAndEnvelopedData;

    @BeforeAll
    private static void init() throws IOException {
        // 由 https://www.gmcert.org/# 生成的国密签名数字信封
        String p7bFile = "src/test/resources/asn1/test_enc_envelope.p7b";
        byte[] bytes = FileUtils.readFileToByteArray(new File(p7bFile));
        bytes = Base64.decodeBase64(bytes);
        // 外面带了一层结构，只取出需要的
        ASN1Sequence instance = ASN1Sequence.getInstance(bytes);
        ASN1Encodable encodable = instance.getObjectAt(1);
        SignedAndEnvelopedData sae = SignedAndEnvelopedData.getInstance(
            DLTaggedObject.getInstance(encodable).getBaseObject()
        );

        signedAndEnvelopedData = SignedAndEnvelopedData.getInstance(sae.getEncoded());
    }

    @Test
    void createRecipientInfo() throws IOException {

        RecipientInfo recipientInfo = RecipientInfo.getInstance(signedAndEnvelopedData.getRecipientInfos().getObjectAt(0));
        RecipientInfo createdRecipientInfo = createRecipientInfo(recipientInfo);

        // 判断是否相同
        assertArrayEquals(recipientInfo.getEncoded(), createdRecipientInfo.getEncoded());

    }

    private RecipientInfo createRecipientInfo(RecipientInfo oldRecipientInfo) {
        // 从 sample 中获取相关信息
        X500Name x500Name = oldRecipientInfo.getIssuerAndSerialNumber().getName();
        BigInteger recipientSerialNumber = oldRecipientInfo.getIssuerAndSerialNumber().getCertificateSerialNumber().getValue();
        AlgorithmIdentifier keyEncryptionAlgorithm = oldRecipientInfo.getKeyEncryptionAlgorithm();
        byte[] encryptedKey = oldRecipientInfo.getEncryptedKey().getOctets();

        // 通过参数构造 RecipientInfo
        return PkcsUtils.createRecipientInfo(
            x500Name,
            recipientSerialNumber,
            keyEncryptionAlgorithm,
            encryptedKey
        );
    }

    @Test
    void createEncryptedContentInfo() throws IOException {

        EncryptedContentInfo encryptedContentInfo = signedAndEnvelopedData.getEncryptedContentInfo();
        EncryptedContentInfo createdEncryptedContentInfo = createEncryptedContentInfo(encryptedContentInfo);

        // 判断是否相同
        assertArrayEquals(encryptedContentInfo.getEncoded(), createdEncryptedContentInfo.getEncoded());
    }

    private EncryptedContentInfo createEncryptedContentInfo(EncryptedContentInfo oldEncryptedContentInfo) {
        // 从 sample 中获取相关信息
        AlgorithmIdentifier contentEncryptionAlgorithm = oldEncryptedContentInfo.getContentEncryptionAlgorithm();
        byte[] encryptedContent = signedAndEnvelopedData.getEncryptedContentInfo().getEncryptedContent().getOctets();

        // 通过参数构造 RecipientInfo
        return PkcsUtils.createEncryptedContentInfo(
            contentEncryptionAlgorithm,
            encryptedContent
        );
    }

    @Test
    void createSignerInfo() throws IOException {

        SignerInfo signerInfo = SignerInfo.getInstance(signedAndEnvelopedData.getSignerInfos().getObjectAt(0));
        SignerInfo updatedSignerInfo = updateSignerInfo(signerInfo);
        SignerInfo createdSignerInfo = createSignerInfo(updatedSignerInfo);

        // 判断是否相同
        assertArrayEquals(updatedSignerInfo.getEncoded(), createdSignerInfo.getEncoded());
    }

    private SignerInfo updateSignerInfo(SignerInfo oldSignerInfo) throws IOException {

        // 更新 SignerInfo 版本号
        byte[] encodedSignerInfo = oldSignerInfo.getEncoded();
        // 通过 https://www.gmcert.org/# 生成的 SignedAndEnvelopedData 结构中，SignerInfo 的版本被设置为 0；
        // 根据《0014-2012_数字证书认证系统密码协议规范》，该值应为 1，因此在此处进行修改
        encodedSignerInfo[5] = 1;

        return SignerInfo.getInstance(
            ASN1Sequence.getInstance(encodedSignerInfo)
        );
    }

    private SignerInfo createSignerInfo(SignerInfo updatedSignerInfo) throws IOException {

        // 从 sample 中获取相关信息
        X500Name signerName = updatedSignerInfo.getIssuerAndSerialNumber().getName();
        BigInteger signerSerialNumber = updatedSignerInfo.getIssuerAndSerialNumber().getCertificateSerialNumber().getValue();
        AlgorithmIdentifier digestAlgorithmIdentifier = updatedSignerInfo.getDigestAlgorithm();
        AlgorithmIdentifier digEncryptionAlgorithm = updatedSignerInfo.getDigestEncryptionAlgorithm();
        byte[] signedAndEncryptedKeyDigest = updatedSignerInfo.getEncryptedDigest().getOctets();

        // 通过参数构造 SignerInfo
        return PkcsUtils.createSignerInfo(
            signerName,
            signerSerialNumber,
            digestAlgorithmIdentifier,
            digEncryptionAlgorithm,
            signedAndEncryptedKeyDigest
        );
    }

    @Test
    void createSignedAndEnvelopedData() throws IOException {

        // 构造 RecipientInfo
        RecipientInfo recipientInfo = RecipientInfo.getInstance(signedAndEnvelopedData.getRecipientInfos().getObjectAt(0));
        RecipientInfo createdRecipientInfo = createRecipientInfo(recipientInfo);
        // 构造 EncryptedContentInfo
        EncryptedContentInfo encryptedContentInfo = signedAndEnvelopedData.getEncryptedContentInfo();
        EncryptedContentInfo createdEncryptedContentInfo = createEncryptedContentInfo(encryptedContentInfo);
        // 构造 SignerInfo
        SignerInfo signerInfo = SignerInfo.getInstance(signedAndEnvelopedData.getSignerInfos().getObjectAt(0));
        SignerInfo createdSignerInfo = createSignerInfo(signerInfo);
        // 获取证书
        ASN1Set asn1Set = signedAndEnvelopedData.getCertificates();
        List<Certificate> certificateList = new ArrayList<>();
        for (int i = 0; i < asn1Set.size(); i++) {
            certificateList.add(Certificate.getInstance(ExtendedCertificateOrCertificate.getInstance(asn1Set.getObjectAt(i)).toASN1Primitive()));
        }

        // 构造 SignedAndEnvelopedData
        SignedAndEnvelopedData createdSignedAndEnvelopedData = PkcsUtils.createSignedAndEnvelopedData(
            Collections.singletonList(createdRecipientInfo).toArray(new RecipientInfo[0]),
            createdEncryptedContentInfo,
            certificateList.toArray(new Certificate[0]),
            Collections.singletonList(createdSignerInfo).toArray(new SignerInfo[0])
        );

        // 通过 https://www.gmcert.org/# 生成的 SignedAndEnvelopedData 结构中，SignerInfo 的版本被设置为 0；
        // 根据《0014-2012_数字证书认证系统密码协议规范》，该值应为 1，因此在此处进行修改
        byte[] encodedSignedAndEnvelopedData = signedAndEnvelopedData.getEncoded();
        encodedSignedAndEnvelopedData[860] = 1;

        // 判断是否相同
        assertArrayEquals(encodedSignedAndEnvelopedData, createdSignedAndEnvelopedData.getEncoded());

    }
}
