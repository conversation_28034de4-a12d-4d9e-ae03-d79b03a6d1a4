package kl.npki.base.core.common.office.excel;

import kl.npki.base.core.biz.log.model.ApiLogInfo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.security.KeyFactory;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 生成带签名(XAdES)的Excel文件单元测试
 *
 * <AUTHOR>
 * @create 2025/2/12 下午1:43
 */
class ExcelWithKeyExporterTest {

    private String privateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCahyUvECZnqIpCX42pOlj+KUvzS7ZgdWAIDYAJtX6t+ImqRZ2Mq9TNHcGG3X9A5KOIl8A+1hAcBkwsyh6kbNpij4GJq2fPWvp2gAudB1TKdpPyi3z8U8IdbDWzAb1UeOQwg6jTRWZB5PhUxdtqd5B3RtA7zvfyw9YUY/C4LARDcB5JbqCnM524/Oq4QkrCpjUgqXWpKL0tnazZf3/MiWI8ZqxFsviUXMCCruyyVuFP24OjYhWXdKKzFItlN/txAPZtO6xNetV6hteJnQF+d5sKG/HUgd/cjYHZiNLBdYBGjDxxUQLsTmukZi6sp5GHrGOpxm2J/rFnx96WiP2zMjRpAgMBAAECggEACK+4I2EhB4qt2vVMHUsuXXsszcxfwCRY9Zrsq58MM/NYqb2oEZRHLhCSJGanFJrFppQBNocJ+MlR1uK1vkBwNkIi4f5Eo59VnPutq3ISDh+ESYoVyIlczBKdIibi4bC6AxAjmVy5VtLsMre8kcuOz2WnpvUHcxoXGqZTjcbLpo68h4LUxUSxZZaV62NsfAj0Mdwd1lfBFEaChBfrQfy4jeaCEk5FOJ4f0U8DkXRqQ3mwSAWihPZZgv0mVVP05jxFyRbBujaN4fFrVlSWiZb8/eMr+gwwSun8j4NOAl/y7x9G/+hHRonDD7i9g6O8sCkVPsZI1o96lRpzKu1G2x4TkQKBgQDC7zcUCJ11Dn3adLnyUS+pmH/b9q9ffrCRiwUSWcDf9TY4AL0mq/gasbZ8zVSiXUotnytzupVlGdd7DFuSaEIamQyJzkeivwivNsMS92nkuPPVN2T+RKd+R96OpSpihjePHEvLXIG5H6lKctyEIopRtBukTkrGeyR61qwXXFmtiwKBgQDK74Z4UlL8T+INEa9EvunhyBJF3FXT0E9PYcQhtgz89CUPUFUxUyy/bYrcRBJjFvvPpL2LKusFW5/s38owLaHzFTBULyDYo6MJfNQIyESRkMxBf1wX24/iHBkQ2XJ+0sziwcCeNfJjeaIfIGl3TfB+7eq+VYfrt8EcxjmdNW0MWwKBgHt7o41KQ8xCCNFYve3VJfcuBliltMQucdVQlj7gokB8igMB3NBhgYIPb0UxoNpVdEbgHFtsJ3Tq4MgutNdSugU1TG8DP/fFQdAURZB/MBJWYwd+1rgHb36cMkx0LhxJX2qFhzHkgLxvZQMnLa96XdqR2CzF97IxkZNK56rIsnovAoGBAILkcchj2RDS+YvSb0kZFsPBhz+2OUmwn6ck+RuBT+lMFCdHeluhOUiTgDBIW8+8AFlS+cqsuQAws/tV8RSYKlAex5hU+YuiKEOzVxP/EHMPgR9x5VWiEbEWzHgYuQfiMbLEo9eoFhphVtda0YvZIpjMHk/73PqGADHuDTVAQ4DnAoGBALqLr01Iwg1Eg5xlSs0vq/YxeyBjQgCesAnItWsKpyl5H39vPeDESGfsizxd5RIMJOIrkBnjXZgy5NW+mxPOML1l0ehEuyQ23gFU8/GeAGhkEvyUit7dctOlpl2LmiwBs2dVICxzzU5sHCxCsbeqk4mkzx8IOVcDHB3nzCLlMCuL";
    private String certificate = "MIIE4jCCAsqgAwIBAgIMSV0AAAAAAAAAAAXzMA0GCSqGSIb3DQEBCwUAMCQxCzAJBgNVBAYTAkNOMRUwEwYDVQQDDAxsb2NhbF9jYV9yc2EwHhcNMjUwMTE3MDM0NDA2WhcNMjcwMTE3MDM0NDA1WjAtMQswCQYDVQQGEwJDTjEeMBwGA1UEAwwV5YyF5ZCr5pe26Ze05oiz6K+B5LmmMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmoclLxAmZ6iKQl+NqTpY/ilL80u2YHVgCA2ACbV+rfiJqkWdjKvUzR3Bht1/QOSjiJfAPtYQHAZMLMoepGzaYo+Biatnz1r6doALnQdUynaT8ot8/FPCHWw1swG9VHjkMIOo00VmQeT4VMXbaneQd0bQO8738sPWFGPwuCwEQ3AeSW6gpzOduPzquEJKwqY1IKl1qSi9LZ2s2X9/zIliPGasRbL4lFzAgq7sslbhT9uDo2IVl3SisxSLZTf7cQD2bTusTXrVeobXiZ0BfnebChvx1IHf3I2B2YjSwXWARow8cVEC7E5rpGYurKeRh6xjqcZtif6xZ8feloj9szI0aQIDAQABo4IBCTCCAQUwDgYDVR0PAQH/BAQDAgDAMCAGA1UdJQEB/wQWMBQGCCsGAQUFBwMDBggrBgEFBQcDBDAMBgNVHRMEBTADAQEAMIGCBggrBgEFBQcBCwR2MHQwcgYIKwYBBQUHMAOGZmh0dHA6Ly8xMC4yLjEyLjE5MTo1MTAwNS91Y3MvdjEvY3J5cHRvL3RzR210U2lnbkRhdGE/YWs9MDE5YjY3NmExYWU0NGFiYTgyNWI2ZjNlODFmMzcxMDMmY2VydEFsaWFzPVRTQTAfBgNVHSMEGDAWgBS/SSm/YUN2ZbpzPcQDeS3sbKNJbDAdBgNVHQ4EFgQUfNaLmzB0VJv+yVVIEVErRa+4XbgwDQYJKoZIhvcNAQELBQADggIBACoEvI3cROETVjsMKwey4ieTenL+LY9fGnyfHHZpnepcghYexMzX6vLd05U2LgdRH8vkJdqtrdNEax1YfPGl8zIq0F/wlJLLiyEYz6g1TL0sz6vOlIUQd7u5yNaxL8AZSScu1PmhHgJes2CkNeG8BtqTJBOeZkS+eYOClPyM8aVJPOVT/Weck3lIQj1OET7VyCT1jdvM9yxWzFQI/iMKHn7jdc0tMsWYGXjYjMZf4OxhKcXP086LWavYkXvNp5peLo+hZ08j70+FZ+uQHU6QfPXv0Z0hlSONuckLjJTNNxVQR0aN1KZwHvGx5lU3DwgnOkUnNwkR05PdwIeDLYDesZczahoY/8HrtNjFrwN48RV31dJ9chsu6B5bmRq3ahl6t4f1Csmx47hENjOEnI27Btvogylr1rr0oumhZ747T9kTq8OMjBWDf5176ZRdn9fTs4++4XzOmM9T48TogqBj+7OfXuepPabc1Q6wRVlTpKldHeDekxcnJrJQ6qpjDGLdcNCtbsbV63UVdlcbqcw/YgQWqDcSS7C2Rq2SAITT36QgCpbZqScvRzwscMSyWa9weDhq5PI5fjM1tPxMVOr8E1hOD7PfebOKTiu3mEiM12uYWErXc1bf6tcLL1t0rfZCCOQ0fVuLn672tY5WIuKW39Hg2HGC87UoKlpZ0qEwcdZU";


    @Test
    void testExport() throws Exception {
        // 1. 准备测试数据
        List<ApiLogInfo> testData = getTestData();

        // 2. 构造Excel文件然后将测数据写入Excel文件中
        RSAPrivateKey key = parsePrivateKey(privateKey);
        X509Certificate cert = parseCertification(certificate);
        String exportDir = "./";
        ExcelWithKeyExporter<ApiLogInfo> excelExporter =
                new ExcelWithKeyExporter<>(
                        ApiLogInfo.class,
                        exportDir,
                        getEnableFieldMapping(),
                        key,
                        Collections.singletonList(cert));
        excelExporter.write(testData);
        excelExporter.write(testData);

        // 3. 完成数据写入，导出Excel文件并对Excel文件做签名
        File exportFile = excelExporter.doFinal();

        // 4. 验证Excel中XAdES签名
        Assertions.assertTrue(excelExporter.verify(exportFile));
    }

    private RSAPrivateKey parsePrivateKey(String privateKey) throws Exception {
        byte[] encodedKey = Base64.getDecoder().decode(privateKey.getBytes());
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(encodedKey);
        KeyFactory keyFac = KeyFactory.getInstance("RSA");
        return (RSAPrivateKey) keyFac.generatePrivate(pkcs8KeySpec);
    }

    private X509Certificate parseCertification(String certificate) throws Exception {
        CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
        try (InputStream in = new ByteArrayInputStream(Base64.getDecoder().decode(certificate))) {
            return (X509Certificate) certificateFactory.generateCertificate(in);
        }
    }

    /**
     * 获取定制表头和导出字段标题设置
     *
     * @return
     */
    private static Map<String, List<String>> getEnableFieldMapping() {
        Map<String, List<String>> enableFieldMapping = new HashMap<>();
        enableFieldMapping.put("traceId", Arrays.asList("System API logs", "Trace id"));
        enableFieldMapping.put("clientIp", Arrays.asList("System API logs", "Client ip"));
        enableFieldMapping.put("logWhen", Arrays.asList("System API logs", "Log when"));
        enableFieldMapping.put("biz", Arrays.asList("System API logs", "Business"));
        enableFieldMapping.put("bizId", Arrays.asList("System API logs", "Business id"));
        enableFieldMapping.put("detail", Arrays.asList("System API logs", "Business Details"));
        enableFieldMapping.put("result", Arrays.asList("System API logs", "Result"));
        enableFieldMapping.put("request", Arrays.asList("System API logs", "Request"));
        enableFieldMapping.put("response", Arrays.asList("System API logs", "Response"));
        enableFieldMapping.put("callerName", Arrays.asList("System API logs", "Caller"));
        return enableFieldMapping;
    }


    private List<ApiLogInfo> getTestData() {
        ApiLogInfo apiLogInfo = new ApiLogInfo()
                .setTraceId("73281093471091237")
                .setClientIp("127.0.0.1")
                .setLogWhen(LocalDateTime.now())
                .setBiz("系统登录")
                .setBizId("7812399123122")
                .setDetail("业务操作员登录")
                .setResult(true)
                .setRequest("{\"xx\":\"xx\"}")
                .setResponse("{\"xx\":\"xx\"}")
                .setCallerId("812371043713")
                .setCallerName("测试");
        List<ApiLogInfo> exportDataTestList = new ArrayList<>();
        exportDataTestList.add(apiLogInfo);
        return exportDataTestList;
    }

}