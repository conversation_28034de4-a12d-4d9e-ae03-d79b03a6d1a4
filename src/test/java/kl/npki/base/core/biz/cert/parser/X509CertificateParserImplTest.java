package kl.npki.base.core.biz.cert.parser;

import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.util.encoders.Base64;
import kl.nbase.security.utils.PEMUtil;
import kl.npki.base.core.biz.cert.model.parser.x509.CertificateInfo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;

class X509CertificateParserImplTest {

    @Test
    void testParse() throws IOException {
        String cert = "-----BEGIN CERTIFICATE-----\n" +
            "MIIGpTCCBY2gAwIBAgIQCXS08TM88V/1MzB+vJPtUzANBgkqhkiG9w0BAQsFADBf\n" +
            "MQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3\n" +
            "d3cuZGlnaWNlcnQuY29tMR4wHAYDVQQDExVHZW9UcnVzdCBDTiBSU0EgQ0EgRzEw\n" +
            "HhcNMjQwMzA4MDAwMDAwWhcNMjUwNDA4MjM1OTU5WjBsMQswCQYDVQQGEwJDTjES\n" +
            "MBAGA1UECAwJ5YyX5Lqs5biCMTMwMQYDVQQKDCrljJfkuqzkuZ3lpKnkupHliJvk\n" +
            "v6Hmga/mioDmnK/mnInpmZDlhazlj7gxFDASBgNVBAMMCyouNTFjdG8uY29tMIIB\n" +
            "IjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAx65Yn1dynELmQ5l5+3gzbiN0\n" +
            "mXq7ejsXLFtHRtUyClH+97EoPMwZHORF0eRBpWEZIxdTtBgXvWFmQATav0RKnSjU\n" +
            "hW4zi3DPUv8a3SXIflxktuwng0lgWtmBTOBNNFejAVkpHiG186hWl0G4fjyP42m2\n" +
            "tTb2L7uJasuTfpXy7OoFObgMiA3heMHXQthRBNn0ks22DF5Jw9picO2yLUR/+W1t\n" +
            "SVMzPg+tlaWxCkDhbxxGuTeJ7l6Xq+ZfWCkLsu15oelgQeMar2GW1NIZ07HeXaru\n" +
            "8kd4a6nVfrl/1TBaOQumr3L8xqYAEpvmfeLWxZu42KthBx86r8ahnAYMv1h9SwID\n" +
            "AQABo4IDTjCCA0owHwYDVR0jBBgwFoAUkZ9eMRWuEJ+tYMH3wcyqSDQvDCYwHQYD\n" +
            "VR0OBBYEFBwl6Xhl16PRS2e5MB9nF7p5AQOOMCEGA1UdEQQaMBiCCyouNTFjdG8u\n" +
            "Y29tggk1MWN0by5jb20wPgYDVR0gBDcwNTAzBgZngQwBAgIwKTAnBggrBgEFBQcC\n" +
            "ARYbaHR0cDovL3d3dy5kaWdpY2VydC5jb20vQ1BTMA4GA1UdDwEB/wQEAwIFoDAd\n" +
            "BgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUHAwIwdQYDVR0fBG4wbDA0oDKgMIYu\n" +
            "aHR0cDovL2NybDMuZGlnaWNlcnQuY29tL0dlb1RydXN0Q05SU0FDQUcxLmNybDA0\n" +
            "oDKgMIYuaHR0cDovL2NybDQuZGlnaWNlcnQuY29tL0dlb1RydXN0Q05SU0FDQUcx\n" +
            "LmNybDBvBggrBgEFBQcBAQRjMGEwIQYIKwYBBQUHMAGGFWh0dHA6Ly9vY3NwLmRj\n" +
            "b2NzcC5jbjA8BggrBgEFBQcwAoYwaHR0cDovL2NybC5kaWdpY2VydC1jbi5jb20v\n" +
            "R2VvVHJ1c3RDTlJTQUNBRzEuY3J0MAwGA1UdEwEB/wQCMAAwggF+BgorBgEEAdZ5\n" +
            "AgQCBIIBbgSCAWoBaAB2AE51oydcmhDDOFts1N8/Uusd8OCOG41pwLH6ZLFimjnf\n" +
            "AAABjh13n34AAAQDAEcwRQIgaJHjwdauviZDoHyULKVipPfBW8HUuo6qMVLmeyOo\n" +
            "An0CIQCyxOSX7xfT9tfeGAMtbTyWhviNkDOMmNY0TCVrzIRuyAB2AH1ZHhLheCp7\n" +
            "HGFnfF79+NCHXBSgTpWeuQMv2Q6MLnm4AAABjh13nz4AAAQDAEcwRQIgNQvlG7FY\n" +
            "NFEba2NYB7VXFazv+SrMIhHQ28+J/EIL3NkCIQD3OVq0jwcUWIrYO1eGc6WTS7/Y\n" +
            "rW7542RUk6aCaRXQ4gB2AObSMWNAd4zBEEEG13G5zsHSQPaWhIb7uocyHf0eN45Q\n" +
            "AAABjh13n2AAAAQDAEcwRQIhAJ3X9w+Fm9AsGu/CfgQZw6C+uBGxrpYzVPv5slim\n" +
            "HichAiAMeHXhiUbevtjhEGUpsTX5g8SWitydJZVUkCnvq8Cy5TANBgkqhkiG9w0B\n" +
            "AQsFAAOCAQEANmLaVXBgjwJ15coVG5IZCn9siF2ggbIFI3CN/pl/nHtmYBaam0wu\n" +
            "0v/S07adOzyIKiSxiSSqvPfqrYPrpV9+L3v6l/NE8rQf3r2fMLbj4lysw52Px47g\n" +
            "AiEzdpYs0N1bAIyA2pVnHSmuhdu/wzszextXr2hf4glu4pIM8x6+vj3eCexvPkSM\n" +
            "pP0+VB9BSch8oYsoJTJNqCvXhvg/magq6L0gsBp8fZW9LFppZdTMu/LPlZSwIGL7\n" +
            "bB+186q2mpmdGYHpkX7gc4BYuHwKgcWhzXP1PJ1ccwYYgwRAui47+H4mGzmPi3L0\n" +
            "2EtCd6FY2KFb//4fHcdl03bL1XqgTddcmQ==\n" +
            "-----END CERTIFICATE-----\n";

        Certificate certificateObj = Certificate.getInstance(Base64.decode(PEMUtil.unFormatCert(cert)));
        CertificateInfo certificateInfo = CertificateParserFactory.getX509CertificateParser().parse(certificateObj);
        Assertions.assertTrue(certificateInfo.getAttributeInfo().size() > 1);
    }
}