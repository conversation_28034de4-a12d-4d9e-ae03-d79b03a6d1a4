<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true">
    <property name="LOG_PATH" value="${logs.dir}/logs" />
    <property name="maxFileSize" value="200MB"/>
    <conversionRule conversionWord="msg" converterClass="kl.npki.base.service.common.log.HmacLogMessageConverter" />
    <conversionRule conversionWord="ip"  converterClass="kl.npki.base.service.common.log.ServerIpConvert" />
    <springProperty scope="context" name="level" source="kl.base.log.level" defaultValue="info"/>
    <springProperty scope="context" name="maxHistory" source="kl.base.log.maxHistory" defaultValue="180"/>
    <springProperty scope="context" name="totalSizeCap" source="kl.base.log.totalSizeCap" defaultValue="4096MB"/>
    <springProperty scope="context" name="syslogEnable" source="kl.base.log.syslogEnable" defaultValue="false"/>
    <springProperty scope="context" name="syslogIp" source="kl.base.log.syslogIp" defaultValue="127.0.0.1"/>
    <springProperty scope="context" name="syslogPort" source="kl.base.log.syslogPort" defaultValue="514"/>
    <springProperty scope="context" name="syslogProtocol" source="kl.base.log.syslogProtocol" defaultValue="TCP"/>
    <springProperty scope="context" name="server_name" source="spring.application.name" defaultValue="ngpki-base-management"/>

    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="kl.nbase.log.filter.LogMaskingPatternLayout">
                <maskPattern>\bAuthorization[=:\s]+\s*([^\s]+)\b</maskPattern>
                <maskPattern>\bpassword[=:\s]+\s*([^\s]+)\b</maskPattern>
                <maskPattern>\bsecureBackupKey[=:\s]+\s*([^\s]+)\b</maskPattern>
                <maskLevel>DEBUG</maskLevel>
                <maskClassNames>org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor,org.eclipse.jetty.server.HttpChannel,org.eclipse.jetty.http.HttpParser</maskClassNames>
                <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %highlight([${server_name}:%ip]) %highlight([bizId=%X{biz_id}]) %highlight([clientIp=%X{client_ip_port}]) %highlight([%thread]) %highlight([traceId=%X{trace_id}]) %highlight([spanId=%X{span_id}]) %-5level %cyan(%logger{36}:%L) - %msg%n
                </Pattern>
            </layout>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="RollingFile" class="kl.npki.base.service.common.log.ProtectLogRollingFileAppender">
        <file>${LOG_PATH}/${server_name}.log</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="kl.nbase.log.filter.LogMaskingPatternLayout">
                <maskPattern>\bAuthorization[=:\s]+\s*([^\s]+)\b</maskPattern>
                <maskPattern>\bsecureBackupKey[=:\s]+\s*([^\s]+)\b</maskPattern>
                <maskPattern>\bpassword[=:\s]+\s*([^\s]+)\b</maskPattern>
                <maskLevel>DEBUG</maskLevel>
                <maskClassNames>org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor,org.eclipse.jetty.server.HttpChannel,org.eclipse.jetty.http.HttpParser</maskClassNames>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [${server_name}:%ip] [bizId=%X{biz_id}] [clientIp=%X{client_ip_port}] [%thread] [traceId=%X{trace_id}] [spanId=%X{span_id}] %-5level %logger{36}:%L - %msg%n</pattern>
            </layout>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${server_name}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--&lt;!&ndash; 每产生一个日志文件，该日志文件的保存期限天数 &ndash;&gt;-->
            <MaxHistory>${maxHistory}</MaxHistory>
            <!--保存文件的大小，超过该大小自动创建新文件。-->
            <maxFileSize>${maxFileSize}</maxFileSize>
            <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <!--默认false。如果设置为true，再项目启动的时候会自动删除老的日志文件-->
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <appender name="AsyncRollingFile" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RollingFile" />
        <!-- 设置异步阻塞队列的大小，为了不丢失日志建议设置的大一些 -->
        <queueSize>10000</queueSize>
        <!-- 设置丢弃DEBUG、TRACE、INFO日志的阀值，不丢失 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 设置队列入队时非阻塞，当队列满时会直接丢弃日志，但是对性能提升极大 -->
        <neverBlock>true</neverBlock>
    </appender>

    <!-- 定义一个 SyslogAppender，默认使用UDP协议 -->
    <appender name="AuditServiceUDP" class="ch.qos.logback.classic.net.SyslogAppender">
        <syslogHost>${syslogIp}</syslogHost>
        <port>${syslogPort}</port>
        <facility>LOCAL0</facility>
        <suffixPattern>%-4relative [%thread]  [%d{yyyy-MM-dd HH:mm:ss:SSS}] - %msg</suffixPattern>
    </appender>

    <!-- 定义一个TCP协议的SyslogAppender -->
    <appender name="AuditServiceTCP" class="kl.npki.base.core.log.appender.SysLogAppenderTCP">
        <syslogHost>${syslogIp}</syslogHost>
        <port>${syslogPort}</port>
        <facility>LOCAL0</facility>
        <suffixPattern>%-4relative [%thread]  [%d{yyyy-MM-dd HH:mm:ss:SSS}] - %msg</suffixPattern>
    </appender>

    <!-- 定义一个特定的 logger，只有该类产生的日志才会发送到 syslog -->
    <logger name="kl.npki.base.core.utils.SysLogSendUtil"  level="info" additivity="false">
        <if condition='${syslogEnable}'>
            <then>
                <if condition='"${syslogProtocol}".equals("UDP")'>
                    <then>
                        <appender-ref ref="AuditServiceUDP"/>
                    </then>
                    <else>
                        <appender-ref ref="AuditServiceTCP"/>
                    </else>
                </if>
            </then>
        </if>
    </logger>

    <root level="${level}">
        <appender-ref ref="Console" />
        <appender-ref ref="AsyncRollingFile"/>
    </root>
</configuration>
