INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROL<PERSON>_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448512, null, '33', '402774087329448337', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448513, null, '33', '402774087329448410', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448514, null, '33', '402567276064211022', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448589, null, '33', '900', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448590, null, '33', '402567276064210971', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448591, null, '33', '402567276064210972', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448592, null, '33', '402567276064210973', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448593, null, '33', '402567276064210974', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448594, null, '33', '402567276064210975', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448595, null, '33', '402567276064211004', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448596, null, '33', '402567276064211005', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448597, null, '33', '402567276064211037', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448598, null, '33', '402567276064211038', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448599, null, '33', '402567276064211083', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448600, null, '33', '402567276064211155', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448601, null, '33', '402567276064211156', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448602, null, '33', '402567276064211157', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448603, null, '33', '402567276064211158', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448604, null, '33', '402774087329448311', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448605, null, '33', '402774087329448407', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (403431509383512085, null, '33', '403431509383512079', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (403431509383512086, null, '33', '403431509383512082', null, null, null, null, 0, null);

INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159653, null, '33', '402567276064211128', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159654, null, '33', '402567276064211129', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159655, null, '33', '402567276064211130', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159656, null, '33', '402567276064211131', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159657, null, '33', '402567276064211159', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159658, null, '33', '402774087329448318', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159675, null, '33', '405536094898159660', null, null, null, null, 0, null);
