INSERT INTO T_ROLE (ID, TENANT_ID, ROLE_NAME, ROLE_NAME_I18N_KEY, ROLE_CODE, PARENT_ROLE_CODE, REMARK, IS_CUSTOM, IS_DELETE) VALUES (7, null, '司法取证人员', 'kl.npki.management.core.i18n_law_admin', '48', '8', 'kl.npki.base.management.i18n_law_admin_remark', 0, 0);

-- km的资源id范围为 101~200
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (101, '高级配置', 'kl.npki.km.management.i18n_advanced_configuration', '100_900', 'SYSTEM_MANAGE_KMCONFIG', '100', 2, null, null, 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (102, '密钥管理', 'kl.npki.km.management.i18n_km_management', '600', 'KM_MANAGE', null, 2, null, null, 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (103, '在用密钥库管理', 'kl.npki.km.management.i18n_current_in_use_key_store_management', '600_100', 'KM_MANAGE_KEYSTORE', '600', 2, null, null, 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (104, '历史密钥库管理', 'kl.npki.km.management.i18n_historical_key_store_management', '600_200', 'KM_MANAGE_KEYHISTORY', '600', 2, null, null, 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (105, '备用密钥库管理', 'kl.npki.km.management.i18n_backup_key_store_management', '600_300', 'KM_MANAGE_KEYALTERNATE', '600', 2, null, null, 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (107, '司法取证', 'kl.npki.km.management.i18n_judicial_evidence_collection', '700', 'JUDICIAL_MANAGE', null, 2, null, null, 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (108, '统计报表', 'kl.npki.km.management.i18n_statistical_report', '800', 'KM_REPORT_MANAGE', null, 2, null, null, 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (109, '密钥库统计', 'kl.npki.km.management.i18n_key_store_statistics', '800_100', 'KM_REPORT_MANAGE_KEYSTORE', '800', 2, null, null, 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (110, '密钥使用数据报表', 'kl.npki.km.management.i18n_key_usage_data_report', '800_200', 'KM_REPORT_MANAGE_KEYUSEDATA', '800', 2, null, null, 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (113, 'CA管理', 'kl.npki.km.management.i18n_ca_management', '900', 'KM_CA_MANAGE_ACCESSCA', null, 2, null, null, 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (114, 'CA服务统计报表', 'kl.npki.km.management.i18n_ca_service_statistical_report', '800_500', 'KM_REPORT_MANAGE_CAKEY ', '800', 2, null, null, 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (115, '协同服务统计报表', 'kl.npki.km.management.i18n_sks_service_stat_report', '800_600', 'KM_REPORT_MANAGE_SKSKEY ', '800', 2, null, null, 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE,RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (117, '导入管理员', 'kl.npki.km.management.i18n_admin_import', '200_100_import', 'ADMIN_IMPORT', '100', 2, null, null, 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE,RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, IS_DELETE) VALUES (412945574908723211, '导入管理员证书', 'kl.npki.base.management.i18n_import_administrator_certificate','402567276064211047', null, '100_100', 4, '/admin/importCert', 'POST', 0, 0);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064210963, '归档密钥', 'kl.npki.km.management.i18n_archive_key', '402567276064210963', null, '600_200', 4, '/keyCurrent/archive/*', 'PUT', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064210965, '获取KM配置', 'kl.npki.km.management.i18n_get_km_conf', '402567276064210965', null, '100_900', 4, '/config/km', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064210966, '修改KM配置', 'kl.npki.km.management.i18n_update_km_conf', '402567276064210966', null, '100_900', 4, '/config/km', 'PUT', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064210967, '获取密钥配置', 'kl.npki.km.management.i18n_get_key_conf', '402567276064210967', null, '600_300', 4, '/config/key', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064210968, '修改密钥配置', 'kl.npki.km.management.i18n_update_key_conf', '402567276064210968', null, '600_300', 4, '/config/key', 'PUT', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064210969, '获取归档配置', 'kl.npki.km.management.i18n_get_archive_key_config', '402567276064210969', null, '600_200', 4, '/config/archive', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064210970, '修改归档配置', 'kl.npki.km.management.i18n_update_archive_key_conf', '402567276064210970', null, '600_200', 4, '/config/archive', 'PUT', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064210971, 'CA更新', 'kl.npki.km.management.i18n_ca_update', '402567276064210971', null, '900', 4, '/ca/update/*', 'PUT', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064210972, 'CA解冻', 'kl.npki.km.management.i18n_ca_unfreeze', '402567276064210972', null, '900', 4, '/ca/unfreeze/*', 'PUT', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064210973, 'CA废除', 'kl.npki.km.management.i18n_ca_revoke', '402567276064210973', null, '900', 4, '/ca/revoke/*', 'PUT', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064210974, 'CA恢复', 'kl.npki.km.management.i18n_ca_recovery', '402567276064210974', null, '900', 4, '/ca/recovery/*', 'PUT', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064210975, 'CA冻结', 'kl.npki.km.management.i18n_ca_freeze', '402567276064210975', null, '900', 4, '/ca/freeze/*', 'PUT', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211004, '更新CA站点证书', 'kl.npki.km.management.i18n_update_ca_ssl_cert', '402567276064211004', null, '900', 4, '/sslClientCert/updateCaSSLCert', 'POST', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211005, '签发CA站点证书', 'kl.npki.km.management.i18n_sign_ca_ssl_cert', '402567276064211005', null, '900', 4, '/sslClientCert/signCaSSLCert', 'POST', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211020, '司法恢复', 'kl.npki.km.management.i18n_law_recover', '402567276064211020', null, '700', 4, '/law/recover', 'POST', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211021, '签发管理员证书', 'kl.npki.km.management.i18n_issue_admin_cert', '402567276064211021', null, '200_100', 4, '/kmAdmin/issueAdminCert', 'POST', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211022, '管理员初始化签发证书', 'kl.npki.km.management.i18n_init_admin_cert', '402567276064211022', null, '100_100', 4, '/kmAdmin/init/issueAdmin', 'POST', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211023, '延期管理员证书', 'kl.npki.km.management.i18n_postpone_admin_cert', '402567276064211023', null, '200_100', 4, '/kmAdmin/extendAdminCert', 'POST', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211024, '备份管理员证书', 'kl.npki.km.management.i18n_backup_admin_cert', '402567276064211024', null, '200_200', 4, '/kmAdmin/backup/issueAdmin', 'POST', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211037, 'CA资源添加', 'kl.npki.km.management.i18n_ca_resource_addition', '402567276064211037', null, '900', 4, '/ca/addKeyNum/*', 'POST', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211038, 'CA接入', 'kl.npki.km.management.i18n_ca_access_service', '402567276064211038', null, '900', 4, '/ca/access', 'POST', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211071, '密钥库统计', 'kl.npki.km.management.i18n_key_vault_stats', '402567276064211071', null, '800_100', 4, '/statistic/keystore', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211072, '导出密钥库统计报表', 'kl.npki.km.management.i18n_exported_key_vault_statistical_report', '402567276064211072', null, '800_100', 4, '/statistic/keystore/export', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211073, '密钥使用情况统计', 'kl.npki.km.management.i18n_total_key_usage_count', '402567276064211073', null, '800_200', 4, '/statistic/keyUse', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211074, '导出密钥使用情况统计报表', 'kl.npki.km.management.i18n_exported_key_usage_stat_report', '402567276064211074', null, '800_200', 4, '/statistic/keyUse/export', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211077, 'CA服务统计报表', 'kl.npki.km.management.i18n_ca_service_statistical_report', '402567276064211077', null, '800_500', 4, '/statistic/ca', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211078, '导出CA服务统计报表', 'kl.npki.km.management.i18n_exported_ca_service_report', '402567276064211078', null, '800_500', 4, '/statistic/ca/export', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211083, '导出CA站点证书', 'kl.npki.km.management.i18n_export_ca_ssl_cert', '402567276064211083', null, '900', 4, '/sslClientCert/exportCaSSLCert/*', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211114, '获取密钥轨迹详情', 'kl.npki.km.management.i18n_query_key_trace_detail', '402567276064211114', null, '600_100', 4, '/keyTrace/detail/*', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211115, '查询备用密钥库', 'kl.npki.km.management.i18n_bak_current_key_list', '402567276064211115', null, '600_300', 4, '/keyPool/list', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211116, '查询历史密钥库', 'kl.npki.km.management.i18n_his_admin_list', '402567276064211116', null, '600_200', 4, '/keyHistory/list', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211117, '获取历史密钥详情', 'kl.npki.km.management.i18n_his_query_current_key_detail', '402567276064211117', null, '600_200', 4, '/keyHistory/detail/*', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211118, '获取当前密钥库', 'kl.npki.km.management.i18n_current_key_list', '402567276064211118', null, '600_200', 4, '/keyCurrent/list', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211119, '获取当前密钥详情', 'kl.npki.km.management.i18n_query_current_key_detail', '402567276064211119', null, '600_100', 4, '/keyCurrent/detail/*', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211120, '获取待归档密钥', 'kl.npki.km.management.i18n_archive_key_list', '402567276064211120', null, '600_200', 4, '/keyCurrent/archiveList', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211128, '密钥状态数据统计', 'kl.npki.km.management.i18n_key_status_data_stats', '402567276064211128', null, '000', 4, '/home/<USER>', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211129, '密钥库数据统计', 'kl.npki.km.management.i18n_key_vault_data_stats', '402567276064211129', null, '000', 4, '/home/<USER>', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211130, 'CA密钥使用数据统计', 'kl.npki.km.management.i18n_key_usage_data_stats', '402567276064211130', null, '000', 4, '/home/<USER>/*', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211131, 'CA密钥服务调用情况', 'kl.npki.km.management.i18n_ca_key_service_invocation_stats', '402567276064211131', null, '000', 4, '/home/<USER>/*', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211153, '获取支持的非对称密钥类型(包括抗量子算法)', 'kl.npki.km.management.i18n_key_type_list', '402567276064211153', null, '200_100', 4, '/config/keyType', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211154, '手动归档触发', 'kl.npki.km.management.i18n_trigger_key_archive_job', '402567276064211154', null, '600_200', 4, '/config/archive/trigger', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211155, 'CA版本列表', 'kl.npki.km.management.i18n_ca_version_list', '402567276064211155', null, '900', 4, '/ca/version/list', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211156, 'CA密钥服务响应封装格式版本', 'kl.npki.km.management.i18n_ca_key_response_standard_version_list', '402567276064211156', null, '900', 4, '/ca/standard-version/list', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211157, '查询CA信息列表', 'kl.npki.km.management.i18n_ca_info_list_query', '402567276064211157', null, '900', 4, '/ca/list', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211158, '查询CA信息详情', 'kl.npki.km.management.i18n_query_ca_info_details', '402567276064211158', null, '900', 4, '/ca/detail/*', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402567276064211159, '查询所有CA', 'kl.npki.km.management.i18n_query_all_cas', '402567276064211159', null, '000', 4, '/ca/all', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448312, '获取支持的非对称密钥类型(包括抗量子算法)', 'kl.npki.km.management.i18n_key_type_list', '402774087329448311', null, '900', 4, '/config/keyType', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448319, '获取归档配置', 'kl.npki.km.management.i18n_get_archive_key_config', '402774087329448318', null, '000', 4, '/config/archive', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448332, '查询协同服务统计报表', null, '402774087329448331', null, '800_600', 4, '/statistic/sks', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448335, '导出协同服务统计报表', null, '402774087329448334', null, '800_600', 4, '/statistic/sks/export', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448347, '查询系统通用配置', 'kl.npki.base.management.i18n_query_system_general_configuration', '402774087329448346', null, '100_900', 4, '/sysConfig/info', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448354, '保存系统通用配置', 'kl.npki.base.management.i18n_save_system_general_configuration', '402774087329448353', null, '100_900', 4, '/sysConfig/save', 'POST', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448378, '获取当前密钥库', 'kl.npki.km.management.i18n_current_key_list', '402774087329448377', null, '600_100', 4, '/keyCurrent/list', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448387, '获取密钥轨迹详情', 'kl.npki.km.management.i18n_query_key_trace_detail', '402774087329448386', null, '600_200', 4, '/keyTrace/detail/*', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448408, '获取支持的非对称密钥类型(包括抗量子算法)', 'kl.npki.km.management.i18n_key_type_list', '402774087329448407', null, '900', 4, '/config/keyType', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448411, '获取支持的非对称密钥类型(包括抗量子算法)', 'kl.npki.km.management.i18n_key_type_list', '402774087329448410', null, '000', 4, '/config/keyType', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448415, '获取当前密钥库', 'kl.npki.km.management.i18n_current_key_list', '402774087329448414', null, '700', 4, '/keyCurrent/list', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448422, '查询历史密钥库', 'kl.npki.km.management.i18n_his_admin_list', '402774087329448421', null, '700', 4, '/keyHistory/list', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (403431509383512080, '修改KM配置', 'kl.npki.km.management.i18n_update_km_conf', '403431509383512079', null, '900', 4, '/config/km', 'PUT', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (403431509383512083, '获取KM配置', 'kl.npki.km.management.i18n_get_km_conf', '403431509383512082', null, '900', 4, '/config/km', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (403502625451999632, '查询司法取证管理员列表', 'kl.npki.km.management.i18n_query_law_recover_admin_list', '403502625451999632', null, '700', 4, '/kmAdmin/lawRecover/adminList', 'GET', 0, null, null, null, null, null, null, null, 0, null);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, IS_DELETE) VALUES (406330079732367362, '申请延期管理员', 'kl.npki.base.management.i18n_apply_for_extension_administrator', '406330079732367361', null, '200_200', 4, '/admin/requestExtendAdmin/*', 'PUT', 0, 0);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, IS_DELETE) VALUES (406330079732367365, '延期管理员证书', 'kl.npki.km.management.i18n_postpone_admin_cert', '406330079732367364', null, '200_200', 4, '/kmAdmin/extendAdminCert', 'POST', 0, 0);
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, IS_DELETE) VALUES (406330079732367368, '导入延期管理员证书', 'kl.npki.base.management.i18n_import_extended_administrator_certificate', '406330079732367367', null, '200_200', 4, '/admin/importExtendCert', 'POST', 0, 0);

-- TODO 删除下面的权限，由于前端的首页部分暂时无法修改，需要为所有管理员添加首页权限
INSERT INTO T_RESOURCE (ID, RESOURCE_NAME, RESOURCE_NAME_I18N_KEY, RESOURCE_CODE, REMARK, PARENT_RESOURCE_CODE, RESOURCE_TYPE, RESOURCE_URL, REQUEST_METHOD, IS_ANONYMOUS, SHOW_ORDER, TENANT_ID, RESOURCE_PATH, PARENT_PATH, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159661, '获取密钥配置', 'kl.npki.km.management.i18n_get_key_conf', '405536094898159660', null, '000', 4, '/config/key', 'GET', 0, null, null, null, null, null, null, null, 0, null);
-- TODO END


-- 资源角色中间表初始化问题脚本添加
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448484, null, '1', '402774087329448337', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448485, null, '1', '402774087329448410', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448486, null, '1', '402567276064211021', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448487, null, '1', '402567276064211023', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448488, null, '1', '402567276064211153', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448489, null, '1', '402567276064211024', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448491, null, '2', '402774087329448337', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448492, null, '2', '402774087329448410', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448493, null, '2', '402567276064211024', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448496, null, '4', '402774087329448337', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448497, null, '4', '402774087329448410', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448498, null, '4', '402567276064211021', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448499, null, '4', '402567276064211023', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448500, null, '4', '402567276064211153', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448501, null, '4', '402567276064211024', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448503, null, '8', '402774087329448337', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448504, null, '8', '402774087329448410', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448505, null, '8', '402567276064211021', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448506, null, '8', '402567276064211023', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448507, null, '8', '402567276064211153', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448509, null, '16', '402774087329448337', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448510, null, '16', '402774087329448410', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448516, null, '32', '402567276064211128', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448517, null, '32', '402567276064211129', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448518, null, '32', '402567276064211130', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448519, null, '32', '402567276064211131', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448520, null, '32', '402567276064211159', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448521, null, '32', '402774087329448318', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448522, null, '32', '402774087329448337', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448523, null, '32', '402774087329448410', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448524, null, '32', '402567276064211022', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448530, null, '32', '100_900', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448531, null, '32', '402567276064210965', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448532, null, '32', '402567276064210966', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448533, null, '32', '402774087329448346', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448534, null, '32', '402774087329448353', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448535, null, '32', '600', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448536, null, '32', '600_100', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448537, null, '32', '402567276064211114', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448538, null, '32', '402567276064211119', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448539, null, '32', '402774087329448377', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448540, null, '32', '600_200', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448541, null, '32', '402567276064210963', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448542, null, '32', '402567276064210969', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448543, null, '32', '402567276064210970', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448544, null, '32', '402567276064211116', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448545, null, '32', '402567276064211117', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448546, null, '32', '402567276064211118', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448547, null, '32', '402567276064211120', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448548, null, '32', '402567276064211154', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448549, null, '32', '402774087329448386', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448550, null, '32', '600_300', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448551, null, '32', '402567276064210967', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448552, null, '32', '402567276064210968', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448553, null, '32', '402567276064211115', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448554, null, '32', '700', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448555, null, '32', '402567276064211020', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448556, null, '32', '402774087329448414', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448557, null, '32', '402774087329448421', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448558, null, '32', '800', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448559, null, '32', '800_100', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448560, null, '32', '402567276064211071', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448561, null, '32', '402567276064211072', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448562, null, '32', '800_200', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448563, null, '32', '402567276064211073', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448564, null, '32', '402567276064211074', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448565, null, '32', '800_500', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448566, null, '32', '402567276064211077', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448567, null, '32', '402567276064211078', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448571, null, '32', '900', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448572, null, '32', '402567276064210971', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448573, null, '32', '402567276064210972', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448574, null, '32', '402567276064210973', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448575, null, '32', '402567276064210974', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448576, null, '32', '402567276064210975', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448577, null, '32', '402567276064211004', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448578, null, '32', '402567276064211005', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448579, null, '32', '402567276064211037', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448580, null, '32', '402567276064211038', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448581, null, '32', '402567276064211083', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448582, null, '32', '402567276064211155', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448583, null, '32', '402567276064211156', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448584, null, '32', '402567276064211157', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448585, null, '32', '402567276064211158', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448586, null, '32', '402774087329448311', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (402774087329448587, null, '32', '402774087329448407', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (403431509383512088, null, '32', '403431509383512079', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (403431509383512089, null, '32', '403431509383512082', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (403502625451999715, null, '32', '403502625451999632', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, ROLE_CODE, RESOURCE_CODE) VALUES (406330079732367370, '1', '406330079732367361');
INSERT INTO T_ROLE_RESOURCE_LINK (ID, ROLE_CODE, RESOURCE_CODE) VALUES (406330079732367371, '1', '406330079732367364');
INSERT INTO T_ROLE_RESOURCE_LINK (ID, ROLE_CODE, RESOURCE_CODE) VALUES (406330079732367372, '1', '406330079732367367');
INSERT INTO T_ROLE_RESOURCE_LINK (ID, ROLE_CODE, RESOURCE_CODE) VALUES (406330079732367374, '2', '406330079732367361');
INSERT INTO T_ROLE_RESOURCE_LINK (ID, ROLE_CODE, RESOURCE_CODE) VALUES (406330079732367375, '2', '406330079732367364');
INSERT INTO T_ROLE_RESOURCE_LINK (ID, ROLE_CODE, RESOURCE_CODE) VALUES (406330079732367376, '2', '406330079732367367');
INSERT INTO T_ROLE_RESOURCE_LINK (ID, ROLE_CODE, RESOURCE_CODE) VALUES (406330079732367378, '4', '406330079732367361');
INSERT INTO T_ROLE_RESOURCE_LINK (ID, ROLE_CODE, RESOURCE_CODE) VALUES (406330079732367379, '4', '406330079732367364');
INSERT INTO T_ROLE_RESOURCE_LINK (ID, ROLE_CODE, RESOURCE_CODE) VALUES (406330079732367380, '4', '406330079732367367');
INSERT INTO T_ROLE_RESOURCE_LINK (ID, ROLE_CODE, RESOURCE_CODE) VALUES (406330079732367381, '1', '200_100_import');
INSERT INTO T_ROLE_RESOURCE_LINK (ID, ROLE_CODE, RESOURCE_CODE) VALUES (406330079732367382, '4', '200_100_import');
INSERT INTO T_ROLE_RESOURCE_LINK (ID, ROLE_CODE, RESOURCE_CODE) VALUES (406330079732367383, '8', '200_100_import');

-- TODO 删除下面的权限，由于前端的首页部分暂时无法修改，需要为所有管理员添加首页权限
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159618, null, '1', '402567276064211128', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159619, null, '1', '402567276064211129', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159620, null, '1', '402567276064211130', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159621, null, '1', '402567276064211131', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159622, null, '1', '402567276064211159', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159623, null, '1', '402774087329448318', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159625, null, '2', '402567276064211128', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159626, null, '2', '402567276064211129', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159627, null, '2', '402567276064211130', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159628, null, '2', '402567276064211131', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159629, null, '2', '402567276064211159', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159630, null, '2', '402774087329448318', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159632, null, '4', '402567276064211128', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159633, null, '4', '402567276064211129', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159634, null, '4', '402567276064211130', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159635, null, '4', '402567276064211131', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159636, null, '4', '402567276064211159', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159637, null, '4', '402774087329448318', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159639, null, '8', '402567276064211128', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159640, null, '8', '402567276064211129', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159641, null, '8', '402567276064211130', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159642, null, '8', '402567276064211131', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159643, null, '8', '402567276064211159', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159644, null, '8', '402774087329448318', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159646, null, '16', '402567276064211128', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159647, null, '16', '402567276064211129', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159648, null, '16', '402567276064211130', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159649, null, '16', '402567276064211131', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159650, null, '16', '402567276064211159', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159651, null, '16', '402774087329448318', null, null, null, null, 0, null);

INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159663, null, '1', '405536094898159660', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159665, null, '2', '405536094898159660', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159667, null, '4', '405536094898159660', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159669, null, '8', '405536094898159660', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159671, null, '16', '405536094898159660', null, null, null, null, 0, null);
INSERT INTO T_ROLE_RESOURCE_LINK (ID, TENANT_ID, ROLE_CODE, RESOURCE_CODE, INCLUDE_SUB_RESOURCE, CREATE_BY, UPDATE_BY, UPDATE_TIME, IS_DELETE, FULL_DATA_HASH) VALUES (405536094898159673, null, '32', '405536094898159660', null, null, null, null, 0, null);

-- TODO END


-- 预置JKS
INSERT INTO T_FILE_CONFIG (ID, FILE_NAME, FILE_PATH, FILE_CONTENT_BASE64, UPDATE_BY, UPDATE_TIME, IS_DELETE)
VALUES (1, 'siteKeyStore.jks', 'config/siteKeyStore.jks',
        '/u3+7QAAAAIAAAACAAAAAQADZW5jAAABiNLESPcAAADUMIHRMA4GCisGAQQBKgIRAQEFAASBvv4RlV7tA7dVMAbl0Y50/+WCmncK9KBb+OxptHGT0URkSl0K6ZA98qX1phWp8TDlwdcFfEdyZ952SRc+RtVDJbCK/PfT/lk++mg8xiIX9sesHdNpdxFQIZc88NYgxbql3t4mUmSbPFkEz+c62sqciJ2ycaZLIhBA8SY5PoVoOz9O0gjxVA9WOP2lkq+C7WKELJHF3UTk20BVtUG9Y8dezMlNkOFXYURubu6YTlIcwg6Cs+mOmnWJFjkBABnpjloAAAABAAVYLjUwOQAAAfQwggHwMIIBlKADAgECAgxXIQAAAAAAB/J83RswDAYIKoEcz1UBg3UFADAmMQswCQYDVQQGEwJDTjEXMBUGA1UEAwwOc3ViY2Ffcm9vdF9zbTIwIBcNMjMwNjE4MTYwMDAwWhgPMjExMjA2MDgxNTU5NTlaMEMxCzAJBgNVBAYTAkNOMREwDwYDVQQIDAhzaGFuZ2hhaTEQMA4GA1UEBwwHc2hhbmdoYTEPMA0GA1UEAwwGa2wuY29tMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEkXxmxwV9BUgqcpM30doQNhtaTtq121ZgaNI/UuyexHkgWtZyMPikW+3hqocBmUx5Sw12ftIFRmFxxfIS0gzQMqOBhjCBgzARBglghkgBhvhCAQEEBAMCAEAwCwYDVR0PBAQDAgD4MAwGA1UdEwQFMAMBAQAwEwYDVR0lBAwwCgYIKwYBBQUHAwEwHwYDVR0jBBgwFoAUCMApMk7K9ukhlMNWSEoxL2cUSlIwHQYDVR0OBBYEFI7dotUKc8/u3gh2kQBc2dwBSkxHMAwGCCqBHM9VAYN1BQADSAAwRQIhALWbAgpRL4L7XVDRoXplqZPAHMkZ768bTj3zkCYsNvb9AiAZJBs25vtLAZ9ZIXuGjmcjvwjM47ci7oOhqpxc2ZgqSQAAAAEAA3NpZwAAAYjSxEj2AAAA1DCB0TAOBgorBgEEASoCEQEBBQAEgb5qNiuquh1U5AVrexOBvolRsL5UCOwbN1fB0SwpkmHI8f6EVOU5ase5DRP3hFRyQXUZSBfdc7Ko6XxrZQdvcZ1x5gRXWeyFH0y6cPg4qX6fizrOJt0oFKZ6WdgE5cL9Ky685sYBQyyjJLrmk30o5qGrhjGjcjGNKi4NWKmllUqpDCgOJxSAxNAjXRNDJTOy2nytXZ18ZhwQb93iR8gmTAZzXCO+PtNoKar3/k5SHMIOgrPpjpp1iRY5AQAZ6Y5aAAAAAQAFWC41MDkAAAH0MIIB8DCCAZSgAwIBAgIMVyEAAAAAAAfyfN0bMAwGCCqBHM9VAYN1BQAwJjELMAkGA1UEBhMCQ04xFzAVBgNVBAMMDnN1YmNhX3Jvb3Rfc20yMCAXDTIzMDYxODE2MDAwMFoYDzIxMTIwNjA4MTU1OTU5WjBDMQswCQYDVQQGEwJDTjERMA8GA1UECAwIc2hhbmdoYWkxEDAOBgNVBAcMB3NoYW5naGExDzANBgNVBAMMBmtsLmNvbTBZMBMGByqGSM49AgEGCCqBHM9VAYItA0IABJF8ZscFfQVIKnKTN9HaEDYbWk7atdtWYGjSP1LsnsR5IFrWcjD4pFvt4aqHAZlMeUsNdn7SBUZhccXyEtIM0DKjgYYwgYMwEQYJYIZIAYb4QgEBBAQDAgBAMAsGA1UdDwQEAwIA+DAMBgNVHRMEBTADAQEAMBMGA1UdJQQMMAoGCCsGAQUFBwMBMB8GA1UdIwQYMBaAFAjAKTJOyvbpIZTDVkhKMS9nFEpSMB0GA1UdDgQWBBSO3aLVCnPP7t4IdpEAXNncAUpMRzAMBggqgRzPVQGDdQUAA0gAMEUCIQC1mwIKUS+C+11Q0aF6ZamTwBzJGe+vG04985AmLDb2/QIgGSQbNub7SwGfWSF7ho5nI78IzOO3Iu6DoaqcXNmYKkmZdz8IogMgsEMBSCWVpb498QZ9Gw==',
        NULL, NULL, 0);
INSERT INTO T_FILE_CONFIG (ID, FILE_NAME, FILE_PATH, FILE_CONTENT_BASE64, UPDATE_BY, UPDATE_TIME, IS_DELETE)
VALUES (2, 'trustStore.jks', 'config/trustStore.jks',
        '/u3+7QAAAAIAAAACAAAAAgAGcm9vdGNhAAABiNLESPwABVguNTA5AAABjzCCAYswggEuoAMCAQICDFb5/wAmKZFxwEKUAzAMBggqgRzPVQGDdQUAMCcxCzAJBgNVBAYTAkNOMRgwFgYDVQQDDA9yb290Y2Ffcm9vdF9zbTIwIBcNMjMwNjE1MDcxNDQwWhgPMjEyMzA1MjIwNzE0NDBaMCcxCzAJBgNVBAYTAkNOMRgwFgYDVQQDDA9yb290Y2Ffcm9vdF9zbTIwWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAARb61wRpkybxaMPjApDxxKExiWrpPA4M+umy2boGX5y50Os6FvCqP5JXsupSOo8MwAueA2YCJskLqlxYmpexSYnozwwOjALBgNVHQ8EBAMCAIYwDAYDVR0TBAUwAwEB/zAdBgNVHQ4EFgQU4Qw5Zik4xZkpuQcDnBwFONIFc+YwDAYIKoEcz1UBg3UFAANJADBGAiEAlQ/2y6ux/6EzR9Vu29YPKTiI+u6xqsqClHUlarPDWacCIQDhDeI3gobCemXPG45CDIREvmA8idgKdssyK8wbTE/NnwAAAAIABXN1YmNhAAABiNLESPwABVguNTA5AAABrzCCAaswggFOoAMCAQICDFcN/wA7P98v1b1wwzAMBggqgRzPVQGDdQUAMCcxCzAJBgNVBAYTAkNOMRgwFgYDVQQDDA9yb290Y2Ffcm9vdF9zbTIwIBcNMjMwNjE4MTYwMDAwWhgPMjExMzA1MjgxNTU5NTlaMCYxCzAJBgNVBAYTAkNOMRcwFQYDVQQDDA5zdWJjYV9yb290X3NtMjBZMBMGByqGSM49AgEGCCqBHM9VAYItA0IABPwLy3HOTUyScAfi3+oRvA2xSSsQO2itFFzNXYTeni1cvsDaQ1YnbGS4NHbxcS/ZOLQeqT6GBQtZ1Sj3fYn/G4ujXTBbMAsGA1UdDwQEAwIAhjAMBgNVHRMEBTADAQH/MB8GA1UdIwQYMBaAFOEMOWYpOMWZKbkHA5wcBTjSBXPmMB0GA1UdDgQWBBQIwCkyTsr26SGUw1ZISjEvZxRKUjAMBggqgRzPVQGDdQUAA0kAMEYCIQC/3fK/312ydckewBzbtKmsgEceW0u31aiY4/STgj/89AIhANDQRGNv8eJp8OOC98oW8EoaIj7qGRdwQSPG9pnm4MiXu4KNw/mlQLRi3fBYPv+aHHOK888=',
        NULL, NULL, 0);
