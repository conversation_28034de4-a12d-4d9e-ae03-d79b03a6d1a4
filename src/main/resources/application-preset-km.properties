# mgmt服务监听端口
server.port=11080
# mgmt在http端口(11080)上开启https服务
server.ssl.enabled=false
# 是否启用客户端认证
server.ssl.client-auth=want

# service(netty) http(s)服务监听端口，可以单独启动
kl.base.service.http.port=18880
# 是否启用service(netty) http(s)服务服务
kl.base.service.http.enabled=false
# 是否开启ssl，如果不需要开启ssl，则此处修改为false，下面的配置也不需要配置
kl.base.service.http.sslEnabled=false
# 客户端认证，可选值：NONE,OPTIONAL,REQUIRE
kl.base.service.http.sslClientAuth=OPTIONAL

# service(netty) tcp服务
# 监听端口
kl.base.service.tcp.port=18800
# 是否启用
kl.base.service.tcp.enabled=true
# 是否开启ssl，如果不需要开启ssl，则此处修改为false，下面的配置也不需要配置
kl.base.service.tcp.sslEnabled=false
# 客户端认证，可选值：NONE,OPTIONAL,REQUIRE
kl.base.service.tcp.sslClientAuth=OPTIONAL

server.api-prefix=/mgmt/v1

# mgmt ssl配置
server.ssl.protocol=TLSv1.2
server.ssl.keyAlias=ssl_sign_server_cert
#server.ssl.ciphers=GMVPN_SM2_WITH_SM4_CBC_SM3,GMVPN_SM2_WITH_SM4_GCM_SM3
server.ssl.key-store=config/ssl-site.p12
server.ssl.key-store-type=PKCS12
server.ssl.key-store-password=koal@ssl@admin
server.ssl.key-store-provider=BC
server.ssl.key-password=koal@ssl@admin
server.ssl.trust-store=config/ssl-trust.p12
server.ssl.trust-store-password=koal@ssl@admin
server.ssl.trust-store-provider=BC
server.ssl.trust-store-type=PKCS12

# Netty http服务ssl配置
# 使用Netty启动https(支持SM2和RSA)服务
# service(netty)服务监听地址
kl.base.service.http.protocol=GMVPNv1.1
kl.base.service.http.address=0.0.0.0
# 业务线程数量
# 处理业务的时候会有一个线程池，当前配置就是给线程池使用
kl.base.service.http.bizThreadCount=10
# netty处理请求的线程池线程数量
kl.base.service.http.ioThreadCount=10
# http ssl配置
#kl.base.service.http.sslCiphers=GMVPN_SM2_WITH_SM4_CBC_SM3,GMVPN_SM2_WITH_SM4_GCM_SM3
kl.base.service.http.sslKeyStore=config/siteKeyStore.jks
kl.base.service.http.sslKeyStorePassword=KeyStorePassword
kl.base.service.http.sslKeyStoreType=JKS
kl.base.service.http.sslKeyStoreProvider=SUN
kl.base.service.http.sslTrustStore=config/trustStore.jks
kl.base.service.http.sslTrustStorePassword=KeyStorePassword
kl.base.service.http.sslTrustStoreType=JKS
kl.base.service.http.sslTrustStoreProvider=SUN

# tcp
# service(netty) tcp服务监听地址
kl.base.service.tcp.protocol=TLSv1.2
kl.base.service.tcp.sslKeyAlias=ssl_sign_server_cert
kl.base.service.tcp.address=0.0.0.0
kl.base.service.tcp.bizThreadCount=10
# 接口业务处理类，此处配置的是容器中业务处理实现类bean的名称
# 组件内置了一个业务处理实现类：nettyHttpServlet
# 如果当前配置的不是nettyHttpServlet，则不需要配置kl.base.service.tcp.httpRequestUri和kl.base.service.tcp.httpRequestHeaders，
# 反之必须配置kl.base.service.tcp.httpRequestUri，kl.base.service.tcp.httpRequestHeaders配置是可选的
kl.base.service.tcp.bizClassName=kl.npki.km.service.handler.KmServerHandler
kl.base.service.tcp.ioThreadCount=10
# 接口消息解码器，此处配置的是spring容器中消息解码器bean的名称
# 组件内置了两个消息解码器：asn1DerMsgDecoder、koalMsgCodec
kl.base.service.tcp.messageDecoderClassName=kl.nbase.netty.server.tcp.codec.Asn1DerMsgDecoder
# netty tcp服务 ssl配置
#kl.base.service.tcp.sslCiphers=GMVPN_SM2_WITH_SM4_CBC_SM3,GMVPN_SM2_WITH_SM4_GCM_SM3
kl.base.service.tcp.sslKeyStore=config/ssl-site.p12
kl.base.service.tcp.sslKeyStorePassword=koal@ssl@admin
kl.base.service.tcp.sslKeyStoreProvider=BC
kl.base.service.tcp.sslKeyStoreType=PKCS12
kl.base.service.tcp.sslTrustStore=config/ssl-trust.p12
kl.base.service.tcp.sslTrustStorePassword=koal@ssl@admin
kl.base.service.tcp.sslTrustStoreProvider=BC
kl.base.service.tcp.sslTrustStoreType=PKCS12
# sysInfo
# 系统信息
kl.base.sysInfo.name=kl.npki.km.management.i18n_sysInfo_name
## 版本
kl.base.sysInfo.version=SYT0901-G V6.8
## 内核版本
kl.base.sysInfo.kernelVersion=V9.1.0
## 定制版本/行业版本
kl.base.sysInfo.customizedVersion=
kl.base.sysInfo.shortName=NGPKI-KM

# sharding
kl.base.sharding.shardingEnabled=true
kl.base.sharding.readWriteEnabled=false
kl.base.sharding.databaseDiscoveryEnabled=false
kl.base.sharding.databaseShardingColumn=
kl.base.sharding.databaseShardingAlgorithm=
kl.base.sharding.multiMastersAndSlavesEnabled=false
kl.base.sharding.healthCheck.delay=0
kl.base.sharding.healthCheck.period=5
kl.base.sharding.healthCheck.enableAutoSwitch=false

# 限流组件
kl.base.traffic.enabled=false
kl.base.traffic.limiterType=sentinel
kl.base.traffic.defaultFlowRule.resource=default-rule
kl.base.traffic.defaultFlowRule.grade=1
kl.base.traffic.defaultFlowRule.count=10000
kl.base.traffic.defaultDegradeRule.resource=default-rule
kl.base.traffic.defaultDegradeRule.grade=2
kl.base.traffic.defaultDegradeRule.count=10
kl.base.traffic.defaultDegradeRule.minRequestAmount=50
kl.base.traffic.defaultDegradeRule.timeWindow=10
kl.base.traffic.defaultDegradeRule.slowRatioThreshold=0.6
kl.base.traffic.defaultDegradeRule.statIntervalMs=1000

kl.base.traffic.flowRules[0].resource=kmServiceGbv2
kl.base.traffic.flowRules[0].grade=1
kl.base.traffic.flowRules[0].count=10000
kl.base.traffic.flowRules[3].resource=getRandomNumber
kl.base.traffic.flowRules[3].grade=1
kl.base.traffic.flowRules[3].count=10000
kl.base.traffic.flowRules[4].resource=createPrivateKey
kl.base.traffic.flowRules[4].grade=1
kl.base.traffic.flowRules[4].count=10000
kl.base.traffic.flowRules[5].resource=getPrivateKey
kl.base.traffic.flowRules[5].grade=1
kl.base.traffic.flowRules[5].count=10000
kl.base.traffic.flowRules[6].resource=updatePrivateKey
kl.base.traffic.flowRules[6].grade=1
kl.base.traffic.flowRules[6].count=10000
kl.base.traffic.flowRules[7].resource=batchGetPrivateKey
kl.base.traffic.flowRules[7].grade=1
kl.base.traffic.flowRules[7].count=10000
kl.base.traffic.flowRules[8].resource=removePrivateKey
kl.base.traffic.flowRules[8].grade=1
kl.base.traffic.flowRules[8].count=10000

# 默认开启PKI密钥模块
kl.km.feature.enablePkiKm=true
# 默认不开启IBC密钥模块
kl.km.feature.enableIbcKm=false
# 默认不开启协同密钥模块
kl.km.feature.enableSks=false
