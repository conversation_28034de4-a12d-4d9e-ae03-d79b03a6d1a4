---
# Source: java/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  #批量替换成实际名称
  name: kcsp-ngpki-km-management
  labels:
    Appname: kcsp-ngpki-km-management
    Projectname: kcsp
spec:
  #测试时配置这个，后续要换成 ClusterIP
  type: NodePort
  ports:
    - name: http
      #批量替换成服务实际端口
      port: 11080
      protocol: TCP
      targetPort: 18080
  selector:
    Appname: kcsp-ngpki-km-management
    Projectname: kcsp
---
# Source: java/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kcsp-ngpki-km-management
  labels:
    Appname: kcsp-ngpki-km-management
    Projectname: kcsp
    macvlan.panda.io/macvlanIpType: auto
    macvlan.pandaria.cattle.io/subnet: e-r2vtb
spec:
  minReadySeconds: 20
  replicas: 1
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  selector:
    matchLabels:
      Appname: kcsp-ngpki-km-management
      Projectname: kcsp
  template:
    metadata:
      annotations:
        k8s.v1.cni.cncf.io/networks: '[{"name":"static-macvlan-cni-attach","interface":"eth1"}]'
        macvlan.pandaria.cattle.io/ip: auto
        macvlan.pandaria.cattle.io/mac: auto
        macvlan.pandaria.cattle.io/subnet: e-r2vtb
      labels:
        Appname: kcsp-ngpki-km-management
        Projectname: kcsp
    spec:
      containers:
        - name: kcsp-ngpki-km-management
          #镜像地址填 CI，推送的镜像地址
          image: "**********/kcsp/kcsp-npki-kms-service:1.0.1"
          imagePullPolicy: Always
          #环境变量
          env:
            # 海东青注册名称
            - name: APP_NAME
              value: kcsp-npki-kms-service
            # JVM参数
            - name: JVM_OPTION
              value: -Xms4096m -Xmx8192m -XX:+UseG1GC -javaagent:/home/<USER>/pcas-agent/pcas-agent-launcher.jar
          envFrom:
            - configMapRef:
                name: ctsp-service
            #args:
            #  - java
            #  - -jar
            # 修改成实际的包
          #  - /usr/local/koal/ctsp-kms-service-3.0.0.jar
          args:
            - sh
            - -c
            # 修改成实际的包
            - /usr/local/koal/service.sh
          ports:
            - name: http
              containerPort: 11080
              protocol: TCP
          resources:
            limits:
              cpu: 4
              memory: 12Gi
            requests:
              cpu: 2
              memory: 8192Mi
        - name: kcsp-ngpki-km-management-sidecar
          image: **********/servicemesh/sm_qilin7:2.3.3.1_p1
          imagePullPolicy: Always
          args:
            - sh
            - -c
            - start_se_sidecar.sh
          env:
            - name: SERVICE_HTTP_CHECK_METHOD
              value: get
            - name: SERVICE_HTTP_CHECK_URL
              #修改成实际的health接口
              value: http://127.0.0.1:18089/health
            - name: SERVICE_NAME
              #修改成实际的服务名称
              value: kcsp-ngpki-km-management
            - name: SERVICE_PORT
              value: "18089"
            - name: SRC_ROOT
              value: /home/<USER>
            - name: SYSTEM_ID
              value: CTSP
            - name: VERSION
              value: "1"
          envFrom:
            - configMapRef:
                name: ctsp-sidecar
          resources:
            limits:
              nvidia.com/gpu: "0"
            requests:
              nvidia.com/gpu: "0"
          securityContext:
            allowPrivilegeEscalation: false
            capabilities: {}
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: false
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      volumes: