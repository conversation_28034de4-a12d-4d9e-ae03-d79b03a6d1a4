version: '3'  #指定compose文件的版本，无需修改

services:  #定义所有的服务信息

  kcsp-npki-km-management:   #指定容器的名称，可包含密码应用服务英文缩写等
    image:  ${IMAGE} #指定镜像，这里取的是上述.env文件中定义的image
    container_name: ${APP_NAME}
    privileged: true
    ports:  #映射端口
      - ${MGMT_HTTP_PORT}:${MGMT_HTTP_PORT}
      - ${MGMT_HTTPS_PORT}:${MGMT_HTTPS_PORT}
      - ${TCP_SVC_PORT}:${TCP_SVC_PORT}
#      - ${DEBUG_PORT}:${DEBUG_PORT}
      - ${JMX_OUTER_PORT}:${JMX_INNER_PORT}
    volumes:  #挂载目录到容器内部，可自定义增加挂载目录
      - .:/app
      - ./data/config:/app/config
      - ./jar:/app/lib
      - ./front/static:/app/defaultconfig/static
      - ./logs:/logs
      - ${JAVA_AGENT_PATH}:/java-agent
    environment:
      APP_NAME: "${APP_NAME}"
      CONFIG_STORE_TYPE: "${CONFIG_STORE_TYPE}"
      CONSUL_HOST: "${CONSUL_HOST}"
      CONSUL_PORT: "${CONSUL_PORT}"
      CONSUL_CONFIG_KEY: "${CONSUL_CONFIG_KEY}"
      CONSUL_DEREGISTER_CRITICAL_SERVICE_TIME: "${CONSUL_DEREGISTER_CRITICAL_SERVICE_TIME}"
      JMX_EXPORTER_JVM_OPTION: "${JMX_EXPORTER_JVM_OPTION}"
      ENABLE_CONSUL: "${ENABLE_CONSUL}"
    #容器启动后默认执行的命令，服务按需修改
    command: sh -c 'sh /app/service.sh start & tail -f /dev/null'
    healthcheck:           #用于检查服务测试服务使用的容器是否正常
      test: [ "CMD", "bash", "-c", "cat < /dev/null > /dev/tcp/127.0.0.1/${MGMT_HTTP_PORT}" ]
      interval: 10s
      timeout: 240s
      retries: 20
      # start_period: 50s
    networks:                   #加入指定的网络
      - ctsp_network
    deploy:                     #指定与部署和运行服务相关的配置
      mode: replicated          #该模式可指定容器数量（默认）
      replicas: ${REPLICAS}      #指定容器数量
      restart_policy:           #配置容器的重新启动
        condition: on-failure   #重新启动的条件
        #        delay: 5s
        max_attempts: 3         #在放弃之前尝试重新启动容器次数
      #        window: 240s
      resources:                #配置资源限制
        limits:
          cpus: '${LIMIT_CPU}'
          memory: ${MAX_MEM}M
    logging:
      driver: ${LOG_DRIVER}
      options:
        tag: kcsp-npki-km-management

networks:   #自定义网络
  ctsp_network:
    external: true