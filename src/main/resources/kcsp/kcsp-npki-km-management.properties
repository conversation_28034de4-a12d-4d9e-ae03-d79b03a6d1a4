# 配置登录模式，目前取值有kcsp_sso,pwd, ssl_client, sign, ssl_client_sign, multi_sign_2_of_3, multi_sign_3_of_5
kl.base.login.loginModel=kcsp_sso
# database
kl.base.datasource.slotName=default
kl.base.datasource.sharding.shardingEnabled=true
kl.base.datasource.sharding.tables[0].dataNodes=ds0.T_KEY_CURRENT_$->{0..7}
kl.base.datasource.sharding.tables[0].keyGenerateAlgorithm=kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
kl.base.datasource.sharding.tables[0].keyGenerateColumn=
kl.base.datasource.sharding.tables[0].shardingColumn=ENC_CERT_SN
kl.base.datasource.sharding.tables[0].shardingAlgorithm=KL_HASH
kl.base.datasource.sharding.tables[0].name=T_KEY_CURRENT
kl.base.datasource.sharding.tables[1].keyGenerateColumn=
kl.base.datasource.sharding.tables[1].keyGenerateAlgorithm=kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
kl.base.datasource.sharding.tables[1].shardingColumn=ENC_CERT_SN
kl.base.datasource.sharding.tables[1].shardingAlgorithm=KL_HASH
kl.base.datasource.sharding.tables[1].name=T_KEY_HISTORY
kl.base.datasource.sharding.tables[1].dataNodes=ds0.T_KEY_HISTORY_$->{0..7}
kl.base.datasource.sharding.tables[2].keyGenerateAlgorithm=kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
kl.base.datasource.sharding.tables[2].keyGenerateColumn=
kl.base.datasource.sharding.tables[2].dataNodes=ds0.T_API_LOG,ds0.T_API_LOG_$->{2000..2099}_0$->{1..9},ds0.T_API_LOG_$->{2000..2099}_1$->{0..2}
kl.base.datasource.sharding.tables[2].shardingColumn=LOG_WHEN
kl.base.datasource.sharding.tables[2].shardingAlgorithm=KL_MONTH
kl.base.datasource.sharding.tables[2].name=T_API_LOG
kl.base.datasource.sharding.props.month-sharding-cron=0 0 1 L * ?
kl.base.datasource.sharding.props.month-sharding-classes=kl.npki.base.service.repository.entity.TApiLogDO
kl.base.datasource.sharding.props.month-sharding-num=3
kl.base.datasource.sharding.props.sharding-count=8
kl.base.datasource.sharding.healthCheck.enableAutoSwitch=false
kl.base.datasource.sharding.healthCheck.period=5
kl.base.datasource.sharding.healthCheck.delay=0
kl.base.datasource.sharding.readWriteEnabled=false
kl.base.datasource.sharding.databaseDiscoveryEnabled=false
kl.base.datasource.sharding.databaseShardingAlgorithm=
kl.base.datasource.sharding.databaseShardingColumn=
kl.base.datasource.sharding.multiMastersAndSlavesEnabled=false
kl.base.datasource.datasource[0].name=ds0
kl.base.datasource.datasource[0].username=${database_username_1}
kl.base.datasource.datasource[0].url=${database_url_1}
kl.base.datasource.datasource[0].password=${database_password_1}
kl.base.datasource.datasource[0].dbRoleType=MASTER
kl.base.datasource.druid.driverClassName=${database_driver_1}
#kl.base.datasource.druid.filters=stat,slf4j
kl.base.datasource.druid.initialSize=1
kl.base.datasource.druid.logSlowSql=true
kl.base.datasource.druid.maxActive=32
kl.base.datasource.druid.maxWait=10000
kl.base.datasource.druid.maxEvictableIdleTimeMillis=300000
kl.base.datasource.druid.maxOpenPreparedStatements=0
kl.base.datasource.druid.minEvictableIdleTimeMillis=300000
kl.base.datasource.druid.minIdle=1
kl.base.datasource.druid.poolPreparedStatements=false
kl.base.datasource.druid.slowSqlMillis=3000
kl.base.datasource.druid.testOnBorrow=false
kl.base.datasource.druid.testOnReturn=false
kl.base.datasource.druid.testWhileIdle=true
kl.base.datasource.druid.timeBetweenEvictionRunsMillis=60000
kl.base.datasource.druid.validationQuery=SELECT 1 FROM DUAL
kl.base.datasource.druid.useUnfairLock=true
kl.base.datasource.druid.keepAlive=false
# sys algo
kl.base.algo.asymmetric=SM2,RSA_2048
kl.base.algo.symmetric=SM4/GCM/NoPadding
kl.base.algo.support.hash=SM3,SHA,SHA3
kl.base.algo.support.asymmetric=SM2,RSA_2048,RSA_3072,RSA_4096
kl.base.algo.support.symmetric=SM1/CBC/PKCS5Padding,SM4/GCM/NoPadding
kl.base.algo.support.postQuantum=KYBER,DILITHIUM
kl.base.algo.hash=SM3,SHA-256
# cache
kl.base.cache.enabled=true
# Ehcache缓存堆内存最大条目，条目达到最大会自动扩容
kl.base.cache.ehcache.heapEntries=10000
# 缓存类型，支持ehcache、redis、caffeine
kl.base.cache.type=redis
# 缓存默认过期时间，小于等于0表示永久生效
kl.base.cache.defaultTimeToLiveMills=0
# caffeine缓存堆内存最大条目，条目达到最大会自动淘汰
kl.base.cache.caffeine.maximumSize=10000
# Redis运行模式  single:单机模式，cluster:集群模式 sentinel:哨兵模式
kl.base.cache.redis.runMode=single
# Redis服务读取超时时间，单位毫秒
kl.base.cache.redis.readTimeoutMills=3000
# Redis服务地址多个地址,分割
kl.base.cache.redis.nodes=${redis_host}:${redis_port}
#kl.base.cache.redis.nodes=***********:26480,***********:26481,***********:26482
# Redis服务连接超时时间，单位毫秒
kl.base.cache.redis.connectTimeoutMills=10000
# Redis集群模式配置（可选）
kl.base.cache.redis.cluster.dynamicRefreshSources=true
# Redis服务认证用户名
kl.base.cache.redis.username=
# Redis服务认证密码
kl.base.cache.redis.password=${redis_password}
# Redis连接的数据库索引
kl.base.cache.redis.database=0
# 是否启用SSL
kl.base.cache.redis.ssl=false
# Redis哨兵模式配置（可选）
kl.base.cache.redis.sentinel.master=mymaster
kl.base.cache.redis.sentinel.password=${redis_password}

# key
kl.km.key.cacheCheckInterval=60
kl.km.key.cacheSize=100
kl.km.key.cacheSupplyThreshold=50
kl.km.key.cacheAutoSupply=true
kl.km.key.keyUniquenessCheck=false
kl.km.key.keyLimits[0].enable=true
kl.km.key.keyLimits[0].keyType=RSA_2048
kl.km.key.keyLimits[0].keyGenCronExpression=0 0 0/1 * * ?
kl.km.key.keyLimits[0].limit=1000
kl.km.key.keyLimits[1].enable=true
kl.km.key.keyLimits[1].keyType=SM2
kl.km.key.keyLimits[1].keyGenCronExpression=0 0 0/1 * * ?
kl.km.key.keyLimits[1].limit=1000
# archive
kl.km.archive.enable=true
kl.km.archive.lateArchiveTime=365
kl.km.archive.archivePageThreshold=200
kl.km.archive.archiveCronExpression=0 0 0 * * ?
# kmConfig
kl.km.conf.signResponse=true
kl.km.conf.sksKeyValidityYears=2
kl.km.conf.sksKeyCacheEnable=false
kl.km.conf.sksKeyCacheTimeToLive=3600000
kl.km.conf.checkSnExist=true
kl.km.conf.restoreCA=true
kl.km.conf.reduceRealTime=true
kl.km.conf.reduceExecuteInterval=15
kl.km.conf.verifyRequest=true
kl.km.conf.testCenter=false