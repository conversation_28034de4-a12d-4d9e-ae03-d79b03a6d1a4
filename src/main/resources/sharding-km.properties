# 需要分表的表名
kl.base.sharding.tables[0].name=T_KEY_CURRENT
# 需要分表的分区键，默认为id
kl.base.sharding.tables[0].shardingColumn=ENC_CERT_SN
# 需要分表的完整分库分表节点表达式
kl.base.sharding.tables[0].dataNodes=ds0.T_KEY_CURRENT_$->{0..15}
# 分表算法
kl.base.sharding.tables[0].shardingAlgorithm=KL_HASH
# 分表数量，如果需要分表，必须有值，0、1、空都默认不分表
kl.base.sharding.props.sharding-count=16

kl.base.sharding.tables[1].name=T_KEY_HISTORY
kl.base.sharding.tables[1].shardingColumn=ENC_CERT_SN
kl.base.sharding.tables[1].dataNodes=ds0.T_KEY_HISTORY_$->{0..15}
kl.base.sharding.tables[1].shardingAlgorithm=KL_HASH

kl.base.sharding.tables[3].name=T_KEY_TRACE
kl.base.sharding.tables[3].shardingColumn=ENTITY_SN
kl.base.sharding.tables[3].dataNodes=ds0.T_KEY_TRACE_$->{0..15}
kl.base.sharding.tables[3].shardingAlgorithm=KL_HASH

kl.base.sharding.tables[2].name=T_API_LOG
kl.base.sharding.tables[2].shardingColumn=LOG_WHEN
kl.base.sharding.tables[2].dataNodes=ds0.T_API_LOG,ds0.T_API_LOG_$->{2000..2099}_0$->{1..9},ds0.T_API_LOG_$->{2000..2099}_1$->{0..2}
kl.base.sharding.tables[2].shardingAlgorithm=KL_MONTH
# 创建月份分表定时任务corn
kl.base.sharding.props.month-sharding-cron=0 0 1 L * ?
# 月份分表预创建的数量
kl.base.sharding.props.month-sharding-num=3
# 需要按月分表的表对应的DO类全限定名称，用于解析注解生成SQL。如果需要配置多个类，请用英文逗号隔开
kl.base.sharding.props.month-sharding-classes=kl.npki.base.service.repository.entity.TApiLogDO

# 是否打印SQL
kl.base.sharding.props.sql-show=false
# 每个查询可以打开的最大连接数量，默认为1，增加此配置会提升查询并发性能，例如密钥库统计，但是会占用更多数据库连接资源，请谨慎增加使用
kl.base.sharding.props.max-connections-size-per-query=1