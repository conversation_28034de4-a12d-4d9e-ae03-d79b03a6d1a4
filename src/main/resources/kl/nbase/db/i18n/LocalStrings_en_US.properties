kl.nbase.db.i18n_SYSTEM_ERROR=System error
kl.nbase.db.i18n_LACK_DB_CONF_ERROR=Missing necessary database configuration!
kl.nbase.db.i18n_LACK_READ_WRITE_CONF_ERROR=Missing necessary read-write separation configuration!
kl.nbase.db.i18n_LACK_LOAD_BALANCER_CONF_ERROR=Missing necessary load balancing algorithm configuration!
kl.nbase.db.i18n_ID_GEN_ERROR=Error in ID generation scheduled task interval!
kl.nbase.db.i18n_SNOWFLAKE_WORK_ID_GEN_ERROR=Error in Snowflake algorithm work ID generation!
kl.nbase.db.i18n_REFRESH_SHARDING_TABLE_NODE_ERROR=Error in refreshing sharding table node!
kl.nbase.db.i18n_ID_BITS_ERROR=Error in ID bits!
kl.nbase.db.i18n_ID_GEN_SCHEDULE_INTERVAL_ERROR=Error in ID generation scheduled task interval!
kl.nbase.db.i18n_ID_BUFFER_CONFIG_ERROR=Error in ID buffer configuration!
kl.nbase.db.i18n_ID_GEN_CONFIG_ERROR=Error in ID generation configuration!
kl.nbase.db.i18n_PARAM_NOT_NULL=Parameter cannot be null!
kl.nbase.db.i18n_QUERY_ALL_TABLE_NAME_ERROR=Querying all table names is incorrect!
kl.nbase.db.i18n_EXCEEDING_MAX_PAGE_SIZE_LIMIT=Exceeding max page size limit!

# kl.nbase.db.support.slot.SwitchableDataSourceBuilder
kl.nbase.db.i18n_confirmation_multi_master_multi_slave_configuration = Please correctly configure multi-master multi-slave!
kl.nbase.db.i18n_instruction_multi_master_multi_slave_configuration = Please configure the multi-master and multi-slave correctly!