kl.nbase.db.i18n_SYSTEM_ERROR=系统错误
kl.nbase.db.i18n_LACK_DB_CONF_ERROR=缺少必要的db配置！
kl.nbase.db.i18n_LACK_READ_WRITE_CONF_ERROR=缺少必要的读写分离配置！
kl.nbase.db.i18n_LACK_LOAD_BALANCER_CONF_ERROR=缺少必要的负载均衡算法配置！
kl.nbase.db.i18n_ID_GEN_ERROR=ID 生成定时任务执行间隔错误！
kl.nbase.db.i18n_SNOWFLAKE_WORK_ID_GEN_ERROR=雪花算法work id生成错误！
kl.nbase.db.i18n_REFRESH_SHARDING_TABLE_NODE_ERROR=刷新sharding分表节点失败
kl.nbase.db.i18n_ID_BITS_ERROR=ID 位数错误！
kl.nbase.db.i18n_ID_GEN_SCHEDULE_INTERVAL_ERROR=ID 生成定时任务执行间隔错误！
kl.nbase.db.i18n_ID_BUFFER_CONFIG_ERROR=ID 缓存配置错误！
kl.nbase.db.i18n_ID_GEN_CONFIG_ERROR=ID 生成配置错误！
kl.nbase.db.i18n_PARAM_NOT_NULL=参数不能为空！
kl.nbase.db.i18n_QUERY_ALL_TABLE_NAME_ERROR=查询所有表名错误！
kl.nbase.db.i18n_EXCEEDING_MAX_PAGE_SIZE_LIMIT=超过最大分页限制!

# kl.nbase.db.support.slot.SwitchableDataSourceBuilder
kl.nbase.db.i18n_confirmation_multi_master_multi_slave_configuration = 请确认是否正确配置了多主多从!
kl.nbase.db.i18n_instruction_multi_master_multi_slave_configuration =  请正确配置多主多从!