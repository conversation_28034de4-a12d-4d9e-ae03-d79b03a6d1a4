kl.nbase.auth.i18n_multifactorial_identifier_discrepancy_error=mismatch in multi-factor authentication identity!
kl.nbase.auth.i18n_sso_impl_not_found_error = Unsupported authentication type!
kl.nbase.auth.i18n_sso_extending_type_error = SSO extension field type must be Map type!
kl.nbase.auth.i18n_sso_type_already_exist_error = SSO authentication type already exists!
kl.nbase.auth.i18n_sso_type_already_exist_error_desc = SSO type conflict: The type "{0}" is associated with multiple implementations ({1} and {2}). Please ensure only one ISsoAuthSpi implementation is registered per type.
kl.nbase.auth.i18n_sso_type_not_null_error = SSO authentication type cannot be empty!

kl.nbase.auth.i18n_system_error=system error!
kl.nbase.auth.i18n_unsupported_credential=unsupported authentication credential!
kl.nbase.auth.i18n_unsupported_authn_type=unsupported authentication type!
kl.nbase.auth.i18n_unsupported_authority_handler=unsupported authority handler!
kl.nbase.auth.i18n_invalid_token=the token is incorrect!
kl.nbase.auth.i18n_token_expired_error=the token has expired
kl.nbase.auth.i18n_token_has_expired_on=the token has expired on {0}
kl.nbase.auth.i18n_decoded_jwt_error=jwt decoding exception
kl.nbase.auth.i18n_invalid_auth_info=invalid authentication information
# kl.nbase.auth.utils.jwthelper
kl.nbase.auth.i18n_error_token_empty=token is empty !
