kl.nbase.auth.i18n_multifactorial_identifier_discrepancy_error=多种因子认证身份不一致！
kl.nbase.auth.i18n_sso_impl_not_found_error = 不支持的认证类型!
kl.nbase.auth.i18n_sso_extending_type_error = SSO扩展字段类型必须是Map类型!
kl.nbase.auth.i18n_sso_type_already_exist_error = SSO认证类型已经存在!
kl.nbase.auth.i18n_sso_type_already_exist_error_desc = SSO认证类型冲突：类型 “{0}” 对应多个实现类（{1} 与 {2}），请避免重复注册。
kl.nbase.auth.i18n_sso_type_not_null_error = SSO认证类型不能为空！

kl.nbase.auth.i18n_system_error=系统错误！
kl.nbase.auth.i18n_unsupported_credential=不支持的认证凭证！
kl.nbase.auth.i18n_unsupported_authn_type=不支持的认证方式！
kl.nbase.auth.i18n_unsupported_authority_handler=不支持的鉴权方式！
kl.nbase.auth.i18n_invalid_token=token不正确！
kl.nbase.auth.i18n_token_expired_error=token已过期
kl.nbase.auth.i18n_token_has_expired_on=token已于{0}过期
kl.nbase.auth.i18n_decoded_jwt_error=jwt 解码异常
kl.nbase.auth.i18n_invalid_auth_info=无效的认证信息
# kl.nbase.auth.utils.jwthelper
kl.nbase.auth.i18n_error_token_empty = token 不能为空！