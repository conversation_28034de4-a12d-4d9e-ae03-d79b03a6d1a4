kl.npki.km.core.i18n_ca_resource_not_enough=The CA key resource is insufficient!
kl.npki.km.core.i18n_ca_cert_publickey_ecode_error=Decoding the CA certificate public key fails!
kl.npki.km.core.i18n_ca_resource_unauthorized=The CA key resource is unauthorized!
kl.npki.km.core.i18n_ca_medium_sn_not_exist=Operation type of the media SN that does not exist
kl.npki.km.core.i18n_key_escrow_service_error=Key Request service error!
kl.npki.km.core.i18n_user_key_decode_error=User public key decoding fails!
kl.npki.km.core.i18n_key_restore_error=Key recovery failed!
kl.npki.km.core.i18n_key_archive_error=Key archiving failed!
kl.npki.km.core.i18n_law_recover_cert_create_error=Failed to generate the judicial recovery installation certificate!
kl.npki.km.core.i18n_engine_gen_keypair_error=The cipher machine fails to generate the key pair!
kl.npki.km.core.i18n_spare_key_save_error=Backup key pair store failed!
kl.npki.km.core.i18n_gen_random_number_error=Failed to get a random number!
kl.npki.km.core.i18n_manage_root_not_issue=Management root certificate not issued!
kl.npki.km.core.i18n_manage_root_already_exist=The management root certificate exists!
kl.npki.km.core.i18n_id_cert_not_issue=Identity certificate not issued!
kl.npki.km.core.i18n_id_cert_already_exist=The identity certificate already exists!
kl.npki.km.core.i18n_cert_issue_error=Failed to issue the certificate!
kl.npki.km.core.i18n_key_pair_get_error=Failed to obtain the key pair!
kl.npki.km.core.i18n_gen_pkcs10_request_error=Failed to generate a p10 request!
kl.npki.km.core.i18n_dn_build_error=DN Construction failed!
kl.npki.km.core.i18n_file_read_error=Failed to load file!
kl.npki.km.core.i18n_asn_decode_error=ASN structure decoder failed!
kl.npki.km.core.i18n_asn_encode_error=ASN structure encoder failed!
kl.npki.km.core.i18n_cert_decode_error=Certificate ASN structure decoded failed!
kl.npki.km.core.i18n_cert_not_yet_valid_error=The certificate is invalid!
kl.npki.km.core.i18n_cert_expired_error=The certificate has expired!
kl.npki.km.core.i18n_jks_load_error=Failed to load JKS!
kl.npki.km.core.i18n_cert_encode_error=Failed to decode certificate ASN structure!
kl.npki.km.core.i18n_get_public_key_error=Failed to obtain the public key!
kl.npki.km.core.i18n_get_private_key_error=Failed to obtain the private key!
kl.npki.km.core.i18n_km_internal_error=KM internal service exception!
kl.npki.km.core.i18n_ca_id_cert_existing_error=The CA identity certificate already exists!
kl.npki.km.core.i18n_ca_not_existing_error=The CA record does not exist!
kl.npki.km.core.i18n_ca_status_abnormal_error=The CA cannot be used because the CA is frozen or abnormal_error is deleted!
kl.npki.km.core.i18n_ca_id_cert_not_existing_error=The CA identity certificate does not exist!
kl.npki.km.core.i18n_ca_id_cert_not_signing_cert=The CA identity certificate is not a signing certificate!
kl.npki.km.core.i18n_ca_sn_or_key_hash_or_name_existing_error=The serial number, key hash, or name of the CA certificate already exists!
kl.npki.km.core.i18n_ca_name_existing_error=CA Name Already Exists!

kl.npki.km.core.i18n_signed_verify_error=Failed to request signature verification!
kl.npki.km.core.i18n_sign_error=Failed to sign!
kl.npki.km.core.i18n_ca_unfreeze_error=The CA fails to be unfrozen.
kl.npki.km.core.i18n_ca_recovery_error=CA recovery fails because the KM configuration does not allow recovery of the invalid CA.
kl.npki.km.core.i18n_protocol_unknow_error=The unknown type of key service message request!
kl.npki.km.core.i18n_key_request_decode_error=Failed to request ASN decoding for key service message!
kl.npki.km.core.i18n_request_version_nonsupport_error=Unsupported request version type!
kl.npki.km.core.i18n_key_type_nonsupport_error=Key type not supported by key service!
kl.npki.km.core.i18n_public_key_lack_error=Required user public key parameter missing from key request!
kl.npki.km.core.i18n_symmetric_nonsupport_error=Symmetric algorithm type not supported by key service!
kl.npki.km.core.i18n_cert_sn_lack_error=Required user certificate serial number parameter missing for key request!
kl.npki.km.core.i18n_key_request_nonsupport_error=Unsupported key request type!
kl.npki.km.core.i18n_medium_sn_lack_error=Key request lacks necessary media sequence number!
kl.npki.km.core.i18n_medium_sn_existing_error=Duplicate key request medium serial number!
kl.npki.km.core.i18n_validity_abnormal_error=The key expiration time cannot be smaller than the effective time and current time
kl.npki.km.core.i18n_key_destroy_sn_not_existing_error=Key Revocation Certificate serial number not found!
kl.npki.km.core.i18n_key_restore_sn_not_existing_error=Serial number of key recovery certificate not found!
kl.npki.km.core.i18n_restore_id_not_existing_error=The key to be restored is not found!
kl.npki.km.core.i18n_verify_law_recover_signature_error=Failed to verify the signature of the judicial recovery request
kl.npki.km.core.i18n_archive_id_not_found_error=The archive key is not found!
kl.npki.km.core.i18n_archive_id_status_unsupported_for_archive_error=The archive key status is not supported for archiving!
kl.npki.km.core.i18n_sks_key_existing_error=The Threshold Key with the same keyHash already exists!
kl.npki.km.core.i18n_sks_key_not_existing_error=Threshold Key Record does not exist!
kl.npki.km.core.i18n_sks_key_status_revoked_error=Threshold Key has been abolished!
kl.npki.km.core.i18n_batch_number_excess_error=The number of batch synchronization threshold keys exceeds the configured threshold. Narrow the query range.
kl.npki.km.core.i18n_sks_key_remove_error=Deletion of non-self-checked threshold keys is prohibited.
kl.npki.km.core.i18n_parse_error=ASN structure parsing failed!
kl.npki.km.core.i18n_encode_error=Coding failure!
kl.npki.km.core.i18n_unsupported_algorithm_error=Unsupported algorithm!
kl.npki.km.core.i18n_demo_key_usage_exceeded=The test environment has reached the maximum number of keys (200), Please deploy in the production environment and import the corresponding LICENSE!
kl.npki.km.core.i18n_illegal_license_status=The system license has expired or is invalid, please import a new license!
kl.npki.km.core.i18n_hmac_verify_error=hmac verify error
kl.npki.km.core.i18n_asym_algo_inconsistent=The algorithm of the applicant's public key and the issuer's certificate are inconsistent
kl.npki.km.core.i18n_ca_status_not_normal=CA status is not normal
kl.npki.km.core.i18n_gen_ca_response_failed= Generate CA response failed
kl.npki.km.core.i18n_ca_request_convert_error=CA requests that cannot be resolved
kl.npki.km.core.i18n_license_sys_npki_type_error=The NPKI system type in the license does not match
kl.npki.km.core.i18n_retasymalg_not_equal_public_key=The asymmetric algorithm does not match the algorithm in the public key
kl.npki.km.core.i18n_sks_key_update_error=Updating of non-threshold keys is not allowed.

# kl.npki.km.core.biz.trans.model.EscrowMsgInfo
kl.npki.km.core.i18n_validity_abnormal_error_msg = The expiration time {0} cannot be less than the effective time {1} and the current time!

# kl.npki.km.core.biz.law.service.LawRecoverService
kl.npki.km.core.i18n_failed_to_obtain_judicial_admin_public_key = Failed to obtain the public key of the judicial administrator
kl.npki.km.core.i18n_failed_to_construct_key_envelope = Failed to construct the key envelope

# kl.npki.km.core.biz.ca.model.CaEntity
kl.npki.km.core.i18n_ca_abolished_with_name = The CA has been abolished, CA Name: {0}
kl.npki.km.core.i18n_ca_unfrozen_with_name = The CA is not in a frozen state, CA Name: {0}
kl.npki.km.core.i18n_ca_unrecoverable_with_name = The CA cannot be recovered, CA Name: {0}

# kl.npki.km.core.biz.ca.service.CaManager
kl.npki.km.core.i18n_ca_key_resources_insufficient = The {1} key resources of the CA with public key index {0} are insufficient

# kl.npki.km.core.biz.sks.model.SksKeyEntity
kl.npki.km.core.i18n_sks_key_status_abolished = The Threshold key has been abolished, keyHash: {0}

# kl.npki.km.core.biz.trans.service.ProtocolProcessorFactory
kl.npki.km.core.i18n_gbv1_request_type_not_supported = The request type of GBV1 version is not supported currently
kl.npki.km.core.i18n_unsupported_request_type = Unsupported request type
kl.npki.km.core.i18n_ca_fast_api_not_enabled=CA Quick Access is not enabled
kl.npki.km.core.i18n_key_request_missing_structure_error=The key service request is missing key request information！
kl.npki.km.core.i18n_system_auth_token_not_config=The system authentication token is not configured!
# kl.npki.km.core.biz.trans.service.handler.impl.KeyRevokeBiz
kl.npki.km.core.i18n_certificate_serial_number = Certificate serial number: {0}

# kl.npki.km.core.biz.trans.service.impl.GB0014ProtocolProcessor
## unsupported_request_type
kl.npki.km.core.i18n_non_standard_request_unparsable = The request is non - standard and cannot be parsed
kl.npki.km.core.i18n_unsupported_message_version = Unsupported message version

# kl.npki.km.core.biz.trans.service.msg.impl.ASNReqMsgAdapter
kl.npki.km.core.i18n_unsupported_encryption_key_requirements = The algorithm and length requirements for the encryption key in the request are not supported {0}
kl.npki.km.core.i18n_parse_not_before_or_not_after_failed = Failed to parse NotBefore or NotAfter in AppUserInfo
kl.npki.km.core.i18n_missing_signature_cert_public_key = Missing the public key of the signature certificate!
kl.npki.km.core.i18n_signature_cert_public_key_missing = Failed to obtain the public key of the signature certificate!
kl.npki.km.core.i18n_signature_cert_public_key_value_parsing_failed = Failed to parse the value of the public key of the signature certificate!
kl.npki.km.core.i18n_request_response_asymmetric_algorithm_mismatch = In the request, Asymmetric algorithm in the response: {0}, User public key algorithm in the request: {1}, Public key algorithm parameters: {2}, the two do not match

# kl.npki.km.core.service.ca.impl.CaAccessServiceImpl
kl.npki.km.core.i18n_parse_ca_access_request_failed = Failed to parse as a CA access request

# kl.npki.km.core.service.ca.impl.CaSiteCertServiceImpl
kl.npki.km.core.i18n_entity_status_display = Status: {0}
## non_standard_request_unparsable

# kl.npki.km.core.service.statistic.AbstractApiLogStatisticService
kl.npki.km.core.i18n_unsupported_statistical_type = Unsupported statistical business type: {0}

# kl.npki.km.core.common.constants.SksBizConstants
kl.npki.km.core.i18n_get_random_number = Obtain Random Number
kl.npki.km.core.i18n_create_private_key = Save Threshold Key
kl.npki.km.core.i18n_get_private_key = Query Threshold Key
kl.npki.km.core.i18n_update_private_key = Update Threshold Key Status
kl.npki.km.core.i18n_batch_get_private_key = Batch Obtain Threshold Keys
kl.npki.km.core.i18n_remove_private_key = Delete Self-Check Threshold Key

# kl.npki.km.core.common.constants.ArchiveReason
kl.npki.km.core.i18n_revoke=CA revoked
kl.npki.km.core.i18n_expired=Key expired

# kl.npki.km.core.common.constants.CaKeyServiceType
kl.npki.km.core.i18n_key_apply=Apply Key
kl.npki.km.core.i18n_key_restore=Restore Key
kl.npki.km.core.i18n_key_revoke=Revoke Key
kl.npki.km.core.i18n_unknown_key_service=Key Service
kl.npki.km.core.i18n_ca_access=CA Quick Access
kl.npki.km.core.i18n_ca_ssl_cert_sign=Apply CA client certificate
# kl.npki.km.core.common.constants.KeyPosition
kl.npki.km.core.i18n_pos_current=Active Repository
kl.npki.km.core.i18n_pos_history=Historical Repository
kl.npki.km.core.i18n_pos_pool=Standby Repository

# kl.npki.km.core.common.constants.KmJobEnum
kl.npki.km.core.i18n_key_gen=Generate key
kl.npki.km.core.i18n_key_archive=Archive key
kl.npki.km.core.i18n_key_cache_add=Add key cache
kl.npki.km.core.i18n_key_resource_reduce=Reduce key resource
kl.npki.km.core.i18n_home_page_data_update=Update homepage statistical data 
kl.npki.km.core.i18n_ca_statistic_result_add=CA statistic result

# kl.npki.km.core.common.constants.CaVersionTypeEnum
kl.npki.km.core.i18n_koal_v6_3_x=KOAL V6.3.X
kl.npki.km.core.i18n_koal_v6_4_x_to_6_7_x=KOAL V6.4.X to V6.7.X
kl.npki.km.core.i18n_koal_v6_8_x=KOAL V6.8.X
kl.npki.km.core.i18n_koal_v7_1_x=KOAL V7.1.X
kl.npki.km.core.i18n_koal_v9_x_x=KOAL V9.X.X
kl.npki.km.core.i18n_other_version=Other version

# kl.npki.km.core.common.constants.CaAccessTypeEnum
kl.npki.km.core.i18n_manual=Manual Access
kl.npki.km.core.i18n_online=Online Access

# kl.npki.km.core.service.archive.impl.KeyArchiveServiceImpl
kl.npki.km.core.i18n_expired_key_scan=Expired Key Scan
kl.npki.km.core.i18n_archive_key=Archive Key

# kl.npki.km.core.common.constants.SksKey
kl.npki.km.core.i18n_sks=Threshold Key

# other
kl.npki.km.core.i18n_total_statistic=total
