kl.npki.km.core.i18n_ca_resource_not_enough=CA密钥资源不够！
kl.npki.km.core.i18n_ca_cert_publickey_ecode_error=CA证书公钥解码失败！
kl.npki.km.core.i18n_ca_resource_unauthorized=CA密钥资源未授权！
kl.npki.km.core.i18n_ca_medium_sn_not_exist=不存在的介质序列号操作类型
kl.npki.km.core.i18n_key_escrow_service_error=密钥申请服务报错！
kl.npki.km.core.i18n_user_key_decode_error=用户公钥解码失败！
kl.npki.km.core.i18n_key_restore_error=密钥恢复失败！
kl.npki.km.core.i18n_key_archive_error=密钥归档失败！
kl.npki.km.core.i18n_law_recover_cert_create_error=司法恢复安装证书生成失败！
kl.npki.km.core.i18n_engine_gen_keypair_error=密码机生成密钥对失败!
kl.npki.km.core.i18n_spare_key_save_error=备用密钥对存储仓库失败!
kl.npki.km.core.i18n_gen_random_number_error=获取随机数失败!
kl.npki.km.core.i18n_manage_root_not_issue=管理根证书尚未签发!
kl.npki.km.core.i18n_manage_root_already_exist=管理根证书已存在!
kl.npki.km.core.i18n_id_cert_not_issue=身份证书尚未签发!
kl.npki.km.core.i18n_id_cert_already_exist=身份证书已存在!
kl.npki.km.core.i18n_cert_issue_error=证书签发失败!
kl.npki.km.core.i18n_key_pair_get_error=密钥对获取失败!
kl.npki.km.core.i18n_gen_pkcs10_request_error=生成p10请求失败!
kl.npki.km.core.i18n_dn_build_error=DN构造失败!
kl.npki.km.core.i18n_file_read_error=文件的加载失败! 
kl.npki.km.core.i18n_asn_decode_error=ASN结构解码失败!
kl.npki.km.core.i18n_asn_encode_error=ASN结构编码失败!
kl.npki.km.core.i18n_cert_decode_error=证书ASN结构解码失败!
kl.npki.km.core.i18n_cert_not_yet_valid_error=证书未生效!
kl.npki.km.core.i18n_cert_expired_error=证书已经过期!
kl.npki.km.core.i18n_jks_load_error=JKS 加载失败！
kl.npki.km.core.i18n_cert_encode_error=证书ASN结构解码失败!
kl.npki.km.core.i18n_get_public_key_error=获取公钥失败!
kl.npki.km.core.i18n_get_private_key_error=获取私钥失败!
kl.npki.km.core.i18n_km_internal_error=KM内部服务异常!
kl.npki.km.core.i18n_ca_id_cert_existing_error=CA身份证书已经存在！
kl.npki.km.core.i18n_ca_not_existing_error=CA记录不存在！
kl.npki.km.core.i18n_ca_status_abnormal_error=CA已经被冻结或者废除，无法正常使用！
kl.npki.km.core.i18n_ca_id_cert_not_existing_error=CA身份证书不存在！
kl.npki.km.core.i18n_ca_id_cert_not_signing_cert=CA身份证书不是签名证书！
kl.npki.km.core.i18n_ca_sn_or_key_hash_or_name_existing_error=该CA证书的序列号、密钥哈希值或者名称已经存在！
kl.npki.km.core.i18n_signed_verify_error=请求签名验签不通过！
kl.npki.km.core.i18n_sign_error=签名失败！
kl.npki.km.core.i18n_ca_unfreeze_error=CA解冻失败，无法解冻！
kl.npki.km.core.i18n_ca_recovery_error=CA恢复失败，当前KM配置不容许恢复已废除CA！
kl.npki.km.core.i18n_ca_name_existing_error=该CA名称已经存在！

kl.npki.km.core.i18n_protocol_unknow_error=未知的密钥服务消息请求类型！
kl.npki.km.core.i18n_key_request_decode_error=密钥服务消息请求ASN解码失败！
kl.npki.km.core.i18n_request_version_nonsupport_error=不支持的请求版本类型！
kl.npki.km.core.i18n_key_type_nonsupport_error=密钥服务不支持的密钥类型！
kl.npki.km.core.i18n_public_key_lack_error=密钥请求缺少的必须的用户公钥参数！
kl.npki.km.core.i18n_symmetric_nonsupport_error=密钥服务不支持的对称算法类型！
kl.npki.km.core.i18n_cert_sn_lack_error=密钥请求缺少的必须的用户证书序列号参数！
kl.npki.km.core.i18n_key_request_nonsupport_error=不支持的密钥请求类型！
kl.npki.km.core.i18n_medium_sn_lack_error=密钥请求缺少必要的介质序列号！
kl.npki.km.core.i18n_medium_sn_existing_error=密钥请求介质序列号重复！
kl.npki.km.core.i18n_validity_abnormal_error=密钥失效时间不能小于生效时间和当前时间！
kl.npki.km.core.i18n_key_destroy_sn_not_existing_error=密钥撤销证书序列号未找到！
kl.npki.km.core.i18n_key_restore_sn_not_existing_error=密钥恢复证书序列号未找到！
kl.npki.km.core.i18n_restore_id_not_existing_error=待恢复密钥未找到！
kl.npki.km.core.i18n_verify_law_recover_signature_error=司法恢复请求验签失败
kl.npki.km.core.i18n_archive_id_not_found_error=待归档密钥未找到！
kl.npki.km.core.i18n_archive_id_status_unsupported_for_archive_error=当前密钥状态不支持归档！
kl.npki.km.core.i18n_sks_key_existing_error=相同keyHash的SKS Key已经存在！
kl.npki.km.core.i18n_sks_key_not_existing_error=SKS Key记录不存在！
kl.npki.km.core.i18n_sks_key_status_revoked_error=SKS Key已经废除！
kl.npki.km.core.i18n_batch_number_excess_error=SKS 批量同步密钥数量超过配置的阈值，请缩小查询范围！
kl.npki.km.core.i18n_sks_key_remove_error=SKS 非自检数据不容许删除！
kl.npki.km.core.i18n_parse_error=ASN结构解析失败!
kl.npki.km.core.i18n_encode_error=编码失败!
kl.npki.km.core.i18n_unsupported_algorithm_error=不支持的算法!
kl.npki.km.core.i18n_demo_key_usage_exceeded=测试环境已达到密钥数量上限(200个),请部署使用正式环境并导入相应的LICENSE!
kl.npki.km.core.i18n_illegal_license_status=系统许可证已过期或无效,请导入新的 license!
kl.npki.km.core.i18n_hmac_verify_error=请求HMAC验证不通过！
kl.npki.km.core.i18n_asym_algo_inconsistent=申请者公钥和颁发者证书算法不一致
kl.npki.km.core.i18n_ca_status_not_normal=CA状态不正常
kl.npki.km.core.i18n_gen_ca_response_failed=生成CA响应异常
kl.npki.km.core.i18n_ca_request_convert_error=无法解析的CA请求
kl.npki.km.core.i18n_license_sys_npki_type_error=License中的NPKI系统类型不匹配
kl.npki.km.core.i18n_retasymalg_not_equal_public_key=非对称算法与公钥中算法不匹配
kl.npki.km.core.i18n_sks_key_update_error=SKS非自检数据不容许更新！

# kl.npki.km.core.biz.trans.model.EscrowMsgInfo
kl.npki.km.core.i18n_validity_abnormal_error_msg=失效时间{0} 不能小于生效时间{1} 和当前时间！

# kl.npki.km.core.biz.law.service.LawRecoverService
kl.npki.km.core.i18n_failed_to_obtain_judicial_admin_public_key=获取司法管理员公钥失败
kl.npki.km.core.i18n_failed_to_construct_key_envelope=构造密钥信封失败

# kl.npki.km.core.biz.ca.model.CaEntity
kl.npki.km.core.i18n_ca_abolished_with_name=CA已经被废除, CA名称: {0}
kl.npki.km.core.i18n_ca_unfrozen_with_name=CA非冻结状态, CA名称: {0}
kl.npki.km.core.i18n_ca_unrecoverable_with_name=CA无法恢复, CA名称: {0}

# kl.npki.km.core.biz.ca.service.CaManager
kl.npki.km.core.i18n_ca_key_resources_insufficient=公钥索引为【{0}】CA的 {1} 密钥资源不够

# kl.npki.km.core.biz.sks.model.SksKeyEntity
kl.npki.km.core.i18n_sks_key_status_abolished=SKS密钥已经被废除, keyHash: {0}

# kl.npki.km.core.biz.trans.service.ProtocolProcessorFactory
kl.npki.km.core.i18n_gbv1_request_type_not_supported=目前不支持GBV1版本的请求类型
kl.npki.km.core.i18n_unsupported_request_type=不支持的请求类型
kl.npki.km.core.i18n_ca_fast_api_not_enabled=CA快速接入未开启
kl.npki.km.core.i18n_key_request_missing_structure_error=密钥服务请求缺少密钥申请信息
kl.npki.km.core.i18n_system_auth_token_not_config=系统认证令牌未配置!
# kl.npki.km.core.biz.trans.service.handler.impl.KeyRevokeBiz
kl.npki.km.core.i18n_certificate_serial_number=证书序列号：{0}

# kl.npki.km.core.biz.trans.service.impl.GB0014ProtocolProcessor
## unsupported_request_type
kl.npki.km.core.i18n_non_standard_request_unparsable=请求非标准，无法解析
kl.npki.km.core.i18n_unsupported_message_version=不支持的消息版本

# kl.npki.km.core.biz.trans.service.msg.impl.ASNReqMsgAdapter
kl.npki.km.core.i18n_unsupported_encryption_key_requirements=请求中对加密密钥的算法和长度要求，不被支持{0}
kl.npki.km.core.i18n_parse_not_before_or_not_after_failed=解析 AppUserInfo 中的 NotBefore 或 NotAfter 失败
kl.npki.km.core.i18n_missing_signature_cert_public_key=缺少签名证书公钥！
kl.npki.km.core.i18n_signature_cert_public_key_missing=获取签名证书公钥失败！
kl.npki.km.core.i18n_signature_cert_public_key_value_parsing_failed=签名证书公钥值解析失败！
kl.npki.km.core.i18n_request_response_asymmetric_algorithm_mismatch=请求中【响应中的非对称算法】:{0}, 请求中【用户公钥算法】:{1} 【公钥算法参数】: {2}，两者不匹配

# kl.npki.km.core.service.ca.impl.CaAccessServiceImpl
kl.npki.km.core.i18n_parse_ca_access_request_failed=解析为CA接入请求失败

# kl.npki.km.core.service.ca.impl.CaSiteCertServiceImpl
kl.npki.km.core.i18n_entity_status_display=状态为：{0}
## non_standard_request_unparsable

# kl.npki.km.core.service.statistic.AbstractApiLogStatisticService
kl.npki.km.core.i18n_unsupported_statistical_type=不支持的统计业务类型:{0}

# kl.npki.km.core.common.constants.SksBizConstants
kl.npki.km.core.i18n_get_random_number=获取随机数
kl.npki.km.core.i18n_create_private_key=保存协同密钥
kl.npki.km.core.i18n_get_private_key=查询协同密钥
kl.npki.km.core.i18n_update_private_key=更新协同密钥状态
kl.npki.km.core.i18n_batch_get_private_key=批量获取协同密钥
kl.npki.km.core.i18n_remove_private_key=删除自检协同密钥

# kl.npki.km.core.common.constants.ArchiveReason
kl.npki.km.core.i18n_revoke=CA撤消
kl.npki.km.core.i18n_expired=密钥过期

# kl.npki.km.core.common.constants.CaKeyServiceType
kl.npki.km.core.i18n_key_apply=密钥申请
kl.npki.km.core.i18n_key_restore=密钥恢复
kl.npki.km.core.i18n_key_revoke=密钥撤销
kl.npki.km.core.i18n_unknown_key_service=密钥服务
kl.npki.km.core.i18n_ca_access=CA快速接入
kl.npki.km.core.i18n_ca_ssl_cert_sign=CA客户端证书签发
# kl.npki.km.core.common.constants.KeyPosition
kl.npki.km.core.i18n_pos_current=当前库
kl.npki.km.core.i18n_pos_history=历史库
kl.npki.km.core.i18n_pos_pool=备用库

# kl.npki.km.core.common.constants.KmJobEnum
kl.npki.km.core.i18n_key_gen=密钥生成定时任务
kl.npki.km.core.i18n_key_archive=密钥归档定时任务
kl.npki.km.core.i18n_key_cache_add=密钥缓存增加定时任务
kl.npki.km.core.i18n_key_resource_reduce=密钥资源异步减少定时任务
kl.npki.km.core.i18n_home_page_data_update=首页数据缓存更新定时任务
kl.npki.km.core.i18n_ca_statistic_result_add=CA统计结果定时任务

# kl.npki.km.core.common.constants.CaVersionTypeEnum
kl.npki.km.core.i18n_koal_v6_3_x=格尔V6.3.X版本
kl.npki.km.core.i18n_koal_v6_4_x_to_6_7_x=格尔V6.4.X到V6.7.X版本
kl.npki.km.core.i18n_koal_v6_8_x=格尔V6.8.X版本
kl.npki.km.core.i18n_koal_v7_1_x=格尔V7.1.X版本
kl.npki.km.core.i18n_koal_v9_x_x=格尔V9.X.X版本
kl.npki.km.core.i18n_other_version=其他版本

# kl.npki.km.core.common.constants.CaAccessTypeEnum
kl.npki.km.core.i18n_manual=手动接入
kl.npki.km.core.i18n_online=在线接入

# kl.npki.km.core.service.archive.impl.KeyArchiveServiceImpl
kl.npki.km.core.i18n_expired_key_scan=过期密钥扫描
kl.npki.km.core.i18n_archive_key=密钥归档

# kl.npki.km.core.common.constants.SksKey
kl.npki.km.core.i18n_sks=协同密钥

# other
kl.npki.km.core.i18n_total_statistic=合计
