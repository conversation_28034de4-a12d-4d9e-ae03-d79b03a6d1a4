kl.npki.km.management.i18n_CURRENT_KEY_NOT_FOUND_ERROR= 此ID当前库密钥未找到！
kl.npki.km.management.i18n_HISTORY_KEY_NOT_FOUND_ERROR=此ID历史库密钥未找到！
kl.npki.km.management.i18n_KEY_TRACE_NOT_FOUND_ERROR=此ID的密钥轨迹未找到！
kl.npki.km.management.i18n_REMOTE_SYSTEM_ERROR=调用第三方服务错误！
kl.npki.km.management.i18n_PARAM_ERROR=参数错误!
kl.npki.km.management.i18n_SYSTEM_ERROR=系统错误！
kl.npki.km.management.i18n_SM9_KEY_SERVICE_ENABLED_ERROR=SM9密钥服务开启失败！

# kl.npki.km.management.controller.home.SslClientCertController
kl.npki.km.management.i18n_ssl_client_cert_zip_name=%s_SSL客户端证书_%s.zip

# kl.npki.km.management.controller.home.HomePageController
kl.npki.km.management.i18n_key_status_data_stats=密钥状态数据统计
kl.npki.km.management.i18n_key_vault_data_stats=密钥库数据统计
kl.npki.km.management.i18n_ca_key_usage_stats=CA密钥使用数据统计
kl.npki.km.management.i18n_ca_key_service_invocation_stats=CA密钥服务调用情况

# kl.npki.km.management.controller.statistic.CaBizStatisticController
kl.npki.km.management.i18n_ca_service_statistical_report=CA服务统计报表
kl.npki.km.management.i18n_exported_ca_service_report=导出CA服务统计报表

# kl.npki.km.management.controller.statistic.KeyStatisticController
kl.npki.km.management.i18n_key_vault_stats=密钥库统计
kl.npki.km.management.i18n_exported_key_vault_statistical_report=导出密钥库统计报表
kl.npki.km.management.i18n_total_key_usage_count=密钥使用情况统计
kl.npki.km.management.i18n_exported_key_usage_stat_report=导出密钥使用情况统计报表
kl.npki.km.management.i18n_key_service_stats_not_implemented=密钥服务统计(未实现)
kl.npki.km.management.i18n_key_log_analysis_stats_not_implemented=密钥日志分析统计(未实现)

# kl.npki.km.management.controller.statistic.SksBizStatisticController
kl.npki.km.management.i18n_sks_service_stat_report=协同服务统计报表
kl.npki.km.management.i18n_exported_sks_service_stat_report=导出协同服务统计报表

# kl.npki.km.management.controller.CaManagementController
kl.npki.km.management.i18n_ca_info_list_query=查询CA信息列表
kl.npki.km.management.i18n_query_all_cas=查询所有CA
kl.npki.km.management.i18n_query_ca_info_details=查询CA信息详情
kl.npki.km.management.i18n_ca_access_service=CA接入
kl.npki.km.management.i18n_ca_resource_addition=CA资源添加
kl.npki.km.management.i18n_ca_update=CA更新
kl.npki.km.management.i18n_ca_freeze=CA冻结
kl.npki.km.management.i18n_ca_unfreeze=CA解冻
kl.npki.km.management.i18n_ca_revoke=CA废除
kl.npki.km.management.i18n_ca_recovery=CA恢复
kl.npki.km.management.i18n_ca_version_list=CA版本列表
kl.npki.km.management.i18n_ca_key_response_standard_version_list=CA密钥服务响应封装格式版本

# kl.npki.km.management.controller.ConfigController
kl.npki.km.management.i18n_get_archive_key_config=获取归档配置
kl.npki.km.management.i18n_update_archive_key_conf=修改归档配置
kl.npki.km.management.i18n_trigger_key_archive_job=手动归档触发
kl.npki.km.management.i18n_get_key_conf=获取密钥配置
kl.npki.km.management.i18n_update_key_conf=修改密钥配置
kl.npki.km.management.i18n_get_km_conf=获取KM配置
kl.npki.km.management.i18n_update_km_conf=修改KM配置
kl.npki.km.management.i18n_key_type_list=获取支持的非对称密钥类型(包括抗量子算法)

# kl.npki.km.management.controller.KeyCurrentController
kl.npki.km.management.i18n_current_key_list=获取当前密钥库
kl.npki.km.management.i18n_archive_key_list=获取待归档密钥
kl.npki.km.management.i18n_archive_key=归档密钥
kl.npki.km.management.i18n_query_current_key_detail=获取当前密钥详情

# kl.npki.km.management.controller.KeyHistoryController
kl.npki.km.management.i18n_his_admin_list=查询历史密钥库
kl.npki.km.management.i18n_his_query_current_key_detail=获取历史密钥详情

# kl.npki.km.management.controller.KeyPoolController
kl.npki.km.management.i18n_bak_current_key_list=查询备用密钥库

# kl.npki.km.management.controller.KeyTraceController
kl.npki.km.management.i18n_query_key_trace_detail=获取密钥轨迹详情

# kl.npki.km.management.controller.KmAdminMgrController
kl.npki.km.management.i18n_init_admin_cert=管理员初始化签发证书
kl.npki.km.management.i18n_backup_admin_cert=备份管理员证书
kl.npki.km.management.i18n_issue_admin_cert=签发管理员证书
kl.npki.km.management.i18n_postpone_admin_cert=延期管理员证书
kl.npki.km.management.i18n_query_law_recover_admin_list=查询司法取证管理员列表

# kl.npki.km.management.controller.LawRecoverController
kl.npki.km.management.i18n_law_recover=司法恢复

# kl.npki.km.management.controller.SslClientCertController
kl.npki.km.management.i18n_sign_ca_ssl_cert=签发CA站点证书
kl.npki.km.management.i18n_update_ca_ssl_cert=更新CA站点证书
kl.npki.km.management.i18n_export_ca_ssl_cert=导出CA站点证书

# AddResourceRequest
kl.npki.km.management.i18n_key_type=密钥类型
kl.npki.km.management.i18n_the_length_of_the_key_type_cannot_exceed_128_characters=密钥类型长度不能大于128字符
kl.npki.km.management.i18n_the_key_type_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=密钥类型不能以空格、%20、%0a、%00开头或结尾
kl.npki.km.management.i18n_the_number_of_key_authorizations_cannot_be_less_than_0=增加密钥授权个数不能小于0
kl.npki.km.management.i18n_the_number_of_key_authorizations_cannot_exceed_1000000=增加密钥授权个数不能大于1000000
kl.npki.km.management.i18n_the_minimum_number_of_key_warnings_cannot_be_less_than_0=最少密钥警告个数不能小于0
kl.npki.km.management.i18n_the_minimum_number_of_key_warnings_cannot_exceed_1000000=最少密钥警告个数不能大于1000000

# CaRequest
kl.npki.km.management.i18n_ca_name_cannot_be_empty=CA名称不能为空
kl.npki.km.management.i18n_the_length_of_ca_name_cannot_exceed_128_characters=CA名称长度不能大于128字符
kl.npki.km.management.i18n_ca_name_cannot_contain_spaces_20_starting_or_ending_with_0a_or_00=CA名称不能以空格、%20、%0a、%00开头或结尾
kl.npki.km.management.i18n_ca_version_cannot_be_empty=CA版本不能为空
kl.npki.km.management.i18n_the_length_of_ca_version_cannot_exceed_128_characters=CA版本长度不能大于128字符
kl.npki.km.management.i18n_ca_version_cannot_use_spaces_20_starting_or_ending_with_0a_or_00=CA版本不能以空格、%20、%0a、%00开头或结尾
kl.npki.km.management.i18n_ca_identity_certificate_cannot_be_empty=CA身份证书不能为空
kl.npki.km.management.i18n_the_length_of_ca_identity_certificate_cannot_exceed_9216_characters=CA身份证书长度不能大于9216字符
kl.npki.km.management.i18n_ca_identity_certificate_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=CA身份证书不能以空格、%20、%0a、%00开头或结尾
kl.npki.km.management.i18n_the_default_validity_period_of_the_key_cannot_be_less_than_1_year=密钥默认有效期不能小于1年
kl.npki.km.management.i18n_the_default_validity_period_of_the_key_cannot_exceed_100_years=密钥默认有效期不能大于100年
kl.npki.km.management.i18n_the_sm2_key_standard_version_cannot_be_less_than_1=SM2密钥服务响应格式版本不能小于1
kl.npki.km.management.i18n_the_sm2_key_standard_version_cannot_exceed_2=SM2密钥服务响应格式版本不能大于2

# CaResourceRequest
kl.npki.km.management.i18n_ca_resource_key_type_cannot_be_empty=CA资源密钥类型不能为空
kl.npki.km.management.i18n_the_length_of_ca_resource_key_type_cannot_exceed_128_characters=CA资源密钥类型长度不能大于128字符
kl.npki.km.management.i18n_ca_resource_key_type_cannot_use_spaces_20_starting_or_ending_with_0a_or_00=CA资源密钥类型不能以空格、%20、%0a、%00开头或结尾
kl.npki.km.management.i18n_the_number_of_key_distributions_cannot_be_less_than_0=密钥分配数量不能小于0
kl.npki.km.management.i18n_the_number_of_key_distributions_cannot_exceed_1000000=密钥分配数量不能大于1000000
kl.npki.km.management.i18n_the_number_of_key_warnings_cannot_be_less_than_0=密钥警告数量不能小于0
kl.npki.km.management.i18n_the_number_of_key_warnings_cannot_exceed_100000=密钥警告数量不能大于100000

# CaUpdateRequest
kl.npki.km.management.i18n_the_length_of_ca_identity_certificate_cannot_exceed_4096_characters=CA身份证书长度不能大于4096字符

# ArchiveKeyConfigRequest
kl.npki.km.management.i18n_the_delayed_archiving_time_days_cannot_be_less_than_0=延迟归档时间(天)不能小于0
kl.npki.km.management.i18n_the_delayed_archiving_time_days_cannot_exceed_1000=延迟归档时间(天)不能大于1000
kl.npki.km.management.i18n_archive_paging_threshold_cannot_be_less_than_100=归档分页阈值不能小于100
kl.npki.km.management.i18n_archive_paging_threshold_cannot_exceed_1000=归档分页阈值不能大于1000
kl.npki.km.management.i18n_the_cron_expression_for_key_generation_execution_interval_cannot_be_empty=密钥生成执行间隔cron表达式不能为空！

# ArchiveKeyConfigResponse

# KeyConfigRequest
kl.npki.km.management.i18n_cache_check_time_ss_cannot_be_less_than_5=缓存检查时间(S/秒)不能小于5
kl.npki.km.management.i18n_cache_check_time_ss_cannot_exceed_86400=缓存检查时间(S/秒)不能大于86400
kl.npki.km.management.i18n_the_size_of_the_key_pool_cache_cannot_be_less_than_100=密钥缓存池的大小不能小于100
kl.npki.km.management.i18n_the_size_of_the_key_pool_cache_cannot_exceed_100000=密钥缓存池的大小不能大于100000
kl.npki.km.management.i18n_the_threshold_for_supplementing_the_key_cache_pool_cannot_be_less_than_0=密钥缓存池补充阈值百分比不能小于0
kl.npki.km.management.i18n_the_key_cache_pool_replenishment_threshold_cannot_exceed_1=密钥缓存池补充阈值百分比不能大于1
kl.npki.km.management.i18n_the_key_generation_type_configuration_cannot_be_empty=密钥生成类型配置不能为空

# KeyConfigResponse

# KeyLimitConfigRequest
kl.npki.km.management.i18n_the_key_type_cannot_be_empty=密钥类型不能为空！
kl.npki.km.management.i18n_the_key_type_cannot_exceed_128_characters=密钥类型不能大于128字符
kl.npki.km.management.i18n_the_upper_limit_of_key_generation_cannot_be_less_than_0=密钥产生上限不能小于0
kl.npki.km.management.i18n_the_upper_limit_of_key_generation_cannot_exceed_100000=密钥产生上限不能大于100000
kl.npki.km.management.i18n_the_execution_interval_for_key_generation_cannot_be_empty=密钥生成执行间隔不能为空！

# LawRecoverRequest
kl.npki.km.management.i18n_the_user_key_id_to_be_restored_cannot_be_empty=待恢复的用户密钥ID不能为空
kl.npki.km.management.i18n_the_key_location_cannot_be_empty=密钥位置不能为空
kl.npki.km.management.i18n_judicial_recovery_administrator_id_cannot_be_empty=司法恢复管理员ID不能为空
kl.npki.km.management.i18n_the_certificate_request_in_the_medium_to_be_restored_cannot_be_empty=待恢复介质中的证书请求不能为空
kl.npki.km.management.i18n_the_signature_value_of_the_judicial_administrator_cannot_be_empty=司法管理员的签名值不能为空
kl.npki.km.management.i18n_the_original_signature_cannot_be_empty=签名原文不能为空

# sql T_RESOURCE
kl.npki.km.management.i18n_advanced_configuration=高级配置
kl.npki.km.management.i18n_km_management=密钥管理
kl.npki.km.management.i18n_current_in_use_key_store_management=在用密钥库管理
kl.npki.km.management.i18n_historical_key_store_management=历史密钥库管理
kl.npki.km.management.i18n_backup_key_store_management=备用密钥库管理
kl.npki.km.management.i18n_judicial_evidence_collection=司法取证
kl.npki.km.management.i18n_statistical_report=统计报表
kl.npki.km.management.i18n_key_store_statistics=密钥库统计
kl.npki.km.management.i18n_key_usage_data_report=密钥使用数据报表
kl.npki.km.management.i18n_ca_management=CA管理
## kl.npki.km.management.i18n_ca_service_statistical_report
## kl.npki.km.management.i18n_sks_service_stat_report

# kl.npki.km.management.check.CaResourceCheckItem
kl.npki.km.management.i18n_ca_resource_normal_i18n_key=密钥资源数量正常
kl.npki.km.management.i18n_ca_resource_warning_i18n_key=密钥资源数量{0}到达警告值{1}
kl.npki.km.management.i18n_ca_resource_exhausted_i18n_key=密钥资源已耗尽
kl.npki.km.management.i18n_no_ca_resource_record_i18n_key=暂无CA密钥资源记录
kl.npki.km.management.i18n_all_ca_key_resources_normal_i18n_key=所有CA密钥资源数量正常
kl.npki.km.management.i18n_some_ca_key_resources_warning_i18n_key=部分CA的密钥资源数量到达警告值
kl.npki.km.management.i18n_some_ca_key_resources_exhausted_i18n_key=部分CA的密钥资源已耗尽

# kl.npki.km.management.check.KmServiceCheckItem
kl.npki.km.management.i18n_km_service_normal_i18n_key=KM服务运行正常
kl.npki.km.management.i18n_km_service_abnormal_i18n_key=KM服务运行异常
kl.npki.km.management.i18n_km_service_check_success_i18n_key=正常
kl.npki.km.management.i18n_km_service_check_error_message_i18n_key=错误信息
kl.npki.km.management.i18n_km_service_check_stack_trace_i18n_key=堆栈跟踪

# kl.npki.km.management.check.SksServiceCheckItem
kl.npki.km.management.i18n_all_sks_api_normal_i18n_key=所有协同服务接口正常
kl.npki.km.management.i18n_some_sks_api_abnormal_i18n_key=部分协同服务接口异常

# SksServiceCheckItem API keys
kl.npki.km.management.i18n_sks_get_random_number_api_i18n_key=获取随机数接口
kl.npki.km.management.i18n_sks_create_private_key_api_i18n_key=保存协同密钥接口
kl.npki.km.management.i18n_sks_get_private_key_api_i18n_key=获取协同密钥接口
kl.npki.km.management.i18n_sks_update_private_key_api_i18n_key=更新协同密钥状态接口
kl.npki.km.management.i18n_sks_batch_get_private_key_api_i18n_key=批量获取协同密钥接口
kl.npki.km.management.i18n_sks_remove_private_key_api_i18n_key=删除自检协同密钥接口

# SksServiceCheckItem API status
kl.npki.km.management.i18n_sks_api_check_success_i18n_key=接口自检成功
kl.npki.km.management.i18n_sks_api_check_failure_i18n_key=接口自检失败，错误信息：{0}

# kl.base.sysInfo.name
kl.npki.km.management.i18n_sysInfo_name=格尔密钥管理系统

# kl.npki.km.management.controller.ConfigController
kl.npki.km.management.i18n_engine_not_support_sm9=当前加密机不支持SM9算法，无法开启SM9密钥服务！
