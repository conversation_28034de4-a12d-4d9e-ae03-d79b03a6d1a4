kl.npki.km.management.i18n_CURRENT_KEY_NOT_FOUND_ERROR= The current library key of this ID is not found!
kl.npki.km.management.i18n_HISTORY_KEY_NOT_FOUND_ERROR = The ID history library key was not found!
kl.npki.km.management.i18n_KEY_TRACE_NOT_FOUND_ERROR = The key trace for this ID was not found!
kl.npki.km.management.i18n_REMOTE_SYSTEM_ERROR=Error calling the remote service!
kl.npki.km.management.i18n_PARAM_ERROR=Parameter error!
kl.npki.km.management.i18n_SYSTEM_ERROR=System error!
kl.npki.km.management.i18n_SM9_KEY_SERVICE_ENABLED_ERROR=SM9 key service enabled failed!

# kl.npki.km.management.controller.home.SslClientCertController
kl.npki.km.management.i18n_ssl_client_cert_zip_name=%s_ssl-client-cert_%s.zip

# kl.npki.km.management.controller.home.HomePageController
kl.npki.km.management.i18n_key_status_data_stats=Key status data statistics
kl.npki.km.management.i18n_key_vault_data_stats=Key Vault Data Statistics
kl.npki.km.management.i18n_ca_key_usage_stats=CA Key Usage Data Statistics
kl.npki.km.management.i18n_ca_key_service_invocation_stats=CA Key Service Invocation Situation

# kl.npki.km.management.controller.statistic.CaBizStatisticController
kl.npki.km.management.i18n_ca_service_statistical_report=CA Service Statistical Report
kl.npki.km.management.i18n_exported_ca_service_report=Export CA Service Statistical Report

# kl.npki.km.management.controller.statistic.KeyStatisticController
kl.npki.km.management.i18n_key_vault_stats=Key Vault Statistics
kl.npki.km.management.i18n_exported_key_vault_statistical_report=Export Key Vault Statistical Report
kl.npki.km.management.i18n_total_key_usage_count=Key Usage Statistics
kl.npki.km.management.i18n_exported_key_usage_stat_report=Export Key Usage Statistical Report
kl.npki.km.management.i18n_key_service_stats_not_implemented=Key Service Statistics (Not Implemented)
kl.npki.km.management.i18n_key_log_analysis_stats_not_implemented=Key Log Analysis Statistics (Not Implemented)

# kl.npki.km.management.controller.statistic.SksBizStatisticController
kl.npki.km.management.i18n_sks_service_stat_report=Threshold Service Statistical Report
kl.npki.km.management.i18n_exported_sks_service_stat_report=Export Threshold Service Statistical Report
kl.npki.km.management.i18n_admin_import=Import Administrator
# kl.npki.km.management.controller.CaManagementController
kl.npki.km.management.i18n_ca_info_list_query=Query CA Information List
kl.npki.km.management.i18n_query_all_cas=Query All CAs
kl.npki.km.management.i18n_query_ca_info_details=Query CA Information Details
kl.npki.km.management.i18n_ca_access_service=CA Access
kl.npki.km.management.i18n_ca_resource_addition=CA Resource Addition
kl.npki.km.management.i18n_ca_update=CA Update
kl.npki.km.management.i18n_ca_freeze=CA Freeze
kl.npki.km.management.i18n_ca_unfreeze=CA Unfreeze
kl.npki.km.management.i18n_ca_revoke=CA Revoke
kl.npki.km.management.i18n_ca_recovery=CA Recovery
kl.npki.km.management.i18n_ca_version_list=CA Version List
kl.npki.km.management.i18n_ca_key_response_standard_version_list=CA Key Response Standard Version List

# kl.npki.km.management.controller.ConfigController
kl.npki.km.management.i18n_get_archive_key_config = Get Archive Key Configuration
kl.npki.km.management.i18n_update_archive_key_conf = Update Archive Key Configuration
kl.npki.km.management.i18n_trigger_key_archive_job = Manual Archive Trigger
kl.npki.km.management.i18n_get_key_conf = Get Key Configuration
kl.npki.km.management.i18n_update_key_conf = Update Key Configuration
kl.npki.km.management.i18n_get_km_conf = Get KM Configuration
kl.npki.km.management.i18n_update_km_conf = Update KM Configuration
kl.npki.km.management.i18n_key_type_list = Get Supported Asymmetric Key Types (including post-quantum algorithms)

# kl.npki.km.management.controller.KeyCurrentController
kl.npki.km.management.i18n_current_key_list = Get Current Key Store
kl.npki.km.management.i18n_archive_key_list = Get Keys to be Archived
kl.npki.km.management.i18n_archive_key = Archive Key
kl.npki.km.management.i18n_query_current_key_detail = Get Current Key Details

# kl.npki.km.management.controller.KeyHistoryController
kl.npki.km.management.i18n_his_admin_list = Query Historical Key Store
kl.npki.km.management.i18n_his_query_current_key_detail = Get Historical Key Details

# kl.npki.km.management.controller.KeyPoolController
kl.npki.km.management.i18n_bak_current_key_list = Query Backup Key Store

# kl.npki.km.management.controller.KeyTraceController
kl.npki.km.management.i18n_query_key_trace_detail = Obtain Key Trace Details

# kl.npki.km.management.controller.KmAdminMgrController
kl.npki.km.management.i18n_init_admin_cert = Initialize and Issue Administrator Certificate
kl.npki.km.management.i18n_backup_admin_cert = Backup Administrator Certificate
kl.npki.km.management.i18n_issue_admin_cert = Issue Administrator Certificate
kl.npki.km.management.i18n_postpone_admin_cert = Postpone Administrator Certificate
kl.npki.km.management.i18n_query_law_recover_admin_list=Query Judicial Forensics Administrator List

# kl.npki.km.management.controller.LawRecoverController
kl.npki.km.management.i18n_law_recover=lawRecover

# kl.npki.km.management.controller.SslClientCertController
kl.npki.km.management.i18n_sign_ca_ssl_cert = Issue CA Site Certificate
kl.npki.km.management.i18n_update_ca_ssl_cert = Update CA Site Certificate
kl.npki.km.management.i18n_export_ca_ssl_cert = Export CA Site Certificate

# AddResourceRequest
kl.npki.km.management.i18n_key_type=Key type
kl.npki.km.management.i18n_the_length_of_the_key_type_cannot_exceed_128_characters=The length of the key type cannot exceed 128 characters
kl.npki.km.management.i18n_the_key_type_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=The key type cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'
kl.npki.km.management.i18n_the_number_of_key_authorizations_cannot_be_less_than_0=The number of key authorizations cannot be less than 0
kl.npki.km.management.i18n_the_number_of_key_authorizations_cannot_exceed_1000000=The number of key authorizations cannot exceed 1000000
kl.npki.km.management.i18n_the_minimum_number_of_key_warnings_cannot_be_less_than_0=The minimum number of key warnings cannot be less than 0
kl.npki.km.management.i18n_the_minimum_number_of_key_warnings_cannot_exceed_1000000=The minimum number of key warnings cannot exceed 1000000

# CaRequest
kl.npki.km.management.i18n_ca_name_cannot_be_empty=CA name cannot be empty
kl.npki.km.management.i18n_the_length_of_ca_name_cannot_exceed_128_characters=The length of CA name cannot exceed 128 characters
kl.npki.km.management.i18n_ca_name_cannot_contain_spaces_20_starting_or_ending_with_0a_or_00=CA name cannot contain spaces,% 20. % Starting or ending with '0a' or '% 00'
kl.npki.km.management.i18n_ca_version_cannot_be_empty=CA version cannot be empty
kl.npki.km.management.i18n_the_length_of_ca_version_cannot_exceed_128_characters=The length of CA version cannot exceed 128 characters
kl.npki.km.management.i18n_ca_version_cannot_use_spaces_20_starting_or_ending_with_0a_or_00=CA version cannot use spaces,% 20. % Starting or ending with '0a' or '% 00'
kl.npki.km.management.i18n_ca_identity_certificate_cannot_be_empty=CA identity certificate cannot be empty
kl.npki.km.management.i18n_the_length_of_ca_identity_certificate_cannot_exceed_9216_characters=The length of CA identity certificate cannot exceed 9216 characters
kl.npki.km.management.i18n_ca_identity_certificate_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=CA identity certificate cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'
kl.npki.km.management.i18n_the_default_validity_period_of_the_key_cannot_be_less_than_1_year=The default validity period of the key cannot be less than 1 year
kl.npki.km.management.i18n_the_default_validity_period_of_the_key_cannot_exceed_100_years=The default validity period of the key cannot exceed 100 years
kl.npki.km.management.i18n_the_sm2_key_standard_version_cannot_be_less_than_1=The version of the SM2 key service response format cannot be less than 1
kl.npki.km.management.i18n_the_sm2_key_standard_version_cannot_exceed_2=The version of the SM2 key service response format cannot be greater than 2

# CaResourceRequest
kl.npki.km.management.i18n_ca_resource_key_type_cannot_be_empty=CA resource key type cannot be empty
kl.npki.km.management.i18n_the_length_of_ca_resource_key_type_cannot_exceed_128_characters=The length of CA resource key type cannot exceed 128 characters
kl.npki.km.management.i18n_ca_resource_key_type_cannot_use_spaces_20_starting_or_ending_with_0a_or_00=CA resource key type cannot use spaces,% 20. % Starting or ending with '0a' or '% 00'
kl.npki.km.management.i18n_the_number_of_key_distributions_cannot_be_less_than_0=The number of key distributions cannot be less than 0
kl.npki.km.management.i18n_the_number_of_key_distributions_cannot_exceed_1000000=The number of key distributions cannot exceed 1000000
kl.npki.km.management.i18n_the_number_of_key_warnings_cannot_be_less_than_0=The number of key warnings cannot be less than 0
kl.npki.km.management.i18n_the_number_of_key_warnings_cannot_exceed_100000=The number of key warnings cannot exceed 100000

# CaUpdateRequest
kl.npki.km.management.i18n_the_length_of_ca_identity_certificate_cannot_exceed_4096_characters=The length of CA identity certificate cannot exceed 4096 characters

# ArchiveKeyConfigRequest
kl.npki.km.management.i18n_the_delayed_archiving_time_days_cannot_be_less_than_0=The delayed archiving time (days) cannot be less than 0
kl.npki.km.management.i18n_the_delayed_archiving_time_days_cannot_exceed_1000=The delayed archiving time (days) cannot exceed 1000
kl.npki.km.management.i18n_archive_paging_threshold_cannot_be_less_than_100=Archive paging threshold cannot be less than 100
kl.npki.km.management.i18n_archive_paging_threshold_cannot_exceed_1000=Archive paging threshold cannot exceed 1000
kl.npki.km.management.i18n_the_cron_expression_for_key_generation_execution_interval_cannot_be_empty=The cron expression for key generation execution interval cannot be empty!

# ArchiveKeyConfigResponse

# KeyConfigRequest
kl.npki.km.management.i18n_cache_check_time_ss_cannot_be_less_than_5=Cache check time (S\/s) cannot be less than 5
kl.npki.km.management.i18n_cache_check_time_ss_cannot_exceed_86400=Cache check time (S\/s) cannot exceed 86400
kl.npki.km.management.i18n_the_size_of_the_key_pool_cache_cannot_be_less_than_100=The size of the key pool cache cannot be less than 100
kl.npki.km.management.i18n_the_size_of_the_key_pool_cache_cannot_exceed_100000=The size of the key pool cache cannot exceed 100000
kl.npki.km.management.i18n_the_threshold_for_supplementing_the_key_cache_pool_cannot_be_less_than_0=The replenishment threshold percentage for key cache pool cannot be less than 0
kl.npki.km.management.i18n_the_key_cache_pool_replenishment_threshold_cannot_exceed_1=The replenishment threshold percentage for key cache pool cannot exceed 1
kl.npki.km.management.i18n_the_key_generation_type_configuration_cannot_be_empty=The key generation type configuration cannot be empty

# KeyConfigResponse

# KeyLimitConfigRequest
kl.npki.km.management.i18n_the_key_type_cannot_be_empty=The key type cannot be empty!
kl.npki.km.management.i18n_the_key_type_cannot_exceed_128_characters=The key type cannot exceed 128 characters
kl.npki.km.management.i18n_the_upper_limit_of_key_generation_cannot_be_less_than_0=The upper limit of key generation cannot be less than 0
kl.npki.km.management.i18n_the_upper_limit_of_key_generation_cannot_exceed_100000=The upper limit of key generation cannot exceed 100000
kl.npki.km.management.i18n_the_execution_interval_for_key_generation_cannot_be_empty=The execution interval for key generation cannot be empty!

# LawRecoverRequest
kl.npki.km.management.i18n_the_user_key_id_to_be_restored_cannot_be_empty=The user key ID to be restored cannot be empty
kl.npki.km.management.i18n_the_key_location_cannot_be_empty=The key location cannot be empty
kl.npki.km.management.i18n_judicial_recovery_administrator_id_cannot_be_empty=Judicial recovery administrator ID cannot be empty
kl.npki.km.management.i18n_the_certificate_request_in_the_medium_to_be_restored_cannot_be_empty=The certificate request in the medium to be restored cannot be empty
kl.npki.km.management.i18n_the_signature_value_of_the_judicial_administrator_cannot_be_empty=The signature value of the judicial administrator cannot be empty
kl.npki.km.management.i18n_the_original_signature_cannot_be_empty=The original signature cannot be empty

# sql T_RESOURCE
kl.npki.km.management.i18n_advanced_configuration=Advanced Configuration
kl.npki.km.management.i18n_km_management=Key Management
kl.npki.km.management.i18n_current_in_use_key_store_management=Active Repository
kl.npki.km.management.i18n_historical_key_store_management=Historical Repository
kl.npki.km.management.i18n_backup_key_store_management=Standby Repository
kl.npki.km.management.i18n_judicial_evidence_collection=Digital Forensics
kl.npki.km.management.i18n_statistical_report=Statistical Report
kl.npki.km.management.i18n_key_store_statistics=Key Store Statistics
kl.npki.km.management.i18n_key_usage_data_report=Key Usage Data Report
kl.npki.km.management.i18n_ca_management=CA Management
## kl.npki.km.management.i18n_ca_service_statistical_report
## kl.npki.km.management.i18n_sks_service_stat_report

# kl.npki.km.management.check.CaResourceCheckItem
kl.npki.km.management.i18n_ca_resource_normal_i18n_key=The number of keys is normal
kl.npki.km.management.i18n_ca_resource_warning_i18n_key=The number of keys({0}) has reached the warning threshold({1})
kl.npki.km.management.i18n_ca_resource_exhausted_i18n_key=The number of keys is exhausted
kl.npki.km.management.i18n_no_ca_resource_record_i18n_key=No CA resource records 
kl.npki.km.management.i18n_all_ca_key_resources_normal_i18n_key=The key resources of all CA are normal
kl.npki.km.management.i18n_some_ca_key_resources_warning_i18n_key=The key resources of some CA have reached the warning threshold
kl.npki.km.management.i18n_some_ca_key_resources_exhausted_i18n_key=The key resources of some CA are exhausted

# kl.npki.km.management.check.KmServiceCheckItem
kl.npki.km.management.i18n_km_service_normal_i18n_key=KM service is normal
kl.npki.km.management.i18n_km_service_abnormal_i18n_key=KM service is abnormal
kl.npki.km.management.i18n_km_service_check_success_i18n_key=UP
kl.npki.km.management.i18n_km_service_check_error_message_i18n_key=errorMeesage
kl.npki.km.management.i18n_km_service_check_stack_trace_i18n_key=stackTrace

# kl.npki.km.management.check.SksServiceCheckItem
kl.npki.km.management.i18n_all_sks_api_normal_i18n_key=All Threshold  API is normal
kl.npki.km.management.i18n_some_sks_api_abnormal_i18n_key=Some Threshold  API is abnormal

# SksServiceCheckItem API keys
kl.npki.km.management.i18n_sks_get_random_number_api_i18n_key=Get Random Number API
kl.npki.km.management.i18n_sks_create_private_key_api_i18n_key=Save Threshold Key API
kl.npki.km.management.i18n_sks_get_private_key_api_i18n_key=Get Threshold Key API
kl.npki.km.management.i18n_sks_update_private_key_api_i18n_key=Update Threshold Key Status API
kl.npki.km.management.i18n_sks_batch_get_private_key_api_i18n_key=Batch Get Threshold Keys API
kl.npki.km.management.i18n_sks_remove_private_key_api_i18n_key=Remove Self-Check Threshold Key API

# SksServiceCheckItem API status
kl.npki.km.management.i18n_sks_api_check_success_i18n_key=API self-check successful
kl.npki.km.management.i18n_sks_api_check_failure_i18n_key=API self-check failed, error message: {0}

# kl.base.sysInfo.name
kl.npki.km.management.i18n_sysInfo_name=KOAL Key Management System

# kl.npki.km.management.controller.ConfigController
kl.npki.km.management.i18n_engine_not_support_sm9=The current engine does not support the SM9 algorithm and cannot enable the SM9 key service!