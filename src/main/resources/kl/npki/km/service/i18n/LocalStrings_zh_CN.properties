# kl.npki.km.service.controller.KmController
kl.npki.km.service.i18n_key_service=密钥服务

# kl.npki.km.service.listener.AbstractKeyEntityChangeListener
kl.npki.km.service.i18n_system_operator=系统自身

# kl.npki.km.service.constant.SksConstants.KmsError
kl.npki.km.service.i18n_system_error=系统错误
kl.npki.km.service.i18n_field_validate_faild=参数校验失败
kl.npki.km.service.i18n_user_not_exist=用户密钥不存在
kl.npki.km.service.i18n_user_not_self_checking=密钥个人片段非自检数据，不容许删除
kl.npki.km.service.i18n_cmk_index_error=系统缓存中不包含该索引的cmk，请确认该索引
kl.npki.km.service.i18n_client_key_revoked=分量已被废除
kl.npki.km.service.i18n_duplicate_key=数据库中已存在该记录
kl.npki.km.service.i18n_db_timeout=连接数据库超时
kl.npki.km.service.i18n_db_service_unavailable=数据库服务不可用
kl.npki.km.service.i18n_sdf_client_error=加密机或者加密卡连接异常
kl.npki.km.service.i18n_trigger_access_restrictions=请求数达到访问限制
kl.npki.km.service.i18n_demo_key_usage_exceeded=测试环境已达到密钥数量上限(100个),请部署使用正式环境并导入相应的license!
kl.npki.km.service.i18n_license_validation_fails=license效验不通过,接口不可用
