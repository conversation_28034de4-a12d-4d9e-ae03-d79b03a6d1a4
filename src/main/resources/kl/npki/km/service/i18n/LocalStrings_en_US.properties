# kl.npki.km.service.controller.KmController
kl.npki.km.service.i18n_key_service=Key Service

# kl.npki.km.service.listener.AbstractKeyEntityChangeListener
kl.npki.km.service.i18n_system_operator=SYSTEM

# kl.npki.km.service.constant.SksConstants.KmsError
kl.npki.km.service.i18n_system_error=System error
kl.npki.km.service.i18n_field_validate_faild=Parameter verification failed
kl.npki.km.service.i18n_user_not_exist=The key doesn't exist
kl.npki.km.service.i18n_user_not_self_checking=The key is not self checking data and can not be deleted
kl.npki.km.service.i18n_cmk_index_error=System main key not found
kl.npki.km.service.i18n_client_key_revoked=The key has been revoked
kl.npki.km.service.i18n_duplicate_key=The key already exists
kl.npki.km.service.i18n_db_timeout=DB timeout
kl.npki.km.service.i18n_db_service_unavailable=DB unavailable
kl.npki.km.service.i18n_sdf_client_error=Cryptographic device unavailable
kl.npki.km.service.i18n_trigger_access_restrictions=Current requests exceeded the flow control threshold
kl.npki.km.service.i18n_demo_key_usage_exceeded=Trial keys quota(100) exhausted, please deploy the system and import a license to proceed
kl.npki.km.service.i18n_license_validation_fails=License verification failed