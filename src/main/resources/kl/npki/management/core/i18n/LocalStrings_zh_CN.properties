# ManagementInternalError
kl.npki.management.core.i18n_system_error=系统错误
kl.npki.management.core.i18n_no_such_config_error=不存在对应配置错误
kl.npki.management.core.i18n_refresh_data_source_error=刷新数据源错误
kl.npki.management.core.i18n_save_config_error=保存配置信息错误
kl.npki.management.core.i18n_file_save_error=刷新配置文件错误
kl.npki.management.core.i18n_property_not_exists=配置中不存在该属性
kl.npki.management.core.i18n_file_load_error=配置文件加载错误
kl.npki.management.core.i18n_sql_script_run_error=数据库脚本执行失败
kl.npki.management.core.i18n_create_datasource_error=创建数据源失败
kl.npki.management.core.i18n_system_init_config_error=系统初始化配置失败
kl.npki.management.core.i18n_no_such_ket_type_em_config=不存在对应密钥类型的加密机配置
kl.npki.management.core.i18n_no_such_asym_algo_em_config=不存在对应算法类型的加密机配置
kl.npki.management.core.i18n_cache_put_error=设置缓存错误
kl.npki.management.core.i18n_cache_get_error=获取缓存错误
kl.npki.management.core.i18n_captcha_gen_error=验证码生成错误
kl.npki.management.core.i18n_not_login=未登录
kl.npki.management.core.i18n_admin_cert_return_fail=管理员证书返回失败
kl.npki.management.core.i18n_admin_permission_filter_error=管理员权限过滤异常
kl.npki.management.core.i18n_encode_error=编码异常
kl.npki.management.core.i18n_decode_error=解码异常
kl.npki.management.core.i18n_db_insert_error=数据新增异常
kl.npki.management.core.i18n_db_delete_error=数据删除异常
kl.npki.management.core.i18n_db_update_error=数据修改异常
kl.npki.management.core.i18n_db_query_error=数据查询异常
kl.npki.management.core.i18n_role_used=角色在使用
kl.npki.management.core.i18n_issue_cert_error=签发证书失败
kl.npki.management.core.i18n_verify_signed_error=验证签名失败
kl.npki.management.core.i18n_not_trust_cert_error=证书不被信任
kl.npki.management.core.i18n_trust_cert_is_empty=信任证书为空
kl.npki.management.core.i18n_dn_build_error=证书DN构造失败
kl.npki.management.core.i18n_keystore_already_exists=Keystore文件已存在
kl.npki.management.core.i18n_generate_keystore_error=生成Keystore文件失败
kl.npki.management.core.i18n_pkcs10_request_not_null=p10请求不能为空
kl.npki.management.core.i18n_pkcs10_request_dn_error=p10请求内容已篡改
kl.npki.management.core.i18n_pkcs10_content_error=[{0}]与管理员信息不匹配
kl.npki.management.core.i18n_pkcs10_request_algo_error=p10请求算法与根证书算法不匹配
kl.npki.management.core.i18n_not_found_db_config=数据库配置不存在
kl.npki.management.core.i18n_db_conn_failed=数据库连接失败
kl.npki.management.core.i18n_get_engine_service_fail=获取加密机服务失败
kl.npki.management.core.i18n_get_key_pair_fail=获取密钥对失败
kl.npki.management.core.i18n_engine_config_valid_fail=加密机配置验证失败
kl.npki.management.core.i18n_engine_config_ip_valid_fail=加密机无法连接，ip或端口配置错误
kl.npki.management.core.i18n_audit_log_config_error=请填写审计服务相关配置
kl.npki.management.core.i18n_file_read_fail=文件读取失败
kl.npki.management.core.i18n_log_path_not_exist=日志路径不存在
kl.npki.management.core.i18n_file_create_fail=文件创建失败
kl.npki.management.core.i18n_file_delete_fail=文件删除失败
kl.npki.management.core.i18n_config_file_data_full_init_fail=配置文件完整性值初始化失败
kl.npki.management.core.i18n_config_file_data_full_check_fail=配置文件被篡改
kl.npki.management.core.i18n_encrypt_zip_fail=加密压缩文件失败
kl.npki.management.core.i18n_un_encrypt_zip_fail=解压加密压缩文件失败
kl.npki.management.core.i18n_restore_system_failed=系统恢复失败
kl.npki.management.core.i18n_un_encrypt_zip_pwd_fail=解压密码不正确
kl.npki.management.core.i18n_ssl_protocol_cannot_be_empty=SSL协议不能为空
kl.npki.management.core.i18n_duplicate_admin_username=管理员名称重复
kl.npki.management.core.i18n_log_file_size_parse_error=日志文件容量配置解析失败
# ManagementRemoteError

# ManagementValidationError
kl.npki.management.core.i18n_param_error=参数错误
kl.npki.management.core.i18n_biz_status_error=业务状态错误
kl.npki.management.core.i18n_cert_status_error=证书状态错误
kl.npki.management.core.i18n_admin_not_exists_error=管理员不存在
kl.npki.management.core.i18n_role_not_exists=角色不存在
kl.npki.management.core.i18n_sys_role_not_remove=系统角色不能删除
kl.npki.management.core.i18n_auth_ref_id_error=认证ID不能为空
kl.npki.management.core.i18n_duplication_username=用户名重复
kl.npki.management.core.i18n_admin_pwd_error=管理员密码不正确
kl.npki.management.core.i18n_role_type_error=角色类型错误
kl.npki.management.core.i18n_no_permission=没有权限
kl.npki.management.core.i18n_log_file_size_config_error=日志文件容量配置有误
kl.npki.management.core.i18n_sys_log_config_error=请填写审计服务相关配置
kl.npki.management.core.i18n_invalid_subject_cn=无效的SubjectCn
kl.npki.management.core.i18n_init_admin_timing_error=非部署阶段不容许初始化管理员
kl.npki.management.core.i18n_init_admin_role_error=部署阶段不能初始化该角色的管理员
kl.npki.management.core.i18n_init_admin_limit_error=该角色的管理员初始化数量不能超出限制
kl.npki.management.core.i18n_ssl_client_cert_already_exists=SSL客户端证书已存在
kl.npki.management.core.i18n_ssl_client_cert_not_found=SSL客户端证书不存在
kl.npki.management.core.i18n_backup_admin_timing_error=部署未完成不容许备份管理员
kl.npki.management.core.i18n_backup_admin_role_error=不容许备份该角色的管理员
kl.npki.management.core.i18n_backup_admin_limit_error=该角色的管理员备份数量不能超出限制
kl.npki.management.core.i18n_imported_cert_not_support_error=导入的证书不支持当前操作
kl.npki.management.core.i18n_not_imported_admin_error=当前管理员不是导入的管理员
kl.npki.management.core.i18n_old_admin_has_encrypted_cert=当前管理员已有加密证书
kl.npki.management.core.i18n_redis_connection_error=REDIS连通性检测失败
kl.npki.management.core.i18n_redis_address_empty=REDIS地址配置为空
kl.npki.management.core.i18n_cert_req_not_base64=证书请求不符合BASE64编码
kl.npki.management.core.i18n_cert_req_not_pkcs10=证书请求不符合P10结构
kl.npki.management.core.i18n_invalid_auth_type=无效的认证类型
kl.npki.management.core.i18n_invalid_cert_sn=无效的证书序列号
kl.npki.management.core.i18n_invalid_cookie_info=无效的cookie信息
kl.npki.management.core.i18n_missing_other_admin_login_info=缺少其它管理员登录信息
kl.npki.management.core.i18n_admin_cannot_repeat=多选登录下，不允许使用重复的管理员进行登录
kl.npki.management.core.i18n_captcha_verify_fail=验证码错误
kl.npki.management.core.i18n_system_backup_pwd_cannot_be_null=备份恢复密码不能为空
kl.npki.management.core.i18n_system_backup_file_not_zip=当前文件不是zip格式
kl.npki.management.core.i18n_system_backup_file_not_current_system=恢复文件跟当前系统不匹配

#Enum
kl.npki.management.core.i18n_super_admin=超级管理员
kl.npki.management.core.i18n_security_admin=安全管理员
kl.npki.management.core.i18n_audit_admin=审计管理员
kl.npki.management.core.i18n_biz_admin=业务管理员
kl.npki.management.core.i18n_audit_oper=审计员
kl.npki.management.core.i18n_biz_oper=业务操作员
kl.npki.management.core.i18n_law_admin=司法取证人员
kl.npki.management.core.i18n_deploy_operator=部署操作员
kl.npki.management.core.i18n_protected_key_import=通过保护密钥对的方式进行导入
kl.npki.management.core.i18n_pfx_import=通过pfx类型的证书进行导入
kl.npki.management.core.i18n_cert_import=通过证书进行导入
kl.npki.management.core.i18n_deploy_oper=部署操作员
kl.npki.management.core.i18n_sys_role=系统角色
kl.npki.management.core.i18n_custom_role=自定义角色

# 异常描述
kl.npki.management.core.i18n_id_cert_issue_error=身份证书签发失败
kl.npki.management.core.i18n_id_cert_update_error=身份证书更新失败
kl.npki.management.core.i18n_ssl_cert_issue_error=SSL证书签发失败
kl.npki.management.core.i18n_unsupported_system_module=不支持的系统模块:{0}
kl.npki.management.core.i18n_password_machine_service_address_resolution_failed=密码机服务地址解析失败
kl.npki.management.core.i18n_certificate_status_does_not_match_expectations=当前证书(CN={0})状态为: {1}, 期望的证书状态为: {2}
kl.npki.management.core.i18n_the_dn_entries_of_the_encryption_certificate_and_the_signature_certificate_do_not_match=加密证书与签名证书DN项不匹配
kl.npki.management.core.i18n_please_initialize_the_following_administrators=请初始化以下管理员:{0}
kl.npki.management.core.i18n_the_role_does_not_meet_the_requirements=当前角色为：{0}，需要的角色为：{1}
kl.npki.management.core.i18n_user_status_does_not_match_expectations=当前用户状态为: {0}，期望的用户状态为:{1}
kl.npki.management.core.i18n_duplicate_user_names=用户名[{0}]已存在
kl.npki.management.core.i18n_inconsistent_user_names=用户名不一致
kl.npki.management.core.i18n_number_of_initialized_administrators=已经初始化【{0}】个管理员
kl.npki.management.core.i18n_number_of_backed_up_administrators=已经备份【{0}】个管理员
kl.npki.management.core.i18n_the_imported_certificate_is_not_a_signed_certificate=导入的证书不是签名证书
kl.npki.management.core.i18n_the_imported_certificate_is_not_an_encrypted_certificate=导入的证书不是加密证书
kl.npki.management.core.i18n_the_original_encryption_certificate_is_not_empty_the_imported_encryption_certificate_is_empty=原加密证书不为空，导入的加密证书为空
kl.npki.management.core.i18n_no_permission_to_request_revocation_of_this_type_of_administrator_certificate=没有请求废除该类型管理员证书的权限
kl.npki.management.core.i18n_the_administrators_permission_for_this_type_has_not_been_revoked=没有废除该类型管理员的权限
kl.npki.management.core.i18n_do_not_have_the_authority_to_revoke_the_administrator_certificate_revocation_application_for_this_type=没有撤销该类型管理员证书废除申请的权限
kl.npki.management.core.i18n_do_not_have_permission_to_log_out_of_this_type_of_administrator=没有注销该类型管理员的权限
kl.npki.management.core.i18n_no_extension_of_administrator_privileges_for_this_type=没有延期该类型管理员的权限
kl.npki.management.core.i18n_administrator_entered_incorrect_old_password=管理员输入的旧密码错误
kl.npki.management.core.i18n_administrator_entered_old_and_new_password_cannot_be_the_same=管理员输入的旧密码与新密码不能相同
kl.npki.management.core.i18n_repeated_subjectcn=重复的SubjectCn
kl.npki.management.core.i18n_failed_to_construct_anti_quantum_key_envelope=构建抗量子密钥信封失败
kl.npki.management.core.i18n_failed_to_construct_enveloped_data=构建数字信封失败
kl.npki.management.core.i18n_do_not_have_the_authority_to_extend_this_type_of_administrator_certificate=没有延期该类型管理员证书的权限
kl.npki.management.core.i18n_anti_quantum_encryption_key_type_not_specified=抗量子加密密钥类型未指定
kl.npki.management.core.i18n_the_role_name_already_exists=角色名已存在
kl.npki.management.core.i18n_the_role_code_already_exists=角色编码已存在
kl.npki.management.core.i18n_please_delete_the_user_using_this_role_first=请先删除使用该角色的用户
kl.npki.management.core.i18n_invalid_authentication_type=无效的认证类型{0}
kl.npki.management.core.i18n_ssl_certificate_does_not_exist=SSL证书{0}不存在
kl.npki.management.core.i18n_cookie_is_empty_ssl_channel_verification_failed=cookie为空，ssl通道验证失败
kl.npki.management.core.i18n_ssl_authentication_login_the_cookie_information_needs_to_include_the_hexadecimal_certificate_serial_number=ssl认证登录，cookie信息需要包含十六进制证书序列号
kl.npki.management.core.i18n_unable_to_find_the_corresponding_ssl_client_certificate_please_check_the_id=无法找到对应的ssl客户端证书，请检查id:{0}是否正确，并确保已有对应的证书存在
kl.npki.management.core.i18n_get_engine_info_is_empty=获取engine信息为空
kl.npki.management.core.i18n_failed_to_read_file_content=读取文件[{0}]内容失败
kl.npki.management.core.i18n_log_path_not_found=日志路径不存在
kl.npki.management.core.i18n_failed_to_perform_data_cleaning_operation_after_filtering_resource_permissions=数据清理操作过滤资源权限失败

# 常量
kl.npki.management.core.i18n_failed_to_obtain_administrator_information=获取管理员信息失败
kl.npki.management.core.i18n_partial_administrators_failed_to_issue_status_checks=部分管理员签发状态检测失败
kl.npki.management.core.i18n_detection_failed=检测失败:{0}
kl.npki.management.core.i18n_failed_to_obtain=获取{0}失败
kl.npki.management.core.i18n_not_found=未找到{0}
kl.npki.management.core.i18n_insufficient_permissions_please_log_in_to_the_system_as_an_administrator_with_the_following_roles=权限不足，请使用以下角色的管理员登录系统
kl.npki.management.core.i18n_total_number_of_tasks_and_number_of_enabled_tasks=总任务数: {0}，启用的任务数: {1}
kl.npki.management.core.i18n_is_it_enabled=是否启用
kl.npki.management.core.i18n_timed_task_status=定时任务状态
kl.npki.management.core.i18n_running=运行中
kl.npki.management.core.i18n_stopped=停止
kl.npki.management.core.i18n_last_execution_time=上次执行时间
kl.npki.management.core.i18n_unexecuted=未执行
kl.npki.management.core.i18n_certificate_validity_period_remaining=证书有效期剩余{0}天
kl.npki.management.core.i18n_certificate_has_expired=证书已过期{0}天
kl.npki.management.core.i18n_there_are_certificates_that_are_about_to_expire=存在即将在{0}天后过期的证书
kl.npki.management.core.i18n_key_generation=密钥生成
kl.npki.management.core.i18n_asymmetric_encryption_and_decryption=非对称加密解密
kl.npki.management.core.i18n_asymmetric_signature_verification=非对称签名验证
kl.npki.management.core.i18n_symmetric_encryption_and_decryption=对称加密解密
kl.npki.management.core.i18n_password_machine_service=密码机服务
kl.npki.management.core.i18n_password_machine_type=密码机类型
kl.npki.management.core.i18n_hash=哈希
kl.npki.management.core.i18n_testing=检测
kl.npki.management.core.i18n_normal=正常
kl.npki.management.core.i18n_abnormal=异常
kl.npki.management.core.i18n_system_administrator_certificate= 系统管理员证书名称
kl.npki.management.core.i18n_identity_certificate= 身份证书名称
kl.npki.management.core.i18n_manage_root_certificates= 管理根证书名称
kl.npki.management.core.i18n_ssl_site_certificate= SSL站点证书名称
kl.npki.management.core.i18n_certificate_is_valid= 证书检测有效
kl.npki.management.core.i18n_the_certificate_has_expired= 存在证书已过期
kl.npki.management.core.i18n_issued= 证书已签发
kl.npki.management.core.i18n_not_issued= 未签发
kl.npki.management.core.i18n_there_are_unassigned_administrator_roles= 存在未分配的管理员角色
kl.npki.management.core.i18n_all_administrators_have_issued_correctly= 所有管理员已正确签发
kl.npki.management.core.i18n_sign_site_certificate= SSL客户端证书_{0}.crt
kl.npki.management.core.i18n_encryption_site_certificate= 加密站点证书.crt
kl.npki.management.core.i18n_encryption_certificate_key= 加密证书密钥.key
kl.npki.management.core.i18n_certificate_chain= 证书链_{0}.crt
kl.npki.management.core.i18n_please_initialize_the_auditor=请初始化审计员
kl.npki.management.core.i18n_please_initialize_the_operator=请初始化操作员
kl.npki.management.core.i18n_ip_address= IP地址:{0}
kl.npki.management.core.i18n_port=端口:{0}
kl.npki.management.core.i18n_db_name=数据库名称:{0}
kl.npki.management.core.i18n_username=用户名:{0}

# DbConfigInfo
kl.npki.management.core.i18n_the_database_address_cannot_be_empty=数据库地址不能为空
kl.npki.management.core.i18n_the_length_of_the_database_address_cannot_exceed_128_characters=数据库地址长度不能大于128字符
kl.npki.management.core.i18n_the_database_address_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=数据库地址不能以空格、%20、%0a、%00开头或结尾
kl.npki.management.core.i18n_the_database_name_cannot_be_empty=数据库名称不能为空
kl.npki.management.core.i18n_the_length_of_the_database_name_cannot_exceed_128_characters=数据库名称长度不能大于128字符
kl.npki.management.core.i18n_the_database_name_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=数据库名称不能以空格、%20、%0a、%00开头或结尾
kl.npki.management.core.i18n_the_database_username_cannot_be_empty=数据库用户名不能为空
kl.npki.management.core.i18n_the_length_of_the_database_username_cannot_exceed_128_characters=数据库用户名长度不能大于128字符
kl.npki.management.core.i18n_the_database_username_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=数据库用户名不能以空格、%20、%0a、%00开头或结尾
kl.npki.management.core.i18n_the_database_password_cannot_be_empty=数据库密码不能为空
kl.npki.management.core.i18n_the_length_of_the_database_password_cannot_exceed_128_characters=数据库密码长度不能大于128字符
kl.npki.management.core.i18n_the_database_password_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=数据库密码不能以空格、%20、%0a、%00开头或结尾
kl.npki.management.core.i18n_the_database_character_set_cannot_be_empty=数据库字符集不能为空
kl.npki.management.core.i18n_the_length_of_the_database_character_set_cannot_exceed_128_characters=数据库字符集长度不能大于128字符
kl.npki.management.core.i18n_the_database_character_set_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=数据库字符集不能以空格、%20、%0a、%00开头或结尾
# 数据库自检相关
kl.npki.management.core.i18n_database_type=数据库类型
kl.npki.management.core.i18n_db_connection=数据库连接
kl.npki.management.core.i18n_db_query=数据库查询
kl.npki.management.core.i18n_database_service=数据库服务
kl.npki.management.core.i18n_database_version=数据库版本
# 数据库自检相关
kl.npki.management.core.i18n_disk_space=磁盘空间
kl.npki.management.core.i18n_disk_total=总空间
kl.npki.management.core.i18n_disk_free=可用空间
kl.npki.management.core.i18n_disk_used=已用空间
kl.npki.management.core.i18n_disk_usage=使用率
kl.npki.management.core.i18n_disk_path=磁盘路径
kl.npki.management.core.i18n_disk_insufficient_template=磁盘空间不足，当前使用率%s，超过阈值%d%%
kl.npki.management.core.i18n_unknown=未知
# JVM内存自检相关
kl.npki.management.core.i18n_jvm_memory=JVM内存
kl.npki.management.core.i18n_heap_memory=堆内存
kl.npki.management.core.i18n_non_heap_memory=非堆内存
kl.npki.management.core.i18n_memory_used=已用内存
kl.npki.management.core.i18n_memory_max=最大内存
kl.npki.management.core.i18n_memory_usage=使用率
kl.npki.management.core.i18n_memory_insufficient_template=JVM内存不足，当前使用率%s，超过阈值%d%%
kl.npki.management.core.i18n_no_physical_datasource=未找到物理数据源
kl.npki.management.core.i18n_connection_closed=连接已关闭
kl.npki.management.core.i18n_cannot_establish_connection=无法建立连接

# ResourceType
kl.npki.management.core.i18n_page=页面
kl.npki.management.core.i18n_menu=菜单
kl.npki.management.core.i18n_button=按钮
kl.npki.management.core.i18n_api=接口

#LogConfigService
kl.npki.management.core.i18n_must_not_be_less_than_1gb=不能小于1GB
kl.npki.management.core.i18n_cannot_exceed_1tb=不能大于1TB