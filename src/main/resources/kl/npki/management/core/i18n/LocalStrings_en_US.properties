# ManagementInternalError
kl.npki.management.core.i18n_system_error=System error!
kl.npki.management.core.i18n_no_such_config_error=No corresponding configuration error exists!
kl.npki.management.core.i18n_refresh_data_source_error=Refresh data source error!
kl.npki.management.core.i18n_save_config_error=The configuration information is saved incorrectly!
kl.npki.management.core.i18n_file_save_error=Failed to refresh the configuration file!
kl.npki.management.core.i18n_property_not_exists=This attribute does not exist in the configuration!
kl.npki.management.core.i18n_file_load_error=The configuration file failed to load!
kl.npki.management.core.i18n_sql_script_run_error=The database script fails to be executed!
kl.npki.management.core.i18n_create_datasource_error=Data source creation failure!
kl.npki.management.core.i18n_system_init_config_error=Failed to initialize the system. Procedure!
kl.npki.management.core.i18n_no_such_ket_type_em_config=No encryption machine configuration corresponding to the key type exists!
kl.npki.management.core.i18n_no_such_asym_algo_em_config=No encryption machine configuration for the algorithm type exists!
kl.npki.management.core.i18n_cache_put_error=Cache put error!
kl.npki.management.core.i18n_cache_get_error=Cache get error!
kl.npki.management.core.i18n_not_login=Not logged in!
kl.npki.management.core.i18n_admin_cert_return_fail=Description failed to return the administrator certificate!
kl.npki.management.core.i18n_admin_permission_filter_error=Admin permission filter error!
kl.npki.management.core.i18n_encode_error=Coding exception!
kl.npki.management.core.i18n_decode_error=Decoding exception!
kl.npki.management.core.i18n_db_insert_error=Data addition exception!
kl.npki.management.core.i18n_db_delete_error=Data deletion exception!
kl.npki.management.core.i18n_db_update_error=Data modification exception!
kl.npki.management.core.i18n_db_query_error=Data query exception!
kl.npki.management.core.i18n_role_used=Role in use!
kl.npki.management.core.i18n_issue_cert_error=Certificate issue failure!
kl.npki.management.core.i18n_verify_signed_error=Signature verification failure!
kl.npki.management.core.i18n_not_trust_cert_error=The certificate is not trusted!
kl.npki.management.core.i18n_trust_cert_is_empty=Trust certificate is empty!
kl.npki.management.core.i18n_dn_build_error=Description Failed to construct the certificate DN!
kl.npki.management.core.i18n_keystore_already_exists=Keystore file already exists!
kl.npki.management.core.i18n_generate_keystore_error=Failed to generate Keystore file!
kl.npki.management.core.i18n_pkcs10_request_not_null=P10 request cannot be empty!
kl.npki.management.core.i18n_pkcs10_request_dn_error=P10 request content has been tampered with！
kl.npki.management.core.i18n_pkcs10_content_error=[{0}] Mismatch with administrator information!
kl.npki.management.core.i18n_pkcs10_request_algo_error=The p10 request algorithm does not match the root certificate algorithm!
kl.npki.management.core.i18n_not_found_db_config=The database configuration does not exist!
kl.npki.management.core.i18n_db_conn_failed=Database connection failure!
kl.npki.management.core.i18n_get_engine_service_fail=Description Failed to obtain the encryptor service!
kl.npki.management.core.i18n_get_key_pair_fail=Failed to obtain the key pair!
kl.npki.management.core.i18n_engine_config_valid_fail=Valid engine config failed!
kl.npki.management.core.i18n_engine_config_ip_valid_fail=The encryption machine cannot connect， and the IP or port is misconfigured！
kl.npki.management.core.i18n_audit_log_config_error=Please fill in the configuration related to audit services!
kl.npki.management.core.i18n_file_read_fail=File read failed!
kl.npki.management.core.i18n_log_path_not_exist=Log path does not exist!
kl.npki.management.core.i18n_file_create_fail=File creation failed!
kl.npki.management.core.i18n_file_delete_fail=File deletion failed!
kl.npki.management.core.i18n_config_file_data_full_init_fail=Config file integrity value initialization failed!
kl.npki.management.core.i18n_config_file_data_full_check_fail=Config file integrity value check failed!
kl.npki.management.core.i18n_encrypt_zip_fail=Failed to encrypt the compressed file!
kl.npki.management.core.i18n_un_encrypt_zip_fail=Failed to decompress the encrypted compressed file!
kl.npki.management.core.i18n_restore_system_failed=System recovery failed!
kl.npki.management.core.i18n_un_encrypt_zip_pwd_fail=Decompression password is incorrect!
kl.npki.management.core.i18n_ssl_protocol_cannot_be_empty=SSL protocol cannot be empty!
kl.npki.management.core.i18n_duplicate_admin_username=Duplicate administrator username!
kl.npki.management.core.i18n_log_file_size_parse_error=Log file size parse error!
# ManagementRemoteError

# ManagementValidationError
kl.npki.management.core.i18n_param_error=Parameter error!
kl.npki.management.core.i18n_biz_status_error=Business status error!
kl.npki.management.core.i18n_cert_status_error=Certificate status error!
kl.npki.management.core.i18n_admin_not_exists_error=Administrator does not exist!
kl.npki.management.core.i18n_role_not_exists=Role nonexistence!
kl.npki.management.core.i18n_sys_role_not_remove=The system role cannot be deleted!
kl.npki.management.core.i18n_temp_session_id_error=The temporary session id is incorrect!
kl.npki.management.core.i18n_duplication_username=Duplicate user name!
kl.npki.management.core.i18n_admin_pwd_error=The administrator password is incorrect!
kl.npki.management.core.i18n_role_type_error=Role type error!
kl.npki.management.core.i18n_no_permission=No permission!
kl.npki.management.core.i18n_log_file_size_config_error=Log file size configuration is incorrect!
kl.npki.management.core.i18n_sys_log_config_error=Please fill in the configuration related to audit services!
kl.npki.management.core.i18n_invalid_subject_cn=The SubjectCn is invalid!
kl.npki.management.core.i18n_init_admin_timing_error=Initialization of administrators is not allowed outside the deployment phase!
kl.npki.management.core.i18n_init_admin_role_error=Initialization of administrators for this role is not allowed during the deployment phase!
kl.npki.management.core.i18n_init_admin_limit_error=The number of administrators for this role cannot exceed the limit during the deployment phase!
kl.npki.management.core.i18n_ssl_client_cert_already_exists=SSL client certificate already exists!
kl.npki.management.core.i18n_ssl_client_cert_not_found=SSL client certificate not found!
kl.npki.management.core.i18n_backup_admin_timing_error=Backup of administrators is not allowed before deployment is completed!
kl.npki.management.core.i18n_backup_admin_role_error=Backup of administrators for this role is not allowed!
kl.npki.management.core.i18n_backup_admin_limit_error=The number of backup administrators for this role cannot exceed the limit!
kl.npki.management.core.i18n_imported_cert_not_support_error=The imported certificate does not support the current operation!
kl.npki.management.core.i18n_not_imported_admin_error=The current administrator is not the imported administrator!
kl.npki.management.core.i18n_old_admin_has_encrypted_cert=The current administrator has an encrypted certificate!
kl.npki.management.core.i18n_redis_connection_error=Redis Connectivity Detection Failed!
kl.npki.management.core.i18n_redis_address_empty=The Redis address is empty!
kl.npki.management.core.i18n_cert_req_not_base64=The certificate request data is not BASE64
kl.npki.management.core.i18n_cert_req_not_pkcs10=The certificate request data is not PKCS10
kl.npki.management.core.i18n_invalid_auth_type=Invalid authentication type!
kl.npki.management.core.i18n_invalid_cert_sn=Invalid certificate serial number!
kl.npki.management.core.i18n_invalid_cookie_info=Invalid cookie information!
kl.npki.management.core.i18n_missing_other_admin_login_info=Missing other administrator login information!
kl.npki.management.core.i18n_admin_cannot_repeat=In multi-login mode, duplicate administrators are not allowed to log in!
kl.npki.management.core.i18n_captcha_verify_fail=Verification code error
kl.npki.management.core.i18n_system_backup_pwd_cannot_be_null=The backup and recovery password cannot be empty
kl.npki.management.core.i18n_system_backup_file_not_zip=The current file is not in zip format
kl.npki.management.core.i18n_system_backup_file_not_current_system=The recovered file does not match the current system

#Enum
kl.npki.management.core.i18n_super_admin=Super Administrator
kl.npki.management.core.i18n_security_admin=Security Administrator
kl.npki.management.core.i18n_audit_admin=Audit Administrator
kl.npki.management.core.i18n_biz_admin=Business Administrator
kl.npki.management.core.i18n_audit_oper=Audit Operator
kl.npki.management.core.i18n_biz_oper=Business Operator
kl.npki.management.core.i18n_law_admin=Forensic Personnel
kl.npki.management.core.i18n_deploy_operator=Deploy Operator
kl.npki.management.core.i18n_protected_key_import=Import via Protected Key Pair
kl.npki.management.core.i18n_pfx_import=Import via PFX Certificate
kl.npki.management.core.i18n_cert_import=Import via Certificate
kl.npki.management.core.i18n_deploy_oper=Deployment Operator
kl.npki.management.core.i18n_sys_role=System Role
kl.npki.management.core.i18n_custom_role=Custom Role

# 异常描述
kl.npki.management.core.i18n_id_cert_issue_error=Identity certificate issuance failed
kl.npki.management.core.i18n_id_cert_update_error=Identity certificate update failed
kl.npki.management.core.i18n_ssl_cert_issue_error=SSL certificate issuance failed
kl.npki.management.core.i18n_unsupported_system_module=Unsupported system module: {0}
kl.npki.management.core.i18n_password_machine_service_address_resolution_failed=Crytographic device service address resolution failed
kl.npki.management.core.i18n_certificate_status_does_not_match_expectations=Current certificate (CN={0}) status is: {1}, expected certificate status is: {2}
kl.npki.management.core.i18n_the_dn_entries_of_the_encryption_certificate_and_the_signature_certificate_do_not_match=DN entries of the encryption certificate and the signature certificate do not match
kl.npki.management.core.i18n_please_initialize_the_following_administrators=Please initialize the following administrators: {0}
kl.npki.management.core.i18n_the_role_does_not_meet_the_requirements=Current role is: {0}, required role is: {1}
kl.npki.management.core.i18n_user_status_does_not_match_expectations=Current user status is: {0}, expected user status is: {1}
kl.npki.management.core.i18n_duplicate_user_names=Username [{0}] already exists
kl.npki.management.core.i18n_inconsistent_user_names=Inconsistent usernames
kl.npki.management.core.i18n_number_of_initialized_administrators=Already initialized 【{0}】 administrators
kl.npki.management.core.i18n_number_of_backed_up_administrators=Already backed up 【{0}】 administrators
kl.npki.management.core.i18n_the_imported_certificate_is_not_a_signed_certificate=The imported certificate is not a signed certificate
kl.npki.management.core.i18n_the_imported_certificate_is_not_an_encrypted_certificate=The imported certificate is not an encrypted certificate
kl.npki.management.core.i18n_the_original_encryption_certificate_is_not_empty_the_imported_encryption_certificate_is_empty=The original encryption certificate is not empty, but the imported encryption certificate is empty
kl.npki.management.core.i18n_no_permission_to_request_revocation_of_this_type_of_administrator_certificate=No permission to request revocation of this type of administrator certificate
kl.npki.management.core.i18n_the_administrators_permission_for_this_type_has_not_been_revoked=No permission to revoke this type of administrator
kl.npki.management.core.i18n_do_not_have_the_authority_to_revoke_the_administrator_certificate_revocation_application_for_this_type=Do not have the authority to revoke the administrator certificate revocation application for this type
kl.npki.management.core.i18n_do_not_have_permission_to_log_out_of_this_type_of_administrator=Do not have permission to log out of this type of administrator
kl.npki.management.core.i18n_no_extension_of_administrator_privileges_for_this_type=No extension of administrator privileges for this type
kl.npki.management.core.i18n_administrator_entered_incorrect_old_password=Administrator entered incorrect old password
kl.npki.management.core.i18n_administrator_entered_old_and_new_password_cannot_be_the_same=Administrator old and new password cannot be the same
kl.npki.management.core.i18n_repeated_subjectcn=Repeated SubjectCn
kl.npki.management.core.i18n_failed_to_construct_anti_quantum_key_envelope=Failed to construct anti-quantum key envelope
kl.npki.management.core.i18n_failed_to_construct_enveloped_data=Failed to construct enveloped data
kl.npki.management.core.i18n_do_not_have_the_authority_to_extend_this_type_of_administrator_certificate=Do not have the authority to extend this type of administrator certificate
kl.npki.management.core.i18n_anti_quantum_encryption_key_type_not_specified=Anti-quantum encryption key type not specified
kl.npki.management.core.i18n_the_role_name_already_exists=Role name already exists
kl.npki.management.core.i18n_the_role_code_already_exists=Role code already exists
kl.npki.management.core.i18n_please_delete_the_user_using_this_role_first=Please delete the user using this role first
kl.npki.management.core.i18n_invalid_authentication_type=Invalid authentication type: {0}
kl.npki.management.core.i18n_ssl_certificate_does_not_exist=SSL certificate {0} does not exist
kl.npki.management.core.i18n_cookie_is_empty_ssl_channel_verification_failed=Cookie is empty, SSL channel verification failed
kl.npki.management.core.i18n_ssl_authentication_login_the_cookie_information_needs_to_include_the_hexadecimal_certificate_serial_number=SSL authentication login, cookie information needs to include the hexadecimal certificate serial number
kl.npki.management.core.i18n_unable_to_find_the_corresponding_ssl_client_certificate_please_check_the_id=Unable to find the corresponding SSL client certificate, please check the ID: {0} and ensure the corresponding certificate exists
kl.npki.management.core.i18n_get_engine_info_is_empty=Failed to get engine information, result is empty
kl.npki.management.core.i18n_failed_to_read_file_content=Failed to read file [{0}] content
kl.npki.management.core.i18n_log_path_not_found=Log path not found
kl.npki.management.core.i18n_failed_to_perform_data_cleaning_operation_after_filtering_resource_permissions=Failed to perform data cleaning operation after filtering resource permissions

kl.npki.management.core.i18n_failed_to_obtain_administrator_information=Failed to obtain administrator information
kl.npki.management.core.i18n_partial_administrators_failed_to_issue_status_checks=Partial administrators failed to issue status checks
kl.npki.management.core.i18n_detection_failed=Detection failed: {0}
kl.npki.management.core.i18n_failed_to_obtain=Failed to obtain {0}
kl.npki.management.core.i18n_not_found=Not found {0}
kl.npki.management.core.i18n_insufficient_permissions_please_log_in_to_the_system_as_an_administrator_with_the_following_roles=Insufficient permissions, please log in to the system as an administrator with the following roles
kl.npki.management.core.i18n_total_number_of_tasks_and_number_of_enabled_tasks=Total number of tasks: {0}, number of enabled tasks: {1}
kl.npki.management.core.i18n_is_it_enabled=Is it enabled
kl.npki.management.core.i18n_timed_task_status=Timed task status
kl.npki.management.core.i18n_running=Running
kl.npki.management.core.i18n_stopped=Stopped
kl.npki.management.core.i18n_last_execution_time=Last execution time
kl.npki.management.core.i18n_unexecuted=Unexecuted
kl.npki.management.core.i18n_certificate_validity_period_remaining=Certificate validity period remaining {0} days
kl.npki.management.core.i18n_certificate_has_expired=Certificate has expired {0} days
kl.npki.management.core.i18n_there_are_certificates_that_are_about_to_expire=There are certificates that are about to expire in {0} days
kl.npki.management.core.i18n_key_generation=Key Generation 
kl.npki.management.core.i18n_asymmetric_encryption_and_decryption=Asymmetric Encryption And Decryption 
kl.npki.management.core.i18n_asymmetric_signature_verification=Asymmetric Signature Verification 
kl.npki.management.core.i18n_symmetric_encryption_and_decryption=Symmetric Encryption And Decryption 
kl.npki.management.core.i18n_password_machine_service=HSM Service
kl.npki.management.core.i18n_password_machine_type=HSM Type
kl.npki.management.core.i18n_hash=Hash 
kl.npki.management.core.i18n_testing=Test
kl.npki.management.core.i18n_normal=Normal
kl.npki.management.core.i18n_abnormal=Abnormal
kl.npki.management.core.i18n_system_administrator_certificate=System administrator certificate name
kl.npki.management.core.i18n_identity_certificate=Identity certificate name
kl.npki.management.core.i18n_manage_root_certificates=Manage root certificate name
kl.npki.management.core.i18n_ssl_site_certificate=SSL site certificate name
kl.npki.management.core.i18n_certificate_is_valid=Certificate is valid
kl.npki.management.core.i18n_the_certificate_has_expired=The certificate has expired
kl.npki.management.core.i18n_issued=Certificate issued
kl.npki.management.core.i18n_not_issued=Not issued
kl.npki.management.core.i18n_there_are_unassigned_administrator_roles=There are unassigned administrator roles
kl.npki.management.core.i18n_all_administrators_have_issued_correctly=All administrators have been issued correctly
kl.npki.management.core.i18n_sign_site_certificate=SSL site certificate_{0}.crt
kl.npki.management.core.i18n_encryption_site_certificate=Encryption site certificate.crt
kl.npki.management.core.i18n_encryption_certificate_key=Encryption certificate key.key
kl.npki.management.core.i18n_certificate_chain=Certificate chain_{0}.crt
kl.npki.management.core.i18n_please_initialize_the_auditor=Please initialize the auditor
kl.npki.management.core.i18n_please_initialize_the_operator=Please initialize the operator
kl.npki.management.core.i18n_ip_address=IP address: {0}
kl.npki.management.core.i18n_port=Port: {0}
kl.npki.management.core.i18n_db_name=Database name: {0}
kl.npki.management.core.i18n_username=Username: {0}

# DbConfigInfo
kl.npki.management.core.i18n_the_database_address_cannot_be_empty=The database address cannot be empty
kl.npki.management.core.i18n_the_length_of_the_database_address_cannot_exceed_128_characters=The length of the database address cannot exceed 128 characters
kl.npki.management.core.i18n_the_database_address_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=The database address cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'
kl.npki.management.core.i18n_the_database_name_cannot_be_empty=The database name cannot be empty
kl.npki.management.core.i18n_the_length_of_the_database_name_cannot_exceed_128_characters=The length of the database name cannot exceed 128 characters
kl.npki.management.core.i18n_the_database_name_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=The database name cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'
kl.npki.management.core.i18n_the_database_username_cannot_be_empty=The database username cannot be empty
kl.npki.management.core.i18n_the_length_of_the_database_username_cannot_exceed_128_characters=The length of the database username cannot exceed 128 characters
kl.npki.management.core.i18n_the_database_username_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=The database username cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'
kl.npki.management.core.i18n_the_database_password_cannot_be_empty=The database password cannot be empty
kl.npki.management.core.i18n_the_length_of_the_database_password_cannot_exceed_128_characters=The length of the database password cannot exceed 128 characters
kl.npki.management.core.i18n_the_database_password_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=The database password cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'
kl.npki.management.core.i18n_the_database_character_set_cannot_be_empty=The database character set cannot be empty
kl.npki.management.core.i18n_the_length_of_the_database_character_set_cannot_exceed_128_characters=The length of the database character set cannot exceed 128 characters
kl.npki.management.core.i18n_the_database_character_set_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=The database character set cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'
# Db Service Check Related
kl.npki.management.core.i18n_database_type=Database Type
kl.npki.management.core.i18n_db_connection=Database Connection 
kl.npki.management.core.i18n_database_service=Database Service 
kl.npki.management.core.i18n_db_query=Database Query 
kl.npki.management.core.i18n_database_version=Database Version
# Disk Space Check Related
kl.npki.management.core.i18n_disk_space=Disk Space 
kl.npki.management.core.i18n_disk_total=Total Space 
kl.npki.management.core.i18n_disk_free=Free Space 
kl.npki.management.core.i18n_disk_used=Used Space 
kl.npki.management.core.i18n_disk_usage=Usage 
kl.npki.management.core.i18n_disk_path=Disk Path 
kl.npki.management.core.i18n_disk_insufficient_template=Insufficient disk space, current usage %s, exceeds threshold %d%%
kl.npki.management.core.i18n_unknown=Unknown
# JVM Memory Check Related
kl.npki.management.core.i18n_jvm_memory=JVM Memory 
kl.npki.management.core.i18n_heap_memory=Heap Memory 
kl.npki.management.core.i18n_non_heap_memory=Non-Heap Memory 
kl.npki.management.core.i18n_memory_used=Used Memory 
kl.npki.management.core.i18n_memory_max=Maximum Memory 
kl.npki.management.core.i18n_memory_usage=Usage 
kl.npki.management.core.i18n_memory_insufficient_template=JVM memory insufficient, current usage %s, exceeds threshold %d%%
kl.npki.management.core.i18n_no_physical_datasource=No physical data source found
kl.npki.management.core.i18n_connection_closed=Connection is closed
kl.npki.management.core.i18n_cannot_establish_connection=Cannot establish connection

# ResourceType
kl.npki.management.core.i18n_page=Page
kl.npki.management.core.i18n_menu=Menu
kl.npki.management.core.i18n_button=Button
kl.npki.management.core.i18n_api=API

#LogConfigService
kl.npki.management.core.i18n_must_not_be_less_than_1gb=Must not be less than 1GB
kl.npki.management.core.i18n_cannot_exceed_1tb=Cannot exceed 1TB