# ManagementInternalError
kl.npki.base.management.i18n_system_error=System exception
kl.npki.base.management.i18n_db_insert_error=Failed to insert data to db!
kl.npki.base.management.i18n_db_delete_error=Failed to delete data from db!
kl.npki.base.management.i18n_db_update_error=Failed to update data in db!
kl.npki.base.management.i18n_db_query_error=Failed to query data from db!
kl.npki.base.management.i18n_verify_signed_error=Failed to verify sign data!
kl.npki.base.management.i18n_sign_key_not_found=Signing key does not exist!
kl.npki.base.management.i18n_license_parse_error=License parsing failed!
kl.npki.base.management.i18n_file_download_fail=File download failed!
kl.npki.base.management.i18n_file_create_fail=File creation failed!
kl.npki.base.management.i18n_verify_full_data_hash_error=Failed to verify data integrity!
kl.npki.base.management.i18n_cert_preview_error=Certificate preview failed!
kl.npki.base.management.i18n_cert_encode_error=Certificate ASN structure encoding failed!

# ManagementRemoteError
kl.npki.base.management.i18n_remote_service_error=Remote service error!

# ManagementValidationError
kl.npki.base.management.i18n_param_error=Parameter error!
kl.npki.base.management.i18n_biz_status_error=Business status error!
kl.npki.base.management.i18n_admin_not_exists_error=Invalid administrator!
kl.npki.base.management.i18n_role_not_exists=Role does not exist!
kl.npki.base.management.i18n_role_not_exists_by_code=Role not found by role code!
kl.npki.base.management.i18n_admin_info_error=Administrator information is abnormal, please log in again
kl.npki.base.management.i18n_sys_role_not_remove=System roles cannot be deleted!
kl.npki.base.management.i18n_auth_ref_id_error=Authentication ID cannot be empty！
kl.npki.base.management.i18n_invalid_auth_type=Invalid authentication type!
kl.npki.base.management.i18n_invalid_cookie_info=Invalid cookie information!
kl.npki.base.management.i18n_ssl_login_fail=SSL login authentication failed!
kl.npki.base.management.i18n_admin_username_or_pwd_error=Administrator username or password error!
kl.npki.base.management.i18n_invalid_cert_sn=Invalid certificate serial number!
kl.npki.base.management.i18n_admin_role_not_exists=Administrator role does not exist!
kl.npki.base.management.i18n_other_admin_role_inconsistent=Other administrator roles are inconsistent with the main login administrator role!
kl.npki.base.management.i18n_login_status_expired=The user is not logged in or the sign-in status has expired!
kl.npki.base.management.i18n_login_status_has_been_replaced=This account has been logged in elsewhere, you have been forced offline. If it was not you, please log in again and modify your login information in time!
kl.npki.base.management.i18n_login_account_has_been_locked=Login account has been locked!
kl.npki.base.management.i18n_other_admin_group_inconsistent=The backup management and initialization administrators are not in the same group!
kl.npki.base.management.i18n_login_status_ip_error=The IP address in the login information is incorrect, please log in again!
kl.npki.base.management.i18n_kcsp_sso_fail=KCSP SSO failed!
kl.npki.base.management.i18n_unsupported_db_type=Unsupported database type!
kl.npki.base.management.i18n_no_permission=No permission!
kl.npki.base.management.i18n_role_code_is_not_numeric=Role code must be numeric!
kl.npki.base.management.i18n_asym_algo_name_repeat_error=Asymmetric algorithm name repeat!
kl.npki.base.management.i18n_resource_code_already_exists=Resource code already exists!
kl.npki.base.management.i18n_query_backup_admin_error=This role does not support viewing the list of backup administrators!
kl.npki.base.management.i18n_config_file_verify_hmac_failed=Failed to verify config file data integrity!
kl.npki.base.management.i18n_demo_init_error=System has been deployed!
kl.npki.base.management.i18n_captcha_verify_fail=Captcha wrong!
kl.npki.base.management.i18n_administrator_certificate_does_not_exist=The administrator certificate does not exist!
kl.npki.base.management.i18n_admin_is_cancel=Administrator account has been cancelled!
kl.npki.base.management.i18n_management_root_cert_not_exist=The management root certificate does not exist
kl.npki.base.management.i18n_login_method_has_been_modified=Login method has been modified!
kl.npki.base.management.i18n_please_login_to_system_again=Please login to system again
kl.npki.base.management.i18n_org_not_found=The organization does not exist
kl.npki.base.management.i18n_upload_file_name_error=Unable to get file name
kl.npki.base.management.i18n_upload_file_empty_error=The content of the uploaded file is empty
kl.npki.base.management.i18n_upload_file_not_found_error=The file does not exist
kl.npki.base.management.i18n_upload_file_exist_unfinished_error=The file upload operation is not complete
kl.npki.base.management.i18n_upload_file_interface_not_exist_error=There is no file upload operation for this service
kl.npki.base.management.upload_file_finished_error=The uploaded file has been processed
kl.npki.base.management.upload_file_processing_error=The upload file is being processed

# AdminMgrController
kl.npki.base.management.i18n_registration_administrator=Registration Administrator
kl.npki.base.management.i18n_query_the_list_of_operable_roles=Query the list of operable roles
kl.npki.base.management.i18n_import_administrator_certificate=Import administrator certificate
kl.npki.base.management.i18n_import_extended_administrator_certificate=Import Extended Administrator Certificate
kl.npki.base.management.i18n_request_to_revoke_certificate=Request to revoke certificate
kl.npki.base.management.i18n_abolish_certificate=Abolish certificate
kl.npki.base.management.i18n_abolish_super_administrators=Abolish top administrators
kl.npki.base.management.i18n_revocation_of_certificate_revocation_application=Revocation of Certificate Revocation Application
kl.npki.base.management.i18n_query_the_list_of_system_administrators=Query the list of system administrators
kl.npki.base.management.i18n_query_the_list_of_backup_administrators=Query the list of backup administrators
kl.npki.base.management.i18n_system_administrator_information_details=System administrator information details
kl.npki.base.management.i18n_system_administrator_certificate_sn=System administrator certificate SN
kl.npki.base.management.i18n_apply_for_cancellation_of_administrator=Apply for cancellation of administrator
kl.npki.base.management.i18n_cancel_administrator=Cancel Administrator
kl.npki.base.management.i18n_apply_for_extension_administrator=Apply for Extension Administrator
kl.npki.base.management.i18n_import_certificate_chain=Import certificate chain
kl.npki.base.management.i18n_during_deployment_retrieve_the_built_in_administrator_list=During deployment, retrieve the built in administrator list
kl.npki.base.management.i18n_change_password=Change Password
kl.npki.base.management.i18n_unlock_the_account=Unlock the account
kl.npki.base.management.get_admin_user_status=Query Admin User Status
kl.npki.base.management.get_admin_cert_status=Query Admin Cert Status

# AuditController
kl.npki.base.management.i18n_audit_log_list_query=Audit log list query
kl.npki.base.management.i18n_log_audit=Log Audit
kl.npki.base.management.i18n_audit_log_details_query=Audit log details query

# SecurityMgrController
kl.npki.base.management.i18n_pending_review_list=Pending Review List
kl.npki.base.management.i18n_approved_by_review=Approved by review
kl.npki.base.management.i18n_review_rejected=Review Rejected
kl.npki.base.management.i18n_view_audit_details=View audit details

# AuthenticateController
kl.npki.base.management.i18n_retrieve_the_original_login_signature_data=Retrieve the original login signature data
kl.npki.base.management.i18n_obtain_the_required_number_of_administrators_for_login=Obtain the required number of administrators for login
kl.npki.base.management.i18n_obtain_the_serial_number_of_the_administrator_signature_certificate=Obtain the serial number of the administrator signature certificate
kl.npki.base.management.i18n_verify_ssl_channel=Verify SSL channel
kl.npki.base.management.i18n_administrator_login=Administrator login
kl.npki.base.management.i18n_log_out_of_login=Log out of login
kl.npki.base.management.i18n_sso_auth_addr=Single sign-on authentication platform information
kl.npki.base.management.i18n_annotation.get_captcha=Get captcha image Base64 encoding

# SystemBackupRestoreController
kl.npki.base.management.i18n_configure_backup=Configure backup
kl.npki.base.management.i18n_configure_recovery=Configure recovery

# CertController
kl.npki.base.management.i18n_certificate_preview_analysis=Certificate Preview Analysis
kl.npki.base.management.i18n_parse_certificate=Parse Certificate

# IdCertController
kl.npki.base.management.i18n_self_issued_identity_certificate=Self issued identity certificate
kl.npki.base.management.i18n_update_identity_certificate=Update identity certificate
kl.npki.base.management.i18n_obtain_identity_certificate=Obtain identity certificate

# SslServerCertController
kl.npki.base.management.i18n_self_issued_site_certificate=Self issued site certificate
kl.npki.base.management.i18n_obtain_the_key_list_for_issuing_site_certificates=Obtain the key list for issuing site certificates
kl.npki.base.management.i18n_view_detailed_information_of_server_site_certificate=View detailed information of server site certificate
kl.npki.base.management.i18n_view_detailed_information_of_server_id_certificate_info=View identity certificate request information
kl.npki.base.management.i18n_view_detailed_information_of_server_ssl_certificate_info=View SSL certificate request information
kl.npki.base.management.i18n_view_detailed_information_of_server_root_certificate_info=View root certificate request information

# TrustCertController
kl.npki.base.management.i18n_query_management_root_certificate=Query management root certificate
kl.npki.base.management.i18n_query_management_root_certificate_algorithm=Query management root certificate algorithm
kl.npki.base.management.i18n_self_signed_hair_root_certificate=Self signed hair root certificate
kl.npki.base.management.i18n_re_sign_the_hair_root_certificate=Re sign the hair root certificate
kl.npki.base.management.i18n_import_trusted_certificate=Import trusted certificate
kl.npki.base.management.i18n_delete_trusted_certificate_chain=Delete trusted certificate chain
kl.npki.base.management.i18n_import_management_root_certificate=Import management root certificate
kl.npki.base.management.i18n_update_import_management_root_certificate=Update import management root certificate
kl.npki.base.management.i18n_generate_certificate_request=Generate certificate request
kl.npki.base.management.i18n_update_certificate_request=Update certificate request
kl.npki.base.management.i18n_export_certificate_request=Export certificate request
kl.npki.base.management.i18n_export_certificate=Export certificate
kl.npki.base.management.i18n_query_the_list_of_trusted_certificates=Query the list of trusted certificates

# CacheConfigController
kl.npki.base.management.i18n_query_cache_configuration=Query cache configuration
kl.npki.base.management.i18n_save_cache_configuration=Save cache configuration
kl.npki.base.management.i18n_test_cache_connection=TestApi cache connection

# CryptoAlgoConfigController
kl.npki.base.management.i18n_query_the_password_algorithm_configuration_supported_by_the_password_machine=Query the password algorithm configuration supported by the password machine
kl.npki.base.management.i18n_query_the_password_algorithm_configuration_of_the_system_configuration=Query the password algorithm configuration of the system configuration
kl.npki.base.management.i18n_modify_password_algorithm_configuration=Modify password algorithm configuration

# DbConfigController
kl.npki.base.management.i18n_query_database_configuration=Query database configuration
kl.npki.base.management.i18n_save_database_configuration=Save database configuration
kl.npki.base.management.i18n_save_database_passwd_configuration=Save database passwd configuration
kl.npki.base.management.i18n_test_database_connection=Test database connection
kl.npki.base.management.i18n_query_configuration_status=Query configuration status

# EngineConfigController
kl.npki.base.management.i18n_query_encryption_machine_configuration=Query encryption machine configuration
kl.npki.base.management.i18n_save_encryption_machine_configuration=Save encryption machine configuration
kl.npki.base.management.i18n_query_the_list_of_encryption_machine_types=Query the list of encryption machine types
kl.npki.base.management.i18n_test_encryption_machine_connection=Test encryption machine connection
kl.npki.base.management.i18n_query_the_list_of_asymmetric_key_types_supported_by_the_current_configured_encryption_machine=Query the list of asymmetric key types supported by the current configured encryption machine
kl.npki.base.management.i18n_query_the_list_of_anti_quantum_encryption_key_types_supported_by_the_current_configured_encryption_machine=Query the list of anti quantum encryption key types supported by the current configured encryption machine
kl.npki.base.management.i18n_query_the_list_of_supported_asymmetric_key_types_based_on_the_encryption_machine_type=Query the list of supported asymmetric key types based on the encryption machine type
kl.npki.base.management.i18n_query_key_index_list=Query key index list
kl.npki.base.management.i18n_check_if_the_configuration_is_complete=Check if the configuration is complete

# LogConfigController
kl.npki.base.management.i18n_query_log_configuration=Query log configuration
kl.npki.base.management.i18n_save_log_configuration=Save log configuration
kl.npki.base.management.i18n_get_log_output_type=Get log output type
kl.npki.base.management.i18n_get_log_level_type_dropdown_options=Get log level type dropdown options

# LoginTypeConfigController
kl.npki.base.management.i18n_save_login_mode=Save login mode
kl.npki.base.management.i18n_get_login_mode=Get login mode
kl.npki.base.management.i18n_get_login_mode_enumeration_list=Get login mode enumeration list

# PortConfigController
kl.npki.base.management.i18n_get_port_configuration=Get port configuration
kl.npki.base.management.i18n_modify_port_configuration=Modify port configuration

# SystemConfigController
kl.npki.base.management.i18n_obtain_system_configuration_template=Obtain system configuration template
kl.npki.base.management.i18n_query_the_current_environment_of_the_system=Query the current environment of the system
kl.npki.base.management.i18n_complete_system_initialization=Complete system initialization
kl.npki.base.management.i18n_demo_environment_initialization=Demo environment initialization
kl.npki.base.management.i18n_query_the_overall_configuration_status_and_progress_of_the_system=Query the overall configuration status and progress of the system
kl.npki.base.management.i18n_query_system_general_configuration=Query system general configuration
kl.npki.base.management.i18n_save_system_general_configuration=Save system general configuration

# BaseHomePageController
kl.npki.base.management.i18n_service_self_inspection_statistics_results=Service self inspection statistics results

# LicenseController
kl.npki.base.management.i18n_import_license=Import License
kl.npki.base.management.i18n_query_license=Query License
kl.npki.base.management.i18n_parse_license=Parse License

# ApiLogController
kl.npki.base.management.i18n_query_service_log_list=Query service log list
kl.npki.base.management.i18n_query_detailed_information_of_service_logs=Query detailed information of service logs
kl.npki.base.management.i18n_query_excel_electronic_signature_information=Query Excel electronic signature information
kl.npki.base.management.i18n_export_signed_file_of_service_logs_i18n_key=Export signed logs
kl.npki.base.management.i18n_export_file_of_service_logs_i18n_key=Export Log
kl.npki.base.management.i18n_export_file_of_view_logs_i18n_key=View log file
kl.npki.base.management.i18n_export_file_of_delete_logs_i18n_key=Delete log file
kl.npki.base.management.i18n_export_file_of_download_logs_i18n_key=Download log file
kl.npki.base.management.i18n_init=init


# OpLogController
kl.npki.base.management.i18n_query_operation_log_list=Query operation log list
kl.npki.base.management.i18n_query_operation_log_detailed_information=Query operation log detailed information

# RunLogController
kl.npki.base.management.i18n_view_the_list_of_running_logs=View the list of running logs
kl.npki.base.management.i18n_view_operation_logs=View operation logs
kl.npki.base.management.i18n_download_running_logs=Download running logs

# RoleController
kl.npki.base.management.i18n_add_new_role=Add new role
kl.npki.base.management.i18n_disable_role=Disable role
kl.npki.base.management.i18n_role_list_query=Role List Query
kl.npki.base.management.i18n_initialize_role_permission_tree=Initialize role permission tree
kl.npki.base.management.i18n_save_role_permission_tree=Save role permission tree
kl.npki.base.management.i18n_get_role_unified_resources=Get role unified resources
kl.npki.base.management.i18n_update_role_unified_resources=Update role unified resources

# Role Cache Related
kl.npki.base.management.i18n_role_cache_clear_success=Role cache cleared successfully
kl.npki.base.management.i18n_role_cache_clear_failed=Failed to clear role cache
kl.npki.base.management.i18n_role_cache_put_success=Role cache updated successfully
kl.npki.base.management.i18n_role_cache_put_failed=Failed to update role cache
kl.npki.base.management.i18n_role_cache_get_success=Role cache retrieved successfully
kl.npki.base.management.i18n_role_cache_get_failed=Failed to retrieve role cache
kl.npki.base.management.i18n_role_cache_not_found=Role cache not found
kl.npki.base.management.i18n_role_cache_expired=Role cache has expired

# SelfCheckController
kl.npki.base.management.i18n_execute_all_service_self_check_items=Execute all service self check items
kl.npki.base.management.i18n_execute_specified_service_self_test_items=Execute specified service self-test items
kl.npki.base.management.i18n_query_all_self_checking_data=Query all self checking data
kl.npki.base.management.i18n_query_alarm_self_test_data=Query alarm self-test data

# SysHelpController
kl.npki.base.management.i18n_obtain_operation_guidance_information=Obtain operation guidance information
kl.npki.base.management.i18n_modify_operation_guidance_information=Modify operation guidance information

# SysInfoController
kl.npki.base.management.i18n_obtain_system_information_related_configurations=Obtain system information related configurations
kl.npki.base.management.i18n_update_system_information_configuration=Update system information configuration
kl.npki.base.management.i18n_check_if_the_user_agrees_to_use_the_license_agreement=Check if the user agrees to use the license agreement
kl.npki.base.management.i18n_the_user_agrees_to_use_the_license_agreement=The user agrees to use the license agreement
kl.npki.base.management.i18n_current_language_query_in_the_system=Current language query in the system
kl.npki.base.management.i18n_the_system_supports_language_list_query=The system supports language list query
kl.npki.base.management.i18n_system_language_settings=System language settings
kl.npki.base.management.i18n_system_date_time_settings=System date formatting settings

# InspectionController
kl.npki.base.management.i18n_execute_inspection=执行系统巡检
kl.npki.base.management.i18n_query_inspection_record_list=查询巡检列表
kl.npki.base.management.i18n_query_inspection_record_details=查询巡检结果详情
kl.npki.base.management.i18n_delete_inspection_record=删除巡检记录
kl.npki.base.management.i18n_export_inspection_record=导出巡检报告

# OrgManagementController
kl.npki.base.management.i18n_annotation.query_institution_information_list=Query institution information list
kl.npki.base.management.i18n_annotation.query_institution_information_tree=Query institution information tree
kl.npki.base.management.i18n_annotation.search_all_institutions=Search all institutions
kl.npki.base.management.i18n_annotation.query_the_root_institution=Query the root institution
kl.npki.base.management.i18n_annotation.query_sub_institutions=Query sub institutions
kl.npki.base.management.i18n_annotation.query_information_details=Query information details
kl.npki.base.management.i18n_annotation.query_information_details_based_on_institutional_code=Query information details based on institutional code
kl.npki.base.management.i18n_annotation.newly_added_institutions=Newly added institutions
kl.npki.base.management.i18n_annotation.institutional_update=Institutional update
kl.npki.base.management.i18n_annotation.abolish_institutions=Abolish institutions
kl.npki.base.management.i18n_annotation.delete_institution=Delete Institution
kl.npki.base.management.i18n_annotation.batch_deletion_of_institutions=Batch deletion of institutions
kl.npki.base.management.i18n_annotation.batch_export_of_institutions=Batch export of institutions
kl.npki.base.management.i18n_annotation.obtain_the_historical_trajectory_of_the_institution=Obtain the historical trajectory of the institution

# UploadFileController
kl.npki.base.management.i18n_annotation.upload_files=Upload files
kl.npki.base.management.i18n_annotation.process_uploaded_files=Process uploaded files
kl.npki.base.management.i18n_annotation.delete_uploaded_files=Delete uploaded files
kl.npki.base.management.i18n_annotation.check_if_there_are_any_unfinished_files_in_the_current_interface=Check if there are any unfinished files in the current interface
kl.npki.base.management.i18n_annotation.file_upload_list=File upload list
kl.npki.base.management.i18n_annotation.download_source_files=Download source files
kl.npki.base.management.i18n_annotation.download_result_file=Download result file
kl.npki.base.management.i18n_annotation.download_template=Download Template
kl.npki.base.management.i18n_annotation.all_support_batch_operation_interface_names=All support batch operation interface names

# OrgAddRequest
kl.npki.base.management.i18n_annotation.institution_name=Institution Name
kl.npki.base.management.i18n_annotation.the_length_of_the_institution_name_cannot_exceed_256=The length of the institution name cannot exceed 256 characters
kl.npki.base.management.i18n_annotation.the_length_of_the_full_name_of_the_institution_cannot_exceed_256=The length of the full name of the institution cannot exceed 256 characters
kl.npki.base.management.i18n_annotation.the_pinyin_of_the_institution_name_cannot_exceed_256=The pinyin of the institution name cannot exceed 256 characters
kl.npki.base.management.i18n_annotation.institution_code=Institution code
kl.npki.base.management.i18n_annotation.institution_code_cannot_exceed_256=Institution code cannot exceed 256 characters
kl.npki.base.management.i18n_annotation.superior_institution_code=Superior institution code
kl.npki.base.management.i18n_annotation.the_superior_organization_code_cannot_exceed_256=The superior organization code cannot exceed 256 characters
kl.npki.base.management.i18n_annotation.contact_person_cannot_exceed_256=Contact person cannot exceed 256 characters
kl.npki.base.management.i18n_annotation.the_contact_phone_number_cannot_exceed_256=The contact phone number cannot exceed 256 characters
kl.npki.base.management.i18n_annotation.the_postal_code_cannot_exceed_256=The postal code cannot exceed 256 characters
kl.npki.base.management.i18n_annotation.the_contact_address_cannot_exceed_256=The contact address cannot exceed 256 characters
kl.npki.base.management.i18n_annotation.institution_description_cannot_exceed_256=Institution description cannot exceed 256 characters

# OrgUpdateRequest
kl.npki.base.management.i18n_annotation.the_complete_institutional_code_cannot_exceed_256=The complete institutional code cannot exceed 256 characters
kl.npki.base.management.i18n_annotation.the_institutional_level_cannot_be_less_than_0=The institutional level cannot be less than 0


# AdminUpdatePwdRequest
kl.npki.base.management.i18n_administrator_id_cannot_be_empty=Administrator ID cannot be empty
kl.npki.base.management.i18n_old_password_cannot_be_empty=Old password cannot be empty
kl.npki.base.management.i18n_the_length_of_the_old_password_digest_cannot_exceed_128_characters=The length of the old password digest cannot exceed 128 characters
kl.npki.base.management.i18n_old_password_digest_cannot_contain_spaces_20_starting_or_ending_with_0a_or_00=Old password digest cannot contain spaces,% 20. % Starting or ending with '0a' or '% 00'
kl.npki.base.management.i18n_the_new_password_cannot_be_empty=The new password cannot be empty
kl.npki.base.management.i18n_new_password_digest_format_error=New password digest format error

# ExtendAdminCertRequest
kl.npki.base.management.i18n_the_administrator_id_should_not_be_empty=The administrator ID should not be empty
kl.npki.base.management.i18n_the_certificate_extension_period_should_not_be_less_than_1_day=The certificate extension period should not be less than 1 day

# ImportAdminCertRequest
kl.npki.base.management.i18n_role_information_cannot_be_empty=Role information cannot be empty
kl.npki.base.management.i18n_the_length_of_the_character_code_cannot_exceed_128_characters=The length of the character code cannot exceed 128 characters
kl.npki.base.management.i18n_character_encoding_cannot_use_spaces_or_20_starting_or_ending_with_0a_or_00=Character encoding cannot use spaces or% 20. % Starting or ending with '0a' or '% 00'
kl.npki.base.management.i18n_password_information_cannot_be_empty=Password information cannot be empty
kl.npki.base.management.i18n_password_hash_length_cannot_exceed_128_characters=Password hash length cannot exceed 128 characters
kl.npki.base.management.i18n_password_hash_cannot_use_spaces_20_starting_or_ending_with_0a_or_00=Password hash cannot use spaces,% 20. % Starting or ending with '0a' or '% 00'
kl.npki.base.management.i18n_the_value_of_the_signature_certificate_cannot_be_empty=The value of the signature certificate cannot be empty
kl.npki.base.management.i18n_the_length_of_the_signature_certificate_value_cannot_exceed_4096_characters=The length of the signature certificate value cannot exceed 4096 characters
kl.npki.base.management.i18n_the_certificate_value_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=The certificate value cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'
kl.npki.base.management.i18n_the_length_of_the_encryption_certificate_value_cannot_exceed_4096_characters=The length of the encryption certificate value cannot exceed 4096 characters
kl.npki.base.management.i18n_the_encryption_certificate_value_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=The encryption certificate value cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'

# ImportExtendAdminCertRequest

# IssueAdminCertForDeployRequest
kl.npki.base.management.i18n_certificate_issuance_request_should_not_be_empty=Certificate issuance request should not be empty

# IssueAdminCertRequest
kl.npki.base.management.i18n_user_id_should_not_be_empty=User ID should not be empty

# LoginRequest
kl.npki.base.management.i18n_authentication_type_cannot_be_empty=Authentication type cannot be empty
kl.npki.base.management.i18n_the_authentication_type_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=The authentication type cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'

# RegisterAdminRequest
kl.npki.base.management.i18n_role_information_should_not_be_empty=Role information should not be empty
kl.npki.base.management.i18n_the_length_of_character_information_cannot_exceed_128_characters=The length of character information cannot exceed 128 characters
kl.npki.base.management.i18n_administrator_name_should_not_be_empty=Administrator name should not be empty
kl.npki.base.management.i18n_the_length_of_the_administrator_name_cannot_exceed_128_characters=The length of the administrator name cannot exceed 128 characters
kl.npki.base.management.i18n_the_name_of_the_corner_administrator_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=The name of the corner administrator cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'
kl.npki.base.management.i18n_password_cannot_be_empty=Password cannot be empty
kl.npki.base.management.i18n_password_digest_format_error=Password digest format error
kl.npki.base.management.i18n_the_data_length_of_organizational_information_cannot_exceed_128_characters=The data length of organizational information cannot exceed 128 characters
kl.npki.base.management.i18n_the_data_length_of_institutional_information_cannot_exceed_128_characters=The data length of institutional information cannot exceed 128 characters
kl.npki.base.management.i18n_the_data_length_of_province_information_cannot_exceed_128_characters=The data length of province information cannot exceed 128 characters
kl.npki.base.management.i18n_the_data_length_of_city_information_cannot_exceed_128_characters=The data length of city information cannot exceed 128 characters
kl.npki.base.management.i18n_email_error=Email error

kl.npki.base.management.i18n_parsing_type_accept_values=Parsing Type only accepts [pubKey]

# UpdateLoginTypeRequest
kl.npki.base.management.i18n_login_mode_cannot_be_empty=Login mode cannot be empty

# AlgoConfigRequest
kl.npki.base.management.i18n_symmetric_key_algorithm_cannot_be_empty=Symmetric key algorithm cannot be empty
kl.npki.base.management.i18n_symmetric_key_algorithm_cannot_exceed_128_characters=Symmetric key algorithm cannot exceed 128 characters
kl.npki.base.management.i18n_symmetric_key_algorithm_cannot_use_spaces_20_starting_or_ending_with_0a_or_00=Symmetric key algorithm cannot use spaces,% 20. % Starting or ending with '0a' or '% 00'

# CacheConfigRequest
kl.npki.base.management.i18n_cache_type_cannot_be_empty=Cache type cannot be empty
kl.npki.base.management.i18n_the_length_of_the_database_type_cannot_exceed_20_characters=The length of the database type cannot exceed 20 characters
kl.npki.base.management.i18n_cache_operation_mode_cannot_be_empty=Cache operation mode cannot be empty
kl.npki.base.management.i18n_cache_operation_mode_cannot_exceed_20_characters=Cache operation mode cannot exceed 20 characters
kl.npki.base.management.i18n_cache_operation_mode_cannot_use_spaces_20_starting_or_ending_with_0a_or_00=Cache operation mode cannot use spaces,% 20. % Starting or ending with '0a' or '% 00'
kl.npki.base.management.i18n_ssl_switch_cannot_be_empty=SSL switch cannot be empty
kl.npki.base.management.i18n_redis_node_configuration_cannot_be_empty=Redis node configuration cannot be empty

# CertSignRequest
kl.npki.base.management.i18n_the_certificate_universal_name_cannot_be_empty=The certificate universal name cannot be empty!
kl.npki.base.management.i18n_the_length_of_the_certificate_generic_name_cannot_exceed_32_characters=The length of the certificate generic name cannot exceed 32 characters
kl.npki.base.management.i18n_the_certificate_common_name=The common name of the certificate
kl.npki.base.management.i18n_the_data_length_of_unit_information_cannot_exceed_128_characters=The data length of unit information cannot exceed 128 characters
kl.npki.base.management.i18n_the_validity_period_of_the_certificate_cannot_be_empty=The validity period of the certificate cannot be empty
kl.npki.base.management.i18n_the_length_of_the_key_type_cannot_exceed_128_characters=The length of the key type cannot exceed 128 characters
kl.npki.base.management.i18n_the_key_type_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=The key type cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'
kl.npki.base.management.i18n_the_encryption_key_type_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=The encryption key type cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'
kl.npki.base.management.i18n_the_keystore_password_cannot_be_greater_than_128_characters=The keyStore password cannot be greater than 128 characters
kl.npki.base.management.i18n_the_unit_name=The unit name
kl.npki.base.management.i18n_the_province_name=The province_name
kl.npki.base.management.i18n_the_city_name=The city name
kl.npki.base.management.i18n_the_organization_name=The organization name
kl.npki.base.management.i18n_the_admin_name=The admin name


# ExportCertRequest
kl.npki.base.management.i18n_the_certificate_export_format=Certificate export format can only be DER, PEM, P7B_NO_CHAIN, P7B_WITH_CHAIN

# ExportKeyRequest
kl.npki.base.management.i18n_the_key_export_format=Key export format can only be PFX, GMT0009_2012, GMT0009_2023, GMT0016_2023, P8

# ImportCertChainRequest
kl.npki.base.management.i18n_certificate_cannot_be_empty=Certificate cannot be empty

# ImportTrustCertRequest
kl.npki.base.management.i18n_trusted_certificate_cannot_be_empty=Trusted certificate cannot be empty

# DbConfigRequest
kl.npki.base.management.i18n_the_database_type_cannot_be_empty=The database type cannot be empty
kl.npki.base.management.i18n_the_length_of_the_database_type_cannot_exceed_128_characters=The length of the database type cannot exceed 128 characters
kl.npki.base.management.i18n_the_database_type_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=The database type cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'
kl.npki.base.management.i18n_the_maximum_number_of_connections_cannot_be_empty=The maximum number of connections cannot be empty
kl.npki.base.management.i18n_whether_to_enable_readwrite_separation_configuration_cannot_be_empty=Whether to enable read-write separation configuration cannot be empty

# DbConfigResponse

# AsymKeyIndexInfo
kl.npki.base.management.i18n_asymmetric_algorithm_name_cannot_be_empty=Asymmetric algorithm name cannot be empty

# EngineConfigRequest
kl.npki.base.management.i18n_the_encryption_machine_type_cannot_be_empty=The encryption machine type cannot be empty
kl.npki.base.management.i18n_the_length_of_the_encryption_machine_type_cannot_exceed_128_characters=The length of the encryption machine type cannot exceed 128 characters
kl.npki.base.management.i18n_encryption_machine_type_cannot_use_spaces_20_starting_or_ending_with_0a_or_00=Encryption machine type cannot use spaces,% 20. % Starting or ending with '0a' or '% 00'
kl.npki.base.management.i18n_asymmetric_key_index_information_cannot_be_empty=Asymmetric key index information cannot be empty

# GroupEngineConfigRequest
kl.npki.base.management.i18n_the_configuration_of_encryption_machines_within_the_group_cannot_be_empty=The configuration of encryption machines within the group cannot be empty

# LogConfigRequest
kl.npki.base.management.i18n_the_system_log_level_cannot_be_empty=The system log level cannot be empty
kl.npki.base.management.i18n_the_length_of_the_system_log_level_cannot_exceed_128_characters=The length of the system log level cannot exceed 128 characters
kl.npki.base.management.i18n_the_system_log_level_cannot_be_filled_with_spaces_or_20_starting_or_ending_with_0a_or_00=The system log level cannot be filled with spaces or% 20. % Starting or ending with '0a' or '% 00'
kl.npki.base.management.i18n_the_maximum_capacity_of_system_logs_cannot_be_empty=The maximum capacity of system logs cannot be empty
kl.npki.base.management.i18n_the_maximum_length_of_the_log_capacity_value_cannot_exceed_32_characters=The maximum length of the log capacity value cannot exceed 32 characters
kl.npki.base.management.i18n_the_retention_period_of_logs_cannot_be_less_than_1_day=The retention period of logs cannot be less than 1 day
kl.npki.base.management.i18n_the_retention_period_of_logs_cannot_exceed_1000_days=The retention period of logs cannot exceed 1000 days
kl.npki.base.management.i18n_whether_the_audit_service_is_enabled_cannot_be_empty=Whether the audit service is enabled cannot be empty
kl.npki.base.management.i18n_audit_service_ip_address_is_out_of_range_or_formatted_incorrectly=Audit service IP address is out of range or formatted incorrectly
kl.npki.base.management.i18n_audit_service_port_number_is_out_of_range_or_formatted_incorrectly=Audit service port number is out of range or formatted incorrectly
kl.npki.base.management.i18n_audit_service_protocol_is_incorrect=Audit service protocol is incorrect

# AddRoleRequest
kl.npki.base.management.i18n_the_role_name_cannot_be_empty=The role name cannot be empty
kl.npki.base.management.i18n_the_length_of_the_character_name_cannot_exceed_128_characters=The length of the character name cannot exceed 128 characters
kl.npki.base.management.i18n_the_role_name_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=The role name cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'
kl.npki.base.management.i18n_role_code_cannot_be_empty=Role code cannot be empty
kl.npki.base.management.i18n_the_length_of_the_role_note_cannot_exceed_128_characters=The length of the role note cannot exceed 128 characters
kl.npki.base.management.i18n_the_length_of_the_parent_role_code_cannot_exceed_128_characters=The length of the parent role code cannot exceed 128 characters
kl.npki.base.management.i18n_parent_role_code_cannot_use_spaces_or_20_starting_or_ending_with_0a_or_00=Parent role code cannot use spaces or %20 starting or ending with %0a or %00
kl.npki.base.management.i18n_parent_role_code_cannot_be_empty=Parent role code cannot be empty

# PermissionTreeSaveRequest
kl.npki.base.management.i18n_the_role_name_should_not_be_empty=The role name should not be empty

# PortConfigRequest
kl.npki.base.management.i18n_the_management_service_port_number_cannot_be_less_than_1=The management service port number cannot be less than 1
kl.npki.base.management.i18n_the_management_service_port_number_cannot_exceed_65535=The management service port number cannot exceed 65535
kl.npki.base.management.i18n_the_management_service_ssl_switch_cannot_be_empty=The management service ssl switch cannot be empty
kl.npki.base.management.i18n_the_https_management_service_port_number_cannot_be_less_than_1=The HTTPS management service port number cannot be less than 1
kl.npki.base.management.i18n_the_https_management_service_port_number_cannot_exceed_65535=The HTTPS management service port number cannot exceed 65535
kl.npki.base.management.i18n_the_http_business_service_switch_cannot_be_empty=The HTTP business service switch cannot be empty
kl.npki.base.management.i18n_the_http_business_service_port_number_cannot_be_less_than_1=The HTTP business service port number cannot be less than 1
kl.npki.base.management.i18n_the_http_business_service_port_number_cannot_exceed_65535=The HTTP business service port number cannot exceed 65535
kl.npki.base.management.i18n_the_ssl_switch_for_http_business_services_cannot_be_empty=The SSL switch for HTTP business services cannot be empty
kl.npki.base.management.i18n_tcp_service_switch_cannot_be_empty=TCP service switch cannot be empty
kl.npki.base.management.i18n_the_tcp_service_port_number_cannot_be_less_than_1=The TCP service port number cannot be less than 1
kl.npki.base.management.i18n_the_tcp_service_port_number_cannot_exceed_65535=The TCP service port number cannot exceed 65535
kl.npki.base.management.i18n_tcp_business_service_ssl_switch_cannot_be_empty=TCP business service SSL switch cannot be empty

# SysManagerRequest
kl.npki.base.management.i18n_the_system_name_cannot_be_empty=The system name cannot be empty
kl.npki.base.management.i18n_the_length_of_the_system_name_cannot_exceed_128_characters=The length of the system name cannot exceed 128 characters
kl.npki.base.management.i18n_the_system_name_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00=The system name cannot contain spaces or% 20. % Starting or ending with '0a' or '% 00'
kl.npki.base.management.i18n_the_system_logo_format_is_incorrect=The system logo format is incorrect

# RegionAndLanguageRequest
kl.npki.base.management.i18n_the_region_cannot_be_empty=The regional cannot be empty
kl.npki.base.management.i18n_the_region_param_error=The regional parameters are incorrect
kl.npki.base.management.i18n_the_language_cannot_be_empty=The language cannot be empty
kl.npki.base.management.i18n_the_language_param_error=The language parameters are incorrect

# PortConfigResponse
kl.npki.base.management.i18n_the_management_service_port_number_cannot_be_less_than_0=The management service port number cannot be less than 0
kl.npki.base.management.i18n_the_https_management_service_port_number_cannot_be_less_than_0=The HTTPS management service port number cannot be less than 0
kl.npki.base.management.i18n_the_http_business_service_port_number_cannot_be_less_than_0=The HTTP business service port number cannot be less than 0
kl.npki.base.management.i18n_the_tcp_service_port_number_cannot_be_less_than_0=The TCP service port number cannot be less than 0

# Enum
kl.npki.base.management.i18n_not_deployed=Not Deployed
kl.npki.base.management.i18n_deployed=Deployed

# ServiceStatusEnum
kl.npki.base.management.i18n_start=Service startup
kl.npki.base.management.i18n_stop=Service shutdown

# 异常描述
kl.npki.base.management.i18n_could_not_create_keystore_directory=Failed to create Keystore directory {0}
kl.npki.base.management.i18n_administrator_certificate_information_integrity_verification_failed=Administrator certificate information integrity verification failed, username: {0}
kl.npki.base.management.i18n_character_information_integrity_verification_failed=Role information integrity verification failed, role name: {0}
kl.npki.base.management.i18n_the_session_id_does_not_exist=Session ID does not exist
kl.npki.base.management.i18n_remote_call_to_kcsp_user_information_interface_authentication_failed=Remote call to KCSP user information interface authentication failed, error details: {0}; HTTP status code: {1}; response body: {2}
kl.npki.base.management.i18n_unknown_error_occurred_while_processing_kcsp_user_authentication=An unknown error occurred while processing KCSP user authentication, error details: {0}
kl.npki.base.management.i18n_the_user_status_is_abnormal_please_contact_the_administrator=User status is abnormal, please contact the administrator
kl.npki.base.management.i18n_failed_to_obtain_valid_user_information=Failed to obtain valid user information
kl.npki.base.management.i18n_no_access_token_provided_user_not_logged_in_or_session_expired=No AccessToken provided, user not logged in or session expired
kl.npki.base.management.i18n_the_file_size_exceeds_the_limit=File size exceeds the limit: {0}
kl.npki.base.management.i18n_file_is_not_a_dat_file=File is not a dat file: {0}
kl.npki.base.management.i18n_license_file_read_exception=License file read exception
kl.npki.base.management.i18n_no_permission_to_register_as_an_administrator_for_this_type=No permission to register as an administrator for this type
kl.npki.base.management.i18n_no_permission_to_import_this_type_of_administrator=No permission to import this type of administrator
kl.npki.base.management.i18n_no_permission_to_request_revocation_of_this_type_of_administrator_certificate=No permission to request revocation of this type of administrator certificate
kl.npki.base.management.i18n_do_not_allow_administrators_to_abolish_themselves=Do not allow administrators to abolish themselves
kl.npki.base.management.i18n_do_not_have_the_authority_to_revoke_the_administrator_certificate_revocation_application_for_this_type=Do not have the authority to revoke the administrator certificate revocation application for this type
kl.npki.base.management.i18n_do_not_have_permission_to_log_out_of_this_type_of_administrator=Do not have permission to log out of this type of administrator
kl.npki.base.management.i18n_no_extension_of_administrator_privileges_for_this_type=No extension of administrator privileges for this type
kl.npki.base.management.i18n_database_configuration_information_cannot_be_empty=Database configuration information cannot be empty
kl.npki.base.management.i18n_h2_database_configuration_is_not_allowed=H2 database configuration is not allowed
kl.npki.base.management.i18n_data_is_empty=Data is empty
kl.npki.base.management.i18n_please_assign_file_must_not_be_empty_or_null=Please assign a file, it must not be empty or null
kl.npki.base.management.i18n_invalid_log_path=Invalid log name: {0}
kl.npki.base.management.i18n_file_download_failed=File [{0}] download failed
kl.npki.base.management.i18n_the_role_not_exist=Role does not exist
kl.npki.base.management.i18n_the_resource_not_exist=Resource does not exist
kl.npki.base.management.i18n_inconsistent_identity_credentials_for_multi_factor_authentication=Inconsistent identity credentials for multi-factor authentication: {0} and {1}
kl.npki.base.management.i18n_port_used_error=The port is already in use

# SQL中的资源国际化
kl.npki.base.management.i18n_home_page=Home
kl.npki.base.management.i18n_system_management=System Management
kl.npki.base.management.i18n_system_configuration=System Configuration
kl.npki.base.management.i18n_system_information=System Information
kl.npki.base.management.i18n_system_logs=System Logs
kl.npki.base.management.i18n_admin_management=Admin Management
kl.npki.base.management.i18n_personnel_management=Personnel Management
kl.npki.base.management.i18n_import_admin_certificate=Import Admin Certificate
kl.npki.base.management.i18n_audit_management=Audit Management
kl.npki.base.management.i18n_review_management=Review Management
kl.npki.base.management.i18n_role_management=Role Management
kl.npki.base.management.i18n_system_operation=System Maintenance
kl.npki.base.management.i18n_backup_admin=Backup Administrator
kl.npki.base.management.i18n_system_self_check=System Self-Check
kl.npki.base.management.i18n_super_admin_remark=Responsible for managing the lifecycle of Business Administrator
kl.npki.base.management.i18n_security_admin_remark=Responsible for approving operation requests related to administrator lifecycles and assigning permissions to administrators
kl.npki.base.management.i18n_audit_admin_remark=Responsible for managing the lifecycle of Auditor
kl.npki.base.management.i18n_biz_admin_remark=Responsible for managing the lifecycle of Business Operator
kl.npki.base.management.i18n_audit_oper_remark=Responsible for querying and auditing system security logs
kl.npki.base.management.i18n_biz_oper_remark=Responsible for configuring system parameters, performing business operations, querying system logs, etc
kl.npki.base.management.i18n_law_admin_remark=Able to transfer user private key to the forensic key storage device as required by regulations
kl.npki.base.management.i18n_deploy_operator_remark=Responsible for deploying the system
kl.npki.base.management.i18n_system_license=System License
kl.npki.base.management.i18n_system_inspection=System Inspection
kl.npki.base.management.i18n_org_management=Org Management
kl.npki.base.management.i18n_org_detail=Org Detail
kl.npki.base.management.i18n_org_batch_import=Org Batch Import
kl.npki.base.management.i18n_resource_mgmt=Resource Management

# ApiLogExportRequest
kl.npki.base.management.i18n_signer_certificate_chain_cannot_be_empty=Signer certificate chain cannot be empty
kl.npki.base.management.i18n_list_of_fields_specified_for_export_cannot_be_empty=List of fields specified for export cannot be empty

# LogExcelFileRequest
kl.npki.base.management.i18n_signature_time_cannot_be_empty=Signature time cannot be empty
kl.npki.base.management.i18n_file_id_cannot_be_empty=File ID cannot be empty
kl.npki.base.management.i18n_signature_cannot_be_empty=Signature cannot be empty

# RegionAndLanguageConfigController
kl.npki.base.management.i18n_RegionAndLanguage_SAVE_REGION_LANGUAGE=Save region and language
kl.npki.base.management.i18n_RegionAndLanguage_GET_REGION_LANGUAGE=Get region and language
kl.npki.base.management.i18n_RegionAndLanguage_LIST_REGION_LANGUAGE=Get region and language list

# UtilController
kl.npki.base.management.i18n_annotation.get_env_value=Get configuration value
kl.npki.base.management.i18n_annotation.pinyin_conversion=Pinyin conversion
kl.npki.base.management.i18n_annotation.set_language=Set language
# BaseConstantController
kl.npki.base.management.i18n_annotation.constant_asym_algo=Query asymmetric algorithm constants
kl.npki.base.management.i18n_annotation.constant_cert_status=Query the certificate status constant
kl.npki.base.management.i18n_annotation.constant_user_status=Query user status constants
kl.npki.base.management.i18n_annotation.constant_ca_level=Query the CA level constant
kl.npki.base.management.i18n_annotation.constant_entity_status=Query entity status constants
kl.npki.base.management.i18n_annotation.constant_http_method=Query HTTP request type list
kl.npki.base.management.i18n_annotation.constant_resource_types=Query unified resource type constants

# API Resource Permission Management
kl.npki.base.management.i18n_query_api_resource_permissions_tree=Query API resource permissions list (tree structure)
kl.npki.base.management.i18n_query_api_resource_permission_details=Query API resource permission details
kl.npki.base.management.i18n_create_api_resource_permission=Create API resource permission
kl.npki.base.management.i18n_update_api_resource_permission=Update API resource permission
kl.npki.base.management.i18n_delete_api_resource_permission=Delete API resource permission
kl.npki.base.management.i18n_list_api_resources=Query API resource permissions list
kl.npki.base.management.i18n_create_api_resource=Create API resource
kl.npki.base.management.i18n_update_api_resource=Update API resource
kl.npki.base.management.i18n_delete_api_resource=Delete API resource
kl.npki.base.management.i18n_get_role_api_resources=Get role API resource permissions
kl.npki.base.management.i18n_update_role_api_resources=Update role API resource permissions

# API Resource Permission Parameter Validation
kl.npki.base.management.i18n_api_resource_name_should_not_be_empty=API resource name should not be empty
kl.npki.base.management.i18n_url_should_not_be_empty=URL should not be empty
kl.npki.base.management.i18n_request_method_should_not_be_empty=Request method should not be empty

# KcspSvcSyncController
kl.npki.base.management.i18n_annotation.kcsp_sync_service=KCSP platform service synchronization (add, update, delete public services)

# Unified Resource Management
kl.npki.base.management.i18n_query_unified_resources_tree=Query unified resource list (tree structure)
kl.npki.base.management.i18n_create_unified_resource=Create unified resource
kl.npki.base.management.i18n_update_unified_resource=Update unified resource
kl.npki.base.management.i18n_delete_unified_resource=Delete unified resource
kl.npki.base.management.i18n_batch_delete_unified_resources=Batch delete unified resources

# 管理员信息异常描述
kl.npki.base.management.i18n_admin_id_error=Administrator ID: "{0}" does not exist
kl.npki.base.management.i18n_admin_name_error=Administrator name: "{0}" does not exist
kl.npki.base.management.i18n_admin_name_id_no_match_error=Administrator ID "{0}" and administrator name "{1}" do not match

# Unified Resource Parameter Validation
kl.npki.base.management.i18n_resource_name_should_not_be_empty=Resource name should not be empty
kl.npki.base.management.i18n_resource_name_length_exceed_limit=Resource name length cannot exceed 255 characters
kl.npki.base.management.i18n_resource_code_should_not_be_empty=Resource code should not be empty
kl.npki.base.management.i18n_resource_code_length_exceed_limit=Resource code length cannot exceed 255 characters
kl.npki.base.management.i18n_resource_type_should_not_be_empty=Resource type should not be empty
kl.npki.base.management.i18n_menu_icon_length_exceed_limit=Menu icon length cannot exceed 255 characters
kl.npki.base.management.i18n_page_path_length_exceed_limit=Page path length cannot exceed 500 characters
kl.npki.base.management.i18n_url_length_exceed_limit=URL length cannot exceed 500 characters
kl.npki.base.management.i18n_request_method_length_exceed_limit=Request method length cannot exceed 20 characters
kl.npki.base.management.i18n_remark_length_exceed_limit=Remark length cannot exceed 500 characters
kl.npki.base.management.i18n_resource_code_cannot_equals_parent_code=Resource code cannot be the same as the parent resource code
kl.npki.base.management.i18n_role_code_cannot_equals_parent_code=Role code cannot be the same as the parent role code

# Resource Management Error Messages
kl.npki.base.management.i18n_resource_has_child_resources_cannot_delete=This resource has child resources and cannot be deleted
kl.npki.base.management.i18n_batch_delete_has_child_resources_not_selected=Cannot delete, there are child resources under the selected resources that are not being deleted together