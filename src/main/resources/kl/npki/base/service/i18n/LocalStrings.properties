# BaseServiceInternalError
kl.npki.base.service.i18n_system_error=系统错误
kl.npki.base.service.i18n_db_refresh_error=监听器db配置刷新失败！
kl.npki.base.service.i18n_db_config_database_name_format_error=db配置【数据库名称】解析失败！
kl.npki.base.service.i18n_db_tenant_slot_config_not_found=租户id对应的slot配置未找到！
kl.npki.base.service.i18n_invalid_log_type=无效的日志类型
kl.npki.base.service.i18n_tenant_holder_init_failed=租户托管容器初始化失败
kl.npki.base.service.i18n_request_wrapper_error=包装请求失败
kl.npki.base.service.i18n_create_ssl_connection_factory_failed=创建SslConnectionFactory失败
kl.npki.base.service.i18n_file_write_error=文件写入失败
kl.npki.base.service.i18n_file_read_error=文件读取失败
kl.npki.base.service.i18n_file_config_entity_save_failed=文件配置保存失败
kl.npki.base.service.i18n_operation_signature_verification_failed=操作签名验证失败
kl.npki.base.service.i18n_administrator_certificate_does_not_exist=管理员证书不存在
kl.npki.base.service.i18n_no_such_log_level=不存在对应日志级别

# BaseServiceRemoteError

# BaseServiceValidationError
kl.npki.base.service.i18n_slave_db_count_error=只读数据库数量错误！
kl.npki.base.service.i18n_user_not_login=用户未登录
kl.npki.base.service.i18n_db_config_not_set_error=当前环境数据库未配置，需要先配置数据库！
kl.npki.base.service.i18n_username_field_in_header_is_empty=请求头中username字段为空
kl.npki.base.service.i18n_iv_field_in_header_is_empty=请求头中IV字段为空
kl.npki.base.service.i18n_irregular_request=不规范的请求
kl.npki.base.service.i18n_unsupport_request_method=不支持的请求方法
kl.npki.base.service.i18n_unsupport_media_type=不支持的媒体类型
kl.npki.base.service.i18n_token_expired_error=TOKEN已超时
kl.npki.base.service.i18n_decoded_jwt_error=JWT 解码异常
kl.npki.base.service.i18n_invalid_cookie_info=无效的cookie信息
kl.npki.base.service.i18n_license_error=License异常
kl.npki.base.service.i18n_missing_request_param=缺失请求参数
kl.npki.base.service.i18n_param_convert_error=请求参数类型转换异常
kl.npki.base.service.i18n_missing_operation_signature_error=缺少操作签名
kl.npki.base.service.i18n_signature_timestamp_check_error=本地时间与服务器不一致，请检查系统时间设置是否正确
kl.npki.base.service.i18n_signature_timestamp_format_error=本地时间戳格式错误

#Enum
kl.npki.base.service.i18n_console=控制台
kl.npki.base.service.i18n_rolling_file=文件
kl.npki.base.service.i18n_async_rolling_file=异步文件
kl.npki.base.service.i18n_audit_service=审计服务器
kl.npki.base.service.i18n_off=关闭
kl.npki.base.service.i18n_error=错误
kl.npki.base.service.i18n_warn=警告
kl.npki.base.service.i18n_info=一般
kl.npki.base.service.i18n_debug=调试
kl.npki.base.service.i18n_trace=跟踪
kl.npki.base.service.i18n_all=所有

# 异常描述信息
kl.npki.base.service.i18n_system_configuration_not_read_from_configuration_center=系统配置{0}未从配置中心读取到
kl.npki.base.service.i18n_default_tenant_id_not_read_from_system_configuration=默认租户id未从系统配置{0}读取到
kl.npki.base.service.i18n_please_log_in_again=请重新登录
kl.npki.base.service.i18n_exceeds_license_limit=超过系统授权数量,业务标识 {0},最大授权数 {1}
kl.npki.base.service.i18n_license_update_error=授权数据更新异常,业务标识 {0},当前更新版本号 {1}
kl.npki.base.service.i18n_missing_request_param_field=请求中缺少字段 {0}
kl.npki.base.service.i18n_missing_request_header_param_field=请求头中缺少字段 {0}
kl.npki.base.service.i18n_signature_timestamp_format_error_desc=服务器时间 {0}，本地时间 {1}，时间偏差 {2} 分钟
kl.npki.base.service.i18n_param_convert_error_desc=参数 '{0}' 的值 '{1}' 无法转换为 {2} 类型



# ApiLogResolver
kl.npki.base.service.i18n_parse_request_to_json_error=请求转换为JSON异常
kl.npki.base.service.i18n_parse_response_to_json_error=响应转换为JSON异常

#AsnApiLogResolver
kl.npki.base.service.i18n_parse_request_to_asn_error=请求转换为ASN异常
kl.npki.base.service.i18n_parse_response_to_asn_error=响应转换为ASN异常

# 其他
kl.npki.km.service.i18n_system_operator=系统自身

# 系统巡检相关
kl.npki.base.service.i18n_system_release_version=发行版本
kl.npki.base.service.i18n_system_version_inspection=系统版本检查
kl.npki.base.service.i18n_system_kernel_version=内核版本
kl.npki.base.service.i18n_system_customized_version=定制版本

# MetricsController
kl.npki.base.service.i18n_annotation.metrics=获取监控指标
kl.npki.base.service.i18n_annotation.metrics_health=获取健康监控指标

# MgmtRootCAController
kl.npki.base.service.i18n_annotation.mgmt_root_ca_download=下载管理根CA证书或CRL