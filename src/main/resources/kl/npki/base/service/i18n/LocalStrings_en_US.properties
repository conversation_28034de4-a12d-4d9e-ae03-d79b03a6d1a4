# BaseServiceInternalError
kl.npki.base.service.i18n_system_error=System error!
kl.npki.base.service.i18n_db_refresh_error=Failed to refresh the listener db configuration!
kl.npki.base.service.i18n_db_config_database_name_format_error=Failed to parse db configuration [database name]!
kl.npki.base.service.i18n_db_tenant_slot_config_not_found=The slot configuration corresponding to the tenant id was not found!
kl.npki.base.service.i18n_invalid_log_type=Invalid log type!
kl.npki.base.service.i18n_tenant_holder_init_failed=Failed to initialize the tenant managed container!
kl.npki.base.service.i18n_request_wrapper_error=Failed to wrap request!
kl.npki.base.service.i18n_create_ssl_connection_factory_failed=Failed to create SslConnectionFactory!
kl.npki.base.service.i18n_file_write_error=Failed to write file!
kl.npki.base.service.i18n_file_read_error=Failed to read file!
kl.npki.base.service.i18n_file_config_entity_save_failed=Failed to save file configuration entity!
kl.npki.base.service.i18n_operation_signature_verification_failed=Operation signature verification failed!
kl.npki.base.service.i18n_administrator_certificate_does_not_exist=The administrator certificate does not exist!
kl.npki.base.service.i18n_no_such_log_level=The log level does not exist!

# BaseServiceRemoteError

# BaseServiceValidationError
kl.npki.base.service.i18n_slave_db_count_error=The number of read-only databases is incorrect!
kl.npki.base.service.i18n_user_not_login=User not logged in!
kl.npki.base.service.i18n_db_config_not_set_error=The current environment database is not configured. You need to configure the database first!
kl.npki.base.service.i18n_username_field_in_header_is_empty=The username field in the request header is empty!
kl.npki.base.service.i18n_iv_field_in_header_is_empty=The IV field in the request header is empty!
kl.npki.base.service.i18n_irregular_request=The request is irregular!
kl.npki.base.service.i18n_unsupport_request_method=The request method is not supported!
kl.npki.base.service.i18n_unsupport_media_type=The media type is not supported!
kl.npki.base.service.i18n_token_expired_error=TOKEN has timed out!
kl.npki.base.service.i18n_decoded_jwt_error=JWT Decoding exception!
kl.npki.base.service.i18n_invalid_cookie_info=Invalid cookie information!
kl.npki.base.service.i18n_license_error=License error
kl.npki.base.service.i18n_missing_request_param=Missing request parameter
kl.npki.base.service.i18n_param_convert_error=Request parameter type conversion exception
kl.npki.base.service.i18n_missing_operation_signature_error=Missing operation signature
kl.npki.base.service.i18n_signature_timestamp_check_error=The local time is inconsistent with the server, please check whether the system time setting is correct
kl.npki.base.service.i18n_signature_timestamp_format_error=Local timestamp format error

#Enum
kl.npki.base.service.i18n_console=Console
kl.npki.base.service.i18n_rolling_file=File
kl.npki.base.service.i18n_async_rolling_file=Async File
kl.npki.base.service.i18n_audit_service=Audit Service
kl.npki.base.service.i18n_off=Off
kl.npki.base.service.i18n_error=Error
kl.npki.base.service.i18n_warn=Warn
kl.npki.base.service.i18n_info=Info
kl.npki.base.service.i18n_debug=Debug
kl.npki.base.service.i18n_trace=Trace
kl.npki.base.service.i18n_all=All

# 异常描述信息
kl.npki.base.service.i18n_system_configuration_not_read_from_configuration_center=System configuration {0} was not read from the configuration center
kl.npki.base.service.i18n_default_tenant_id_not_read_from_system_configuration=The default tenant ID was not read from the system configuration {0}
kl.npki.base.service.i18n_please_log_in_again=Please log in again
kl.npki.base.service.i18n_exceeds_license_limit=Exceeding license limit, business identifier {0}, maximum  number {1}
kl.npki.base.service.i18n_license_update_error=License update error,business identifier  {0},current update version {1}
kl.npki.base.service.missing_request_param_field=Request missing parameter field {0}
kl.npki.base.service.i18n_missing_request_header_param_field=Request header missing parameter field {0}
kl.npki.base.service.i18n_signature_timestamp_format_error_desc=Server time {0}, local time {1}, time deviation {2} minutes
kl.npki.base.service.i18n_param_convert_error_desc=Value '{1}' of parameter '{0}' cannot be converted to type {2}

# ApiLogResolver
kl.npki.base.service.i18n_parse_request_to_json_error=Parse request to JSON error
kl.npki.base.service.i18n_parse_response_to_json_error=parse response to JSON error

#AsnApiLogResolver
kl.npki.base.service.i18n_parse_request_to_asn_error=Parse request to ASN error
kl.npki.base.service.i18n_parse_response_to_asn_error=Parse response to ASN error

# 其他
kl.npki.km.service.i18n_system_operator=SYSTEM

# 系统巡检相关
kl.npki.base.service.i18n_system_release_version=Release Version
kl.npki.base.service.i18n_system_version_inspection=System version check
kl.npki.base.service.i18n_system_kernel_version=Kernel Version
kl.npki.base.service.i18n_system_customized_version=Customized Version

# MetricsController
kl.npki.base.service.i18n_annotation.metrics=Get monitoring metrics
kl.npki.base.service.i18n_annotation.metrics_health=Get health monitoring metrics

# MgmtRootCAController
kl.npki.base.service.i18n_annotation.mgmt_root_ca_download=Download management root CA certificate or CRL