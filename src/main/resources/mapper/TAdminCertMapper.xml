<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kl.npki.base.management.repository.mapper.TAdminCertMapper">
    <select id="queryLoginCertSnList" resultType="java.lang.String">
        SELECT sign_cert_sn
        FROM t_admin_cert tac
        LEFT JOIN t_admin_info tai on tac.admin_info_id = tai.id
        <where>
            tac.status > 0
            AND tac.sign_cert_sn IS NOT NULL
            AND tac.is_delete = 0
            AND tai.role_code != 48
        </where>
    </select>
</mapper>
