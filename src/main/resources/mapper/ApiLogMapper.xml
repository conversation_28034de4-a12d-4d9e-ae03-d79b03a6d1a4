<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kl.npki.base.service.repository.mapper.ApiLogMapper">

    <select id="countGroupByCallerAndResultAndBiz" resultType="kl.npki.base.core.biz.log.model.ApiLogCountServiceResult">
        select count(*) as count, CALLER_NAME , RESULT, BIZ
        from t_api_log
        WHERE LOG_WHEN between #{startTime} and #{endTime}
             <if test="callerNames != null and callerNames.size > 0">
                  AND CALLER_NAME  in
                    <foreach collection="callerNames" item="callName" separator="," open="(" close=")">
                            #{callName}
                    </foreach>
             </if>
        group by CALLER_NAME , RESULT, BIZ
    </select>

    <select id="countGroupByResultAndBiz" resultType="kl.npki.base.core.biz.log.model.ApiLogCountServiceResult">
        select count(*) as count , RESULT, BIZ
        from t_api_log
        WHERE LOG_WHEN between #{startTime} and #{endTime}
                <if test="bizNames != null and bizNames.size > 0">
                        AND BIZ in
                            <foreach collection="bizNames" item="bizName" separator="," open="(" close=")">
                                #{bizName}
                            </foreach>
                </if>
        group by RESULT, BIZ
    </select>

</mapper>
