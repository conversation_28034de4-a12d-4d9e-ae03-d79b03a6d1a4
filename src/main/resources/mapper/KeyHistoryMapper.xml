<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kl.npki.km.service.repository.mapper.KeyHistoryMapper">
    <select id="countKeyType" resultType="kl.npki.km.core.biz.stat.model.KeyCountInfo">
        SELECT key_type AS keyType, COUNT(*) AS count
        FROM t_key_history
        GROUP BY key_type
    </select>

    <select id="countByCaIds" resultType="kl.npki.km.core.biz.stat.model.CaCountInfo">
        SELECT ca_id, COUNT(*) AS count
        FROM t_key_history
        WHERE ca_id IN
        <foreach item="item" index="index" collection="caIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY ca_id
    </select>
</mapper>
