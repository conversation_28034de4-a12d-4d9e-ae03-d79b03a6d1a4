<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kl.npki.base.management.repository.mapper.TResourceMapper">

    <update id="updateResourceStatus">
        UPDATE t_resource SET is_delete = #{status}
        WHERE
        <foreach item="resourceCode" index="index" collection="resourceList" separator="OR">
            resource_code LIKE #{resourceCode}
        </foreach>
    </update>

</mapper>

