<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kl.npki.base.management.repository.mapper.TRoleMapper">
    <resultMap id="RolePermissionMap" type="kl.npki.base.core.biz.security.model.UrlRoleInfo">
        <id column="id" property="id"/>
        <result column="resource_url" property="url"/>
        <result column="request_method" property="requestMethod"/>
        <collection property="roleCodeList" ofType="java.lang.String">
            <constructor>
                <arg column="role_code"/>
            </constructor>
        </collection>
    </resultMap>

    <select id="selectRoleByResourceCode" resultType="kl.npki.base.management.repository.entity.TRoleDO">
        SELECT
            t1.id,
            t1.role_code,
            t1.role_name,
            t1.role_name_i18n_key
        FROM t_role t1
        LEFT JOIN t_role_resource_link t2 ON t1.role_code = t2.role_code
        WHERE t2.resource_code = #{resourceCode}
    </select>

    <select id="listUrlRoles" resultMap="RolePermissionMap">
        SELECT RES.id,
               RES.resource_url,
               RES.request_method,
               RRL.role_code
        FROM t_resource RES
                 LEFT JOIN t_role_resource_link RRL ON RES.resource_code = RRL.resource_code
                 LEFT JOIN t_role R ON RRL.role_code = R.role_code
        WHERE RES.is_anonymous = 0
          AND RES.resource_type = 4
          AND RES.resource_url IS NOT NULL
          AND R.is_delete = 0
          AND RES.is_delete = 0
          AND RRL.is_delete = 0
    </select>
</mapper>

