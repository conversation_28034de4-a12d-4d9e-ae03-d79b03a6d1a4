<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kl.npki.base.management.repository.mapper.TAdminInfoMapper">
    <select id="queryForList" resultType="kl.npki.management.core.biz.admin.model.info.AdminInfoListInfo">
        SELECT
        T1.ID AS adminInfoId,
        T3.ROLE_NAME AS roleName,
        T3.ROLE_NAME_I18N_KEY AS roleNameI18nKey,
        T1.ROLE_CODE AS roleCode,
        T1.USERNAME AS username,
        T1.ORGANIZATION AS organization,
        T1.ORGANIZATION_UNIT AS organizationUnit,
        T1.EMAIL AS email,
        T2.ISSUER_CN AS issuerCn,
        T1.CREATE_TIME AS createTime,
        T1.UPDATE_TIME AS updateTime,
        T1.STATUS AS status,
        T2.STATUS AS certStatus,
        T2.IS_IMPORTED AS imported,
        T1.LOGIN_RETRIES AS loginRetries,
        T1.LOCK_TIME AS lockTime,
        T2.VALID_START AS validStart,
        T2.VALID_END AS validEnd,
        T2.SIGN_CERT_SN AS signCertSn,
        T2.ENC_CERT_SN AS encCertSn,
        T1.PROVINCE AS province,
        T1.CITY AS city,
        T1.ORG_ID AS orgId
        FROM
        T_ADMIN_INFO T1
        LEFT JOIN
        T_ADMIN_CERT T2
        ON T1.ID = T2.ADMIN_INFO_ID
        LEFT JOIN
        T_ROLE T3
        ON T1.ROLE_CODE = T3.ROLE_CODE
        <where>
            T1.IS_DELETE = 0
            AND T1.ROLE_CODE IN
            <foreach collection="roleCodes" index="index" item="roleCode" open="(" separator="," close=")">
                #{roleCode}
            </foreach>
            <if test="toBeCheckedUserStatusSet != null and toBeCheckedUserStatusSet.size() > 0
                    and toBeCheckedCertStatusSet != null and toBeCheckedCertStatusSet.size() > 0">
                AND
                (
                T1.STATUS IN
                <foreach collection="toBeCheckedUserStatusSet" item="userStatusItem" open="(" separator="," close=")">
                    #{userStatusItem}
                </foreach>
                OR
                T2.STATUS IN
                <foreach collection="toBeCheckedCertStatusSet" item="certStatusItem" open="(" separator="," close=")">
                    #{certStatusItem}
                </foreach>
                )
            </if>
            <if test="adminInfoListQuery.status != null">
                AND
                <choose>
                    <when test="adminInfoListQuery.status == @<EMAIL>()">
                        <!-- 枚举类中定义的LOCKED状态码为2000，实际锁定的状态码会大于2000且有多种锁定状态，因此此处用>进行查询 -->
                        T1.STATUS &gt; #{adminInfoListQuery.status}
                    </when>
                    <otherwise>
                        T1.STATUS = #{adminInfoListQuery.status}
                    </otherwise>
                </choose>
            </if>
            <if test="adminInfoListQuery.certStatus != null">
                AND
                T2.STATUS = #{adminInfoListQuery.certStatus}
            </if>
            <if test="adminInfoListQuery.quick and (adminInfoListQuery.username != null or adminInfoListQuery.username != null or adminInfoListQuery.issuerCn != null)">
                AND ((
                1 != 1
                <if test="adminInfoListQuery.username != null and adminInfoListQuery.username !=''">
                    <bind name="fuzzyUsername" value="'%' + adminInfoListQuery.username + '%'"/>
                    OR
                    T1.USERNAME like #{fuzzyUsername}
                </if>
                <if test="adminInfoListQuery.username != null and adminInfoListQuery.roleName !=''">
                    <bind name="fuzzyRoleName" value="'%' + adminInfoListQuery.roleName + '%'"/>
                    OR
                    T3.ROLE_NAME like #{fuzzyRoleName}
                </if>
                <if test="adminInfoListQuery.issuerCn != null and adminInfoListQuery.issuerCn !=''">
                    <bind name="fuzzyIssuerCn" value="'%' + adminInfoListQuery.issuerCn + '%'"/>
                    OR
                    T2.ISSUER_CN like #{fuzzyIssuerCn}
                </if>
                ))
            </if>
            <if test="!adminInfoListQuery.quick">
                <if test="adminInfoListQuery.username != null and adminInfoListQuery.username !=''">
                    <bind name="fuzzyUsername" value="'%' + adminInfoListQuery.username + '%'"/>
                    AND
                    T1.USERNAME like #{fuzzyUsername}
                </if>
                <if test="adminInfoListQuery.roleName != null and adminInfoListQuery.roleName !=''">
                    <bind name="fuzzyRoleName" value="'%' + adminInfoListQuery.roleName + '%'"/>
                    AND
                    T3.ROLE_NAME like #{fuzzyRoleName}
                </if>
                <if test="adminInfoListQuery.issuerCn != null and adminInfoListQuery.issuerCn !=''">
                    <bind name="fuzzyIssuerCn" value="'%' + adminInfoListQuery.issuerCn + '%'"/>
                    AND
                    T2.ISSUER_CN like #{fuzzyIssuerCn}
                </if>
                <if test="adminInfoListQuery.adminGroup != null and adminInfoListQuery.adminGroup !=''">
                    AND
                    T1.ADMIN_GROUP = #{adminInfoListQuery.adminGroup}
                </if>
                <if test="adminInfoListQuery.leCreateAt != null">
                    AND
                    T1.CREATE_TIME &gt; #{adminInfoListQuery.leCreateAt}
                </if>
                <if test="adminInfoListQuery.geCreateAt != null">
                    AND
                    T1.CREATE_TIME &lt; #{adminInfoListQuery.geCreateAt}
                </if>
            </if>
        </where>
        ORDER BY T1.CREATE_TIME DESC
    </select>

    <select id="queryForDetail" resultType="kl.npki.management.core.biz.admin.model.info.AdminInfoDetailInfo">
        SELECT
        T1.ID AS adminInfoId,
        T1.ROLE_CODE AS roleCode,
        T1.USERNAME AS username,
        T1.CREATE_TIME AS createTime,
        T1.UPDATE_TIME AS updateTime,
        T3.ROLE_NAME AS roleName,
        T3.ROLE_NAME_I18N_KEY AS roleNameI18nKey,
        T3.IS_CUSTOM AS isCustom,
        T2.SUBJECT_CN AS subjectCn,
        T1.ORGANIZATION AS organization,
        T1.ORGANIZATION_UNIT AS organizationUnit,
        T1.ORG_ID AS orgId,
        T1.EMAIL AS email,
        T2.ISSUER_CN AS issuerCn,
        T2.SIGN_CERT_SN AS signCertSn,
        T2.ENC_CERT_SN AS encCertSn,
        T1.PROVINCE AS province,
        T1.CITY AS city,
        T1.STATUS AS status,
        T2.STATUS AS certStatus,
        T2.IS_IMPORTED AS imported,
        T1.LOGIN_RETRIES AS loginRetries,
        T1.LOCK_TIME AS lockTime,
        T2.VALID_START AS validStart,
        T2.VALID_END AS validEnd,
        T2.SIGN_CERT_VALUE AS signCertValue,
        T2.ENC_CERT_VALUE AS encCertValue
        FROM
        T_ADMIN_INFO T1
        LEFT JOIN
        T_ADMIN_CERT T2
        ON T1.ID = T2.ADMIN_INFO_ID
        LEFT JOIN
        T_ROLE T3
        ON T1.ROLE_CODE = T3.ROLE_CODE
        <where>
            T1.IS_DELETE = 0
            AND T3.ROLE_CODE IN
            <foreach collection="roleCodes" index="index" item="roleCode" open="(" separator="," close=")">
                #{roleCode}
            </foreach>
            <if test="adminInfoId !=null">
                AND
                T1.ID = #{adminInfoId}
            </if>
        </where>
    </select>

    <select id="countIssuedAdminByRole" resultType="java.lang.Long">
        SELECT COUNT(*) FROM(
        SELECT T1.ID FROM T_ADMIN_INFO T1
        LEFT JOIN T_ADMIN_CERT T2 ON T1.ID = T2.ADMIN_INFO_ID
        <where>
            T1.ROLE_CODE = #{roleCode}
            AND T2.STATUS &gt;= #{issuedCertStatus}
            AND T1.IS_DELETE = 0
            AND T2.IS_DELETE = 0
            <if test="adminGroup !=null and adminGroup !=''">
                AND
                T1.ADMIN_GROUP = #{adminGroup}
            </if>
        </where>

        ) T3
    </select>
    <select id="checkAdminIsCancelled" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM T_ADMIN_INFO  where IS_DELETE=1 and USERNAME=#{username}
    </select>

</mapper>

