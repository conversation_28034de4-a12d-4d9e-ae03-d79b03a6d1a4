<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kl.npki.base.management.repository.mapper.TAdminInfoMapper">
    <select id="queryForList" resultType="kl.npki.management.core.biz.admin.model.info.AdminInfoListInfo">
        SELECT
        T1.id AS adminInfoId,
        T3.role_name AS roleName,
        T3.role_name_i18n_key AS roleNameI18nKey,
        T1.role_code AS roleCode,
        T1.username AS username,
        T1.organization AS organization,
        T1.organization_unit AS organizationUnit,
        T1.email AS email,
        T2.issuer_cn AS issuerCn,
        T1.create_time AS createTime,
        T1.update_time AS updateTime,
        T1.status AS status,
        T2.status AS certStatus,
        T2.is_imported AS imported,
        T1.login_retries AS loginRetries,
        T1.lock_time AS lockTime,
        T2.valid_start AS validStart,
        T2.valid_end AS validEnd,
        T2.sign_cert_sn AS signCertSn,
        T2.enc_cert_sn AS encCertSn,
        T1.province AS province,
        T1.city AS city,
        T1.org_id AS orgId
        FROM
        t_admin_info T1
        LEFT JOIN
        t_admin_cert T2
        ON T1.id = T2.admin_info_id
        LEFT JOIN
        t_role T3
        ON T1.role_code = T3.role_code
        <where>
            T1.is_delete = 0
            AND T1.role_code IN
            <foreach collection="roleCodes" index="index" item="roleCode" open="(" separator="," close=")">
                #{roleCode}
            </foreach>
            <if test="toBeCheckedUserStatusSet != null and toBeCheckedUserStatusSet.size() > 0
                    and toBeCheckedCertStatusSet != null and toBeCheckedCertStatusSet.size() > 0">
                AND
                (
                T1.status IN
                <foreach collection="toBeCheckedUserStatusSet" item="userStatusItem" open="(" separator="," close=")">
                    #{userStatusItem}
                </foreach>
                OR
                T2.status IN
                <foreach collection="toBeCheckedCertStatusSet" item="certStatusItem" open="(" separator="," close=")">
                    #{certStatusItem}
                </foreach>
                )
            </if>
            <if test="adminInfoListQuery.status != null">
                AND
                <choose>
                    <when test="adminInfoListQuery.status == @<EMAIL>()">
                        <!-- 枚举类中定义的LOCKED状态码为2000，实际锁定的状态码会大于2000且有多种锁定状态，因此此处用>进行查询 -->
                        T1.status &gt; #{adminInfoListQuery.status}
                    </when>
                    <otherwise>
                        T1.status = #{adminInfoListQuery.status}
                    </otherwise>
                </choose>
            </if>
            <if test="adminInfoListQuery.certStatus != null">
                AND
                T2.status = #{adminInfoListQuery.certStatus}
            </if>
            <if test="adminInfoListQuery.quick and (adminInfoListQuery.username != null or adminInfoListQuery.username != null or adminInfoListQuery.issuerCn != null)">
                AND ((
                1 != 1
                <if test="adminInfoListQuery.username != null and adminInfoListQuery.username !=''">
                    <bind name="fuzzyUsername" value="'%' + adminInfoListQuery.username + '%'"/>
                    OR
                    T1.username like #{fuzzyUsername}
                </if>
                <if test="adminInfoListQuery.username != null and adminInfoListQuery.roleName !=''">
                    <bind name="fuzzyRoleName" value="'%' + adminInfoListQuery.roleName + '%'"/>
                    OR
                    T3.role_name like #{fuzzyRoleName}
                </if>
                <if test="adminInfoListQuery.issuerCn != null and adminInfoListQuery.issuerCn !=''">
                    <bind name="fuzzyIssuerCn" value="'%' + adminInfoListQuery.issuerCn + '%'"/>
                    OR
                    T2.issuer_cn like #{fuzzyIssuerCn}
                </if>
                ))
            </if>
            <if test="!adminInfoListQuery.quick">
                <if test="adminInfoListQuery.username != null and adminInfoListQuery.username !=''">
                    <bind name="fuzzyUsername" value="'%' + adminInfoListQuery.username + '%'"/>
                    AND
                    T1.username like #{fuzzyUsername}
                </if>
                <if test="adminInfoListQuery.roleName != null and adminInfoListQuery.roleName !=''">
                    <bind name="fuzzyRoleName" value="'%' + adminInfoListQuery.roleName + '%'"/>
                    AND
                    T3.role_name like #{fuzzyRoleName}
                </if>
                <if test="adminInfoListQuery.issuerCn != null and adminInfoListQuery.issuerCn !=''">
                    <bind name="fuzzyIssuerCn" value="'%' + adminInfoListQuery.issuerCn + '%'"/>
                    AND
                    T2.issuer_cn like #{fuzzyIssuerCn}
                </if>
                <if test="adminInfoListQuery.adminGroup != null and adminInfoListQuery.adminGroup !=''">
                    AND
                    T1.admin_group = #{adminInfoListQuery.adminGroup}
                </if>
                <if test="adminInfoListQuery.leCreateAt != null">
                    AND
                    T1.create_time &gt; #{adminInfoListQuery.leCreateAt}
                </if>
                <if test="adminInfoListQuery.geCreateAt != null">
                    AND
                    T1.create_time &lt; #{adminInfoListQuery.geCreateAt}
                </if>
            </if>
        </where>
        ORDER BY T1.create_time DESC
    </select>

    <select id="queryForDetail" resultType="kl.npki.management.core.biz.admin.model.info.AdminInfoDetailInfo">
        SELECT
        T1.id AS adminInfoId,
        T1.role_code AS roleCode,
        T1.username AS username,
        T1.create_time AS createTime,
        T1.update_time AS updateTime,
        T3.role_name AS roleName,
        T3.role_name_i18n_key AS roleNameI18nKey,
        T3.is_custom AS isCustom,
        T2.subject_cn AS subjectCn,
        T1.organization AS organization,
        T1.organization_unit AS organizationUnit,
        T1.org_id AS orgId,
        T1.email AS email,
        T2.issuer_cn AS issuerCn,
        T2.sign_cert_sn AS signCertSn,
        T2.enc_cert_sn AS encCertSn,
        T1.province AS province,
        T1.city AS city,
        T1.status AS status,
        T2.status AS certStatus,
        T2.is_imported AS imported,
        T1.login_retries AS loginRetries,
        T1.lock_time AS lockTime,
        T2.valid_start AS validStart,
        T2.valid_end AS validEnd,
        T2.sign_cert_value AS signCertValue,
        T2.enc_cert_value AS encCertValue
        FROM
        t_admin_info T1
        LEFT JOIN
        t_admin_cert T2
        ON T1.id = T2.admin_info_id
        LEFT JOIN
        t_role T3
        ON T1.role_code = T3.role_code
        <where>
            T1.is_delete = 0
            AND T3.role_code IN
            <foreach collection="roleCodes" index="index" item="roleCode" open="(" separator="," close=")">
                #{roleCode}
            </foreach>
            <if test="adminInfoId !=null">
                AND
                T1.id = #{adminInfoId}
            </if>
        </where>
    </select>

    <select id="countIssuedAdminByRole" resultType="java.lang.Long">
        SELECT COUNT(*) FROM(
        SELECT T1.id FROM t_admin_info T1
        LEFT JOIN t_admin_cert T2 ON T1.id = T2.admin_info_id
        <where>
            T1.role_code = #{roleCode}
            AND T2.status &gt;= #{issuedCertStatus}
            AND T1.is_delete = 0
            AND T2.is_delete = 0
            <if test="adminGroup !=null and adminGroup !=''">
                AND
                T1.admin_group = #{adminGroup}
            </if>
        </where>

        ) T3
    </select>
    <select id="checkAdminIsCancelled" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_admin_info  where is_delete=1 and username=#{username}
    </select>

</mapper>

