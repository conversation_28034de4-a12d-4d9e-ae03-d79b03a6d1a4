<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kl.npki.base.service.repository.mapper.LicenseCountMapper">


    <update id="updateWithOptimisticLock">
        UPDATE T_LICENSE_COUNT
        SET
            UPDATE_TIME = #{updateTime},
            VERSION = VERSION + 1,
            CUS_VALUE = CASE
                            WHEN CUS_VALUE + #{cusValue} <![CDATA[ < ]]> 0 THEN 0
                            ELSE CUS_VALUE + #{cusValue}
                END
        WHERE ID = #{id} AND VERSION = #{version}
    </update>
    <update id="batchUpdate">
        update T_LICENSE_COUNT
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="CUS_KEY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.cusKey}
                </foreach>
            </trim>
            <trim prefix="CUS_VALUE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.cusValue}
                </foreach>
            </trim>
            <trim prefix="VERSION= case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.version}
                </foreach>
            </trim>
            <trim prefix="UPDATE_TIME= case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.updateTime}
                </foreach>
            </trim>
        </trim>
        WHERE ID IN
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id}
        </foreach>
    </update>

</mapper>