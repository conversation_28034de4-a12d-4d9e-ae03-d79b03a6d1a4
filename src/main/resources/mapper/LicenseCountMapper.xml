<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kl.npki.base.service.repository.mapper.LicenseCountMapper">


    <update id="updateWithOptimisticLock">
        UPDATE t_license_count
        SET
            update_time = #{updateTime},
            version = version + 1,
            cus_value = CASE
                            WHEN cus_value + #{cusValue} <![CDATA[ < ]]> 0 THEN 0
                            ELSE cus_value + #{cusValue}
                END
        WHERE id = #{id} AND version = #{version}
    </update>
    <update id="batchUpdate">
        update t_license_count
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="cus_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.cusKey}
                </foreach>
            </trim>
            <trim prefix="cus_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.cusValue}
                </foreach>
            </trim>
            <trim prefix="version= case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.version}
                </foreach>
            </trim>
            <trim prefix="update_time= case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.updateTime}
                </foreach>
            </trim>
        </trim>
        WHERE id IN
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id}
        </foreach>
    </update>

</mapper>