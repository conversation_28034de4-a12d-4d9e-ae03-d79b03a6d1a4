<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kl.npki.base.management.repository.mapper.TRoleResourceLinkMapper">
    <select id="selectResourceByRole" resultType="kl.npki.base.management.repository.entity.TResourceDO">
        SELECT
        a.id,
        a.resource_name,
        a.resource_name_i18n_key,
        a.resource_code,
        a.resource_type
        FROM
        t_resource a
        JOIN
        t_role_resource_link b
        ON
        a.resource_code = b.resource_code
        WHERE
        b.role_code = #{roleCode}
        AND b.is_delete = '0'
        AND a.is_delete = '0'
        <if test="resourceType != null">
            and a.resource_type in
            <foreach item="resourceType" index="index" collection="resourceType" open="(" separator="," close=" )">
                #{resourceType}
            </foreach>
        </if>
    </select>
    <select id="selectRoleResourceIdByRoleCode" resultType="java.lang.Long">
        SELECT res.id
        FROM t_role_resource_link res
        JOIN (
        SELECT resource_code, MAX(id) AS maxId
        FROM t_role_resource_link
        <if test="roleCodeList != null">
            WHERE
            <foreach item="roleCode" index="index" collection="roleCodeList" separator="OR">
                resource_code LIKE #{roleCode}
            </foreach>
        </if>
        GROUP BY resource_code
        ) resTemp
        ON res.id = resTemp.maxId
    </select>


    <update id="updateRoleResourceStatus">
        UPDATE t_role_resource_link SET is_delete = #{status}

        WHERE id IN
        <foreach collection="roleResourceIdList" item="roleResourceId" open="(" close=")" separator=",">
            #{roleResourceId}
        </foreach>
    </update>

</mapper>

