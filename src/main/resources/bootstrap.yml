spring:
  application:
    name: ${APP_NAME:ngpki-km-management}
  web:
    resources:
      static-locations: static/,public/,classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/
  cloud:
    consul:
      enabled: ${ENABLE_CONSUL:false}
      host: ${CONSUL_HOST:127.0.0.1}
      port: ${CONSUL_PORT:8500}
      # 服务发现
      discovery:
        # 启用服务发现
        enabled: ${ENABLE_CONSUL:false}
        # 启用服务注册
        register: ${ENABLE_CONSUL:false}
        instance-id: ${spring.application.name}:${spring.cloud.client.ip-address}:${server.port}:${random.value} #当同一服务器上有多个相同服务时,需要添加随机数以区分各服务实例ID(应用名称+服务器IP+端口+随机数)
        prefer-ip-address: true    #指定生产者上报自检的IP地址
        heartbeat:
          enabled: true #是否启用心跳检测
          reregister-service-on-failure: true #失败后重新注册服务
        query-passing: true #返回所有健康检查通过的服务
        health-check-critical-timeout: ${CONSUL_DEREGISTER_CRITICAL_SERVICE_TIME:86400s} #当心跳丢失时间超过该值时,则Consul会将服务标记为不健康并下线
        #healthCheckPath: health
        #healthCheckInterval: ${CONSUL_SERVICE_CHECK_TTL:15s}
        # KCSP平台自定义属性
        tags: management
        metadata:
          # 服务类型,例如: kl-nkm:下一代格尔密钥管理系统
          service-type: kl-nkm
          # 功能类型,0: 计算,1: 管理,2: 计算—+管理
          function-type: 1
    nacos:
      config:
        enabled: ${NACOS_ENABLED:false}
        server-addr: ${NACOS_SERVER_ADDR:127.0.0.1:31848}
        namespace: ${NACOS_NAMESPACE:DEFAULT}
        group: ${NACOS_GROUP:DEFAULT_GROUP}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:k0a1@nacos}
  health:
    metrics:
      datasource:
        enabled: true
context:
  initializer:
    classes: kl.npki.base.management.runner.H2EnvInitEvent
kl:
  server:
    netty:
      # 是否开启netty服务
      enabled: true
  application:
    configClassLocation: kl
    configStoreType: ${CONFIG_STORE_TYPE:FILE}
    configFileLocation: config
    configFileRegexps: npki-.*\.properties,application.properties
config:
  db:
    user:
    password:
    url: jdbc:h2:file:db/npki;AUTO_SERVER=TRUE;MODE=MySQL;
    siteName: 1
    # 切换业务高可用数据源。默认为false，待系统完成部署后（数据库配置完成后）自动修改此配置开启切换
    switchDataSource: false
  consul:
    host: ${CONSUL_HOST:127.0.0.1}
    port: ${CONSUL_PORT:8500}
#    aclToken: 2534684e-3877-82ea-f303-bd11420a0623
    # 设置配置的基本文件夹，可以理解为配置文件所在的最外层文件夹
    prefix: ${consulConfigKey:config/${spring.application.name}}
    watch:
      # 监听线程执行间隔时间，单位秒
      delay: 1
      # 阻塞查询的等待时间，单位秒
      wait: 55
    # 配置文件名映射，使用逗号分隔，格式为：本地配置文件名:配置中心中的配置文件名
    configMapping: 'npki-default.properties:data'
app:
  id: ${APOLLO_APP_ID:ngpki}
apollo:
  env: ${APOLLO_ENV:DEV}
  cluster: ${APOLLO_CLUSTER:default}
  meta: ${APOLLO_META:http://127.0.0.1:8080}
  portalUrl: ${APOLLO_PORTAL_URL:http://127.0.0.1:31070}
  token: ${APOLLO_TOKEN:14455bdfc673b7856e7a7b79c49ded77b622be28}
