spring:
  application:
    name: npki-base-management
  cloud:
    nacos:
      config:
        enabled: false
        server-addr: 127.0.0.1:31848
        group: DEFAULT_GROUP
        username: nacos
        password: k0a1@nacos
springdoc:
  swagger-ui:
    enabled: false
  api-docs:
    enabled: false
context:
  initializer:
    classes: kl.npki.base.management.runner.H2EnvInitEvent
kl:
  server:
    netty:
      # 是否开启netty服务
      enabled: true
  application:
    configClassLocation: kl
    configStoreType: FILE
    configFileLocation: config
    health:
      metrics:
        datasource:
          enabled: true

app:
  id: ${APOLLO_APP_ID:ngpki-base-management}
