# application
server.embed-name=jetty
server.http-disallowed=TRACE,TRACK
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
#spring.h2.console.enabled=true
#spring.h2.console.path=/h2
logging.config=${kl.application.configFileLocation}/logback-server.xml
mybatis-plus.global-config.db-config.logic-delete-field=isDelete
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0
mybatis-plus.mapper-locations=classpath*:mapper/*Mapper.xml
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.slf4j.Slf4jImpl
# port
## 是否已完成端口配置
server.portConfigured=true
# feign
feign.sentinel.enabled=true
feign.client.config.default.connectTimeout=30000
feign.client.config.default.readTimeout=30000
feign.compression.request.enabled=true
feign.compression.request.min-request-size=2048
feign.compression.request.mime-types=text/xml,application/xml,application/json,application/octet-stream
feign.compression.response.enabled=true
feign.httpclient.enabled=false
feign.httpclient.max-connections=200
feign.httpclient.max-connections-per-route=50
feign.httpclient.connection-timeout=30000
feign.httpclient.connection-timer-repeat=3000
feign.httpclient.time-to-live=900
feign.httpclient.time-to-live-unit=seconds
feign.httpclient.disable-ssl-validation=true
feign.httpclient.follow-redirects=true
feign.okhttp.enabled=true
# log
kl.base.log.level=INFO
kl.base.log.maxHistory=180
kl.base.log.syslogEnable=false
kl.base.log.syslogIp=127.0.0.1
kl.base.log.syslogPort=514
kl.base.log.syslogProtocol=TCP
kl.base.log.totalSizeCap=4096MB
# 日志存储类型, 如需兼容多个以逗号分隔
kl.base.log.storeType=dbLogStore,sysLogStore,fileLogStore
# login
# token生命周期，单位ms
kl.base.login.accessTokenLifecycle=1800000
# token有效期小于等于该值时，自动刷新token，单位ms
kl.base.login.refreshTokenThreshold=900000
# 刷新token后，旧token可使用的宽限期，默认30秒
kl.base.login.gracePeriod=30000
kl.base.login.accessControlEnabled=false
# license
kl.base.license.license=
kl.base.license.device=
# 指定的部署环境参数,当设置UNSPECIFIED时系统自动判断部署环境
# 可选UNSPECIFIED/OLD/FILE/WINDOWS/LINUX/DOCKER/GATEWAY
kl.base.license.specifiedDeployEnv=UNSPECIFIED
# 备用的部署环境参数,当specifiedDeployEnv无效时使用
kl.base.license.standbyDeployEnv=FILE
# sysInfo
## 是否进行操作签名
kl.base.sysInfo.clientSign=true
## 邮箱
kl.base.sysInfo.email=
kl.base.sysInfo.environmentSwitchId=demo
kl.base.sysInfo.tenantIds=demo,default
## 系统LOGO
kl.base.sysInfo.logo=
## 登录模式
kl.base.sysInfo.loginMode=
kl.base.sysInfo.userLicenseAgreement=false
## 是否进行密码人机分离Db
kl.base.sysInfo.passwdSeparateDb=false
## 启用csp
kl.base.sys.showCSP=true
## 启用完整性保护功能
kl.base.sys.openDataFull=true
## 是否加密请求体
kl.base.sys.encryptRequestBody=false
## 是否开启性能测试
kl.base.sys.performanceTest=false
# PKI中间件版本号
kl.base.sys.pkiClientVersion=2.4.5
# 系统间通讯认证的HMAC值
kl.base.sys.systemAuthToken=fe8034f676d25e6182869b2678fa6d4e
# timer
kl.base.timer.enabled=true
kl.base.timer.distributedEnabled=treu
kl.base.timer.executor.coreSize=5
kl.base.timer.type=spring
kl.base.timer.spring.coreSize=5
kl.base.timer.spring.awaitTerminationSeconds=60
kl.base.timer.xxlJob.adminAddresses=
kl.base.timer.xxlJob.address=
kl.base.timer.xxlJob.accessToken=
kl.base.timer.xxlJob.appName=KM-Service
kl.base.timer.xxlJob.ip=
kl.base.timer.xxlJob.port=58101
kl.base.timer.xxlJob.logPath=
kl.base.timer.xxlJob.logRetentionDays=30
kl.base.timer.dbLock.dbUrl=
kl.base.timer.dbLock.username=
kl.base.timer.dbLock.password=
# Self-check job
kl.base.timer.job.self.check.enable=true
kl.base.timer.job.self.check.cron=0 0 2 * * ?
# System inspection job
kl.base.timer.job.system.inspection.enabled=true
# 每月1号执行一次
kl.base.timer.job.system.inspection.cron=0 0 0 1 * ?

# thread
kl.base.thread.corePoolSize=5
kl.base.thread.maxPoolSize=8
kl.base.thread.keepAliveTime=30
kl.base.thread.queueCapacity=1000
# 健康组件
kl.health.metrics.dbMaxWaitMillis=10000
kl.health.metrics.measure-execution-time.enabled=true
kl.health.metrics.datasource.enabled=true
# id
kl.base.id.distributedEnabled=false
kl.base.id.timeBits=30
kl.base.id.workerBits=17
kl.base.id.seqBits=16
kl.base.id.scheduleInterval=
kl.base.id.epochStr=2024-01-01
# 限流组件
kl.base.traffic.enabled=true
kl.base.traffic.limiterType=sentinel
kl.base.traffic.defaultFlowRule.resource=default-rule
kl.base.traffic.defaultFlowRule.grade=1
kl.base.traffic.defaultFlowRule.count=100
kl.base.traffic.defaultDegradeRule.resource=default-rule
kl.base.traffic.defaultDegradeRule.grade=2
kl.base.traffic.defaultDegradeRule.count=10
kl.base.traffic.defaultDegradeRule.minRequestAmount=50
kl.base.traffic.defaultDegradeRule.timeWindow=10
kl.base.traffic.defaultDegradeRule.slowRatioThreshold=0.6
kl.base.traffic.defaultDegradeRule.statIntervalMs=1000
# 区域与语言配置
kl.base.regionalLanguage.region=CN
kl.base.regionalLanguage.language=zh
# 文件上传设置,单位 MB
kl.base.multipart.maxFileSize=5
kl.base.multipart.maxRequestSize=50