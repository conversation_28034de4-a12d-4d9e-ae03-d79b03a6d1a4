server.port=18088
server.embed.name=jetty
#spring.h2.console.enabled=true
#spring.h2.console.path=/h2
logging.config=${kl.application.configFileLocation}/logback-server.xml
mybatis-plus.global-config.db-config.logic-delete-field=isDelete
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0
mybatis-plus.mapper-locations=classpath*\:mapper/*Mapper.xml
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.slf4j.Slf4jImpl
# log
kl.base.log.level=INFO
kl.base.log.maxHistory=30
kl.base.log.maxFileSize=20MB
kl.base.log.syslogEnable=false
kl.base.log.syslogIp=127.0.0.1
kl.base.log.syslogPort=514
# 日志存储类型
kl.base.log.storeType=dbLogStore
# login
kl.base.login.accessTokenLifecycle=86400000
kl.base.login.accessControlEnabled=false
# 配置登录模式，目前取值有pwd, ssl_client, sign, ssl_client_sign
kl.base.login.loginModel=pwd
kl.base.login.urlWhitelist[0]=/login/**
kl.base.login.urlWhitelist[1]=/getRandom
kl.base.login.urlWhitelist[2]=/**
# license
kl.base.license.license=
kl.base.license.device=
# 指定的部署环境参数,当设置UNSPECIFIED时系统自动判断部署环境
# 可选UNSPECIFIED/OLD/FILE/WINDOWS/LINUX/DOCKER/GATEWAY
kl.base.license.specifiedDeployEnv=UNSPECIFIED
# 备用的部署环境参数,当specifiedDeployEnv无效时使用
kl.base.license.standbyDeployEnv=FILE
kl.base.license.refreshSpan=3600
# sysInfo
# 系统信息
kl.base.sysInfo.name=SYT0901-KM
## 版本
kl.base.sysInfo.version=V9.1.0
## 补丁版本
kl.base.sysInfo.packVersion=
## 定制版本
kl.base.sysInfo.customizedVersion=
## 是否进行操作签名
kl.base.sysInfo.clientSign=true
## 邮箱
kl.base.sysInfo.email=
kl.base.sysInfo.environmentSwitchId=default
kl.base.sysInfo.tenantIds=demo,default
## 系统LOGO
kl.base.sysInfo.logo=
## 登录模式
kl.base.sysInfo.loginMode=
## 启用csp
kl.base.sysInfo.showCSP=true
kl.base.sysInfo.startTime=1683799307771
## 是否进行完整性保护
kl.base.sysInfo.openDataFull=false
kl.base.sysInfo.userLicenseAgreement=true
kl.base.sysInfo.deploymentTime=1683359860856
# sharding
kl.base.sharding.shardingEnabled=false
kl.base.sharding.readwriteSplittingEnabled=false
kl.base.sharding.readWrite=
kl.base.sharding.readWriteEnabled=false
kl.base.sharding.databaseDiscoveryEnabled=false
kl.base.sharding.databaseShardingColumn=
kl.base.sharding.databaseShardingAlgorithm=kl.nbase.db.support.sharding.algorithm.DefaultDatabaseShardingAlgorithm
kl.base.sharding.multiMastersAndSlavesEnabled=false
kl.base.sharding.loadBalancers=
kl.base.sharding.props=
kl.base.sharding.healthCheck=
kl.base.sharding.healthCheck.delay=0
kl.base.sharding.healthCheck.period=5
kl.base.sharding.healthCheck.enableAutoSwitch=false
kl.base.sharding.tables[0].shardingColumn=enc_cert_sn
kl.base.sharding.tables[0].shardingAlgorithm=kl.nbase.db.support.sharding.algorithm.HashModShardingAlgorithm
kl.base.sharding.tables[0].keyGenerateAlgorithm=kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
kl.base.sharding.tables[0].keyGenerateColumn=
kl.base.sharding.tables[0].dataNodes=ds0.t_key_current_$->{0..2}
kl.base.sharding.tables[0].name=t_key_current
kl.base.sharding.tables[1].dataNodes=ds0.t_key_history_$->{0..2}
kl.base.sharding.tables[1].keyGenerateAlgorithm=kl.nbase.db.support.sharding.algorithm.SnowflakeAlgorithm
kl.base.sharding.tables[1].keyGenerateColumn=
kl.base.sharding.tables[1].name=t_key_history
kl.base.sharding.tables[1].shardingAlgorithm=kl.nbase.db.support.sharding.algorithm.HashModShardingAlgorithm
kl.base.sharding.tables[1].shardingColumn=enc_cert_sn
# timer
kl.base.timer.enabled=true
kl.base.timer.executor.coreSize=5
kl.base.timer.type=spring
kl.base.timer.spring.coreSize=5
kl.base.timer.spring.awaitTerminationSeconds=60
kl.base.timer.xxlJob.adminAddresses=
kl.base.timer.xxlJob.address=
kl.base.timer.xxlJob.accessToken=
kl.base.timer.xxlJob.appName=KM-Service
kl.base.timer.xxlJob.ip=
kl.base.timer.xxlJob.port=58101
kl.base.timer.xxlJob.logPath=
kl.base.timer.xxlJob.logRetentionDays=30
# thread
kl.base.thread.corePoolSize=1
kl.base.thread.maxPoolSize=5
kl.base.thread.keepAliveTime=30
kl.base.thread.queueCapacity=1000
# tcp
server.tcp.enabled=true
server.tcp.address=0.0.0.0
server.tcp.port=39991
server.tcp.bizThreadCount=10
# 接口业务处理类，此处配置的是容器中业务处理实现类bean的名称
# 组件内置了一个业务处理实现类：nettyHttpServlet
# 如果当前配置的不是nettyHttpServlet，则不需要配置tcp.httpRequestUri和tcp.httpRequestHeaders，
# 反之必须配置tcp.httpRequestUri，tcp.httpRequestHeaders配置是可选的
server.tcp.bizClassName=kl.npki.km.service.handler.KmServerHandler
server.tcp.ioThreadCount=10
# 接口消息解码器，此处配置的是spring容器中消息解码器bean的名称
# 组件内置了两个消息解码器：asn1DerMsgDecoder、koalMsgCodec
server.tcp.messageDecoderClassName=kl.nbase.netty.server.tcp.codec.Asn1DerMsgDecoder
# 是否开启ssl
# 如果不需要开启ssl，则此处修改为false，下面的配置也不需要配置
server.tcp.sslEnabled=false
server.tcp.sslCiphers=GMVPN_SM2_WITH_SM4_CBC_SM3,GMVPN_SM2_WITH_SM4_GCM_SM3
server.tcp.sslClientAuth=none
server.tcp.sslKeyStore=classpath\:site.p12
server.tcp.sslKeyStorePassword=koal@ssl@admin
server.tcp.sslKeyStoreProvider=BC
server.tcp.sslKeyStoreType=PKCS12hb
server.tcp.sslTrustStore=classpath\:trust.p12
server.tcp.sslTrustStorePassword=koal@ssl@admin
server.tcp.sslTrustStoreProvider=BC
server.tcp.sslTrustStoreType=PKCS12
