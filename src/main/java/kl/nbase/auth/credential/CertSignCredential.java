package kl.nbase.auth.credential;

/**
 * <AUTHOR>
 * Created on 2022/09/13 15:08
 */
public class CertSignCredential extends AbstractCredential {
    private String signData;
    private String hashAlgo;
    private String certSn;

    public CertSignCredential(String signData, String hashAlgo, String certSn) {
        this.signData = signData;
        this.hashAlgo = hashAlgo;
        this.certSn = certSn;
    }

    public String getSignData() {
        return signData;
    }

    public void setSignData(String signData) {
        this.signData = signData;
    }

    public String getHashAlgo() {
        return hashAlgo;
    }

    public void setHashAlgo(String hashAlgo) {
        this.hashAlgo = hashAlgo;
    }

    public String getCertSn() {
        return certSn;
    }

    public void setCertSn(String certSn) {
        this.certSn = certSn;
    }
}
