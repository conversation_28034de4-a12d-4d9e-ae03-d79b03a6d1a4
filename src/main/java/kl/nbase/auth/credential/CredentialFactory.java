package kl.nbase.auth.credential;

import kl.nbase.auth.core.AuthnType;
import kl.nbase.auth.entity.AuthnRequest;
import kl.nbase.auth.exception.AuthInternalError;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * Created on 2022/09/13 16:10
 */
public class CredentialFactory {

    private CredentialFactory() {
    }

    public static List<Credential> build(AuthnRequest authnRequest) {
        List<String> authnTypes = authnRequest.getAuthnTypes();
        if (authnTypes == null || authnTypes.isEmpty()) {
            return Collections.emptyList();
        }
        ArrayList<Credential> credentials = new ArrayList<>(4);
        for (String authCode : authnTypes) {
            Credential credential;
            if (authCode.toLowerCase().startsWith(AuthnType.SSO.getCode())) {
                // 单点登录的 code 格式为 sso_kcsp、sso_icbc 等。
                // 为支持动态扩展单点登录实现，AuthnType 中使用统一的 SSO 类型表示所有单点登录方式，
                // 因此此处需对单点登录类型做特殊处理。
                credential = new SsoCredential();
                credential.setAuthnType(authCode);
                credential.setExtending(authnRequest.getExtending());
                credentials.add(credential);
                break;
            }

            AuthnType authnType = AuthnType.getByCode(authCode);
            switch (authnType) {
                case PWD:
                    credential = new UsernamePasswordCredential(authnRequest.getPwd().getUsername(), authnRequest.getPwd().getPassword());
                    break;
                case CERT_SIGN:
                    AuthnRequest.Cert cert = authnRequest.getCert();
                    credential = new CertSignCredential(cert.getSignData(), cert.getHashAlgo(), cert.getCertSn());
                    break;
                case MULTI_CERT_SIGN:
                    AuthnRequest.MultiCert multiCert = authnRequest.getMultiCert();
                    CertSignCredential mainCredential = new CertSignCredential(multiCert.getMainCert().getSignData(), multiCert.getMainCert().getHashAlgo(), multiCert.getMainCert().getCertSn());
                    List<CertSignCredential> otherCredential = new ArrayList<>();
                    for (AuthnRequest.Cert otherCert : multiCert.getOtherCerts()) {
                        otherCredential.add(new CertSignCredential(otherCert.getSignData(), otherCert.getHashAlgo(), otherCert.getCertSn()));
                    }
                    credential = new MultiCertSignCredential(mainCredential, otherCredential);
                    break;
                case SSL:
                    // 从认证请求中获取对应的证书序列号
                    credential = new SslCredential(authnRequest.getSsl().getCertSn());
                    break;
                case BASIC_AUTH:
                    credential = new BasicAuthCredential(authnRequest.getBasicAuth().getHeader());
                    break;
                case DIGEST_AUTH:
                    credential = new DigestAuthCredential(authnRequest.getDigestAuth().getDigestHeader(),
                            authnRequest.getDigestAuth().getResource());
                    break;
                default:
                    throw AuthInternalError.UNSUPPORTED_AUTHN_TYPE.toException(authnType.getCode());
            }
            credential.setAuthnType(authnType.getCode());
            credential.setExtending(authnRequest.getExtending());
            credentials.add(credential);
        }

        return credentials;
    }
}
