package kl.nbase.auth.entity;


import java.util.List;

/**
 * <AUTHOR>
 * Created on 2022/09/13 16:02
 */
public class AuthnRequest {

    private final List<String> authnTypes;
    private String token;
    private Object extending;

    public AuthnRequest(List<String> authnTypes) {
        this.authnTypes = authnTypes;
    }

    private Pwd pwd;

    private Cert cert;
    
    private Ssl ssl;

    private MultiCert multiCert;

    private BasicAuth basicAuth;

    private DigestAuth digestAuth;

    public static class Pwd {
        /**
         * 用户名
         */
        private String username;

        /**
         * 密码
         */
        private String password;

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }
    
    public static class Cert {
        /**
         * 签名数据
         */
        private String signData;

        /**
         * 签名算法
         */
        private String hashAlgo;

        /**
         * 证书序列号
         */
        private String certSn;

        public String getSignData() {
            return signData;
        }

        public void setSignData(String signData) {
            this.signData = signData;
        }

        public String getHashAlgo() {
            return hashAlgo;
        }

        public void setHashAlgo(String hashAlgo) {
            this.hashAlgo = hashAlgo;
        }

        public String getCertSn() {
            return certSn;
        }

        public void setCertSn(String certSn) {
            this.certSn = certSn;
        }
    }

    public static class MultiCert {
        /**
         * 主登录者信息
         */
        private Cert mainCert;
        /**
         * 其它登录者信息
         */
        List<Cert> otherCerts;

        public Cert getMainCert() {
            return mainCert;
        }

        public void setMainCert(Cert mainCert) {
            this.mainCert = mainCert;
        }

        public List<Cert> getOtherCerts() {
            return otherCerts;
        }

        public void setOtherCerts(List<Cert> otherCerts) {
            this.otherCerts = otherCerts;
        }
    }

    /**
     * ssl登录
     *  登录的时候，能够从网关携带的cookie中获取到对应的证书序列号
     */
    public static class Ssl {
        
       private String certSn;

        public String getCertSn() {
            return certSn;
        }

        public void setCertSn(String certSn) {
            this.certSn = certSn;
        }
    }

    public static class BasicAuth{

        /**
         * 请求头凭证数据
         */
        private String header;

        public String getHeader() {
            return header;
        }

        public void setHeader(String header) {
            this.header = header;
        }
    }

    public static class DigestAuth {

        /**
         * 请求头摘要凭证数据
         */
        private String digestHeader;

        /**
         * 请求的资源
         */
        private String resource;

        public String getDigestHeader() {
            return digestHeader;
        }

        public void setDigestHeader(String digestHeader) {
            this.digestHeader = digestHeader;
        }

        public String getResource() {
            return resource;
        }

        public void setResource(String resource) {
            this.resource = resource;
        }
    }

    public static class IcbcSso {

        /**
         * 签名auth
         */
        private String sslAuth;

        /**
         * 签名值
         */
        private String sslSign;

        public String getSslAuth() {
            return sslAuth;
        }

        public void setSslAuth(String sslAuth) {
            this.sslAuth = sslAuth;
        }

        public String getSslSign() {
            return sslSign;
        }

        public void setSslSign(String sslSign) {
            this.sslSign = sslSign;
        }
    }

    public List<String> getAuthnTypes() {
        return authnTypes;
    }

    public void setExtending(Object extending) {
        this.extending = extending;
    }

    public Object getExtending() {
        return extending;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Pwd getPwd() {
        return pwd;
    }

    public void setPwd(Pwd pwd) {
        this.pwd = pwd;
    }

    public Cert getCert() {
        return cert;
    }

    public void setCert(Cert cert) {
        this.cert = cert;
    }

    public Ssl getSsl() {
        return ssl;
    }

    public MultiCert getMultiCert() {
        return multiCert;
    }

    public void setMultiCert(MultiCert multiCert) {
        this.multiCert = multiCert;
    }

    public void setSsl(Ssl ssl) {
        this.ssl = ssl;
    }

    public BasicAuth getBasicAuth() {
        return basicAuth;
    }

    public void setBasicAuth(BasicAuth basicAuth) {
        this.basicAuth = basicAuth;
    }

    public DigestAuth getDigestAuth() {
        return digestAuth;
    }

    public void setDigestAuth(DigestAuth digestAuth) {
        this.digestAuth = digestAuth;
    }
}
