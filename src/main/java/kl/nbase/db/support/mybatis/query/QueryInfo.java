package kl.nbase.db.support.mybatis.query;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.validation.constraints.Pattern;
import kl.nbase.db.exception.DbInternalError;
import kl.nbase.helper.utils.ReflectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static kl.nbase.db.constant.DBI18N.INVALID_SORT_FIELD_NAME;
import static kl.nbase.db.constant.DBI18N.INVALID_SORT_TYPE;

/**
 * 所有条件查询的父类，可以使用toQueryWrapper()方法转成QueryWrapper对象，也可直接作为Mapper方法的参数
 *
 * <AUTHOR>
 * Created on 2022/10/05 15:30
 */
public class QueryInfo {

	/**
	 * 开始时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime leCreateAt;
	/**
	 * 结束时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime geCreateAt;
	/**
	 * 是否快速查询
	 */
	private boolean quick;

	/**
	 * 排序字段
	 */
	@Pattern(regexp = "^[A-Za-z][A-Za-z0-9_]{0,127}$", message = INVALID_SORT_FIELD_NAME)
	private String sortField;

	/**
	 * 排序方式，参见{@link SortType#getSortCode()}
	 */
	@Pattern(regexp = "^[01]$", message = INVALID_SORT_TYPE)
	private String sortType;

	/**
	 * 构造查询对象
	 * 为了不影响之前的接口调用, 默认为false, 及查询条件可为空
	 *
	 * @return QueryWrapper
	 */
	@SuppressWarnings("all")
	public <T> QueryWrapper<T> toQueryWrapper() {
		return buildQueryWrapper(Boolean.FALSE);
	}

	/**
	 * 构造查询对象
	 *
	 * @param ignoreNullValue 查询条件是否忽略空值, true:查询条件空值不做处理, false:查询条件空值也会构造条件
	 * @return QueryWrapper
	 */
	@SuppressWarnings("all")
	public <T> QueryWrapper<T> buildQueryWrapper(boolean ignoreNullValue) {
		QueryWrapper<T> queryWrapper = new QueryWrapper<>();
		Field[] fields = getAllFields();
		try {
			for (Field field : fields) {
				ReflectionUtils.makeAccessible(field);
				Object value = field.get(this);
				if (value != null) {
					QueryField[] annotations = field.getAnnotationsByType(QueryField.class);
					QueryFieldOpType opType = QueryFieldOpType.EQ; // 默认按eq处理
					String fieldName = field.getName(); // 默认按当前字段名处理
					if (annotations.length > 0) {
						// 判断是否有注解
						opType = annotations[0].op();
						if (StringUtils.isNotEmpty(annotations[0].value())) {
							fieldName = annotations[0].value();
						}
					} else {
						continue;
					}
					// 快速查询,条件查询结果相或
					if (getQuick()) {
						queryWrapper.or();
					}
					opType.wrapperQuery(fieldName, value, queryWrapper, ignoreNullValue);
				}
			}
		} catch (IllegalAccessException e) {
			throw DbInternalError.SYSTEM_ERROR.toException(e);
		}

		// 排序字段不为空，则设置排序
		queryWrapper.orderBy(StringUtils.isNotBlank(sortField), isAsc(), sortField);

		return queryWrapper;
	}

	/**
	 * 获取当前类以及父类的所有字段
	 *
	 * @return 当前类以及父类的所有字段
	 */
	private Field[] getAllFields() {
		List<Field> fieldList = new ArrayList<>();
		Class<?> intermediateClass = this.getClass();
		while (!QueryInfo.class.equals(intermediateClass)) {
			fieldList.addAll(Arrays.asList(intermediateClass.getDeclaredFields()));
			intermediateClass = intermediateClass.getSuperclass();
		}
		return fieldList.toArray(new Field[0]);
	}

    public LocalDateTime getLeCreateAt() {
        return leCreateAt;
    }

    public void setLeCreateAt(LocalDateTime leCreateAt) {
        this.leCreateAt = leCreateAt;
    }

    public LocalDateTime getGeCreateAt() {
        return geCreateAt;
    }

    public void setGeCreateAt(LocalDateTime geCreateAt) {
        this.geCreateAt = geCreateAt;
    }

	public boolean getQuick() {
		return quick;
	}

	public QueryInfo setQuick(boolean quick) {
		this.quick = quick;
		return this;
	}

	public String getSortField() {
		return sortField;
	}

	public QueryInfo setSortField(String sortField) {
		this.sortField = sortField;
		return this;
	}

	public String getSortType() {
		return sortType;
	}

	public QueryInfo setSortType(String sortType) {
		this.sortType = sortType;
		return this;
	}

	public boolean isAsc() {
		return SortType.isAsc(this.sortType);
	}
}
