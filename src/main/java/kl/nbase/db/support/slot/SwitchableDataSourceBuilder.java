package kl.nbase.db.support.slot;

import com.alibaba.druid.pool.DruidDataSource;
import kl.nbase.db.constant.DbRoleTypeEnum;
import kl.nbase.db.core.DataSourceContext;
import kl.nbase.db.core.SwitchableDataSource;
import kl.nbase.db.exception.DbInternalError;
import kl.nbase.db.support.druid.DruidDataSourceBuilder;
import kl.nbase.db.support.druid.SlotDruidDataSourceProperties;
import kl.nbase.db.support.dynamic.DynamicDataSource;
import kl.nbase.db.support.dynamic.DynamicDataSourceBuilder;
import kl.nbase.db.support.service.DbAutoSwitchService;
import kl.nbase.db.support.sharding.ShardingUtils;
import kl.nbase.db.support.sharding.config.ShardingConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.shardingsphere.driver.api.ShardingSphereDataSourceFactory;
import org.apache.shardingsphere.infra.config.rule.RuleConfiguration;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static kl.nbase.db.constant.DBI18N.INSTRUCTION_MULTI_MASTER_MULTI_SLAVE_CONFIGURATION;

/**
 * <AUTHOR>
 * @date 2022/12/22 20:06
 * @Description: SwitchableDataSource 构建类
 */
public class SwitchableDataSourceBuilder {
    private SwitchableDataSourceBuilder() {
    }

    /**
     * 构造SwitchableDataSource
     *
     * @param switchableDataSourceConfig
     * @param globalConfig
     * @return
     * @throws SQLException
     */
    public static SwitchableDataSource build(SwitchableDataSourceConfig switchableDataSourceConfig,
                                             ShardingConfig globalConfig) throws SQLException {
        Pair<Map<String, DataSource>, Map<String, List<DruidDataSource>>> mapPair =
            buildSwitchDataSource(switchableDataSourceConfig, globalConfig);

        return new SwitchableDataSource(mapPair.getLeft(), mapPair.getRight());
    }

    /**
     * 刷新datasource
     *
     * @param switchableDataSource       原switchableDataSource对象
     * @param switchableDataSourceConfig 新增的slotConfig
     * @param globalConfig               全局sharding配置
     * @return SwitchableDataSource  刷新的数据源
     * @throws SQLException
     */
    public static synchronized SwitchableDataSource refresh(SwitchableDataSource switchableDataSource,
                                                            SwitchableDataSourceConfig switchableDataSourceConfig,
                                                            ShardingConfig globalConfig) throws SQLException {
        Pair<Map<String, DataSource>, Map<String, List<DruidDataSource>>> mapPair =
            buildSwitchDataSource(switchableDataSourceConfig, globalConfig);
        switchableDataSource.getDataSources()
            .clear();
        switchableDataSource.getDataSources()
            .putAll(mapPair.getLeft());
        switchableDataSource.getAllDruidDataSources()
            .clear();
        switchableDataSource.getAllDruidDataSources()
            .putAll(mapPair.getRight());
        return switchableDataSource;
    }

    /**
     * 刷新datasource
     *
     * @param switchableDataSource 原switchableDataSource对象
     * @param slotDataSourceConfig 新增或者更新的slotConfig
     * @param globalConfig         全局sharding配置
     * @return SwitchableDataSource  刷新的数据源
     * @throws SQLException
     */
    public static synchronized SwitchableDataSource refreshBySlot(SwitchableDataSource switchableDataSource,
                                                                  SlotDataSourceConfig slotDataSourceConfig,
                                                                  ShardingConfig globalConfig) throws SQLException {
        Map<String, List<DruidDataSource>> allDruidDataSources = switchableDataSource.getAllDruidDataSources();
        Map<String, DataSource> dataSources = switchableDataSource.getDataSources();
        try {
            switchableDataSource.writeLock();
            Pair<List<DruidDataSource>, DataSource> mapPair = buildDataSource(switchableDataSource, slotDataSourceConfig, globalConfig);
            String slotName = slotDataSourceConfig.getSlotName();
            allDruidDataSources.put(slotName, mapPair.getLeft());
            dataSources.put(slotName, mapPair.getRight());
            // 重置主库、从库索引
            DataSourceContext.setMasterIndex(slotDataSourceConfig.getSlotName(), 0);
            DataSourceContext.setSlaveIndex(slotDataSourceConfig.getSlotName(), 0);
            return switchableDataSource;
        } finally {
            switchableDataSource.writeUnlock();
        }
    }

    private static Pair<Map<String, DataSource>, Map<String, List<DruidDataSource>>> buildSwitchDataSource(SwitchableDataSourceConfig switchableDataSourceConfig, ShardingConfig globalConfig) throws SQLException {
        List<SlotDataSourceConfig> databaseList = switchableDataSourceConfig.getSwitchable();
        if (CollectionUtils.isEmpty(databaseList)) {
            throw DbInternalError.LACK_DB_CONF_ERROR.toException();
        }
        Map<String, DataSource> shardingDataSourceList = new ConcurrentHashMap<>(databaseList.size());
        Map<String, List<DruidDataSource>> druidDataSourceList = new ConcurrentHashMap<>(databaseList.size());
        for (SlotDataSourceConfig slotDataSourceConfig : databaseList) {
            Pair<List<DruidDataSource>, DataSource> dataSourcePair = buildDataSource(null, slotDataSourceConfig, globalConfig);
            String slotName = slotDataSourceConfig.getSlotName();
            shardingDataSourceList.put(slotName, dataSourcePair.getRight());
            druidDataSourceList.put(slotName, dataSourcePair.getLeft());
        }
        return Pair.of(shardingDataSourceList, druidDataSourceList);
    }


    private static Pair<List<DruidDataSource>, DataSource> buildDataSource(SwitchableDataSource oldSwitchableDataSource,
                                                                           SlotDataSourceConfig slotDataSourceConfig,
                                                                           ShardingConfig globalConfig) throws SQLException {
        ShardingConfig shardingConfig = slotDataSourceConfig.getSharding();
        // 优先使用slot中sharding配置，如果slot中sharding配置为空，使用全局sharding配置
        if (ObjectUtils.isEmpty(shardingConfig) && ObjectUtils.isNotEmpty(globalConfig)) {
            shardingConfig = globalConfig;
        }

        SlotDruidDataSourceProperties druid = slotDataSourceConfig.getDruid();
        List<DruidDataSource> druidDataSources = DruidDataSourceBuilder.build(slotDataSourceConfig.getDatasource(), druid);
        Map<String, DataSource> druidDataSourceMap = ShardingUtils.initDataSourceMap(druidDataSources);

        //判断是否启用了多主多从
        boolean multiMastersAndSlavesEnabled = checkIfMultiMastersAndSlavesEnabled(druidDataSourceMap.keySet());
        shardingConfig.setMultiMastersAndSlavesEnabled(multiMastersAndSlavesEnabled);

        Collection<RuleConfiguration> ruleConfigs = ObjectUtils.isEmpty(shardingConfig) ? Collections.emptyList() :
            ShardingUtils.initShardingRuleConfiguration(shardingConfig);

        DataSource dataSource;

        String slotName = slotDataSourceConfig.getSlotName();
//        if(slotName.equals("default")){
//            slotName = "ca3";
//        }
        DbAutoSwitchService.shutdownDbHealthCheckListener(slotName);

        if (Objects.nonNull(oldSwitchableDataSource)) {
            oldSwitchableDataSource.close(slotName);
        }

        if (multiMastersAndSlavesEnabled) {
            Map<String, DataSource> dynamicDataSourceMap = new HashMap<>(2);
            for (DbRoleTypeEnum dbRoleType : DbRoleTypeEnum.values()) {
                DynamicDataSource dynamicDataSource = DynamicDataSourceBuilder.build(druidDataSources, dbRoleType);
                dynamicDataSourceMap.put(dbRoleType.getValue(), dynamicDataSource);
            }
            DbAutoSwitchService.switchDataSource(slotName, dynamicDataSourceMap, true);
            dataSource = ShardingSphereDataSourceFactory
                .createDataSource(slotName, dynamicDataSourceMap, ruleConfigs, shardingConfig.getProps());
            DataSourceContext.setDynamicDataSourceMap(slotName, dynamicDataSourceMap);
        } else {
            if (shardingConfig.isReadWriteEnabled()) {
                throw DbInternalError.LACK_DB_CONF_ERROR.toException(INSTRUCTION_MULTI_MASTER_MULTI_SLAVE_CONFIGURATION);
            }
            dataSource = ShardingSphereDataSourceFactory
                .createDataSource(slotName, druidDataSourceMap, ruleConfigs, shardingConfig.getProps());
            DataSourceContext.setDynamicDataSourceMap(slotName, new HashMap<>());
        }
        DbAutoSwitchService.createOrRefreshDbHealthListener(slotName, shardingConfig.getHealthCheck());
        return Pair.of(druidDataSources, dataSource);
    }

    private static boolean checkIfMultiMastersAndSlavesEnabled(Set<String> keySet) {
        if (CollectionUtils.isEmpty(keySet)) {
            return false;
        }
        //判断是否有多个master开头的key
        long masterCount = keySet.stream()
            .filter(key -> key.toLowerCase(Locale.ROOT).startsWith(DbRoleTypeEnum.MASTER.getValue()))
            .count();
        long slaveCount = keySet.stream()
            .filter(key -> key.toLowerCase(Locale.ROOT).startsWith(DbRoleTypeEnum.SLAVE.getValue()))
            .count();
        return masterCount >= 1 && slaveCount >= 1;
    }
}
