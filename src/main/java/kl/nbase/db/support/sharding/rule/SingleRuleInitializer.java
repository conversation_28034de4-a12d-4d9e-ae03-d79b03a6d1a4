package kl.nbase.db.support.sharding.rule;

import kl.nbase.db.support.sharding.config.ShardingConfig;
import org.apache.shardingsphere.infra.config.rule.RuleConfiguration;
import org.apache.shardingsphere.single.api.config.SingleRuleConfiguration;

import java.util.Collection;

/**
 * @Author: guoq
 * @Date: 2023/8/22
 * @description: sharding 5.4开始，单表不在自动加载，交给开发者管理，
 * 如果未加载单表使用时返回报错 NoSuchTableException
 */
public class SingleRuleInitializer implements RuleInitializer {
    @Override
    public RuleConfiguration initRuleConfiguration(ShardingConfig shardingConfig) {
        SingleRuleConfiguration singleRuleConfiguration = new SingleRuleConfiguration();
        Collection<String> tables = singleRuleConfiguration.getTables();
        // 默认加载所有库所有单表
        tables.add("*.*");
        // PostgreSQL 风格 加载所有单表
        tables.add("ds0.nkmgq.*");
        return singleRuleConfiguration;
    }

    @Override
    public boolean isSupport(ShardingConfig shardingConfig) {
        return true;
    }
}
