package kl.nbase.db.support.sharding;

import com.alibaba.druid.pool.DruidDataSource;
import kl.nbase.db.support.sharding.config.ShardingConfig;
import kl.nbase.db.support.sharding.rule.ReadWriteRuleInitializer;
import kl.nbase.db.support.sharding.rule.RuleInitializer;
import kl.nbase.db.support.sharding.rule.SingleRuleInitializer;
import kl.nbase.db.support.sharding.rule.TableShardingRuleInitializer;
import org.apache.shardingsphere.infra.config.rule.RuleConfiguration;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Created on 2022/08/02 11:26
 */
public class ShardingUtils {

    private static final List<RuleInitializer> ruleInitializers = new ArrayList<>();

    static {
        ruleInitializers.add(new TableShardingRuleInitializer());
        ruleInitializers.add(new ReadWriteRuleInitializer());
//        ruleInitializers.add(new SingleRuleInitializer());
    }

    private ShardingUtils() {
    }

    /**
     * 构建多数据源的ShardingDataSource
     */
    public static Map<String, DataSource> initDataSourceMap(List<DruidDataSource> druidDataSources) {
        Map<String, DataSource> dataSourceMap = new LinkedHashMap<>();
        druidDataSources.forEach(dataSource -> dataSourceMap.put(dataSource.getName(), dataSource));
        return dataSourceMap;
    }

    public static List<RuleConfiguration> initShardingRuleConfiguration(ShardingConfig shardingConfig) {
        List<RuleConfiguration> configurations = new ArrayList<>();
        ruleInitializers.stream()
            .filter(r -> r.isSupport(shardingConfig))
            .forEach(r -> configurations.add(r.initRuleConfiguration(shardingConfig)));
        return configurations;
    }

}
