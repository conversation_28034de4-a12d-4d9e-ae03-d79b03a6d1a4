/**
 * @projectName npki-ca-management
 * @package kl.nbase.db.utils
 * @className kl.nbase.db.support.sharding.ShardingTableUtil
 */
package kl.nbase.db.support.sharding;

import kl.nbase.db.core.DataSourceContext;
import kl.nbase.db.core.SwitchableDataSource;
import kl.nbase.db.exception.DbInternalError;
import kl.nbase.db.exception.DbValidationError;
import kl.nbase.helper.utils.CheckUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.driver.jdbc.core.datasource.ShardingSphereDataSource;
import org.apache.shardingsphere.infra.datanode.DataNode;
import org.apache.shardingsphere.infra.metadata.database.ShardingSphereDatabase;
import org.apache.shardingsphere.infra.metadata.database.schema.model.*;
import org.apache.shardingsphere.infra.rule.ShardingSphereRule;
import org.apache.shardingsphere.infra.rule.identifier.type.TableNamesMapper;
import org.apache.shardingsphere.mode.manager.ContextManager;
import org.apache.shardingsphere.sharding.rule.ShardingRule;
import org.apache.shardingsphere.sharding.rule.TableRule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static kl.nbase.db.utils.DataSourceUtil.getShardingTables;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/2 17:34
 * @description 使用sharding 进行动态分表,根据配置的分表规则将创建的分表加载到数据源中
 */
public class ShardingTableUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(ShardingTableUtils.class);

    /**
     * 数据库元数据字段-列名
     */
    private static final String COLUMN_NAME_KEY = "COLUMN_NAME";

    /**
     * 数据库元数据字段-列类型
     */
    private static final String DATA_TYPE_KEY = "DATA_TYPE";

    /**
     * 数据库元数据字段-是否自增
     */
    private static final String AUTO_INCREMENT_KEY = "IS_AUTOINCREMENT";

    /**
     * 数据库元数据字段-是否可空
     */
    private static final String NULLABLE_KEY = "NULLABLE";

    /**
     * 数据库元数据字段-索引名
     */
    private static final String INDEX_NAME_KEY = "INDEX_NAME";

    /**
     * 数据库元数据字段-主键名
     */
    private static final String PRIMARY_KEY_NAME_KEY = "PK_NAME";

    private ShardingTableUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 刷新指定表的分表节点，通过逻辑表以及逻辑表分表正则进行过滤分表并刷新
     *
     * @param logicalTableName   逻辑表名，不允许为空
     * @param shardingTableRegex 分表表名正则，不允许为空
     */
    public static void refreshShardingTable(String logicalTableName, String shardingTableRegex) {

        CheckUtils.notEmpty(logicalTableName, DbValidationError.PARAM_NOT_NULL.toException("logicalTableName"));
        CheckUtils.notEmpty(shardingTableRegex, DbValidationError.PARAM_NOT_NULL.toException("shardingTableRegex"));

        Pattern shardingTablePattern = Pattern.compile(shardingTableRegex);
        List<String> shardingTables = getShardingTables(shardingTablePattern);
        if (CollectionUtils.isEmpty(shardingTables)) {
            return;
        }

        refreshShardingTable(logicalTableName, shardingTables);
    }

    /**
     * 刷新指定表的分表节点
     *
     * @param logicalTableName 逻辑表名
     * @param splitTables      分表表名集合
     */
    public static void refreshShardingTable(String logicalTableName, Collection<String> splitTables) {

        try {
            // 将表名转成大写
            splitTables = splitTables.stream().map(String::toUpperCase).collect(Collectors.toList());
            String tenantId = DataSourceContext.getTenantId();
            SwitchableDataSource switchableDataSource = DataSourceContext.getSwitchableDataSource();
            ShardingSphereDataSource shardingSphereDataSource = (ShardingSphereDataSource) (switchableDataSource.getDataSources().get(tenantId));
            Class<? extends ShardingSphereDataSource> sphereDataSourceClass = shardingSphereDataSource.getClass();
            Field contextManagerField = sphereDataSourceClass.getDeclaredField("contextManager");
            contextManagerField.setAccessible(true);
            ContextManager contextManager = (ContextManager) contextManagerField.get(shardingSphereDataSource);
            ShardingSphereDatabase shardingSphereDatabase = contextManager.getMetaDataContexts().getMetaData().getDatabase(tenantId);
            if (shardingSphereDatabase == null) {
                return;
            }
            Collection<ShardingSphereRule> rules = shardingSphereDatabase.getRuleMetaData().getRules();
            ShardingRule shardingRule = null;
            for (ShardingSphereRule rule : rules) {
                if (rule instanceof ShardingRule) {
                    shardingRule = (ShardingRule) rule;
                }
            }
            if (shardingRule == null) {
                return;
            }
            TableRule tableRule = shardingRule.getTableRule(logicalTableName);
            if (null == tableRule) {
                return;
            }

            String dataSourceName = tableRule.getActualDataNodes().get(0).getDataSourceName();
            List<DataNode> newDataNodes = getDataNodes(dataSourceName, splitTables);

            if (newDataNodes.isEmpty()) {
                return;
            }
            //shardingTableDataNodes
            dynamicRefreshDatasource(dataSourceName, tableRule, newDataNodes);

            HashMap<String, LinkedList<DataNode>> shardingTableDataNodes = getShardingTableDataNodes(logicalTableName, newDataNodes);

            // 动态刷新：shardingTableDataNodes
            Field shardingTableDataNodesField = ShardingRule.class.getDeclaredField("shardingTableDataNodes");
            Field modifiersField = Field.class.getDeclaredField("modifiers");
            modifiersField.setAccessible(true);
            modifiersField.setInt(shardingTableDataNodesField, shardingTableDataNodesField.getModifiers() & ~Modifier.FINAL);
            shardingTableDataNodesField.setAccessible(true);
            shardingTableDataNodesField.set(shardingRule, shardingTableDataNodes);

            // 动态刷新：actualTableMapper
            TableNamesMapper actualTableMapper = getActualTableMapper(splitTables);
            Field actualTableMapperField = ShardingRule.class.getDeclaredField("actualTableMapper");
            modifiersField.setInt(actualTableMapperField, actualTableMapperField.getModifiers() & ~Modifier.FINAL);
            actualTableMapperField.setAccessible(true);
            actualTableMapperField.set(shardingRule, actualTableMapper);

            // 动态更新 ShardingSphereSchema 中的 tables
            Map<String, ShardingSphereSchema> schemaMap = shardingSphereDatabase.getSchemas();
            for (ShardingSphereSchema schema : schemaMap.values()) {
                updateShardingSphereSchemaTables(schema, logicalTableName, splitTables, shardingSphereDataSource);
            }

            LOGGER.info("Success to refresh sharding table for {}, refreshed tables: {}", logicalTableName, splitTables);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while refreshing sharding table for {}", logicalTableName);
            throw DbInternalError.REFRESH_SHARDING_TABLE_NODE_ERROR.toException(e);
        }
    }

    private static TableNamesMapper getActualTableMapper(Collection<String> splitTables) {
        TableNamesMapper tableNamesMapper = new TableNamesMapper();
        splitTables.forEach(tableNamesMapper::put);
        return tableNamesMapper;
    }

    private static HashMap<String, LinkedList<DataNode>> getShardingTableDataNodes(String baseTable, List<DataNode> dataNodes) {
        HashMap<String, LinkedList<DataNode>> shardingTableDataNodes = new HashMap<>();
        if (null == dataNodes || dataNodes.isEmpty()) {
            return shardingTableDataNodes;
        }
        LinkedList<DataNode> dataNodes1 = new LinkedList<>(dataNodes);

        shardingTableDataNodes.put(baseTable.toUpperCase(), dataNodes1);
        return shardingTableDataNodes;
    }

    /**
     * 获取数据节点
     */
    private static List<DataNode> getDataNodes(String dataSourceName, Collection<String> splitTables) {

        if (null == splitTables || splitTables.isEmpty()) {
            return new ArrayList<>(0);
        }

        Set<DataNode> newDataNodes = new HashSet<>();
        StringBuilder stringBuilder = new StringBuilder().append(dataSourceName).append(".");
        final int length = stringBuilder.length();

        // 设置所有表
        splitTables.forEach(splitTableName -> {
            stringBuilder.setLength(length);
            stringBuilder.append(splitTableName.toUpperCase());
            DataNode dataNode = new DataNode(stringBuilder.toString());
            newDataNodes.add(dataNode);
        });

        return new ArrayList<>(newDataNodes);
    }

    /**
     * 动态刷新数据源
     */
    private static void dynamicRefreshDatasource(String dataSourceName, TableRule tableRule, List<DataNode> newDataNodes)
        throws NoSuchFieldException, IllegalAccessException {
        Set<String> actualTables = new HashSet<>();
        Map<DataNode, Integer> dataNodeIndexMap = new HashMap<>();
        AtomicInteger index = new AtomicInteger(0);
        newDataNodes.forEach(dataNode -> {
            actualTables.add(dataNode.getTableName());
            if (index.intValue() == 0) {
                dataNodeIndexMap.put(dataNode, 0);
            } else {
                dataNodeIndexMap.put(dataNode, index.intValue());
            }
            index.incrementAndGet();
        });
        // 动态刷新：actualDataNodesField
        Field actualDataNodesField = TableRule.class.getDeclaredField("actualDataNodes");
        Field modifiersField = Field.class.getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(actualDataNodesField, actualDataNodesField.getModifiers() & ~Modifier.FINAL);
        actualDataNodesField.setAccessible(true);
        actualDataNodesField.set(tableRule, newDataNodes);
        // 动态刷新：actualTablesField
        Field actualTablesField = TableRule.class.getDeclaredField("actualTables");
        actualTablesField.setAccessible(true);
        actualTablesField.set(tableRule, actualTables);
        // 动态刷新：dataNodeIndexMapField
        Field dataNodeIndexMapField = TableRule.class.getDeclaredField("dataNodeIndexMap");
        dataNodeIndexMapField.setAccessible(true);
        dataNodeIndexMapField.set(tableRule, dataNodeIndexMap);
        // 动态刷新：datasourceToTablesMapField
        Map<String, Collection<String>> datasourceToTablesMap = new HashMap<>();
        datasourceToTablesMap.put(dataSourceName, actualTables);
        //
        Field datasourceToTablesMapField = TableRule.class.getDeclaredField("dataSourceToTablesMap");
        datasourceToTablesMapField.setAccessible(true);
        datasourceToTablesMapField.set(tableRule, datasourceToTablesMap);
    }

    /**
     * 动态更新 ShardingSphereSchema 中的 tables 映射表信息。
     * 如果逻辑表不存在，则从数据库中加载元数据并构建新的 ShardingSphereTable 对象。
     *
     * @param schema           ShardingSphereSchema 实例
     * @param logicalTableName 逻辑表名
     * @param splitTables      分表集合
     * @throws NoSuchFieldException   如果反射访问失败
     * @throws IllegalAccessException 如果字段访问权限不足
     */
    private static void updateShardingSphereSchemaTables(ShardingSphereSchema schema, String logicalTableName,
                                                         Collection<String> splitTables, ShardingSphereDataSource shardingSphereDataSource) throws NoSuchFieldException, IllegalAccessException {

        Field tablesField = ShardingSphereSchema.class.getDeclaredField("tables");
        tablesField.setAccessible(true);

        @SuppressWarnings("unchecked")
        // 当前已经加载的表数据
        Map<String, ShardingSphereTable> tableMap = (Map<String, ShardingSphereTable>) tablesField.get(schema);

        // 待添加的逻辑表和分表
        List<String> allTables = new ArrayList<>(8);
        allTables.add(logicalTableName);
        if (CollectionUtils.isNotEmpty(splitTables)) {
            allTables.addAll(splitTables);
        }

        allTables.stream().filter(StringUtils::isNotBlank).forEach(tableName -> {
            String lowerCaseTableName = tableName.toLowerCase();
            ShardingSphereTable shardingSphereTable = tableMap.get(lowerCaseTableName);
            if (Objects.isNull(shardingSphereTable)) {
                // 表不存在，则仿照Sharding创建一个
                try (Connection connection = shardingSphereDataSource.getConnection()) {
                    DatabaseMetaData metaData = connection.getMetaData();
                    String catalog = connection.getCatalog();

                    // 避免重复尝试相同的表名（适用于大小写不敏感的数据库）
                    Set<String> tables = new LinkedHashSet<>();
                    tables.add(lowerCaseTableName);
                    tables.add(lowerCaseTableName.toUpperCase());

                    // 获取列信息
                    List<ShardingSphereColumn> columns = getColumns(metaData, catalog, null, tables);
                    // 获取索引信息
                    List<ShardingSphereIndex> indexes = getIndexes(metaData, catalog, null, tables);
                    // 获取主键约束信息
                    List<ShardingSphereConstraint> constraints = getConstraints(metaData, catalog, null, tables);

                    shardingSphereTable = new ShardingSphereTable(lowerCaseTableName, columns, indexes, constraints);
                } catch (SQLException e) {
                    LOGGER.error("Failed to load metadata for table: {}", lowerCaseTableName, e);
                }

                if (shardingSphereTable != null) {
                    tableMap.put(lowerCaseTableName, shardingSphereTable);
                }
            }
        });

        tablesField.set(schema, tableMap);
    }


    /**
     * 获取指定表的列信息。
     * 若使用原始表名未查询到列信息，则尝试使用大写表名再次查询。
     *
     * @param metaData   数据库元数据对象
     * @param catalog    数据库目录名
     * @param schemaName 数据库模式名
     * @param tables     表名
     * @return 列信息列表
     * @throws SQLException SQL异常
     */
    private static List<ShardingSphereColumn> getColumns(DatabaseMetaData metaData, String catalog, String schemaName, Set<String> tables) throws SQLException {
        return tryMultipleTableNames(
            tables,
            tn -> {
                try {
                    return metaData.getColumns(catalog, schemaName, tn, null);
                } catch (SQLException e) {
                    LOGGER.error("Failed to get columns for table: {}", tn, e);
                }
                return null;
            },
            ShardingTableUtils::addColumn
        );
    }


    /**
     * 获取指定表的索引信息。
     *
     * @param metaData   数据库元数据对象
     * @param catalog    数据库目录名
     * @param schemaName 数据库模式名
     * @param tables     表名
     * @return 索引信息列表
     * @throws SQLException SQL异常
     */
    private static List<ShardingSphereIndex> getIndexes(DatabaseMetaData metaData, String catalog, String schemaName, Set<String> tables) throws SQLException {
        return tryMultipleTableNames(
            tables,
            tn -> {
                try {
                    return metaData.getIndexInfo(catalog, schemaName, tn, false, true);
                } catch (SQLException e) {
                    LOGGER.error("Failed to get columns for table: {}", tn, e);
                }
                return null;
            },
            ShardingTableUtils::addIndex
        );
    }


    /**
     * 获取指定表的主键约束信息。
     *
     * @param metaData   数据库元数据对象
     * @param catalog    数据库目录名
     * @param schemaName 数据库模式名
     * @param tables     表名
     * @return 主键约束信息列表
     * @throws SQLException SQL异常
     */
    private static List<ShardingSphereConstraint> getConstraints(DatabaseMetaData metaData, String catalog, String schemaName, Set<String> tables) throws SQLException {
        List<ShardingSphereConstraint> constraints = new ArrayList<>();

        tryMultipleTableNames(
            tables,
            tableCandidate -> {
                try {
                    return metaData.getPrimaryKeys(catalog, schemaName, tableCandidate);
                } catch (SQLException e) {
                    LOGGER.error("Failed to get primary keys for table: {}", tableCandidate, e);
                }
                return null;
            },
            rs -> {
                try {
                    String pkName = rs.getString(PRIMARY_KEY_NAME_KEY);
                    if (pkName != null) {
                        constraints.add(new ShardingSphereConstraint(pkName, tables.stream().findFirst().get().toLowerCase()));
                    }
                } catch (SQLException e) {
                    LOGGER.error("Error reading constraint metadata", e);
                }
                return true;
            }
        );

        return constraints;
    }


    /**
     * 将 ResultSet 行映射为 ShardingSphereColumn 对象。
     *
     * @param rs 结果集
     * @return 列对象
     */
    private static ShardingSphereColumn addColumn(ResultSet rs) {
        try {
            boolean isCaseSensitive = false;
            boolean isVisible = true;
            boolean isUnsigned = false;
            String columnName = rs.getString(COLUMN_NAME_KEY);
            int dataType = rs.getInt(DATA_TYPE_KEY);
            boolean isPrimaryKey = false;
            boolean isGenerated = Boolean.parseBoolean(rs.getString(AUTO_INCREMENT_KEY));
            boolean isNullable = rs.getInt(NULLABLE_KEY) == DatabaseMetaData.columnNullable;

            return new ShardingSphereColumn(columnName, dataType, isPrimaryKey, isGenerated, isCaseSensitive, isVisible, isUnsigned, isNullable);
        } catch (SQLException e) {
            LOGGER.error("Error reading column metadata", e);
            return null;
        }
    }

    /**
     * 将 ResultSet 行映射为 ShardingSphereIndex 对象。
     *
     * @param rs 结果集
     * @return 索引对象
     */
    private static ShardingSphereIndex addIndex(ResultSet rs) {
        try {
            String indexName = rs.getString(INDEX_NAME_KEY);
            return new ShardingSphereIndex(indexName);
        } catch (SQLException e) {
            LOGGER.error("Error reading index metadata", e);
            return null;
        }
    }

    /**
     * 处理结果集，并将每一行数据通过 rowMapper 映射为对象后添加到结果列表中。
     *
     * @param rs        结果集（不可为 null）
     * @param rowMapper 用于将 ResultSet 映射为指定类型 T 的函数式接口
     * @param results   存储映射结果的列表（不可为 null）
     * @throws SQLException 当遍历 ResultSet 出现异常时抛出
     */
    private static <T> void processResultSet(ResultSet rs, Function<ResultSet, T> rowMapper, List<T> results) throws SQLException {
        while (rs.next()) {
            T result = rowMapper.apply(rs);
            if (result != null) {
                results.add(result);
            }
        }
    }


    /**
     * 尝试使用多个表名来执行数据库操作，并返回第一个成功的结果。
     *
     * @param tableNames
     * @param resultSetSupplier
     * @param rowMapper
     * @param <T>
     * @return
     * @throws SQLException
     */
    private static <T> List<T> tryMultipleTableNames(
        Iterable<String> tableNames,
        Function<String, ResultSet> resultSetSupplier,
        Function<ResultSet, T> rowMapper) throws SQLException {
        List<T> results = new ArrayList<>();
        for (String currentTableName : tableNames) {
            try (ResultSet rs = resultSetSupplier.apply(currentTableName)) {
                if (rs == null) {
                    // 结果集为空，可能是因为表名大小写敏感，继续下一个循环
                    break;
                }
                // 处理结果集，ResultSet在使用处进行遍历
                processResultSet(rs, rowMapper, results);
            }
        }

        return results;
    }
}
