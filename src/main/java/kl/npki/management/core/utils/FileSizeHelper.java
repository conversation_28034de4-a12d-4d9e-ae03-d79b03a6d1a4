package kl.npki.management.core.utils;

/**
 * 文件大小转换工具类，用于将文件大小转换为人类可读格式。
 *
 * <AUTHOR>
 * @date 2023/07/04
 */
public class FileSizeHelper {
    /**
     * 以1024为界限
     */
    private static final long KB = 1024;
    private static final long MB = KB * 1024;
    private static final long GB = MB * 1024;
    private static final long TB = GB * 1024;
    /**
     * 最小日志容量限制
     */
    public static final long MIN_LOG_FILE_SIZE = GB;
    /**
     * 最大日志容量限制
     */
    public static final long MAX_LOG_FILE_SIZE = TB;

    /**
     * 将给定的字节数转换为人类可读格式。
     *
     * @param bytes 要转换的字节数。
     * @return 文件大小的人类可读格式字符串。
     */
    public static String humanReadableByteCount(long bytes) {
        if (bytes < KB) {
            return bytes + " B";
        } else if (bytes < MB) {
            return String.format("%.2f KB", bytes / (double) KB);
        } else if (bytes < GB) {
            return String.format("%.2f MB", bytes / (double) MB);
        } else if (bytes < TB) {
            return String.format("%.2f GB", bytes / (double) GB);
        } else {
            return String.format("%.2f TB", bytes / (double) TB);
        }
    }
}
