package kl.npki.management.core.utils;

import kl.npki.base.core.constant.DbConfigType;
import kl.npki.base.core.utils.URLCoder;
import kl.npki.management.core.biz.config.model.DbConfigInfo;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2022/9/2 13:45
 */
public class DbUrlUtil {

    private DbUrlUtil(){
        throw new IllegalStateException("Utility class");
    }
    // 默认的Firebird数据库存储路径前缀，用于Firebird采用相对路径表达时
    private static final String DEFAULT_GDB_PATH_PREFIX = AppPathUtil.INSTANCE.getRootAbsolutPath(StringUtils.EMPTY);

    public static String getUrl(DbConfigInfo dbConfigInfo, String dbTypeName) {
        // 尝试获取数据库连接
        String actualPath;
        DbConfigType dbType = DbConfigType.valueOf(dbTypeName);
        String urlPattern = dbType.getJdbcUrl();
        switch (dbType) {
            case FIREBIRD:
            case SQLITE:
                actualPath = getActualPath(dbConfigInfo.getDbName());
                break;
            default:
                actualPath = dbConfigInfo.getDbName();
        }

        String ret = urlPattern.replaceFirst("\\$\\{ip\\}", dbConfigInfo.getDbAddress());
        ret = ret.replaceFirst("\\$\\{port\\}", String.valueOf(dbConfigInfo.getDbPort()));
        if (ret.contains("charSet")) {
            ret = ret.replace("${charSet}", dbConfigInfo.getCharSet());
        }
        ret = ret.replaceFirst("\\$\\{path\\}", actualPath.replace("\\", "\\\\"));

        switch (dbType) {
            case ORACLE_RAC:
                ret = ret.replaceFirst("\\$\\{ip_rac\\}", dbConfigInfo.getSlaveIp());
                ret = ret.replaceFirst("\\$\\{port_rac\\}", String.valueOf(dbConfigInfo.getSlavePort()));
                break;
            case MYSQL:
            case MYSQL8:
            case GREATDB:
            case OCAEANBASE:
                // 将时区信息进行URL编码，如GMT+8编码为GMT%2B8
                String timeZoneForUrl = URLCoder.encode(dbConfigInfo.getTimeZone());
                ret = ret.replaceFirst("\\$\\{time_zone\\}", timeZoneForUrl);
                break;
            default:
                break;
        }
        return ret;

    }

    /**
     * 从url解析出字符集
     * TODO 完善其他数据库的字符集解析
     *
     * @param url    jdbc url
     * @param dbType 数据库类型
     * @return 字符集
     */
    public static String getCharSetFromUrl(String url, DbConfigType dbType) {
        if (StringUtils.isBlank(url) && dbType == null) {
            return StringUtils.EMPTY;
        }
        String[] split = url.split("&");

        String charsetKey = StringUtils.EMPTY;
        switch (dbType) {
            case MYSQL:
            case MYSQL8:
            case GREATDB:
            case DM:
                charsetKey = "characterEncoding";
                break;
            default:
                break;
        }

        if (StringUtils.isBlank(charsetKey)) {
            return StringUtils.EMPTY;
        }
        for (String s : split) {
            if (s.contains(charsetKey)) {
                return s.split("=")[1];
            }
        }
        return StandardCharsets.UTF_8.name();
    }

    /**
     * 从url解析出时区
     * TODO 完善其他数据库的字符集解析
     *
     * @param url          jdbc url
     * @param dbConfigType 数据库类型
     * @return 时区
     */
    public static String getTimeZoneFromUrl(String url, DbConfigType dbConfigType) {
        if (StringUtils.isBlank(url) && dbConfigType == null) {
            return StringUtils.EMPTY;
        }
        String[] split = url.split("&");
        String timeZoneKey = StringUtils.EMPTY;
        switch (dbConfigType) {
            case MYSQL:
            case MYSQL8:
            case GREATDB:
            case OCAEANBASE:
                timeZoneKey = "serverTimezone";
                break;
            default:
                break;
        }
        if (StringUtils.isBlank(timeZoneKey)) {
            return TimeZone.getDefault().getID();
        }
        String timeZoneValue = StringUtils.EMPTY;
        for (String s : split) {
            if (s.contains(timeZoneKey)) {
                timeZoneValue = s.split("=")[1];
            }
        }

        switch (dbConfigType) {
            case MYSQL:
            case MYSQL8:
            case GREATDB:
            case OCAEANBASE:
                if (StringUtils.isNotBlank(timeZoneValue)) {
                    // 将经URL编码后的时区信息转成原来的格式，如GMT%2B8转成GMT+8
                    timeZoneValue = URLCoder.decode(timeZoneValue);
                }
                break;
            default:
                break;
        }
        return timeZoneValue;
    }

    /**
     * 更新url中的时区配置
     *
     * @param url          jdbc url
     * @param timeZone     时区信息，如GMT+8、Asia/Shanghai
     * @param dbConfigType 数据库类型
     * @return
     */
    public static String updateUrlTimeZone(String url, String timeZone, DbConfigType dbConfigType) {
        switch (dbConfigType) {
            case MYSQL:
            case MYSQL8:
            case GREATDB:
            case OCAEANBASE:
                timeZone = URLCoder.encode(timeZone);
                return url.contains("serverTimezone") ? url.replaceAll("serverTimezone=([^&]+)", "serverTimezone=" + timeZone)
                        : url + "&serverTimezone=" + timeZone;
            default:
                return url;
        }
    }

    /**
     * 针对本地文件数据库采用相对路径，如"./db/firebird.gdb"类似的情况<br>
     * 将相对路径转换为绝对路径
     *
     * @param dbNamePath 　相对路径
     * @return 绝对路径
     */
    public static String getActualPath(String dbNamePath) {
        String actualPath = null;
        if (dbNamePath.startsWith(".")) {
            // 如果这是一个相对路径
            // 如果已预设置了前缀路径
            String filePath = DEFAULT_GDB_PATH_PREFIX + File.separatorChar + dbNamePath;
            try {
                actualPath = new File(filePath).getCanonicalPath();
            } catch (IOException e) {
                // 尝试性地获取路径，失败的话忽视即可
                // ignore
            }

            if (actualPath == null) {
                // 如果已预前缀路径为空或者组合路径错误，则要尝试直接转换
                actualPath = new File(dbNamePath).getAbsolutePath();
            }
        } else {//如果是绝对路径，直接等
            actualPath = dbNamePath;
        }
        return actualPath;
    }

}
