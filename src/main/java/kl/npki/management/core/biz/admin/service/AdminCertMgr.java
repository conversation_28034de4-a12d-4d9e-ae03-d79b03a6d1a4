package kl.npki.management.core.biz.admin.service;

import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.tenantholders.common.AbstractCommonTenant;
import kl.npki.base.core.thread.BaseThreadPool;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import kl.npki.management.core.repository.IAdminCertRepository;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 管理证书管理类
 *
 * <AUTHOR>
 * @date 2025/05/16
 */
public class AdminCertMgr extends AbstractCommonTenant {

    private final IAdminCertRepository adminCertRepository = RepositoryFactory.get(IAdminCertRepository.class);

    private final Map<Long, AdminCertEntity> adminCertMap = new ConcurrentHashMap<>();

    @Override
    protected void refresh() {
        // 清空缓存中的管理员信息
        adminCertMap.clear();
        // 从数据库查询管理员信息
        List<AdminCertEntity> allAdminCert = adminCertRepository.findValidAll();
        // 将需要的数据加载到缓存中: Map<id,AdminEntity>
        allAdminCert.stream()
            .filter(Objects::nonNull)
            .forEach(adminCert -> adminCertMap.put(adminCert.getAdminInfoId(), adminCert));
    }

    /**
     * 获取管理员信息
     *
     * @param adminId 管理员ID
     * @return {@link AdminEntity }
     * @date 2024/09/14 15:43
     * <AUTHOR>
     */
    public AdminCertEntity getAdminCert(Long adminId) {
        AdminCertEntity adminCertEntity = adminCertMap.get(adminId);
        // 如果缓存中存在, 直接返回
        if (adminCertEntity != null) {
            return adminCertEntity;
        }
        // 如果缓存中不存在, 则从数据库中查询
        adminCertEntity = adminCertRepository.getByAdminInfoId(adminId);
        // 如果数据库中存在
        if (adminCertEntity != null) {
            // 异步刷新缓存
            BaseThreadPool.INSTANCE.getTaskExecutor().execute(this::refreshInternal);
        }
        return adminCertEntity;
    }

    /**
     * 获取所有管理员证书信息
     *
     * @return {@link Map }<{@link Long }, {@link AdminCertEntity }>
     * @date 2024/09/14 15:48
     * <AUTHOR>
     */
    public Map<Long, AdminCertEntity> getAdminCertMap() {
        if (MapUtils.isEmpty(adminCertMap)) {
            refreshInternal();
        }
        return MapUtils.unmodifiableMap(adminCertMap);
    }


}
