package kl.npki.management.core.biz.config.model;

import java.io.Serializable;

/**
 * @Author: guoq
 * @Date: 2024/1/30
 * @description: 地址信息
 */
public class AddressInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    private String host;

    /**
     * 服务端口
     */
    private int port = 6379;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String toIpPort(){
        return host + ":" + port;
    }
}
