package kl.npki.management.core.biz.config.model;

import kl.npki.base.core.constant.BaseConstant;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 算法配置信息
 *
 * <AUTHOR>
 * @date 2024/4/12
 */
public class AlgoConfigInfo implements Serializable {

    private static final long serialVersionUID = 58360433544238324L;

    /**
     * 对称密钥算法
     */
    private List<String> symAlgo;

    /**
     * 非对称密钥算法
     */
    private List<String> asymAlgo;

    /**
     * Hash算法
     */
    private List<String> hashAlgo;

    /**
     * 抗量子密码算法
     */
    private List<String> pqcAlgo;

    /**
     * 是否已配置对称密钥算法 : 默认为false, 系统部署生成主密钥后[对称密钥算法]不可更改
     */
    private Boolean symAlgoConfigured = Boolean.FALSE;

    public AlgoConfigInfo() {

    }

    public AlgoConfigInfo(List<String> symAlgo, List<String> asymAlgo, List<String> hashAlgo, List<String> pqcAlgo) {
        this.symAlgo = symAlgo;
        this.asymAlgo = asymAlgo;
        this.hashAlgo = hashAlgo;
        this.pqcAlgo = pqcAlgo;
    }

    public AlgoConfigInfo(List<String> symAlgo, List<String> asymAlgo, List<String> hashAlgo, List<String> pqcAlgo, Boolean symAlgoConfigured) {
        this(symAlgo, asymAlgo, hashAlgo, pqcAlgo);
        this.symAlgoConfigured = symAlgoConfigured;
    }

    public List<String> getSymAlgo() {
        return symAlgo == null ? new ArrayList<>() : symAlgo;
    }

    /**
     * 获取对称密钥算法的简短名称集合
     *
     * @return {@link List }<{@link String }>
     */
    public List<String> getSimplifySymAlgo() {
        return simplifyAlgorithmNames(getSymAlgo());
    }

    /**
     * 简化算法名称
     *
     * @param supportedSymAlgorithms 支持的对称算法
     * @return {@link List }<{@link String }>
     */
    private List<String> simplifyAlgorithmNames(List<String> supportedSymAlgorithms) {
        // 使用Map来统计每个简短名称的对应的完整算法列表
        Map<String, List<String>> algoMap = new HashMap<>(supportedSymAlgorithms.size());

        for (String algo : supportedSymAlgorithms) {
            String shortName = StringUtils.substringBefore(algo, BaseConstant.SYM_ALGORITHM_SEPARATOR);
            algoMap.computeIfAbsent(shortName, k -> new ArrayList<>()).add(algo);
        }

        // 根据简短名称的统计结果来决定输出格式
        List<String> simplifiedNames = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : algoMap.entrySet()) {
            List<String> algos = entry.getValue();
            if (algos.size() == 1) {
                // 如果只有一个算法使用这个简短名称，则添加简短名称
                simplifiedNames.add(entry.getKey());
            } else {
                // 如果有多个算法使用这个简短名称，则添加所有详细名称
                simplifiedNames.addAll(algos);
            }
        }

        return simplifiedNames;
    }

    public void setSymAlgo(List<String> symAlgo) {
        this.symAlgo = symAlgo;
    }

    public List<String> getAsymAlgo() {
        return asymAlgo == null ? new ArrayList<>() : asymAlgo;
    }

    public void setAsymAlgo(List<String> asymAlgo) {
        this.asymAlgo = asymAlgo;
    }

    public List<String> getHashAlgo() {
        return hashAlgo == null ? new ArrayList<>() : hashAlgo;
    }

    public void setHashAlgo(List<String> hashAlgo) {
        this.hashAlgo = hashAlgo;
    }

    public List<String> getPqcAlgo() {
        return pqcAlgo == null ? new ArrayList<>() : pqcAlgo;
    }

    public void setPqcAlgo(List<String> pqcAlgo) {
        this.pqcAlgo = pqcAlgo;
    }

    public Boolean getSymAlgoConfigured() {
        return symAlgoConfigured;
    }

    public void setSymAlgoConfigured(Boolean symAlgoConfigured) {
        this.symAlgoConfigured = symAlgoConfigured;
    }
}
