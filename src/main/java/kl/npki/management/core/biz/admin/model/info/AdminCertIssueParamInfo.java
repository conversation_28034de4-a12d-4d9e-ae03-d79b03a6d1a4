package kl.npki.management.core.biz.admin.model.info;

import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;

/**
 * 管理员证书签发参数
 * <AUTHOR>
 */
public class AdminCertIssueParamInfo {
    /**
     * 登录的用户id
     */
    private String loginUserId;
    /**
     * 待签发的管理员实体
     */
    private AdminEntity adminEntity;
    /**
     * 待签发的管理员证书实体
     */
    private AdminCertEntity adminCertEntity;
    /**
     * 颁发者证书 (使用该证书对管理员进行颁发)
     */
    private String base64IssueCert;

    /**
     * 是否自签发(默认走自签发流程)
     */
    private Boolean selfSign = true;

    /**
     * 是否是自签发
     */
    private Boolean twinCert;

    /**
     * 抗量子加密密钥类型
     */
    private String pqEncKeyType;

    public AdminCertIssueParamInfo(AdminEntity adminEntity, AdminCertEntity adminCertEntity) {
        this.adminEntity = adminEntity;
        this.adminCertEntity = adminCertEntity;
    }


    public AdminCertIssueParamInfo(AdminEntity adminEntity, AdminCertEntity adminCertEntity, String base64IssueCert) {
        this.adminEntity = adminEntity;
        this.adminCertEntity = adminCertEntity;
        this.base64IssueCert = base64IssueCert;
    }

    public AdminCertIssueParamInfo(AdminEntity adminEntity, AdminCertEntity adminCertEntity, String base64IssueCert, String pqEncKeyType) {
        this.adminEntity = adminEntity;
        this.adminCertEntity = adminCertEntity;
        this.base64IssueCert = base64IssueCert;
        this.pqEncKeyType = pqEncKeyType;
    }

    public AdminCertIssueParamInfo(String loginUserId, AdminEntity adminEntity, AdminCertEntity adminCertEntity, String base64IssueCert) {
        this.loginUserId = loginUserId;
        this.adminEntity = adminEntity;
        this.adminCertEntity = adminCertEntity;
        this.base64IssueCert = base64IssueCert;
    }

    public AdminCertIssueParamInfo(String loginUserId, AdminEntity adminEntity, AdminCertEntity adminCertEntity, String base64IssueCert, String pqEncKeyType) {
        this.loginUserId = loginUserId;
        this.adminEntity = adminEntity;
        this.adminCertEntity = adminCertEntity;
        this.base64IssueCert = base64IssueCert;
        this.pqEncKeyType = pqEncKeyType;
    }

    public AdminCertIssueParamInfo(String loginUserId, AdminEntity adminEntity, AdminCertEntity adminCertEntity, String base64IssueCert, String pqEncKeyType, Boolean selfSign, Boolean twinCert) {
        this.loginUserId = loginUserId;
        this.adminEntity = adminEntity;
        this.adminCertEntity = adminCertEntity;
        this.base64IssueCert = base64IssueCert;
        this.pqEncKeyType = pqEncKeyType;
        this.selfSign = selfSign;
        this.twinCert = twinCert;
    }

    public String getLoginUserId() {
        return loginUserId;
    }

    public void setLoginUserId(String loginUserId) {
        this.loginUserId = loginUserId;
    }

    public AdminEntity getAdminEntity() {
        return adminEntity;
    }

    public void setAdminEntity(AdminEntity adminEntity) {
        this.adminEntity = adminEntity;
    }

    public AdminCertEntity getAdminCertEntity() {
        return adminCertEntity;
    }

    public void setAdminCertEntity(AdminCertEntity adminCertEntity) {
        this.adminCertEntity = adminCertEntity;
    }

    public String getBase64IssueCert() {
        return base64IssueCert;
    }

    public void setBase64IssueCert(String base64IssueCert) {
        this.base64IssueCert = base64IssueCert;
    }

    public String getPqEncKeyType() {
        return pqEncKeyType;
    }

    public void setPqEncKeyType(String pqEncKeyType) {
        this.pqEncKeyType = pqEncKeyType;
    }

    public Boolean getSelfSign() {
        return selfSign;
    }

    public void setSelfSign(Boolean selfSign) {
        this.selfSign = selfSign;
    }

    public Boolean getTwinCert() {
        return twinCert;
    }

    public void setTwinCert(Boolean twinCert) {
        this.twinCert = twinCert;
    }
}
