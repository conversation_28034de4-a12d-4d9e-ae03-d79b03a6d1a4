package kl.npki.management.core.biz.admin.model.dto;

import kl.nbase.db.support.mybatis.query.QueryInfo;

/**
 * <AUTHOR>
 * @date 2022/9/30
 * @desc
 */
public class AdminInfoListDTO extends QueryInfo {
    /**
     * 用户名
     */
    private String username;
    /**
     * 角色名
     */
    private String roleName;
    /**
     * 用户状态
     */
    private Integer status;
    /**
     * 证书状态
     */
    private Integer certStatus;
    /**
     * 颁发者名称
     */
    private String issuerCn;

    /**
     * 管理员组
     */
    private String adminGroup;

    public String getUsername() {
        return username;
    }

    public AdminInfoListDTO setUsername(String username) {
        this.username = username;
        return this;
    }

    public String getRoleName() {
        return roleName;
    }

    public AdminInfoListDTO setRoleName(String roleName) {
        this.roleName = roleName;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public AdminInfoListDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Integer getCertStatus() {
        return certStatus;
    }

    public AdminInfoListDTO setCertStatus(Integer certStatus) {
        this.certStatus = certStatus;
        return this;
    }

    public String getIssuerCn() {
        return issuerCn;
    }

    public AdminInfoListDTO setIssuerCn(String issuerCn) {
        this.issuerCn = issuerCn;
        return this;
    }

    public String getAdminGroup() {
        return adminGroup;
    }

    public AdminInfoListDTO setAdminGroup(String adminGroup) {
        this.adminGroup = adminGroup;
        return this;
    }

    @Override
    public String toString() {
        return "AdminInfoListDTO{" +
                "username='" + username + '\'' +
                ", roleName='" + roleName + '\'' +
                ", status=" + status +
                ", certStatus=" + certStatus +
                ", issuerCn='" + issuerCn + '\'' +
                ", adminGroup='" + adminGroup + '\'' +
                '}';
    }
}
