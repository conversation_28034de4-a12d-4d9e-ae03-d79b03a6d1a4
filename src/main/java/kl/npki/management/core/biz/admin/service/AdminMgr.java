package kl.npki.management.core.biz.admin.service;

import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.tenantholders.common.ICommonTenant;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import kl.npki.management.core.repository.IAdminInfoRepository;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 管理员管理类
 *
 * <AUTHOR>
 * @date 2024/9/14 14:21
 */
public class AdminMgr implements ICommonTenant {

    private static final IAdminInfoRepository ADMIN_INFO_REPOSITORY = RepositoryFactory.get(IAdminInfoRepository.class);

    private static final Map<Long, AdminEntity> ADMIN_INFO_MAP = new ConcurrentHashMap<>();

    private boolean alreadyLoaded = false;

    @Override
    public void reload() {
        adminEntityLoad();
        if (MapUtils.isNotEmpty(ADMIN_INFO_MAP)) {
            alreadyLoaded = true;
        }
    }

    private void adminEntityLoad() {
        // 清空缓存中的管理员信息
        ADMIN_INFO_MAP.clear();
        alreadyLoaded = false;
        // 从数据库查询管理员信息
        List<AdminEntity> allAdminInfo = ADMIN_INFO_REPOSITORY.findValidAll();
        // 将需要的数据加载到缓存中: Map<id,AdminEntity>
        allAdminInfo.forEach(adminEntity -> ADMIN_INFO_MAP.put(adminEntity.getId(), adminEntity));
    }


    @Override
    public boolean alreadyLoaded() {
        return alreadyLoaded;
    }

    /**
     * 获取管理员信息
     *
     * @param adminId 管理员ID
     * @return {@link AdminEntity }
     * @date 2024/09/14 15:43
     * <AUTHOR>
     */
    public AdminEntity getAdminInfo(Long adminId) {
        // 缓存未加载, 重新加载
        if (!alreadyLoaded) {
            reload();
        }
        // 缓存中不存在, 重新加载
        if (MapUtils.isEmpty(ADMIN_INFO_MAP) || !ADMIN_INFO_MAP.containsKey(adminId)) {
            AdminEntity adminEntity = ADMIN_INFO_REPOSITORY.getById(adminId);
            if (adminEntity != null) {
                reload();
            } else {
                return null;
            }
        }
        return ADMIN_INFO_MAP.get(adminId);
    }

    /**
     * 获取所有管理员信息
     *
     * @return {@link Map }<{@link Long }, {@link AdminEntity }>
     * @date 2024/09/14 15:48
     * <AUTHOR>
     */
    public Map<Long, AdminEntity> getAdminInfoMap() {
        if (!alreadyLoaded) {
            reload();
        }
        if (MapUtils.isEmpty(ADMIN_INFO_MAP)) {
            reload();
        }
        return MapUtils.unmodifiableMap(ADMIN_INFO_MAP);
    }


}
