package kl.npki.management.core.biz.admin.model.info;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 证书登录信息
 * <AUTHOR>
 */
public class CertLoginInfo {
    @Schema(description = "签名数据")
    private String signData;

    @Schema(description = "哈希算法")
    private String hashAlgo;

    @Schema(description = "证书序列号")
    private String certSn;

    public String getSignData() {
        return signData;
    }

    public void setSignData(String signData) {
        this.signData = signData;
    }

    public String getHashAlgo() {
        return hashAlgo;
    }

    public void setHashAlgo(String hashAlgo) {
        this.hashAlgo = hashAlgo;
    }

    public String getCertSn() {
        return certSn;
    }

    public void setCertSn(String certSn) {
        this.certSn = certSn;
    }
}
