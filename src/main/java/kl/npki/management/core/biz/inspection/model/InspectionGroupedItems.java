package kl.npki.management.core.biz.inspection.model;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 系统巡检已分组巡检项
 *
 * <AUTHOR>
 * @date 07/05/2025 19:56
 **/
public class InspectionGroupedItems {

    /**
     * 巡检项目类型
     */
    @Schema(description = "巡检项目类型")
    private String category;

    /**
     * 巡检项目名称
     */
    @Schema(description = "巡检项目名称列表")
    private List<String> items;

    public InspectionGroupedItems(String category, List<String> items) {
        this.category = category;
        this.items = items;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public List<String> getItems() {
        return items;
    }

    public void setItems(List<String> items) {
        this.items = items;
    }
}
