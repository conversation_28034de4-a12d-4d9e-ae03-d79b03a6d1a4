package kl.npki.management.core.biz.admin.model.info;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/9/29
 * @desc
 */
public class AdminInfoListInfo {

    /**
     * 用户ID
     */
    private Long adminInfoId;

    /**
     * 用户角色名称
     */
    private String roleName;

    /**
     * 角色名国际化key
     */
    private String roleNameI18nKey;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 人员名称
     */
    private String username;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 组织
     */
    private String organization;

    /**
     * 机构
     */
    private String organizationUnit;

    /**
     * 所属组织机构ID
     */
    private Long orgId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 颁发者名称
     */
    private String issuerCn;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 审核状态
     */
    private Integer status;

    /**
     * 证书状态
     */
    private Integer certStatus;

    /**
     * 是否导入
     */
    private Boolean imported;

    /**
     * 证书有效期--起始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime validStart;

    /**
     * 证书有效期--结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime validEnd;

    /**
     * 证书序列号
     */
    private String signCertSn;

    private String encCertSn;

    /**
     * 账号被锁定时的时间
     */
    private LocalDateTime lockAt;

    public Long getAdminInfoId() {
        return adminInfoId;
    }

    public void setAdminInfoId(Long adminInfoId) {
        this.adminInfoId = adminInfoId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getRoleNameI18nKey() {
        return roleNameI18nKey;
    }

    public void setRoleNameI18nKey(String roleNameI18nKey) {
        this.roleNameI18nKey = roleNameI18nKey;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public String getOrganizationUnit() {
        return organizationUnit;
    }

    public void setOrganizationUnit(String organizationUnit) {
        this.organizationUnit = organizationUnit;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getIssuerCn() {
        return issuerCn;
    }

    public void setIssuerCn(String issuerCn) {
        this.issuerCn = issuerCn;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCertStatus() {
        return certStatus;
    }

    public void setCertStatus(Integer certStatus) {
        this.certStatus = certStatus;
    }

    public LocalDateTime getValidStart() {
        return validStart;
    }

    public void setValidStart(LocalDateTime validStart) {
        this.validStart = validStart;
    }

    public LocalDateTime getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(LocalDateTime validEnd) {
        this.validEnd = validEnd;
    }

    public String getSignCertSn() {
        return signCertSn;
    }

    public void setSignCertSn(String signCertSn) {
        this.signCertSn = signCertSn;
    }

    public String getEncCertSn() {
        return encCertSn;
    }

    public void setEncCertSn(String encCertSn) {
        this.encCertSn = encCertSn;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public LocalDateTime getLockAt() {
        return lockAt;
    }

    public void setLockAt(LocalDateTime lockAt) {
        this.lockAt = lockAt;
    }

    public Boolean getImported() {
        return imported;
    }

    public void setImported(Boolean imported) {
        this.imported = imported;
    }
}
