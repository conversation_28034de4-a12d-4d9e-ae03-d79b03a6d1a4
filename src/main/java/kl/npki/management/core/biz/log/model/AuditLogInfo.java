package kl.npki.management.core.biz.log.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import kl.npki.base.core.constant.AuditStatusEnum;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 日志审计list响应
 *
 * <AUTHOR>
 * @date 2023/3/21
 */
public class AuditLogInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long logId;

    /**
     * 操作名称
     */
    private String logDo;

    private String logWhat;

    /**
     * 操作结果id
     */
    private boolean result;

    /**
     * 客户端ip
     */
    private String clientIp;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime logWhen;

    private String request;

    /**
     * 审计状态
     */
    private AuditStatusEnum auditStatus;

    /**
     * 审计时间
     */
    private LocalDateTime auditTime;

    private String username;

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public String getLogDo() {
        return logDo;
    }

    public void setLogDo(String logDo) {
        this.logDo = logDo;
    }

    public String getLogWhat() {
        return logWhat;
    }

    public void setLogWhat(String logWhat) {
        this.logWhat = logWhat;
    }

    public boolean isResult() {
        return result;
    }

    public void setResult(boolean result) {
        this.result = result;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public LocalDateTime getLogWhen() {
        return logWhen;
    }

    public void setLogWhen(LocalDateTime logWhen) {
        this.logWhen = logWhen;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public AuditStatusEnum getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(AuditStatusEnum auditStatus) {
        this.auditStatus = auditStatus;
    }

    public LocalDateTime getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(LocalDateTime auditTime) {
        this.auditTime = auditTime;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    @Override
    public String toString() {
        return "AuditLogInfo{" +
            "logId=" + logId +
            ", logDo='" + logDo + '\'' +
            ", logWhat='" + logWhat + '\'' +
            ", result=" + result +
            ", clientIp='" + clientIp + '\'' +
            ", logWhen=" + logWhen +
            ", request='" + request + '\'' +
            ", auditStatus=" + auditStatus +
            ", auditTime=" + auditTime +
            ", username='" + username + '\'' +
            '}';
    }
}
