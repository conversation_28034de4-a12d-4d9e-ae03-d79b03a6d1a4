package kl.npki.management.core.biz.check;

import kl.nbase.helper.stopwatch.StopWatch;
import kl.npki.base.core.biz.check.ISelfCheckItem;
import kl.npki.base.core.biz.check.model.*;
import kl.npki.base.core.biz.inspection.InspectionItemTypeEnum;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.management.core.utils.FileSizeHelper;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.Map;

import static kl.npki.management.core.constants.I18nConstant.getBaseMgmtCoreI18nMessage;

/**
 * JVM内存自检,超过阈值85%则提示异常
 *
 * <AUTHOR>
 * @date 2024/05/15 16:30
 */
public class JvmMemoryCheckItem implements ISelfCheckItem {

    private static final int DEFAULT_THRESHOLD_PERCENT = 85;
    private final int thresholdPercent;

    public JvmMemoryCheckItem() {
        this(DEFAULT_THRESHOLD_PERCENT);
    }

    public JvmMemoryCheckItem(int thresholdPercent) {
        this.thresholdPercent = thresholdPercent;
    }

    public synchronized void setCheckResultFlag(boolean checkResultFlag) {
        JvmMemoryCheckItem.checkResultFlag = checkResultFlag;
    }

    private static boolean checkResultFlag = false;

    @Override
    public SelfCheckResult check() {
        I18nMessages i18n = getI18n();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        // 用于记录自检详情
        Map<String, Object> details = new LinkedHashMap<>();
        // 初始化检测结果标识为false
        setCheckResultFlag(false);

        // 检查JVM内存
        checkJvmMemory(details,i18n);

        // 设置通用项
        SelfCheckResult result = SelfCheckResult.builder()
            .version(ConfigHolder.get().get(SysInfoConfig.class).getEnvironmentSwitchId())
            .code(getFullCode())
            .name(getName())
            .category(getCategory().getName())
            .checkTime(LocalDateTime.now())
            .details(details)
            .build();

        result.setSeverity(checkResultFlag ? Severity.NONE : Severity.HIGH);
        result.setStatus(checkResultFlag ? Status.SUCCESS : Status.FAILURE);
        stopWatch.stop();
        result.setDuration(stopWatch.getTotalTimeMillis());

        if (checkResultFlag) {
            result.setMessage(i18n.jvmMemory + i18n.normal);
        } else {
            // 获取当前使用率
            String usageInfo = (String) details.get(i18n.heapMemory + i18n.memoryUsage);
            result.setMessage(String.format(i18n.memoryInsufficientTemplate, usageInfo, thresholdPercent));
        }

        setCheckResultFlag(false);
        return result;
    }

    private void checkJvmMemory(Map<String, Object> details, I18nMessages i18n) {
        MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();

        // 堆内存信息
        MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
        long heapUsed = heapMemoryUsage.getUsed();
        long heapMax = heapMemoryUsage.getMax();
        double heapUsagePercent = (double) heapUsed / heapMax * 100;

        details.put(i18n.heapMemory + i18n.memoryUsed, FileSizeHelper.humanReadableByteCount(heapUsed));
        details.put(i18n.heapMemory + i18n.memoryMax, FileSizeHelper.humanReadableByteCount(heapMax));
        details.put(i18n.heapMemory + i18n.memoryUsage, String.format("%.2f%%", heapUsagePercent));

        // 非堆内存信息
        MemoryUsage nonHeapMemoryUsage = memoryMXBean.getNonHeapMemoryUsage();
        long nonHeapUsed = nonHeapMemoryUsage.getUsed();
        long nonHeapMax = nonHeapMemoryUsage.getMax();

        details.put(i18n.nonHeapMemory + i18n.memoryUsed, FileSizeHelper.humanReadableByteCount(nonHeapUsed));
        if (nonHeapMax > 0) {
            double nonHeapUsagePercent = (double) nonHeapUsed / nonHeapMax * 100;
            details.put(i18n.nonHeapMemory + i18n.memoryMax, FileSizeHelper.humanReadableByteCount(nonHeapMax));
            details.put(i18n.nonHeapMemory + i18n.memoryUsage, String.format("%.2f%%", nonHeapUsagePercent));
        } else {
            details.put(i18n.nonHeapMemory + i18n.memoryMax, i18n.unknown);
            details.put(i18n.nonHeapMemory + i18n.memoryUsage, i18n.unknown);
        }

        // 判断堆内存使用率是否超过阈值
        if (heapUsagePercent < thresholdPercent) {
            setCheckResultFlag(true);
        }
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public String getName() {
        return SelfCheckItemEnum.JVM_MEMORY_CHECK.getName();
    }

    @Override
    public Category getCategory() {
        return Category.PERFORMANCE;
    }

    @Override
    public InspectionItemTypeEnum getInspectionItemType() {
        return InspectionItemTypeEnum.INSPECTION_SYSTEM_RESOURCE;
    }

    @Override
    public String getCode() {
        return Category.PERFORMANCE.getCode() + SelfCheckItemEnum.JVM_MEMORY_CHECK.getCode();
    }

    private I18nMessages getI18n() {
        I18nMessages i18n = new I18nMessages();
        i18n.init();
        return i18n;
    }

    private static class I18nMessages {
        String jvmMemory;
        String heapMemory;
        String nonHeapMemory;
        String memoryUsed;
        String memoryMax;
        String memoryUsage;
        String memoryInsufficientTemplate;
        String normal;
        String unknown;

        void init() {
            jvmMemory = getBaseMgmtCoreI18nMessage("jvm_memory");
            heapMemory = getBaseMgmtCoreI18nMessage("heap_memory");
            nonHeapMemory = getBaseMgmtCoreI18nMessage("non_heap_memory");
            memoryUsed = getBaseMgmtCoreI18nMessage("memory_used");
            memoryMax = getBaseMgmtCoreI18nMessage("memory_max");
            memoryUsage = getBaseMgmtCoreI18nMessage("memory_usage");
            memoryInsufficientTemplate = getBaseMgmtCoreI18nMessage("memory_insufficient_template");
            normal = getBaseMgmtCoreI18nMessage("normal");
            unknown = getBaseMgmtCoreI18nMessage("unknown");
        }
    }
}