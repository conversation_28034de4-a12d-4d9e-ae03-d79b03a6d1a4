package kl.npki.management.core.biz.cert.model;

import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.EnvironmentEnum;
import kl.npki.base.core.constant.UserStatus;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;

/**
 * 管理员证书导入信息类
 * <AUTHOR>
 */
public class ImportAdminCertInfo {
    private String roleCode;

    private Long orgId;

    private String passwordHash;

    private String certValue;

    private String encCertValue;

    /**
     * 解析为证书实体类
     * @return
     */
    public AdminEntity toAdminEntity(){
        String env = BaseConfigWrapper.getSysInfoConfig().getEnvironmentSwitchId();
        boolean ifDeploy = EnvironmentEnum.DEMO.getId().equals(env);
        // 解析证书状态
        int status = UserStatus.REGISTER_TO_BE_CHECKED.getCode();
        if (ifDeploy) {
            // 处于系统部署阶段时，无需审核
            status = UserStatus.NORMAL.getCode();
        }

        // 构造AdminEntity
        AdminEntity adminEntity = new AdminEntity();
        adminEntity.setPasswordHash(this.passwordHash);
        adminEntity.setRoleCode(this.roleCode);
        adminEntity.setStatus(status);
        adminEntity.setOrgId(this.orgId);

        return adminEntity;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getPasswordHash() {
        return passwordHash;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }

    public String getCertValue() {
        return certValue;
    }

    public void setCertValue(String certValue) {
        this.certValue = certValue;
    }

    public String getEncCertValue() {
        return encCertValue;
    }

    public void setEncCertValue(String encCertValue) {
        this.encCertValue = encCertValue;
    }

}
