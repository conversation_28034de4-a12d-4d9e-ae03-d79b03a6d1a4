package kl.npki.management.core.biz.check;

import kl.nbase.security.asn1.x509.Certificate;
import kl.npki.base.core.biz.check.model.Category;
import kl.npki.base.core.biz.check.model.SelfCheckItemEnum;
import kl.npki.base.core.common.manager.MgrHolder;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static kl.npki.management.core.constants.I18nConstant.getBaseMgmtCoreI18nMessage;
import static kl.npki.management.core.constants.SelfCheckConstant.IDENTIFY_CERT_NAME_I18N_KEY;
import static kl.npki.management.core.constants.SelfCheckConstant.Paths.SERVER_CERT_GUIDE_PATH;

/**
 * 身份证书自检项
 *
 * <AUTHOR>
 * @date 2023/10/10
 */
public class IdentityCertValidityPeriodCheckItem extends AbstractCertValidityPeriodCheckItem {

    @Override
    public String getCode() {
        return Category.CERT.getCode() + SelfCheckItemEnum.IDENTITY_CERT_VALIDITY_CHECK.getCode();
    }

    @Override
    public String getName() {
        return SelfCheckItemEnum.IDENTITY_CERT_VALIDITY_CHECK.getName();
    }

    @Override
    protected String getGuidePath() {
        return SERVER_CERT_GUIDE_PATH;
    }

    @Override
    protected List<Certificate> getCertificates() {
        Certificate idCert = MgrHolder.getIdCertMgr().getIdCert();
        if (Objects.isNull(idCert)) {
            return Collections.emptyList();
        }
        return Collections.singletonList(idCert);
    }

    @Override
    protected String getCertificateName() {
        return getBaseMgmtCoreI18nMessage(IDENTIFY_CERT_NAME_I18N_KEY);
    }
}
