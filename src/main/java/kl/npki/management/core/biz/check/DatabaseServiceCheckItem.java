package kl.npki.management.core.biz.check;

import com.alibaba.druid.pool.DruidDataSource;
import kl.nbase.db.core.SwitchableDataSource;
import kl.nbase.helper.stopwatch.StopWatch;
import kl.npki.base.core.biz.check.ISelfCheckItem;
import kl.npki.base.core.biz.check.model.*;
import kl.npki.base.core.biz.inspection.InspectionItemTypeEnum;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.management.core.constants.SelfCheckConstant;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static kl.npki.management.core.constants.I18nConstant.getBaseMgmtCoreI18nMessage;

/**
 * 数据库服务自检
 *
 * <AUTHOR>
 * @date 2024/05/15 10:30
 */
public class DatabaseServiceCheckItem implements ISelfCheckItem {
    private final DataSource dataSource;
    private static volatile boolean checkResultFlag = false;

    public synchronized void setCheckResultFlag(boolean checkResultFlag) {
        DatabaseServiceCheckItem.checkResultFlag = checkResultFlag;
    }

    public DatabaseServiceCheckItem(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public SelfCheckResult check() {
        I18nMessages i18n = getI18n();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        // 初始化检测结果标识为false
        setCheckResultFlag(false);

        // 创建结果对象
        SelfCheckResult result = SelfCheckResult.builder()
            .version(ConfigHolder.get().get(SysInfoConfig.class).getEnvironmentSwitchId())
            .code(getFullCode())
            .name(getName())
            .category(getCategory().getName())
            .checkTime(LocalDateTime.now())
            .details(new LinkedHashMap<>())
            .build();

        // 执行综合检查（连接、元数据、查询）
        performDatabaseChecks(result, i18n);

        // 完成结果设置
        completeResult(result, stopWatch, i18n);

        return result;
    }

    /**
     * 执行数据库综合检查，包括连接、元数据和查询测试
     *
     * @param result 检查结果对象
     * @param i18n   国际化消息
     * @return 检查是否全部成功
     */
    private boolean performDatabaseChecks(SelfCheckResult result, I18nMessages i18n) {
        Map<String, Object> details = result.getDetails();
        List<DataSource> physicalDataSources = extractPhysicalDataSources(dataSource);

        if (physicalDataSources.isEmpty()) {
            details.put(i18n.dbType, i18n.unknown);
            details.put(i18n.dbVersion, i18n.unknown);
            details.put(i18n.dbConnection + i18n.check, i18n.dbConnection + i18n.exception + I18nMessages.COLON_SEPARATOR + i18n.noPhysicalDataSource);
            details.put(i18n.dbQuery + i18n.check, i18n.dbQuery + i18n.exception + I18nMessages.COLON_SEPARATOR + i18n.noPhysicalDataSource);
            return false;
        }

        boolean allSuccess = true;

        for (int i = 0; i < physicalDataSources.size(); i++) {
            DataSource ds = physicalDataSources.get(i);
            String suffix = physicalDataSources.size() > 1 ? I18nMessages.DS_PREFIX + (i + 1) + I18nMessages.DS_SUFFIX : I18nMessages.EMPTY_STRING;

            try (Connection connection = ds.getConnection()) {
                // 首先获取数据库类型和版本信息
                String dbProductName = i18n.unknown;
                String dbProductVersion = i18n.unknown;
                
                try {
                    dbProductName = connection.getMetaData().getDatabaseProductName();
                    dbProductVersion = connection.getMetaData().getDatabaseProductVersion();
                } catch (SQLException e) {
                    dbProductVersion = i18n.unknown + I18nMessages.COLON_SEPARATOR + e.getMessage();
                }
                
                details.put(i18n.dbType + suffix, dbProductName);
                details.put(i18n.dbVersion + suffix, dbProductVersion);
                
                // 检查连接
                if (connection != null && !connection.isClosed()) {
                    details.put(i18n.dbConnection + i18n.check + suffix, i18n.dbConnection + i18n.normal);
                    
                    // 执行查询测试
                    try {
                        String testSql = getTestSqlForDatabase(dbProductName);
                        try (PreparedStatement stmt = connection.prepareStatement(testSql);
                             ResultSet rs = stmt.executeQuery()) {
                            
                            if (rs.next() && rs.getInt(1) == 1) {
                                details.put(i18n.dbQuery + i18n.check + suffix, i18n.dbQuery + i18n.normal);
                            } else {
                                details.put(i18n.dbQuery + i18n.check + suffix, i18n.dbQuery + i18n.exception);
                                allSuccess = false;
                            }
                        }
                    } catch (Exception e) {
                        details.put(i18n.dbQuery + i18n.check + suffix, i18n.dbQuery + i18n.exception + I18nMessages.COLON_SEPARATOR + e.getMessage());
                        allSuccess = false;
                    }
                } else {
                    details.put(i18n.dbConnection + i18n.check + suffix, i18n.dbConnection + i18n.exception);
                    details.put(i18n.dbQuery + i18n.check + suffix, i18n.dbQuery + i18n.exception + I18nMessages.COLON_SEPARATOR + i18n.connectionClosed);
                    allSuccess = false;
                }
            } catch (SQLException e) {
                // 连接失败，所有检查都失败
                details.put(i18n.dbType + suffix, i18n.unknown);
                details.put(i18n.dbVersion + suffix, i18n.unknown);
                details.put(i18n.dbConnection + i18n.check + suffix, i18n.dbConnection + i18n.exception + I18nMessages.COLON_SEPARATOR + e.getMessage());
                details.put(i18n.dbQuery + i18n.check + suffix, i18n.dbQuery + i18n.exception + I18nMessages.COLON_SEPARATOR + i18n.cannotEstablishConnection);
                allSuccess = false;
            }
        }

        setCheckResultFlag(allSuccess);
        return allSuccess;
    }

    private void completeResult(SelfCheckResult result, StopWatch stopWatch, I18nMessages i18n) {
        result.setGuidance(checkResultFlag ? "" : SelfCheckConstant.Paths.DATABASE_GUIDE_PATH);
        result.setSeverity(checkResultFlag ? Severity.NONE : Severity.HIGH);
        result.setStatus(checkResultFlag ? Status.SUCCESS : Status.FAILURE);
        stopWatch.stop();
        result.setDuration(stopWatch.getTotalTimeMillis());
        result.setMessage(checkResultFlag ?
            i18n.dbService + i18n.normal :
            i18n.dbService + i18n.exception);
        setCheckResultFlag(false);
    }

    /**
     * 根据数据库类型返回适当的测试SQL语句
     *
     * @param databaseProductName 数据库产品名称
     * @return 适用于该数据库的测试SQL语句
     */
    private String getTestSqlForDatabase(String databaseProductName) {
        if (databaseProductName == null) {
            return I18nMessages.SQL_SELECT_1;
        }
        
        String dbNameLower = databaseProductName.toLowerCase();

        if (dbNameLower.contains(I18nMessages.DB_ORACLE) || 
            dbNameLower.contains(I18nMessages.DB_DM) || 
            dbNameLower.contains(I18nMessages.DB_DAMENG)) {
            return I18nMessages.SQL_SELECT_1_FROM_DUAL;
        } else {
            // 默认SQL，适用于大多数数据库
            return I18nMessages.SQL_SELECT_1;
        }
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public String getName() {
        return SelfCheckItemEnum.DATABASE_SERVICE_CHECK.getName();
    }

    @Override
    public Category getCategory() {
        return Category.DATABASE;
    }

    @Override
    public InspectionItemTypeEnum getInspectionItemType() {
        return InspectionItemTypeEnum.INSPECTION_EXTERNAL_SERVER_STATUS;
    }

    @Override
    public String getCode() {
        return Category.CONFIGURATION.getCode() + SelfCheckItemEnum.DATABASE_SERVICE_CHECK.getCode();
    }

    private I18nMessages getI18n() {
        I18nMessages i18n = new I18nMessages();
        i18n.init();
        return i18n;
    }

    /**
     * 国际化消息和常量封装类
     */
    private static class I18nMessages {
        // 国际化消息
        String dbConnection;
        String dbQuery;
        String dbService;
        String dbType;
        String dbVersion;
        String check;
        String normal;
        String exception;
        String unknown;
        String noPhysicalDataSource;
        String connectionClosed;
        String cannotEstablishConnection;
        
        // SQL常量
        static final String SQL_SELECT_1 = "SELECT 1";
        static final String SQL_SELECT_1_FROM_DUAL = "SELECT 1 FROM DUAL";
        
        // 数据库类型常量
        static final String DB_ORACLE = "oracle";
        static final String DB_DM = "dm";
        static final String DB_DAMENG = "dameng";
        
        // 其他常量
        static final String DS_PREFIX = " (DS-";
        static final String DS_SUFFIX = ")";
        static final String COLON_SEPARATOR = ": ";
        static final String EMPTY_STRING = "";

        void init() {
            this.dbConnection = getBaseMgmtCoreI18nMessage("db_connection");
            this.dbQuery = getBaseMgmtCoreI18nMessage("db_query");
            this.dbService = getBaseMgmtCoreI18nMessage("database_service");
            this.dbType = getBaseMgmtCoreI18nMessage("database_type");
            this.dbVersion = getBaseMgmtCoreI18nMessage("database_version");
            this.check = getBaseMgmtCoreI18nMessage("testing");
            this.normal = getBaseMgmtCoreI18nMessage("normal");
            this.exception = getBaseMgmtCoreI18nMessage("abnormal");
            this.unknown = getBaseMgmtCoreI18nMessage("unknown");
            this.noPhysicalDataSource = getBaseMgmtCoreI18nMessage("no_physical_datasource");
            this.connectionClosed = getBaseMgmtCoreI18nMessage("connection_closed");
            this.cannotEstablishConnection = getBaseMgmtCoreI18nMessage("cannot_establish_connection");
        }
    }

    /**
     * 从逻辑数据源中提取所有物理数据源
     *
     * @param dataSource 数据源
     * @return 物理数据源列表
     */
    private List<DataSource> extractPhysicalDataSources(DataSource dataSource) {
        List<DataSource> physicalDataSources = new ArrayList<>();

        if (dataSource instanceof SwitchableDataSource) {
            SwitchableDataSource switchableDataSource = (SwitchableDataSource) dataSource;
            List<DruidDataSource> druidDataSources = switchableDataSource.getDruidDataSources();
            if (druidDataSources != null && !druidDataSources.isEmpty()) {
                physicalDataSources.addAll(druidDataSources);
            }
        } else {
            // 如果不是SwitchableDataSource，则认为是物理数据源
            physicalDataSources.add(dataSource);
        }

        return physicalDataSources;
    }
}