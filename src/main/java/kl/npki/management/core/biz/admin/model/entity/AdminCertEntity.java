package kl.npki.management.core.biz.admin.model.entity;

import kl.nbase.helper.utils.CheckUtils;
import kl.nbase.security.asn1.x500.X500Name;
import kl.nbase.security.asn1.x509.Certificate;
import kl.npki.base.core.biz.cert.model.CertRevokedInfo;
import kl.npki.base.core.common.manager.MgrHolder;
import kl.npki.base.core.constant.CertStatus;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.management.core.exception.ManagementValidationError;
import kl.npki.management.core.repository.IAdminCertRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.List;

import static kl.npki.management.core.constants.I18nExceptionInfoConstant.CERTIFICATE_STATUS_DOES_NOT_MATCH_EXPECTATIONS_I18N_KEY;

/**
 * <AUTHOR>
 * @date 2022/10/10 13:59
 * @desc
 */
@Service
public class AdminCertEntity {

    private final IAdminCertRepository adminCertRepository;

    /**
     * 唯一标识
     */
    private Long id;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 签名证书序列号（16进制）
     */
    private String signCertSn;

    /**
     * 加密证书序列号
     */
    private String encCertSn;

    /**
     * 签名证书值
     */
    private String signCertValue;

    /**
     * 加密证书值
     */
    private String encCertValue;

    /**
     * P10证书请求
     */
    private String p10CertReq;

    /**
     * 是否为导入方式
     */
    private Boolean imported;

    /**
     * 管理员信息id
     */
    private Long adminInfoId;

    /**
     * 证书CN
     */
    private String subjectCn;

    /**
     * 签发者CN
     */
    private String issuerCn;

    /**
     * 证书DN
     */
    private String subjectDn;

    /**
     * 证书有效期-起始
     */
    private LocalDateTime validStart;

    /**
     * 证书有效期-结束
     */
    private LocalDateTime validEnd;

    /**
     * 创建者id
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者id
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 证书状态
     */
    private Integer status;

    /**
     * 是否是双证
     */
    private Boolean twinCert;

    /**
     * PFX文件密码
     */
    private String pfxKey;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 完整性Hash值
     */
    private String fullDataHash;

    public AdminCertEntity(String sigCertValue) {
        this();
        this.signCertValue = sigCertValue;
        Certificate cert = CertUtil.parseCert(sigCertValue);
        fillPropertiesByCert(cert);
    }

    public AdminCertEntity(String sigCertValue, String encCertValue) {
        this(sigCertValue);
        if (StringUtils.isBlank(encCertValue)) {
            return;
        }
        this.encCertValue = encCertValue;
        this.encCertSn = CertUtil.getHexSnOfCert(CertUtil.parseCert(encCertValue));
    }

    public AdminCertEntity() {
        this.adminCertRepository = RepositoryFactory.get(IAdminCertRepository.class);
    }

    public Long getId() {
        return id;
    }

    public AdminCertEntity setId(Long id) {
        this.id = id;
        return this;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getSignCertSn() {
        return signCertSn;
    }

    public AdminCertEntity setSignCertSn(String signCertSn) {
        this.signCertSn = signCertSn;
        return this;
    }

    public String getEncCertSn() {
        return encCertSn;
    }

    public AdminCertEntity setEncCertSn(String encCertSn) {
        this.encCertSn = encCertSn;
        return this;
    }

    public String getP10CertReq() {
        return p10CertReq;
    }

    public void setP10CertReq(String p10CertReq) {
        this.p10CertReq = p10CertReq;
    }

    public Long getAdminInfoId() {
        return adminInfoId;
    }

    public AdminCertEntity setAdminInfoId(Long adminInfoId) {
        this.adminInfoId = adminInfoId;
        return this;
    }

    public String getSubjectCn() {
        return subjectCn;
    }

    public AdminCertEntity setSubjectCn(String subjectCn) {
        this.subjectCn = subjectCn;
        return this;
    }

    public String getIssuerCn() {
        return issuerCn;
    }

    public AdminCertEntity setIssuerCn(String issuerCn) {
        this.issuerCn = issuerCn;
        return this;
    }

    public String getSubjectDn() {
        return subjectDn;
    }

    public AdminCertEntity setSubjectDn(String subjectDn) {
        this.subjectDn = subjectDn;
        return this;
    }

    public LocalDateTime getValidStart() {
        return validStart;
    }

    public AdminCertEntity setValidStart(LocalDateTime validStart) {
        this.validStart = validStart;
        return this;
    }

    public LocalDateTime getValidEnd() {
        return validEnd;
    }

    public AdminCertEntity setValidEnd(LocalDateTime validEnd) {
        this.validEnd = validEnd;
        return this;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public AdminCertEntity setCreateBy(Long createBy) {
        this.createBy = createBy;
        return this;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public AdminCertEntity setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
        return this;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public AdminCertEntity setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
        return this;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public AdminCertEntity setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public AdminCertEntity setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public String getSignCertValue() {
        return signCertValue;
    }

    public void setSignCertValue(String signCertValue) {
        this.signCertValue = signCertValue;
    }

    public String getEncCertValue() {
        return encCertValue;
    }

    public void setEncCertValue(String encCertValue) {
        this.encCertValue = encCertValue;
    }

    public Boolean getTwinCert() {
        return twinCert;
    }

    public void setTwinCert(Boolean twinCert) {
        this.twinCert = twinCert;
    }

    public String getPfxKey() {
        return pfxKey;
    }

    public void setPfxKey(String pfxKey) {
        this.pfxKey = pfxKey;
    }

    public Boolean getImported() {
        return imported;
    }

    public void setImported(Boolean imported) {
        this.imported = imported;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getFullDataHash() {
        return fullDataHash;
    }

    public void setFullDataHash(String fullDataHash) {
        this.fullDataHash = fullDataHash;
    }

    /**
     * 通过证书填充字段
     *
     * @param cert
     */
    public void fillPropertiesByCert(Certificate cert) {
        X500Name subject = cert.getSubject();
        this.issuerCn = CertUtil.getCommonName(cert.getIssuer());
        this.subjectCn = CertUtil.getCommonName(subject);
        this.subjectDn = subject.toString();
        this.validStart = CertUtil.getValidateFrom(cert);
        this.validEnd = CertUtil.getValidateTo(cert);
        this.signCertSn = CertUtil.getHexSnOfCert(cert);
    }

    /**
     * 保存管理员证书
     *
     * @param adminInfoId
     */
    public void save(Long adminInfoId) {
        // 管理员信息id必须存在
        this.adminInfoId = adminInfoId;
        // 构造数据
        setCreateTime(LocalDateTime.now());
        setIsDelete(0);
        if (getImported() == null) {
            setImported(false);
        }
        // 保存数据
        this.id = adminCertRepository.save(this);
    }

    /**
     * 更新管理员证书
     */
    public void update() {
        // 1.构造数据
        setUpdateTime(LocalDateTime.now());
        // 2.保存数据
        adminCertRepository.update(this);
    }

    /**
     * 删除管理员证书
     */
    public void delete() {
        // 删除数据
        adminCertRepository.delete(this.id);
    }

    /**
     * 部署时证书签发后根据证书信息构造证书实体
     *
     * @param cert        证书
     * @param adminInfoId 管理员信息id
     */
    public void issueAtDeploy(Certificate cert, Long adminInfoId) {
        // 解析证书
        fillPropertiesByCert(cert);
        // 状态改为已签发
        this.status = CertStatus.ISSUED.getCode();
        save(adminInfoId);
    }

    /**
     * 证书签发后根据证书信息构造证书实体
     *
     * @param cert 证书
     */
    public void issueAfter(Certificate cert) {
        fillPropertiesByCert(cert);
        this.status = CertStatus.ISSUED.getCode();
        update();
    }

    /**
     * 导入证书
     *
     * @param adminInfoId
     */
    public void importAdmin(Long adminInfoId) {
        setStatus(CertStatus.ISSUED.getCode());
        setImported(true);
        save(adminInfoId);
    }

    /**
     * 导入延期证书
     */
    public void importExtendAdmin(String signCertValue, String encCertValue) {
        setStatus(CertStatus.ISSUED.getCode());
        setImported(true);
        this.signCertValue = signCertValue;
        Certificate cert = CertUtil.parseCert(signCertValue);
        fillPropertiesByCert(cert);
        if (StringUtils.isNotBlank(encCertValue)) {
            this.encCertValue = encCertValue;
            this.encCertSn = CertUtil.getHexSnOfCert(CertUtil.parseCert(encCertValue));
        }
        update();
    }

    /**
     * 请求废除证书
     */
    public void requestRevoke() {
        // 检查证书状态为已签发
        checkStatus(CertStatus.ISSUED);
        // 修改证书状态为废除待审核
        setStatus(CertStatus.REVOKE_TO_BE_CHECKED.getCode());
        // 更新
        update();
    }

    /**
     * 废除证书
     */
    public void revoke() {
        // 检查证书状态为待废除
        checkStatus(CertStatus.TO_BE_REVOKED);
        // 修改状态为已废除
        setStatus(CertStatus.REVOKED.getCode());
        // 更新
        update();
        // 生成实时CRL
        CertRevokedInfo certRevokedInfo = new CertRevokedInfo(new BigInteger(this.signCertSn, 16));
        MgrHolder.getManageCertMgr().asyncGenerateMgrRootCrl(certRevokedInfo);
    }

    /**
     * 撤销废除申请
     */
    public void cancelRevoke() {
        //检查证书状态为待废除
        checkStatus(CertStatus.TO_BE_REVOKED);
        // 修改状态为已签发
        setStatus(CertStatus.ISSUED.getCode());
        //更新
        update();
    }

    /**
     * 注销
     */
    public void cancel() {
        // 检查证书状态为已废除或待签发
        checkStatus(CertStatus.REVOKED, CertStatus.TO_BE_ISSUED);
        // 删除
        delete();
    }

    /**
     * 检验当前状态，符合入参列表的任意一个状态即可
     *
     * @param toCheckStatusList 期望的状态列表，符合其中一个即可
     */
    public void checkStatus(CertStatus... toCheckStatusList) {
        boolean success = false;
        StringBuilder needStatus = new StringBuilder();
        CertStatus certStatus = CertStatus.valueOf(this.status);
        for (CertStatus adminCertStatus : toCheckStatusList) {
            if (certStatus.equals(adminCertStatus)) {
                success = true;
            }
            needStatus.append(adminCertStatus.getDesc()).append(";");
        }
        CheckUtils.isTrue(success, ManagementValidationError.BIZ_STATUS_ERROR.toException(CERTIFICATE_STATUS_DOES_NOT_MATCH_EXPECTATIONS_I18N_KEY ,this.getSubjectCn(), certStatus.getDesc(), needStatus));
    }

    /**
     * 获取管理员签名证书序列号，用来给前端进行key列表的过滤
     *
     * @return
     */
    public List<String> getSignCertSnList() {
        return adminCertRepository.getSignCertSnList();
    }

    /**
     * 审核通过
     */
    public void checkPass() {
        // 获取审核通过后对应的状态
        CertStatus newStatus = CertStatus.passCheck(getStatus());
        setStatus(newStatus.getCode());
        update();
    }

    /**
     * 审核拒绝
     */
    public void checkFail() {
        // 获取审核拒绝后对应的状态
        CertStatus newStatus = CertStatus.rejectCheck(getStatus());
        setStatus(newStatus.getCode());
        update();
    }
}
