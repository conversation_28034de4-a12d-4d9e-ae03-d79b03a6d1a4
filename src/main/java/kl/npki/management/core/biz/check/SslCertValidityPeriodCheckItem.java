package kl.npki.management.core.biz.check;

import com.alibaba.excel.util.StringUtils;
import kl.nbase.security.asn1.x509.Certificate;
import kl.npki.base.core.biz.cert.model.TrustCertEntity;
import kl.npki.base.core.biz.check.model.Category;
import kl.npki.base.core.biz.check.model.SelfCheckItemEnum;
import kl.npki.base.core.constant.TrustCertType;
import kl.npki.base.core.repository.ITrustCertRepository;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.utils.CertUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static kl.npki.management.core.constants.I18nConstant.getBaseMgmtCoreI18nMessage;
import static kl.npki.management.core.constants.SelfCheckConstant.Paths.SERVER_CERT_GUIDE_PATH;
import static kl.npki.management.core.constants.SelfCheckConstant.SSL_SITE_CERT_NAME_I18N_KEY;

/**
 * SSL站点证书自检项
 *
 * <AUTHOR>
 * @date 2023/10/10
 */
public class SslCertValidityPeriodCheckItem extends AbstractCertValidityPeriodCheckItem {

    private final ITrustCertRepository trustCertRepository;

    public SslCertValidityPeriodCheckItem() {
        this.trustCertRepository = RepositoryFactory.get(ITrustCertRepository.class);
    }

    @Override
    protected List<Certificate> getCertificates() {
        List<Certificate> certificates = new ArrayList<>();
        TrustCertEntity sslCertEntity = trustCertRepository.getCert(TrustCertType.SSL);
        if (Objects.isNull(sslCertEntity)) {
            return certificates;
        }
        if (StringUtils.isNotBlank(sslCertEntity.getCertValue())) {
            certificates.add(CertUtil.parseCert(sslCertEntity.getCertValue()));
        }
        if (StringUtils.isNotBlank(sslCertEntity.getEncCertValue())) {
            certificates.add(CertUtil.parseCert(sslCertEntity.getEncCertValue()));
        }
        return certificates;
    }

    @Override
    protected String getCertificateName() {
        return getBaseMgmtCoreI18nMessage(SSL_SITE_CERT_NAME_I18N_KEY);
    }

    @Override
    public String getCode() {
        return Category.CERT.getCode() + SelfCheckItemEnum.SSL_CERT_VALIDITY_CHECK.getCode();
    }

    @Override
    public String getName() {
        return SelfCheckItemEnum.SSL_CERT_VALIDITY_CHECK.getName();
    }

    @Override
    protected String getGuidePath() {
        return SERVER_CERT_GUIDE_PATH;
    }
}
