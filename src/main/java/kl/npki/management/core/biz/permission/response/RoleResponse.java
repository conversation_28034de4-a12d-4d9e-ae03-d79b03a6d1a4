package kl.npki.management.core.biz.permission.response;

import java.io.Serializable;

/**
 * 管理员角色响应
 *
 * <AUTHOR>
 * @date 2023/5/22
 */
public class RoleResponse implements Serializable {

    private static final long serialVersionUID = 6515661899343369135L;
    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 父角色编码
     */
    private String parentRoleCode;

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getParentRoleCode() {
        return parentRoleCode;
    }

    public void setParentRoleCode(String parentRoleCode) {
        this.parentRoleCode = parentRoleCode;
    }
}
