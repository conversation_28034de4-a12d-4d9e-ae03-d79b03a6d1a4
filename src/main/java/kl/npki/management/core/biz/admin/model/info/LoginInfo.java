package kl.npki.management.core.biz.admin.model.info;


import java.util.List;

/**
 * 登录对象
 *
 * <AUTHOR>
 * Created on 2022/06/24 11:35
 */

public class LoginInfo {

    private String authnType;

    private String username;

    private String password;

    /**
     * 用于单点登录的token
     */
    private String accessToken;

    /**
     * 主管理员证书登录信息
     */
    private CertLoginInfo mainAdmin;

    /**
     * 5选3 3选2 模式中的其它管理员证书信息
     */
    private List<CertLoginInfo> otherAdmins;

    /**
     * 认证引用 ID。
     * <p>
     * 用于标识服务端缓存中的临时认证数据，供后续验证使用。
     * <ul>
     *   <li><b>用户名密码登录：</b>标识缓存中的图形验证码ID。</li>
     *   <li><b>证书签名登录：</b>标识服务端生成并缓存的签名用随机数 ID。</li>
     * </ul>
     * 前端在登录请求中需携带该 ID，服务端通过此值获取对应临时数据进行校验。
     */
    private String authRefId;

    public CertLoginInfo getMainAdmin() {
        return mainAdmin;
    }

    public void setMainAdmin(CertLoginInfo mainAdmin) {
        this.mainAdmin = mainAdmin;
    }

    public List<CertLoginInfo> getOtherAdmins() {
        return otherAdmins;
    }

    public void setOtherAdmins(List<CertLoginInfo> otherAdmins) {
        this.otherAdmins = otherAdmins;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public LoginInfo setPassword(String password) {
        this.password = password;
        return this;
    }

    public String getAuthnType() {
        return authnType;
    }

    public LoginInfo setAuthnType(String authnType) {
        this.authnType = authnType;
        return this;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getAuthRefId() {
        return authRefId;
    }

    public LoginInfo setAuthRefId(String authRefId) {
        this.authRefId = authRefId;
        return this;
    }

    @Override
    public String toString() {
        return "LoginInfo{" +
            "authnType='" + authnType + '\'' +
            ", username='" + username + '\'' +
            ", password='" + password + '\'' +
            ", mainAdmin=" + mainAdmin +
            ", otherAdmins=" + otherAdmins +
            '}';
    }
}
