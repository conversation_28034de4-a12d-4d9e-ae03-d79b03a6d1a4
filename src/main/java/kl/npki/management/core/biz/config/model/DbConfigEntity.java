package kl.npki.management.core.biz.config.model;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class DbConfigEntity implements Serializable {
    private static final long serialVersionUID = 3948925499855652064L;
    @Schema(description = "数据库类型")
    private String dbType;
    @Schema(description = "最大连接数")
    private Integer maxConnect;
    @Schema
    private Boolean enableReadWrite;
    @Schema(description = "数据库配置信息列表")
    private List<DbConfigInfo> dbConfigInfoList;

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }

    public Integer getMaxConnect() {
        return maxConnect;
    }

    public void setMaxConnect(Integer maxConnect) {
        this.maxConnect = maxConnect;
    }

    public Boolean getEnableReadWrite() {
        return enableReadWrite;
    }

    public void setEnableReadWrite(Boolean enableReadWrite) {
        this.enableReadWrite = enableReadWrite;
    }

    public List<DbConfigInfo> getDbConfigInfoList() {
        return dbConfigInfoList;
    }

    public void setDbConfigInfoList(List<DbConfigInfo> dbConfigInfoList) {
        this.dbConfigInfoList = dbConfigInfoList;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DbConfigEntity)) return false;
        DbConfigEntity that = (DbConfigEntity) o;
        return Objects.equals(getDbType(), that.getDbType()) && Objects.equals(getMaxConnect(), that.getMaxConnect()) && Objects.equals(getEnableReadWrite(), that.getEnableReadWrite()) && Objects.equals(getDbConfigInfoList(), that.getDbConfigInfoList());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getDbType(), getMaxConnect(), getEnableReadWrite(), getDbConfigInfoList());
    }

    @Override
    public String toString() {
        return "DbConfigEntity{" + "dbType='" + dbType + '\'' + ", maxConnect=" + maxConnect + ", enableReadWrite=" + enableReadWrite + ", dbConfigInfoList=" + dbConfigInfoList + '}';
    }
}
