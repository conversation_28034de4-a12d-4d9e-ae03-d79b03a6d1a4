package kl.npki.management.core.biz.kcsp.model;

import kl.nbase.security.entity.algo.AsymAlgo;

import java.io.Serializable;
import java.util.List;

/**
 * KCSP 服务同步请求
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
public class KcspServiceSyncRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作类型，可以是 "service.add", "service.update", "service.delete"
     */
    private String opType;

    /**
     * 服务数据
     */
    private ServiceData data;

    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }

    public ServiceData getData() {
        return data;
    }

    public void setData(ServiceData data) {
        this.data = data;
    }

    public static class ServiceData implements Serializable {

        private static final long serialVersionUID = 2405172041950251807L;

        /**
         * KCSP与密码应用服务开通的服务标识，后续对服务的操作都用此标识
         */
        private String serviceId;

        /**
         * 密码机服务的地址
         */
        private String servHsmUrl;

        /**
         * 服务访问标识
         */
        private String ak;

        /**
         * 服务访问安全标识
         */
        private String sk;

        /**
         * 密钥索引数组
         */
        private List<SecretKeyInfo> secretKeys;

        /**
         * 扩展属性
         */
        private ServiceExtension ext;

        public String getServiceId() {
            return serviceId;
        }

        public void setServiceId(String serviceId) {
            this.serviceId = serviceId;
        }

        public String getServHsmUrl() {
            return servHsmUrl;
        }

        public void setServHsmUrl(String servHsmUrl) {
            this.servHsmUrl = servHsmUrl;
        }

        public String getAk() {
            return ak;
        }

        public void setAk(String ak) {
            this.ak = ak;
        }

        public String getSk() {
            return sk;
        }

        public void setSk(String sk) {
            this.sk = sk;
        }

        public List<SecretKeyInfo> getSecretKeys() {
            return secretKeys;
        }

        public void setSecretKeys(List<SecretKeyInfo> secretKeys) {
            this.secretKeys = secretKeys;
        }

        public ServiceExtension getExt() {
            return ext;
        }

        public void setExt(ServiceExtension ext) {
            this.ext = ext;
        }
    }

    public static class SecretKeyInfo {

        /**
         * 密钥索引
         */
        private int secretKeyid;

        /**
         * 密钥别名
         */
        private String alias;

        public void updateAlias() {
            // 默认RSA长度是2048
            if (AsymAlgo.RSA_2048.getAlgoName().startsWith(this.alias)) {
                this.alias = AsymAlgo.RSA_2048.getAlgoName();
            }
        }

        public int getSecretKeyid() {
            return secretKeyid;
        }

        public void setSecretKeyid(int secretKeyid) {
            this.secretKeyid = secretKeyid;
        }

        public String getAlias() {
            return alias;
        }

        public void setAlias(String alias) {
            this.alias = alias;
        }
    }

    public static class ServiceExtension {

        /**
         * 管理网关代理地址
         */
        private String mgrGatewayUrl;

        /**
         * 服务网关代理地址
         */
        private String svcGatewayUrl;

        /**
         * Prometheus抓取数据端口
         */
        private String prometheusPort;

        public String getMgrGatewayUrl() {
            return mgrGatewayUrl;
        }

        public void setMgrGatewayUrl(String mgrGatewayUrl) {
            this.mgrGatewayUrl = mgrGatewayUrl;
        }

        public String getSvcGatewayUrl() {
            return svcGatewayUrl;
        }

        public void setSvcGatewayUrl(String svcGatewayUrl) {
            this.svcGatewayUrl = svcGatewayUrl;
        }

        public String getPrometheusPort() {
            return prometheusPort;
        }

        public void setPrometheusPort(String prometheusPort) {
            this.prometheusPort = prometheusPort;
        }
    }
}
