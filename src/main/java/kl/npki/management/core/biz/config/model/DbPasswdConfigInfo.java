package kl.npki.management.core.biz.config.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import java.io.Serializable;

import static kl.npki.management.core.constants.I18nParameterVerifyConstant.*;

/**
 * 数据库密码配置信息
 *
 * <AUTHOR>
 */
public class DbPasswdConfigInfo implements Serializable {
    @NotBlank(message = THE_DATABASE_NAME_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = THE_LENGTH_OF_THE_DATABASE_NAME_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = THE_DATABASE_NAME_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "数据库名称")
    private String dbName;
    @NotBlank(message = THE_DATABASE_PASSWORD_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = THE_LENGTH_OF_THE_DATABASE_PASSWORD_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = THE_DATABASE_PASSWORD_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "数据库密码")
    private String password;

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
