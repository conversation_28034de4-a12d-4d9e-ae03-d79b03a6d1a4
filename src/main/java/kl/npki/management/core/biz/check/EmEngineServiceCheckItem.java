package kl.npki.management.core.biz.check;

import kl.nbase.check.adapter.impl.KJCCSelfCheckAdapter;
import kl.nbase.check.algo.impl.AsymEncDecSelfCheck;
import kl.nbase.check.algo.impl.AsymSignVerifySelfCheck;
import kl.nbase.check.algo.impl.HashSelfCheck;
import kl.nbase.check.algo.impl.SymEncDecSelfCheck;
import kl.nbase.check.entity.ISelfCheckResult;
import kl.nbase.check.entity.impl.AsymEncDecSelfCheckParam;
import kl.nbase.check.entity.impl.AsymSignVerifySelfCheckParam;
import kl.nbase.check.entity.impl.HashSelfCheckParam;
import kl.nbase.check.entity.impl.SymEncDecSelfCheckParam;
import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.helper.stopwatch.StopWatch;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.entity.algo.BlockSymAlgo;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.nbase.security.entity.data.impl.OriginalData;
import kl.nbase.security.entity.key.sym.impl.RawByteSymKey;
import kl.nbase.security.entity.param.crypto.asym.enc.impl.RSADecParam;
import kl.nbase.security.entity.param.crypto.asym.enc.impl.RSAEncParam;
import kl.nbase.security.entity.param.crypto.asym.sign.impl.RSASignParam;
import kl.nbase.security.entity.param.crypto.asym.sign.impl.RSAVerifyParam;
import kl.nbase.security.entity.param.crypto.sym.impl.SymDecParam;
import kl.nbase.security.entity.param.crypto.sym.impl.SymEncParam;
import kl.npki.base.core.biz.check.ISelfCheckItem;
import kl.npki.base.core.biz.check.model.*;
import kl.npki.base.core.biz.inspection.InspectionItemTypeEnum;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.npki.base.core.thread.ThreadExecutorFactory;
import kl.npki.management.core.constants.SelfCheckConstant;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.KeyPair;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

import static kl.npki.management.core.constants.I18nConstant.getBaseMgmtCoreI18nMessage;

/**
 * 加密机服务自检
 *
 * <AUTHOR>
 * @date 2024/12/26 14:12
 */
public class EmEngineServiceCheckItem implements ISelfCheckItem {


    private static final Logger log = LoggerFactory.getLogger(EmEngineServiceCheckItem.class);

    private static boolean checkResultFlag = false;

    private static final String ASYNC_BIZ_NAME = "engineSelfCheck";

    public synchronized void setCheckResultFlag(boolean checkResultFlag) {
        EmEngineServiceCheckItem.checkResultFlag = checkResultFlag;
    }

    @Override
    public SelfCheckResult check() {
        I18nMessages i18n = getI18n();
        StopWatch stopWatch = new StopWatch();
        // 设置密码机服务
        ClusterEngine engine = EngineHolder.get();
        SelfCheckResult result = before(stopWatch, engine, i18n);
        // 构造 adapter
        KJCCSelfCheckAdapter adapter = new KJCCSelfCheckAdapter(engine);
        //  1、密钥生成检测
        KeyPair keyPair = genKeyPairCheck(result, engine, i18n);

        ExecutorService executorService = ThreadExecutorFactory.getExecutorService(ASYNC_BIZ_NAME);
        List<CompletableFuture<Void>> completableFutureList = Lists.newArrayList();

        if (keyPair != null) {
            // 2. 非对称加解密检测
            completableFutureList.add(createAsyncTask(() -> asymEncDecCheck(result, keyPair, adapter, i18n), executorService));
            // 3. 非对称签名验签检测
            completableFutureList.add(createAsyncTask(() -> asymSignVerifyCheck(result, keyPair, adapter, i18n), executorService));
        }
        // 4. 对称加解密检测
        completableFutureList.add(createAsyncTask(() -> symEncDecCheck(result, adapter, i18n), executorService));
        // 5. 哈希检测
        completableFutureList.add(createAsyncTask(() -> hashCheck(result, adapter, i18n), executorService));

        // 等待所有任务完成
        try {
            CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            // 异常记录
            log.error("completable task execute failed", e);
        }

        // 结果汇总
        return after(result, stopWatch, i18n);
    }

    /**
     * 创建异步任务
     *
     * @param task     task
     * @param executor 线程池
     * @return CompletableFuture<Void>
     */
    private CompletableFuture<Void> createAsyncTask(Runnable task, ExecutorService executor) {
        return CompletableFuture.runAsync(task, executor)
                .exceptionally(ex -> {
                    log.error("async task failed", ex);
                    return null;
                });
    }

    private SelfCheckResult before(StopWatch stopWatch, ClusterEngine engine, I18nMessages i18n) {
        // 用于记录自检详情
        Map<String, Object> details = new LinkedHashMap<>();
        // 初始化检测结果标识为false
        setCheckResultFlag(false);
        stopWatch.start();
        //  // 添加密码机类型信息
        String engineAlias = engine.getEngineInfo().getEngineAlias();
        details.put(i18n.emengineType, engineAlias);
        // 设置通用项
        return SelfCheckResult.builder()
            .version(ConfigHolder.get().get(SysInfoConfig.class).getEnvironmentSwitchId())
            .code(getFullCode())
            .name(getName())
            .category(getCategory().getName())
            .checkTime(LocalDateTime.now())
            .details(details)
            .build();
    }

    private SelfCheckResult after(SelfCheckResult result, StopWatch stopWatch, I18nMessages i18n) {
        result.setGuidance(checkResultFlag ? "" : SelfCheckConstant.Paths.EMENGINE_GUIDE_PATH);
        result.setSeverity(checkResultFlag ? Severity.NONE : Severity.HIGH);
        result.setStatus(checkResultFlag ? Status.SUCCESS : Status.FAILURE);
        stopWatch.stop();
        result.setDuration(stopWatch.getTotalTimeMillis());
        result.setMessage(checkResultFlag ? i18n.emengineService + i18n.normal : i18n.emengineService + i18n.exception);
        setCheckResultFlag(false);
        return result;
    }

    private void hashCheck(SelfCheckResult result, KJCCSelfCheckAdapter adapter, I18nMessages i18n) {
        Map<String, Object> details = result.getDetails();
        // 5. 哈希检测
        // 构造哈希自检参数
        HashSelfCheckParam hashSelfCheckParam = new HashSelfCheckParam(RandomUtils.nextBytes(32), HashAlgo.SM3);
        // 执行检查
        ISelfCheckResult sm3HashResult = new HashSelfCheck(adapter).exec(hashSelfCheckParam);
        // 检查结果
        details.put(i18n.hash + i18n.check, sm3HashResult.getResult() ? i18n.hash + i18n.normal : i18n.hash + i18n.exception);
        setCheckResultFlag(checkResultFlag && sm3HashResult.getResult());
    }

    private void symEncDecCheck(SelfCheckResult result, KJCCSelfCheckAdapter adapter, I18nMessages i18n) {
        Map<String, Object> details = result.getDetails();
        // 4. 对称加解密检测
        // 构造对称加解密自检参数
        BlockSymAlgo symAlgo = BlockSymAlgo.SM4_CBC_NOPADDING;
        SymEncParam symEncParam = new SymEncParam(symAlgo.isNeedIv() ? RandomUtils.nextBytes(symAlgo.getIvLength()) : null);
        SymEncDecSelfCheckParam symEncSelfCheckParam = new SymEncDecSelfCheckParam(new RawByteSymKey(symAlgo, RandomUtils.nextBytes(symAlgo.getKeyLength())), OriginalData.wrap(RandomUtils.nextBytes(symAlgo.getBlockSize() * 3)),
            symEncParam, SymDecParam.from(symEncParam));
        // 执行检查
        ISelfCheckResult sm4EncResult = new SymEncDecSelfCheck(adapter).exec(symEncSelfCheckParam);
        // 检查结果
        details.put(i18n.symEncDec + i18n.check, sm4EncResult.getResult() ? i18n.symEncDec + i18n.normal : i18n.symEncDec + i18n.exception);
        setCheckResultFlag(checkResultFlag && sm4EncResult.getResult());
    }

    private void asymSignVerifyCheck(SelfCheckResult result, KeyPair keyPair, KJCCSelfCheckAdapter adapter, I18nMessages i18n) {
        Map<String, Object> details = result.getDetails();
        // 3. 签名验签检测
        // 构造非对称签名验签自检参数
        AsymSignVerifySelfCheckParam asymSignSelfCheckParam = new AsymSignVerifySelfCheckParam(keyPair, OriginalData.wrap(RandomUtils.nextBytes(32)),
                RSASignParam.builder().hashAlgo(HashAlgo.SHA3_256).build(), RSAVerifyParam.builder().hashAlgo(HashAlgo.SHA3_256).build());
        // 执行检查
        ISelfCheckResult sm2AsymSignResult = new AsymSignVerifySelfCheck(adapter).exec(asymSignSelfCheckParam);
        // 检查结果
        details.put(i18n.asymSignVerify + i18n.check, sm2AsymSignResult.getResult() ? i18n.asymSignVerify + i18n.normal : i18n.asymSignVerify + i18n.exception);
        setCheckResultFlag(checkResultFlag && sm2AsymSignResult.getResult());
    }

    private void asymEncDecCheck(SelfCheckResult result, KeyPair keyPair, KJCCSelfCheckAdapter adapter, I18nMessages i18n) {
        Map<String, Object> details = result.getDetails();
        // 2. 非对称加解密检测
        // 构造非对称加解密自检参数
        AsymEncDecSelfCheckParam asymEncSelfCheckParam = new AsymEncDecSelfCheckParam(keyPair, OriginalData.wrap(RandomUtils.nextBytes(32)), new RSAEncParam(), new RSADecParam());
        // 执行检查
        ISelfCheckResult sm2AsymEncResult = new AsymEncDecSelfCheck(adapter).exec(asymEncSelfCheckParam);
        // 检查结果
        details.put(i18n.asymEncDec + i18n.check, sm2AsymEncResult.getResult() ? i18n.asymEncDec + i18n.normal : i18n.asymEncDec + i18n.exception);
        setCheckResultFlag(checkResultFlag && sm2AsymEncResult.getResult());
    }

    private KeyPair genKeyPairCheck(SelfCheckResult result, ClusterEngine engine, I18nMessages i18n) {
        Map<String, Object> details = result.getDetails();
        // 生成用于自检的密钥对，通过该密钥对构造相应测试参数，实际测试时 KJCC 时，也可以使用内置密钥对进行自检
        // 1. 密钥对生成检测
        KeyPair keyPair;
        try {
            keyPair = engine.genKeyPair(AsymAlgo.RSA_2048);
            if (keyPair == null) {
                details.put(i18n.keyPairGeneration + i18n.check, i18n.keyPairGeneration + i18n.exception);
            } else {
                details.put(i18n.keyPairGeneration + i18n.check, i18n.keyPairGeneration + i18n.normal);
                setCheckResultFlag(true);
                return keyPair;
            }
        } catch (Exception e) {
            details.put(i18n.keyPairGeneration + i18n.check, i18n.keyPairGeneration + i18n.exception + e.getMessage());
        }
        return null;
    }


    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public String getName() {
        return SelfCheckItemEnum.EM_ENGINE_SERVICE_CHECK.getName();
    }

    @Override
    public Category getCategory() {
        return Category.CONFIGURATION;
    }

    @Override
    public InspectionItemTypeEnum getInspectionItemType() {
        return InspectionItemTypeEnum.INSPECTION_EXTERNAL_SERVER_STATUS;
    }

    @Override
    public String getCode() {
        return Category.CONFIGURATION.getCode() + SelfCheckItemEnum.EM_ENGINE_SERVICE_CHECK.getCode();
    }

    private I18nMessages getI18n() {
        I18nMessages i18n = new I18nMessages();
        i18n.init();
        return i18n;
    }

    private static class I18nMessages {
        String keyPairGeneration;
        String asymEncDec;
        String asymSignVerify;
        String symEncDec;
        String emengineService;
        String emengineType;
        String hash;
        String check;
        String normal;
        String exception;

        void init() {
            keyPairGeneration = getBaseMgmtCoreI18nMessage("key_generation");
            asymEncDec = getBaseMgmtCoreI18nMessage("asymmetric_encryption_and_decryption");
            asymSignVerify = getBaseMgmtCoreI18nMessage("asymmetric_signature_verification");
            symEncDec = getBaseMgmtCoreI18nMessage("symmetric_encryption_and_decryption");
            emengineService = getBaseMgmtCoreI18nMessage("password_machine_service");
            emengineType = getBaseMgmtCoreI18nMessage("password_machine_type");
            hash = getBaseMgmtCoreI18nMessage("hash");
            check = getBaseMgmtCoreI18nMessage("testing");
            normal = getBaseMgmtCoreI18nMessage("normal");
            exception = getBaseMgmtCoreI18nMessage("abnormal");
        }
    }

}
