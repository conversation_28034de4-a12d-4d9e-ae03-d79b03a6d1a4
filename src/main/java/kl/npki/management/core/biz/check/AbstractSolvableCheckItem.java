package kl.npki.management.core.biz.check;

import kl.npki.base.core.biz.check.SolvableCheckItem;
import kl.npki.base.core.biz.check.model.SelfCheckResult;
import kl.npki.base.core.biz.check.model.Status;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.management.core.biz.admin.model.entity.RoleEntity;
import kl.npki.management.core.repository.IRoleRepository;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static kl.npki.management.core.constants.I18nConstant.getBaseMgmtCoreI18nMessage;

/**
 * <AUTHOR>
 * @Date 2024/8/20
 */
public abstract class AbstractSolvableCheckItem implements SolvableCheckItem {

    /**
     * 获取自检项告警解决页面的资源编码
     */
    protected abstract String getGuidePageResourceCode();

    @Override
    public SelfCheckResult updateSelfCheckResultByRole(SelfCheckResult selfCheckResult, Set<String> roleCodeSet) {
        // 在新建的SelfCheckResult对象上修改，避免影响内存存储中的SelfCheckResult。
        SelfCheckResult newSelfCheckResult = selfCheckResult.clone();
        // 自检成功、数据库未配置等情况无需显示“解决“按钮
        if (Status.SUCCESS.equals(selfCheckResult.getStatus()) || !SystemUtil.isDataSourceConfigured()) {
            // guidance置空表示无需显示“解决”按钮
            newSelfCheckResult.setGuidance("");
            return newSelfCheckResult;
        }
        // 根据页面资源编码获取有权访问该页面的所有角色
        List<RoleEntity> roleEntityList = RepositoryFactory.get(IRoleRepository.class).getRoleByResourceCode(getGuidePageResourceCode());
        boolean hasPermission = false;
        for (RoleEntity roleEntity : roleEntityList) {
            // 判断角色是否匹配
            hasPermission = hasPermission || roleCodeSet.contains(roleEntity.getRoleCode());
        }
        // 权限不足则将guidance改为提示用户权限不足。
        if (!hasPermission) {
            String message = getBaseMgmtCoreI18nMessage("insufficient_permissions_please_log_in_to_the_system_as_an_administrator_with_the_following_roles");
            newSelfCheckResult.setGuidance(message + roleEntityList.stream().map(RoleEntity::getRoleName).collect(Collectors.toList()));
        }
        return newSelfCheckResult;
    }

}
