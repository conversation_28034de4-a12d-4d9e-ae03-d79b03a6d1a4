package kl.npki.management.core.biz.inspection.model;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.biz.inspection.model.InspectionItemResult;

import java.util.List;

/**
 * 系统巡检已分组巡检结果
 *
 * <AUTHOR>
 * @date 07/05/2025 19:56
 **/
public class InspectionGroupedResults {

    /**
     * 巡检项目类型
     */
    @Schema(description = "巡检项目类型")
    private String category;

    /**
     * 巡检项目名称
     */
    @Schema(description = "巡检项目结果列表")
    private List<InspectionItemResult> itemResults;

    public InspectionGroupedResults(String category, List<InspectionItemResult> itemResults) {
        this.category = category;
        this.itemResults = itemResults;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public List<InspectionItemResult> getItemResults() {
        return itemResults;
    }

    public void setItemResults(List<InspectionItemResult> itemResults) {
        this.itemResults = itemResults;
    }
}

