package kl.npki.management.core.biz.admin.model.info;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 2022/9/29
 * @desc
 */
public class AdminInfoDetailInfo extends AdminInfoListInfo {

    /**
     * 证书cn
     */
    @Schema(description = "证书cn")
    private String subjectCn;

    @Schema(description = "签名证书值")
    private String signCertValue;

    @Schema(description = "加密证书值")
    private String encCertValue;

    public String getSubjectCn() {
        return subjectCn;
    }

    public void setSubjectCn(String subjectCn) {
        this.subjectCn = subjectCn;
    }

    public String getSignCertValue() {
        return signCertValue;
    }

    public void setSignCertValue(String signCertValue) {
        this.signCertValue = signCertValue;
    }

    public String getEncCertValue() {
        return encCertValue;
    }

    public void setEncCertValue(String encCertValue) {
        this.encCertValue = encCertValue;
    }
}
