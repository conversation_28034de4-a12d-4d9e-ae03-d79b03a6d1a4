package kl.npki.management.core.biz.config.model;

import kl.nbase.cache.client.CacheType;
import kl.nbase.cache.config.CacheConfig;
import kl.nbase.cache.config.RedisConfigProperties;
import kl.nbase.cache.constants.RedisRunModeEnum;
import kl.nbase.cache.uitls.CacheConfigurationUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: guoq
 * @Date: 2024/1/30
 * @description: 缓存配置信息
 */
public class CacheConfigInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private boolean enabled;

    private String type;

    private String runMode;

    private boolean ssl;

    /**
     * 用户名，可选
     */
    private String username;

    /**
     * 访问密码，可选
     */
    private String password;

    /**
     * 连接超时时间，单位毫秒
     */
    private int connectTimeoutMills = 10000;

    /**
     * 响应读取超时时间，单位毫秒
     */
    private int readTimeoutMills = 3000;

    private List<AddressInfo> addressInfos;

    private String masterName;

    private String sentinelPassword;

    public CacheConfigInfo() {
    }

    public CacheConfigInfo(CacheConfig cacheConfig) {
        this.enabled = cacheConfig.isEnabled();
        this.type = cacheConfig.getType();
        RedisConfigProperties redis = cacheConfig.getRedis();
        this.runMode = redis.getRunMode();
        this.ssl = redis.isSsl();
        this.username = redis.getUsername();
        this.password = redis.getPassword();
        this.connectTimeoutMills = redis.getConnectTimeoutMills();
        this.readTimeoutMills = redis.getReadTimeoutMills();
        this.addressInfos = new ArrayList<>(4);
        String nodes = redis.getNodes();
        if (StringUtils.isEmpty(nodes)) {
            return;
        }
        String[] nodeList = nodes.split(CacheConfigurationUtils.REDIS_NODES_DELIMITER);
        for (String node : nodeList) {
            Pair<String, Integer> ipPort = CacheConfigurationUtils.readHostAndPortFromAddress(node);
            AddressInfo addressInfo = new AddressInfo();
            addressInfo.setHost(ipPort.getLeft());
            addressInfo.setPort(ipPort.getRight());
            this.addressInfos.add(addressInfo);
        }
        RedisConfigProperties.SentinelProperties sentinel = redis.getSentinel();
        if(ObjectUtils.isNotEmpty(sentinel)){
            this.masterName = sentinel.getMaster();
            this.sentinelPassword = sentinel.getPassword();
        }
    }

    public void updateCacheConfig(CacheConfig cacheConfig) {
        cacheConfig.setEnabled(enabled);
        if (!enabled) {
           return;
        }
        cacheConfig.setType(type);
        if (!CacheType.REDIS.getCode().equals(type)) {
            return;
        }
        RedisConfigProperties redisConfig = cacheConfig.getRedis();
        redisConfig.setRunMode(runMode);
        redisConfig.setSsl(ssl);
        redisConfig.setUsername(username);
        redisConfig.setPassword(password);
        redisConfig.setReadTimeoutMills(readTimeoutMills);
        redisConfig.setConnectTimeoutMills(connectTimeoutMills);
        redisConfig.setNodes(addressInfos.stream()
            .map(AddressInfo::toIpPort)
            .collect(Collectors.joining(CacheConfigurationUtils.REDIS_NODES_DELIMITER)));
        if(RedisRunModeEnum.SENTINEL.getRunMode().equals(runMode)){
            redisConfig.getSentinel().setMaster(masterName);
            redisConfig.getSentinel().setPassword(sentinelPassword);
        }
    }

    public CacheConfig toCacheConfig() {
        CacheConfig cacheConfig = new CacheConfig();
        RedisConfigProperties redisConfig = new RedisConfigProperties();
        redisConfig.setCluster(new RedisConfigProperties.ClusterProperties());
        redisConfig.setSentinel(new RedisConfigProperties.SentinelProperties());
        cacheConfig.setRedis(redisConfig);
        cacheConfig.setEnabled(true);
        updateCacheConfig(cacheConfig);
        return cacheConfig;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public CacheConfigInfo setEnabled(boolean enabled) {
        this.enabled = enabled;
        return this;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRunMode() {
        return runMode;
    }

    public void setRunMode(String runMode) {
        this.runMode = runMode;
    }

    public boolean isSsl() {
        return ssl;
    }

    public void setSsl(boolean ssl) {
        this.ssl = ssl;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getConnectTimeoutMills() {
        return connectTimeoutMills;
    }

    public void setConnectTimeoutMills(int connectTimeoutMills) {
        this.connectTimeoutMills = connectTimeoutMills;
    }

    public int getReadTimeoutMills() {
        return readTimeoutMills;
    }

    public void setReadTimeoutMills(int readTimeoutMills) {
        this.readTimeoutMills = readTimeoutMills;
    }

    public List<AddressInfo> getAddressInfos() {
        return addressInfos;
    }

    public void setAddressInfos(List<AddressInfo> addressInfos) {
        this.addressInfos = addressInfos;
    }

    public String getMasterName() {
        return masterName;
    }

    public void setMasterName(String masterName) {
        this.masterName = masterName;
    }

    public String getSentinelPassword() {
        return sentinelPassword;
    }

    public void setSentinelPassword(String sentinelPassword) {
        this.sentinelPassword = sentinelPassword;
    }
}
