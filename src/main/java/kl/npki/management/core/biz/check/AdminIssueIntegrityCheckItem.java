package kl.npki.management.core.biz.check;

import kl.nbase.helper.stopwatch.StopWatch;
import kl.npki.base.core.biz.check.model.*;
import kl.npki.base.core.biz.inspection.InspectionItemTypeEnum;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.constant.CertStatus;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import kl.npki.management.core.biz.admin.model.entity.RoleEntity;
import kl.npki.management.core.constants.RoleCodeEnum;
import kl.npki.management.core.constants.SelfCheckConstant;
import kl.npki.management.core.repository.IAdminCertRepository;
import kl.npki.management.core.repository.IAdminInfoRepository;
import kl.npki.management.core.repository.IRoleRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static kl.npki.management.core.constants.I18nConstant.getBaseMgmtCoreI18nMessage;
import static kl.npki.management.core.constants.SelfCheckConstant.*;

/**
 * 管理员签发完整性自检,检测超级管理员、安全管理员、审计管理员、业务管理员、业务操作员、审计员是否签发
 *
 * <AUTHOR>
 * @date 2023/10/17
 */
public class AdminIssueIntegrityCheckItem extends AbstractSolvableCheckItem {

    private static final Logger log = LoggerFactory.getLogger(AdminIssueIntegrityCheckItem.class);

    private final IAdminInfoRepository adminInfoRepository;
    private final IAdminCertRepository adminCertRepository;
    private final IRoleRepository roleRepository;

    public AdminIssueIntegrityCheckItem() {
        this.adminInfoRepository = RepositoryFactory.get(IAdminInfoRepository.class);
        this.adminCertRepository = RepositoryFactory.get(IAdminCertRepository.class);
        this.roleRepository = RepositoryFactory.get(IRoleRepository.class);
    }

    @Override
    public SelfCheckResult check() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        // 设置通用项
        SelfCheckResult checkResult = SelfCheckResult.builder()
                .version(ConfigHolder.get().get(SysInfoConfig.class).getEnvironmentSwitchId())
                .code(getFullCode())
                .name(getName())
                .category(getCategory().getName())
                .checkTime(LocalDateTime.now())
                .guidance(SelfCheckConstant.Paths.ADMIN_GUIDE_PATH)
                .build();
        // key是角色，value是签发情况
        Map<String, Object> details = new LinkedHashMap<>();

        List<AdminEntity> adminEntities;
        try {
            // 查询所有合法的管理员信息
            adminEntities = adminInfoRepository.findValidAll();
        } catch (Exception e) {
            stopWatch.stop();
            String message = getBaseMgmtCoreI18nMessage("failed_to_obtain_administrator_information");
            details.put(message, e.getMessage());
            checkResult.setMessage(message);
            checkResult.setDetails(details);
            checkResult.setStatus(Status.FAILURE);
            checkResult.setSeverity(Severity.HIGH);
            checkResult.setDuration(stopWatch.getTotalTimeMillis());
            log.error(message, e);
            return checkResult;
        }

        // 对超级管理员、安全管理员、审计管理员、业务管理员、业务操作员、审计员进行检测
        List<RoleEntity> roleEntityList = roleRepository.getAllSystemRole();
        for (RoleEntity roleEntity : roleEntityList) {
            details.put(roleEntity.getRoleName(), getBaseMgmtCoreI18nMessage(NOT_ISSUED_I18N_KEY));
        }
        String message = getBaseMgmtCoreI18nMessage(UNASSIGNED_ADMIN_ROLES_I18N_KEY);
        for (AdminEntity adminEntity : adminEntities) {
            // 获取该管理员对应角色
            RoleCodeEnum roleCodeEnum = RoleCodeEnum.getRoleCodeEnumByRoleCode(adminEntity.getRoleCode());
            String roleName = Objects.nonNull(roleCodeEnum) ? roleCodeEnum.getDesc() : null;
            if (details.containsKey(roleName)) {
                try {
                    // 检测该管理员是否已签发
                    checkCertStatus(adminEntity, details, roleName);
                } catch (Exception e) {
                    message = getBaseMgmtCoreI18nMessage("partial_administrators_failed_to_issue_status_checks");
                    details.put(roleName, getBaseMgmtCoreI18nMessage("detection_failed", e.getMessage()));
                }
            }
        }
        // 判断所有管理员是否均已签发
        boolean isAllIssued = isAllIssued(details, roleEntityList);
        if (isAllIssued) {
            // 所有管理员均已签发，移除引导信息
            checkResult.setGuidance(StringUtils.EMPTY);
        }
        stopWatch.stop();
        checkResult.setDetails(details);
        checkResult.setStatus(isAllIssued ? Status.SUCCESS : Status.WARNING);
        checkResult.setSeverity(isAllIssued ? Severity.NONE : Severity.HIGH);
        checkResult.setMessage(isAllIssued ? getBaseMgmtCoreI18nMessage(ALL_ADMINS_ISSUED_CORRECTLY_I18N_KEY) : message);
        checkResult.setDuration(stopWatch.getTotalTimeMillis());
        return checkResult;
    }

    /**
     * 检测管理员是否已签发
     */
    private void checkCertStatus(AdminEntity adminEntity, Map<String, Object> details, String roleName) {
        // 检测相应签发的管理员证书合法性
        AdminCertEntity adminCert = adminCertRepository.getByAdminInfoId(adminEntity.getId());
        if (adminCert != null && adminCert.getSignCertValue() != null && adminCert.getStatus() >= CertStatus.ISSUED.getCode()) {
            details.put(roleName, getBaseMgmtCoreI18nMessage(ISSUED_I18N_KEY));
        }
    }

    /**
     * 判断是否所有管理员均已签发
     */
    private boolean isAllIssued(Map<String, Object> details, List<RoleEntity> roleEntityList) {
        for (RoleEntity roleEntity : roleEntityList) {
            String roleName = roleEntity.getRoleName();
            if (!getBaseMgmtCoreI18nMessage(ISSUED_I18N_KEY).equals(details.get(roleName))) {
                return false;
            }
        }
        return true;
    }

    @Override
    protected String getGuidePageResourceCode() {
        return Paths.PERSONNEL_MANAGEMENT_PATH_RESOURCE_CODE;
    }

    @Override
    public boolean isEnabled() {
        // 正式部署环境开启自检
        return SystemUtil.isDeployed();
    }

    @Override
    public String getName() {
        return SelfCheckItemEnum.ADMIN_ISSUE_INTEGRITY_CHECK.getName();
    }

    @Override
    public Category getCategory() {
        return Category.ADMIN;
    }

    @Override
    public InspectionItemTypeEnum getInspectionItemType() {
        return InspectionItemTypeEnum.INSPECTION_SYSTEM_RESOURCE;
    }

    @Override
    public String getCode() {
        return Category.ADMIN.getCode() + SelfCheckItemEnum.ADMIN_ISSUE_INTEGRITY_CHECK.getCode();
    }

    @Override
    public SelfCheckResult updateSelfCheckResultByRole(SelfCheckResult selfCheckResult, Set<String> roleCodeSet) {
        // 在新建的SelfCheckResult对象上修改，避免影响内存存储中的SelfCheckResult。
        SelfCheckResult newSelfCheckResult = selfCheckResult.clone();
        // 自检成功、数据库未配置等情况无需显示“解决“按钮
        if (Status.SUCCESS.equals(selfCheckResult.getStatus()) || !SystemUtil.isDataSourceConfigured()) {
            // guidance置空表示无需显示“解决”按钮
            newSelfCheckResult.setGuidance("");
            return newSelfCheckResult;
        }
        // 根据页面资源编码获取有权访问该页面的所有角色
        List<RoleEntity> roleEntityList = RepositoryFactory.get(IRoleRepository.class).getRoleByResourceCode(getGuidePageResourceCode());
        List<RoleEntity> allRoles = roleRepository.getAllSystemRole();
        boolean hasPermission = false;
        for (RoleEntity roleEntity : roleEntityList) {
            // 判断角色是否匹配
            hasPermission = hasPermission || roleCodeSet.contains(roleEntity.getRoleCode());
        }
        // 权限不足则将guidance改为提示用户权限不足。
        if (!hasPermission) {
            String message = getBaseMgmtCoreI18nMessage("insufficient_permissions_please_log_in_to_the_system_as_an_administrator_with_the_following_roles");
            Map<String, Object> details = selfCheckResult.getDetails();
            List<String> notIssuedRoleNames = details.entrySet().stream().filter(entry ->
                entry.getValue().equals(getBaseMgmtCoreI18nMessage(NOT_ISSUED_I18N_KEY))).map(Map.Entry::getKey).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notIssuedRoleNames)) {
                // 查找对应的角色实体
                List<String> parentRoleNames = allRoles.stream()
                    .filter(role -> notIssuedRoleNames.contains(role.getRoleName()))
                    .map(roleEntity1 -> {
                        if (roleEntity1.getParentRoleCode() != null) {
                            RoleEntity parentRole = roleRepository.getRoleByCode(roleEntity1.getParentRoleCode());
                            return parentRole.getRoleName();
                        }
                        return null;
                    }).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(parentRoleNames)) {
                    newSelfCheckResult.setGuidance(message + parentRoleNames);
                }
            }

        }
        return newSelfCheckResult;
    }
}
