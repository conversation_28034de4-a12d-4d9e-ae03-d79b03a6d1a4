package kl.npki.management.core.biz.config.model;

import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.management.core.repository.ISystemConfigRepository;

import java.time.LocalDateTime;

/**
 * 系统配置表实体类
 *
 * <AUTHOR>
 * @date 2022/8/26 17:09
 */
public class SystemConfigEntity {

    private final ISystemConfigRepository systemConfigRepository;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 配置名称
     */
    private String configName;
    /**
     * 配置序号
     */
    private Integer configOrder;
    /**
     * 配置文件名称
     */
    private String configFileName;

    /**
     * 配置文件内容
     */
    private String configFileValue;
    /**
     * 配置类名
     */
    private String configClass;
    /**
     * 部署状态0:未完成,1:已完成
     */
    private Integer isComplete;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 更新者id
     */
    private Long updateBy;

    public SystemConfigEntity() {
        this.systemConfigRepository = RepositoryFactory.get(ISystemConfigRepository.class);
    }

    public ISystemConfigRepository getSystemConfigRepository() {
        return systemConfigRepository;
    }

    public Integer getId() {
        return id;
    }

    public SystemConfigEntity setId(Integer id) {
        this.id = id;
        return this;
    }

    public String getConfigName() {
        return configName;
    }

    public SystemConfigEntity setConfigName(String configName) {
        this.configName = configName;
        return this;
    }

    public Integer getConfigOrder() {
        return configOrder;
    }

    public SystemConfigEntity setConfigOrder(Integer configOrder) {
        this.configOrder = configOrder;
        return this;
    }

    public String getConfigFileName() {
        return configFileName;
    }

    public SystemConfigEntity setConfigFileName(String configFileName) {
        this.configFileName = configFileName;
        return this;
    }

    public String getConfigFileValue() {
        return configFileValue;
    }

    public SystemConfigEntity setConfigFileValue(String configFileValue) {
        this.configFileValue = configFileValue;
        return this;
    }

    public String getConfigClass() {
        return configClass;
    }

    public SystemConfigEntity setConfigClass(String configClass) {
        this.configClass = configClass;
        return this;
    }

    public Integer getIsComplete() {
        return isComplete;
    }

    public SystemConfigEntity setIsComplete(Integer isComplete) {
        this.isComplete = isComplete;
        return this;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public SystemConfigEntity setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
        return this;
    }

    public void save() {
        setCreateTime(LocalDateTime.now());
        systemConfigRepository.save(this);
    }

    public void update() {
        setUpdateTime(LocalDateTime.now());
        systemConfigRepository.update(this);
    }

}
