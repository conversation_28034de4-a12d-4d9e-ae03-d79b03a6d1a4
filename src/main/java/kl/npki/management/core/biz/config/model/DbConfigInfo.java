package kl.npki.management.core.biz.config.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.util.Objects;

import static kl.npki.management.core.constants.I18nParameterVerifyConstant.*;

/**
 * <AUTHOR>
 */
public class DbConfigInfo implements Serializable {
    private static final long serialVersionUID = 58360433544238324L;
    @Schema(description = "数据库服务端口")
    private int dbPort;
    @Schema(description = "数据库类型，主库或从库")
    private String dbRoleType;
    @NotBlank(message = THE_DATABASE_ADDRESS_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = THE_LENGTH_OF_THE_DATABASE_ADDRESS_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = THE_DATABASE_ADDRESS_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "数据库ip地址")
    private String dbAddress;
    @NotBlank(message = THE_DATABASE_NAME_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = THE_LENGTH_OF_THE_DATABASE_NAME_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = THE_DATABASE_NAME_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "数据库名称")
    private String dbName;
    @NotBlank(message = THE_DATABASE_USERNAME_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = THE_LENGTH_OF_THE_DATABASE_USERNAME_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = THE_DATABASE_USERNAME_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "数据库用户名")
    private String username;
    @Size(max = 128, message = THE_LENGTH_OF_THE_DATABASE_PASSWORD_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = THE_DATABASE_PASSWORD_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "数据库密码")
    private String password;
    @NotBlank(message = THE_DATABASE_CHARACTER_SET_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = THE_LENGTH_OF_THE_DATABASE_CHARACTER_SET_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = THE_DATABASE_CHARACTER_SET_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "数据库字符集")
    private String charSet;
    @Schema(description = "时区")
    private String timeZone;
    @Schema(description = "从机ip")
    private String slaveIp;
    @Schema(description = "从机端口")
    private Integer slavePort;

    @Override
    public String toString() {
        return "DbConfigInfo{" + "dbPort=" + dbPort + ", dbRoleType='" + dbRoleType + '\'' + ", dbAddress='" + dbAddress + '\'' + ", dbName='" + dbName + '\'' + ", username='" + username + '\'' + ", password='" + password + '\'' + ", charSet='" + charSet + '\'' + ", timeZone='" + timeZone + '\'' + ", slaveIp='" + slaveIp + '\'' + ", slavePort=" + slavePort + '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DbConfigInfo)) return false;
        DbConfigInfo that = (DbConfigInfo) o;
        return getDbPort() == that.getDbPort() && Objects.equals(getDbRoleType(), that.getDbRoleType()) && Objects.equals(getDbAddress(), that.getDbAddress()) && Objects.equals(getDbName(), that.getDbName()) && Objects.equals(getUsername(), that.getUsername()) && Objects.equals(getPassword(), that.getPassword()) && Objects.equals(getCharSet(), that.getCharSet()) && Objects.equals(getTimeZone(), that.getTimeZone()) && Objects.equals(getSlaveIp(), that.getSlaveIp()) && Objects.equals(getSlavePort(), that.getSlavePort());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getDbPort(), getDbRoleType(), getDbAddress(), getDbName(), getUsername(), getPassword(),
            getCharSet(), getTimeZone(), getSlaveIp(), getSlavePort());
    }

    public String getDbRoleType() {
        return dbRoleType;
    }

    public void setDbRoleType(String dbRoleType) {
        this.dbRoleType = dbRoleType;
    }

    public int getDbPort() {
        return dbPort;
    }

    public void setDbPort(int dbPort) {
        this.dbPort = dbPort;
    }

    public String getDbAddress() {
        return dbAddress;
    }

    public void setDbAddress(String dbAddress) {
        this.dbAddress = dbAddress;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getCharSet() {
        return charSet;
    }

    public void setCharSet(String charSet) {
        this.charSet = charSet;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getSlaveIp() {
        return slaveIp;
    }

    public void setSlaveIp(String slaveIp) {
        this.slaveIp = slaveIp;
    }

    public Integer getSlavePort() {
        return slavePort;
    }

    public void setSlavePort(Integer slavePort) {
        this.slavePort = slavePort;
    }
}
