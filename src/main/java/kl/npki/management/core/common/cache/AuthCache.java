package kl.npki.management.core.common.cache;

import kl.npki.base.core.common.ICommonCache;

/**
 * <AUTHOR>
 * @since 2025/7/15 上午11:13
 */
public enum AuthCache implements ICommonCache {

    /**
     * 单例对象
     */
    INSTANCE;

    private static final String CACHE_ID = "base:auth";

    /**
     * 获取缓存
     *
     * @param authRefId 认证ID
     * @return sourceData 签名源数据
     */
    public String get(String authRefId) {
        return getClient().get(wrapCacheKey(authRefId));
    }

    /**
     * 设置缓存
     *
     * @param authRefId  认证ID
     * @param sourceData 签名源数据
     * @param timeToLive 过期时间，单位毫秒
     */
    public void set(String authRefId, String sourceData, long timeToLive) {
        getClient().set(wrapCacheKey(authRefId), sourceData, timeToLive);
    }

    /**
     * 移除缓存
     *
     * @param authRefId 认证ID
     */
    public void remove(String authRefId) {
        getClient().delete(wrapCacheKey(authRefId));
    }

    @Override
    public String getId() {
        return CACHE_ID;
    }
}
