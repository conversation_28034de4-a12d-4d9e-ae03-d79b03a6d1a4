package kl.npki.management.core.constants;

import static kl.npki.management.core.constants.I18nConstant.BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX;


/**
 * i18n参数验证常量
 *
 * <AUTHOR>
 * @date 2025/01/22
 */
public final class I18nParameterVerifyConstant {

    // region kl.npki.management.core.biz.config.model.DbConfigInfo
    /**
     * 数据库地址不能为空
     */
    public static final String THE_DATABASE_ADDRESS_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_database_address_cannot_be_empty";
    /**
     * 数据库地址长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_DATABASE_ADDRESS_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_length_of_the_database_address_cannot_exceed_128_characters";
    /**
     * 数据库地址不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String THE_DATABASE_ADDRESS_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_database_address_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00";
    /**
     * 数据库名称不能为空
     */
    public static final String THE_DATABASE_NAME_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_database_name_cannot_be_empty";
    /**
     * 数据库名称长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_DATABASE_NAME_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_length_of_the_database_name_cannot_exceed_128_characters";
    /**
     * 数据库名称不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String THE_DATABASE_NAME_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_database_name_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00";
    /**
     * 数据库用户名不能为空
     */
    public static final String THE_DATABASE_USERNAME_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_database_username_cannot_be_empty";
    /**
     * 数据库用户名长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_DATABASE_USERNAME_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_length_of_the_database_username_cannot_exceed_128_characters";
    /**
     * 数据库用户名不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String THE_DATABASE_USERNAME_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_database_username_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00";
    /**
     * 数据库密码不能为空
     */
    public static final String THE_DATABASE_PASSWORD_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_database_password_cannot_be_empty";
    /**
     * 数据库密码长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_DATABASE_PASSWORD_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_length_of_the_database_password_cannot_exceed_128_characters";
    /**
     * 数据库密码不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String THE_DATABASE_PASSWORD_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_database_password_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00";
    /**
     * 数据库字符集不能为空
     */
    public static final String THE_DATABASE_CHARACTER_SET_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_database_character_set_cannot_be_empty";
    /**
     * 数据库字符集长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_DATABASE_CHARACTER_SET_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_length_of_the_database_character_set_cannot_exceed_128_characters";
    /**
     * 数据库字符集不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String THE_DATABASE_CHARACTER_SET_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_database_character_set_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00";
    // endregion



    // region kl.npki.management.core.service.config.LogConfigService
    /**
     * 不能小于1GB
     */
    public static final String   MUST_NOT_BE_LESS_THAN_1GB_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "must_not_be_less_than_1gb";
    /**
     * 不能超过1TB
     */
    public static final String   CANNOT_EXCEED_1TB_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "cannot_exceed_1tb";

    // endregion


}
