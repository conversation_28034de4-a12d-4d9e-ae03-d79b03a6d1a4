package kl.npki.management.core.constants;

/**
 * <AUTHOR>
 * @date 2022/11/4 16:04
 * @desc
 */
public enum PropertyKey {
    
    /**
     * 是否初始化完成
     */
    INIT_COMPLETE("initComplete"),
    /**
     * 日志文件位置
     */
    LOGGING_FILE("logging.file"),
    /**
     * 日志文件大小
     */
    LOGGING_LOGBACK_ROLLINGPOLICY_MAX_FILE_SIZE("logging.logback.rollingpolicy.max.file.size"),
    /**
     * 日志等级
     */
    LOGGING_LEVEL_ROOT("logging.level");
    
    String value;
    
    PropertyKey(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
}
