package kl.npki.management.core.constants;

public enum ConfigType {
    DB("db"),
    HISDB("hisdb"),
    LOG("log"),
    ENGINE("engine"),
    MANAGER_ROOT("manager_root"),
    ADMIN("admin"),
    SSL_SITE_CERTIFICATE("ssl_site_certificate"),
    KM_CERTIFICATE("km_certificate"),
    CA("ca"),
    LICENSE("license")
    ;

    private String configName;

    ConfigType(String configName) {
        this.configName = configName;
    }

    public String getConfigName() {
        return configName;
    }

    public static ConfigType getType(String configName) {
        ConfigType configType = null;
        for (ConfigType value : ConfigType.values()) {
            if (configName.substring(0, configName.indexOf(".")).equals(value.getConfigName())) {
                configType = value;
                break;
            }
        }
        return configType;
    }
}
