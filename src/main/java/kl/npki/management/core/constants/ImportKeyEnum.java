package kl.npki.management.core.constants;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.management.core.constants.I18nConstant.BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 加密证书导入key的方式枚举
 * 
 * <AUTHOR>
 * @date 2023/2/15
 */
public enum ImportKeyEnum implements EnumI18n {

    PROTECTED_KEY_IMPORT(1, "通过保护密钥对的方式进行导入"),
    
    PFX_IMPORT(2, "通过pfx类型的证书进行导入"),

    CERT_IMPORT(3, "通过证书进行导入")
    ;


    private int code;
    
    private String desc;

    ImportKeyEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return tr();
    }

    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
