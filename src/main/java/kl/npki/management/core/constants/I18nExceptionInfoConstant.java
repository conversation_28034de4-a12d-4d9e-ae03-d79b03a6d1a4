package kl.npki.management.core.constants;

import static kl.npki.management.core.constants.I18nConstant.BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX;


/**
 * i18n异常信息常量
 *
 * <AUTHOR>
 * @date 2025/01/22
 */
public final class I18nExceptionInfoConstant {

    // region kl.npki.management.core.service.permission.impl.PermissionServiceImpl
    /**
     * 数据清理操作过滤资源权限失败
     */
    public static final String FAILED_TO_PERFORM_DATA_CLEANING_OPERATION_AFTER_FILTERING_RESOURCE_PERMISSIONS_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "failed_to_perform_data_cleaning_operation_after_filtering_resource_permissions";
    // endregion

    // region kl.npki.management.core.service.log.impl.RunLogServiceImpl
    /**
     * 读取文件[{0}]内容失败
     */
    public static final String FAILED_TO_READ_FILE_CONTENT_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "failed_to_read_file_content";
    /**
     * 日志路径不存在
     */
    public static final String LOG_PATH_NOT_FOUND_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "log_path_not_found";
    // endregion

    // region kl.npki.management.core.biz.admin.model.entity.AdminEntity
    /**
     * 加密证书与签名证书DN项不匹配
     */
    public static final String THE_DN_ENTRIES_OF_THE_ENCRYPTION_CERTIFICATE_AND_THE_SIGNATURE_CERTIFICATE_DO_NOT_MATCH_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_dn_entries_of_the_encryption_certificate_and_the_signature_certificate_do_not_match";
    /**
     * 当前角色为：{0}，需要的角色为：{1}
     */
    public static final String THE_ROLE_DOES_NOT_MEET_THE_REQUIREMENTS_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_role_does_not_meet_the_requirements";
    /**
     * 当前用户状态为: {0}，期望的用户状态为:{1}
     */
    public static final String USER_STATUS_DOES_NOT_MATCH_EXPECTATIONS_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "user_status_does_not_match_expectations";
    /**
     * 用户名重复，用户名：{0}
     */
    public static final String DUPLICATE_USER_NAMES_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "duplicate_user_names";
    /**
     * 用户名不一致
     */
    public static final String INCONSISTENT_USER_NAMES_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "inconsistent_user_names";
    // endregion

    // region kl.npki.management.core.service.SecurityService
    /**
     * 角色名已存在
     */
    public static final String THE_ROLE_NAME_ALREADY_EXISTS_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_role_name_already_exists";
    /**
     * 角色编码已存在
     */
    public static final String THE_ROLE_CODE_ALREADY_EXISTS_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_role_code_already_exists";
    /**
     * 请先删除使用该角色的用户
     */
    public static final String PLEASE_DELETE_THE_USER_USING_THIS_ROLE_FIRST_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "please_delete_the_user_using_this_role_first";
    // endregion

    // region kl.npki.management.core.service.cert.impl.SslCertServiceImpl
    /**
     * SSL证书签发失败
     */
    public static final String SSL_CERT_ISSUE_ERROR_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "ssl_cert_issue_error";
    /**
     *  SSL证书签发失败
     */
    public static final String SSL_CERT_ISSUE_BY_CA_ERROR_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "ssl_cert_issue_by_ca_error";
    /**
     * 无法找到对应的ssl客户端证书，请检查id:{0}是否正确，并确保已有对应的证书存在
     */
    public static final String UNABLE_TO_FIND_THE_CORRESPONDING_SSL_CLIENT_CERTIFICATE_PLEASE_CHECK_THE_ID_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "unable_to_find_the_corresponding_ssl_client_certificate_please_check_the_id";

    /**
     * SSL协议不能为空
     */
    public static final String SSL_PROTOCOL_CANNOT_BE_EMPTY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "ssl_protocol_cannot_be_empty";
    // endregion

    // region kl.npki.management.core.biz.admin.model.entity.AdminCertEntity
    /**
     * 当前证书(CN={0})状态为: {1}, 期望的证书状态为: {2}
     */
    public static final String CERTIFICATE_STATUS_DOES_NOT_MATCH_EXPECTATIONS_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "certificate_status_does_not_match_expectations";
    // endregion

    // region kl.npki.management.core.service.cert.impl.IdCertServiceImpl
    /**
     * 身份证书签发失败
     */
    public static final String ID_CERT_ISSUE_ERROR_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "id_cert_issue_error";
    /**
     * 身份证书更新失败
     */
    public static final String ID_CERT_UPDATE_ERROR_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "id_cert_update_error";
    // endregion

    // region kl.npki.management.core.service.auth.impl.AuthnServiceImpl
    /**
     * 无效的认证类型{0}
     */
    public static final String INVALID_AUTHENTICATION_TYPE_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "invalid_authentication_type";
    /**
     * SSL证书{0}不存在
     */
    public static final String SSL_CERTIFICATE_DOES_NOT_EXIST_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "ssl_certificate_does_not_exist";
    /**
     * cookie为空，ssl通道验证失败
     */
    public static final String COOKIE_IS_EMPTY_SSL_CHANNEL_VERIFICATION_FAILED_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "cookie_is_empty_ssl_channel_verification_failed";
    /**
     * ssl认证登录，cookie信息需要包含十六进制证书序列号
     */
    public static final String SSL_AUTHENTICATION_LOGIN_THE_COOKIE_INFORMATION_NEEDS_TO_INCLUDE_THE_HEXADECIMAL_CERTIFICATE_SERIAL_NUMBER_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "ssl_authentication_login_the_cookie_information_needs_to_include_the_hexadecimal_certificate_serial_number";
    // endregion

    // region kl.npki.management.core.service.AdminMgrService
    /**
     * 已经初始化【{0}】个管理员
     */
    public static final String NUMBER_OF_INITIALIZED_ADMINISTRATORS_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "number_of_initialized_administrators";
    /**
     * 已经备份【{0}】个管理员
     */
    public static final String NUMBER_OF_BACKED_UP_ADMINISTRATORS_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "number_of_backed_up_administrators";
    /**
     * 导入的证书不是签名证书
     */
    public static final String THE_IMPORTED_CERTIFICATE_IS_NOT_A_SIGNED_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_imported_certificate_is_not_a_signed_certificate";
    /**
     * 导入的证书不是加密证书
     */
    public static final String THE_IMPORTED_CERTIFICATE_IS_NOT_AN_ENCRYPTED_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_imported_certificate_is_not_an_encrypted_certificate";
    /**
     * 原加密证书不为空，导入的加密证书为空
     */
    public static final String THE_ORIGINAL_ENCRYPTION_CERTIFICATE_IS_NOT_EMPTY_THE_IMPORTED_ENCRYPTION_CERTIFICATE_IS_EMPTY_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_original_encryption_certificate_is_not_empty_the_imported_encryption_certificate_is_empty";
    /**
     * 没有请求废除该类型管理员证书的权限
     */
    public static final String NO_PERMISSION_TO_REQUEST_REVOCATION_OF_THIS_TYPE_OF_ADMINISTRATOR_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "no_permission_to_request_revocation_of_this_type_of_administrator_certificate";
    /**
     * 没有废除该类型管理员的权限
     */
    public static final String THE_ADMINISTRATORS_PERMISSION_FOR_THIS_TYPE_HAS_NOT_BEEN_REVOKED_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "the_administrators_permission_for_this_type_has_not_been_revoked";
    /**
     * 没有撤销该类型管理员证书废除申请的权限
     */
    public static final String DO_NOT_HAVE_THE_AUTHORITY_TO_REVOKE_THE_ADMINISTRATOR_CERTIFICATE_REVOCATION_APPLICATION_FOR_THIS_TYPE_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "do_not_have_the_authority_to_revoke_the_administrator_certificate_revocation_application_for_this_type";
    /**
     * 没有注销该类型管理员的权限
     */
    public static final String DO_NOT_HAVE_PERMISSION_TO_LOG_OUT_OF_THIS_TYPE_OF_ADMINISTRATOR_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "do_not_have_permission_to_log_out_of_this_type_of_administrator";
    /**
     * 没有延期该类型管理员的权限
     */
    public static final String NO_EXTENSION_OF_ADMINISTRATOR_PRIVILEGES_FOR_THIS_TYPE_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "no_extension_of_administrator_privileges_for_this_type";
    /**
     * 管理员输入的旧密码错误
     */
    public static final String ADMINISTRATOR_ENTERED_INCORRECT_OLD_PASSWORD_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "administrator_entered_incorrect_old_password";
    /**
     * 更新密码时输入的旧密码与新密码不能相同
     */
    public static final String ADMINISTRATOR_ENTERED_OLD_AND_NEW_PASSWORD_CANNOT_BE_THE_SAME_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "administrator_entered_old_and_new_password_cannot_be_the_same";
    /**
     * 重复的SubjectCn
     */
    public static final String REPEATED_SUBJECTCN_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "repeated_subjectcn";
    /**
     * 构建抗量子密钥信封失败
     */
    public static final String FAILED_TO_CONSTRUCT_ANTI_QUANTUM_KEY_ENVELOPE_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "failed_to_construct_anti_quantum_key_envelope";
    /**
     * 构建数字信封失败
     */
    public static final String FAILED_TO_CONSTRUCT_ENVELOPED_DATA_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "failed_to_construct_enveloped_data";
    /**
     * 没有延期该类型管理员证书的权限
     */
    public static final String DO_NOT_HAVE_THE_AUTHORITY_TO_EXTEND_THIS_TYPE_OF_ADMINISTRATOR_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "do_not_have_the_authority_to_extend_this_type_of_administrator_certificate";
    /**
     * 抗量子加密密钥类型未指定
     */
    public static final String ANTI_QUANTUM_ENCRYPTION_KEY_TYPE_NOT_SPECIFIED_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "anti_quantum_encryption_key_type_not_specified";
    // endregion

    // region kl.npki.management.core.service.help.SysHelpTxtServiceImpl
    /**
     * 不支持的系统模块:{0}
     */
    public static final String UNSUPPORTED_SYSTEM_MODULE_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "unsupported_system_module";
    // endregion

    // region kl.npki.management.core.service.config.EngineConfigService
    /**
     * 获取engine信息为空
     */
    public static final String GET_ENGINE_INFO_IS_EMPTY_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "get_engine_info_is_empty";
    // endregion

    // region kl.npki.management.core.service.kcsp.impl.KcspSvcSyncServiceImpl
    /**
     * 密码机服务地址解析失败
     */
    public static final String PASSWORD_MACHINE_SERVICE_ADDRESS_RESOLUTION_FAILED_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "password_machine_service_address_resolution_failed";

    /**
     * P10内容错误:{0}
     */
    public static final String THE_P10_CONTENT_ERROR_I18N_KEY = BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + "pkcs10_content_error";

}
