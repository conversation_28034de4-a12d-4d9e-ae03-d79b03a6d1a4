package kl.npki.management.core.constants;

import kl.nbase.i18n.i18n.EnumI18n;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.BaseConstant;

import java.util.*;
import java.util.stream.Collectors;

import static kl.npki.management.core.constants.I18nConstant.BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * <AUTHOR>
 */
public enum RoleCodeEnum implements EnumI18n {
    /**
     * 初始化三管理员， 超级管理员，安全管理员，审计管理员
     */
    SUPER_ADMIN("超级管理员", "1", null),

    SECURITY_ADMIN("安全管理员", "2", null),

    AUDIT_ADMIN("审计管理员", "4", null),

    BIZ_ADMIN("业务管理员", "8", "1"),

    AUDIT_OPER("审计员", "16", "4"),

    BIZ_OPER("业务操作员", "32", "8"),

    DEPLOY_OPER("部署操作员", "33", null),

    /**
     * 司法取证人员是KM特有的角色
     */
    LAW_ADMIN("司法取证人员", "48", "8", BaseConstant.NPKI_KM_SHORT_NAME),

    ;

    private static final Map<String, RoleCodeEnum> ROLE_CODE_ENUM_MAP = new HashMap<>();
    private static final Map<String, Set<RoleCodeEnum>> PARENT_ROLE_CODE_ENUM_SET_MAP = new HashMap<>();

    private static final Set<String> DEPLOY_INIT_ROLE_CODE = new HashSet<>(Arrays.asList(
        SUPER_ADMIN.getRoleCode(),
        SECURITY_ADMIN.getRoleCode(),
        AUDIT_ADMIN.getRoleCode()
    ));

    private static final String sysInfoShortName = BaseConfigWrapper.getSysInfoConfig().getShortName();

    static {
        for (RoleCodeEnum roleCodeEnum : RoleCodeEnum.values()) {
            // 过滤不属于当前系统的角色
            if (!roleCodeEnum.sysNameList.isEmpty() &&
                    !roleCodeEnum.sysNameList.contains(sysInfoShortName)) {
                // 当前系统没有这个角色，跳过这个角色
                continue;
            }
            // 添加角色
            ROLE_CODE_ENUM_MAP.put(roleCodeEnum.getRoleCode(), roleCodeEnum);
            if (roleCodeEnum.getParentRoleCode() != null) {
                PARENT_ROLE_CODE_ENUM_SET_MAP.computeIfAbsent(roleCodeEnum.getParentRoleCode(), k -> new HashSet<>())
                        .add(roleCodeEnum);
            }
        }
        DEPLOY_INIT_ROLE_CODE.add(SUPER_ADMIN.getRoleCode());
        DEPLOY_INIT_ROLE_CODE.add(SECURITY_ADMIN.getRoleCode());
        DEPLOY_INIT_ROLE_CODE.add(AUDIT_ADMIN.getRoleCode());
    }

    private final String desc;
    private final String roleCode;
    private final String parentRoleCode;
    private final List<String> sysNameList;

    public List<String> getSysNameList() {
        return sysNameList;
    }

    RoleCodeEnum(String desc, String roleCode, String parentRoleCode) {
        this.desc = desc;
        this.roleCode = roleCode;
        this.parentRoleCode = parentRoleCode;
        // 为空表示所有系统都会用到当前角色，不需要单独设置
        sysNameList = new ArrayList<>();
    }

    RoleCodeEnum(String desc, String roleCode, String parentRoleCode, String... sysName) {
        this.desc = desc;
        this.roleCode = roleCode;
        this.parentRoleCode = parentRoleCode;
        // 指定了当前角色都有哪些系统用到。因为对于某些角色，可能会在指定系统中存在，比如 “司法恢复”
        this.sysNameList = Arrays.stream(sysName).collect(Collectors.toList());
    }
    public static Set<String> getDeployedRoleCodeEnum() {
        return DEPLOY_INIT_ROLE_CODE;
    }
    public static RoleCodeEnum getRoleCodeEnumByRoleCode(String roleCode) {
        return ROLE_CODE_ENUM_MAP.get(roleCode);
    }

    public String getDesc() {
        return tr();
    }

    public String getRoleCode() {
        return roleCode;
    }

    public String getParentRoleCode() {
        return parentRoleCode;
    }

    public static RoleCodeEnum getByRoleName(String roleName) {
        return Arrays.stream(RoleCodeEnum.values())
                .filter(role -> role.getDesc().equals(roleName))
                .findFirst()
                .orElse(null);
    }
    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }

}
