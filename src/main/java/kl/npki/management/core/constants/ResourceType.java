package kl.npki.management.core.constants;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.management.core.constants.I18nConstant.BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * <AUTHOR>
 *     Created on 2022/06/27 17:10
 */

public enum ResourceType implements EnumI18n {
    /**
     * 页面资源
     */
    PAGE(1),
    /**
     * 菜单资源
     */
    MENU(2),
    /**
     * 按钮资源
     */
    BUTTON(3),
    /**
     * 接口资源
     */
    API(4),
    ;

    ResourceType(Integer type) {
        this.type = type;
    }

    private final Integer type;

    public Integer getType() {
        return type;
    }

    public String getDescription() {
        return tr();
    }

    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
