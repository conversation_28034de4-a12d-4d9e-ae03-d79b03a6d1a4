package kl.npki.management.core.service.auth.impl;

import kl.nbase.auth.core.AuthnType;
import kl.nbase.auth.core.IAuthenticateService;
import kl.nbase.auth.entity.AuthenticationResult;
import kl.nbase.auth.entity.AuthnRequest;
import kl.nbase.auth.token.AuthenticationToken;
import kl.nbase.auth.utils.TokenUtils;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.biz.login.model.LoginTypeInfo;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.*;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.management.core.biz.admin.model.LoginResponse;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import kl.npki.management.core.biz.admin.model.info.AdminInfoInfo;
import kl.npki.management.core.biz.admin.model.info.CaptchaPayloadInfo;
import kl.npki.management.core.biz.admin.model.info.CertLoginInfo;
import kl.npki.management.core.biz.admin.model.info.LoginInfo;
import kl.npki.management.core.common.cache.AuthCache;
import kl.npki.management.core.constants.RoleCodeEnum;
import kl.npki.management.core.exception.ManagementInternalError;
import kl.npki.management.core.exception.ManagementValidationError;
import kl.npki.management.core.repository.IAdminCertRepository;
import kl.npki.management.core.repository.IAdminInfoMgrRepository;
import kl.npki.management.core.repository.IRoleResourceLinkMgrRepository;
import kl.npki.management.core.service.auth.IAuthnService;
import kl.npki.management.core.service.config.LoginTypeConfigService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.security.SecureRandom;
import java.util.List;
import java.util.*;

import static kl.npki.management.core.constants.I18nExceptionInfoConstant.*;

/**
 * <AUTHOR>
 * @date 2022/10/12 9:58
 * @desc
 */
@Service
public class AuthnServiceImpl implements IAuthnService {

    private static final Logger logger = LoggerFactory.getLogger(AuthnServiceImpl.class);

    /**
     * SSL 登录，cookie里面对应的证书序列号的名称
     */
    private static final String SSL_LOGIN_CERT_SN_NAME_IN_COOKIE = "KOAL_CERT_SERIAL_NUMBER_HEX";

    /**
     * 多选模式下，除三员的角色外，其余角色登录时所需的管理员数量
     */
    private static final Integer SINGLE_LOGIN_ADMIN_NUM = 1;

    @Resource
    private IRoleResourceLinkMgrRepository resourceLinkMgrRepository;

    @Resource
    private LoginTypeConfigService loginTypeConfigService;

    @Resource
    private IAuthenticateService authenticateService;

    @Resource
    private IAdminCertRepository adminCertRepository;

    @Resource
    private IAdminInfoMgrRepository adminInfoRepository;


    /**
     * ssl客户端登录
     *
     * @param authnRequest
     * @param request
     */
    private AuthnRequest buildAuthnRequestForSSL(AuthnRequest authnRequest, HttpServletRequest request) {

        String certSn = getCertSnFromRequest(request, false);
        if (ObjectUtils.isNotEmpty(certSn)) {
            // 通道里面包含证书序列号，则进行验证
            AuthnRequest.Ssl ssl = new AuthnRequest.Ssl();
            ssl.setCertSn(certSn);
            authnRequest.setSsl(ssl);
            authnRequest.getAuthnTypes().add(AuthnType.SSL.getCode());
        }

        return authnRequest;
    }

    /**
     * 构造证书签名登录请求
     *
     * @param authnRequest 认证请求
     * @param loginInfo    登录信息
     * @return 认证请求
     */
    private AuthnRequest buildAuthnRequestForCert(AuthnRequest authnRequest, LoginInfo loginInfo) {
        authnRequest.getAuthnTypes().add(AuthnType.CERT_SIGN.getCode());
        AuthnRequest.Cert cert = new AuthnRequest.Cert();
        CertLoginInfo mainAdminInfo = loginInfo.getMainAdmin();
        cert.setCertSn(mainAdminInfo.getCertSn()
            .trim()
            .toUpperCase());
        cert.setHashAlgo(mainAdminInfo.getHashAlgo());
        cert.setSignData(mainAdminInfo.getSignData());
        authnRequest.setCert(cert);
        authnRequest.setExtending(loginInfo.getAuthRefId());
        return authnRequest;
    }

    /**
     * 构造密码登录请求
     *
     * @param authnRequest
     * @param loginInfo
     * @return
     */
    private AuthnRequest buildAuthnRequestForPwd(AuthnRequest authnRequest, LoginInfo loginInfo) {
        authnRequest.getAuthnTypes().add(AuthnType.PWD.getCode());
        AuthnRequest.Pwd pwd = new AuthnRequest.Pwd();
        pwd.setUsername(loginInfo.getUsername());
        pwd.setPassword(loginInfo.getPassword());
        authnRequest.setPwd(pwd);
        authnRequest.setExtending(loginInfo.getAuthRefId());
        return authnRequest;
    }

    /**
     * 构造sso登录请求
     *
     * @param authnRequest 认证请求
     * @param loginInfo    登录信息
     */
    private void buildAuthnRequestForSso(AuthnRequest authnRequest, LoginInfo loginInfo, HttpServletRequest request) {
        authnRequest.getAuthnTypes().add(loginInfo.getAuthnType());

        // 将请求头中的值放入extending中，用于后续SSO认证，例如KCSP的work_no和role_code
        Map<String, String> extending = new HashMap<>();
        authnRequest.setExtending(extending);
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String key = headerNames.nextElement();
            String value = request.getHeader(key);
            extending.put(key, value);
        }

        // 将请求的值都放入extending中，用于后续SSO认证，例如ICBC的SSIAuth和SSISign
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String key = parameterNames.nextElement();
            String value = request.getParameter(key);
            extending.put(key, value);
        }
    }

    private void buildAuthnRequestFromAuthnType(LoginType loginType, AuthnType authnType, AuthnRequest authnRequest,
                                                LoginInfo loginInfo, HttpServletRequest request) {
        switch (authnType) {
            case SSO:
                buildAuthnRequestForSso(authnRequest, loginInfo, request);
                break;
            case PWD:
                buildAuthnRequestForPwd(authnRequest, loginInfo);
                break;
            case CERT_SIGN:
                buildAuthnRequestForCert(authnRequest, loginInfo);
                break;
            case MULTI_CERT_SIGN:
                if (LoginType.MULTI_SIGN_2_OF_3.equals(loginType)) {
                    adminLoginByMultiCert(authnRequest, loginInfo, MultiSignLoginCertCountEnum.CHOICE_2_OF_3.getLoginNum());
                    break;
                } else if (LoginType.MULTI_SIGN_3_OF_5.equals(loginType)) {
                    adminLoginByMultiCert(authnRequest, loginInfo, MultiSignLoginCertCountEnum.CHOICE_3_OF_5.getLoginNum());
                    break;
                }
            default:
                throw ManagementValidationError.INVALID_AUTH_TYPE.toException(INVALID_AUTHENTICATION_TYPE_I18N_KEY, authnType.getCode());
        }
    }

    /**
     * 登录
     *
     * @param loginInfo
     * @return
     */
    @Override
    public LoginResponse adminLogin(LoginInfo loginInfo, HttpServletRequest request) {
        // 获取认证类型
        String authnTypeCode = loginInfo.getAuthnType();
        LoginType loginType = LoginType.getLoginTypeByType(authnTypeCode);
        List<String> authnTypes = new ArrayList<>(4);
        AuthnRequest authnRequest = new AuthnRequest(authnTypes);
        // SSL登录
        buildAuthnRequestForSSL(authnRequest, request);
        // 根据登录方式获取到认证类型， 根据对应的认证类型构造认证请求
        loginType.getAuthnTypes().forEach(authnType ->
                buildAuthnRequestFromAuthnType(loginType, authnType, authnRequest, loginInfo, request));
        LoginResponse loginResponse = buildLoginResponse(authenticateService.authenticate(authnRequest));
        // 部署完成后才需要进行操作签名
        boolean deployed = SystemUtil.isDeployed();
        if (deployed) {
            // 当前的登录方式是否要进行客户端签名
            boolean clientSign = loginType.needCertificate();
            // 配置中设置是否需要进行操作签名
            SysInfoConfig sysInfoConfig = BaseConfigWrapper.getSysInfoConfig();
            boolean configClientSign = sysInfoConfig.isClientSign();
            // 当前登录方式需要签名且配置中配置了需要客户端签名，则客户端必须要进行操作签名
            loginResponse.setClientSign(clientSign && configClientSign);
        } else {
            // 未部署完成，不进行客户端签名
            loginResponse.setClientSign(false);
        }
        return loginResponse;
    }

    /**
     * 多选管理员登录
     *
     * @param loginInfo        登录信息
     * @param needAdminCertNum 需要登录的管理员数量
     * @return
     */
    private AuthnRequest adminLoginByMultiCert(AuthnRequest authnRequest, LoginInfo loginInfo, int needAdminCertNum) {
        // 1. 获取主登录者角色
        CertLoginInfo mainAdmin = loginInfo.getMainAdmin();

        if (isNeedMultiAdminToLogin(mainAdmin.getCertSn())) {
            int otherAdminSize = loginInfo.getOtherAdmins().size();
            // 验证管理员数量是否符合要求
            if (needAdminCertNum != (otherAdminSize + 1)) {
                throw ManagementValidationError.MISSING_OTHER_ADMIN_LOGIN_INFO.toException();
            }

            // 构建AuthnRequest
            buildAuthnRequestForMultiLogin(authnRequest, loginInfo, needAdminCertNum);
        } else {
            buildAuthnRequestForCert(authnRequest, loginInfo);
        }
        return authnRequest;
    }

    private AuthnRequest buildAuthnRequestForMultiLogin(AuthnRequest authnRequest, LoginInfo loginInfo, int needAdminCertNum) {
        // 用来判断管理员是否重复
        HashSet<String> snSet = new HashSet<>();

        // 构造主登录者信息
        CertLoginInfo mainAdmin = loginInfo.getMainAdmin();
        AuthnRequest.Cert mainCert = new AuthnRequest.Cert();
        mainCert.setCertSn(mainAdmin.getCertSn());
        mainCert.setHashAlgo(mainAdmin.getHashAlgo());
        mainCert.setSignData(mainAdmin.getSignData());
        snSet.add(mainAdmin.getCertSn());
        // 构造其它登录者信息
        List<AuthnRequest.Cert> otherCerts = new ArrayList<>();
        for (CertLoginInfo otherAdminInfo : loginInfo.getOtherAdmins()) {
            AuthnRequest.Cert otherCert = new AuthnRequest.Cert();
            otherCert.setCertSn(otherAdminInfo.getCertSn());
            otherCert.setHashAlgo(otherAdminInfo.getHashAlgo());
            otherCert.setSignData(otherAdminInfo.getSignData());
            otherCerts.add(otherCert);
            snSet.add(otherAdminInfo.getCertSn());
        }

        if (snSet.size() != needAdminCertNum) {
            throw ManagementValidationError.ADMIN_CANNOT_REPEAT.toException();
        }

        // 构造认证请求类
        AuthnRequest.MultiCert multiCert = new AuthnRequest.MultiCert();
        multiCert.setMainCert(mainCert);
        multiCert.setOtherCerts(otherCerts);

        // 认证请求
        authnRequest.getAuthnTypes().add(AuthnType.MULTI_CERT_SIGN.getCode());
        authnRequest.setMultiCert(multiCert);
        authnRequest.setExtending(loginInfo.getAuthRefId());

        return authnRequest;
    }

    /**
     * 验证ssl客户端登录
     *
     * @param request
     * @return
     */
    @Override
    public String verifySslClient(HttpServletRequest request) {
        String certSn = getCertSnFromRequest(request, true);
        AdminCertEntity adminCert = adminCertRepository.getByCertSn(certSn);
        // 验证sn是否有效
        CheckUtils.notNull(adminCert, ManagementValidationError.INVALID_CERT_SN.toException(SSL_CERTIFICATE_DOES_NOT_EXIST_I18N_KEY, certSn));
        // 检查当前证书状态是否允许登录
        CheckUtils.isTrue(CertStatus.allowToLogin(adminCert.getStatus()),
                BaseValidationError.USER_CERT_STATUS_ERROR.toException());
        // 检查当前用户状态是否允许登录
        AdminEntity adminEntity = adminInfoRepository.getById(adminCert.getAdminInfoId());
        CheckUtils.isTrue(UserStatus.allowToLogin(adminEntity.getStatus()), BaseValidationError.USER_STATUS_ERROR.toException());
        return certSn;
    }

    @Override
    public Integer getLoginAdminNum(String certSn) {
        LoginTypeInfo loginTypeInfo = loginTypeConfigService.getLoginType();
        LoginType loginType = LoginType.getLoginTypeByType(loginTypeInfo.getType());

        switch (loginType) {
            case MULTI_SIGN_3_OF_5:
                return isNeedMultiAdminToLogin(certSn) ? MultiSignLoginCertCountEnum.CHOICE_3_OF_5.getLoginNum() :
                        SINGLE_LOGIN_ADMIN_NUM;
            case MULTI_SIGN_2_OF_3:
                return isNeedMultiAdminToLogin(certSn) ? MultiSignLoginCertCountEnum.CHOICE_2_OF_3.getLoginNum() :
                        SINGLE_LOGIN_ADMIN_NUM;
            default:
                return SINGLE_LOGIN_ADMIN_NUM;
        }
    }

    @Override
    public CaptchaPayloadInfo getLoginCaptcha() {
        try {
            // 生成随机验证码文本
            String captchaText = generateCaptchaText();

            // 存储验证码到Session（后续登录时校验）
            String authRefId = TokenUtils.generateToken();
            AuthCache.INSTANCE.set(authRefId, captchaText, LoginConstant.DEFAULT_SESSION_ID_TTL);

            // 创建图片
            int width = 120;
            int height = 40;
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = image.createGraphics();

            // 设置背景和字体
            g.setColor(Color.WHITE);
            g.fillRect(0, 0, width, height);
            g.setFont(new Font("DejaVu Sans", Font.BOLD, 24));

            // 绘制验证码文本
            for (int i = 0; i < captchaText.length(); i++) {
                g.setColor(new Color(new SecureRandom().nextInt(255), new SecureRandom().nextInt(255), new SecureRandom().nextInt(255)));
                g.drawString(String.valueOf(captchaText.charAt(i)), 20 + i * 20, 25);
            }

            // 转换为Base64
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "png", baos);
            String base64 = Base64.getEncoder().encodeToString(baos.toByteArray());
            String captcha = "data:image/png;base64," + base64;
            return new CaptchaPayloadInfo()
                    .setAuthRefId(authRefId)
                    .setCaptcha(captcha);

        } catch (Exception e) {
            throw ManagementInternalError.CAPTCHA_GEN_ERROR.toException(e);
        }
    }

    @Override
    public void verifyCaptcha(String authRefId, String captcha) {
        // 判断验证码是否为空
        if (StringUtils.isBlank(captcha)) {
            throw ManagementValidationError.CAPTCHA_VERIFY_FAIL.toException();
        }

        if (StringUtils.isBlank(authRefId)) {
            throw ManagementValidationError.AUTH_REF_ID_ERROR.toException();
        }

        // 判断传入的验证码与存储的验证码是否一致
        String storedCaptcha = AuthCache.INSTANCE.get(authRefId);
        // 验证后移除验证码，防止重复使用
        AuthCache.INSTANCE.remove(authRefId);
        if (!captcha.equalsIgnoreCase(storedCaptcha)) {
            throw ManagementValidationError.CAPTCHA_VERIFY_FAIL.toException();
        }
    }

    /**
     * 生成随机验证码
     *
     * @return 随机4位字母或数字验证码
     */
    private String generateCaptchaText() {
        String chars = "ABCDEFGHIJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxy3456789";
        StringBuilder sb = new StringBuilder();
        SecureRandom random = new SecureRandom();
        for (int i = 0; i < 4; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }


    /**
     * 构建登录响应
     *
     * @param result 认证结果
     * @return 登录响应
     */
    private LoginResponse buildLoginResponse(AuthenticationResult result) {
        AuthenticationToken token = result.getAuthenticationToken();
        // 根据role获取资源
        Set<String> roles = token
                .getPrincipal()
                .getRoles();
        // 管理员id
        String userId = token
                .getPrincipal()
                .getId();
        // 管理员姓名
        String username = token
                .getPrincipal()
                .getAttributes()
                .get(LoginConstant.USERNAME)
                .toString();
        //        构造响应
        LoginResponse loginResponse = new LoginResponse();
        loginResponse.setToken(token.getToken());
        List<String> resource = resourceLinkMgrRepository.queryResourceByRoles(roles.iterator().next());
        loginResponse.setResources(resource);
        loginResponse.setAdminInfo(new AdminInfoInfo(userId, username, roles));
        return loginResponse;
    }

    /**
     * 从cookie中获取对应的证书序列号
     *
     * @param request
     * @return
     */
    private String getCertSnFromRequest(HttpServletRequest request, boolean throwExt) {
        Cookie[] cookies = request.getCookies();
        if (cookies == null || cookies.length == 0) {
            if (throwExt) {
                throw ManagementValidationError.INVALID_COOKIE_INFO.toException(COOKIE_IS_EMPTY_SSL_CHANNEL_VERIFICATION_FAILED_I18N_KEY);
            }
            return null;
        }
        Cookie cookie = null;
        for (Cookie cookieValue : cookies) {
            if (cookieValue.getName().equalsIgnoreCase(SSL_LOGIN_CERT_SN_NAME_IN_COOKIE)) {
                cookie = cookieValue;
                break;
            }
        }
        if (cookie == null || ObjectUtils.isEmpty(cookie.getValue())) {
            if (throwExt) {
                // cookie为空或者cookie中没有所包含的信息，则抛出对应的异常信息
                throw ManagementValidationError.INVALID_COOKIE_INFO.toException(SSL_AUTHENTICATION_LOGIN_THE_COOKIE_INFORMATION_NEEDS_TO_INCLUDE_THE_HEXADECIMAL_CERTIFICATE_SERIAL_NUMBER_I18N_KEY);
            }
            return null;

        }
        return cookie.getValue();
    }

    /**
     * 多选登录模式下，判断是否需要多个管理员登录
     *
     * @param certSn
     * @return
     */
    private boolean isNeedMultiAdminToLogin(String certSn) {
        AdminCertEntity mainAdminCertEntity = adminCertRepository.getByCertSn(certSn);
        AdminEntity mainAdminEntity = adminInfoRepository.getById(mainAdminCertEntity.getAdminInfoId());
        String roleCode = mainAdminEntity.getRoleCode();

        return roleCode.equals(RoleCodeEnum.SUPER_ADMIN.getRoleCode()) ||
                roleCode.equals(RoleCodeEnum.SECURITY_ADMIN.getRoleCode()) ||
                roleCode.equals(RoleCodeEnum.AUDIT_ADMIN.getRoleCode());
    }

}
