package kl.npki.management.core.service.log;


import kl.npki.management.core.biz.log.model.AuditLogInfo;

/**
 * 审计service
 * <AUTHOR>
 * @date 2023/3/21
 */
public interface IAuditLogService {

    /**
     * 日志审计
     * @param logId
     * @return
     */
    boolean auditLog(Long logId);

    /**
     * 获取日志审计详情
     * @param logId
     * @return
     */
    AuditLogInfo getAuditLogResponse(Long logId);

}
