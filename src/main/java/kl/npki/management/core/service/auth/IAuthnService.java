package kl.npki.management.core.service.auth;

import kl.npki.management.core.biz.admin.model.LoginResponse;
import kl.npki.management.core.biz.admin.model.info.CaptchaPayloadInfo;
import kl.npki.management.core.biz.admin.model.info.LoginInfo;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2022/10/12 9:54
 * @desc
 */
public interface IAuthnService {

    /**
     * 登录
     * @param loginInfo
     * @return
     */
    LoginResponse adminLogin(LoginInfo loginInfo, HttpServletRequest request);
    /**
     * 验证ssl客户端登录
     * @param request
     * @return
     */
    String verifySslClient(HttpServletRequest request);

    /**
     * 多选登录模式下，需要登录管理员的数量
     * @param certSn
     * @return
     */
    Integer getLoginAdminNum(String certSn);

    /**
     * 获取登录界面的图形验证码
     * @return 验证码响应
     */
    CaptchaPayloadInfo getLoginCaptcha();

    /**
     * 验证传入的验证码是否正确
     * @param authRefId
     * @param captcha
     */
    void verifyCaptcha(String authRefId, String captcha);
}
