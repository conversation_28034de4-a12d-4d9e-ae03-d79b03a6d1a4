package kl.npki.management.core.service.kcsp;

import kl.npki.base.core.biz.kcsp.model.KcspRestResponse;
import kl.npki.management.core.biz.kcsp.model.KcspServiceSyncRequest;

/**
 * KCSP 服务同步业务接口
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
public interface KcspSvcSyncService {

    /**
     * 激活开通NPKI服务
     *
     * @param data 服务数据
     * @return {@code KcspApiResponse}
     */
    KcspRestResponse activateService(KcspServiceSyncRequest.ServiceData data);

    /**
     * 更新NPKI服务
     *
     * @param data 服务数据
     * @return {@code KcspApiResponse}
     */
    KcspRestResponse updateService(KcspServiceSyncRequest.ServiceData data);

    /**
     * 删除服务
     *
     * @param serviceId 服务ID
     * @return {@code KcspApiResponse}
     */
    KcspRestResponse disableService(String serviceId);
}
