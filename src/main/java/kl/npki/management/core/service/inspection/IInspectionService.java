package kl.npki.management.core.service.inspection;

import kl.npki.base.core.biz.inspection.InspectionReportTypeEnum;
import kl.npki.base.core.biz.inspection.model.InspectionReportInfo;
import kl.npki.management.core.biz.inspection.model.InspectionGroupedItems;
import kl.npki.management.core.biz.inspection.model.InspectionGroupedResults;

import java.util.List;

/**
 * 系统巡检服务接口
 *
 * <AUTHOR>
 * @date 08/05/2025 11:16
 **/
public interface IInspectionService {

    /**
     * 获取所有巡检项
     *
     * @return 巡检项列表
     */
    List<InspectionGroupedItems> getInspectionItems();

    /**
     * 执行巡检
     *
     * @param name 本次巡检名称
     * @return 巡检记录ID
     */
    Long executeInspection(String name);

    /**
     * 获取巡检结果
     *
     * @param id 巡检记录ID
     * @return 巡检结果
     */
    List<InspectionGroupedResults> getInspectionResults(Long id);

    /**
     * 根据巡检类型对巡检结果列表进行分组
     *
     * @param itemResultsJson 巡检结果JSON字符串
     * @return 分组后的巡检结果
     */
    List<InspectionGroupedResults> groupInspectionResultsByType(String itemResultsJson);

    /**
     * 导出巡检报告
     *
     * @param id         巡检记录ID
     * @param reportType 巡检报告类型，参见{@link InspectionReportTypeEnum#name()}
     * @return 巡检报告
     */
    InspectionReportInfo generateInspectionReport(Long id, String reportType);
}
