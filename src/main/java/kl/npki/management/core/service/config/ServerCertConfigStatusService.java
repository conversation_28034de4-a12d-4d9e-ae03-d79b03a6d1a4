package kl.npki.management.core.service.config;

import kl.npki.base.core.biz.cert.model.TrustCertEntity;
import kl.npki.base.core.biz.config.model.ConfigStatus;
import kl.npki.base.core.biz.config.service.IConfigStatusService;
import kl.npki.base.core.constant.TrustCertType;
import kl.npki.base.core.repository.ITrustCertRepository;
import kl.npki.base.core.repository.RepositoryFactory;
import org.apache.commons.lang3.StringUtils;

/**
 * 服务器证书配置状态
 * 站点证书 以及 身份证书的配置状态
 *
 * <AUTHOR>
 */
public class ServerCertConfigStatusService implements IConfigStatusService {
    private static final String SERVER_CERT_CONFIG_NAME = "serverCert";

    private final ITrustCertRepository trustCertRepository;

    public ServerCertConfigStatusService() {
        this.trustCertRepository = RepositoryFactory.get(ITrustCertRepository.class);
    }

    @Override
    public ConfigStatus queryConfigStatusWhenDbNotReady() {
        ConfigStatus configStatus = new ConfigStatus(SERVER_CERT_CONFIG_NAME);
        configStatus.setStatus(false);
        configStatus.setWarnInfo(DB_NOT_CONFIGURED);
        return configStatus;
    }

    @Override
    public ConfigStatus queryConfigStatus() {
        TrustCertEntity sslCertEntity = trustCertRepository.getCert(TrustCertType.SSL);
        TrustCertEntity idCertEntity = trustCertRepository.getCert(TrustCertType.IDENTITY);
        String b64IdCert = idCertEntity == null ? null : idCertEntity.getCertValue();
        String sslCert = sslCertEntity == null ? null : sslCertEntity.getCertValue();
        boolean status = StringUtils.isNotBlank(b64IdCert) && StringUtils.isNotBlank(sslCert);
        return new ConfigStatus(SERVER_CERT_CONFIG_NAME, status);
    }
}
