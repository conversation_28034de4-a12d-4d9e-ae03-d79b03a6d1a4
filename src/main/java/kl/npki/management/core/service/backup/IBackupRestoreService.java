package kl.npki.management.core.service.backup;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IBackupRestoreService {

    /**
     * 高优先级参考值
     */
    int HIGHEST_PRIORITY = 1;

    /**
     * 中等优先级参考值
     */
    int MEDIUM_PRIORITY = 5;

    /**
     * 最低优先级参考值
     */
    int LOWEST_PRIORITY = 10;

    /**
     * 获得待备份的文件路径
     * @return
     */
    List<String> getToBeBackupFiles();

    /**
     * 进行恢复
     * @param restoreFilePath 恢复文件的路径
     * @return
     */
    boolean restore(String restoreFilePath);

    /**
     * 恢复失败后需要执行的操作
     * @return
     */
    boolean failedAfterRestore();

    /**
     * 恢复成功后需要执行的操作
     */
    void successAfterRestore();

    /**
     * 执行顺序, 值越大，执行顺序越靠后
     * @return
     */
    int getOrder();
}
