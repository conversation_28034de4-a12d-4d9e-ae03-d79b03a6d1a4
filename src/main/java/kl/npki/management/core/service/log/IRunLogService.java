package kl.npki.management.core.service.log;


import kl.npki.management.core.biz.log.model.RunLogInfo;

import java.io.File;
import java.util.List;

/**
 * 处理运行日志的接口
 *
 * <AUTHOR>
 * @date 2022/12/14
 */
public interface IRunLogService {

    /**
     * 获取日志文件列表
     *
     * @return
     */
    List<RunLogInfo> getRunLogList();

    /**
     * 获取日志文件
     * @return
     */
    File getLogFileByName(String logName);;

    /**
     * 查看运行日志
     *
     * @param logName 日志文件的文件名
     * @return
     */
    List<String> viewRunLog(String logName, int logSize);

}



