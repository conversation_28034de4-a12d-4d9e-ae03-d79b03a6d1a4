package kl.npki.management.core.service.config;

import kl.nbase.emengine.entity.EngineAlgoInfo;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.entity.algo.BlockSymAlgo;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.npki.base.core.biz.config.model.ConfigStatus;
import kl.npki.base.core.biz.config.service.IConfigStatusService;
import kl.npki.base.core.biz.mainkey.service.MainKeyManager;
import kl.npki.base.core.configs.ClusterEmConfig;
import kl.npki.base.core.configs.SysSupportedAlgoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.management.core.biz.config.model.AlgoConfigInfo;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 密码算法配置服务
 *
 * <AUTHOR>
 * @date 2024/4/12
 */
public class CryptoAlgoConfigService implements IConfigStatusService {

    private static final String ALGO_CONFIG_NAME = "algo";

    @Override
    public ConfigStatus queryConfigStatus() {
        SysSupportedAlgoConfig sysAlgoConfig = BaseConfigWrapper.getSysAlgoConfig();
        return new ConfigStatus(ALGO_CONFIG_NAME,
            StringUtils.isNotBlank(sysAlgoConfig.getAsymmetric()) &&
                StringUtils.isNotBlank(sysAlgoConfig.getSymmetric()) &&
                StringUtils.isNotBlank(sysAlgoConfig.getHash()) &&
                // 密码算法配置依赖主密钥配置
                isMainKeyConfigured());
    }

    @Override
    public ConfigStatus queryConfigStatusWhenDbNotReady() {
        ConfigStatus configStatus = new ConfigStatus(ALGO_CONFIG_NAME);
        configStatus.setStatus(false);
        configStatus.setWarnInfo(DB_NOT_CONFIGURED);
        return configStatus;
    }

    /**
     * 主密钥是否配置
     *
     * @return 是否配置
     */
    public boolean isMainKeyConfigured() {
        // 主密钥不为空，说明已经初始化主密钥
        return ObjectUtils.isNotEmpty(MainKeyManager.getInstance().getCurrentMainKeyId());
    }

    /**
     * 查询密码机支持的密码算法(经过业务层控制过滤的)
     *
     * @return 密码机支持的密码算法(经过业务层控制过滤的)
     */
    public AlgoConfigInfo queryEngineSupportAlgo() {
        // 集群密码设备支持的算法
        EngineAlgoInfo engineAlgoInfo = ConfigHolder.get().get(ClusterEmConfig.class).queryEmEngineSupportedAlgo();
        Set<String> symAlgAbility = engineAlgoInfo.getSymAlgAbility().stream().map(BlockSymAlgo::getAlgoStr).collect(Collectors.toSet());
        Set<String> asymAlgAbility = engineAlgoInfo.getAsymAlgAbility().stream().map(AsymAlgo::getAlgoName).collect(Collectors.toSet());
        Set<String> hashAlgAbility = engineAlgoInfo.getHashAlgAbility().stream().map(HashAlgo::getAlgoName).collect(Collectors.toSet());
        Set<String> pqcAlgAbility = engineAlgoInfo.getAsymAlgAbility().stream().filter(AsymAlgo::isPQC).map(AsymAlgo::getAlgoName).collect(Collectors.toSet());
        // 辅助密码设备支持的算法
        EngineAlgoInfo backupEngineAlgoInfo = ConfigHolder.get().get(ClusterEmConfig.class).queryBackupEngineSupportedAlgo();
        if (Objects.nonNull(backupEngineAlgoInfo)) {
            // 若启用了辅助密码设备，则将辅助密码设备支持的算法合并到集合中
            symAlgAbility.addAll(backupEngineAlgoInfo.getSymAlgAbility().stream().map(BlockSymAlgo::getAlgoStr).collect(Collectors.toSet()));
            asymAlgAbility.addAll(backupEngineAlgoInfo.getAsymAlgAbility().stream().map(AsymAlgo::getAlgoName).collect(Collectors.toSet()));
            hashAlgAbility.addAll(backupEngineAlgoInfo.getHashAlgAbility().stream().map(HashAlgo::getAlgoName).collect(Collectors.toSet()));
            pqcAlgAbility.addAll(backupEngineAlgoInfo.getAsymAlgAbility().stream().filter(AsymAlgo::isPQC).map(AsymAlgo::getAlgoName).collect(Collectors.toSet()));
        }
        // 业务层面对可用算法的限制
        SysSupportedAlgoConfig sysAlgoConfig = BaseConfigWrapper.getSysAlgoConfig();
        // 补充对称算法的支持
        supplementSymmetricAlgorithmSupport(sysAlgoConfig, symAlgAbility);
        // 过滤出业务层可以使用的算法
        return new AlgoConfigInfo(sysAlgoConfig.filterSupportedSymAlgorithms(new ArrayList<>(symAlgAbility)),
                sysAlgoConfig.filterSupportedAsymAlgorithms(new ArrayList<>(asymAlgAbility)),
                sysAlgoConfig.filterSupportedHashAlgorithms(new ArrayList<>(hashAlgAbility)),
                sysAlgoConfig.filterSupportedPQCAlgorithms(new ArrayList<>(pqcAlgAbility)));
    }

    /**
     * 查询系统已配置支持的密码算法
     *
     * @return 系统已配置支持的密码算法
     */
    public AlgoConfigInfo querySystemSupportAlgo() {
        // 对称密码算法
        List<String> supportSymAlgo = BaseConfigWrapper.getSysAlgoConfig().extractSymmetricList();
        // 对对称密码算法
        List<String> supportAsymAlgo = BaseConfigWrapper.getSysAlgoConfig().extractAsymmetricList();
        // Hash算法
        List<String> supportHashAlgo = BaseConfigWrapper.getSysAlgoConfig().extractHashList();
        // 抗量子密码算法
        List<String> supportPostQuantumAlgo = BaseConfigWrapper.getSysAlgoConfig().extractPostQuantumList();
        return new AlgoConfigInfo(supportSymAlgo, supportAsymAlgo, supportHashAlgo, supportPostQuantumAlgo);
    }

    /**
     * 保存密码算法配置
     *
     * @param request 算法配置信息
     */
    public void save(AlgoConfigInfo request) {
        AlgoConfigInfo engineSupportedAlgo = queryEngineSupportAlgo();
        SysSupportedAlgoConfig sysAlgoConfig = BaseConfigWrapper.getSysAlgoConfig();
        symAlgoSave(request, engineSupportedAlgo, sysAlgoConfig);
        asymAlgoSave(request, engineSupportedAlgo, sysAlgoConfig);
        pqcAlgoSave(request, engineSupportedAlgo, sysAlgoConfig);
        hashAlgoSave(request, engineSupportedAlgo, sysAlgoConfig);
        ConfigHolder.get().save(sysAlgoConfig);
    }

    /**
     * 保存对称算法配置
     *
     * @param request 算法配置请求
     * @param engineSupportedAlgo 密码设备支持的算法（经过业务层过滤的）
     * @param sysAlgoConfig 待更新的配置对象
     */
    private static void symAlgoSave(AlgoConfigInfo request, AlgoConfigInfo engineSupportedAlgo, SysSupportedAlgoConfig sysAlgoConfig) {
        String mainKeyAlgo = null;
        boolean isSupportAlgo = false;
        Set<String> symAlgAbility = new HashSet<>(engineSupportedAlgo.getSymAlgo());
        // 补充对称算法的支持
        supplementSymmetricAlgorithmSupport(sysAlgoConfig, symAlgAbility);
        for (String completeSymAlgo : symAlgAbility) {
            for (String symAlgo : request.getSymAlgo()) {
                if (completeSymAlgo.startsWith(symAlgo)) {
                    mainKeyAlgo = completeSymAlgo;
                    isSupportAlgo = true;
                    break;
                }
            }
        }
        if (!isSupportAlgo) {
            throw BaseValidationError.ALGORITHM_NOT_SUPPORTED.toException(StringUtils.join(request.getSymAlgo(), BaseConstant.ALGORITHMIC_SPLIT_SYMBOL));
        }
        //配置更新刷新缓存
        MainKeyManager.getInstance().initMainKey(mainKeyAlgo);
        // 对称密码算法配置
        sysAlgoConfig.setSymmetric(mainKeyAlgo);
    }

    /**
     * 保存非对称算法配置
     *
     * @param request 算法配置请求
     * @param engineSupportedAlgo 密码设备支持的算法（经过业务层过滤的）
     * @param sysAlgoConfig 待更新的配置对象
     */
    private static void asymAlgoSave(AlgoConfigInfo request, AlgoConfigInfo engineSupportedAlgo, SysSupportedAlgoConfig sysAlgoConfig) {
        // 非对称密码算法配置校验
        for (String asymAlgo : request.getAsymAlgo()) {
            if (!engineSupportedAlgo.getAsymAlgo().contains(asymAlgo)) {
                throw BaseValidationError.ALGORITHM_NOT_SUPPORTED.toException(asymAlgo);
            }
        }
        sysAlgoConfig.setAsymmetric(StringUtils.join(request.getAsymAlgo(), BaseConstant.ALGORITHMIC_SPLIT_SYMBOL));
    }

    /**
     * 保存Hash算法配置
     *
     * @param request 算法配置请求
     * @param engineSupportedAlgo 密码设备支持的算法（经过业务层过滤的）
     * @param sysAlgoConfig 待更新的配置对象
     */
    private static void hashAlgoSave(AlgoConfigInfo request, AlgoConfigInfo engineSupportedAlgo, SysSupportedAlgoConfig sysAlgoConfig) {
        // Hash算法配置校验
        for (String hashAlgo : request.getHashAlgo()) {
            if (!engineSupportedAlgo.getHashAlgo().contains(hashAlgo)) {
                throw BaseValidationError.ALGORITHM_NOT_SUPPORTED.toException(hashAlgo);
            }
        }
        sysAlgoConfig.setHash(StringUtils.join(request.getHashAlgo(), BaseConstant.ALGORITHMIC_SPLIT_SYMBOL));
    }

    /**
     * 保存抗量子算法配置
     *
     * @param request 算法配置请求
     * @param engineSupportedAlgo 密码设备支持的算法（经过业务层过滤的）
     * @param sysAlgoConfig 待更新的配置对象
     */
    private static void pqcAlgoSave(AlgoConfigInfo request, AlgoConfigInfo engineSupportedAlgo, SysSupportedAlgoConfig sysAlgoConfig) {
        // 抗量子算法配置校验
        for (String pqcAlgo : request.getPqcAlgo()) {
            if (!engineSupportedAlgo.getPqcAlgo().contains(pqcAlgo)) {
                throw BaseValidationError.ALGORITHM_NOT_SUPPORTED.toException(pqcAlgo);
            }
        }
        sysAlgoConfig.setPostQuantum(StringUtils.join(request.getPqcAlgo(), BaseConstant.ALGORITHMIC_SPLIT_SYMBOL));
    }

    /**
     * 补充KJCC返回的对称算法支持列表。
     *
     * <p>由于对称算法的特殊性，其算法名称、分组模式和填充模式存在笛卡尔积关系，KJCC不会列举出所有支持的对称算法组合。</p>
     * <p>这个方法通过比较系统配置支持的算法和KJCC返回的支持算法列表，来补充可能被遗漏的算法组合。</p>
     *
     * <p>例如：如果KJCC返回的支持列表中包含"SM4/GCM"，但没有"SM4/GCM/NoPadding"，
     * 本方法会将"SM4/GCM/NoPadding"添加到支持列表中，因为实际上KJCC是支持这个完整组合的。<p>
     *
     * @param sysAlgoConfig 系统支持的算法配置
     * @param symAlgAbility KJCC返回的对称算法支持集合，此方法将向其中添加匹配的算法
     */
    private static void supplementSymmetricAlgorithmSupport(SysSupportedAlgoConfig sysAlgoConfig, Set<String> symAlgAbility) {
        // 获取系统可以支持的对称算法列表
        List<String> sysSymAlgList = sysAlgoConfig.getSupport().extractSymmetricList();
        for (String sysAlg : sysSymAlgList) {
            String[] parts = sysAlg.split(BaseConstant.SYM_ALGORITHM_SEPARATOR);
            if (parts.length < 2) {
                continue;
            }

            // 通过算法名称和块模式进行匹配
            String algoNameAndBlock = parts[0] + BaseConstant.SYM_ALGORITHM_SEPARATOR + parts[1];
            boolean hasMatch = symAlgAbility.stream()
                .anyMatch(engAlg -> engAlg.startsWith(algoNameAndBlock));

            if (hasMatch) {
                symAlgAbility.add(sysAlg);
            }
        }
    }
}
