package kl.npki.management.core.service.cert.impl;

import kl.nbase.cache.lock.ILock;
import kl.nbase.helper.utils.JsonUtils;
import kl.nbase.security.asn1.x509.Certificate;
import kl.npki.base.core.biz.cert.model.*;
import kl.npki.base.core.biz.cert.service.CaCertMgr;
import kl.npki.base.core.biz.cert.service.ManageCertMgr;
import kl.npki.base.core.common.CommonLock;
import kl.npki.base.core.common.manager.MgrHolder;
import kl.npki.base.core.constant.CertTypeEnum;
import kl.npki.base.core.constant.TrustCertType;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.repository.ITrustCertRepository;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.management.core.biz.cert.model.CertDetailInfo;
import kl.npki.management.core.service.cert.IIdCertService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.Objects;


import static kl.npki.management.core.constants.I18nExceptionInfoConstant.ID_CERT_ISSUE_ERROR_I18N_KEY;
import static kl.npki.management.core.constants.I18nExceptionInfoConstant.ID_CERT_UPDATE_ERROR_I18N_KEY;

/**
 * 通用管理页面身份证书管理服务实现类
 *
 * <AUTHOR>
 */
public class IdCertServiceImpl implements IIdCertService {
    private static final String LOCK_ID = "id";

    private final ITrustCertRepository trustCertRepository;

    public IdCertServiceImpl() {
        this.trustCertRepository = RepositoryFactory.get(ITrustCertRepository.class);
    }

    @Override
    public void issueIdCert(CertRequestInfo rootAndIdCertRequestInfo) {
        TrustCertEntity idCertEntity = trustCertRepository.getCert(TrustCertType.IDENTITY);
        // 判断身份证书是否已签发
        if (Objects.nonNull(idCertEntity) && StringUtils.isNotBlank(idCertEntity.getCertValue())) {
            throw BaseInternalError.ID_CERT_ALREADY_EXIST.toException();
        }
        ILock lock = CommonLock.INSTANCE.lock(LOCK_ID);
        if (!lock.tryLock()) {
            throw BaseInternalError.ID_CERT_INIT_GET_LOCK_ERROR.toException();
        }
        try {
            // 签发证书，身份证书只签发单证书
            TrustCertEntity trustCertEntity = doIssue(rootAndIdCertRequestInfo);
            // 设置注册信息
            ServerCertRequestInfo serverCertRequestInfo = rootAndIdCertRequestInfo.toServerCertRequestInfo();
            trustCertEntity.setRegisterInfo(JsonUtils.toJson(serverCertRequestInfo));
            // 保存数据
            trustCertEntity.save();
            // 更新缓存
            MgrHolder.getIdCertMgr().forceReload();
        } catch (Exception e) {
            throw BaseInternalError.CERT_ISSUE_ERROR.toException(ID_CERT_ISSUE_ERROR_I18N_KEY, e);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void issueIdCertByCa(CertRequestInfo rootAndIdCertRequestInfo) {
        TrustCertEntity idCertEntity = trustCertRepository.getCert(TrustCertType.IDENTITY);
        // 判断身份证书是否已签发
        if (Objects.nonNull(idCertEntity) && StringUtils.isNotBlank(idCertEntity.getCertValue())) {
            throw BaseInternalError.ID_CERT_ALREADY_EXIST.toException();
        }
        ILock lock = CommonLock.INSTANCE.lock(LOCK_ID);
        if (!lock.tryLock()) {
            throw BaseInternalError.ID_CERT_INIT_GET_LOCK_ERROR.toException();
        }
        try {
            // 签发证书，身份证书只签发单证书
            TrustCertEntity trustCertEntity = doIssueByCa(rootAndIdCertRequestInfo);
            // 设置注册信息
            ServerCertRequestInfo serverCertRequestInfo = rootAndIdCertRequestInfo.toServerCertRequestInfo();
            trustCertEntity.setRegisterInfo(JsonUtils.toJson(serverCertRequestInfo));
            // 保存数据
            trustCertEntity.save();
            // 更新缓存
            MgrHolder.getIdCertMgr().forceReload();
        } catch (Exception e) {
            throw BaseInternalError.CERT_ISSUE_ERROR.toException(ID_CERT_ISSUE_ERROR_I18N_KEY, e);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void updateIdCert(CertRequestInfo rootAndIdCertRequestInfo) {
        try {
            // 签发证书，身份证书只签发单证书
            TrustCertEntity newTrustCertEntity = doIssue(rootAndIdCertRequestInfo);
            // 删除旧数据
            TrustCertEntity oldTrustCertEntity = trustCertRepository.getCert(TrustCertType.IDENTITY);
            trustCertRepository.delete(oldTrustCertEntity.getId());
            // 设置注册信息
            ServerCertRequestInfo serverCertRequestInfo = rootAndIdCertRequestInfo.toServerCertRequestInfo();
            newTrustCertEntity.setRegisterInfo(JsonUtils.toJson(serverCertRequestInfo));
            // 保存新数据
            newTrustCertEntity.save();
            // 更新缓存
            MgrHolder.getIdCertMgr().forceReload();
            // 内部签发则实时生成CRL
            String hexSn = oldTrustCertEntity.getHexSn();
            MgrHolder.getManageCertMgr().asyncGenerateMgrRootCrl(new CertRevokedInfo(new BigInteger(hexSn, 16)));
        } catch (Exception e) {
            throw BaseInternalError.CERT_ISSUE_ERROR.toException(ID_CERT_UPDATE_ERROR_I18N_KEY, e);
        }
    }

    @Override
    public void updateIdCertByCa(CertRequestInfo rootAndIdCertRequestInfo) {
        try {
            // 签发证书，身份证书只签发单证书
            TrustCertEntity newTrustCertEntity = doIssueByCa(rootAndIdCertRequestInfo);
            // 删除旧数据
            TrustCertEntity oldTrustCertEntity = trustCertRepository.getCert(TrustCertType.IDENTITY);
            trustCertRepository.delete(oldTrustCertEntity.getId());
            // 设置注册信息
            ServerCertRequestInfo serverCertRequestInfo = rootAndIdCertRequestInfo.toServerCertRequestInfo();
            newTrustCertEntity.setRegisterInfo(JsonUtils.toJson(serverCertRequestInfo));
            // 保存新数据
            newTrustCertEntity.save();
            // 更新缓存
            MgrHolder.getIdCertMgr().forceReload();
        } catch (Exception e) {
            throw BaseInternalError.CERT_ISSUE_ERROR.toException(ID_CERT_UPDATE_ERROR_I18N_KEY, e);
        }
    }

    @Override
    public String exportIdCert() {
        TrustCertEntity idCertEntity = trustCertRepository.getCert(TrustCertType.IDENTITY);
        if (Objects.isNull(idCertEntity) || StringUtils.isBlank(idCertEntity.getCertValue())) {
            return null;
        }
        return idCertEntity.getCertValue();
    }

    @Override
    public CertDetailInfo queryIdCert() {
        CertDetailInfo certDetailInfo = new CertDetailInfo();
        TrustCertEntity idCertEntity = trustCertRepository.getCert(TrustCertType.IDENTITY);
        if (Objects.isNull(idCertEntity) || StringUtils.isBlank(idCertEntity.getCertValue())) {
            return certDetailInfo;
        }
        certDetailInfo.toCertDetail(idCertEntity.getCertValue(), idCertEntity.getEncCertValue());
        return certDetailInfo;
    }

    @Override
    public ServerCertRequestInfo getIdCertRequestInfo() {
        TrustCertEntity idCertEntity = trustCertRepository.getCert(TrustCertType.IDENTITY);
        if (Objects.isNull(idCertEntity) || StringUtils.isEmpty(idCertEntity.getRegisterInfo())) {
            return new ServerCertRequestInfo();
        }
        return JsonUtils.from(idCertEntity.getRegisterInfo(), ServerCertRequestInfo.class);
    }

    private TrustCertEntity doIssue(CertRequestInfo rootAndIdCertRequestInfo) {
        // 使用管理根签发证书
        ManageCertMgr manageCertMgr = MgrHolder.getManageCertMgr();
        if (ObjectUtils.isEmpty(manageCertMgr.getManageCertEntity())) {
            throw BaseInternalError.ROOT_NOT_EXIST.toException();
        }
        // 签发签名证书
        Certificate signCert = manageCertMgr.signCert(rootAndIdCertRequestInfo, CertTypeEnum.USER_SIGN_CERT);
        return new TrustCertEntity(signCert, TrustCertType.IDENTITY);
    }

    private TrustCertEntity doIssueByCa(CertRequestInfo rootAndIdCertRequestInfo) {
        // 使用根签发证书
        CaCertMgr caCertMgr = MgrHolder.getCaCertMgr();
        // 签发签名证书
        IssueCertInfo issueCertInfo = caCertMgr.signAuthCert(rootAndIdCertRequestInfo);
        return new TrustCertEntity(issueCertInfo.getSignCertValue(), TrustCertType.IDENTITY);
    }
}
