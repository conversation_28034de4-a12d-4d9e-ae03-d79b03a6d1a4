package kl.npki.management.core.service.config;

import kl.npki.base.core.biz.config.model.ConfigStatus;
import kl.npki.base.core.biz.config.service.IConfigStatusService;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.management.core.constants.RoleCodeEnum;
import kl.npki.management.core.repository.IAdminInfoRepository;

import static kl.npki.management.core.constants.I18nConstant.getBaseMgmtCoreI18nMessage;

/**
 * 查询审计员的初始化情况，完成证书签发才认为初始化完成
 *
 * <AUTHOR> Guiyu
 * @Date 2023/10/17
 */
public class AuditOperInitStatusService implements IConfigStatusService {

    private static final String AUDIT_OPER_CONFIG_STATUS = "auditOper";

    private static final String BIZ_AUDIT_WARN_INFO_I18N_KEY = "please_initialize_the_auditor";

    @Override
    public ConfigStatus queryConfigStatus() {
        IAdminInfoRepository adminInfoRepository = RepositoryFactory.get(IAdminInfoRepository.class);
        long count = adminInfoRepository.countAdminByRoleCode(RoleCodeEnum.AUDIT_OPER.getRoleCode());
        if (count > 0) {
            return new ConfigStatus(AUDIT_OPER_CONFIG_STATUS, true);
        } else {
            return new ConfigStatus(AUDIT_OPER_CONFIG_STATUS, false, getBaseMgmtCoreI18nMessage(BIZ_AUDIT_WARN_INFO_I18N_KEY));
        }
    }

    @Override
    public ConfigStatus queryConfigStatusWhenDbNotReady() {
        ConfigStatus configStatus = new ConfigStatus(AUDIT_OPER_CONFIG_STATUS);
        configStatus.setStatus(false);
        configStatus.setWarnInfo(DB_NOT_CONFIGURED);
        return configStatus;
    }

    @Override
    public boolean isRequiredConfig() {
        return false;
    }
}
