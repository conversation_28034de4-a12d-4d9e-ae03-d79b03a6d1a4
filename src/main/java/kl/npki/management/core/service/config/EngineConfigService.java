package kl.npki.management.core.service.config;

import kl.nbase.emengine.conf.ClusterEngineConfig;
import kl.nbase.emengine.conf.EngineConfig;
import kl.nbase.emengine.conf.GroupEngineConfig;
import kl.nbase.emengine.conf.KeyIndex;
import kl.nbase.emengine.constant.EngineTypeEnum;
import kl.nbase.emengine.entity.EngineAlgoInfo;
import kl.nbase.emengine.entity.EngineInfo;
import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.security.constants.AlgoPurposeEnum;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.biz.config.model.ConfigStatus;
import kl.npki.base.core.biz.config.service.IConfigStatusService;
import kl.npki.base.core.configs.ClusterEmConfig;
import kl.npki.base.core.configs.EmConfig;
import kl.npki.base.core.configs.GroupEmConfig;
import kl.npki.base.core.configs.SysSupportedAlgoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.EmTypeEnum;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.utils.EngineUtil;
import kl.npki.base.core.utils.NetworkValidationUtils;
import kl.npki.management.core.biz.config.model.EngineAsymAlgoResponse;
import kl.npki.management.core.biz.config.model.EngineTypeInfo;
import kl.npki.management.core.exception.ManagementInternalError;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

import static kl.npki.management.core.constants.I18nExceptionInfoConstant.GET_ENGINE_INFO_IS_EMPTY_I18N_KEY;

/**
 * 密码机配置服务
 *
 * <AUTHOR>
 */
public class EngineConfigService implements IConfigStatusService {

    private static final Logger log = LoggerFactory.getLogger(EngineConfigService.class);

    private static final String ENGINE_CONFIG_NAME = "engine";

    /**
     * 连接超时时间，单位毫秒
     */
    private static final int CONNECT_TIMEOUT_MILLS = 3000;

    /**
     * 查询加密机配置
     *
     * @return
     */
    public Optional<ClusterEmConfig> queryEngineConfig() {
        ClusterEmConfig clusterEmConfig = ConfigHolder.get().get(ClusterEmConfig.class);
        if (ObjectUtils.isEmpty(clusterEmConfig)) {
            log.warn("Password machine configuration not configured!");
            return Optional.empty();
        }

        return Optional.of(clusterEmConfig);
    }

    /**
     * 保存或新增加密机配置
     *
     * @param clusterEmConfig
     * @return
     */
    public void saveEngineConfig(ClusterEmConfig clusterEmConfig) {
        // 验证配置
        validEngineConfig(clusterEmConfig);
        // 保存配置
        ConfigHolder.get().save(clusterEmConfig, false);
    }

    public void validEngineConfig(ClusterEmConfig clusterEmConfig) {
        validEngineConfigReachable(clusterEmConfig);
        ClusterEngine clusterEngine = null;
        try {
            ClusterEngineConfig clusterEngineConfig = clusterEmConfig.toClusterEngineConfig();
            // 文件加密机不需要进行测试
            GroupEngineConfig groupEngine = clusterEngineConfig.getGroupEngine();
            List<EngineConfig> engineConfigList = groupEngine.getEngineConfigList();
            // 存在文件加密机则不对文件加密机进行测试
            engineConfigList.removeIf(engineConfig -> engineConfig.getEngineType().equals(EngineTypeEnum.FILE_ENGINE.getDesc()));
            if (CollectionUtils.isEmpty(engineConfigList)){
                return;
            }
            // 辅助密码机一定是文件加密机所以忽略，不进行测试
            groupEngine.setBackupEngineConfig(null);
            clusterEngine = new ClusterEngine(clusterEngineConfig);
            log.info("Key pair generation completed");
            Map<String, EngineInfo> allEngineInfo = clusterEngine.getAllEngineInfo();
            if (MapUtils.isEmpty(allEngineInfo)) {
                throw ManagementInternalError.ENGINE_CONFIG_VALID_FAIL.toException(GET_ENGINE_INFO_IS_EMPTY_I18N_KEY);
            }
        } catch (Exception e) {
            log.error("Failed to validate the configuration", e);
            throw ManagementInternalError.ENGINE_CONFIG_VALID_FAIL.toException();
        } finally {
            if (null != clusterEngine) {
                clusterEngine.close();
            }
        }
    }

    private void validEngineConfigReachable(ClusterEmConfig clusterEmConfig) {
        if (Objects.isNull(clusterEmConfig)) {
            return;
        }
        GroupEmConfig groupEngine = clusterEmConfig.getGroupEngine();
        if (Objects.isNull(groupEngine)) {
            return;
        }
        List<EmConfig> engineList = groupEngine.getEngines();
        if (CollectionUtils.isEmpty(engineList)) {
            return;
        }
        engineList.forEach(emConfig -> {
            String ip = emConfig.getIp();
            if (StringUtils.isBlank(ip)) {
                return;
            }
            Integer port = emConfig.getPort();
            if (Objects.isNull(port)) {
                return;
            }
            boolean serverReachable = NetworkValidationUtils.ipDetection(ip, port, CONNECT_TIMEOUT_MILLS);
            if (!serverReachable) {
                throw ManagementInternalError.ENGINE_CONFIG_IP_VALID_FAIL.toException();
            }
        });
    }

    /**
     * 查询支持的加密机类型列表
     *
     * @return 加密机类型名称列表
     */
    public List<EngineTypeInfo> queryEngineType() {
        List<EngineTypeInfo> engineTypeInfos = new ArrayList<>();
        EmTypeEnum[] allEngine = EmTypeEnum.values();

        Arrays.stream(allEngine).forEach(emTypeEnum ->
            engineTypeInfos.add(new EngineTypeInfo(emTypeEnum.getEngineType(), emTypeEnum.getEngineAlias())));

        return engineTypeInfos;
    }


    /**
     * 查询当前配置的加密机所支持的非对称签名算法
     *
     * @return
     */
    public List<EngineAsymAlgoResponse> querySignKeyType() {
        ClusterEmConfig clusterEmConfig = ConfigHolder.get().get(ClusterEmConfig.class);
        List<EmConfig> engines = clusterEmConfig.getGroupEngine().getEngines();
        if (CollectionUtils.isEmpty(engines)) {
            return Collections.emptyList();
        }
        // 默认取第一个加密机配置
        Map<String, String> signIndexes = engines.get(0).getSignIndexes();
        // 已配置密钥对的密码算法
        Set<String> engineKeySet = signIndexes.keySet();
        // 获取系统配置的非对称算法
        SysSupportedAlgoConfig sysAlgoConfig = BaseConfigWrapper.getSysAlgoConfig();
        List<String> asymmetricList = sysAlgoConfig.extractAsymmetricList();
        // 取交集
        engineKeySet.retainAll(asymmetricList);
        return engineKeySet.stream().map(EngineAsymAlgoResponse::new).collect(Collectors.toList());
    }

    /**
     * 查询当前配置的加密机所支持的抗量子加密算法
     *
     * @return
     */
    public List<EngineAsymAlgoResponse> queryPqEncKeyType() {
        List<String> pqcAsymAlgoStr = BaseConfigWrapper.getSysAlgoConfig().extractPostQuantumList();
        return pqcAsymAlgoStr.stream().map(EngineAsymAlgoResponse::new).filter(
            asymAlgo -> AlgoPurposeEnum.ENC.equals(asymAlgo.getPurpose())).collect(Collectors.toList());
    }

    /**
     * 根据加密机类型获取支持的非对称密钥类型
     *
     * @param engineType 加密机类型
     * @return 加密机支持的非对称非对称密钥类型
     */
    public List<EngineAsymAlgoResponse> queryKeyTypeByEngineType(String engineType) {
        EngineAlgoInfo engineAlgoByType = EngineUtil.getEngineAlgoByType(EmTypeEnum.getEmTypeEnumByEngineType(engineType));
        Set<AsymAlgo> asymAlgAbility = engineAlgoByType.getAsymAlgAbility();
        SysSupportedAlgoConfig sysSupportedAlgoConfig = ConfigHolder.get().get(SysSupportedAlgoConfig.class);
        List<String> asymmetricList = sysSupportedAlgoConfig.getSupport().extractAsymmetricList();
        List<String> postQuantumList = sysSupportedAlgoConfig.getSupport().extractPostQuantumList();
        return asymAlgAbility.stream()
            // 求当前密码机支持算法与系统支持算法的交集
            .filter(asymAlgo -> {
                String algoName = asymAlgo.getAlgoName();
                boolean anyMatchAsym = asymmetricList.stream().anyMatch(algoName::contains);
                boolean anyMatchPq = postQuantumList.stream().anyMatch(algoName::contains);
                return anyMatchAsym || anyMatchPq;
            })
            .map(asymAlgo -> new EngineAsymAlgoResponse(
                asymAlgo.getAlgoName()
            ))
            .sorted(Comparator.comparing(EngineAsymAlgoResponse::getAlgoName))
            .collect(Collectors.toList());
    }

    public Optional<KeyIndex> queryKeyIndexByKeyType(String keyType) {
        ClusterEmConfig groupEmConfig = ConfigHolder.get().get(ClusterEmConfig.class);
        // 默认获取第一个加密机组配置
        List<EmConfig> engines = groupEmConfig.getGroupEngine().getEngines();
        if (CollectionUtils.isNotEmpty(engines)) {
            // 默认取第一个加密机配置
            List<KeyIndex> indexs = engines.get(0).toEngineConfig().getAsymKeyList();
            for (KeyIndex keyIndex : indexs) {
                if (keyIndex.getKeyAlgo().getAlgoName().equals(keyType)) {
                    return Optional.of(keyIndex);
                }
            }
        }
        return Optional.empty();
    }

    @Override
    public ConfigStatus queryConfigStatus() {
        ClusterEmConfig groupEmConfig = ConfigHolder.get().get(ClusterEmConfig.class);

        ConfigStatus configStatus = new ConfigStatus(ENGINE_CONFIG_NAME);
        boolean status = !ObjectUtils.isEmpty(groupEmConfig) && Objects.nonNull(groupEmConfig.getGroupEngine());
        configStatus.setStatus(status);

        return configStatus;
    }


    /**
     * 获取辅助密码机密钥对支持算法
     *
     * @param engineTypes 加密机类型列表
     * @return {@link List }<{@link EngineAsymAlgoResponse }>
     */
    public List<EngineAsymAlgoResponse> queryKeyTypeByBackup(List<String> engineTypes) {
        // 辅助密码机支持算法 = （辅助密码机支持算法 ∩ 系统支持算法） - 主密码机支持算法
        // 辅助密码机一定是文件加密机，获取文件加密机的算法支持
        EngineAlgoInfo engineAlgoByType = EngineUtil.getEngineAlgoByType(EmTypeEnum.FILE_ENGINE);
        Set<AsymAlgo> backupEngineSupportAlgoInfo = engineAlgoByType.getAsymAlgAbility();

        // 系统支持算法
        SysSupportedAlgoConfig sysSupportedAlgoConfig = BaseConfigWrapper.getSysAlgoConfig();
        List<String> asymmetricList = sysSupportedAlgoConfig.getSupport().extractAsymmetricList();
        List<String> postQuantumList = sysSupportedAlgoConfig.getSupport().extractPostQuantumList();

        // 主密码机支持算法
        Set<AsymAlgo> masterEnginesSupportAlgoInfoSet = new HashSet<>();
        for (String engineType : engineTypes) {
            EngineAlgoInfo engineAlgoInfo = EngineUtil.getEngineAlgoByType(EmTypeEnum.getEmTypeEnumByEngineType(engineType));
            masterEnginesSupportAlgoInfoSet.addAll(engineAlgoInfo.getAsymAlgAbility());
        }
        // 执行条件过滤
        return backupEngineSupportAlgoInfo.stream()
            // 求辅助密码机支持算法与系统支持算法的交集
            .filter(asymAlgo -> {
                String algoName = asymAlgo.getAlgoName();
                boolean anyMatchAsym = asymmetricList.stream().anyMatch(algoName::contains);
                boolean anyMatchPq = postQuantumList.stream().anyMatch(algoName::contains);
                return anyMatchAsym || anyMatchPq;
            })
            // 求剩下的算法与主密码机支持算法的差集
            .filter(asymAlgo -> !masterEnginesSupportAlgoInfoSet.contains(asymAlgo))
            .map(asymAlgo -> new EngineAsymAlgoResponse(asymAlgo.getAlgoName()))
            .sorted(Comparator.comparing(EngineAsymAlgoResponse::getAlgoName))
            .collect(Collectors.toList());
    }

}
