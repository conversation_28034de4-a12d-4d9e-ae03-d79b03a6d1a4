package kl.npki.management.core.service.log.impl;

import kl.npki.base.core.biz.log.model.OpLogInfo;
import kl.npki.base.core.constant.AuditStatusEnum;
import kl.npki.base.core.repository.IOpLogRepository;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.management.core.service.log.IOpLogService;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @date 2022/12/9
 */
@Service
public class OpLogServiceImpl implements IOpLogService {

    private final IOpLogRepository opLogRepository;

    public OpLogServiceImpl() {
        this.opLogRepository = RepositoryFactory.get(IOpLogRepository.class);
    }
    
    /**
     * 操作日志详情展示
     * @param logId
     * @return
     */
    @Override
    public OpLogInfo searchOpLogById(Long logId) {
        return opLogRepository.searchOpLogById(logId);
    }

    /**
     * 修改审计状态
     *
     * @param logId
     * @param auditStatus
     */
    @Override
    public void modifyAuditStatus(Long logId, AuditStatusEnum auditStatus) {
        opLogRepository.modifyAuditStatus(logId, auditStatus);
    }

}
