package kl.npki.management.core.service.cert;

import kl.npki.base.core.biz.cert.model.CertRequestInfo;
import kl.npki.base.core.biz.cert.model.ServerCertRequestInfo;
import kl.npki.management.core.biz.cert.model.CertDetailInfo;

/**
 * 身份证书管理服务接口
 * <AUTHOR>
 */
public interface IIdCertService {
    /**
     * 签发身份证书
     */
    void issueIdCert(CertRequestInfo rootAndIdCertRequestInfo);

    /**
     * 签发身份证书
     */
    void issueIdCertByCa(CertRequestInfo rootAndIdCertRequestInfo);

    /**
     * 更新身份证书
     */
    void updateIdCert(CertRequestInfo rootAndIdCertRequestInfo);
    /**
     * 更新身份证书
     */
    void updateIdCertByCa(CertRequestInfo rootAndIdCertRequestInfo);

    /**
     * 导出身份证书
     * @return
     */
    String exportIdCert();

    /**
     * 查询身份证书详细信息
     * @return CertDetailInfo
     */
    CertDetailInfo queryIdCert();

    /**
     * 获取身份证书请求信息
     *
     * @return ServerCertRequestInfo
     */
    ServerCertRequestInfo getIdCertRequestInfo();

}
