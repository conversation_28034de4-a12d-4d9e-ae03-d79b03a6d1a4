package kl.npki.management.core.service.config;

import ch.qos.logback.core.util.FileSize;
import kl.npki.base.core.biz.config.model.ConfigStatus;
import kl.npki.base.core.biz.config.service.IConfigStatusService;
import kl.npki.base.core.configs.LogConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.utils.SysLogSendUtil;
import kl.npki.management.core.exception.ManagementValidationError;
import kl.npki.management.core.utils.FileSizeHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

import static kl.npki.management.core.constants.I18nParameterVerifyConstant.CANNOT_EXCEED_1TB_I18N_KEY;
import static kl.npki.management.core.constants.I18nParameterVerifyConstant.MUST_NOT_BE_LESS_THAN_1GB_I18N_KEY;

/**
 * 日志配置服务
 *
 * <AUTHOR>
 */
public class LogConfigService implements IConfigStatusService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LogConfigService.class);

    private static final String LOG_CONFIG_NAME = "log";

    /**
     * 查询日志配置
     *
     * @return 日志配置
     */
    public LogConfig queryLogConfig() {
        return BaseConfigWrapper.getLogConfig();
    }

    /**
     * 保存日志配置
     *
     * @param logConfig
     */
    public void saveLogConfig(LogConfig logConfig) {
        // 审计服务启用时校验配置
        checkSysLogConfig(logConfig);
        // 以为byte为单位时的fileSize
        long fileSizeInBytes;
        try {
            // 解析字符串形式的日志容量大小配置
            FileSize fileSize = FileSize.valueOf(logConfig.getTotalSizeCap());
            fileSizeInBytes = fileSize.getSize();
        } catch (Exception e) {
            LOGGER.error("Log file size parse error", e);
            throw ManagementValidationError.LOG_FILE_SIZE_PARSE_ERROR.toException();
        }
        // 校验日志容量大小配置的范围
        if (fileSizeInBytes < FileSizeHelper.MIN_LOG_FILE_SIZE) {
            throw ManagementValidationError.LOG_FILE_SIZE_CONFIG_ERROR.toException(MUST_NOT_BE_LESS_THAN_1GB_I18N_KEY);
        }
        if (fileSizeInBytes > FileSizeHelper.MAX_LOG_FILE_SIZE) {
            throw ManagementValidationError.LOG_FILE_SIZE_CONFIG_ERROR.toException(CANNOT_EXCEED_1TB_I18N_KEY);
        }
        // 判断关键配置是否变更，是否需要切换服务， 需要则进行切换
        judgeIfNeedSwitchAndSwitch(logConfig);

        // 保存配置
        ConfigHolder.get().save(logConfig);
    }

    private  void checkSysLogConfig(LogConfig logConfig) {
        String syslogEnable = logConfig.getSyslogEnable();
        if (Boolean.FALSE.toString().equalsIgnoreCase(syslogEnable)) {
            return;
        }
        String syslogIp = logConfig.getSyslogIp();
        String syslogPort = logConfig.getSyslogPort();
        if (StringUtils.isBlank(syslogIp) || StringUtils.isBlank(syslogPort)) {
            throw ManagementValidationError.AUDIT_LOG_CONFIG_ERROR.toException();
        }
    }


    /**
     * 判断是否需要做审计服务器连接信息变更
     * @param logConfig 当前日志配置请求类
     */
    private void judgeIfNeedSwitchAndSwitch(LogConfig logConfig) {
        // 获取当前环境变量中的配置
        LogConfig oldLogConfig = queryLogConfig();
        // 最新请求的配置里 审计服务的开关为开时做后续处理
        if (Boolean.TRUE.toString().equalsIgnoreCase(logConfig.getSyslogEnable())) {
            // 如果日志配置中的审计服务器相关配置有变动，验证并切换
            boolean configChanged = !Objects.equals(logConfig.getSyslogIp(), oldLogConfig.getSyslogIp())
                    || !Objects.equals(logConfig.getSyslogPort(), oldLogConfig.getSyslogPort())
                    || !Objects.equals(logConfig.getSyslogProtocol(), oldLogConfig.getSyslogProtocol());
            if (configChanged) {
                SysLogSendUtil.switchAppender(logConfig.getSyslogIp(), Integer.parseInt(logConfig.getSyslogPort()), logConfig.getSyslogProtocol());
            }
        }
    }

    @Override
    public ConfigStatus queryConfigStatus() {
        return new ConfigStatus(LOG_CONFIG_NAME, true);
    }
}
