package kl.npki.management.core.service.help;

import kl.npki.management.core.exception.ManagementValidationError;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static kl.npki.management.core.constants.I18nExceptionInfoConstant.UNSUPPORTED_SYSTEM_MODULE_I18N_KEY;

/**
 * 系统帮助信息实现类
 *
 * <AUTHOR>
 * @date 2023/8/25
 */
public class SysHelpTxtServiceImpl implements SysHelpService {

    private static final Logger logger = LoggerFactory.getLogger(SysHelpTxtServiceImpl.class);

    private static final String BASE_PATH = "public/helper";
    private static final String HELP_FILE_NAME = "help.txt";

    private static final List<String> LEGAL_MODULES = new ArrayList<>();

    @PostConstruct
    public void init() throws URISyntaxException {
        URL baseUrl = getClass().getClassLoader().getResource(BASE_PATH);
        if (baseUrl == null) {
            // 找不到系统操作引导资源目录BASE_PATH直接返回
            return;
        }
        Path path = Paths.get(baseUrl.toURI());

        if (Files.exists(path) && Files.isDirectory(path)) {
            // 获取目录下的所有文件夹名称
            try (Stream<Path> stream = Files.list(path)) {
                stream.filter(Files::isDirectory)
                    .map(Path::getFileName)
                    .map(Path::toString)
                    .forEach(LEGAL_MODULES::add);
            } catch (IOException e) {
                logger.error("Unable to read system operation boot directory: {}", e.getMessage());
            }
        } else {
            logger.error("The specified system operation boot path is not a directory: {}", path);
        }
    }

    @Override
    public String getHelpInfo(String module) {
        if (!LEGAL_MODULES.contains(module)) {
            throw ManagementValidationError.PARAM_ERROR.toException(UNSUPPORTED_SYSTEM_MODULE_I18N_KEY, module);
        }
        String filePath = buildFilePath(module);
        try (InputStream is = getClass().getClassLoader().getResourceAsStream(filePath)) {
            if (is == null) {
                return "";
            }
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
                return reader.lines().collect(Collectors.joining("\n"));
            }
        } catch (IOException e) {
            logger.error("Failed to read system operation boot information, module: {}, reason:", module, e);
            return "";
        }
    }

    @Override
    public void updateHelpInfo(String module, String newInfo) {
        if (!LEGAL_MODULES.contains(module)) {
            throw ManagementValidationError.PARAM_ERROR.toException(UNSUPPORTED_SYSTEM_MODULE_I18N_KEY, module);
        }
        String filePath = buildFilePath(module);
        try {
            URL resourceUrl = getClass().getClassLoader().getResource(filePath);
            Path resourcePath;
            if (resourceUrl == null) {
                // 获取BASE_PATH目录的路径
                URL baseUrl = getClass().getClassLoader().getResource(BASE_PATH);
                if (baseUrl == null) {
                    logger.error("Cannot find system operation boot resource directory: {}", BASE_PATH);
                    return;
                }
                resourcePath = Paths.get(baseUrl.toURI());
                // 创建module目录
                resourcePath = resourcePath.resolve(module);
                Files.createDirectories(resourcePath);
                // 创建新文件
                resourcePath = resourcePath.resolve(HELP_FILE_NAME);
                Files.createFile(resourcePath);
            } else {
                resourcePath = Paths.get(resourceUrl.toURI());
            }

            // 写入新的帮助信息
            Files.write(resourcePath, newInfo.getBytes(StandardCharsets.UTF_8));
        } catch (IOException | URISyntaxException e) {
            logger.error("Failed to write system operation boot information, module: {}, reason:", module, e);
        }
    }

    private String buildFilePath(String module) {
        return BASE_PATH + File.separator + module + File.separator + HELP_FILE_NAME;
    }
}
