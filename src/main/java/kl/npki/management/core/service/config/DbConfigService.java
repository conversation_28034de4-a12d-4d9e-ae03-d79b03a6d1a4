package kl.npki.management.core.service.config;

import com.alibaba.druid.pool.DruidDataSource;
import kl.nbase.db.constant.DbRoleTypeEnum;
import kl.nbase.db.support.druid.DruidDataSourceBuilder;
import kl.nbase.db.support.druid.DruidDataSourceProperties;
import kl.nbase.db.support.druid.SlotDruidDataSourceProperties;
import kl.nbase.db.support.slot.SlotDataSourceConfig;
import kl.nbase.db.utils.DBScriptRunner;
import kl.nbase.db.utils.DataSourceUtil;
import kl.nbase.db.utils.DbConnValidUtils;
import kl.npki.base.core.biz.config.model.ConfigStatus;
import kl.npki.base.core.biz.config.service.IConfigStatusService;
import kl.npki.base.core.biz.db.service.IDbSwitchConfigService;
import kl.npki.base.core.configs.DbPasswdConfig;
import kl.npki.base.core.configs.DbPasswdInfo;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.DbConfigType;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.utils.RegionAndLanguageUtil;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.management.core.biz.config.model.DbConfigEntity;
import kl.npki.management.core.biz.config.model.DbConfigInfo;
import kl.npki.management.core.biz.config.model.DbPasswdConfigInfo;
import kl.npki.management.core.constants.DatasourceIndexEnum;
import kl.npki.management.core.exception.ManagementInternalError;
import kl.npki.management.core.utils.DbUrlUtil;
import kl.npki.management.core.utils.RegionChangeHandleUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;

import static kl.npki.base.core.constant.BaseConstant.WEB_ROOT;
import static kl.npki.management.core.constants.I18nConstant.getBaseMgmtCoreI18nMessage;

/**
 * 数据库配置服务
 *
 * <AUTHOR>
 */
public class DbConfigService implements IConfigStatusService {

    private static final String DB_CONFIG_NAME = "db";

    private static final String DML_SQL_FILE_PATH = "/sql/data/npki.sql";

    private static final Logger log = LoggerFactory.getLogger(DbConfigService.class);

    /**
     * 需要检查是否存在的表名，用来决定是否允许执行初始化脚本
     */
    private static final String TABLE_NAME_CHECK = "T_TRUST_CERT";

    private static final String REGION_TABLE_NAME = "T_REGION_MGR";
    /**
     * 默认数据库连接超时时间,单位秒
     */
    private static final int DEFAULT_DB_CONNECT_TIMEOUT = 3;
    private final IDbSwitchConfigService dbSwitchConfigService;

    public DbConfigService(IDbSwitchConfigService dbSwitchConfigService) {
        this.dbSwitchConfigService = dbSwitchConfigService;
    }

    private static DruidDataSourceProperties dbConfigInfo2DruidDataSourceProperties(String dbType,
                                                                                    DbConfigInfo dbConfigInfo,
                                                                                    DbRoleTypeEnum dbRoleType,
                                                                                    String dbName) {
        DruidDataSourceProperties druidDataSourceProperties = new DruidDataSourceProperties();
        druidDataSourceProperties.setName(dbName);
        druidDataSourceProperties.setUrl(DbUrlUtil.getUrl(dbConfigInfo, dbType));
        druidDataSourceProperties.setUsername(dbConfigInfo.getUsername());
        druidDataSourceProperties.setPassword(dbConfigInfo.getPassword());
        druidDataSourceProperties.setDbRoleType(dbRoleType);
        return druidDataSourceProperties;
    }

    public SlotDataSourceConfig queryDbConfig() {
        SlotDataSourceConfig slotConfig = ConfigHolder.get()
                .get(SlotDataSourceConfig.class);

        if (ObjectUtils.isEmpty(slotConfig)) {
            log.warn("not found db config！");
            return null;
        }
        List<DruidDataSourceProperties> druidDataSourceProperties = slotConfig.getDatasource();
        if (CollectionUtils.isEmpty(druidDataSourceProperties) || ObjectUtils.isEmpty(slotConfig.getDruid()) || ObjectUtils.isEmpty(slotConfig.getSharding())) {
            return null;
        }
        return slotConfig;
    }

    public void saveDbConfig(DbConfigEntity dbConfigEntity) {

        testDbConnection(dbConfigEntity);

        List<DbConfigInfo> dbConfigInfoList = dbConfigEntity.getDbConfigInfoList();
        List<DruidDataSourceProperties> dbPropertiesList = new ArrayList<>();

        // 获取其中一个主库配置用于初始化sql语句
        DruidDataSourceProperties masterDruidProperties = null;
        String dbType = dbConfigEntity.getDbType();
        DbConfigType dbConfigType = DbConfigType.valueOf(dbType);
        // 从区域配置中获取时区信息
        String timeZone = RegionAndLanguageUtil.getGMTByRegionConfig();
        if (dbConfigInfoList.size() == 1) {
            // 只有一个库，认为就是逻辑数据源ds0
            masterDruidProperties = dbConfigInfo2DruidDataSourceProperties(dbType, dbConfigInfoList.get(0),
                    DbRoleTypeEnum.MASTER, DatasourceIndexEnum.FORMAL_DATA_BASE.getDesc());
            masterDruidProperties.setCharSet(dbConfigInfoList.get(0).getCharSet());
            masterDruidProperties.setTimeZone(timeZone);
            masterDruidProperties.setDbType(dbType);
            dbPropertiesList.add(masterDruidProperties);
        } else {
            int masterIndex = 1;
            int slaveIndex = 1;
            for (DbConfigInfo dbConfigInfo : dbConfigInfoList) {
                DbRoleTypeEnum dbRoleType = DbRoleTypeEnum.valueOfByName(dbConfigInfo.getDbRoleType());
                String dbName = DbRoleTypeEnum.MASTER.equals(dbRoleType) ? String.format("%s_%d",
                        DbRoleTypeEnum.MASTER.getValue(), masterIndex++) : String.format("%s_%d",
                        DbRoleTypeEnum.SLAVE.getValue(), slaveIndex++);
                DruidDataSourceProperties druidDataSourceProperties = dbConfigInfo2DruidDataSourceProperties(dbType,
                        dbConfigInfo, dbRoleType, dbName);
                druidDataSourceProperties.setCharSet(dbConfigInfo.getCharSet());
                druidDataSourceProperties.setTimeZone(timeZone);
                druidDataSourceProperties.setDbType(dbType);
                dbPropertiesList.add(druidDataSourceProperties);
                // 获取其中一个主库
                if (ObjectUtils.isEmpty(masterDruidProperties) && DbRoleTypeEnum.MASTER.equals(dbRoleType)) {
                    masterDruidProperties = druidDataSourceProperties;
                }
            }
        }

        SlotDruidDataSourceProperties druid = new SlotDruidDataSourceProperties();
        druid.setDriverClassName(dbConfigType.getDriver());
        // 这里不设置默认的校验语句，则会执行 SlotDruidDataSourceProperties 中设置的默认校验语句，部分数据库不支持默认的语句，从而报错
        druid.setValidationQuery(dbConfigType.getValidationQuery());
        int maxConnect = dbConfigEntity.getMaxConnect();
        if (maxConnect > 0) {
            druid.setMaxActive(maxConnect);
        }
        // 由于sharding初始化数据源需要提取创建表，先初始化数据库表
        initDatabase(masterDruidProperties, druid, dbConfigType);

        // 保存数据库配置
        dbSwitchConfigService.saveOrUpdateDataSourceConfig(dbPropertiesList, druid,
                dbConfigEntity.getEnableReadWrite());

    }

    /**
     * 初始化数据库
     *
     * @param masterDruidProperties 主库配置
     * @param druid                 druid通用配置
     */
    private void initDatabase(DruidDataSourceProperties masterDruidProperties, SlotDruidDataSourceProperties druid,
                              DbConfigType dbConfigType) {
        // 使用uuid作为数据源名称，避免与已有的数据源冲突
        try (DruidDataSource dataSource = DruidDataSourceBuilder.getDruidDataSource(masterDruidProperties, druid, UUID.randomUUID().toString())) {
            // 获取数据库脚本(根据数据库类型)
            List<String> sqlFilePathList = new ArrayList<>(2);

            // 添加sql脚本路径
            // 设置sql脚本路径为绝对路径
            sqlFilePathList.add(WEB_ROOT + File.separator + dbConfigType.getSqlFilePath());
            sqlFilePathList.add(WEB_ROOT + File.separator + DML_SQL_FILE_PATH);

            // 执行数据库创建表脚本&执行资源数据脚本
            try {
                if (!DataSourceUtil.isTableExist(dataSource, TABLE_NAME_CHECK)) {
                    DBScriptRunner.runScript(dataSource, sqlFilePathList);
                } else {
                    log.warn("The business table already exists, initialization script will no longer be executed");
                }
            } catch (Exception e) {
                throw ManagementInternalError.SQL_SCRIPT_RUN_ERROR.toException(e);
            }
        }

        // 区域初始化脚本存在 则新建个数据库连接执行
        String regionSqlPath = judgeRegionSqlIfExist();
        if (StringUtils.isNotBlank(regionSqlPath)) {
            try (DruidDataSource dataSource2 = DruidDataSourceBuilder.getDruidDataSource(masterDruidProperties, druid, UUID.randomUUID().toString())) {
                try {
                    // 区域表存在的情况下，先清空再插入数据
                    if (DataSourceUtil.isTableExist(dataSource2, REGION_TABLE_NAME)) {
                        String deleteSql = "DELETE FROM T_REGION_MGR WHERE ID > 0;";
                        DBScriptRunner.runScript(dataSource2, deleteSql);
                        DBScriptRunner.runScript(dataSource2, Collections.singletonList(regionSqlPath));
                    } else {
                        log.warn("The region table does not exists, initialization script will no longer be executed");
                    }
                } catch (Exception e) {
                    throw ManagementInternalError.SQL_SCRIPT_RUN_ERROR.toException(e);
                }
            }
        }


    }

    @Override
    public ConfigStatus queryConfigStatus() {
        return new ConfigStatus(DB_CONFIG_NAME, SystemUtil.isDataSourceConfigured());
    }

    public void testDbConnection(DbConfigEntity dbConfigEntity) {
        List<DbConfigInfo> dbConfigInfoList = dbConfigEntity.getDbConfigInfoList();

        String dbType = dbConfigEntity.getDbType();
        DbConfigType dbConfigType = DbConfigType.valueOf(dbType);
        String driverClassName = dbConfigType.getDriver();

        for (DbConfigInfo dbConfigInfo : dbConfigInfoList) {
            // 从区域配置中获取时区信息
            dbConfigInfo.setTimeZone(RegionAndLanguageUtil.getGMTByRegionConfig());
            // 测试数据库能否连接
            String dbUrl = DbUrlUtil.getUrl(dbConfigInfo, dbType);
            String username = dbConfigInfo.getUsername();
            String password = dbConfigInfo.getPassword();
            boolean validResult = DbConnValidUtils.validConnection(dbUrl, username, password, driverClassName, DEFAULT_DB_CONNECT_TIMEOUT);
            if (!validResult) {

                String appendMsg = new StringBuilder(100)
                        .append("[ ")
                        .append(getBaseMgmtCoreI18nMessage("ip_address", dbConfigInfo.getDbAddress()))
                        .append(",")
                        .append(getBaseMgmtCoreI18nMessage("port", dbConfigInfo.getDbPort()))
                        .append(",")
                        .append(getBaseMgmtCoreI18nMessage("db_name", dbConfigInfo.getDbName()))
                        .append(",")
                        .append(getBaseMgmtCoreI18nMessage("username", dbConfigInfo.getUsername()))
                        .append(" ]").toString();
                throw ManagementInternalError.DB_CONN_FAILED.toException(appendMsg);
            }
        }
    }

    private String judgeRegionSqlIfExist() {
        String currentRegion = BaseConfigWrapper.getRegionalLanguageConfig().getRegion();
        return RegionChangeHandleUtil.getRegionSqlFilePath(currentRegion);
    }

    /**
     * 保存数据库密码到配置文件
     *
     * @param dbPasswdRequestList
     */
    public void saveDbPasswd(List<DbPasswdConfigInfo> dbPasswdRequestList) {
        List<DbPasswdInfo> dbPasswdInfoList = new ArrayList<>();
        for (DbPasswdConfigInfo dbPasswdConfigInfo : dbPasswdRequestList) {
            DbPasswdInfo dbPasswdInfo = new DbPasswdInfo();
            dbPasswdInfo.setDbName(dbPasswdConfigInfo.getDbName());
            dbPasswdInfo.setDbPasswd(dbPasswdConfigInfo.getPassword());
            dbPasswdInfoList.add(dbPasswdInfo);
        }
        DbPasswdConfig dbPasswdConfig = BaseConfigWrapper.getDbPasswdConfig();
        dbPasswdConfig.setDbPasswdInfoList(dbPasswdInfoList);
        ConfigHolder.get().save(dbPasswdConfig);
    }

    /**
     * 配置数据库密码(未配置密码从配置文件中加载)
     *
     * @param dbConfigEntity
     */
    public void reloadDbPasswd(DbConfigEntity dbConfigEntity) {
        List<DbConfigInfo> dbConfigInfoList = dbConfigEntity.getDbConfigInfoList();
        // 如果密码为空，则使用配置文件中的密码（工行项目界面不输入密码）
        for (DbConfigInfo dbConfigInfo : dbConfigInfoList) {
            if (!StringUtils.isBlank(dbConfigInfo.getPassword())) {
                continue;
            }
            List<DbPasswdInfo> dbPasswdInfoList = BaseConfigWrapper.getDbPasswdConfig().getDbPasswdInfoList();
            for (DbPasswdInfo dbPasswdInfo : dbPasswdInfoList) {
                if (dbPasswdInfo.getDbName().equals(dbConfigInfo.getDbName())) {
                    dbConfigInfo.setPassword(dbPasswdInfo.getDbPasswd());
                }
            }
        }
    }
}
