package kl.npki.management.core.service.config;

import kl.nbase.config.utils.ConfigTypeUtils;
import kl.nbase.helper.utils.PropertiesUtils;
import kl.npki.base.core.biz.dataprotect.service.IDataProtectService;
import kl.npki.base.core.biz.dataprotect.service.impl.DataProtectServiceHashMacSm3Impl;
import kl.npki.base.core.biz.mainkey.model.NakedMainKeyEntity;
import kl.npki.base.core.biz.mainkey.service.MainKeyManager;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.core.utils.Base64Util;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import static kl.npki.base.core.constant.BaseConstant.WEB_ROOT;


/**
 * 配置文件完整性值服务
 *
 * <AUTHOR>
 */
public class ProtectConfigFileService {

    private static final Logger log = LoggerFactory.getLogger(ProtectConfigFileService.class);

    /**
     * 保存日志完整性值结果的文件路径
     */
    public static final String CONFIG_FILE_PROTECT_RESULT_FILE = WEB_ROOT +
            "/config/config_file_protect_result.properties";

    /**
     * 待保护的配置文件路径列表
     */
    public static final List<String> TOBE_PROTECT_CONFIG_FILE_PATH_LIST = new ArrayList<>();
    /**
     * 配置文件目录
     */
    private static final String CONFIG_DIR = WEB_ROOT + "/config";

    static {
        TOBE_PROTECT_CONFIG_FILE_PATH_LIST.add(CONFIG_DIR + File.separator + BaseConstant.CONFIG_FILE_NAME);
        TOBE_PROTECT_CONFIG_FILE_PATH_LIST.add(CONFIG_DIR + File.separator + "npki-default.properties");
        TOBE_PROTECT_CONFIG_FILE_PATH_LIST.add(CONFIG_DIR + File.separator + "npki-demo.properties");
    }


    /**
     * 为配置文件初始化完整性值，并生成结果文件
     */
    public void initDataFull() throws Exception {
        // 读取完整性结果文件
        Properties properties = PropertiesUtils.loadOrCreateCfgFile(CONFIG_FILE_PROTECT_RESULT_FILE);

        for (String filePath : TOBE_PROTECT_CONFIG_FILE_PATH_LIST) {
            File file = new File(filePath);
            byte[] hMac = genDataFull(file);
            // 更新至结果文件
            String absolutePath = file.getCanonicalPath();
            String fileKey = Base64Util.base64Encode(absolutePath.getBytes());
            properties.setProperty(fileKey, Base64Util.base64Encode(hMac));
        }

        // 保存结果
        PropertiesUtils.attemptWriteProperties(properties, CONFIG_FILE_PROTECT_RESULT_FILE);
    }

    /**
     * 更新指定配置文件的完整性值
     *
     * @param filePath 文件路径
     */
    public void updateDataFull(String filePath) throws Exception {
        // 读取完整性结果文件
        Properties properties = PropertiesUtils.loadOrCreateCfgFile(CONFIG_FILE_PROTECT_RESULT_FILE);

        File file = new File(filePath);
        byte[] hMac = genDataFull(file);
        // 更新至结果文件
        String absolutePath = file.getCanonicalPath();
        String fileKey = Base64Util.base64Encode(absolutePath.getBytes());
        properties.setProperty(fileKey, Base64Util.base64Encode(hMac));

        // 保存结果
        PropertiesUtils.attemptWriteProperties(properties, CONFIG_FILE_PROTECT_RESULT_FILE);
    }

    /**
     * 校验所有配置文件的完整性
     *
     * @return
     */
    public boolean checkAllDataFull() {

        if(!ConfigTypeUtils.isLocalFileType()){
            log.info("Configuration type is not local file configuration, skip integrity verification of configuration files");
            return true;
        }

        for (String filePath : TOBE_PROTECT_CONFIG_FILE_PATH_LIST) {
            if (!checkSingleFileDataFull(filePath)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 校验文件的完整性
     *
     * @param configFilePath
     * @return
     */
    public boolean checkDataFull(String configFilePath) {

        if(!ConfigTypeUtils.isLocalFileType()){
            log.info("Configuration type is not local file configuration, skip integrity verification of configuration file [{}]", configFilePath);
            return true;
        }

        return checkSingleFileDataFull(configFilePath);
    }

    /**
     * 校验文件的完整性
     *
     * @param configFilePath
     * @return
     */
    private boolean checkSingleFileDataFull(String configFilePath) {
        try {
            // 读取完整性结果文件
            Properties properties = PropertiesUtils.loadOrCreateCfgFile(CONFIG_FILE_PROTECT_RESULT_FILE);

            // 获取文件对应的完整性值
            File configFile = new File(configFilePath);
            String absolutePath = configFile.getCanonicalPath();
            String fileKey = Base64Util.base64Encode(absolutePath.getBytes());
            String hMacValue = properties.getProperty(fileKey);

            // 获取主密钥
            NakedMainKeyEntity mainKey = MainKeyManager.getInstance().getCurrentMainKey();

            // 使用HMacSm3实现
            IDataProtectService dataProcess = new DataProtectServiceHashMacSm3Impl(mainKey.getSecretKey());

            // 读取文件原文
            byte[] oriData = FileUtils.readFileToByteArray(configFile);
            // 验证完整性值
            boolean result = dataProcess.verifyData(oriData, Base64Util.base64Decode(hMacValue));

            // 打印日志
            if (result) {
                log.info("Configuration file: {}, integrity verification successful", configFilePath);
            } else {
                log.error("Configuration file: {}, integrity check failed", configFilePath);
            }
            return result;
        } catch (Exception e) {
            log.error("Configuration file [{}] integrity check exception", configFilePath, e);
            return false;
        }
    }
    /**
     * 生成完整性值
     *
     * @param file
     * @return
     * @throws Exception
     */
    private byte[] genDataFull(File file) throws Exception {
        // 获取主密钥
        NakedMainKeyEntity mainKey = MainKeyManager.getInstance().getCurrentMainKey();

        // 使用HMacSm3实现
        IDataProtectService dataProcess = new DataProtectServiceHashMacSm3Impl(mainKey.getSecretKey());

        // 需要生成完整性值
        byte[] tobeHmac = FileUtils.readFileToByteArray(file);
        // 生成完整性hash值
        return dataProcess.generateData(tobeHmac);
    }

}
