package kl.npki.management.core.service.cert;

import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.management.core.biz.cert.model.KeyIndexInfo;

/**
 * @Author: guoq
 * @Date: 2023/10/7
 * @description: 密钥索引信息
 */
public interface IKeyIndexService {

    /**
     * 根据密钥类型获取预置密钥索引信息
     * @param asymAlgo 算法类型
     * @return
     */
    KeyIndexInfo getKeyIndexInfoByKeyType(AsymAlgo asymAlgo);
}
