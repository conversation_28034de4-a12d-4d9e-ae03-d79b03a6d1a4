package kl.npki.management.core.service.log.impl;

import kl.nbase.bean.convert.ConvertService;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.nbase.security.utils.SignatureAlgoUtil;
import kl.npki.base.core.biz.log.model.OpLogInfo;
import kl.npki.base.core.constant.AuditStatusEnum;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.base.core.utils.KeyUtils;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import kl.npki.management.core.biz.log.model.AuditLogInfo;
import kl.npki.management.core.repository.IAdminCertRepository;
import kl.npki.management.core.service.log.IAuditLogService;
import kl.npki.management.core.service.log.IOpLogService;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.security.PublicKey;

/**
 * <AUTHOR>
 * @date 2023/3/21
 */
@Service
public class AuditLogServiceImpl implements IAuditLogService {

    private static final Logger log = LoggerFactory.getLogger(AuditLogServiceImpl.class);

    @Resource
    private IOpLogService opLogService;

    @Resource
    private IAdminCertRepository adminCertRepository;

    @Resource
    private ConvertService convertService;

    /**
     * 日志审计
     *
     * @param logId
     * @return
     */
    @Override
    public boolean auditLog(Long logId) {
        boolean verify;
        OpLogInfo opLogInfo = null;
        try {
            // 根据logId查询日志相关信息 && 验证数据完整性
            opLogInfo = opLogService.searchOpLogById(logId);
            if (opLogInfo == null) {
                log.error("Log audit failed, log with id {} not found", logId);
                return false;
            }

            // 验证操作者签名
            verify = verifySignData(opLogInfo);
        } catch (Exception e) {
            log.error("Log audit failed, logId: {}, operation name: {}",
                logId,
                opLogInfo != null ? opLogInfo.getLogDo() : "unknown",
                e);
            verify = false;
        }

        // 审计结果
        AuditStatusEnum status = verify ? AuditStatusEnum.SUCCESS : AuditStatusEnum.FAILED;
        opLogService.modifyAuditStatus(logId, status);
        return verify;
    }

    /**
     * 验证签名
     */
    private boolean verifySignData(OpLogInfo opLogInfo) throws Exception {
        // 没有签名也返回ture，兼容非证书登录情况
        if (StringUtils.isEmpty(opLogInfo.getSignData())) {
            return true;
        }
        // 签名值
        String signData = opLogInfo.getSignData();
        // 签名原文
        String originData = opLogInfo.getOriginData();
        // 获取管理员证书
        String logWho = opLogInfo.getLogWho();
        AdminCertEntity adminCertInfo = adminCertRepository.getByAdminInfoId(Long.valueOf(logWho));
        String signCertValue = adminCertInfo.getSignCertValue();
        Certificate signCert = CertUtil.parseCert(signCertValue);
        PublicKey publicKey = KeyUtils.getPublicKey(signCert.getSubjectPublicKeyInfo());
        HashAlgo hashAlgoByOid = SignatureAlgoUtil.getHashAlgoByOid(signCert.getSignatureAlgorithm().getAlgorithm().getId());
        return EngineHolder.get().verify(publicKey, originData.getBytes(), Base64.decodeBase64(signData), hashAlgoByOid, null);
    }

    /**
     * 获取日志审计详情
     *
     * @param logId
     * @return
     */
    @Override
    public AuditLogInfo getAuditLogResponse(Long logId) {
        OpLogInfo opLogInfo = opLogService.searchOpLogById(logId);
        return convertService.convert(opLogInfo, AuditLogInfo.class);
    }

}
