package kl.npki.management.core.service.inspection.impl;

import kl.nbase.helper.utils.JsonUtils;
import kl.npki.base.core.biz.inspection.InspectionItemManager;
import kl.npki.base.core.biz.inspection.InspectionReportTypeEnum;
import kl.npki.base.core.biz.inspection.model.InspectionItemResult;
import kl.npki.base.core.biz.inspection.model.InspectionRecordEntity;
import kl.npki.base.core.biz.inspection.model.InspectionReportInfo;
import kl.npki.base.core.biz.inspection.report.InspectionReportGenerator;
import kl.npki.base.core.repository.IInspectionRecordRepository;
import kl.npki.management.core.biz.inspection.model.InspectionGroupedItems;
import kl.npki.management.core.biz.inspection.model.InspectionGroupedResults;
import kl.npki.management.core.exception.ManagementInternalError;
import kl.npki.management.core.service.inspection.IInspectionService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统巡检服务实现
 *
 * <AUTHOR>
 * @date 08/05/2025 11:16
 **/
@Component
public class InspectionServiceImpl implements IInspectionService {

    @Resource
    private IInspectionRecordRepository inspectionRecordRepository;

    @Override
    public List<InspectionGroupedItems> getInspectionItems() {
        Map<String, List<String>> groupedInspectionItems = InspectionItemManager.getInstance().getGroupedInspectionItems();
        return groupedInspectionItems.entrySet().stream().map(
            entry -> new InspectionGroupedItems(entry.getKey(), entry.getValue())
        ).collect(Collectors.toList());
    }

    @Override
    public Long executeInspection(String name) {

        // 巡检开始时间
        LocalDateTime startTime = LocalDateTime.now();

        // 执行巡检
        List<InspectionItemResult> inspectionItemResults = InspectionItemManager.getInstance().executeInspection();

        // 巡检结束时间
        LocalDateTime endTime = LocalDateTime.now();

        // 巡检耗时
        Long costMills = Duration.between(startTime, endTime).toMillis();

        InspectionRecordEntity inspectionRecordEntity = new InspectionRecordEntity();
        inspectionRecordEntity.setName(name);
        inspectionRecordEntity.setItemResults(inspectionItemResults);
        inspectionRecordEntity.setStartTime(startTime);
        inspectionRecordEntity.setEndTime(endTime);
        inspectionRecordEntity.setCostMills(costMills);

        // 入库保存
        return inspectionRecordRepository.addRecord(inspectionRecordEntity);
    }

    @Override
    public List<InspectionGroupedResults> getInspectionResults(Long id) {

        // 获取巡检结果
        Optional<InspectionRecordEntity> optionalInspectionRecord = inspectionRecordRepository.getRecordById(id);
        if (!optionalInspectionRecord.isPresent()) {
            // 巡检结果不存在，则返回null
            return null;
        }

        List<InspectionItemResult> itemResults = optionalInspectionRecord.get().getItemResults();
        return getInspectionGroupedResults(itemResults);
    }

    @Override
    public List<InspectionGroupedResults> groupInspectionResultsByType(String itemResultsJson) {

        if (StringUtils.isBlank(itemResultsJson)) {
            return Collections.emptyList();
        }

        // 字符串转成JSON
        List<InspectionItemResult> itemResults = JsonUtils.from(itemResultsJson, List.class, InspectionItemResult.class);

        return getInspectionGroupedResults(itemResults);
    }

    @Override
    public InspectionReportInfo generateInspectionReport(Long id, String reportType) {

        // 获取巡检结果
        Optional<InspectionRecordEntity> optionalInspectionRecord = inspectionRecordRepository.getRecordById(id);
        if (!optionalInspectionRecord.isPresent()) {
            // 巡检结果不存在，则返回null
            return null;
        }

        // 获取巡检项结果
        InspectionRecordEntity inspectionRecordEntity = optionalInspectionRecord.get();
        List<InspectionItemResult> itemResults = inspectionRecordEntity.getItemResults();
        // 获取报告生成器
        InspectionReportTypeEnum reportTypeEnum = InspectionReportTypeEnum.valueOf(reportType.toUpperCase());
        InspectionReportGenerator reportGenerator = reportTypeEnum.getReportGenerator();

        // 生成巡检报告
        byte[] reportContent;
        try {
            reportContent = reportGenerator.generate(itemResults);
        } catch (IOException e) {
            throw ManagementInternalError.SYSTEM_ERROR.toException(e);
        }

        return new InspectionReportInfo(reportTypeEnum.getFileName(inspectionRecordEntity.getName()), reportContent);
    }

    private List<InspectionGroupedResults> getInspectionGroupedResults(List<InspectionItemResult> itemResults) {
        if (CollectionUtils.isEmpty(itemResults)) {
            return Collections.emptyList();
        }

        return itemResults.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(InspectionItemResult::getType))
            .entrySet().stream().map(
                results -> new InspectionGroupedResults(results.getKey(), results.getValue())
            ).collect(Collectors.toList());
    }
}
