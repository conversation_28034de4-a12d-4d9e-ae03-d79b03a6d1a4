package kl.npki.management.core.service.cert.impl;

import kl.nbase.cache.lock.ILock;
import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.gmssl.common.GmSslConstants;
import kl.nbase.helper.utils.CheckUtils;
import kl.nbase.helper.utils.JsonUtils;
import kl.nbase.netty.conf.NettyHttpServerConfig;
import kl.nbase.netty.conf.NettyTcpServerConfig;
import kl.nbase.security.asn1.ASN1ObjectIdentifier;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.pkcs.PKCS10CertificationRequest;
import kl.nbase.security.utils.PEMUtil;
import kl.npki.base.core.annotation.KlTransactional;
import kl.npki.base.core.biz.cert.model.*;
import kl.npki.base.core.biz.cert.service.CaCertMgr;
import kl.npki.base.core.biz.cert.service.ManageCertMgr;
import kl.npki.base.core.biz.config.model.FileConfigEntity;
import kl.npki.base.core.biz.ssl.SslClientCertEntity;
import kl.npki.base.core.common.CommonLock;
import kl.npki.base.core.common.crypto.SignatureHelper;
import kl.npki.base.core.common.manager.MgrHolder;
import kl.npki.base.core.configs.ManagementSslConfig;
import kl.npki.base.core.constant.*;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.core.repository.IFileConfigRepository;
import kl.npki.base.core.repository.ISSLCertRepository;
import kl.npki.base.core.repository.ITrustCertRepository;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.base.core.utils.KeyStoreUtil;
import kl.npki.base.core.utils.ZipUtil;
import kl.npki.management.core.biz.ssl.model.SslServerCertInfo;
import kl.npki.management.core.exception.ManagementValidationError;
import kl.npki.management.core.service.cert.ISslCertService;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.math.BigInteger;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.util.*;


import static kl.npki.management.core.constants.I18nConstant.getBaseMgmtCoreI18nMessage;
import static kl.npki.management.core.constants.I18nExceptionInfoConstant.*;

/**
 * 服务器站点证书具体实现
 *
 * <AUTHOR>
 * @date 2023/1/4
 */
public class SslCertServiceImpl implements ISslCertService {

    private static final String SSL_SIGN_SITE_CERT_FILENAME_I18N_KEY = "sign_site_certificate";
    private static final String SSL_ENC_SITE_CERT_FILENAME_I18N_KEY = "encryption_site_certificate";
    private static final String SSL_ENC_SITE_CERT_KEY_FILENAME_I18N_KEY = "encryption_certificate_key";
    private static final String CERT_CHAIN_FORMAT_I18N_KEY = "certificate_chain";
    private static final String LOCK_ID = "ssl";
    private final IFileConfigRepository fileConfigRepository;
    private final ITrustCertRepository trustCertRepository;

    public SslCertServiceImpl() {
        this.fileConfigRepository = RepositoryFactory.get(IFileConfigRepository.class);
        this.trustCertRepository = RepositoryFactory.get(ITrustCertRepository.class);
    }

    private static SslClientCertEntity doSignSSLClientCert(String certReq, Long validDays, Long sysId) {
        // 严格校验证书请求，需为Base64格式
        if (!Base64.isBase64(certReq)) {
            throw ManagementValidationError.CERT_REQ_NOT_BASE64.toException();
        }
        // 解析P10请求
        PKCS10CertificationRequest certRequest;
        try {
            // 格式化certReq
            String formatRequest = PEMUtil.unFormatRequest(certReq);
            certRequest = new PKCS10CertificationRequest(Base64.decodeBase64(formatRequest));
        } catch (Exception e) {
            throw ManagementValidationError.CERT_REQ_NOT_PKCS10.toException(e);
        }

        // 验证P10请求的签名
        if (!SignatureHelper.verifyCSR(certRequest)) {
            throw BaseValidationError.CERT_REQUEST_SIGN_ERROR.toException();
        }

        try {
            ASN1ObjectIdentifier manageCertAlgorithm = MgrHolder.getManageCertMgr().getManageCert().getSubjectPublicKeyInfo().getAlgorithm().getAlgorithm();
            ASN1ObjectIdentifier certRequestAlgorithm = certRequest.getSubjectPublicKeyInfo().getAlgorithm().getAlgorithm();
            if (!manageCertAlgorithm.equals(certRequestAlgorithm)) {
                throw BaseValidationError.MGR_ROOT_KEY_ALGORITHM_MISMATCH.toException();
            }
            // 使用管理根签发证书，SSL客户端证书不需要主体备用名
            Certificate certificate = MgrHolder.getManageCertMgr().signCert(certRequest, validDays,
                    CertTypeEnum.SSL_SIGN_CLIENT_CERT, null);

            SslClientCertEntity sslClientCertEntity = SslClientCertEntity.buildSslClientCertEntity(certificate, sysId);

            // 判断是否签发双证书
            ManageCertMgr manageCertMgr = MgrHolder.getManageCertMgr();
            if (manageCertMgr.isTwinCert()) {
                AsymAlgo certAlgo = manageCertMgr.getManageCertEntity().getCertAlgo();
                ClusterEngine engine = EngineHolder.get();
                KeyPair encKeyPair = engine.genKeyPair(certAlgo);
                // 签发加密证书
                Certificate encCertificate = manageCertMgr.signSslEncCert(certRequest, encKeyPair.getPublic(),
                        validDays, null);
                // 填充加密证书相关的数据
                sslClientCertEntity.fillEncCertInfo(encCertificate, encKeyPair.getPrivate());
            }
            return sslClientCertEntity;
        } catch (Exception e) {
            throw BaseInternalError.CERT_ISSUE_ERROR.toException(e);
        }
    }

    /**
     * 签发服务器站点证书
     *
     * @return
     */
    @Override
    @KlTransactional
    public String signSslServerCert(CertRequestInfo certRequestInfo) {
        ILock lock = CommonLock.INSTANCE.lock(LOCK_ID);
        if (!lock.tryLock()) {
            throw BaseInternalError.SSL_CERT_INIT_GET_LOCK_ERROR.toException();
        }
        try {
            // 通过颁发者公钥获取到对应的非对称算法类型
            ManageCertMgr manageCertMgr = MgrHolder.getManageCertMgr();
            ClusterEngine engine = EngineHolder.get();
            TrustCertEntity manageCertEntity = manageCertMgr.getManageCertEntity();
            if (ObjectUtils.isEmpty(manageCertEntity)) {
                throw BaseInternalError.ROOT_NOT_EXIST.toException();
            }
            AsymAlgo certAlgo = manageCertEntity.getCertAlgo();
            KeyPair signKeyPair = engine.genKeyPair(certAlgo);
            certRequestInfo.setPublicKey(signKeyPair.getPublic());
            // 签发签名证书
            Certificate signCert = manageCertMgr.signCert(certRequestInfo, CertTypeEnum.SSL_SIGN_SERVER_CERT);
            Certificate encCert;
            KeyPair encKeyPair;
            // 判断是否签发双证书
            if (manageCertMgr.isTwinCert()) {
                // 加密证书密钥对
                AsymAlgo encCertAlgo = certAlgo;
                // 判断是否为抗量子算法
                if (certAlgo.isPQC()) {
                    // 检查抗量子加密算法，并返回解析后的算法类
                    encCertAlgo = certRequestInfo.checkAndReturnPqEncKeyType();
                }
                encKeyPair = engine.genKeyPair(encCertAlgo);

                certRequestInfo.setPublicKey(encKeyPair.getPublic());
                // 签发加密证书
                encCert = manageCertMgr.signCert(certRequestInfo, CertTypeEnum.SSL_ENC_SERVER_CERT);
                saveCertInfo(signCert, encCert, signKeyPair.getPrivate(), encKeyPair.getPrivate(), certRequestInfo, true);
            } else {
                saveCertInfo(signCert, signKeyPair, certRequestInfo, true);
            }
            return certAlgo.isSM2() ? BaseConstant.GMSSL_PROTOCOL : BaseConstant.TLS_PROTOCOL;
        } catch (Exception e) {
            throw BaseInternalError.CERT_ISSUE_ERROR.toException(SSL_CERT_ISSUE_ERROR_I18N_KEY, e);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public String signSslServerCertByCa(CertRequestInfo certRequestInfo) {
        ILock lock = CommonLock.INSTANCE.lock(LOCK_ID);
        if (!lock.tryLock()) {
            throw BaseInternalError.SSL_CERT_INIT_GET_LOCK_ERROR.toException();
        }
        CaCertMgr caCertMgr = MgrHolder.getCaCertMgr();
        try {
            IssueCertInfo issueCertInfo = caCertMgr.signSslCert(certRequestInfo);
            saveCertInfo(issueCertInfo.getSignCertValue(),
                    issueCertInfo.getEncCertValue(),
                    issueCertInfo.getSignPrivateKey(),
                    issueCertInfo.getEncPrivateKey(),
                    certRequestInfo, false);
        } catch (Exception e) {
            throw BaseInternalError.CERT_ISSUE_ERROR.toException(SSL_CERT_ISSUE_BY_CA_ERROR_I18N_KEY, e);
        } finally {
            lock.unlock();
        }
        return BaseConstant.TLS_PROTOCOL;
    }

    /**
     * 获取服务器站点证书
     *
     * @return
     */
    @Override
    public SslServerCertInfo getSslServerCert() {
        TrustCertEntity sslCert = trustCertRepository.getCert(TrustCertType.SSL);
        if (Objects.isNull(sslCert) || StringUtils.isEmpty(sslCert.getCertValue())) {
            // 如果还没有进行签发服务器站点证书，那么直接返回一个空对象给前端
            return new SslServerCertInfo();
        }
        return SslServerCertInfo.buildSslServerCertResponse(sslCert.getCertValue(), sslCert.getEncCertValue());
    }

    @Override
    public ServerCertRequestInfo getSslCertRequestInfo() {
        TrustCertEntity sslCert = trustCertRepository.getCert(TrustCertType.SSL);
        if (Objects.isNull(sslCert) || StringUtils.isEmpty(sslCert.getRegisterInfo())) {
            return new ServerCertRequestInfo();
        }
        return JsonUtils.from(sslCert.getRegisterInfo(), ServerCertRequestInfo.class);
    }

    /**
     * 签发ssl站点证书
     *
     * @param certReq
     * @param validDays
     * @return
     */
    @Override
    public void signSslClientCert(String certReq, Long validDays, Long sysId) {
        ISSLCertRepository sslCertRepository = RepositoryFactory.get(ISSLCertRepository.class);
        SslClientCertEntity searchBySysId = sslCertRepository.searchSSLClientCert(sysId);
        CheckUtils.isTrue(Objects.isNull(searchBySysId),
                ManagementValidationError.SSL_CLIENT_CERT_ALREADY_EXISTS.toException("sysId:" + sysId));
        // 签发证书
        SslClientCertEntity sslClientCertEntity = doSignSSLClientCert(certReq, validDays, sysId);
        // 将站点证书存入db
        sslCertRepository.saveSSLClientCert(sslClientCertEntity);
    }

    @Override
    public boolean updateSslClientCert(String certReq, Long validDays, Long sysId) {
        ISSLCertRepository sslCertRepository = RepositoryFactory.get(ISSLCertRepository.class);
        SslClientCertEntity searchBySysId = sslCertRepository.searchSSLClientCert(sysId);
        CheckUtils.isTrue(Objects.nonNull(searchBySysId),
                ManagementValidationError.SSL_CLIENT_CERT_NOT_FOUND.toException("sysId:" + sysId));
        // 签发证书
        SslClientCertEntity sslClientCertEntity = doSignSSLClientCert(certReq, validDays, sysId);
        // DB更新站点证书
        return sslCertRepository.updateSSLClientCert(sslClientCertEntity);
    }

    @Override
    public byte[] generateSslClientCertZipData(Long sysId) {
        ISSLCertRepository sslCertRepository = RepositoryFactory.get(ISSLCertRepository.class);

        SslClientCertEntity sslClientCert = sslCertRepository.searchSSLClientCert(sysId);
        if (Objects.isNull(sslClientCert)) {
            throw ManagementValidationError.PARAM_ERROR.toException(UNABLE_TO_FIND_THE_CORRESPONDING_SSL_CLIENT_CERTIFICATE_PLEASE_CHECK_THE_ID_I18N_KEY + sysId);
        }
        // 要打包到zip中的文件
        Map<String, byte[]> fileNameToContentMap = new HashMap<>();
        // 签名站点证书
        String certValue = sslClientCert.getCertValue();
        if (StringUtils.isNotBlank(certValue)) {
            fileNameToContentMap.put(getBaseMgmtCoreI18nMessage(SSL_SIGN_SITE_CERT_FILENAME_I18N_KEY, sslClientCert.getCn()), certValue.getBytes());
        }
        // 加密站点证书
        String encCertValue = sslClientCert.getEncCertValue();
        if (StringUtils.isNotBlank(encCertValue)) {
            fileNameToContentMap.put(getBaseMgmtCoreI18nMessage(SSL_ENC_SITE_CERT_FILENAME_I18N_KEY), encCertValue.getBytes());
        }
        // 站点加密证书的私钥(数字信封格式)
        String envelopedPriKeyData = sslClientCert.getEnvelopedPriKeyData();
        if (StringUtils.isNotBlank(envelopedPriKeyData)) {
            fileNameToContentMap.put(getBaseMgmtCoreI18nMessage(SSL_ENC_SITE_CERT_KEY_FILENAME_I18N_KEY), envelopedPriKeyData.getBytes());
        }
        // 证书链文件
        Certificate[] certChains = MgrHolder.getManageCertMgr().getCertChains();
        for (Certificate certChain : certChains) {
            String certChainFileName = getBaseMgmtCoreI18nMessage(CERT_CHAIN_FORMAT_I18N_KEY, CertUtil.getCommonName(certChain.getSubject()));
            fileNameToContentMap.put(certChainFileName, CertUtil.encodeCertWithBase64(certChain).getBytes());
        }

        try {
            return ZipUtil.compress(fileNameToContentMap);
        } catch (IOException e) {
            throw BaseInternalError.EXPORT_SSL_SITE_CERT_ERROR.toException(e);
        }
    }

    @Override
    public void signSslServerCertPostHandle(String sslProtocol) {
        if (StringUtils.isBlank(sslProtocol)) {
            throw ManagementValidationError.PARAM_ERROR.toException(SSL_PROTOCOL_CANNOT_BE_EMPTY);
        }
        String ciphers = "";
        String sslProvider = "";
        if (Objects.equals(sslProtocol, BaseConstant.GMSSL_PROTOCOL)) {
            ciphers = String.join(",", GmSslConstants.DEFAULT_GM_CIPHERS);
            sslProvider = GmSslConstants.DEFAULT_SSL_PROVIDER;
        }
        ManagementSslConfig managementSslConfig = ConfigHolder.get().get(ManagementSslConfig.class);
        if (managementSslConfig != null && !Objects.equals(managementSslConfig.getProtocol(), sslProtocol)) {
            managementSslConfig.setCiphers(ciphers);
            managementSslConfig.setProtocol(sslProtocol);
            ConfigHolder.get().save(managementSslConfig, false);
        }


        NettyHttpServerConfig nettyHttpServerConfig = ConfigHolder.get().get(NettyHttpServerConfig.class);
        if (nettyHttpServerConfig != null && !Objects.equals(nettyHttpServerConfig.getProtocol(), sslProtocol)) {
            nettyHttpServerConfig.setProtocol(sslProtocol);
            nettyHttpServerConfig.setSslCiphers(ciphers);
            nettyHttpServerConfig.setSslProvider(sslProvider);
            ConfigHolder.get().save(nettyHttpServerConfig, false);
        }

        NettyTcpServerConfig nettyTcpServerConfig = ConfigHolder.get().get(NettyTcpServerConfig.class);
        if (nettyTcpServerConfig != null && !Objects.equals(nettyTcpServerConfig.getProtocol(), sslProtocol)) {
            nettyTcpServerConfig.setProtocol(sslProtocol);
            nettyTcpServerConfig.setSslCiphers(ciphers);
            nettyTcpServerConfig.setSslProvider(sslProvider);
            ConfigHolder.get().save(nettyTcpServerConfig, false);
        }

    }

    private void saveCertInfo(Certificate signCert, KeyPair signKeyPair, CertRequestInfo certRequestInfo, boolean isInnerIssue) throws Exception {
        TrustCertEntity sslCertInDb = trustCertRepository.getCert(TrustCertType.SSL);
        if (Objects.nonNull(sslCertInDb) && StringUtils.isNotBlank(sslCertInDb.getCertValue())) {
            trustCertRepository.delete(sslCertInDb.getId());
            if (isInnerIssue) {
                // 内部签发则实时生成CRL
                String hexSn = sslCertInDb.getHexSn();
                MgrHolder.getManageCertMgr().asyncGenerateMgrRootCrl(new CertRevokedInfo(new BigInteger(hexSn, 16)));
            }
        }
        // 设置系统证书配置信息
        TrustCertEntity sslCertEntity = new TrustCertEntity(signCert, TrustCertType.SSL);
        // 设置注册信息
        ServerCertRequestInfo serverCertRequestInfo = certRequestInfo.toServerCertRequestInfo();
        sslCertEntity.setRegisterInfo(JsonUtils.toJson(serverCertRequestInfo));
        // 设置生成keyStore所需参数
        List<PrivateKey> privateKeyList = new ArrayList<>();
        List<Certificate> certList = new ArrayList<>();
        List<String> aliasList = new ArrayList<>();
        privateKeyList.add(signKeyPair.getPrivate());
        certList.add(signCert);
        aliasList.add(CertTypeEnum.SSL_SIGN_SERVER_CERT.name());
        sslCertEntity.save();
        saveKeyStore(privateKeyList, certList, aliasList, certRequestInfo);
    }

    protected void saveCertInfo(Certificate signCert, Certificate encCert, PrivateKey signPrivateKey,
                                PrivateKey encPrivateKey, CertRequestInfo certRequestInfo, boolean isInnerIssue) throws Exception {
        TrustCertEntity sslCertInDb = trustCertRepository.getCert(TrustCertType.SSL);
        if (Objects.nonNull(sslCertInDb) && StringUtils.isNotBlank(sslCertInDb.getCertValue())) {
            trustCertRepository.delete(sslCertInDb.getId());
            if (isInnerIssue) {
                // 内部签发则实时生成CRL
                String hexSn = sslCertInDb.getHexSn();
                MgrHolder.getManageCertMgr().asyncGenerateMgrRootCrl(new CertRevokedInfo(new BigInteger(hexSn, 16)));
            }
        }
        // 设置系统证书配置信息
        TrustCertEntity sslCertEntity = new TrustCertEntity(signCert, TrustCertType.SSL);
        // 设置注册信息
        ServerCertRequestInfo serverCertRequestInfo = certRequestInfo.toServerCertRequestInfo();
        sslCertEntity.setRegisterInfo(JsonUtils.toJson(serverCertRequestInfo));
        // 设置生成keyStore所需参数
        List<PrivateKey> privateKeyList = new ArrayList<>();
        List<Certificate> certList = new ArrayList<>();
        List<String> aliasList = new ArrayList<>();
        privateKeyList.add(signPrivateKey);
        certList.add(signCert);
        aliasList.add(CertTypeEnum.SSL_SIGN_SERVER_CERT.name());
        if (encCert != null) {
            sslCertEntity.setEncCertValue(Base64.encodeBase64String(encCert.getEncoded()));
            privateKeyList.add(encPrivateKey);
            certList.add(encCert);
        }
        aliasList.add(CertTypeEnum.SSL_ENC_SERVER_CERT.name());
        // 保存证书配置
        sslCertEntity.save();
        saveKeyStore(privateKeyList, certList, aliasList, certRequestInfo);
    }

    private void saveKeyStore(List<PrivateKey> privateKeyList, List<Certificate> certList, List<String> aliasList,
                              CertRequestInfo certRequestInfo) throws Exception {
        // 生成P12类型keyStore文件
        byte[] keyStoreBytes = KeyStoreUtil.genP12KeyStoreBytes(privateKeyList, certList, aliasList, certRequestInfo.getKeyStorePwd());
        // 保存keyStore文件
        String defaultKeyStoreFilePath = KeyStoreConstants.DEFAULT_KEY_STORE_FILE_RELATIVE_PATH;
        String keyStoreBase64 = Base64Util.base64Encode(keyStoreBytes);
        FileConfigEntity fileConfigEntity = new FileConfigEntity();
        fileConfigEntity.setFileName(KeyStoreConstants.DEFAULT_KEY_STORE_FILE_NAME);
        fileConfigEntity.setFilePath(defaultKeyStoreFilePath);
        fileConfigEntity.setFileContentBase64(keyStoreBase64);
        fileConfigRepository.saveAndRefreshFile(fileConfigEntity);
    }
}
