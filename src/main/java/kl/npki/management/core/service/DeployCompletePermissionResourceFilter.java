package kl.npki.management.core.service;

import kl.npki.management.core.biz.deploy.service.IDeployInitSpi;
import kl.npki.management.core.service.config.SystemConfigService;

/**
 * 用于部署完成时对权限资源进行初始化过滤
 * <p>
 * 该由于权限过滤变更了角色表记录，故其优先级必须高于{@link DeployCompleteFillSystemRoleDataFullHash}，否则会导致角色表完整性被破坏
 *
 * <AUTHOR>
 * @create 2024/6/13 上午11:25
 */
public class DeployCompletePermissionResourceFilter implements IDeployInitSpi {

    SystemConfigService systemConfigService = SystemConfigService.systemConfigService;

    @Override
    public void init() {
        systemConfigService.permissionResourceFilter();
    }

    @Override
    public int getOrder() {
        return 998;
    }
}