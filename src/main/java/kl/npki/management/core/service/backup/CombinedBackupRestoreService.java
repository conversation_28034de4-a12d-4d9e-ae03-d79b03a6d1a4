package kl.npki.management.core.service.backup;

import kl.npki.base.core.biz.export.AbstractTempFile;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.utils.DateUtil;
import kl.npki.base.core.utils.ZipUtil;
import kl.npki.management.core.constants.SysInitConstant;
import kl.npki.management.core.exception.ManagementInternalError;
import kl.npki.management.core.exception.ManagementValidationError;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.crypto.BadPaddingException;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

/**
 * 系统备份恢复业务组合类，触发所有备份恢复业务
 *
 * <AUTHOR>
 */
public class CombinedBackupRestoreService extends AbstractTempFile {

    private static final Logger log = LoggerFactory.getLogger(CombinedBackupRestoreService.class);

    private final List<IBackupRestoreService> backupRestoreServices;

    /**
     * 压缩文件的文件名模板
     */
    private static final String BACK_UP_FILE_TEMPLATE = "%s_%s_backup_%s%s";

    /**
     * 压缩文件的后缀
     */
    private static final String ZIP_SUFFIX = ".zip";


    /**
     * 允许上传的zip文件格式
     */
    private static final String FILE_ZIP = "application/zip";

    /**
     * 允许上传的压缩文件格式
     */
    private static final String FILE_ZIP_COMPRESSED = "application/x-zip-compressed";


    public CombinedBackupRestoreService(List<IBackupRestoreService> backupRestoreServices) {
        backupRestoreServices.sort(Comparator.comparingInt(IBackupRestoreService::getOrder));
        this.backupRestoreServices = backupRestoreServices;
    }

    /**
     * 执行系统备份
     *
     * @param password
     * @return
     */
    public synchronized File doSystemBackup(String password) {
        if (StringUtils.isBlank(password)) {
            throw ManagementValidationError.SYSTEM_BACKUP_PWD_CANNOT_BE_NULL.toException();
        }
        List<String> tobeZipFiles = new ArrayList<>();
        for (IBackupRestoreService backupRestoreService : backupRestoreServices) {
            tobeZipFiles.addAll(backupRestoreService.getToBeBackupFiles());
        }
        SysInfoConfig sysInfoConfig = BaseConfigWrapper.getSysInfoConfig();
        String version = sysInfoConfig.getVersion();
        String shortName = StringUtils.isBlank(sysInfoConfig.getShortName()) ? "file" : sysInfoConfig.getShortName();
        // 生成压缩备份文件
        try {
            String backupFileName = String.format(BACK_UP_FILE_TEMPLATE, version, shortName.toLowerCase(), DateUtil.formatFileDate(new Date()), ZIP_SUFFIX);
            backupFileName = getTempFile(backupFileName).getAbsolutePath();
            ZipUtil.compress(tobeZipFiles, backupFileName, password, true);
            return new File(backupFileName);
        } catch (Exception e) {
            throw ManagementInternalError.ENCRYPT_ZIP_FAIL.toException(e);
        }
    }

    /**
     * 执行系统恢复
     *
     * @param password   密码
     * @param backupFile 备份文件
     */
    public synchronized void doSystemRestore(String password, MultipartFile backupFile) {
        if (StringUtils.isBlank(password)) {
            throw ManagementValidationError.SYSTEM_BACKUP_PWD_CANNOT_BE_NULL.toException();
        }
        boolean result;

        try {
            // 判断是否为zip文件
            judgeZipFile(backupFile);

            SysInfoConfig sysInfoConfig = BaseConfigWrapper.getSysInfoConfig();
            String shortName = sysInfoConfig.getShortName();
            String destDir = unzipMultipartFileToTemp(backupFile, password);

            // 判断恢复文件跟当前系统匹配: 根据配置文件内容系统简称进行判断
            String applicationPath = Paths.get(destDir, SysInitConstant.CONFIG_FILE_DIR_NAME,
                SysInitConstant.APPLICATION_PROPERTIES_FILE).toString();

            Properties properties = loadPropertiesFromFile(applicationPath);
            String sysInfoShortName = properties.getProperty(SysInitConstant.SYS_INFO_SHORTNAME_KEY, StringUtils.EMPTY);

            if (!StringUtils.equalsIgnoreCase(shortName, sysInfoShortName)) {
                throw ManagementValidationError.SYSTEM_BACKUP_FILE_NOT_CURRENT_SYSTEM.toException();
            }

            // 记录执行过恢复的服务类
            List<IBackupRestoreService> restoreExecService = new ArrayList<>();

            result = true;
            for (IBackupRestoreService backupRestoreService : backupRestoreServices) {
                restoreExecService.add(backupRestoreService);
                if (!backupRestoreService.restore(destDir)) {
                    result = false;
                    break;
                }
            }

            // 进行恢复后置操作
            for (IBackupRestoreService backupRestoreService : restoreExecService) {
                if (result) {
                    backupRestoreService.successAfterRestore();
                } else {
                    backupRestoreService.failedAfterRestore();
                }
            }

            try {
                FileUtils.forceDelete(new File(destDir));
            } catch (IOException e) {
                log.warn("Clearing backup upload file cache directory failed", e);
            }
        } catch (Exception e) {
            throw ManagementInternalError.RESTORE_SYSTEM_FAILED.toException(e);
        }

        if (!result) {
            throw ManagementInternalError.RESTORE_SYSTEM_FAILED.toException();
        }
    }

    /**
     * 通过文件路径加载配置
     *
     * @param filePath 文件路径
     * @return Properties
     */
    public Properties loadPropertiesFromFile(String filePath) {
        Properties properties = new Properties();
        try (InputStream input = Files.newInputStream(Paths.get(filePath))) {
            // 加载属性列表
            properties.load(input);
            return properties;
        } catch (IOException e) {
            log.error("loadPropertiesFromFile error", e);
            throw ManagementInternalError.RESTORE_SYSTEM_FAILED.toException();
        }
    }

    private void judgeZipFile(MultipartFile file) {
        if (Objects.isNull(file)) {
            return;
        }
        String originalFilename = file.getOriginalFilename();
        if (!StringUtils.endsWith(originalFilename, ZIP_SUFFIX)) {
            throw ManagementValidationError.SYSTEM_BACKUP_FILE_NOT_ZIP.toException();
        }
    }

    private String unzipMultipartFileToTemp(MultipartFile backupFile, String password) {
        File tmpFile;
        String destDir;
        try {
            // 上传的文件保存至本地缓存目录
            tmpFile = getTempFile(backupFile.getOriginalFilename(), backupFile.getBytes());
            // 解压文件
            long currentedTimeMillis = System.currentTimeMillis();
            File tempDir = getTempDir(String.valueOf(currentedTimeMillis));
            destDir = tempDir.getAbsolutePath();
        } catch (IOException e) {
            throw ManagementInternalError.FILE_CREATE_FAIL.toException(e);
        }
        try {
            ZipUtil.uncompress(tmpFile.getAbsolutePath(), destDir, password);
            return destDir;
        } catch (Exception e) {
            log.error("Abnormal decompression of uploaded backup files", e);
            if (e.getCause() instanceof BadPaddingException) {
                throw ManagementInternalError.UN_ENCRYPT_ZIP_PWD_FAIL.toException(e);
            } else {
                throw ManagementInternalError.UN_ENCRYPT_ZIP_FAIL.toException(e);
            }
        } finally {
            try {
                Files.delete(tmpFile.toPath());
            } catch (IOException e) {
                log.warn("Failed to delete uploaded cache file", e);
            }
        }
    }

}
