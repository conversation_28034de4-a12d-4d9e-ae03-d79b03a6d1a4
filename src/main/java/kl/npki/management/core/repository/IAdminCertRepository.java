package kl.npki.management.core.repository;


import kl.npki.base.core.repository.BaseRepository;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/10 11:24
 * @desc
 */
public interface IAdminCertRepository extends BaseRepository {

    /**
     * 查询所有有效的管理员证书
     *
     * @return 管理员证书集合
     */
    List<AdminCertEntity> findValidAll();
    
    /**
     * 保存管理员证书信息
     *
     * @param adminCertEntity 管理员证书实体
     *
     * @return
     */
    Long save(AdminCertEntity adminCertEntity);
    
    /**
     * 更新管理员证书
     *
     * @param adminCertEntity 管理员证书实体
     */
    void update(AdminCertEntity adminCertEntity);
    
    /**
     * 删除管理员证书
     *
     * @param id 管理员证书id
     */
    void delete(Long id);
    
    /**
     * 通过管理员信息id获取管理员证书实体
     *
     * @param adminInfoId 管理员信息id
     *
     * @return
     */
    AdminCertEntity getByAdminInfoId(Long adminInfoId);

    /**
     * 通过证书sn获取管理员证书信息
     *
     * @param certSn 证书sn
     *
     * @return
     */
    AdminCertEntity getByCertSn(String certSn);

    /**
     * 是否存在同名的subjectCn
     * @param subjectCn
     * @return
     */
    boolean existSubjectCn(String subjectCn);

    /**
     * 获取所有签名证书的sn
     *
     * @return 签名证书sn集合
     */
    List<String> getSignCertSnList();
    
}
