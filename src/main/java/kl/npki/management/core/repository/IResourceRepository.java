package kl.npki.management.core.repository;

import kl.npki.base.core.repository.BaseRepository;
import kl.npki.management.core.biz.admin.model.entity.ResourceEntity;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/26
 * @desc
 */
public interface IResourceRepository extends BaseRepository {

    /**
     * 查询资源列表
     *
     * @return
     */
    List<ResourceEntity> queryForList();


    /**
     * 批量删除
     *
     * @param resourceEntityList 资源实体列表
     * @return boolean
     * @date 2024/10/11 11:22
     * <AUTHOR>
     */
    boolean batchDelete(Collection<ResourceEntity> resourceEntityList);

    /**
     * 更新资源集合状态
     *
     * @param resourceList 资源编码
     * @param status       状态值
     */
    void updateResourceStatus(List<String> resourceList, Integer status);

    /**
     * 获取允许匿名访问的URL列表
     *
     * @return 允许匿名访问的URL列表
     */
    List<String> getAllowList();
}
