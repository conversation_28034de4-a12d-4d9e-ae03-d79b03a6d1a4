package kl.npki.management.core.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import kl.npki.base.core.repository.BaseRepository;
import kl.npki.management.core.biz.admin.model.dto.AdminInfoListDTO;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import kl.npki.management.core.biz.admin.model.info.AdminInfoDetailInfo;
import kl.npki.management.core.biz.admin.model.info.AdminInfoListInfo;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/10/31 14:40
 * @desc
 */
public interface IAdminInfoMgrRepository extends BaseRepository {

    /**
     * 分页查询管理员信息
     *
     * @param page               分页对象
     * @param adminInfoListQuery 通用查询参数
     * @param roleCodes          当前用户的角色可查询的角色集合
     * @return
     */
    IPage<AdminInfoListInfo> queryForList(IPage<AdminInfoListInfo> page, AdminInfoListDTO adminInfoListQuery,
                                          Set<String> roleCodes);

    /**
     * 分页查询待审核管理员信息
     *
     * @param page                     分页对象
     * @param adminInfoListQuery       通用查询参数
     * @param roleCodes                当前用户的角色可查询的角色集合
     * @param toBeCheckedUserStatusSet 待审核用户状态集合
     * @param toBeCheckedCertStatusSet 待审核证书状态集合
     * @return
     */
    IPage<AdminInfoListInfo> queryForToBeCheckedList(IPage<AdminInfoListInfo> page, AdminInfoListDTO adminInfoListQuery,
                                          Set<String> roleCodes, Set<Integer> toBeCheckedUserStatusSet, Set<Integer> toBeCheckedCertStatusSet);

    AdminInfoDetailInfo queryAdminDetail(Long adminInfoId, Set<String> roleSet);

    /**
     * 根据id获取管理员信息
     *
     * @param adminInfoId     管理员id
     * @return
     */
    AdminEntity getById(Long adminInfoId);

    /**
     * 根据用户名 获取管理员信息
     *
     * @param username
     * @return
     */
    AdminEntity getByUsername(String username);
}
