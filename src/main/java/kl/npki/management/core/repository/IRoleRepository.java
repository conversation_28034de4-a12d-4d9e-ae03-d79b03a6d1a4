package kl.npki.management.core.repository;

import kl.npki.base.core.biz.security.model.UrlRoleInfo;
import kl.npki.base.core.repository.BaseRepository;
import kl.npki.management.core.biz.admin.model.entity.RoleEntity;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/8/26
 * @desc
 */
public interface IRoleRepository extends BaseRepository {
    
    /**
     * 获取所有的角色
     *
     * @return
     */
    List<RoleEntity> getAllRole();

    /**
     * 获取所有的系统角色
     *
     * @return
     */
    List<RoleEntity> getAllSystemRole();
    
    /**
     * 检查角色名是否重复
     *
     * @param roleName 角色名
     *
     * @return true 为重复 false 为未重复
     */
    Boolean checkRoleNameRepeat(String roleName);
    
    /**
     * 检查角色是否存在
     *
     * @param roleCode 角色编码
     *
     * @return true 存在 false 为不存在
     */
    Boolean checkRoleExists(String roleCode);
    
    /**
     * 保存角色
     *
     * @param roleEntity 角色信息
     *
     * @return
     */
    Boolean saveRole(RoleEntity roleEntity);
    
    /**
     * 通过id获取角色
     *
     * @param roleId
     *
     * @return
     */
    RoleEntity getRoleById(Long roleId);


    /**
     * 通过角色编码获取角色
     * @param roleCode
     * @return
     */
    RoleEntity getRoleByCode(String roleCode);

    String getRoleNameByCode(String roleCode);

    /**
     * 禁用角色
     * @param roleEntity
     */
    void forbidRole(RoleEntity roleEntity);
    
    
    /**
     * 根据角色id修改角色信息
     *
     * @param setUpdateBy
     */
    void updateRoleByCode(RoleEntity setUpdateBy);

    /**
     * 检查角色编码是否重复
     *
     * @param roleCode 角色编码
     *
     * @return true 为重复 false 为未重复
     */
    Boolean checkRoleCodeRepeat(String roleCode);


    /**
     * 根据角色编码获取统一资源权限
     *
     * @param roleCode 角色编码
     * @return 统一资源权限响应列表
     */
    List<String> getChosenResourceCodesByRoleCode(String roleCode);

    /**
     * 获取系统内置角色
     * @return
     */
    List<RoleEntity> getSystemRoleList();

    /**
     * 根据资源编码获取拥有该资源的角色
     */
    List<RoleEntity> getRoleByResourceCode(String resourceCode);

    /**
     * 根据父角色编码获取子角色
     *
     * @param parentRoleCode 父角色编码
     * @return 子角色列表
     */
    List<RoleEntity> getRoleByParentRoleCode(String parentRoleCode);

    Set<String> getQueryRoleCodeSet(Collection<String> roleCodes);

    /**
     * 获取可查询/可操作的角色列表<br>
     * 超级管理员只能查询签发业务管理员<br>
     * 业务管理员只能查询签发业务操作员、司法管理员<br>
     * 安全管理员可以审核业务管理员、业务操作员/司法管理员、审计员<br>
     * 审计管理员只能查询签发审计员<br>
     * 审计员可以审计日志<br>
     *
     * @param roleCodes 当前用户的角色列表
     * @param roleCodes 包含顶级管理员自己
     * @return 当前用户的角色可查询的角色列表
     */
    Set<String> getQueryRoleCodeSet(Collection<String> roleCodes, boolean includeOneself);


    List<UrlRoleInfo> listUrlRoles();

}
