package kl.npki.management.core.repository;

import kl.npki.base.core.repository.BaseRepository;
import kl.npki.management.core.biz.config.model.SystemConfigEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/26
 * @desc
 */
public interface ISystemConfigRepository extends BaseRepository {
    
    /**
     * 保存
     *
     * @param systemConfigEntity 系统配置实体
     */
    void save(SystemConfigEntity systemConfigEntity);
    
    /**
     * 更新
     *
     * @param systemConfigEntity 系统配置实体
     */
    void update(SystemConfigEntity systemConfigEntity);
    
    /**
     * 获取配置通过配置文件名字
     *
     * @param configFileName 配置文件名称
     *
     * @return {@link SystemConfigEntity}
     */
    SystemConfigEntity getConfigByConfigFileName(String configFileName);
    
    /**
     * 查询列表
     *
     * @param isComplete true为已完成配置的  false为未完成初始化的  null为查询所有
     *
     * @return {@link List}<{@link SystemConfigEntity}>
     */
    List<SystemConfigEntity> queryForList(Boolean isComplete);
    
    /**
     * 保存批量
     *
     *
     * @param configEntityList 配置实体列表
     */
    void saveBatch(List<SystemConfigEntity> configEntityList);
}
