package kl.npki.management.core.repository;

import kl.npki.base.core.repository.BaseRepository;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/10 11:24
 * @desc
 */
public interface IAdminInfoRepository extends BaseRepository {

    /**
     * 查询所有有效的管理员信息
     *
     * @return 管理员信息集合
     */
    List<AdminEntity> findValidAll();

    /**
     * 保存管理员信息
     *
     * @param adminEntity 管理员实体
     * @return
     */
    Long save(AdminEntity adminEntity);

    /**
     * 更新管理员信息
     *
     * @param adminEntity 管理员实体
     */
    void update(AdminEntity adminEntity);

    /**
     * 删除
     *
     * @param adminInfoId 管理员id
     */
    void delete(Long adminInfoId);

    /**
     * 通过id获取管理员实体
     *
     * @param adminInfoId
     * @return
     */
    AdminEntity getById(Long adminInfoId);

    /**
     * 检查角色是否被管理员使用
     *
     * @param roleCode 角色编码
     * @return true 为在使用 false 为未使用
     */
    Boolean checkRoleUse(String roleCode);

    /**
     * 检查是否存在同名的管理员
     *
     * @param username
     * @return
     */
    Boolean checkUsername(String username);

    /**
     * 统计该角色下是否有签发的管理员
     *
     * @param roleCode
     * @return
     */
    long countAdminByRoleCode(String roleCode);

    /**
     * 统计该角色下是否有签发的管理员
     *
     * @param role
     * @param adminGroup
     * @return
     */
    long countAdminByRoleAndGroup(String role,String adminGroup);
}
