package kl.npki.management.core.exception;

import kl.nbase.exception.interfaces.IInternalError;

import static kl.npki.management.core.constants.I18nConstant.BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * @Author: guoq
 * @Date: 2022/8/23
 * @description: 内部错误码
 */
public enum ManagementInternalError implements IInternalError, ErrorCode {
    SYSTEM_ERROR("000", "系统错误"),
    NO_SUCH_CONFIG_ERROR("001", "不存在对应配置错误"),
    REFRESH_DATA_SOURCE_ERROR("002", "刷新数据源错误"),
    SAVE_CONFIG_ERROR("003", "保存配置信息错误"),
    FILE_SAVE_ERROR("004", "刷新配置文件错误"),
    PROPERTY_NOT_EXISTS("005", "配置中不存在该属性"),
    FILE_LOAD_ERROR("006", "配置文件加载错误"),
    SQL_SCRIPT_RUN_ERROR("008", "数据库脚本执行失败"),
    CREATE_DATASOURCE_ERROR("009", "创建数据源失败"),
    SYSTEM_INIT_CONFIG_ERROR("010", "系统初始化配置失败"),
    NO_SUCH_KET_TYPE_EM_CONFIG("011", "不存在对应密钥类型的加密机配置"),
    NO_SUCH_ASYM_ALGO_EM_CONFIG("012", "不存在对应算法类型的加密机配置"),
    CACHE_PUT_ERROR("013", "设置缓存错误"),
    CACHE_GET_ERROR("014", "获取缓存错误"),

    // 管理员管理 101-200
    NOT_LOGIN("101", "未登录"),
    ADMIN_CERT_RETURN_FAIL("113", "管理员证书返回失败"),
    ADMIN_PERMISSION_FILTER_ERROR("114", "管理员权限过滤异常"),

    //    编解码异常
    ENCODE_ERROR("102", "编码异常"),
    DECODE_ERROR("103", "解码异常"),
    // 数据库异常
    DB_INSERT_ERROR("104", "数据新增异常"),
    DB_DELETE_ERROR("105", "数据删除异常"),
    DB_UPDATE_ERROR("106", "数据修改异常"),
    DB_QUERY_ERROR("107", "数据查询异常"),
    ROLE_USED("108", "角色在使用"),
    ISSUE_CERT_ERROR("109", "签发证书失败"),
    VERIFY_SIGNED_ERROR("110", "验证签名失败"),
    NOT_TRUST_CERT_ERROR("111", "证书不被信任"),
    TRUST_CERT_IS_EMPTY("112", "信任证书为空"),

    // 证书相关
    DN_BUILD_ERROR("200", "证书DN构造失败"),
    KEYSTORE_ALREADY_EXISTS("201", "Keystore文件已存在"),
    GENERATE_KEYSTORE_ERROR("202", "生成Keystore文件失败"),
    PKCS10_REQUEST_NOT_NULL("205", "p10请求不能为空"),
    PKCS10_REQUEST_DN_ERROR("206", "p10请求内容已篡改"),
    PKCS10_REQUEST_ALGO_ERROR("207", "p10请求算法与根证书算法不匹配"),

    // 数据库配置管理
    NOT_FOUND_DB_CONFIG("300", "数据库配置不存在"),
    DB_CONN_FAILED("301", "数据库连接失败"),

    // 加密机服务相关
    GET_ENGINE_SERVICE_FAIL("400", "获取加密机服务失败"),
    GET_KEY_PAIR_FAIL("401", "获取密钥对失败"),
    ENGINE_CONFIG_VALID_FAIL("402", "加密机配置验证失败"),
    ENGINE_CONFIG_IP_VALID_FAIL("403", "加密机无法连接，ip或端口配置错误"),
    //文件相关
    FILE_READ_FAIL("500", "文件读取失败"),
    LOG_PATH_NOT_EXIST("501", "日志路径不存在"),
    FILE_CREATE_FAIL("502", "文件创建失败"),
    FILE_DELETE_FAIL("503", "文件删除失败"),

    // 完整性相关
    CONFIG_FILE_DATA_FULL_INIT_FAIL("600", "配置文件完整性值初始化失败"),
    CONFIG_FILE_DATA_FULL_CHECK_FAIL("601", "配置文件被篡改"),

    // 备份恢复相关
    ENCRYPT_ZIP_FAIL("700", "加密压缩文件失败"),
    UN_ENCRYPT_ZIP_FAIL("701", "解压加密压缩文件失败"),
    RESTORE_SYSTEM_FAILED("702", "系统恢复失败"),
    UN_ENCRYPT_ZIP_PWD_FAIL("703", "解压密码不正确"),
    ;

    private final String code;
    private final String desc;

    ManagementInternalError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }


    @Override
    public String getDesc() {
        return desc;
    }
    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }

}
