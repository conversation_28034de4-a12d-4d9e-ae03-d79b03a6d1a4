package kl.npki.management.core.exception;

import kl.nbase.exception.interfaces.IValidationError;

import static kl.npki.management.core.constants.I18nConstant.BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * @Author: guoq
 * @Date: 2022/8/23
 * @description: 校验错误码 状态异常
 */
public enum ManagementValidationError implements IValidationError, ErrorCode {
    /**
     * 页面异常定义
     */
    SYSTEM_ERROR("000", "系统错误"),
    // 管理员管理 101-200
    PARAM_ERROR("101", "参数错误"),
    BIZ_STATUS_ERROR("102", "业务状态错误"),
    CERT_STATUS_ERROR("102", "证书状态错误"),
    ADMIN_NOT_EXISTS_ERROR("103", "管理员不存在"),
    ROLE_NOT_EXISTS("104", "角色不存在"),
    SYS_ROLE_NOT_REMOVE("105", "系统角色不能删除"),
    AUTH_REF_ID_ERROR("106", "认证ID不能为空"),
    DUPLICATION_USERNAME("107", "用户名重复"),
    ADMIN_PWD_ERROR("108", "管理员密码不正确"),
    ROLE_TYPE_ERROR("109", "角色类型错误"),
    NO_PERMISSION("110", "没有权限"),
    LOG_FILE_SIZE_CONFIG_ERROR("111", "日志文件容量配置有误"),
    AUDIT_LOG_CONFIG_ERROR("112", "请填写审计服务相关配置"),
    DUPLICATE_ADMIN_USERNAME("113", "管理员名称重复"),
    LOG_FILE_SIZE_PARSE_ERROR("114", "日志文件容量配置解析失败"),
    // 证书相关模块
    INVALID_SUBJECT_CN("301", "无效的SubjectCn"),

    INIT_ADMIN_TIMING_ERROR("303", "非部署阶段不容许初始化管理员!"),
    INIT_ADMIN_ROLE_ERROR("304", "部署阶段不能初始化该角色的管理员!"),
    INIT_ADMIN_LIMIT_ERROR("305", "该角色的管理员初始化数量不能超出限制!"),
    SSL_CLIENT_CERT_ALREADY_EXISTS("306", "SSL客户端证书已存在"),
    SSL_CLIENT_CERT_NOT_FOUND("307", "SSL客户端证书不存在"),
    BACKUP_ADMIN_TIMING_ERROR("313", "部署未完成不容许备份管理员!"),
    BACKUP_ADMIN_ROLE_ERROR("314", "不容许备份该角色的管理员!"),
    BACKUP_ADMIN_LIMIT_ERROR("315", "该角色的管理员备份数量不能超出限制!"),
    IMPORTED_CERT_NOT_SUPPORT_ERROR("316", "导入的证书不支持当前操作"),
    NOT_IMPORTED_ADMIN_ERROR("317", "当前管理员不是导入的管理员"),
    OLD_ADMIN_HAS_ENCRYPTED_CERT("318", "当前管理员已有加密证书"),
    REDIS_CONNECTION_ERROR("330", "REDIS连通性检测失败!"),
    REDIS_ADDRESS_EMPTY("331", "REDIS地址配置为空!"),
    CERT_REQ_NOT_BASE64("332", "证书请求数据不符合BASE64编码"),
    CERT_REQ_NOT_PKCS10("333", "证书请求数据不符合P10结构"),
    // 身份认证相关
    INVALID_AUTH_TYPE("400", "无效的认证类型"),
    INVALID_CERT_SN("401", "无效的证书序列号"),
    INVALID_COOKIE_INFO("402", "无效的cookie信息"),
    MISSING_OTHER_ADMIN_LOGIN_INFO("403", "缺少其它管理员登录信息"),
    ADMIN_CANNOT_REPEAT("404", "多选登录下，不允许使用重复的管理员进行登录!"),
    CAPTCHA_VERIFY_FAIL("405", "验证码错误"),


    /**
     * 系统备份恢复
     */
    SYSTEM_BACKUP_PWD_CANNOT_BE_NULL("500", "备份恢复密码不允许为空"),

    SYSTEM_BACKUP_FILE_NOT_ZIP("502", "当前文件不是zip格式"),

    SYSTEM_BACKUP_FILE_NOT_CURRENT_SYSTEM("503", "当前文件不适用于系统"),


    ;

    private final String code;
    private final String desc;

    ManagementValidationError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_MANAGEMENT_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }

}
