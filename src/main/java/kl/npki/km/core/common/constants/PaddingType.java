package kl.npki.km.core.common.constants;

/**
 * @Author: guoq
 * @Date: 2022/9/5
 * @description: 填充类型
 */
public enum PaddingType {
    /**
     * no_padding
     */
    NO_PADDING(0),
    /**
     * pkcs5 km默认使用填充方式
     */
    PKCS5_PADDING(1),
    /**
     * pkcs7 左填充，用于SSF33算法，仅用于上海CA
     */
    PKCS7_LEFT_PADDING(2);


    private final int id;

    PaddingType(int id) {
        this.id = id;
    }

    public static PaddingType valueOfById(Integer id) {

        PaddingType[] paddingTypes = PaddingType.values();
        for (PaddingType paddingType : paddingTypes) {
            if (id.equals(paddingType.getId())) {
                return paddingType;
            }
        }

        // 默认返回
        return NO_PADDING;
    }

    public int getId() {
        return id;
    }
}
