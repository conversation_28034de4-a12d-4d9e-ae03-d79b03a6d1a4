package kl.npki.km.core.common.constants;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.km.core.common.constants.KMCoreI18N.KM_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * @Author: guoq
 * @Date: 2022/9/23
 * @description:
 */
public enum KeyPosition implements EnumI18n {
    /**
     * 密钥位置
     */
    POS_CURRENT(0, "当前库"),
    POS_HISTORY(1, "历史库"),
    POS_POOL(2, "备用库");

    private final int id;
    private final String desc;

    KeyPosition(int id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    public static KeyPosition valueOfById(int id) {

        KeyPosition[] keyPositions = KeyPosition.values();
        for (KeyPosition keyPosition : keyPositions) {
            if (id == (keyPosition.getId())) {
                return keyPosition;
            }
        }

        // 默认返回
        return POS_CURRENT;
    }

    public int getId() {
        return id;
    }

    public String getDesc() {
        return tr();
    }

    @Override
    public String getMessageKey() {
        return KM_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }

}
