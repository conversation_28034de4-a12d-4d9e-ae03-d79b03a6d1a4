package kl.npki.km.core.common.constants;

import kl.nbase.security.asn1.ASN1ObjectIdentifier;

/**
 * @Author: guoq
 * @Date: 2022/9/23
 * @description: ca常量
 */
public class CaConstants {

    /**
     * 默认分配数量
     */
    public static final int DEFAULT_LIMIT_NUM = 1000;
    public static final int DEFAULT_WARNING_NUM = 100;

    /**
     * 默认递增数量
     */
    public static final int DEFAULT_INCREASE_NUM = 1;

    /**
     * 不限制密钥数量
     */
    public static final int UNLIMITED_NUMBER = -1;

    /**
     * 未授权限制数量
     */
    public static final int UNAUTHORIZED_NUM = 0;
    /**
     * 未使用数量
     */
    public static final int UNUSED_NUM = 0;

    /**
     * 默认返还数量
     */
    public static final int DEFAULT_GIVEBACK_NUM = -1;

    public static final String GBK_CHARSET = "GBK";

    /**
     * 老CA RSA密钥时会传该算法，该oid无法对应实际的对称算法，默认不加密
     */
    public static final String ID_CN_GMJ_ALGO_FZ = "1.2.156.10197.1.100";

    public static final ASN1ObjectIdentifier ID_CN_GMJ_ALGO_FZ_OID = new ASN1ObjectIdentifier(CaConstants.ID_CN_GMJ_ALGO_FZ);

}
