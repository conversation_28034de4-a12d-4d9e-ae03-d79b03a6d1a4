package kl.npki.km.core.common.constants;

/**
 * <AUTHOR>
 * @date 2022/11/24 14:48
 * @Description: Km 常量
 */
public class KmConstants {

    /**
     * 申请密钥起始有效期最多可以比现在早5个小时，多于5个小时统一设置为当前时间
     */
    public static final int FIVE_HOURS = 5;

    /**
     * 默认分页阈值
     */
    public static final int PAGE_THRESHOLD = 200;

    /**
     * sks自检密钥介质id标识
     */
    public static final String SELF_CHECKING_MEDIUM_ID = "selfCheck";

    /**
     * km config prefix
     */
    public static final String CONFIG_MAIN_KEY_PREFIX = "kl.km.mainKey";
    public static final String CONFIG_KM_PREFIX = "kl.km.conf";
    public static final String CONFIG_KM_FEATURE_PREFIX = "kl.km.feature";
    public static final String CONFIG_KEY_PREFIX = "kl.km.key";
    public static final String CONFIG_ARCHIVE_PREFIX = "kl.km.archive";
    public static final String CONFIG_HOMEPAGE_PREFIX = "kl.km.homePage";
    public static final String CONFIG_API_LOG_STATISTIC_PREFIX = "kl.km.statistic";
    public static final String KEY_GEN_LOCK_PREFIX = "km:keyGen";

    public static final Integer KM_SERVICE_ERROR = 999;

    /**
     * 默认序列号进制
     */
    public static final int SERIALNUMBER_DECIMAL = 16;
    /**
     * 空的对称密钥, 用于不加密时占位
     */
    public static final byte[] EMPTY_SYMMETRIC_KEY = "  ".getBytes();

}
