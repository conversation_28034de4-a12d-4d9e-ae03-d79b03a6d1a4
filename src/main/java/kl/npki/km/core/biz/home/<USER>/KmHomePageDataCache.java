package kl.npki.km.core.biz.home.service;

import kl.nbase.cache.client.ICacheClient;
import kl.npki.base.core.common.ICommonCache;
import kl.npki.base.core.tenantholders.CacheClientHolder;
import kl.npki.km.core.biz.home.model.CaKeyStatisticInfo;
import kl.npki.km.core.biz.home.model.KeyStashDistributionInfo;
import kl.npki.km.core.biz.home.model.KeyStatusStatisticInfo;
import kl.npki.km.core.biz.stat.model.CaBizStatisticInfo;
import kl.npki.km.core.common.FixedSizeCircularQueue;

/**
 * 用于保存首页数据缓存
 *
 * <AUTHOR>
 */
public enum KmHomePageDataCache implements ICommonCache {

    /**
     * 单例对象
     */
    INSTANCE;

    private static final String CACHE_ID = "km:homePageData";

    public static final String KEY_STATUS_STATISTIC_CACHE_KEY = "key_status_statistic_cache_key";

    public static final String KEY_STASH_DISTRIBUTION_STATISTIC_CACHE_KEY = "key_stash_distribution_statistic_cache_key";

    public static final String CA_KEY_STATISTIC_CACHE_KEY = "ca_key_statistic_cache_key_%s";

    public static final String CA_BIZ_STATISTIC_CACHE_KEY = "ca_biz_statistic_cache_key_%s";


    /**
     * 保存密钥状态统计缓存
     *
     * @param keyStatusStatisticInfo
     */
    public void putKeyStatusStatisticCache(KeyStatusStatisticInfo keyStatusStatisticInfo) {
        ICacheClient client = CacheClientHolder.get();
        client.set(wrapCacheKey(KEY_STATUS_STATISTIC_CACHE_KEY), keyStatusStatisticInfo);
    }

    /**
     * 保存密钥库统计缓存
     *
     * @param keyStashDistributionInfo
     */
    public void putKeyStashDistributionCache(KeyStashDistributionInfo keyStashDistributionInfo) {
        ICacheClient client = CacheClientHolder.get();
        client.set(wrapCacheKey(KEY_STASH_DISTRIBUTION_STATISTIC_CACHE_KEY), keyStashDistributionInfo);
    }

    /**
     * 保存 CA密钥统计缓存
     *
     * @param caName             ca名称
     * @param caKeyStatisticInfo
     */
    public void putCaKeyStatisticCache(String caName, CaKeyStatisticInfo caKeyStatisticInfo) {
        ICacheClient client = CacheClientHolder.get();
        String cacheKey = String.format(CA_KEY_STATISTIC_CACHE_KEY, caName);
        client.set(wrapCacheKey(cacheKey), caKeyStatisticInfo);
    }

    /**
     * 保存 ca密钥服务调用情况缓存
     *
     * @param caName
     * @param monthStatisticInfo
     */
    public void putCaBizStatisticCache(String caName, FixedSizeCircularQueue<CaBizStatisticInfo> monthStatisticInfo) {
        ICacheClient client = CacheClientHolder.get();
        String cacheKey = String.format(CA_BIZ_STATISTIC_CACHE_KEY, caName);
        client.set(wrapCacheKey(cacheKey), monthStatisticInfo);
    }

    public KeyStatusStatisticInfo getKeyStatusStatisticCache() {
        ICacheClient client = CacheClientHolder.get();
        return client.get(wrapCacheKey(KEY_STATUS_STATISTIC_CACHE_KEY));
    }

    public KeyStashDistributionInfo getKeyStashDistributionCache() {
        ICacheClient client = CacheClientHolder.get();
        return client.get(wrapCacheKey(KEY_STASH_DISTRIBUTION_STATISTIC_CACHE_KEY));
    }

    public CaKeyStatisticInfo getCaKeyStatisticInfo(String caName) {
        ICacheClient client = CacheClientHolder.get();
        String cacheKey = String.format(CA_KEY_STATISTIC_CACHE_KEY, caName);
        return client.get(wrapCacheKey(cacheKey));
    }

    public FixedSizeCircularQueue<CaBizStatisticInfo> getCaBizStatisticCache(String caName) {
        ICacheClient client = CacheClientHolder.get();
        String cacheKey = String.format(CA_BIZ_STATISTIC_CACHE_KEY, caName);
        return client.get(wrapCacheKey(cacheKey));
    }

    @Override
    public String getId() {
        return CACHE_ID;
    }

}