package kl.npki.km.core.biz.home.model;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.km.core.biz.stat.model.KeyCountInfo;

import java.io.Serializable;
import java.util.List;

/**
 * 用于首页的CA密钥使用情况数据展示
 * <AUTHOR>
 */
public class CaKeyStatisticInfo implements Serializable {

    @Schema(description =  "密钥库数量统计-在用密钥数")
    private Integer currentKeyCount;

    @Schema(description =  "密钥库数量统计-历史库密钥数")
    private Integer hisKeyCount;

    @Schema(description =  "在用密钥类型的统计")
    private List<KeyCountInfo> currentKeyCountInfos;

    public CaKeyStatisticInfo(Integer currentKeyCount, Integer hisKeyCount, List<KeyCountInfo> currentKeyCountInfos) {
        this.currentKeyCount = currentKeyCount;
        this.hisKeyCount = hisKeyCount;
        this.currentKeyCountInfos = currentKeyCountInfos;
    }

    public Integer getCurrentKeyCount() {
        return currentKeyCount;
    }

    public CaKeyStatisticInfo setCurrentKeyCount(Integer currentKeyCount) {
        this.currentKeyCount = currentKeyCount;
        return this;
    }

    public Integer getHisKeyCount() {
        return hisKeyCount;
    }

    public CaKeyStatisticInfo setHisKeyCount(Integer hisKeyCount) {
        this.hisKeyCount = hisKeyCount;
        return this;
    }

    public List<KeyCountInfo> getCurrentKeyCountInfos() {
        return currentKeyCountInfos;
    }

    public CaKeyStatisticInfo setCurrentKeyCountInfos(List<KeyCountInfo> currentKeyCountInfos) {
        this.currentKeyCountInfos = currentKeyCountInfos;
        return this;
    }
}
