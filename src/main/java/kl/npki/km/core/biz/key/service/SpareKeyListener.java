package kl.npki.km.core.biz.key.service;

import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.thread.BaseThreadPool;
import kl.npki.km.core.biz.key.model.SpareKeyEntity;
import kl.npki.km.core.common.constants.KmConstants;
import kl.npki.km.core.configs.KeyLimitConfig;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.core.repository.IKeyPoolRepository;
import kl.npki.km.core.service.gen.IKeyGenService;
import kl.npki.km.core.service.gen.impl.KeyGenServiceImpl;
import kl.npki.km.core.utils.PageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @Author: guoq
 * @Date: 2023/6/18
 * @description: 备用密钥监听者，密钥不够，及时补充密钥
 */
public class SpareKeyListener {

    private static final Logger log = LoggerFactory.getLogger(SpareKeyListener.class);

    /**
     * 单例对象
     */
    private static final SpareKeyListener INSTANCE = new SpareKeyListener();

    private static final Map<String, Future<?>> THREAD_RESULT = new ConcurrentHashMap<>(8);
    /**
     * 密钥存储仓库
     */
    private final IKeyPoolRepository keyPoolRepository;
    private final IKeyGenService keyGenService;
    private final SpareKeyPool spareKeyPool;


    private SpareKeyListener() {
        this.keyPoolRepository = RepositoryFactory.get(IKeyPoolRepository.class);
        this.keyGenService = new KeyGenServiceImpl();
        this.spareKeyPool = SpareKeyPool.getInstance();
    }

    public static SpareKeyListener getInstance() {
        return INSTANCE;
    }

    /**
     * 密钥缓存容量检查补充，由外部定时任务调用
     * 例如：km-service定时任务调用
     */
    public void cacheThresholdListen() {
        List<KeyLimitConfig> keyLimits = KmConfigWrapper.getKeyConf().getKeyLimits();
        keyLimits.forEach(keyLimitConf -> {
            if (!keyLimitConf.isEnable()) {
                return;
            }
            try {
                AsymAlgo asymAlgo = new AsymAlgo(keyLimitConf.getKeyType());
                String algoName = asymAlgo.getAlgoName();
                int cacheCount = spareKeyPool.getCacheCount(asymAlgo);

                // 判断缓存池中密钥对是否够用，判断依据为密钥池中的数量是否低于计算出的阈值
                if (cacheCount >= getActualThreshold()) {
                    log.debug("缓存的【{}】密钥数量充足，目前剩余量【{}】，无需补充。", algoName, cacheCount);
                    return;
                }
                // 判断线程池是否正在执行该密钥补充任务
                Future<?> future = THREAD_RESULT.get(algoName);
                boolean running = ObjectUtils.isNotEmpty(future) && !future.isDone();
                if (running) {
                    log.debug("【{}】密钥正在补充,当前已经补充数量【{}】,不再启用新任务", algoName, cacheCount);
                    return;
                }
                // 补充量
                int padSize = KmConfigWrapper.getKeyConf().getCacheSize() - cacheCount;
                log.info("缓存的【{}】密钥数量不足，目前剩余量【{}】,开始补充【{}】对密钥。", algoName, cacheCount, padSize);
                future =
                    BaseThreadPool.INSTANCE.getTaskExecutor().submit(() -> supplyKey(asymAlgo, padSize));
                THREAD_RESULT.put(algoName, future);
            } catch (Exception e) {
                log.error("检查【{}】密钥缓存数失败！", keyLimitConf.getKeyType(), e);
            }
        });
    }


    public void supplyKey(AsymAlgo asymAlgo, int padSize) {
        if (log.isDebugEnabled()) {
            log.debug("正在执行【{}】密钥缓存添加，密钥线程为：{}", asymAlgo.getAlgoName(), Thread.currentThread());
        }
        try {
            // 判断备用库是否足够，不够触发密钥生成
            Integer surplusSize = keyPoolRepository.count(asymAlgo.getAlgoName());
            if (padSize > surplusSize) {
                log.info("备用库【{}】密钥数量不够，开始执行密钥生成，线程为：{}", asymAlgo.getAlgoName(), Thread.currentThread());
                //备用密钥库密钥不够，需要触发密钥生成
                keyGenService.doKeyGenJobByKeyType(asymAlgo);
            }

            // 获取分页次数
            int loopCount = PageUtil.getPageCount(padSize, KmConstants.PAGE_THRESHOLD);
            for (int pageindex = 0; pageindex < loopCount; pageindex++) {
                final int start = pageindex * KmConstants.PAGE_THRESHOLD;
                final int end = Math.min(start + KmConstants.PAGE_THRESHOLD, padSize);
                if (exportKeyEntityToCache(asymAlgo, end - start)) {
                    return;
                }
            }
        } catch (Exception e) {
            log.error("缓存备用密钥失败！", e);
        }
    }

    public boolean exportKeyEntityToCache(AsymAlgo asymAlgo, int batchSize) {
        List<SpareKeyEntity> entityList = keyPoolRepository.searchKeyEntityList(batchSize, asymAlgo.getAlgoName());

        // 未找到备用密钥直接退出
        if (CollectionUtils.isEmpty(entityList)) {
            return true;
        }
        // 直接进行批量删除，删除成功，后再放入缓存，避免其他集群加载到相同密钥
        List<Long> idsToDelete = entityList.stream().map(SpareKeyEntity::getId).collect(Collectors.toList());
        try {
            boolean isBatchDeleteSuccess = keyPoolRepository.deleteUsedKeys(idsToDelete);
            if (!isBatchDeleteSuccess) {
                log.error("Batch deletion of key entities failed, affected IDs={}", idsToDelete);
                return false;
            }
        } catch (Exception e) {
            log.error("Error occurred during batch deletion of key entities, affected IDs={}", idsToDelete, e);
            return false;
        }

        entityList.forEach(keyEntity -> {
            // 验证密钥完整性未通过，直接跳出本次循环
            if (removeTamperedKey(keyEntity)) {
                return;
            }
            try {
                // 解密私钥
                keyEntity.decryptPriKey();
                spareKeyPool.putKeyEntity(keyEntity);
            } catch (Exception e) {
                log.error("exported key into the cache error", e);
            }

        });

        return false;
    }

    /**
     * 移除被篡改的密钥
     *
     * @param keyEntity
     * @return
     */
    public boolean removeTamperedKey(SpareKeyEntity keyEntity) {
        try {
            boolean result = keyPoolRepository.checkDataIntegrity(keyEntity);
            if (!result) {
                log.error("验证备用密钥[{}]完整性失败，密钥被篡改", keyEntity.getKeyIndex());
                return true;
            }
        } catch (Exception e) {
            log.error("验证备用密钥[{}]完整性失败", keyEntity.getKeyIndex(), e);
            return true;
        }
        return false;
    }

    /**
     * 计算实际阈值
     *
     * @return 实际阈值数量
     */
    private int getActualThreshold() {
        double thresholdPercentage = KmConfigWrapper.getKeyConf().getCacheSupplyThreshold();
        int cacheSize = KmConfigWrapper.getKeyConf().getCacheSize();
        // 确保百分比在有效范围内
        double validPercentage = Math.max(0.0, Math.min(1.0, thresholdPercentage));
        return (int) Math.ceil(cacheSize * validPercentage);
    }
}
