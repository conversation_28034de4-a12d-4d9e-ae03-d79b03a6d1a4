package kl.npki.km.core.biz.stat.model;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.biz.log.model.ApiLogCountServiceResult;
import kl.npki.km.core.biz.statistic.SksStatisticResultEntity;
import org.apache.commons.lang3.ObjectUtils;

import java.util.*;

/**
 * SKS业务统计信息
 *
 * <AUTHOR>
 */
public class SksBizStatisticInfo {
    @Schema(description = "业务名称")
    @ExcelProperty("业务名称")
    private String bizName;

    @Schema(description = "成功次数")
    @ExcelProperty("成功次数")
    private int successCount;

    @Schema(description = "失败次数")
    @ExcelProperty("失败次数")
    private int failCount;

    /**
     * 将统计结果合并
     *
     * @param hisData
     * @param todayData
     */
    public static List<SksBizStatisticInfo> plusStatisticInfo(List<SksBizStatisticInfo> hisData, List<SksBizStatisticInfo> todayData) {
        Map<String, SksBizStatisticInfo> map = new HashMap<>();
        for (SksBizStatisticInfo info : hisData) {
            map.put(info.getBizName(), info);
        }

        for (SksBizStatisticInfo info : todayData) {
            SksBizStatisticInfo his = map.get(info.getBizName());
            if (ObjectUtils.isNotEmpty(his)) {
                his.setFailCount(info.getFailCount() + his.getFailCount());
                his.setSuccessCount(info.getSuccessCount() + his.getSuccessCount());
            } else {
                // 说明该业务当日才开始有数据
                hisData.add(info);
            }
        }

        return hisData;
    }

    public static List<SksBizStatisticInfo> convertEntity2InfoList(List<SksStatisticResultEntity> entities) {
        List<SksBizStatisticInfo> infos = new ArrayList<>(entities.size());
        for (SksStatisticResultEntity entity : entities) {
            SksBizStatisticInfo info = new SksBizStatisticInfo();
            info.setBizName(entity.getBizName());
            info.setSuccessCount(entity.getSuccessTimes());
            info.setFailCount(entity.getFailTimes());

            infos.add(info);
        }

        return infos;
    }

    /**
     * 转化结果
     *
     * @param apiLogCountServiceResults
     * @return
     */
    public static List<SksBizStatisticInfo> convert2SksBizStatisticInfo(List<ApiLogCountServiceResult> apiLogCountServiceResults) {

        // sks业务名称对应的集合
        Map<String, SksBizStatisticInfo> map = new HashMap<>();
        for (ApiLogCountServiceResult result : apiLogCountServiceResults) {
            // SKS业务名称
            String bizName = result.getBiz();

            // 存在对应的结果则取出，不存在则创建
            SksBizStatisticInfo sksBizStatisticInfo = map.computeIfAbsent(bizName, key -> {
                SksBizStatisticInfo tmpSksBizStatisticInfo = new SksBizStatisticInfo();
                tmpSksBizStatisticInfo.setBizName(key);
                return tmpSksBizStatisticInfo;
            });

            // 业务执行次数
            int count = result.getCount();

            if (result.isResult()) {
                sksBizStatisticInfo.setSuccessCount(count);
            } else {
                sksBizStatisticInfo.setFailCount(count);
            }
        }

        return Arrays.asList(map.values().toArray(new SksBizStatisticInfo[]{}));
    }

    public String getBizName() {
        return bizName;
    }

    public SksBizStatisticInfo setBizName(String bizName) {
        this.bizName = bizName;
        return this;
    }

    public int getSuccessCount() {
        return successCount;
    }

    public SksBizStatisticInfo setSuccessCount(int successCount) {
        this.successCount = successCount;
        return this;
    }

    public int getFailCount() {
        return failCount;
    }

    public SksBizStatisticInfo setFailCount(int failCount) {
        this.failCount = failCount;
        return this;
    }
}
