package kl.npki.km.core.biz.key.service;

import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.npki.km.core.biz.key.model.KeyEntity;
import kl.npki.km.core.biz.key.model.SpareKeyEntity;
import kl.npki.km.core.biz.stat.model.KeyCountInfo;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.core.exception.KmInternalError;
import kl.npki.km.core.repository.IKeyCurrentRepository;
import kl.npki.km.core.repository.IKeyHistoryRepository;
import kl.npki.km.core.repository.IKeyPoolRepository;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.KeyPair;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @Author: guoq
 * @Date: 2022/8/23
 * @description: 备用密钥池
 */
public class SpareKeyPool {
    private static final Logger log = LoggerFactory.getLogger(SpareKeyPool.class);
    /**
     * 单例对象
     */
    private static final SpareKeyPool INSTANCE = new SpareKeyPool();
    /**
     * 密钥存储仓库
     */
    private final IKeyPoolRepository keyPoolRepository;
    private final IKeyHistoryRepository keyHistoryRepository;
    private final IKeyCurrentRepository keyCurrentRepository;
    private final KeyPoolCache keyPoolCache;


    private SpareKeyPool() {
        this.keyPoolRepository = RepositoryFactory.get(IKeyPoolRepository.class);
        this.keyHistoryRepository = RepositoryFactory.get(IKeyHistoryRepository.class);
        this.keyCurrentRepository = RepositoryFactory.get(IKeyCurrentRepository.class);
        this.keyPoolCache = KeyPoolCache.INSTANCE;
    }

    public static SpareKeyPool getInstance() {
        return INSTANCE;
    }

    /**
     * 获取密钥库容量
     *
     * @param keyType 密钥类型
     * @return 密钥池容量
     * @throws
     */
    public int getRepositoryCount(AsymAlgo keyType) {
        return keyPoolRepository.count(keyType.getAlgoName());
    }

    public int getCacheCount(AsymAlgo keyType) {
        return keyPoolCache.size(keyType);
    }

    /**
     * 获取密钥池容量（包括缓存中的密钥对）
     *
     * @return 密钥池容量
     * @throws
     */
    public List<KeyCountInfo> getTotalCount() {
        List<String> supportAsymAlgo = KmConfigWrapper.getAsymmetricAndPostQuantumKeyTypesWithoutSM9List();
        return supportAsymAlgo.stream()
                .map(keyType -> new KeyCountInfo(keyType, getTotalCount(new AsymAlgo(keyType))))
                .collect(Collectors.toList());
    }

    /**
     * 获取密钥池容量（包括缓存中的密钥对）
     *
     * @param keyType 密钥类型
     * @return 密钥池容量
     */
    public int getTotalCount(AsymAlgo keyType) {
        return getRepositoryCount(keyType) + getCacheCount(keyType);
    }

    /**
     * 注入密钥对（由GenerateKeyThread完成）
     *
     * @param keyPair 密钥对记录
     */
    public boolean importKeyPair(AsymAlgo keyType, KeyPair keyPair) {
        SpareKeyEntity keyEntity = new SpareKeyEntity(keyType.getAlgoName(), keyPair);
        //1. 加密用户私钥
        keyEntity.encryptPriKey();

        //2. 检查密钥是否重复
        if (checkKeyPairExist(keyEntity)) {
            log.warn("当前密钥生成重复,公钥摘要数据：【{}】", keyEntity.getKeyIndex());
            return false;
        }

        //3. 如果内存中的密钥池不足数量，应该可以考虑优先放到内存中
        if (getCacheCount(keyType) < KmConfigWrapper.getKeyConf().getCacheSize()) {
            keyPoolCache.put(keyEntity);
            return true;
        }

        //4. 保存备用密钥实体
        try {
            keyEntity.save();
            return true;
        } catch (Exception se) {
            throw KmInternalError.SPARE_KEY_SAVE_ERROR.toException(se);
        }
    }

    /**
     * 检查密钥是否已经存在，在各个仓库中
     *
     * @param keyEntity
     * @return true 已存在 false 不存在
     */
    public boolean checkKeyPairExist(KeyEntity keyEntity) {
        //判断是否检查密钥唯一性
        if (!KmConfigWrapper.getKeyConf().isKeyUniquenessCheck()) {
            return false;
        }
        String keyIndex = keyEntity.getKeyIndex();
        if (keyPoolRepository.isExist(keyIndex)) {
            return true;
        }
        if (keyCurrentRepository.isExist(keyIndex)) {
            return true;
        }
        return keyHistoryRepository.isExist(keyIndex);
    }

    /**
     * 获取对应算法密钥对
     *
     * @param keyType
     * @return
     */
    public SpareKeyEntity getKeyEntity(AsymAlgo keyType) {
        SpareKeyEntity spareKeyEntity = keyPoolCache.get(keyType);
        if (ObjectUtils.isNotEmpty(spareKeyEntity)) {
            return spareKeyEntity;
        }
        if (log.isDebugEnabled()) {
            log.debug("缓存中的【{}】算法的密钥数据不存在", keyType);
        }
        // 直接从密码机获取
        KeyPair keyPair;
        try {
            keyPair = EngineHolder.get().genKeyPair(keyType);
        } catch (Exception e) {
            throw KmInternalError.ENGINE_GEN_KEYPAIR_ERROR.toException(e);
        }
        spareKeyEntity = new SpareKeyEntity(keyType.getAlgoName(), keyPair);
        // 加密用户私钥
        spareKeyEntity.encryptPriKey();

        return spareKeyEntity;
    }

    public void putKeyEntity(SpareKeyEntity keyEntity) {
        keyPoolCache.put(keyEntity);
    }

}
