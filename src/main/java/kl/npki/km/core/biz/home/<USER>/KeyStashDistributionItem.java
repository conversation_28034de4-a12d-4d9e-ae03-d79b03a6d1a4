package kl.npki.km.core.biz.home.model;

import java.io.Serializable;

/**
 * 密钥库分布统计
 * <AUTHOR>
 */
public class KeyStashDistributionItem implements Serializable {
    /**
     * 密钥类型
     */
    private String keyType;

    /**
     * 该密钥类型在当前库中的数量
     */
    private int currentKeyCount;

    /**
     * 该密钥类型在历史库中的数量
     */
    private int historyKeyCount;

    /**
     * 该密钥类型在备用库中的数量
     */
    private int poolKeyCount;

    public String getKeyType() {
        return keyType;
    }

    public KeyStashDistributionItem setKeyType(String keyType) {
        this.keyType = keyType;
        return this;
    }

    public int getCurrentKeyCount() {
        return currentKeyCount;
    }

    public KeyStashDistributionItem setCurrentKeyCount(int currentKeyCount) {
        this.currentKeyCount = currentKeyCount;
        return this;
    }

    public int getHistoryKeyCount() {
        return historyKeyCount;
    }

    public KeyStashDistributionItem setHistoryKeyCount(int historyKeyCount) {
        this.historyKeyCount = historyKeyCount;
        return this;
    }

    public int getPoolKeyCount() {
        return poolKeyCount;
    }

    public KeyStashDistributionItem setPoolKeyCount(int poolKeyCount) {
        this.poolKeyCount = poolKeyCount;
        return this;
    }
}
