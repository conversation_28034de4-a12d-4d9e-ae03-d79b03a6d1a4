package kl.npki.km.core.biz.stat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 年月信息
 * <AUTHOR>
 */
public class YearAndMonthInfo implements Serializable {

    @Schema(description =  "年份")
    private int year;

    @Schema(description =  "月份")
    private int month;

    public YearAndMonthInfo(LocalDateTime date) {
        this.year = date.getYear();
        this.month = date.getMonthValue();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (!(o instanceof YearAndMonthInfo)) {
            return false;
        }

        YearAndMonthInfo that = (YearAndMonthInfo) o;

        return new EqualsBuilder()
            .append(getYear(), that.getYear())
            .append(getMonth(), that.getMonth())
            .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
            .append(getYear())
            .append(getMonth())
            .toHashCode();
    }

    public int getYear() {
        return year;
    }

    public YearAndMonthInfo setYear(int year) {
        this.year = year;
        return this;
    }

    public int getMonth() {
        return month;
    }

    public YearAndMonthInfo setMonth(int month) {
        this.month = month;
        return this;
    }

    public String getStr() {
        return String.format("%d.%02d", year, month);
    }
}
