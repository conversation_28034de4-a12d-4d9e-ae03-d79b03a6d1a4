package kl.npki.km.core.biz.key.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import kl.npki.km.core.common.RepositoryHelper;
import org.apache.commons.lang3.ObjectUtils;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author: guoq
 * @Date: 2022/9/15
 * @description:
 */
public class HistoryKeyEntity extends CertKeyEntity {

    private static final long serialVersionUID = 2641534437851591353L;
    /**
     * 归档原因
     */
    private String archiveReason;

    /**
     * 记录密钥分发时间（即当时在用密钥的创建时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime escrowTime;


    public String getArchiveReason() {
        return archiveReason;
    }

    public void setArchiveReason(String archiveReason) {
        this.archiveReason = archiveReason;
    }

    public LocalDateTime getEscrowTime() {
        return escrowTime;
    }

    public void setEscrowTime(LocalDateTime escrowTime) {
        this.escrowTime = escrowTime;
    }

    public Long save() {
        if (!ObjectUtils.isEmpty(getId())) {
            // 保证密钥记录ID 唯一
            setId(null);
        }
        return RepositoryHelper.getKeyHistoryRepository().insertKey(this);
    }

    public boolean delete() {
        return RepositoryHelper.getKeyHistoryRepository().deleteKey(getId(), getEncCertSn());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        HistoryKeyEntity that = (HistoryKeyEntity) o;
        return Objects.equals(archiveReason, that.archiveReason) &&
            Objects.equals(escrowTime, that.escrowTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), archiveReason, escrowTime);
    }

    @Override
    public String toString() {
        return "HistoryKeyEntity{" +
            "archiveReason='" + archiveReason + '\'' +
            ", escrowTime=" + escrowTime +
            ", caId=" + caId +
            ", mediumSn='" + mediumSn + '\'' +
            ", subject='" + subject + '\'' +
            ", validEnd=" + validEnd +
            ", validStart=" + validStart +
            ", encCertSn='" + encCertSn + '\'' +
            ", userInfoExs='" + userInfoExs + '\'' +
            ", id=" + id +
            ", keyIndex='" + keyIndex + '\'' +
            ", publicKey='" + publicKey + '\'' +
            ", privateKey='" + privateKey + '\'' +
            ", plainPriKey='" + plainPriKey + '\'' +
            ", mainKeyId=" + mainKeyId +
            ", keyType='" + keyType + '\'' +
            ", createTime=" + createTime +
            '}';
    }
}
