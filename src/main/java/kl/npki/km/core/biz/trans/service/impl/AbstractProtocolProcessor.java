package kl.npki.km.core.biz.trans.service.impl;

import kl.nbase.security.asn1.x509.Certificate;
import kl.npki.base.core.biz.license.service.LicenseValidator;
import kl.npki.base.core.common.crypto.SignatureHelper;
import kl.npki.base.core.common.service.MsgResult;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.km.core.biz.ca.model.CaEntity;
import kl.npki.km.core.biz.ca.service.CaManager;
import kl.npki.km.core.biz.check.service.KmLicenseValidator;
import kl.npki.km.core.biz.trans.service.IProtocolProcessor;
import kl.npki.km.core.common.constants.KeyServiceConstants;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.core.exception.KmValidationError;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Author: guoq
 * @Date: 2022/9/1
 * @description: 抽象协议处理，处理公用校验
 */
public abstract class AbstractProtocolProcessor implements IProtocolProcessor {

    private static final Logger log = LoggerFactory.getLogger(AbstractProtocolProcessor.class);

    private final CaManager caManager;

    private final LicenseValidator kmLicenseValidator;

    protected AbstractProtocolProcessor() {
        this.caManager = CaManager.getInstance();
        this.kmLicenseValidator = new KmLicenseValidator();
    }


    protected void checkLicense() {
        kmLicenseValidator.check();
    }

    /**
     * 检查CA信息
     * @param certSn
     * @param keyHash
     * @param signature
     * @param toBeSigned
     * @param result
     * @return
     */
    public CaEntity checkCa(String certSn, String keyHash, byte[] signature, byte[] toBeSigned, MsgResult<byte[]> result) {
        // 1. 检查CA记录
        CaEntity ca = caManager.getCaEntity(certSn, keyHash);
        if (ObjectUtils.isEmpty(ca)) {
            throw KmValidationError.CA_NOT_EXISTING_ERROR.toException("certSn:" + certSn + ";keyHash:" + keyHash);
        }
        // 填充ca名称
        result.setCallerId(String.valueOf(ca.getId()));
        result.setCallerName(ca.getCaName());
        //2. 验证CA状态
        ca.checkStatus();

        //3. 验证消息签名
        boolean verifyRequest =
            KmConfigWrapper.getKmConf().isVerifyRequest() && !BaseConfigWrapper.getSysConfig().isPerformanceTest();
        if (verifyRequest) {
            Certificate caCert = ca.getIdCertObj();
            if (caCert == null) {
                throw KmValidationError.CA_ID_CERT_NOT_EXISTING_ERROR.toException("The specified CA identity certificate has not been registered in the KM system!");
            }
            boolean verified = SignatureHelper.verify(caCert, signature, toBeSigned);
            // 如果验证失败，通知客户端验证未通过
            if (!verified) {
                throw KmValidationError.SIGNED_VERIFY_ERROR.toException("The signature verification of the request " +
                    "sent by the CA failed! " + ca.getCaName());
            }
        }
        return ca;
    }

    public byte[] signResponse(byte[] toBeSigned) {
        if (!KmConfigWrapper.getKmConf().isSignResponse()) {
            return KeyServiceConstants.NO_SIGN_MARK.getBytes();
        }

        try {
            return SignatureHelper.sign(toBeSigned);
        } catch (Exception e) {
            log.error("Failed to sign response!", e);
            return KeyServiceConstants.SIGN_ERROR_MARK.getBytes();
        }
    }

}
