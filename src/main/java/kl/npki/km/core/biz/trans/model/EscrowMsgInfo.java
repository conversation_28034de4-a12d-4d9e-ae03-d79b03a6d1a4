package kl.npki.km.core.biz.trans.model;

import kl.npki.km.core.common.constants.KmConstants;
import kl.npki.km.core.exception.KmValidationError;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.time.LocalDateTime;
import java.util.List;

import static kl.npki.km.core.common.constants.KMCoreI18N.VALIDITY_ABNORMAL_ERROR_MSG;


/**
 * @Author: guoq
 * @Date: 2022/9/2
 * @description: 密钥申请消息
 */
public class EscrowMsgInfo extends RetKeyMsgInfo {

    private static final int MEDIUM_SN_INDEX = 0;

    /**
     * 密钥生命周期起始时间，可选
     */
    private LocalDateTime notBefore;

    /**
     * 密钥生命周期结束时间，可选
     */
    private LocalDateTime notAfter;

    /**
     * 用户姓名/DN
     */
    private String username;

    /**
     * 地区编码，可选
     */
    private List<String> dsCodes;

    /**
     * 扩展信息，可选
     */
    private List<String> extendInfoList;

    public LocalDateTime getNotBefore() {
        return notBefore;
    }

    public void setNotBefore(LocalDateTime notBefore) {
        this.notBefore = notBefore;
    }

    public LocalDateTime getNotAfter() {
        return notAfter;
    }

    public void setNotAfter(LocalDateTime notAfter) {
        this.notAfter = notAfter;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public List<String> getDsCodes() {
        return dsCodes;
    }

    public void setDsCodes(List<String> dsCodes) {
        this.dsCodes = dsCodes;
    }

    public List<String> getExtendInfoList() {
        return extendInfoList;
    }

    public void setExtendInfoList(List<String> extendInfoList) {
        this.extendInfoList = extendInfoList;
    }

    public String getMediumSn() {
        if (CollectionUtils.isEmpty(extendInfoList)) {
            return null;
        }
        return getExtendInfoList().get(MEDIUM_SN_INDEX);
    }

    /**
     * 调整时间
     */
    public void adjustTime() {
        LocalDateTime now = LocalDateTime.now();
        if (getNotAfter().isBefore(getNotBefore()) || getNotAfter().isBefore(now)) {
            throw KmValidationError.VALIDITY_ABNORMAL_ERROR.toException(VALIDITY_ABNORMAL_ERROR_MSG, getNotBefore(), getNotAfter());
        }

        // 申请密钥起始有效期最多可以比现在早5个小时，多于5个小时统一设置为当前时间
        LocalDateTime maxBefore = now.plusHours(KmConstants.FIVE_HOURS);
        LocalDateTime minBefore = now.minusHours(KmConstants.FIVE_HOURS);

        // notBefore为空，小于最小时间,大于最大时间，设置默认值
        if (ObjectUtils.isEmpty(getNotBefore()) || getNotBefore().isBefore(minBefore) || getNotBefore().isAfter(maxBefore)) {
            setNotBefore(now);
        }

        int year = getCaEntity().getLimitYear();
        LocalDateTime maxAfter = now.plusYears(year);
        // notAfter为空，大于最大时间设置默认值
        if (ObjectUtils.isEmpty(getNotAfter()) || getNotAfter().isAfter(maxAfter)) {
            setNotAfter(maxAfter);
        }

    }
}
