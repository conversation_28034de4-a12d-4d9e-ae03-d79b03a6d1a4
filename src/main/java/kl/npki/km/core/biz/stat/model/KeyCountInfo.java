package kl.npki.km.core.biz.stat.model;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/3 14:27
 * @Description: 密钥统计信息
 */
public class KeyCountInfo implements Serializable {

    @Schema(description =  "密钥类型")
    private String keyType;
    @Schema(description =  "密钥类型对应的密钥数量")
    private Integer count;

    public KeyCountInfo(String keyType, Integer count) {
        this.keyType = keyType;
        this.count = count;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
