package kl.npki.km.core.biz.ca.model;

import kl.nbase.helper.utils.CheckUtils;
import kl.nbase.security.asn1.x509.Certificate;
import kl.npki.base.core.asn1.custom.CaInfo;
import kl.npki.base.core.asn1.custom.TBSCaFastRequest;
import kl.npki.base.core.biz.cert.service.CertVerifyHelper;
import kl.npki.base.core.common.manager.MgrHolder;
import kl.npki.base.core.constant.CaKeyResponseStandardVersionEnum;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.base.core.utils.DigestUtils;
import kl.npki.km.core.common.RepositoryHelper;
import kl.npki.km.core.common.constants.CaAccessTypeEnum;
import kl.npki.km.core.common.constants.CaConstants;
import kl.npki.km.core.common.constants.CaVersionTypeEnum;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.core.exception.KmInternalError;
import kl.npki.km.core.exception.KmValidationError;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import static kl.npki.km.core.common.constants.KMCoreI18N.*;

/**
 * @Author: guoq
 * @Date: 2022/8/29
 * @description: ca实体对象
 */
public class CaEntity implements Serializable {

    private static final Logger log = LoggerFactory.getLogger(CaEntity.class);

    private static final long serialVersionUID = -1478629590424596736L;

    /**
     * CA ID
     */
    private Long id;

    /**
     * CA名称
     */
    private String caName;

    /**
     * CA版本
     */
    private CaVersionTypeEnum caVersion;

    /**
     * CA接入方式
     */
    private CaAccessTypeEnum caAccessType;

    /**
     * CA 身份证书序列号
     */
    private String certSn;

    /**
     * SM2密钥类型时使用的标准规范版本
     */
    private CaKeyResponseStandardVersionEnum sm2KeyStandardVersion;
    /**
     * 身份证书
     */
    private String idCert;

    /**
     * ssl客户端证书
     */
    private String sslCert;

    /**
     * 密钥有效期
     */
    private int limitYear;

    /**
     * 证书公用hash
     */
    private String keyIndex;

    /**
     * CA资源信息
     */
    private Map<String, CaResourceEntity> resources;

    /**
     * CA状态
     */
    private EntityStatus caStatus;

    /**
     * IP白名单
     */
    private String ipAllowList;

    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    /**
     * 备注
     */
    private String remark;

    public CaEntity() {

    }

    public CaEntity(TBSCaFastRequest tbsCaFastRequest) {
        CaInfo caInfo = tbsCaFastRequest.getCaInfo();
        this.caName = new String(caInfo.getCaName().getOctets());
        String remoteCaVersion = new String(caInfo.getCaVersion().getOctets());
        this.caVersion = CaVersionTypeEnum.valueOfById(remoteCaVersion);
        Certificate idCertificate = caInfo.getIdCert();
        this.idCert = CertUtil.encodeCertWithBase64(idCertificate);
        this.limitYear = tbsCaFastRequest.getLimitYear().intValueExact();
        int sm2KeyStandardVersionInt = tbsCaFastRequest.getSm2KeyStandardVersion().intValueExact();
        sm2KeyStandardVersion = CaKeyResponseStandardVersionEnum.valueOfCode(sm2KeyStandardVersionInt);
    }

    /**
     * 查询ca信息
     *
     * @param caId ca编号信息
     * @return
     */
    public static CaEntity searchCaEntity(Long caId) {
        // 查询对应ca信息
        return RepositoryHelper.getCaRepository().searchCaEntityById(caId);
    }

    public static CaEntity checkCaEntityExist(Long caId) {
        CaEntity caEntity = searchCaEntity(caId);
        CheckUtils.notNull(caEntity, KmValidationError.CA_NOT_EXISTING_ERROR.toException("CA ID = " + caId));
        return caEntity;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCaName() {
        return caName;
    }

    public void setCaName(String caName) {
        this.caName = caName;
    }

    public CaVersionTypeEnum getCaVersion() {
        return caVersion;
    }

    public void setCaVersion(CaVersionTypeEnum caVersion) {
        this.caVersion = caVersion;
    }

    public String getCertSn() {
        return certSn;
    }

    public CaAccessTypeEnum getCaAccessType() {
        return caAccessType;
    }

    public void setCaAccessType(CaAccessTypeEnum caAccessType) {
        this.caAccessType = caAccessType;
    }

    public void setCertSn(String certSn) {
        this.certSn = certSn;
    }

    public String getIdCert() {
        return idCert;
    }

    public void setIdCert(String idCert) {
        this.idCert = idCert;
    }

    public Certificate getIdCertObj() {
        return CertUtil.parsePemCert(getIdCert());
    }

    public String getSslCert() {
        return sslCert;
    }

    public void setSslCert(String sslCert) {
        this.sslCert = sslCert;
    }

    public int getLimitYear() {
        return limitYear;
    }

    public void setLimitYear(int limitYear) {
        this.limitYear = limitYear;
    }

    public String getKeyIndex() {
        return keyIndex;
    }

    public CaKeyResponseStandardVersionEnum getSm2KeyStandardVersion() {
        return sm2KeyStandardVersion;
    }

    public void setSm2KeyStandardVersion(CaKeyResponseStandardVersionEnum sm2KeyStandardVersion) {
        this.sm2KeyStandardVersion = sm2KeyStandardVersion;
    }

    public void setResources(Map<String, CaResourceEntity> resources) {
        this.resources = resources;
    }

    public void setKeyIndex(String keyIndex) {
        this.keyIndex = keyIndex;
    }

    public EntityStatus getCaStatus() {
        return caStatus;
    }

    public void setCaStatus(EntityStatus caStatus) {
        this.caStatus = caStatus;
    }

    public Map<String, CaResourceEntity> getResourceMap() {
        return resources;
    }

    public void setResources(List<CaResourceEntity> resources) {
        if (MapUtils.isEmpty(this.resources)) {
            this.resources = new HashMap<>(8);
        }
        if (CollectionUtils.isEmpty(resources)) {
            return;
        }
        // 已存在的资源，set进来覆盖
        Map<String, CaResourceEntity> collect =
            resources.stream().collect(Collectors.toMap(CaResourceEntity::getKeyType, Function.identity(),
                (key1, key2) -> key2));
        this.resources.putAll(collect);

    }

    public List<CaResourceEntity> getResources() {
        return new ArrayList<>(resources.values());
    }

    public void setResourceMap(Map<String, CaResourceEntity> resources) {
        this.resources = resources;
    }

    public String getIpAllowList() {
        return ipAllowList;
    }

    public void setIpAllowList(String ipAllowList) {
        this.ipAllowList = ipAllowList;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 解析身份证书
     */
    public void parsingIdCert() {
        Certificate certificate = CertUtil.parsePemCert(getIdCert());
        CertVerifyHelper.checkValidity(certificate);
        // 校验证书是否是签名证书
        CheckUtils.isTrue(CertVerifyHelper.isSigningCert(certificate),
            KmValidationError.CA_ID_CERT_NOT_SIGNING_CERT.toException());
        setCertSn(CertUtil.getHexSnOfCert(certificate));
        try {
            // 这里使用sha1 国际规范规定证书公钥使用sha1做摘要（同时兼容老ca）
            setKeyIndex(DigestUtils.digestBySha1(certificate.getSubjectPublicKeyInfo().getEncoded()));
        } catch (IOException e) {
            throw KmInternalError.CA_CERT_PUBLICKEY_ECODE_ERROR.toException();
        }

    }

    public boolean updateIdCert(String idCert) {
        Certificate certificate = CertUtil.parsePemCert(idCert);
        CertVerifyHelper.checkValidity(certificate);
        String hexSn = CertUtil.getHexSnOfCert(certificate);
        if (getCertSn().equals(hexSn)) {
            log.warn("CA:【{}】身份证书未变化，无需更新", getCaName());
            return false;
        }
        setCertSn(hexSn);
        setIdCert(idCert);
        try {
            setKeyIndex(DigestUtils.digestBySha1(certificate.getSubjectPublicKeyInfo().getEncoded()));
        } catch (IOException e) {
            throw KmInternalError.CA_CERT_PUBLICKEY_ECODE_ERROR.toException();
        }
        return true;
    }

    /**
     * 从3个维度，判断ca是否存在
     * caName | certSn |  keyHash
     */
    public void checkExist() {
        if (RepositoryHelper.getCaRepository().checkExist(getCertSn(), getKeyIndex(), getCaName())) {
            throw KmValidationError.CA_SN_OR_KEY_HASH_OR_NAME_EXISTING_ERROR.toException();
        }
    }

    /**
     * 保存CA实体信息
     *
     * @return
     */
    public Long save() {
        return save(EntityStatus.NORMAL);
    }

    /**
     * 保存CA实体信息
     *
     * @return
     */
    public Long save(EntityStatus entityStatus) {
        setCaStatus(entityStatus);
        Long caId = RepositoryHelper.getCaRepository().insertCAEntity(this);
        setId(caId);
        return caId;
    }

    /**
     * 初始化ca密钥资源
     */
    public void initCaKeyResource() {
        List<String> supportAsymAlgo = KmConfigWrapper.getAsymmetricAndPostQuantumKeyTypesWithoutSM9List();
        List<CaResourceEntity> caResourceEntityList =
            supportAsymAlgo.stream().map(keyType ->
                new CaResourceEntity(keyType, CaConstants.DEFAULT_LIMIT_NUM,
                    CaConstants.DEFAULT_WARNING_NUM)).collect(Collectors.toList());
        setResources(caResourceEntityList);
    }

    /**
     * 保存ca密钥资源
     */
    public void saveCaKeyResource() {
        if (MapUtils.isEmpty(resources)) {
            return;
        }
        resources.forEach((key, value) -> {
                value.setCaId(getId());
                value.save();
            }
        );
    }

    /**
     * 查询ca资源集合
     *
     * @return
     */
    public List<CaResourceEntity> searchResourceList() {
        List<CaResourceEntity> caResourceEntityList =
            RepositoryHelper.getCaResourceRepository().searchCaResources(getId());
        setResources(caResourceEntityList);
        // 查询ca资源信息
        return caResourceEntityList;
    }

    /**
     * 更新CA资源信息
     *
     * @param resourceList
     */
    public boolean updateResource(List<AddResourceInfo> resourceList) {
        // 更新标识
        AtomicBoolean isUpdate = new AtomicBoolean(false);
        resourceList.forEach(addResourceInfo -> {
            String keyType = addResourceInfo.getKeyType();
            if (resources.containsKey(keyType)) {
                // 资源已授权，更新资源数量
                CaResourceEntity caResourceEntity = resources.get(keyType);
                isUpdate.set(caResourceEntity.updateNum(addResourceInfo));
            } else {
                // 资源未授权，新增资源
                CaResourceEntity resourceEntity = new CaResourceEntity(keyType, addResourceInfo.getAddNum(),
                    addResourceInfo.getWarningNum());
                resourceEntity.setCaId(getId());
                resourceEntity.save();
                resources.put(keyType, resourceEntity);
                isUpdate.set(true);
            }
        });
        return isUpdate.get();
    }


    /**
     * 检查CA是否为正常状态
     */
    public void checkStatus() {
        if (!EntityStatus.NORMAL.equals(getCaStatus())) {
            throw KmValidationError.CA_STATUS_ABNORMAL_ERROR.toException(caName);
        }
    }

    /**
     * 更新CA
     *
     * @return
     */
    public boolean caUpdate() {
        setUpdateTime(LocalDateTime.now());
        return RepositoryHelper.getCaRepository().updateCAEntity(this);
    }

    /**
     * 冻结ca
     *
     * @return
     */
    public boolean caFreeze() {
        // 检查ca是否为正常状态
        checkStatus();
        // 更新ca状态为冻结
        setCaStatus(EntityStatus.FREEZE);
        // 更新时间
        setUpdateTime(LocalDateTime.now());
        return RepositoryHelper.getCaRepository().updateCAEntity(this);
    }

    /**
     * 解冻ca
     *
     * @return
     */
    public boolean caUnfreeze() {
        switch (getCaStatus()) {
            case REVOKED:
                throw KmValidationError.CA_UNFREEZE_ERROR.toException(CA_ABOLISHED_WITH_NAME, getCaName());
            case NORMAL:
                throw KmValidationError.CA_UNFREEZE_ERROR.toException(CA_UNFROZEN_WITH_NAME, getCaName());
            default:
                break;
        }
        // 更新ca状态为冻结
        setCaStatus(EntityStatus.NORMAL);
        // 更新时间
        setUpdateTime(LocalDateTime.now());
        return RepositoryHelper.getCaRepository().updateCAEntity(this);
    }

    /**
     * 废除ca
     *
     * @return
     */
    public boolean caRevoke() {
        // 检查ca是否为正常状态
        checkStatus();
        // 更新ca状态为废除
        setCaStatus(EntityStatus.REVOKED);
        // 更新时间
        setUpdateTime(LocalDateTime.now());
        return RepositoryHelper.getCaRepository().updateCAEntity(this);
    }

    /**
     * 恢复CA
     *
     * @return
     */
    public boolean caRecovery() {
        if (!KmConfigWrapper.getKmConf().isRestoreCA()) {
            throw KmValidationError.CA_RECOVERY_ERROR.toException(CA_UNRECOVERABLE_WITH_NAME, getCaName());
        }
        // 更新ca状态为正常
        setCaStatus(EntityStatus.NORMAL);
        // 更新时间
        setUpdateTime(LocalDateTime.now());
        return RepositoryHelper.getCaRepository().updateCAEntity(this);
    }

    /**
     * CA身份证书是否可信
     */
    public boolean isIdCertTrusted() {
        try {
            Certificate[] certChains = MgrHolder.getManageCertMgr().getCertChains();

            if (certChains == null || certChains.length == 0) {
                return false;
            }

            return CertVerifyHelper.verifyCertByCertchain(getIdCertObj(), certChains);
        } catch (Exception e) {
            // 证书解析或验证过程中出现异常，设置为不可信
            log.error("CA identity certificate trust verification failed", e);
            return false;
        }
    }

}
