package kl.npki.km.core.biz.trans.service.handler.impl;

import kl.npki.km.core.biz.trans.model.KeyMsgInfo;
import kl.npki.km.core.biz.trans.service.handler.IKeyBiz;

/**
 * @Author: guoq
 * @Date: 2022/9/2
 * @description: 密钥服务通用操作
 */
public abstract class AbstractKeyBiz implements IKeyBiz {

    protected AbstractKeyBiz() {
    }


    @Override
    public void bizBefore(KeyMsgInfo keyMsg) {

    }
}
