package kl.npki.km.core.biz.check.service;

import kl.npki.base.core.biz.license.LicenseMgr;
import kl.npki.base.core.biz.license.service.LicenseValidator;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.km.core.biz.stat.model.KeyCountInfo;
import kl.npki.km.core.exception.KmValidationError;
import kl.npki.km.core.repository.IKeyCurrentRepository;
import kl.security.license.core.LicenseStatus;

import java.util.List;

/**
 * NKM的系统许可校验逻辑实现
 *
 * <AUTHOR>
 * @date 2023/8/24
 */
public class KmLicenseValidator implements LicenseValidator {

    /**
     * DEMO 环境允许的密钥使用量
     */
    public static final int KEY_USAGE_LIMIT = 200;

    @Override
    public void check() {
        // 判断是否为DEMO环境
        if (!SystemUtil.isDeployed()) {
            // 未部署正式环境
            List<KeyCountInfo> currentKeyCounts = RepositoryFactory.get(IKeyCurrentRepository.class).countKeyType();
            boolean isExceeded = currentKeyCounts.stream().mapToInt(KeyCountInfo::getCount).sum() > KEY_USAGE_LIMIT;
            if (isExceeded) {
                throw KmValidationError.DEMO_KEY_USAGE_EXCEEDED.toException();
            }
        } else {
            // 正式环境校验license状态
            LicenseStatus status = LicenseMgr.getInstance().getLicense().getStatus();
            if (!LicenseStatus.FINE.equals(status)) {
                throw KmValidationError.ILLEGAL_LICENSE_STATUS.toException();
            }
        }
    }
}
