package kl.npki.km.core.biz.trans.service.handler.impl;

import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.annotation.KlTransactional;
import kl.npki.base.core.common.trace.InvocationType;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.event.EntityChangeEvent;
import kl.npki.base.core.event.EntityChangeEventManager;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.km.core.biz.ca.model.CaEntity;
import kl.npki.km.core.biz.ca.service.CaManager;
import kl.npki.km.core.biz.key.model.CurrentKeyEntity;
import kl.npki.km.core.biz.key.model.SpareKeyEntity;
import kl.npki.km.core.biz.key.service.SpareKeyPool;
import kl.npki.km.core.biz.trans.model.EscrowMsgInfo;
import kl.npki.km.core.biz.trans.model.KeyMsgInfo;
import kl.npki.km.core.biz.trans.model.ResultInfo;
import kl.npki.km.core.common.constants.CaKeyServiceType;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.core.exception.KmInternalError;
import kl.npki.km.core.mapper.ConvertServiceHelper;
import kl.npki.km.core.repository.IKeyCurrentRepository;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;

/**
 * @Author: guoq
 * @Date: 2022/9/2
 * @description: 密钥申请服务
 */
public class KeyApplyBiz extends AbstractKeyBiz {

    private static final Logger log = LoggerFactory.getLogger(KeyApplyBiz.class);

    private final IKeyCurrentRepository keyCurrentRepository;
    private final CaManager caManager;
    private final SpareKeyPool keyPool;

    public KeyApplyBiz() {
        this.keyCurrentRepository = RepositoryFactory.get(IKeyCurrentRepository.class);
        this.caManager = CaManager.getInstance();
        this.keyPool = SpareKeyPool.getInstance();
    }

    @KlTransactional
    @Override
    public ResultInfo bizExecute(KeyMsgInfo keyMsg) {
        EscrowMsgInfo escrowMsg = (EscrowMsgInfo) keyMsg;

        // 1. 检查要分发的序列号数据是否已经在数据库中存在
        Optional<ResultInfo> repeatResult = checkSnIdempotent(escrowMsg);
        if (repeatResult.isPresent()) {
            return repeatResult.get();
        }

        // 2. 减少密钥资源
        CaEntity caEntity = escrowMsg.getCaEntity();
        AsymAlgo keyType = escrowMsg.getKeyType();
        caManager.reduceIt(keyType, caEntity.getCertSn(), caEntity.getKeyIndex());

        try {
            //3. 从密钥池中获取密钥对
            SpareKeyEntity spareKeyEntity = keyPool.getKeyEntity(keyType);

            CurrentKeyEntity keyEntity = ConvertServiceHelper.getConvertService().convert(spareKeyEntity,
                CurrentKeyEntity.class);

            // 给密钥记录附加用户信息
            keyEntity.setEscrowMsg(escrowMsg);

            //4. 构造应答
            // 缓存池中存储的是私钥明文
            ResultInfo resultInfo = new ResultInfo(escrowMsg, keyEntity, getType());

            //5. 将密钥记录存入当前库
            keyEntity.save();
            // 6. 推送密钥实体变更通知
            EntityChangeEventManager.getInstance().publishEvent(EntityChangeEvent.newInstance(keyEntity,
                CaKeyServiceType.KEY_APPLY.name(),
                InvocationType.TCP_SERVICE_INTERFACE));
            if (log.isDebugEnabled()) {
                log.debug("KeyApply finish. keyIndex:{}, keyType:{}", keyEntity.getKeyIndex(), keyEntity.getKeyType());
            }
            return resultInfo;
        } catch (Exception e) {
            caManager.giveBackIt(keyType, caEntity.getCertSn(), caEntity.getKeyIndex());
            throw KmInternalError.KEY_ESCROW_SERVICE_ERROR.toException(e);
        }
    }

    @Override
    public CaKeyServiceType getType() {
        return CaKeyServiceType.KEY_APPLY;
    }


    /**
     * 重复请求幂等
     *
     * @param escrowMsgInfo
     */
    private Optional<ResultInfo> checkSnIdempotent(EscrowMsgInfo escrowMsgInfo) {
        if (!KmConfigWrapper.getKmConf().isCheckSnExist() || BaseConfigWrapper.getSysConfig().isPerformanceTest()) {
            return Optional.empty();
        }

        String userEncCertNo = escrowMsgInfo.getUserEncCertNo();
        CurrentKeyEntity keyEntity = keyCurrentRepository.searchKeyBySnAndType(userEncCertNo, escrowMsgInfo.getKeyType().getAlgoName());
        if (ObjectUtils.isEmpty(keyEntity)) {
            return Optional.empty();
        }

        // 构造上次请求的响应
        keyEntity.decryptPriKey();

        // 构造数字信封对象返回
        try {
            return Optional.of(new ResultInfo(escrowMsgInfo, keyEntity, getType()));
        } catch (Exception e) {
            throw KmInternalError.KEY_ESCROW_SERVICE_ERROR.toException(e);
        }
    }
}
