package kl.npki.km.core.biz.key.model;

import kl.nbase.security.asn1.pkcs.PrivateKeyInfo;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.utils.RSAKeyUtil;
import kl.npki.km.core.common.RepositoryHelper;

import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.interfaces.RSAPrivateKey;

/**
 * @Author: guoq
 * @Date: 2022/9/15
 * @description: 备用密钥实体
 */
public class SpareKeyEntity extends KeyEntity {

    private static final long serialVersionUID = -2707492260603715550L;

    public SpareKeyEntity() {
    }

    public SpareKeyEntity(String keyType, KeyPair keyPair) {
        this.keyType = keyType;
        byte[] pubKeyEncoded = keyPair.getPublic().getEncoded();
        parsePublicKeyByte(pubKeyEncoded);
        PrivateKey priKey = keyPair.getPrivate();
        AsymAlgo asymAlgo = AsymAlgo.valueOf(priKey);
        if (asymAlgo.isRSA()) {
            // RSA私钥使用PKCS1编码存储
            RSAPrivateKey rsaPrivateKey = RSAKeyUtil.bytes2RSAPrivateKey(priKey.getEncoded());
            byte[] pkcs1PrivateKeyEncoded = PrivateKeyInfo.getInstance(rsaPrivateKey.getEncoded()).getPrivateKey().getOctets();
            setPlainPriKeyByte(pkcs1PrivateKeyEncoded);
        } else {
            setPlainPriKeyByte(priKey.getEncoded());
        }
    }

    /**
     * 保存备用密钥实体
     *
     * @return
     */
    public Long save() {
        return RepositoryHelper.getKeyPoolRepository().insertKeyPair(this);
    }

    @Override
    public String toString() {
        return "SpareKeyEntity{" +
            "id=" + id +
            ", keyIndex='" + keyIndex + '\'' +
            ", publicKey='" + publicKey + '\'' +
            ", privateKey='" + privateKey + '\'' +
            ", plainPriKey='" + plainPriKey + '\'' +
            ", mainKeyId=" + mainKeyId +
            ", keyType='" + keyType + '\'' +
            ", createTime=" + createTime +
            '}';
    }
}
