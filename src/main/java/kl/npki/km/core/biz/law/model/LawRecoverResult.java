package kl.npki.km.core.biz.law.model;

import kl.nbase.security.asn1.custom.pkcs.EnvelopedKeyPairData;
import kl.nbase.security.asn1.x509.Certificate;
import kl.npki.base.core.constant.ProtectKeyTypeEnum;
import kl.npki.base.core.utils.CertUtil;

/**
 * @Author: guoq
 * @Date: 2022/9/23
 * @description: 司法恢复结果
 */
public class LawRecoverResult {

    /**
     * 密钥恢复数据，base64 编码的 {@link EnvelopedKeyPairData}
     */
    private String recoverData;

    /**
     * base64 编码的证书值
     */
    private String certValue;

    /**
     * 密钥类型，RSA 或 SM2
     */
    private String keyType;

    /**
     * 密钥保护类型
     */
    private ProtectKeyTypeEnum protectKeyType;

    public String getRecoverData() {
        return recoverData;
    }

    public void setRecoverData(String recoverData) {
        this.recoverData = recoverData;
    }

    public String getCertValue() {
        return certValue;
    }

    public void setCertValue(String certValue) {
        this.certValue = certValue;
    }

    public void setCertValue(Certificate certificate) {
        this.certValue = CertUtil.encodeCertWithBase64(certificate);
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public ProtectKeyTypeEnum getProtectKeyType() {
        return protectKeyType;
    }

    public void setProtectKeyType(ProtectKeyTypeEnum protectKeyType) {
        this.protectKeyType = protectKeyType;
    }
}
