package kl.npki.km.core.biz.key.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import kl.npki.km.core.common.constants.KeyServiceConstants;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Author: guoq
 * @Date: 2022/9/20
 * @description: 证书密钥实体，该实体用于封装在用库和历史库共有属性
 */
public class CertKeyEntity extends KeyEntity {

    private static final long serialVersionUID = -8727589798247321174L;

    protected Long caId;

    /**
     * 介质序列号
     */
    protected String mediumSn;

    protected String subject;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    protected LocalDateTime validEnd;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    protected LocalDateTime validStart;

    protected String encCertSn;

    protected String userInfoExs;

    public Long getCaId() {
        return caId;
    }

    public void setCaId(Long caId) {
        this.caId = caId;
    }

    public String getMediumSn() {
        return mediumSn;
    }

    public void setMediumSn(String mediumSn) {
        this.mediumSn = mediumSn;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public LocalDateTime getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(LocalDateTime validEnd) {
        this.validEnd = validEnd;
    }

    public LocalDateTime getValidStart() {
        return validStart;
    }

    public void setValidStart(LocalDateTime validStart) {
        this.validStart = validStart;
    }

    public String getEncCertSn() {
        return encCertSn;
    }

    public void setEncCertSn(String encCertSn) {
        this.encCertSn = encCertSn;
    }

    public String getUserInfoExs() {
        return userInfoExs;
    }

    public void setUserInfoExs(String userInfoExs) {
        this.userInfoExs = userInfoExs;
    }

    public String convertUserInfoExs(List<String> userInfoExList) {
        return StringUtils.join(userInfoExList.toArray(), KeyServiceConstants.USER_INFO_SEPARATOR);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        CertKeyEntity keyEntity = (CertKeyEntity) o;
        return Objects.equals(caId, keyEntity.caId) &&
            Objects.equals(mediumSn, keyEntity.mediumSn) &&
            Objects.equals(subject, keyEntity.subject) &&
            Objects.equals(validEnd, keyEntity.validEnd) &&
            Objects.equals(validStart, keyEntity.validStart) &&
            Objects.equals(encCertSn, keyEntity.encCertSn) &&
            Objects.equals(userInfoExs, keyEntity.userInfoExs);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), caId, mediumSn, subject, validEnd, validStart, encCertSn, userInfoExs);
    }

    @Override
    public String toString() {
        return "CertKeyEntity{" +
            "caId=" + caId +
            ", mediumSn='" + mediumSn + '\'' +
            ", subject='" + subject + '\'' +
            ", validEnd=" + validEnd +
            ", validStart=" + validStart +
            ", encCertSn='" + encCertSn + '\'' +
            ", userInfoExs='" + userInfoExs + '\'' +
            ", id=" + id +
            ", keyIndex='" + keyIndex + '\'' +
            ", publicKey='" + publicKey + '\'' +
            ", privateKey='" + privateKey + '\'' +
            ", plainPriKey='" + plainPriKey + '\'' +
            ", mainKeyId=" + mainKeyId +
            ", keyType='" + keyType + '\'' +
            ", createTime=" + createTime +
            '}';
    }
}
