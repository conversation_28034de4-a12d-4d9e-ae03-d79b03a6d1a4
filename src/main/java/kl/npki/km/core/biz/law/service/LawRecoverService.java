package kl.npki.km.core.biz.law.service;

import kl.nbase.helper.utils.CheckUtils;
import kl.nbase.i18n.i18n.I18nUtil;
import kl.nbase.security.asn1.ASN1ObjectIdentifier;
import kl.nbase.security.asn1.pkcs.CertificationRequest;
import kl.nbase.security.asn1.x500.X500Name;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.asn1.x509.SubjectPublicKeyInfo;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.nbase.security.utils.AsymKeyUtil;
import kl.nbase.security.utils.RSAKeyUtil;
import kl.nbase.security.utils.SignatureAlgoUtil;
import kl.npki.base.core.asn1.custom.EnvelopedKeyPairDataFactory;
import kl.npki.base.core.biz.cert.service.CertSignService;
import kl.npki.base.core.biz.mainkey.service.MainKeyManager;
import kl.npki.base.core.constant.ProtectKeyTypeEnum;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.base.core.utils.KeyUtils;
import kl.npki.base.core.utils.PriKeyUtils;
import kl.npki.km.core.biz.key.model.CertKeyEntity;
import kl.npki.km.core.biz.law.model.LawRecoverInfo;
import kl.npki.km.core.biz.law.model.LawRecoverResult;
import kl.npki.km.core.common.constants.KMCoreI18N;
import kl.npki.km.core.common.constants.KeyPosition;
import kl.npki.km.core.exception.KmInternalError;
import kl.npki.km.core.exception.KmValidationError;
import kl.npki.km.core.repository.IKeyCurrentRepository;
import kl.npki.km.core.repository.IKeyHistoryRepository;
import org.apache.commons.codec.binary.Base64;

import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;

import static kl.npki.km.core.common.constants.KMCoreI18N.FAILED_TO_CONSTRUCT_KEY_ENVELOPE;
import static kl.npki.km.core.common.constants.KMCoreI18N.FAILED_TO_OBTAIN_JUDICIAL_ADMIN_PUBLIC_KEY;

/**
 * @Author: guoq
 * @Date: 2022/9/23
 * @description: 司法恢复服务
 */
public class LawRecoverService {

    private final IKeyHistoryRepository keyHistoryRepository;
    private final IKeyCurrentRepository keyCurrentRepository;
    private final MainKeyManager mainKeyManager;


    public LawRecoverService() {
        this.keyHistoryRepository = RepositoryFactory.get(IKeyHistoryRepository.class);
        this.keyCurrentRepository = RepositoryFactory.get(IKeyCurrentRepository.class);
        this.mainKeyManager = MainKeyManager.getInstance();

    }


    public LawRecoverResult doLawRecover(LawRecoverInfo lawRecoverInfo) {
        // 1. 获取司法管理员证书
        Certificate lawRecoverSignCert = CertUtil.parseCert(lawRecoverInfo.getLawSignCert());

        // 2. 验证司法管理员签名
        String lawSignData = lawRecoverInfo.getLawSignData();
        String lawSignText = lawRecoverInfo.getLawSignText();
        PublicKey lawRecoverPublicKey;
        try {
            lawRecoverPublicKey = KeyUtils.getPublicKey(lawRecoverSignCert.getSubjectPublicKeyInfo());
        } catch (Exception e) {
            throw KmInternalError.GET_PUBLIC_KEY_ERROR.toException(FAILED_TO_OBTAIN_JUDICIAL_ADMIN_PUBLIC_KEY, e);
        }
        ASN1ObjectIdentifier signatureAlgo = lawRecoverSignCert.getSignatureAlgorithm().getAlgorithm();
        HashAlgo hashAlgo = SignatureAlgoUtil.getHashAlgoByOid(signatureAlgo.getId());
        boolean verify = EngineHolder.get().verify(
            lawRecoverPublicKey,
            Base64.decodeBase64(lawSignText),
            Base64.decodeBase64(lawSignData),
            hashAlgo,
            null
        );
        if (!verify) {
            throw KmValidationError.VERIFY_LAW_RECOVER_SIGNATURE_ERROR.toException();
        }

        // 3. 获取密钥对
        CertKeyEntity keyEntity;
        Integer position = lawRecoverInfo.getKeyPosition();
        KeyPosition keyPosition = KeyPosition.valueOfById(position);
        if (KeyPosition.POS_HISTORY.equals(keyPosition)) {
            keyEntity = keyHistoryRepository.searchKeyById(lawRecoverInfo.getKeyId());
        } else {
            keyEntity = keyCurrentRepository.searchKeyById(lawRecoverInfo.getKeyId());
        }
        CheckUtils.notNull(keyEntity, KmValidationError.RESTORE_ID_NOT_EXISTING_ERROR.toException());

        // 获得待恢复密钥对中的私钥并预处理
        byte[] plainPriKeyWithStructure = mainKeyManager.decryptPriKeyByMainKey(keyEntity.getMainKeyId(),
            keyEntity.getPrivateKeyByte());
        PublicKey publicKey = AsymKeyUtil.x509Bytes2PublicKey(keyEntity.getPublicKeyByte());
        AsymAlgo asymAlgo = AsymAlgo.valueOf(publicKey);

        // 4. 使用恢复密钥对构造证书，介质安装需要
        LawRecoverResult recoverResult = new LawRecoverResult();
        // 获取恢复介质公钥（加密公钥）
        String recoverCertReq = lawRecoverInfo.getRecoverCertReq();
        CertificationRequest certificationRequest =
            CertificationRequest.getInstance(Base64Util.base64Decode(recoverCertReq));
        SubjectPublicKeyInfo subjectPublicKeyInfo =
            certificationRequest.getCertificationRequestInfo().getSubjectPublicKeyInfo();
        Certificate recoverCert;
        PublicKey recoverPubKey;
        try {
            recoverPubKey = KeyUtils.getPublicKey(subjectPublicKeyInfo);
            PublicKey toBeRecoveredPubKey =
                CertUtil.getPublicKey(SubjectPublicKeyInfo.getInstance(keyEntity.getPublicKeyByte()));
            PrivateKey privateKey = bytes2PrivateKey(asymAlgo, plainPriKeyWithStructure);
            KeyPair keyPair = new KeyPair(toBeRecoveredPubKey, privateKey);
            X500Name subject = certificationRequest.getCertificationRequestInfo()
                .getSubject();
            recoverCert = CertSignService.issueSelfSignCert(
                EngineHolder.get(),
                subject.toString(),
                keyPair,
                365L);
            recoverResult.setCertValue(recoverCert);
        } catch (Exception e) {
            throw KmInternalError.LAW_RECOVER_CERT_CREATE_ERROR.toException(e);
        }

        // 5. 构造数字信封
        byte[] data;
        try {
            byte[] toBeEncPriKey = PriKeyUtils.adaptPrivateKey(plainPriKeyWithStructure, asymAlgo);

            data = EnvelopedKeyPairDataFactory.genSkfEnvelopedKeyPairData(
                toBeEncPriKey,
                keyEntity.getPublicKeyByte(),
                asymAlgo,
                recoverPubKey
            );
        } catch (Exception e) {
            throw KmInternalError.KEY_RESTORE_ERROR.toException(FAILED_TO_CONSTRUCT_KEY_ENVELOPE, e);
        }

        recoverResult.setRecoverData(Base64.encodeBase64String(data));
        recoverResult.setKeyType(keyEntity.getKeyType());
        recoverResult.setProtectKeyType(ProtectKeyTypeEnum.ENVELOP_DATA);
        return recoverResult;
    }

    private static PrivateKey bytes2PrivateKey(AsymAlgo asymAlgo, byte[] plainPriKeyWithStructure) {
        if (asymAlgo.isRSA()) {
            return RSAKeyUtil.pkcs1Bytes2RSAPrivateKey(plainPriKeyWithStructure);
        }
        return AsymKeyUtil.pkcs8Bytes2PrivateKey(plainPriKeyWithStructure);
    }
}
