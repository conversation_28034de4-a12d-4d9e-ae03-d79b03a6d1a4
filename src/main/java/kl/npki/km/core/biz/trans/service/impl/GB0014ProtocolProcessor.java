package kl.npki.km.core.biz.trans.service.impl;

import kl.nbase.exception.BaseException;
import kl.nbase.security.asn1.ASN1Encoding;
import kl.nbase.security.asn1.ASN1Sequence;
import kl.nbase.security.asn1.DEROctetString;
import kl.nbase.security.asn1.custom.gm.gm0014.km.EntName;
import kl.nbase.security.asn1.custom.gm.gm0014.km.request.*;
import kl.nbase.security.asn1.custom.gm.gm0014.km.response.KMRespond;
import kl.nbase.security.asn1.custom.gm.gm0014.km.response.KSRespond;
import kl.nbase.traffic.annotation.TrafficResource;
import kl.nbase.traffic.exception.BlockException;
import kl.npki.base.core.common.manager.MgrHolder;
import kl.npki.base.core.common.service.MsgResult;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.km.core.biz.ca.model.CaEntity;
import kl.npki.km.core.biz.trans.model.KeyMsgInfo;
import kl.npki.km.core.biz.trans.model.KeyResponseInfo;
import kl.npki.km.core.biz.trans.model.ResultInfo;
import kl.npki.km.core.biz.trans.service.handler.IKeyBiz;
import kl.npki.km.core.biz.trans.service.handler.impl.KeyApplyBiz;
import kl.npki.km.core.biz.trans.service.handler.impl.KeyRestoreBiz;
import kl.npki.km.core.biz.trans.service.handler.impl.KeyRevokeBiz;
import kl.npki.km.core.biz.trans.service.msg.impl.GB0014MsgAdapter;
import kl.npki.km.core.common.constants.CaKeyServiceType;
import kl.npki.km.core.common.constants.KeyServiceConstants;
import kl.npki.km.core.exception.KmValidationError;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static kl.npki.km.core.common.constants.KMCoreI18N.*;


/**
 * @Author: guoq
 * @Date: 2022/9/1
 * @description: GB0014 协议处理
 */
public class GB0014ProtocolProcessor extends AbstractProtocolProcessor {

    public static final String SERVICE_TYPE = "GB0014";

    private static final Logger log = LoggerFactory.getLogger(GB0014ProtocolProcessor.class);

    private final GB0014MsgAdapter msgAdapter;

    public GB0014ProtocolProcessor() {
        super();
        this.msgAdapter = new GB0014MsgAdapter();
    }

    @Override
    @TrafficResource(value = "kmServiceGbv2", blockHandler = "handlerBlockException")
    public MsgResult<byte[]> process(byte[] content) {

        // 1. 处理请求
        MsgResult<byte[]> result = new MsgResult<>(CaKeyServiceType.UNKNOWN_KEY_SERVICE.getDesc());
        KeyResponseInfo responseInfo = handleRequest(content, result);

        // 2. 封装响应
        KSRespond ksr = msgAdapter.convertRespond(responseInfo);
        KMRespond kmr;
        // 3. 构造签名
        byte[] signature = KeyServiceConstants.SERVICE_ERROR_MARK.getBytes();
        try {
            byte[] toBeSigned = ksr.getEncoded(ASN1Encoding.DER);
            signature = signResponse(toBeSigned);
        } catch (Exception e) {
            log.error("Failed to sign response!", e);
            result.setErrorMessage(e.getMessage());
            signature = KeyServiceConstants.SERVICE_ERROR_MARK.getBytes();
        } finally {
            kmr = new KMRespond(
                    ksr,
                    MgrHolder.getIdCertMgr().getIdCert().getSignatureAlgorithm(),
                    new DEROctetString(signature)
            );
        }

        // 4. 返回响应
        try {
            // bc库默认使用BER编码，老CA由于公司库原因只支持DER编码，由于DER为BER子集，使用DER编码
            result.setResult(kmr.getEncoded(ASN1Encoding.DER));
        } catch (IOException e) {
            // 这种情况应该不会达到。
            log.error("Response encode error", e);
            result.setResult(KeyServiceConstants.SERVICE_ERROR_MARK.getBytes());
        }
        return result;
    }

    /**
     * 流控异常时返回的响应
     *
     * @param e
     * @return
     */
    public MsgResult<byte[]> handlerBlockException(BlockException e) {
        log.error("Key Service interface traffic exceeded the flow control threshold!", e);
        MsgResult<byte[]> result = new MsgResult<>(CaKeyServiceType.UNKNOWN_KEY_SERVICE.getDesc());
        result.setErrorMessage(e.getMessage());

        // 构造ksr
        List<ResultInfo> resultList = new ArrayList<>();
        resultList.add(new ResultInfo(KeyServiceConstants.REQUEST_REACH_TRAFFIC_RULE, e.getMessage()));
        KeyResponseInfo responseInfo = new KeyResponseInfo(KeyServiceConstants.REQUEST_REACH_TRAFFIC_RULE_TASK_NO, resultList);
        KSRespond ksr = msgAdapter.convertRespond(responseInfo);

        // 构造 kmr
        KMRespond kmr = new KMRespond(
                ksr,
                MgrHolder.getIdCertMgr().getIdCert().getSignatureAlgorithm(),
                new DEROctetString(KeyServiceConstants.SERVICE_ERROR_MARK.getBytes())
        );

        try {
            // bc库默认使用BER编码，老CA由于公司库原因只支持DER编码，由于DER为BER子集，使用DER编码
            result.setResult(kmr.getEncoded(ASN1Encoding.DER));
        } catch (IOException ex) {
            // 这种情况应该不会达到。
            log.error("Response encode error", e);
            result.setResult(KeyServiceConstants.SERVICE_ERROR_MARK.getBytes());
        }

        return result;
    }

    /**
     * 请求处理
     *
     * @param content
     * @return
     */
    private KeyResponseInfo handleRequest(byte[] content, MsgResult<byte[]> result) {
        int taskNo = KeyServiceConstants.UNKNOWN_ERROR_TASK_NO;
        List<ResultInfo> resultList = new ArrayList<>();
        //1. 解析请求
        CARequest cr2;
        CaEntity caEntity;
        try {
            cr2 = checkCaRequest(content);

            // 获取taskNo 每次请求唯一标识
            if (cr2.getKsRequest().getTaskNo() != null) {
                taskNo = cr2.getKsRequest().getTaskNo().getValue().intValue();
            }
            // 设置响应解析结果
            result.setBizId(String.valueOf(taskNo));
            result.setBiz(getServiceType(cr2));

            //2. 根据请求中CA信息，验证CA
            EntName entCaName = cr2.getKsRequest().getCaName();
            byte[] keyHash = entCaName.getEntPubKeyHash().getOctets();
            String keyHashStr = Base64.encodeBase64String(keyHash);
            String certSn = CertUtil.getHexSnOfCert(entCaName.getSerialNumber());
            // 获取签名值
            byte[] signature = cr2.getSignatureValue().getOctets();
            // 获取做签名的原文
            byte[] toBeSigned = cr2.getKsRequest().getEncoded();

            caEntity = checkCa(certSn, keyHashStr, signature, toBeSigned, result);

            // 检查license
            checkLicense();
        } catch (Exception e) {
            log.error("Failed to verify request!", e);
            // 根据异常类型设置错误码
            int errorCode;
            if (e instanceof BaseException) {
                errorCode = Integer.parseInt(((BaseException) e).getSecondCode());
            } else {
                errorCode = KeyServiceConstants.UNKNOWN_ERROR_CODE;
            }
            result.setErrorMessage(e.getMessage());
            resultList.add(new ResultInfo(errorCode, e.getMessage()));
            return new KeyResponseInfo(taskNo, resultList);
        }

        // 3. 执行业务请求,可以存在多个请求
        int componentCount = cr2.getKsRequest().getRequestList().size();
        for (int index = 0; index < componentCount; index++) {
            try {
                Request request = Request.getInstance(cr2.getKsRequest().getRequestList().getObjectAt(index));
                IKeyBiz keyService;
                KeyMsgInfo keyMsgInfo;
                switch (request.getTagNo()) {
                    case 0:
                        keyService = new KeyApplyBiz();
                        ApplyKeyReq akr = (ApplyKeyReq) request.getValue();
                        keyMsgInfo = msgAdapter.convertEscrowMsg(caEntity, akr);
                        break;
                    case 1:
                        keyService = new KeyRestoreBiz();
                        RestoreKeyReq rkr = (RestoreKeyReq) request.getValue();
                        keyMsgInfo = msgAdapter.convertRestoreMsg(rkr);
                        break;
                    case 2:
                        keyService = new KeyRevokeBiz();
                        RevokeKeyReq rkr2 = (RevokeKeyReq) request.getValue();
                        keyMsgInfo = msgAdapter.convertRevokeMsg(rkr2);
                        break;
                    default:
                        throw KmValidationError.KEY_REQUEST_NONSUPPORT_ERROR.toException(UNSUPPORTED_REQUEST_TYPE);
                }
                //添加参数
                keyMsgInfo.setTaskNo(taskNo);
                keyMsgInfo.setCaEntity(caEntity);

                // 设置响应解析结果
                result.setCertId(keyMsgInfo.getUserEncCertNo());

                // 执行密钥服务
                keyService.bizBefore(keyMsgInfo);
                ResultInfo resultInfo = keyService.bizExecute(keyMsgInfo);
                resultList.add(resultInfo);
            } catch (BaseException e) {
                log.error("Failed to execute Key Service, errorCode={}, reason={}", e.getCode(), e.getDesc(), e);
                result.setErrorMessage(e.getMessage());
                resultList.add(new ResultInfo(Integer.valueOf(e.getSecondCode()), e.getCode() + e.getDesc()));
            } catch (Exception e) {
                log.error("Failed to execute Key Service, reason={}", e.getMessage(), e);
                result.setErrorMessage(e.getMessage());
                resultList.add(new ResultInfo(KeyServiceConstants.UNKNOWN_ERROR_CODE, e.getMessage()));
            }
        }

        return new KeyResponseInfo(taskNo, resultList);
    }

    private CARequest checkCaRequest(byte[] content) {

        CARequest caRequest;
        try {
            caRequest = CARequest.getInstance(content);
        } catch (Exception ex) {
            throw KmValidationError.KEY_REQUEST_DECODE_ERROR.toException(NON_STANDARD_REQUEST_UNPARSABLE);
        }
        if (caRequest.getKsRequest().getVersion().getValue().intValue() != 1) {
            throw KmValidationError.REQUEST_VERSION_NONSUPPORT_ERROR.toException(UNSUPPORTED_MESSAGE_VERSION);
        }
        return caRequest;
    }

    /**
     * 获取请求中获取的具体服务类型
     *
     * @return
     */
    private String getServiceType(CARequest caRequest) {
        ASN1Sequence requestList = caRequest.getKsRequest().getRequestList();
        // 校验list是否为空
        if (requestList.size() < 1) {
            throw KmValidationError.KEY_REQUEST_MISSING_STRUCTURE_ERROR.toException();
        }
        Request request = Request.getInstance(requestList.getObjectAt(0));
        switch (request.getTagNo()) {
            case 0:
                return CaKeyServiceType.KEY_APPLY.getDesc();
            case 1:
                return CaKeyServiceType.KEY_RESTORE.getDesc();
            case 2:
                return CaKeyServiceType.KEY_REVOKE.getDesc();
            default:
                throw KmValidationError.KEY_REQUEST_NONSUPPORT_ERROR.toException(UNSUPPORTED_REQUEST_TYPE);
        }
    }

    @Override
    public String getType() {
        return SERVICE_TYPE;
    }

}
