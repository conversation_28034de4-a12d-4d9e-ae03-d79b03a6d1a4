package kl.npki.km.core.biz.trans.service.handler.impl;

import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.annotation.KlTransactional;
import kl.npki.base.core.biz.mainkey.service.MainKeyManager;
import kl.npki.base.core.common.trace.InvocationType;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.base.core.event.EntityChangeEvent;
import kl.npki.base.core.event.EntityChangeEventManager;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.km.core.biz.key.model.CertKeyEntity;
import kl.npki.km.core.biz.key.model.CurrentKeyEntity;
import kl.npki.km.core.biz.key.model.HistoryKeyEntity;
import kl.npki.km.core.biz.trans.model.KeyMsgInfo;
import kl.npki.km.core.biz.trans.model.RestoreMsgInfo;
import kl.npki.km.core.biz.trans.model.ResultInfo;
import kl.npki.km.core.common.constants.CaKeyServiceType;
import kl.npki.km.core.exception.KmValidationError;
import kl.npki.km.core.mapper.ConvertServiceHelper;
import kl.npki.km.core.repository.IKeyCurrentRepository;
import kl.npki.km.core.repository.IKeyHistoryRepository;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Author: guoq
 * @Date: 2022/9/2
 * @description: 密钥恢复服务
 */
public class KeyRestoreBiz extends AbstractKeyBiz {
    private static final Logger log = LoggerFactory.getLogger(KeyRestoreBiz.class);

    private final IKeyCurrentRepository keyCurrentRepository;
    private final IKeyHistoryRepository keyHistoryRepository;
    private final MainKeyManager mainKeyManager;

    public KeyRestoreBiz() {
        this.keyCurrentRepository = RepositoryFactory.get(IKeyCurrentRepository.class);
        this.keyHistoryRepository = RepositoryFactory.get(IKeyHistoryRepository.class);
        this.mainKeyManager = MainKeyManager.getInstance();
    }

    @KlTransactional
    @Override
    public ResultInfo bizExecute(KeyMsgInfo keyMsg) {
        RestoreMsgInfo restoreMsg = (RestoreMsgInfo) keyMsg;
        String userEncCertNo = restoreMsg.getUserEncCertNo();
        //1.1 查找密钥对 从在用库查找密钥对
        CertKeyEntity keyEntity = keyCurrentRepository.searchKeyBySnAndType(userEncCertNo, restoreMsg.getKeyType().getAlgoName());
        // 待推送的密钥变更轨迹实体
        CurrentKeyEntity currentKeyEntity = null;
        boolean needMoveCurrent = false;
        if (ObjectUtils.isEmpty(keyEntity)) {
            log.warn("The key(encCertSN={}) not found in Current Repository.", userEncCertNo);
            // 1.2 在用库没有,从历史库查找
            keyEntity = keyHistoryRepository.searchKeyBySnAndType(userEncCertNo, restoreMsg.getKeyType().getAlgoName());
            needMoveCurrent = true;
            CheckUtils.notNull(keyEntity,
                KmValidationError.KEY_RESTORE_SN_NOT_EXISTING_ERROR.toException(userEncCertNo));
        } else {
            // 如果在当前库中，但状态不正常，则表明属于待归档的数据，状态需要变更至正常
            currentKeyEntity = (CurrentKeyEntity) keyEntity;

            boolean isRevoked = EntityStatus.REVOKED.getId().equals(currentKeyEntity.getKeyStatus());
            boolean isExpired = EntityStatus.EXPIRED.getId().equals(currentKeyEntity.getKeyStatus());
            if (isExpired) {
                // 延期
                currentKeyEntity.extendedValidEnd(restoreMsg.getCaEntity().getLimitYear());
            }
            if (isRevoked || isExpired) {
                // 更新密钥状态
                currentKeyEntity.updateStatus(EntityStatus.NORMAL.getId());
            }
        }
        // 2. 恢复密钥对 解密密钥
        byte[] plainPriKey = mainKeyManager.decryptPriKeyByMainKey(keyEntity.getMainKeyId(),
            keyEntity.getPrivateKeyByte());
        keyEntity.setPlainPriKeyByte(plainPriKey);

        // 3. 当前密钥已经被废除，需要迁移到正式库
        if (needMoveCurrent) {
            try {
                currentKeyEntity = recoverHistoryKey(restoreMsg, (HistoryKeyEntity) keyEntity);
            } catch (Exception e) {
                log.error("Failed to migrate the history key(encCertSN={}) to Current Repository.", userEncCertNo, e);
            }
        }

        // 4. 推送密钥实体变更通知
        EntityChangeEventManager.getInstance().publishEvent(EntityChangeEvent.newInstance(currentKeyEntity,
            CaKeyServiceType.KEY_RESTORE.name(),
            InvocationType.TCP_SERVICE_INTERFACE));

        // 5. 返回响应
        ResultInfo rm = new ResultInfo(restoreMsg, keyEntity, getType());
        if (log.isDebugEnabled()) {
            log.debug("Successfully build KeyRestore result, encCertSN={}", userEncCertNo);
        }
        return rm;
    }

    @KlTransactional
    public CurrentKeyEntity recoverHistoryKey(RestoreMsgInfo restoreMsg, HistoryKeyEntity keyEntity) {
        CurrentKeyEntity currentKeyEntity = ConvertServiceHelper.getConvertService().convert(keyEntity,
            CurrentKeyEntity.class);
        currentKeyEntity.extendedValidEnd(restoreMsg.getCaEntity().getLimitYear());
        currentKeyEntity.save();
        keyEntity.delete();

        return currentKeyEntity;
    }

    @Override
    public CaKeyServiceType getType() {
        return CaKeyServiceType.KEY_RESTORE;
    }
}
