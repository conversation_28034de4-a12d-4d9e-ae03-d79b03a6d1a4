package kl.npki.km.core.biz.trans.service.handler;

import kl.nbase.security.asn1.ASN1ObjectIdentifier;
import kl.nbase.security.asn1.DEROctetString;
import kl.nbase.security.asn1.x509.AlgorithmIdentifier;
import kl.nbase.security.constants.PaddingEnum;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.entity.algo.BlockSymAlgo;
import kl.npki.base.core.constant.EncryptConstant;
import kl.npki.km.core.common.constants.CaConstants;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 密钥封装工厂抽象类
 * <AUTHOR>
 */
public abstract class AbstractEnvelopedKeyFactory {

    /**
     * 构造对称加密算法Oid, 用于定制
     * 1. 商密检测平台请求中使用的OID为Sm4 算法，未指明分组模式，默认使用SM4/CBC/P5 响应中的OID也需要为Sm4 算法
     * 2. 老CA算法oid结构，不能放置ivData，解码报错
     *
     * @param symAlgo
     * @param contentEncryptionOid
     * @param iv
     * @return
     */
    public static AlgorithmIdentifier buildSymEncryptAlgorithmIdentifier(BlockSymAlgo symAlgo,
                                                                          ASN1ObjectIdentifier contentEncryptionOid,
                                                                          byte[] iv) {
        if (KmConfigWrapper.getKmConf().isTestCenter() && EncryptConstant.ID_GM_ALGO_SM4.equals(contentEncryptionOid.toString())) {
            return new AlgorithmIdentifier(contentEncryptionOid, new DEROctetString(iv));
        }
        if (ObjectUtils.isEmpty(symAlgo)) {
            return new AlgorithmIdentifier(CaConstants.ID_CN_GMJ_ALGO_FZ_OID);
        }
        return new AlgorithmIdentifier(symAlgo.getOid());

    }
    /**
     * 构建对称算法,此方法内容做了定制
     * 1. 商密检测平台请求中使用的OID为Sm4 算法，未指明分组模式，默认使用SM4/CBC/P5
     * 2. 老CA不开启密钥加密传输时使用自定义oid
     *
     * @param oid
     * @return
     */
    public static BlockSymAlgo buildSymAlgo(AsymAlgo keyType, String oid) {
        // 检测中心使用SM4/CBC/PADDING5
        if (KmConfigWrapper.getKmConf().isTestCenter() && EncryptConstant.ID_GM_ALGO_SM4.equals(oid)) {
            return BlockSymAlgo.SM4_CBC_PADDING5;
        }
        if (CaConstants.ID_CN_GMJ_ALGO_FZ.equals(oid)) {
            // 如果ca传不加密的对称算法，默认返回空
            return null;
        }
        BlockSymAlgo blockSymAlgo = BlockSymAlgo.valueOf(oid);
        // SM2 SM4_ECB 使用NO_Padding填充方式
        if (keyType.isSM2() && BlockSymAlgo.SM4_ECB_PADDING5.equals(blockSymAlgo)) {
            blockSymAlgo.setPadding(PaddingEnum.NO_PADDING.getDesc());
        }
        return blockSymAlgo;
    }
}
