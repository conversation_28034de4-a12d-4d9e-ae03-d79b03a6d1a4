package kl.npki.km.core.biz.stat.model;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 密钥状态统计信息
 * <AUTHOR>
 */
public class KeyStatusCountInfo implements Serializable {
    @Schema(description =  "密钥状态对应的密钥数量")
    private Integer count;
    @Schema(description =  "密钥状态， 已生效: 0, 已废除: -2 , 已过期: -3")
    private Integer keyStatus;

    public KeyStatusCountInfo(Integer count, Integer keyStatus) {
        this.count = count;
        this.keyStatus = keyStatus;
    }

    public Integer getCount() {
        return count;
    }

    public KeyStatusCountInfo setCount(Integer count) {
        this.count = count;
        return this;
    }

    public Integer getKeyStatus() {
        return keyStatus;
    }

    public KeyStatusCountInfo setKeyStatus(Integer keyStatus) {
        this.keyStatus = keyStatus;
        return this;
    }
}
