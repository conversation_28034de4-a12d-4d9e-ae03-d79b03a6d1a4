package kl.npki.km.core.biz.trans.service.msg.impl.respond;

import kl.nbase.security.asn1.ASN1Object;
import kl.nbase.security.asn1.custom.gm.gm0014.km.response.Respond;
import kl.nbase.security.asn1.custom.gm.gm0014.km.response.RetKeyRespond;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.utils.PriKeyUtils;
import kl.npki.km.core.biz.trans.model.ResultInfo;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;

import java.security.PublicKey;

/**
 * @Author: guoq
 * @Date: 2024/11/22
 * @description:
 */
public abstract class AbstractKeyRespondCreator implements KeyRespondCreator {

    /**
     * 转换私钥结构
     *
     * @param plainPriKey
     * @param keyType
     * @param userPubKey
     * @return
     */
    protected byte[] formatPrivateKey(byte[] plainPriKey, AsymAlgo keyType, PublicKey userPubKey) {
        if (KmConfigWrapper.getKmConf().isTestCenter() && AsymAlgo.SM2.equals(keyType)) {
            return PriKeyUtils.adaptSm2TestCenterPrivateKey(plainPriKey, userPubKey);
        }
        // 构造
        return PriKeyUtils.adaptPrivateKey(plainPriKey, keyType);
    }

    @Override
    public ASN1Object createRetKeyRespond(ResultInfo resultInfo) throws Exception{
        // 解析结果
        RetKeyRespond retKeyRespond =
            createRetKeyRespondWithStandard(resultInfo);

        // 构造响应
        return new Respond(resultInfo.getServiceType().getTagNo(), retKeyRespond);
    }

    /**
     * 生成密钥响应
     * @param resultInfo
     * @return
     * @throws Exception
     */
    protected abstract RetKeyRespond createRetKeyRespondWithStandard(ResultInfo resultInfo) throws Exception;

}
