package kl.npki.km.core.biz.sks.model;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * <AUTHOR>
 * @date 2022/11/28 15:14
 * @Description: sks key批量查询密钥
 * 使用update_time 为了及时查询状态更新的密钥
 */
public class SksKeyBatchSearchInfo {

    /**
     * 密钥对 起始时间 (update_time >= startData)
     */
    private LocalDateTime startTime;

    /**
     * 密钥对 结束时间 (update_time <= endData)
     */
    private LocalDateTime endTime;

    public SksKeyBatchSearchInfo() {
    }

    public SksKeyBatchSearchInfo(Long startTime, Long endTime) {
        this.startTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(startTime), ZoneId.systemDefault());
        this.endTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(endTime), ZoneId.systemDefault());
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
}
