package kl.npki.km.core.biz.home.model;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.km.core.biz.stat.model.KeyCountInfo;

import java.io.Serializable;
import java.util.*;

/**
 * 密钥库分布统计情况
 *
 * <AUTHOR>
 */
public class KeyStashDistributionInfo implements Serializable {

    @Schema(description = "密钥库分布统计结果")
    private List<KeyStashDistributionItem> keyStashDistributionItemList;

    public KeyStashDistributionInfo(List<KeyCountInfo> current, List<KeyCountInfo> history, List<KeyCountInfo> pool) {
        // 转化为key->values形式
        Map<String, Integer> currentMap = new LinkedHashMap<>();
        Map<String, Integer> historyMap = new LinkedHashMap<>();
        Map<String, Integer> poolMap = new LinkedHashMap<>();
        // 统计出共有几种密钥类型
        Set<String> keyTypes = new LinkedHashSet<>();
        for (KeyCountInfo keyCountInfo : current) {
            String keyType = keyCountInfo.getKeyType();
            keyTypes.add(keyType);
            currentMap.put(keyType, keyCountInfo.getCount());
        }
        for (KeyCountInfo keyCountInfo : history) {
            String keyType = keyCountInfo.getKeyType();
            keyTypes.add(keyType);
            historyMap.put(keyType, keyCountInfo.getCount());
        }
        for (KeyCountInfo keyCountInfo : pool) {
            String keyType = keyCountInfo.getKeyType();
            keyTypes.add(keyType);
            poolMap.put(keyType, keyCountInfo.getCount());
        }
        this.keyStashDistributionItemList = new ArrayList<>(keyTypes.size());
        // 处理数据，便于前端展示
        for (String keyType : keyTypes) {
            KeyStashDistributionItem item = new KeyStashDistributionItem();
            item.setKeyType(keyType);
            if (currentMap.get(keyType) != null) {
                item.setCurrentKeyCount(currentMap.get(keyType));
            }
            if (historyMap.get(keyType) != null) {
                item.setHistoryKeyCount(historyMap.get(keyType));
            }
            if (poolMap.get(keyType) != null) {
                item.setPoolKeyCount(poolMap.get(keyType));
            }
            this.keyStashDistributionItemList.add(item);
        }
    }

    public List<KeyStashDistributionItem> getKeyStashDistributionItemList() {
        return keyStashDistributionItemList;
    }

    public KeyStashDistributionInfo setKeyStashDistributionItemList(List<KeyStashDistributionItem> keyStashDistributionItemList) {
        this.keyStashDistributionItemList = keyStashDistributionItemList;
        return this;
    }
}
