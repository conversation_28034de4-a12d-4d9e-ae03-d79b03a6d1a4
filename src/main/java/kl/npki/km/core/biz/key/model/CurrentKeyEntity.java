package kl.npki.km.core.biz.key.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.km.core.biz.trans.model.EscrowMsgInfo;
import kl.npki.km.core.common.RepositoryHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author: guoq
 * @Date: 2022/9/15
 * @description: 当前库实体
 */
public class CurrentKeyEntity extends CertKeyEntity {

    private static final long serialVersionUID = -7171002203971283144L;
    private Integer keyStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updateTime;

    public Integer getKeyStatus() {
        return keyStatus;
    }

    public void setKeyStatus(Integer keyStatus) {
        this.keyStatus = keyStatus;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 给密钥实体添加用户信息
     *
     * @param escrowMsg
     */
    public void setEscrowMsg(EscrowMsgInfo escrowMsg) {
        setEncCertSn(escrowMsg.getUserEncCertNo());
        setValidStart(escrowMsg.getNotBefore());
        setValidEnd(escrowMsg.getNotAfter());
        setSubject(escrowMsg.getUsername());
        setMediumSn(escrowMsg.getMediumSn());
        if (CollectionUtils.isNotEmpty(escrowMsg.getExtendInfoList())) {
            setUserInfoExs(convertUserInfoExs(escrowMsg.getExtendInfoList()));
        }
        setCaId(escrowMsg.getCaEntity().getId());
    }

    /**
     * 保存实体
     *
     * @return 主键
     */
    public Long save() {
        if (!ObjectUtils.isEmpty(getId())) {
            // 保证密钥记录ID 唯一
            setId(null);
        }
        setKeyStatus(EntityStatus.NORMAL.getId());
        Long entityId = RepositoryHelper.getKeyCurrentRepository().escrowKey(this);
        this.setId(entityId);

        return entityId;
    }

    /**
     * 延期 有效截止时间
     */
    public void extendedValidEnd(int limitYear) {
        LocalDateTime now = LocalDateTime.now();
        this.setValidEnd(now.plusYears(limitYear));
    }

    /**
     * 更新实体
     *
     * @return 更新是否成功
     */
    public boolean updateStatus(Integer keyStatus) {
        setKeyStatus(keyStatus);
        setUpdateTime(LocalDateTime.now());
        return RepositoryHelper.getKeyCurrentRepository().updateStatus(this);
    }

    public boolean delete() {
        return RepositoryHelper.getKeyCurrentRepository().deleteKey(getId(), getEncCertSn());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        CurrentKeyEntity that = (CurrentKeyEntity) o;
        return keyStatus.equals(that.keyStatus) &&
            updateTime.equals(that.updateTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), keyStatus, updateTime);
    }

    @Override
    public String toString() {
        return "CurrentKeyEntity{" +
            "keyStatus=" + keyStatus +
            ", updateTime=" + updateTime +
            ", caId=" + caId +
            ", mediumSn='" + mediumSn + '\'' +
            ", subject='" + subject + '\'' +
            ", validEnd=" + validEnd +
            ", validStart=" + validStart +
            ", encCertSn='" + encCertSn + '\'' +
            ", userInfoExs='" + userInfoExs + '\'' +
            ", id=" + id +
            ", keyIndex='" + keyIndex + '\'' +
            ", publicKey='" + publicKey + '\'' +
            ", privateKey='" + privateKey + '\'' +
            ", plainPriKey='" + plainPriKey + '\'' +
            ", mainKeyId=" + mainKeyId +
            ", keyType='" + keyType + '\'' +
            ", createTime=" + createTime +
            '}';
    }
}
