package kl.npki.km.core.biz.trans.service.msg;

import kl.npki.km.core.biz.ca.model.CaEntity;
import kl.npki.km.core.biz.trans.model.EscrowMsgInfo;
import kl.npki.km.core.biz.trans.model.RestoreMsgInfo;
import kl.npki.km.core.biz.trans.model.RevokeMsgInfo;

/**
 * @Author: guoq
 * @Date: 2022/9/5
 * @description: 请求消息格式转换适配器
 */
public interface MsgReqAdapter<T1, T2, T3> {

    /**
     * 转换CA密钥申请对象为KM内部对象
     * @param caEntity
     * @param akr
     * @return
     */
    EscrowMsgInfo convertEscrowMsg(CaEntity caEntity, T1 akr);

    /**
     * 转换CA密钥恢复对象为KM内部恢复对象
     * @param rkr
     * @return
     */
    RestoreMsgInfo convertRestoreMsg(T2 rkr);
    /**
     * 转换CA密钥撤销对象为KM内部撤销对象
     * @param rkr
     * @return
     */
    RevokeMsgInfo convertRevokeMsg(T3 rkr);

}
