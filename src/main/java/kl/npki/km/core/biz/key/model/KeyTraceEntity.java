package kl.npki.km.core.biz.key.model;

import kl.nbase.log.utils.JacksonFiltersUtil;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.utils.ColumnUtil;
import kl.npki.km.core.repository.IKeyTraceRepositoryImpl;

import java.time.LocalDateTime;

/**
 * 密钥轨迹实体
 * <AUTHOR>
 */
public class KeyTraceEntity {
    private Long id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 操作者的用户id
     */
    private Long userId;

    /**
     * 操作者名称
     */
    private String operatorName;

    /**
     * 发起者IP
     */
    private String sourceIp;

    /**
     * 调用方式
     */
    private String invocationType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 变更事件/动作/业务类型
     */
    private String bizName;

    /**
     * 业务流水号
     */
    private String bizId;

    /**
     * 实体序列号，用于分表
     */
    private String entitySn;

    /**
     * 密钥类型
     */
    private String keyType;

    /**
     * json格式的实体数据内容
     */
    private String entityInfo;

    /**
     * 操作结果
     */
    private String operatorResult;

    /**
     * 实体状态
     */
    private Integer entityStatus;

    /**
     * 完整性保护摘要值
     */
    private String fullDataHash;

    /**
     * 扩展项1
     */
    private String ext1;

    /**
     * 扩展项2
     */
    private String ext2;

    /**
     * 扩展项3
     */
    private String ext3;

    private final IKeyTraceRepositoryImpl keyTraceRepository;

    /**
     * entityInfo字段中 json内容数据中，无需存储的键值信息
     */
    private static final String[] ENTITY_INFO_NEED_IGNORE_FIELDS;
    static {
        String publicKeyColumnName = ColumnUtil.getFieldName(KeyEntity::getPublicKey);
        String keyIndexColumnName = ColumnUtil.getFieldName(KeyEntity::getKeyIndex);
        ENTITY_INFO_NEED_IGNORE_FIELDS = new String[]{publicKeyColumnName, keyIndexColumnName};
    }

    public KeyTraceEntity() {
        this.keyTraceRepository = RepositoryFactory.get(IKeyTraceRepositoryImpl.class);
    }

    public void parseHistoryKeyEntity(HistoryKeyEntity keyEntity) {
        parseKeyEntity(keyEntity);
    }

    public void parseCurrentKeyEntity(CurrentKeyEntity keyEntity) {
        parseKeyEntity(keyEntity);
        this.entityStatus = keyEntity.getKeyStatus();
    }

    private void parseKeyEntity(CertKeyEntity keyEntity) {
        this.entityInfo = JacksonFiltersUtil.transJsonFilter(keyEntity, ENTITY_INFO_NEED_IGNORE_FIELDS);
        this.entitySn = keyEntity.encCertSn;
        this.keyType = keyEntity.keyType;
    }

    public void save() {
        keyTraceRepository.insert(this);
    }

    public Long getId() {
        return id;
    }

    public KeyTraceEntity setId(Long id) {
        this.id = id;
        return this;
    }

    public String getTenantId() {
        return tenantId;
    }

    public KeyTraceEntity setTenantId(String tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public Long getUserId() {
        return userId;
    }

    public KeyTraceEntity setUserId(Long userId) {
        this.userId = userId;
        return this;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public KeyTraceEntity setOperatorName(String operatorName) {
        this.operatorName = operatorName;
        return this;
    }

    public String getSourceIp() {
        return sourceIp;
    }

    public KeyTraceEntity setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
        return this;
    }

    public String getInvocationType() {
        return invocationType;
    }

    public KeyTraceEntity setInvocationType(String invocationType) {
        this.invocationType = invocationType;
        return this;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public String getEntitySn() {
        return entitySn;
    }

    public KeyTraceEntity setEntitySn(String entitySn) {
        this.entitySn = entitySn;
        return this;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public KeyTraceEntity setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getBizName() {
        return bizName;
    }

    public KeyTraceEntity setBizName(String bizName) {
        this.bizName = bizName;
        return this;
    }

    public String getBizId() {
        return bizId;
    }

    public KeyTraceEntity setBizId(String bizId) {
        this.bizId = bizId;
        return this;
    }

    public String getEntityInfo() {
        return entityInfo;
    }

    public KeyTraceEntity setEntityInfo(String entityInfo) {
        this.entityInfo = entityInfo;
        return this;
    }

    public String getOperatorResult() {
        return operatorResult;
    }

    public KeyTraceEntity setOperatorResult(String operatorResult) {
        this.operatorResult = operatorResult;
        return this;
    }

    public Integer getEntityStatus() {
        return entityStatus;
    }

    public KeyTraceEntity setEntityStatus(Integer entityStatus) {
        this.entityStatus = entityStatus;
        return this;
    }

    public String getFullDataHash() {
        return fullDataHash;
    }

    public KeyTraceEntity setFullDataHash(String fullDataHash) {
        this.fullDataHash = fullDataHash;
        return this;
    }

    public String getExt1() {
        return ext1;
    }

    public KeyTraceEntity setExt1(String ext1) {
        this.ext1 = ext1;
        return this;
    }

    public String getExt2() {
        return ext2;
    }

    public KeyTraceEntity setExt2(String ext2) {
        this.ext2 = ext2;
        return this;
    }

    public String getExt3() {
        return ext3;
    }

    public KeyTraceEntity setExt3(String ext3) {
        this.ext3 = ext3;
        return this;
    }
}
