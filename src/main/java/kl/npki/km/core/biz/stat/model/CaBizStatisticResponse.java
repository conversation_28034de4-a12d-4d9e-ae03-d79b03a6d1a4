package kl.npki.km.core.biz.stat.model;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.nbase.i18n.i18n.I18nUtil;

import java.util.List;

import static kl.npki.km.core.common.constants.KMCoreI18N.TOTAL_STATISTIC;

/**
 * CA业务统计响应
 * <AUTHOR>
 */
public class CaBizStatisticResponse {
    @Schema(description =  "各CA业务统计情况")
    private List<CaBizStatisticInfo> caBizStatisticInfoList;

    @Schema(description =  "合计一栏的数据")
    private CaBizStatisticInfo totalInfo;

    public CaBizStatisticResponse(List<CaBizStatisticInfo> caBizStatisticInfoList) {
        this.caBizStatisticInfoList = caBizStatisticInfoList;

        this.totalInfo = new CaBizStatisticInfo();
        totalInfo.setCaName(I18nUtil.tr(TOTAL_STATISTIC));

        int totalKeyApplySuccess = 0;
        int totalKeyApplyFail = 0;
        int totalKeyDestroySuccess = 0;
        int totalKeyDestroyFail = 0;
        int totalKeyRestoreSuccess = 0;
        int totalKeyRestoreFail = 0;

        for(CaBizStatisticInfo caBizStatisticInfo : caBizStatisticInfoList){

            totalKeyApplySuccess += caBizStatisticInfo.getKeyApplySuccess();
            totalKeyApplyFail += caBizStatisticInfo.getKeyApplyFail();

            totalKeyDestroySuccess += caBizStatisticInfo.getKeyDestroySuccess();
            totalKeyDestroyFail += caBizStatisticInfo.getKeyDestroyFail();

            totalKeyRestoreSuccess +=caBizStatisticInfo.getKeyRestoreSuccess();
            totalKeyRestoreFail += caBizStatisticInfo.getKeyRestoreFail();
        }

        totalInfo.setKeyApplySuccess(totalKeyApplySuccess);
        totalInfo.setKeyApplyFail(totalKeyApplyFail);

        totalInfo.setKeyDestroySuccess(totalKeyDestroySuccess);
        totalInfo.setKeyDestroyFail(totalKeyDestroyFail);

        totalInfo.setKeyRestoreSuccess(totalKeyRestoreSuccess);
        totalInfo.setKeyRestoreFail(totalKeyRestoreFail);
    }

    public List<CaBizStatisticInfo> getCaBizStatisticInfoList() {
        return caBizStatisticInfoList;
    }

    public CaBizStatisticResponse setCaBizStatisticInfoList(List<CaBizStatisticInfo> caBizStatisticInfoList) {
        this.caBizStatisticInfoList = caBizStatisticInfoList;
        return this;
    }

    public CaBizStatisticInfo getTotalInfo() {
        return totalInfo;
    }

    public CaBizStatisticResponse setTotalInfo(CaBizStatisticInfo totalInfo) {
        this.totalInfo = totalInfo;
        return this;
    }
}
