package kl.npki.km.core.biz.trans.model;


import kl.nbase.security.asn1.x509.AlgorithmIdentifier;

import java.security.PublicKey;

/**
 * @Author: guoq
 * @Date: 2022/9/2
 * @description: 返回密钥消息
 */
public class RetKeyMsgInfo extends KeyMsgInfo {

    /*
     * 用于做数字信封加密私钥的公钥
     */
    protected PublicKey userPubKey;

    /**
     * 响应中的对称算法
     */
    protected AlgorithmIdentifier symAlgo;

    /**
     * 响应中的非对称算法
     */
    protected AlgorithmIdentifier asymAlgo;

    /**
     * 响应中的摘要算法
     */
    protected AlgorithmIdentifier digestAlgo;

    public PublicKey getUserPubKey() {
        return userPubKey;
    }

    public void setUserPubKey(PublicKey userPubKey) {
        this.userPubKey = userPubKey;
    }

    public AlgorithmIdentifier getSymAlgo() {
        return symAlgo;
    }

    public void setSymAlgo(AlgorithmIdentifier symAlgo) {
        this.symAlgo = symAlgo;
    }

    public AlgorithmIdentifier getAsymAlgo() {
        return asymAlgo;
    }

    public void setAsymAlgo(AlgorithmIdentifier asymAlgo) {
        this.asymAlgo = asymAlgo;
    }

    public AlgorithmIdentifier getDigestAlgo() {
        return digestAlgo;
    }

    public void setDigestAlgo(AlgorithmIdentifier digestAlgo) {
        this.digestAlgo = digestAlgo;
    }
}
