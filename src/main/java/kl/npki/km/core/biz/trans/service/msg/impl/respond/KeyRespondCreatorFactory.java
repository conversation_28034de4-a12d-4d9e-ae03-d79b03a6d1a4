package kl.npki.km.core.biz.trans.service.msg.impl.respond;

import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.constant.CaKeyResponseStandardVersionEnum;
import kl.npki.km.core.common.constants.CaVersionTypeEnum;

/**
 * @Author: guoq
 * @Date: 2024/11/25
 * @description: 密钥响应工厂类
 */
public class KeyRespondCreatorFactory {

    public static KeyRespondCreator createKeyRespondCreator(CaKeyResponseStandardVersionEnum standardVersion, AsymAlgo keyType, CaVersionTypeEnum caVersion) {
        if (useGbt2023Standard(standardVersion, keyType)) {
            return new KeyRespondCreator2023StandardImpl();
        } else if (usePQStandard(keyType, caVersion)) {
            return new KeyRespondCreatorPqcStandardImpl();
        } else {
            return new KeyRespondCreator2012StandardImpl();
        }
    }

    /**
     * 是否使用gbt-2023规范
     * @param standardVersion
     * @param keyType
     * @return
     */
    private static boolean useGbt2023Standard(CaKeyResponseStandardVersionEnum standardVersion, AsymAlgo keyType) {
        return CaKeyResponseStandardVersionEnum.GMT00142023 == standardVersion && keyType.isSM2();
    }

    /**
     * 是否使用PQC信封格式
     * 默认9版本CA 使用PQC信封格式
     * @param keyType
     * @return
     */
    private static boolean usePQStandard(AsymAlgo keyType, CaVersionTypeEnum caVersion) {
        return CaVersionTypeEnum.KOAL_V9_X_X.equals(caVersion) && keyType.isPQC();
    }
}
