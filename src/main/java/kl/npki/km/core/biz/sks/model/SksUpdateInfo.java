package kl.npki.km.core.biz.sks.model;

import java.io.Serializable;

/**
 * SKS密钥更新信息
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 * @date 2025/7/9
 */
public class SksUpdateInfo implements Serializable {

    private static final long serialVersionUID = 3162436718213108997L;
    /**
     * 密钥个人片段sm3Hash经Base64编码
     */
    private String clientKeyHash;

    /**
     * 期望更新的状态
     */
    private Integer status;

    public String getClientKeyHash() {
        return clientKeyHash;
    }

    public void setClientKeyHash(String clientKeyHash) {
        this.clientKeyHash = clientKeyHash;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
