package kl.npki.km.core.exception;

import kl.nbase.exception.interfaces.IValidationError;
import kl.npki.km.core.common.constants.KMCoreI18N;

import java.util.Locale;

/**
 * @Author: guoq
 * @Date: 2022/8/23
 * @description: 校验错误码 状态异常
 */
public enum KmValidationError implements IValidationError, ErrorCode {
    /**
     * 1：CA证书
     */
    CA_ID_CERT_EXISTING_ERROR("100", "CA身份证书已经存在！"),
    CA_NOT_EXISTING_ERROR("101", "CA记录不存在！"),
    CA_STATUS_ABNORMAL_ERROR("102", "CA已经被冻结或者废除，无法正常使用！"),
    CA_ID_CERT_NOT_EXISTING_ERROR("103", "CA身份证书不存在！"),
    SIGNED_VERIFY_ERROR("104", "请求签名验签不通过！"),
    SIGN_ERROR("105", "签名失败！"),
    CA_UNFREEZE_ERROR("106", "CA解冻失败，无法解冻！"),
    CA_RECOVERY_ERROR("107", "CA恢复失败，当前KM配置不容许恢复已废除CA！"),
    HMAC_VERIFY_ERROR("108", "请求HMAC验证不通过！"),
    CA_NAME_EXISTING_ERROR("109", "该CA名称已经存在！"),
    CA_ID_CERT_NOT_SIGNING_CERT("110", "CA身份证书不是签名证书！"),
    CA_SN_OR_KEY_HASH_OR_NAME_EXISTING_ERROR("111", "已存在相同公钥或证书序列号或名称的CA身份证书！"),

    /**
     * 2：密钥服务
     */
    PROTOCOL_UNKNOW_ERROR("200", "未知的密钥服务消息请求类型！"),
    KEY_REQUEST_DECODE_ERROR("201", "密钥服务消息请求ASN解码失败！"),
    REQUEST_VERSION_NONSUPPORT_ERROR("202", "不支持的请求版本类型！"),
    KEY_TYPE_NONSUPPORT_ERROR("203", "密钥服务不支持的密钥类型！"),
    PUBLIC_KEY_LACK_ERROR("204", "密钥请求缺少的必须的用户公钥参数！"),
    SYMMETRIC_NONSUPPORT_ERROR("205", "密钥服务不支持的对称算法类型！"),
    CERT_SN_LACK_ERROR("206", "密钥请求缺少的必须的用户证书序列号参数！"),
    KEY_REQUEST_NONSUPPORT_ERROR("207", "不支持的密钥请求类型！"),
    MEDIUM_SN_LACK_ERROR("208", "密钥请求缺少必要的介质序列号！"),
    MEDIUM_SN_EXISTING_ERROR("209", "密钥请求介质序列号重复！"),
    VALIDITY_ABNORMAL_ERROR("210", "密钥失效时间不能小于生效时间！"),
    CA_FAST_API_NOT_ENABLED("211", "未开启CA快速接入功能！"),
    KEY_REQUEST_MISSING_STRUCTURE_ERROR("212", "密钥服务请求缺少密钥申请信息！"),
    SYSTEM_AUTH_TOKEN_NOT_CONFIG("213", "系统认证令牌未配置！"),
    KEY_DESTROY_SN_NOT_EXISTING_ERROR("230", "密钥撤销证书序列号未找到！"),
    KEY_RESTORE_SN_NOT_EXISTING_ERROR("240", "密钥恢复证书序列号未找到！"),
    RESTORE_ID_NOT_EXISTING_ERROR("241", "待恢复密钥未找到！"),
    VERIFY_LAW_RECOVER_SIGNATURE_ERROR("242", "司法恢复请求验签失败"),
    ARCHIVE_ID_NOT_FOUND_ERROR("243", "待归档密钥未找到！"),
    ARCHIVE_ID_STATUS_UNSUPPORTED_FOR_ARCHIVE_ERROR("244", "当前密钥状态不支持归档！"),
    RETASYMALG_NOT_EQUAL_PUBLIC_KEY("255", "非对称算法与公钥中算法不匹配"),

    /**
     * 3: sks 密钥管理
     */
    SKS_KEY_EXISTING_ERROR("300", "相同哈希的协同密钥已经存在！"),

    SKS_KEY_NOT_EXISTING_ERROR("301", "协同密钥记录不存在"),

    SKS_KEY_STATUS_REVOKED_ERROR("302", "协同密钥已经废除"),

    BATCH_NUMBER_EXCESS_ERROR("305", "批量同步协同密钥数量超过配置的阈值，请缩小查询范围"),

    SKS_KEY_REMOVE_ERROR("306", "不允许删除非自检协同密钥"),

    SKS_KEY_UPDATE_ERROR("307", "不允许更新非协同密钥"),

    /**
     * 4.License校验
     */
    DEMO_KEY_USAGE_EXCEEDED("401", "测试环境已达到密钥数量上限(200个),请部署使用正式环境并导入相应的LICENSE!"),
    ILLEGAL_LICENSE_STATUS("402", "系统许可证已过期或无效,请导入新的 license!"),

    /**
     * 9: 通用模块错误码
     */
    PARSE_ERROR("905", "ASN结构解析失败"),
    ENCODE_ERROR("906", "编码失败"),
    UNSUPPORTED_ALGORITHM_ERROR("907", "不支持的算法"),
    LICENSE_SYS_NPKI_TYPE_ERROR("908", "License中的NPKI系统类型不匹配"),
    START_TIME_AFTER_END_TIME("909", "起始时间不能大于截止时间")
    ;

    private final String code;
    private final String desc;

    KmValidationError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getMessageKey() {
        // 在当前的异常枚举定义的包下面，找LocalStrings，key为异常枚举的名称
        return KMCoreI18N.KM_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase(Locale.ROOT);
    }
}
