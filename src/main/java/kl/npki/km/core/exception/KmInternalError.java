package kl.npki.km.core.exception;

import kl.nbase.exception.interfaces.IInternalError;
import kl.npki.km.core.common.constants.KMCoreI18N;

import java.util.Locale;

/**
 * @Author: guoq
 * @Date: 2022/8/23
 * @description: 内部错误码
 */
public enum KmInternalError implements IInternalError, ErrorCode {

    /**
     * 1：CA模块
     */
    CA_RESOURCE_NOT_ENOUGH("100", "CA密钥资源不够！"),
    CA_CERT_PUBLICKEY_ECODE_ERROR("101", "CA证书公钥解码失败！"),
    CA_RESOURCE_UNAUTHORIZED("102", "CA密钥资源未授权！"),
    CA_MEDIUM_SN_NOT_EXIST("103", "不存在的介质序列号操作类型"),
    CA_STATUS_NOT_NORMAL("104", "CA状态不正常"),
    GEN_CA_RESPONSE_FAILED("105", "生成CA响应异常"),
    CA_REQUEST_CONVERT_ERROR("106", "无法解析的CA请求"),

    /**
     * 2： key 服务管理
     */
    KEY_ESCROW_SERVICE_ERROR("200", "密钥申请服务报错！"),
    USER_KEY_DECODE_ERROR("201", "用户公钥解码失败！"),
    KEY_RESTORE_ERROR("202", "密钥恢复失败"),
    KEY_ARCHIVE_ERROR("203", "密钥归档失败"),
    LAW_RECOVER_CERT_CREATE_ERROR("210", "司法恢复安装证书生成失败！"),

    ENGINE_GEN_KEYPAIR_ERROR("220", "密码机生成密钥对失败"),
    SPARE_KEY_SAVE_ERROR("221", "备用密钥对存储仓库失败"),

    /**
     * 3: sks 密钥管理
     */
    GEN_RANDOM_NUMBER_ERROR("300", "获取随机数失败"),

    /**
     * 4: 证书签发
     */
    MANAGE_ROOT_NOT_ISSUE("400", "管理根证书尚未签发"),
    MANAGE_ROOT_ALREADY_EXIST("401", "管理根证书已存在"),
    ID_CERT_NOT_ISSUE("402", "身份证书尚未签发"),
    ID_CERT_ALREADY_EXIST("403", "身份证书已存在"),
    CERT_ISSUE_ERROR("404", "证书签发失败"),
    KEY_PAIR_GET_ERROR("405", "密钥对获取失败"),
    GEN_PKCS10_REQUEST_ERROR("406", "生成p10请求失败"),
    DN_BUILD_ERROR("407", "DN构造失败"),
    ASYM_ALGO_INCONSISTENT("408", "申请者公钥和颁发者证书算法不一致"),

    /**
     * 9：通用管理
     */
    FILE_READ_ERROR("900", "文件的加载失败"),
    ASN_DECODE_ERROR("901", "ASN结构解码失败"),
    CERT_DECODE_ERROR("902", "证书ASN结构解码失败"),
    CERT_NOT_YET_VALID_ERROR("903", "证书未生效"),
    CERT_EXPIRED_ERROR("904", "证书已经过期"),
    JKS_LOAD_ERROR("905", "JKS 加载失败！"),
    CERT_ENCODE_ERROR("906", "证书ASN结构解码失败"),
    GET_PUBLIC_KEY_ERROR("907", "获取公钥失败"),
    GET_PRIVATE_KEY_ERROR("908", "获取私钥失败"),
    ASN_ENCODE_ERROR("909", "ASN结构编码失败"),

    KM_INTERNAL_ERROR("999", "KM内部服务异常"),


    ;
    private final String code;
    private final String desc;

    KmInternalError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getMessageKey() {
        // 在当前的异常枚举定义的包下面，找LocalStrings，key为异常枚举的名称
        return KMCoreI18N.KM_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase(Locale.ROOT);
    }

}
