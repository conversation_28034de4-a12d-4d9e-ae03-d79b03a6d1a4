package kl.npki.km.core.repository;

import kl.npki.base.core.repository.BaseRepository;
import kl.npki.km.core.biz.key.model.HistoryKeyEntity;
import kl.npki.km.core.biz.stat.model.CaCountInfo;
import kl.npki.km.core.biz.stat.model.KeyCountInfo;

import java.util.List;

/**
 * @Author: guoq
 * @Date: 2022/8/24
 * @description: 密钥历史库
 */
public interface IKeyHistoryRepository extends BaseRepository {

    /**
     * 查询密钥对是否存在
     *
     * @param pubKeyIndex 公钥索引
     * @return
     */
    boolean isExist(String pubKeyIndex);

    /**
     * 根据SN和密钥类型查询密钥实体(SN+类型 密钥实体唯一）
     *
     * @param sn 证书序列号
     * @param keyType 密钥类型
     * @return 密钥实体
     */
    HistoryKeyEntity searchKeyBySnAndType(String sn, String keyType);

    /**
     * 根据SN查询密钥实体（由于抗量子业务场景存在，可能会出现一对多的场景）
     *
     * @param sn
     * @return
     */
    List<HistoryKeyEntity> searchKeyBySn(String sn);

    /**
     * 根据Id查询密钥实体
     *
     * @param keyId
     * @return
     */
    HistoryKeyEntity searchKeyById(Long keyId);

    /**
     * 保持历史密钥
     *
     * @param keyEntity
     * @return 主键id
     */
    Long insertKey(HistoryKeyEntity keyEntity);

    /**
     * 查询keyTypeCount
     * @return
     */
    List<KeyCountInfo> countKeyType();

    /**
     * 通过CAId查询keyCount
     * @param caId ca标识
     * @return
     */
    Integer countByCa(Long caId);

    /**
     * 删除记录
     * @param id
     * @param encCertSn
     * @return
     */
    boolean deleteKey(Long id, String encCertSn);

    /**
     * 通过CA ID列表查询密钥使用数量
     * @param caIds
     * @return
     */
    List<CaCountInfo> countByCaIds(List<Long> caIds);
}
