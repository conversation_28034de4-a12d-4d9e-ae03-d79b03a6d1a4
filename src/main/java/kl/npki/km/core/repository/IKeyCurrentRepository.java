package kl.npki.km.core.repository;

import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.npki.base.core.repository.BaseRepository;
import kl.npki.km.core.biz.key.model.CurrentKeyEntity;
import kl.npki.km.core.biz.stat.model.CaCountInfo;
import kl.npki.km.core.biz.stat.model.KeyCountInfo;
import kl.npki.km.core.biz.stat.model.KeyStatusCountInfo;

import java.util.Date;
import java.util.List;

/**
 * @Author: guoq
 * @Date: 2022/8/24
 * @description: 密钥在用库
 */
public interface IKeyCurrentRepository extends BaseRepository {

    /**
     * 查询密钥对是否存在
     *
     * @param pubKeyIndex 公钥索引
     * @return
     */
    boolean isExist(String pubKeyIndex);

    /**
     * 根据SN和密钥类型查询密钥实体(SN+类型 密钥实体唯一）
     *
     * @param sn 证书序列号
     * @param keyType 密钥类型
     * @return 密钥实体
     */
    CurrentKeyEntity searchKeyBySnAndType(String sn, String keyType);

    /**
     * 根据SN查询密钥实体（由于抗量子业务场景存在，可能会出现一对多的场景）
     *
     * @param sn
     * @return
     */
    List<CurrentKeyEntity> searchKeyBySn(String sn);

    /**
     * 根据Id查询密钥实体
     *
     * @param keyId
     * @return
     */
    CurrentKeyEntity searchKeyById(Long keyId);

    /**
     * 根据介质序列号查询密钥实体是否存在
     *
     * @param mediumSn
     * @param caId
     * @return
     */
    boolean searchKeyExistByMediumSn(String mediumSn, Long caId);

    /**
     * 密钥托管，保存到当前库中
     *
     * @param keyEntity
     * @return
     */
    Long escrowKey(CurrentKeyEntity keyEntity);

    /**
     * 更新密钥记录
     *
     * @param keyEntity
     * @return
     */
    boolean updateStatus(CurrentKeyEntity keyEntity);

    /**
     * 删除密钥记录
     *
     * @param keyId
     * @param sn    分表键
     * @return
     */
    boolean deleteKey(Long keyId, String sn);

    /**
     * 根据归档时间查询过期密钥，根据时间排序
     *
     * @param archiveTime
     * @return 过期数量
     */
    int countExpiredKey(Date archiveTime);

    /**
     * 根据当前时间查询过期但未修改状态的密钥，根据时间排序
     *
     * @param nowTime
     * @return 过期数量
     */
    int countExpiredKeyByNormalStatus(Date nowTime);

    /**
     * 根据归档时间查询过期密钥，根据时间排序
     *
     * @param archiveTime 归档时间
     * @param pageInfo    分页信息
     * @return
     */
    List<CurrentKeyEntity> searchExpiredKey(Date archiveTime, PageInfo pageInfo);

    /**
     * 根据当前时间查询过期但未修改状态的密钥，根据时间排序
     *
     * @param nowTime 当前时间
     * @param pageInfo    分页信息
     * @return
     */
    List<CurrentKeyEntity> searchExpiredKeyByNormalStatus(Date nowTime, PageInfo pageInfo);

    /**
     * 根据归档时间查询异常密钥，根据时间排序
     *
     * @param archiveTime
     * @return 过期数量
     */
    int countRevokedKey(Date archiveTime);

    /**
     * 根据归档时间查询异常密钥，根据时间排序
     *
     * @param archiveTime 归档时间
     * @param pageInfo    分页信息
     * @return 当前库密钥实体
     */
    List<CurrentKeyEntity> searchRevokedKey(Date archiveTime, PageInfo pageInfo);

    /**
     * 查询keyTypeCount
     *
     * @return
     */
    List<KeyCountInfo> countKeyType();

    /**
     * 根据CA查询keyTypeCount
     * @param caId
     * @return
     */
    List<KeyCountInfo> countKeyTypeByCa(Long caId);

    /**
     * 统计每一状态下的密钥数量
     *
     * @return
     */
    List<KeyStatusCountInfo> countStatus();

    /**
     * 通过CAId查询keyCount
     *
     * @param caId ca标识
     * @return
     */
    Integer countByCa(Long caId);

    /**
     * 通过CA ID列表批量查询密钥数量
     *
     * @param caIds
     * @return
     */
    List<CaCountInfo> countByCaIds(List<Long> caIds);
}
