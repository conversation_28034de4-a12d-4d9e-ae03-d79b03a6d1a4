package kl.npki.km.core.repository;

import kl.npki.base.core.repository.BaseRepository;
import kl.npki.km.core.biz.ca.model.CaEntity;

import java.util.List;

/**
 * @Author: guoq
 * @Date: 2022/8/29
 * @description: ca信息仓库
 */
public interface ICaRepository extends BaseRepository {

    /**
     * 查询所有CA实体
     * @param name
     * @return
     */
    CaEntity searchCaEntityExceptRevoked(String name);

    /**
     * 查询所有CA实体
     * @return
     */
    List<CaEntity> searchCaEntityList();

    /**
     * 保存ca记录
     * @param caEntity
     * @return
     */
    Long insertCAEntity(CaEntity caEntity);

    /**
     * 修改ca记录
     * @param caEntity
     * @return
     */
    boolean updateCAEntity(CaEntity caEntity);

    /**
     * 通过ID查询ca信息
     * @param caId ca编号信息
     * @return
     */
    CaEntity searchCaEntityById(Long caId);

    /**
     * 通过certSn,keyHash查询ca信息
     * @param certSn
     * @param keyHash
     * @return
     */
    CaEntity searchCaEntity(String certSn, String keyHash);

    /**
     * 根据ca状态统计
     * @param caStatus
     * @return
     */
    Long countByCaStatus(int caStatus);

    /**
     * 统计所有的ca名称
     * @return
     */
    List<String> getCaNameListExceptRevoked();

    /**
     * 查询SN.keyHash,name是否存在
     * @param sn
     * @param keyHash
     * @param caName
     * @return
     */
    boolean checkExist(String sn, String keyHash, String caName);
}
