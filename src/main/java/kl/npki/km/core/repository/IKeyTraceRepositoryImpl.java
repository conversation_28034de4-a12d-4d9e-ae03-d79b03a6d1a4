package kl.npki.km.core.repository;

import kl.npki.base.core.repository.BaseRepository;
import kl.npki.km.core.biz.key.model.KeyTraceEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IKeyTraceRepositoryImpl extends BaseRepository {
    /**
     * 新增密钥轨迹记录
     * @param keyTraceEntity
     */
    void insert(KeyTraceEntity keyTraceEntity);

    /**
     * 根据实体ID查找所有轨迹记录
     * @param entitySn
     * @param keyType
     * @return
     */
    List<KeyTraceEntity> searchByEntitySn(String entitySn, String keyType);
}
