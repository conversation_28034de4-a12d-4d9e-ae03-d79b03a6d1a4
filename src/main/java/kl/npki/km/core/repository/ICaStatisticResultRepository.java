package kl.npki.km.core.repository;

import kl.npki.base.core.repository.BaseRepository;
import kl.npki.km.core.biz.statistic.CaStatisticResultEntity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface ICaStatisticResultRepository extends BaseRepository {
    /**
     * 按天的维度，查找ca最近一天的统计记录
     * @param caName
     * @return
     */
    Optional<CaStatisticResultEntity> searchLatestResultByDay(String caName);

    /**
     * 插入数据
     * @param caStatisticResultEntity
     */
    void insert(CaStatisticResultEntity caStatisticResultEntity);

    /**
     * 查询分组统计结果
     * @param toSearchCaList
     * @param startTime
     * @param endTime
     * @return
     */
    List<CaStatisticResultEntity> countGroupByCaName(List<String> toSearchCaList, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找指定ca，指定月份的数据
     * @param caName
     * @param firstDayOfMonth
     * @return
     */
    Optional<CaStatisticResultEntity> searchMonthDataByCaName(String caName, LocalDateTime firstDayOfMonth);
}
