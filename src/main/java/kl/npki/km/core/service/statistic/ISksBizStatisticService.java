package kl.npki.km.core.service.statistic;

import kl.npki.base.core.biz.log.model.ApiLogCountServiceResult;
import kl.npki.km.core.biz.stat.model.SksBizStatisticResponse;
import kl.npki.km.core.biz.statistic.BizStatisticInfo;

import java.io.OutputStream;
import java.time.LocalDate;
import java.util.List;

/**
 * SKS服务统计接口
 * <AUTHOR>
 */
public interface ISksBizStatisticService {
    /**
     * 统计sks业务报表
     * @param conditionInfo
     * @return
     */
    SksBizStatisticResponse statisticSksBizReport(BizStatisticInfo conditionInfo);

    /**
     * 导出为excel报表
     * @param conditionInfo
     * @param outputStream
     */
    void exportReport(BizStatisticInfo conditionInfo, OutputStream outputStream);

    /**
     * 统计指定日期(某天)的结果
     * @param bizNameList
     * @param localDate
     * @return
     */
    List<ApiLogCountServiceResult> statisticOneDay(List<String> bizNameList, LocalDate localDate);
}
