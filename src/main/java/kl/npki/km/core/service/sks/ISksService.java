package kl.npki.km.core.service.sks;

import kl.npki.km.core.biz.sks.model.*;

/**
 * <AUTHOR>
 * @date 2022/11/23 16:21
 * @Description:
 */
public interface ISksService {

    /**
     * 获取随机数
     *
     * @param sizeInByte 随机数字节长度
     * @return Base64 随机数
     */
    String getRandomNumber(int sizeInByte);

    /**
     * 保存sks密钥
     *
     * @param sksKeyEntity 随机数字节长度
     * @return keyId
     */
    void createPrivateKey(SksKeyEntity sksKeyEntity);

    /**
     * 获取sks密钥
     *
     * @param clientKeyHash 客户端公钥Hash
     * @return keyId
     */
    SksKeyEntity getPrivateKey(String clientKeyHash);

    /**
     * 批量获取
     *
     * @param sksKeyBatchSearchInfo
     * @return sks批量对象
     */
    SksKeyListInfo batchGetPrivateKey(SksKeyBatchSearchInfo sksKeyBatchSearchInfo);

    /**
     * 修改sks密钥状态
     *
     * @param sksUpdateInfo 更新的SKS密钥信息
     * @return 是否成功
     */
    boolean updatePrivateKey(SksUpdateInfo sksUpdateInfo);

    /**
     * 删除自检密钥数据
     * @param clientKeyHash
     * @return
     */
    boolean removeSelfCheckPrivateKey(String clientKeyHash);
}
