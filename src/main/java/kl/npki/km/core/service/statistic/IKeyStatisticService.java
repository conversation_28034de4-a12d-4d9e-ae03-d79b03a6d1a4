package kl.npki.km.core.service.statistic;

import kl.npki.km.core.biz.stat.model.CaKeyUseResponse;
import kl.npki.km.core.biz.stat.model.KeystoreResponse;

import java.io.OutputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/4 10:25
 * @Description: 统计服务接口
 */
public interface IKeyStatisticService {

    /**
     * 统计密钥库数据
     *
     * @return
     */
    List<KeystoreResponse> statisticKeystoreReport();

    /**
     * 统计CA密钥使用数据
     *
     * @return
     */
    List<CaKeyUseResponse> statisticKeyUseReport();

    /**
     * 统计密钥服务日志数据
     *
     * @return
     */
    List<String> statisticKeyServiceReport();

    /**
     * 导出统计报表数据为excel文件
     * @param outputStream
     */
    void exportKeystoreReport(OutputStream outputStream);

    /**
     * 导出密钥使用情况报表
     */
    void exportKeyUseReport(OutputStream outputStream);
}
