package kl.npki.km.core.service.archive.impl;

import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.annotation.KlTransactional;
import kl.npki.base.core.common.trace.InvocationType;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.base.core.event.EntityChangeEvent;
import kl.npki.base.core.event.EntityChangeEventManager;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.km.core.biz.key.model.CurrentKeyEntity;
import kl.npki.km.core.biz.key.model.HistoryKeyEntity;
import kl.npki.km.core.common.constants.ArchiveReason;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.core.exception.KmInternalError;
import kl.npki.km.core.exception.KmValidationError;
import kl.npki.km.core.mapper.ConvertServiceHelper;
import kl.npki.km.core.repository.IKeyCurrentRepository;
import kl.npki.km.core.service.archive.IKeyArchiveService;
import kl.npki.km.core.utils.PageUtil;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;

import static kl.npki.km.core.common.constants.KMCoreI18N.ARCHIVE_KEY;
import static kl.npki.km.core.common.constants.KMCoreI18N.EXPIRED_KEY_SCAN;

/**
 * @Author: guoq
 * @Date: 2022/9/21
 * @description: 密钥归档实现类
 */
public class KeyArchiveServiceImpl implements IKeyArchiveService {

    private static final Logger log = LoggerFactory.getLogger(KeyArchiveServiceImpl.class);

    private final IKeyCurrentRepository keyCurrentRepository;

    public KeyArchiveServiceImpl() {
        keyCurrentRepository = RepositoryFactory.get(IKeyCurrentRepository.class);
    }

    @Override
    public void doKeyArchiveJob() {
        int pageSize = KmConfigWrapper.getArchiveKeyConf().getArchivePageThreshold();
        // 1. 扫描过期的密钥，修改密钥状态
        try {
            doExpiredKeyScan(pageSize);
        } catch (Exception e) {
            log.error("Failed to scan expired key!", e);
        }

        Date archiveTime = getArchiveTime();
        // 2. 归档过期的密钥
        try {
            doExpiredKeyArchive(pageSize, archiveTime);
        } catch (Exception e) {
            log.error("Failed to scan expired key!", e);
        }

        // 3. 归档废除状态的密钥
        try {
            doRevokedKeyArchive(pageSize, archiveTime);
        } catch (Exception e) {
            log.error("Failed to scan expired key!", e);
        }

    }

    @Override
    @KlTransactional
    public void archiveKey(Long keyId) {
        CurrentKeyEntity currentKeyEntity = keyCurrentRepository.searchKeyById(keyId);
        CheckUtils.notNull(currentKeyEntity, KmValidationError.ARCHIVE_ID_NOT_FOUND_ERROR.toException());
        // 当前密钥归档只能是已废除或已过期
        CheckUtils.isTrue(
            currentKeyEntity.getKeyStatus().equals(EntityStatus.REVOKED.getId()) ||
                currentKeyEntity.getKeyStatus().equals(EntityStatus.EXPIRED.getId()),
            KmValidationError.ARCHIVE_ID_STATUS_UNSUPPORTED_FOR_ARCHIVE_ERROR.toException());
        try {
            currentKeyEntity.delete();
            HistoryKeyEntity historyKeyEntity = ConvertServiceHelper.getConvertService().convert(currentKeyEntity,
                HistoryKeyEntity.class);
            // 设置归档原因
            historyKeyEntity.setArchiveReason(
                currentKeyEntity.getKeyStatus().equals(EntityStatus.REVOKED.getId()) ?
                    ArchiveReason.REVOKE.name() : ArchiveReason.EXPIRED.name());
            historyKeyEntity.save();

            // 推送密钥实体变更通知
            EntityChangeEventManager.getInstance().publishEvent(EntityChangeEvent.newInstance(historyKeyEntity,
                ARCHIVE_KEY,
                InvocationType.ADMINISTRATOR_OPERATION));

            if (log.isDebugEnabled()) {
                log.debug("Successfully archive key, keyId={}", currentKeyEntity.getId());
            }
        } catch (Exception e) {
            throw KmInternalError.KEY_ARCHIVE_ERROR.toException(e);
        }
    }

    /**
     * 执行过期密钥扫描
     *
     * @param pageSize
     */
    private void doExpiredKeyScan(int pageSize) {
        Date now = new Date();
        int expiredKeyCount = keyCurrentRepository.countExpiredKeyByNormalStatus(now);

        if (expiredKeyCount <= 0) {
            log.info("[Scan Expired Key] No expired key found");
            return;
        }
        log.info("[Scan Expired Key] Found {} expired keys", expiredKeyCount);
        // 获取分页次数
        int loopCount = PageUtil.getPageCount(expiredKeyCount, pageSize);
        log.debug("[Scan Expired Key] {} expired keys will be processed in {} batches, batch size:{}",expiredKeyCount, loopCount, pageSize);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(pageSize);
        for (int pageindex = 0; pageindex < loopCount; pageindex++) {
            pageInfo.setCurrentPage(pageindex);
            // 分页获取数据
            List<CurrentKeyEntity> keyEntities = keyCurrentRepository.searchExpiredKeyByNormalStatus(now, pageInfo);
            log.debug("[Scan Expired Key] current batch:{}, batch size:{}", pageindex, keyEntities.size());
            keyEntities.forEach(keyEntity -> {
                try {
                    keyEntity.updateStatus(EntityStatus.EXPIRED.getId());
                    // 推送密钥实体变更通知
                    EntityChangeEventManager.getInstance().publishEvent(EntityChangeEvent.newInstance(keyEntity,
                        EXPIRED_KEY_SCAN,
                        InvocationType.SYSTEM_SCHEDULED_TASK));

                } catch (Exception e) {
                    log.error("[Scan Expired Key] Failed to modify expired key status, SN={}", keyEntity.getEncCertSn(), e);
                }
            });
        }
    }

    /**
     * 执行异常密钥归档
     *
     * @param pageSize
     * @param archiveTime
     */
    private void doRevokedKeyArchive(int pageSize, Date archiveTime) {
        int revokedKeyCount = keyCurrentRepository.countRevokedKey(archiveTime);

        if (revokedKeyCount <= 0) {
            log.info("[Archive Revoked Key] No revoked key found");
            return;
        }
        log.info("[Archive Revoked Key] Found {} revoked keys", revokedKeyCount);
        // 获取分页次数
        int loopCount = PageUtil.getPageCount(revokedKeyCount, pageSize);
        log.debug("[Archive Revoked Key] {} revoked keys will be processed in {} batches, batch size:{}", revokedKeyCount, loopCount, pageSize);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(pageSize);
        for (int pageindex = 0; pageindex < loopCount; pageindex++) {
            pageInfo.setCurrentPage(pageindex);
            // 分页获取数据
            List<CurrentKeyEntity> keyEntities = keyCurrentRepository.searchRevokedKey(archiveTime, pageInfo);
            log.debug("[Archive Revoked Key] current batch:{}, batch size:{}", pageindex, keyEntities.size());
            archiveKeyList(keyEntities, ArchiveReason.REVOKE);
        }

    }

    /**
     * 执行过期密钥归档
     *
     * @param pageSize
     * @param archiveTime
     */
    private void doExpiredKeyArchive(int pageSize, Date archiveTime) {
        int expiredKeyCount = keyCurrentRepository.countExpiredKey(archiveTime);

        if (expiredKeyCount <= 0) {
            log.info("[Archive Expired Key] No expired key found");
            return;
        }
        log.info("[Archive Expired Key] Found {} expired keys", expiredKeyCount);
        // 获取分页次数
        int loopCount = PageUtil.getPageCount(expiredKeyCount, pageSize);
        log.debug("[Archive Expired Key] {} expired keys will be processed in {} batches, batch size:{}", expiredKeyCount, loopCount, pageSize);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(pageSize);
        for (int pageindex = 0; pageindex < loopCount; pageindex++) {
            pageInfo.setCurrentPage(pageindex);
            // 分页获取数据
            List<CurrentKeyEntity> keyEntities = keyCurrentRepository.searchExpiredKey(archiveTime, pageInfo);
            log.debug("[Archive Expired Key] current batch:{}, batch size:{}", pageindex, keyEntities.size());
            archiveKeyList(keyEntities, ArchiveReason.EXPIRED);
        }

    }


    /**
     * 归档密钥集合
     *
     * @param keyEntities
     * @param archiveReason
     */
    @KlTransactional
    private void archiveKeyList(List<CurrentKeyEntity> keyEntities, ArchiveReason archiveReason) {
        for (CurrentKeyEntity currentKeyEntity : keyEntities) {
            try {
                currentKeyEntity.delete();
                HistoryKeyEntity historyKeyEntity = ConvertServiceHelper.getConvertService().convert(currentKeyEntity,
                    HistoryKeyEntity.class);
                // 设置归档原因
                historyKeyEntity.setArchiveReason(archiveReason.name());
                historyKeyEntity.save();

                // 推送密钥实体变更通知
                EntityChangeEventManager.getInstance().publishEvent(EntityChangeEvent.newInstance(historyKeyEntity,
                    ARCHIVE_KEY,
                    InvocationType.SYSTEM_SCHEDULED_TASK));

            } catch (Exception e) {
                log.error("Failed to archive key, SN={}", currentKeyEntity.getEncCertSn(), e);
            }
        }
    }

    /**
     * 获取归档时间，
     * 密钥过期后 还要延迟到最后归档时间之后才进行归档
     * 因此 归档时间 = 当前时间-延迟归档时间
     *
     * @return 归档时间
     */
    private Date getArchiveTime() {
        int lateArchiveTime = KmConfigWrapper.getArchiveKeyConf().getLateArchiveTime();
        // 获取最迟归档时间负数
        int negativeLateArchiveTime = (-lateArchiveTime);
        // addDays 负的归档时间 = 当前时间-延迟归档时间
        return DateUtils.addDays(new Date(), negativeLateArchiveTime);
    }
}
