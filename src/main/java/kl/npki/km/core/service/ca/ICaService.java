package kl.npki.km.core.service.ca;

import kl.npki.km.core.biz.ca.model.AddResourceInfo;
import kl.npki.km.core.biz.ca.model.CaEntity;
import kl.npki.km.core.biz.ca.model.CaUpdateInfo;

import java.util.List;

/**
 * @Author: guoq
 * @Date: 2022/9/15
 * @description: ca接入服务
 */
public interface ICaService {

    /**
     * ca接入
     *
     * @param caEntity
     * @return caId
     */
    Long caAccess(CaEntity caEntity);

    /**
     * 增加各类型密钥数量
     * @param caId ca编号信息
     * @param resourceList 增加密钥数量请求信息
     * @return
     */
    boolean addKeyNum(Long caId, List<AddResourceInfo> resourceList);

    /**
     * ca更新
     * @param caId ca编号信息
     * @param caUpdateInfo ca信息
     * @return
     */
    boolean caUpdate(Long caId, CaUpdateInfo caUpdateInfo);

    /**
     * 冻结ca
     * @param caId ca编号信息
     * @return
     */
    boolean caFreeze(Long caId);

    /**
     * 解冻ca
     * @param caId ca编号信息
     * @return
     */
    boolean caUnfreeze(Long caId);

    /**
     * 废除ca
     * @param caId ca编号信息
     * @return
     */
    boolean caRevoke(Long caId);

    /**
     * 恢复ca
     * @param caId ca编号信息
     * @return
     */
    boolean caRecovery(Long caId);

}
