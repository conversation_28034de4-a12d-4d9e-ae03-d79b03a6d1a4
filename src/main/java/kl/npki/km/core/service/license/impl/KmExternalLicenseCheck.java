package kl.npki.km.core.service.license.impl;

import kl.npki.base.core.biz.license.service.impl.AbstractExternalLicenseCheckImpl;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.core.constant.LicenseConstant;
import kl.npki.km.core.exception.KmValidationError;
import kl.security.license.bean.CustomLicenseItem;
import kl.security.license.core.License;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/6/6
 */
public class KmExternalLicenseCheck extends AbstractExternalLicenseCheckImpl {
    private static final String SYS_NGPKI_TYPE = BaseConstant.PROJECT_NAME + "-KM";

    @Override
    public void checkInputLicense(License license) {
        checkLicenseStatus(license);
        CustomLicenseItem customLicenseItem = license.getCusMap().get(LicenseConstant.SYS_NGPKI_TYPE);
        if (Objects.isNull(customLicenseItem) || !StringUtils.equals(customLicenseItem.getValue(), SYS_NGPKI_TYPE)) {
            throw KmValidationError.LICENSE_SYS_NPKI_TYPE_ERROR.toException();
        }
    }
}
