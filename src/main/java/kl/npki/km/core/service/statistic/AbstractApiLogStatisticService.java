package kl.npki.km.core.service.statistic;

import kl.npki.base.core.biz.log.model.ApiLogCountServiceResult;
import kl.npki.base.core.exception.BaseDbException;
import kl.npki.base.core.repository.IApiLogRepository;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.km.core.exception.KmInternalError;
import kl.npki.km.core.utils.DateStatisticUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static kl.npki.km.core.common.constants.KMCoreI18N.UNSUPPORTED_STATISTICAL_TYPE;

/**
 * 日志统计业务，针对处理查询超时的情况
 * <p>
 * 方案: 查询超时的情况下，进行区间逐步切割统计
 *
 * <AUTHOR>
 */
public abstract class AbstractApiLogStatisticService {

    private static final Logger log = LoggerFactory.getLogger(AbstractApiLogStatisticService.class);

    /**
     * 兜底查询时间区间，避免数据量过大出现查询超时，导致统计业务停止
     */
    private static final int QUERY_INTERVAL_MINUTES = 5;
    /**
     * 统计类型，通过调用者进行分组统计
     */
    public static final String COUNT_BY_CALLER = "count_by_caller";
    /**
     * 统计类型，通过业务名称分组统计
     */
    public static final String COUNT_BY_BIZ_NAME = "count_by_biz_name";

    private final IApiLogRepository apiLogRepository;

    protected AbstractApiLogStatisticService() {
        this.apiLogRepository = RepositoryFactory.get(IApiLogRepository.class);
    }

    /**
     * 统计某一天的数据
     * @param callerOrBizNames
     * @param localDate
     * @return
     */
    public List<ApiLogCountServiceResult> statisticOneDay(String countType, List<String> callerOrBizNames, LocalDate localDate) {
        // 定义查询区间范围
        LocalDateTime leftTime = localDate.atTime(0, 0);
        LocalDateTime rightTime = leftTime.plusDays(1).minusSeconds(1);

        List<ApiLogCountServiceResult> apiLogCountServiceResults;
        try {
            apiLogCountServiceResults = executeByCountType(countType, callerOrBizNames,leftTime, rightTime);
        } catch (BaseDbException e) {
            if(!e.isQueryTimeOutException()){
                throw e;
            }
            log.warn("按天维度进行统计，数据量过大，即将缩小查询区间（按小时统计再累加）...., 统计业务类型:{}, 查询条件:{}, 待统计日期:{}",countType, callerOrBizNames, localDate);
            // 查询时间过长，可能是这一天数据量太大导致的，尝试缩小当天的时间区间，进行统计
            apiLogCountServiceResults = statisticOneDayByOneHour(countType, callerOrBizNames, localDate);
        }

        return apiLogCountServiceResults;
    }

    /**
     * 统计一个月的数据
     * @param countType 统计业务类型
     * @param callerOrBizNames 统计条件，调用者或业务名列表
     * @param date 统计条件，待统计的月份
     * @return
     */
    public List<ApiLogCountServiceResult> statisticOneMonthData(String countType, List<String> callerOrBizNames, LocalDateTime date) {
        LocalDateTime firstDay = DateStatisticUtil.getFirstDayOfMonth(date);
        LocalDateTime lastDay = DateStatisticUtil.getLastDayOfMonth(date);

        List<ApiLogCountServiceResult> apiLogCountServiceResults;
        // 查询本月的数据
        try {
            // 根据不同业务名，执行不同的查询语句
            apiLogCountServiceResults = executeByCountType(countType, callerOrBizNames, firstDay, lastDay);
        } catch (BaseDbException e) {
            if(!e.isQueryTimeOutException()){
                throw e;
            }
            log.warn("按月维度进行统计，数据量过大，即将缩小查询区间（按天统计再累加）...., 业务类型:{}, 查询条件:{}, 待统计日期:{}", countType, callerOrBizNames, date);
            apiLogCountServiceResults = statisticOneMonthDataByOneDay(countType, callerOrBizNames, date);
        }

        return apiLogCountServiceResults;
    }

    /**
     * 通过每一天的数据，统计整个月的数据
     * @param countType
     * @param callerOrBizNames
     * @param date
     * @return
     */
    private List<ApiLogCountServiceResult> statisticOneMonthDataByOneDay(String countType, List<String> callerOrBizNames, LocalDateTime date) {
        // 计算查询时间
        LocalDate firstDay = DateStatisticUtil.getFirstDayOfMonth(date).toLocalDate();
        LocalDate lastDay;
        if (DateStatisticUtil.isEqualCurrentMonth(date)) {
            lastDay = LocalDate.now();
        } else {
            lastDay = DateStatisticUtil.getLastDayOfMonth(date).toLocalDate();
        }

        LocalDate currentDay = firstDay;
        // 定义结果集
        List<ApiLogCountServiceResult> results = new ArrayList<>();

        // 每一天为区间，统计一次
        while (currentDay.isBefore(lastDay)) {
            results.addAll(statisticOneDay(countType, callerOrBizNames, currentDay));
            currentDay = currentDay.plusDays(1);
        }

        return results;
    }

    /**
     * 通过统计每一小时的数据，来累加统计每一天的数据
     * @param callerOrBizNames
     * @param localDate
     * @return
     */
    public List<ApiLogCountServiceResult> statisticOneDayByOneHour(String countType, List<String> callerOrBizNames, LocalDate localDate) {
        // 初始化时间区间
        LocalDateTime leftTime = localDate.atTime(0, 0);
        LocalDateTime rightTime = leftTime.plusHours(1);
        // 定义返回结果
        List<ApiLogCountServiceResult> results = new ArrayList<>();
        // 查询条件以1小时为间隔，开始进行查询
        while (leftTime.getDayOfMonth() != localDate.getDayOfMonth() + 1) {
            List<ApiLogCountServiceResult> resultOfHour;
            try {
                // 进行数据库查询
                resultOfHour = executeByCountType(countType, callerOrBizNames, leftTime, rightTime.minusSeconds(1));
            } catch (BaseDbException e) {
                if(!e.isQueryTimeOutException()){
                    throw e;
                }
                log.warn("按小时维度进行统计，数据量过大，即将缩小查询区间（按分钟统计再累加）...., 业务类型:{}, 查询条件:{}, 待统计日期:{}", countType, callerOrBizNames, leftTime);
                // 查询时间过长，可能是这一天数据量太大导致的，尝试缩小当天的时间区间，进行统计
                resultOfHour = statisticOneHourByMinutes(countType, callerOrBizNames, leftTime);
            }
            results.addAll(resultOfHour);

            // 递增时间区间
            leftTime = rightTime;
            rightTime = rightTime.plusHours(1);
        }

        return results;
    }

    /**
     * 通过统计分钟区间，来累加统计小时区间
     * @param countType
     * @param callerOrBizNames
     * @param hour
     * @return
     */
    private List<ApiLogCountServiceResult> statisticOneHourByMinutes(String countType, List<String> callerOrBizNames, LocalDateTime hour) {
        // 定义返回结果
        List<ApiLogCountServiceResult> results = new ArrayList<>();
        // 初始化时间区间
        LocalDateTime leftTime = hour;
        LocalDateTime rightTime = leftTime.plusMinutes(QUERY_INTERVAL_MINUTES);

        while (leftTime.getHour() == hour.getHour()) {
            // 查询该分钟区间内的数据
            log.debug("开始执行最小区间统计(分钟区间)，统计类型:{}, 统计条件:{},查询时间区间:{}~{}", countType, callerOrBizNames, leftTime, rightTime);
            try {
                List<ApiLogCountServiceResult> resultOfMinutes = executeByCountType(countType, callerOrBizNames, leftTime, rightTime.minusSeconds(1));
                results.addAll(resultOfMinutes);
            } catch (BaseDbException e) {
                log.error("执行最小区间统计(分钟区间)失败，查询仍然超时，需人工介入处理...., 业务类型:{}, 查询条件:{}, 查询时间区间:{}~{}", countType, callerOrBizNames, leftTime, rightTime);
                throw e;
            }
            // 递增时间区间
            leftTime = rightTime;
            rightTime = rightTime.plusMinutes(QUERY_INTERVAL_MINUTES);
        }

        return results;
    }

    /**
     * 根据统计业务类型，执行对应的查询语句
     * @param countType
     * @param callerOrBizNames
     * @param startTime
     * @param endTime
     * @return
     */
    private List<ApiLogCountServiceResult> executeByCountType(String countType, List<String> callerOrBizNames, LocalDateTime startTime, LocalDateTime endTime) {
        if (COUNT_BY_CALLER.equals(countType)) {
            return apiLogRepository.countGroupByCallerAndResultAndBiz(callerOrBizNames, startTime, endTime);
        } else if (COUNT_BY_BIZ_NAME.equals(countType)) {
            return apiLogRepository.countGroupByResultAndBiz(callerOrBizNames, startTime, endTime);
        } else {
            throw KmInternalError.KM_INTERNAL_ERROR.toException(UNSUPPORTED_STATISTICAL_TYPE, countType);
        }
    }
}
