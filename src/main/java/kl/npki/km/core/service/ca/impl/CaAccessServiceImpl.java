package kl.npki.km.core.service.ca.impl;

import kl.nbase.exception.BaseException;
import kl.nbase.exception.InternalException;
import kl.nbase.security.asn1.*;
import kl.npki.base.core.annotation.KlTransactional;
import kl.npki.base.core.asn1.custom.*;
import kl.npki.base.core.common.crypto.SignatureHelper;
import kl.npki.base.core.common.manager.MgrHolder;
import kl.npki.base.core.common.service.MsgResult;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.km.core.biz.ca.model.CaEntity;
import kl.npki.km.core.biz.ca.model.CaResourceEntity;
import kl.npki.km.core.biz.ca.service.CaCache;
import kl.npki.km.core.common.RepositoryHelper;
import kl.npki.km.core.common.constants.CaAccessTypeEnum;
import kl.npki.km.core.common.constants.CaKeyServiceType;
import kl.npki.km.core.exception.KmInternalError;
import kl.npki.km.core.exception.KmValidationError;
import kl.npki.km.core.utils.KmResponseUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static kl.npki.km.core.common.constants.KMCoreI18N.PARSE_CA_ACCESS_REQUEST_FAILED;

/**
 * 用于KM快速接入CA的服务
 * <AUTHOR>
 */
public class CaAccessServiceImpl extends AbstractCaFastService {
    private static final Logger log = LoggerFactory.getLogger(CaAccessServiceImpl.class);

    private final CaCache caCache;

    public CaAccessServiceImpl() {
        super();
        this.caCache = CaCache.INSTANCE;
    }

    @Override
    @KlTransactional
    public MsgResult<byte[]> process(byte[] content) {
        MsgResult<byte[]> msgResult = new MsgResult<>(getType());
        byte[] result;
        try{
            checkCaFastApi();
            // 1.解析请求
            CaFastAccessRequest caFastAccessRequest = parseRequest(content);

            // 2.验证请求
            CaEntity tobeAccessCaEntity = verifyRequest(caFastAccessRequest);
            // 设置响应解析结果
            msgResult.setCallerId(String.valueOf(tobeAccessCaEntity.getId()));
            msgResult.setCallerName(tobeAccessCaEntity.getCaName());

            tobeAccessCaEntity.parsingIdCert();
            // 3.处理待接入CA
            CaEntity caEntity = processTobeAccessCa(caFastAccessRequest, tobeAccessCaEntity);

            // 4.构造响应
            result =  buildResponse(caEntity);

        } catch (BaseException e) {
            log.error("处理CA快速接入请求业务出现异常", e);
            msgResult.setErrorMessage(e.getMessage());
            result = KmResponseUtil.buildErrorResponse(e);
        } catch (Exception e) {
            log.error("处理CA快速接入请求业务出现异常", e);
            msgResult.setErrorMessage(e.getMessage());
            InternalException internalException = KmInternalError.KM_INTERNAL_ERROR.toException(e);
            result = KmResponseUtil.buildErrorResponse(internalException);
        }
        msgResult.setResult(result);
        return msgResult;
    }

    /**
     * 处理待接入的CA
     * 数据库不存在CA时，则进行接入流程
     * 存在ca时，则返回该CA信息值客户端
     * @param tobeAccessCaEntity
     */
    private CaEntity processTobeAccessCa(CaFastAccessRequest caFastAccessRequest, CaEntity tobeAccessCaEntity) {
        TBSCaFastRequest tbsCaFastRequest = caFastAccessRequest.getTbsCaFastRequest();
        // 从数据库中查询CA信息
        String tobeAccessCaCertSn = tobeAccessCaEntity.getCertSn();
        String tobeAccessCaKeyHash = tobeAccessCaEntity.getKeyIndex();
        CaEntity caEntityFromDb = RepositoryHelper.getCaRepository().searchCaEntity(tobeAccessCaCertSn, tobeAccessCaKeyHash);

        // 为空时，则进行接入流程
        if (caEntityFromDb == null) {
            // 证书序列号 或 公钥 或 CA名称 不能存在有重复的
            tobeAccessCaEntity.checkExist();
            // 设置CA接入类型为在线接入
            tobeAccessCaEntity.setCaAccessType(CaAccessTypeEnum.ONLINE);
            // 保存CA证书
            tobeAccessCaEntity.save(EntityStatus.NORMAL);
            // 判断是否初始化默认资源
            if (tbsCaFastRequest.getAssignDefaultResources().isTrue()) {
                tobeAccessCaEntity.initCaKeyResource();
            }
            // 保存密钥资源信息
            tobeAccessCaEntity.saveCaKeyResource();
            // 保存缓存
            caCache.putCa(tobeAccessCaEntity);

            return tobeAccessCaEntity;
        } else {
            // 不为空，则返回数据库中的CA信息
            return caEntityFromDb;
        }
    }

    private CaFastAccessRequest parseRequest(byte[] content) {
        try {
            return CaFastAccessRequest.getInstance(content);
        } catch (Exception e) {
            log.error("将请求解析为CaFastAccessRequest出现异常", e);
            throw KmInternalError.CA_REQUEST_CONVERT_ERROR.toException(PARSE_CA_ACCESS_REQUEST_FAILED);
        }
    }

    private byte[] buildResponse(CaEntity caEntity) {
        // KM构造响应
        ASN1EncodableVector caResourceInfoVector = new ASN1EncodableVector();
        Map<String, CaResourceEntity> resources = caEntity.getResourceMap();
        if (MapUtils.isNotEmpty(resources)) {
            List<CaResourceEntity> entityList = new ArrayList<>(resources.values());
            for (CaResourceEntity caResourceEntity : entityList) {
                ASN1OctetString keyType = new DEROctetString(caResourceEntity.getKeyType().getBytes());
                ASN1Integer limitNum = new ASN1Integer(caResourceEntity.getLimitNum());
                ASN1Integer warningNum = new ASN1Integer(caResourceEntity.getWarningNum());
                caResourceInfoVector.add(new CaResourceInfo(keyType, limitNum, warningNum));
            }
        }

        // 获取KM基本信息
        SysInfoConfig sysInfoConfig = ConfigHolder.get().get(SysInfoConfig.class);
        ASN1OctetString version = new DEROctetString(sysInfoConfig.getVersion().getBytes());
        ASN1OctetString name = new DEROctetString(sysInfoConfig.getName().getBytes());

        try {
            // 计算Hmac
            byte[] hmac = hmacService.generateData(name.getOctets());
            ASN1BitString hmacAsn = new DERBitString(hmac);
            KmInfo kmInfo = new KmInfo(version, name, MgrHolder.getIdCertMgr().getIdCert(), hmacAsn);

            ASN1OctetString caName = new  DEROctetString(caEntity.getCaName().getBytes());
            CaResourceInfos caResourceInfos = new CaResourceInfos(caResourceInfoVector);
            ASN1Integer caStatus = new ASN1Integer(caEntity.getCaStatus().getId());
            TBSCaFastResponse tbsCaFastResponse = new TBSCaFastResponse(kmInfo, caResourceInfos, caName, caStatus);

            byte[] tbsData = tbsCaFastResponse.getEncoded();
            // 签名
            byte[] sign = SignatureHelper.sign(tbsData);
            // 签名算法
            String signatureAlgorithmOid = MgrHolder.getIdCertMgr().getSignatureAlgorithmOid();
            // 组装响应信息
            CaFastAccessResponse caFastAccessResponse = new CaFastAccessResponse(tbsCaFastResponse,
                new ASN1ObjectIdentifier(signatureAlgorithmOid),
                new DERBitString(sign));

            return new KmResponse(caFastAccessResponse).getEncoded();
        } catch (Exception e) {
            log.error("生成CA快速接入响应失败", e);
            throw KmInternalError.ASN_ENCODE_ERROR.toException(e);
        }
    }

    private CaEntity verifyRequest(CaFastAccessRequest caFastAccessRequest) {
        TBSCaFastRequest tbsCaFastRequest = caFastAccessRequest.getTbsCaFastRequest();
        CaEntity caEntity = new CaEntity(tbsCaFastRequest);

        byte[] signDataEncoded;
        byte[] tbsCaFastRequestEncode;
        try {
            signDataEncoded = caFastAccessRequest.getSignedData().getOctets();
            tbsCaFastRequestEncode = tbsCaFastRequest.getEncoded();
        } catch (IOException e) {
            log.error("验证CA快速接入请求失败", e);
            throw KmInternalError.ASN_ENCODE_ERROR.toException(e);
        }

        byte[] caNameOctets = tbsCaFastRequest.getCaInfo().getCaName().getOctets();
        byte[] hmac = tbsCaFastRequest.getCaInfo().getHmac().getOctets();
        // 验证Hmac值
        if (!hmacService.verifyData(caNameOctets, hmac)) {
            log.error("验证CA快速接入请求中的Hmac失败, 原文: {}, hmac:{}", new String(caNameOctets), Base64.encodeBase64String(hmac));
            throw KmValidationError.HMAC_VERIFY_ERROR.toException("The hmac verification of the request sent by the CA failed! ");
        }

        // 验证签名
        if (!SignatureHelper.verify(caEntity.getIdCertObj(), signDataEncoded, tbsCaFastRequestEncode)) {
            throw KmValidationError.SIGNED_VERIFY_ERROR.toException("The signature verification of the request sent by the CA failed! ");
        }

        return caEntity;
    }

    @Override
    public String getType() {
        return CaKeyServiceType.CA_ACCESS.getDesc();
    }
}
