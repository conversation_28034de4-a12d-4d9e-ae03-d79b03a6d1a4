package kl.npki.km.core.service.home.impl;

import kl.nbase.cache.client.ICacheClient;
import kl.nbase.cache.key.CacheKey;
import kl.nbase.cache.lock.ILock;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.tenantholders.CacheClientHolder;
import kl.npki.km.core.biz.home.model.KeyStatusStatisticInfo;
import kl.npki.km.core.biz.home.service.KmHomePageDataCache;
import kl.npki.km.core.biz.stat.model.KeyStatusCountInfo;
import kl.npki.km.core.repository.IKeyCurrentRepository;
import kl.npki.km.core.service.home.KeyStatusStatisticService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 密钥状态统计业务
 *
 * <AUTHOR>
 */
public class KeyStatusStatisticServiceImpl implements KeyStatusStatisticService {

    private static final Logger log = LoggerFactory.getLogger(KeyStatusStatisticServiceImpl.class);

    private final IKeyCurrentRepository keyCurrentRepository;

    public KeyStatusStatisticServiceImpl() {
        this.keyCurrentRepository = RepositoryFactory.get(IKeyCurrentRepository.class);
    }

    @Override
    public void updateCache() {
        ILock lock = getLock();
        try {
            if (lock.tryLock()) {
                // 在用密钥库的 三种状态下的统计
                List<KeyStatusCountInfo> keyStatusCountInfos = keyCurrentRepository.countStatus();

                // 组装结果数据
                KeyStatusStatisticInfo keyStatusStatisticInfo = new KeyStatusStatisticInfo(keyStatusCountInfos);

                // 更新缓存数据
                KmHomePageDataCache.INSTANCE.putKeyStatusStatisticCache(keyStatusStatisticInfo);
            }
        } catch (Exception e) {
            log.error("更新密钥数据统计缓存失败！", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public KeyStatusStatisticInfo getKeyStatusStatisticInfo() {
        KeyStatusStatisticInfo keyData = KmHomePageDataCache.INSTANCE.getKeyStatusStatisticCache();
        if (keyData != null) {
            return keyData;
        }

        // 缓存未准备好, 触发缓存更新
        updateCache();

        return KmHomePageDataCache.INSTANCE.getKeyStatusStatisticCache();
    }

    /**
     * 获取锁
     *
     * @return
     */
    private ILock getLock() {
        ICacheClient client = CacheClientHolder.get();
        return client.getLock(new CacheKey(KmHomePageDataCache.KEY_STATUS_STATISTIC_CACHE_KEY));
    }
}
