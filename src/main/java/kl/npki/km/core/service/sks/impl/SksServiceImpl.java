package kl.npki.km.core.service.sks.impl;

import kl.npki.base.core.common.trace.InvocationType;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.base.core.event.EntityChangeEvent;
import kl.npki.base.core.event.EntityChangeEventManager;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.km.core.biz.sks.model.*;
import kl.npki.km.core.biz.sks.service.SksKeyCache;
import kl.npki.km.core.common.RepositoryHelper;
import kl.npki.km.core.common.constants.SksBizConstants;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.core.exception.KmInternalError;
import kl.npki.km.core.exception.KmValidationError;
import kl.npki.km.core.mapper.ConvertServiceHelper;
import kl.npki.km.core.service.sks.ISksService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static kl.npki.km.core.common.constants.KMCoreI18N.SKS_KEY_STATUS_ABOLISHED;

/**
 * <AUTHOR>
 * @date 2022/11/23 20:30
 * @Description: sks服务对接
 */
public class SksServiceImpl implements ISksService {

    private static final Logger log = LoggerFactory.getLogger(SksServiceImpl.class);

    private final SksKeyCache sysKeyCache;

    public SksServiceImpl() {
        this.sysKeyCache = SksKeyCache.INSTANCE;
    }

    @Override
    public String getRandomNumber(int sizeInByte) {
        try {
            byte[] random = EngineHolder.get().getRandom(sizeInByte);
            return Base64Util.base64Encode(random);
        } catch (Exception e) {
            throw KmInternalError.GEN_RANDOM_NUMBER_ERROR.toException(e);
        }
    }

    @Override
    public void createPrivateKey(SksKeyEntity sksKeyEntity) {

        // 1. 主密钥加密私钥
        sksKeyEntity.encryptKey();

        // 2. 初始化有效期
        sksKeyEntity.initValid();

        // 3. 保存实体对象
        Optional<Long> saveId = sksKeyEntity.save();
        if (log.isDebugEnabled()) {
            log.debug("协同密钥保存成功，clientHash=【{}】", sksKeyEntity.getClientKeyHash());
        }

        // 事件通知
        saveId.ifPresent(id -> EntityChangeEventManager.getInstance().publishEvent(EntityChangeEvent.newInstance(sksKeyEntity,
            SksBizConstants.CREATE_PRIVATE_KEY,
            InvocationType.HTTP_SERVICE_INTERFACE))
        );

        // 添加到缓存中
        if (KmConfigWrapper.getKmConf().isSksKeyCacheEnable()) {
            sysKeyCache.put(sksKeyEntity);
        }
    }

    @Override
    public SksKeyEntity getPrivateKey(String clientKeyHash) {
        // 获取未解密的SKS密钥实体
        SksKeyEntity sksKeyEntity = getUnencryptedSksKeyEntity(clientKeyHash);

        // 检测sks密钥状态
        sksKeyEntity.checkStatus();

        // 解密密钥对
        sksKeyEntity.decryptKey();

        return sksKeyEntity;
    }

    /**
     * 获取未解密的SKS密钥实体
     *
     * @param clientKeyHash 客户端密钥哈希
     * @return 未解密的SKS密钥实体
     */
    private SksKeyEntity getUnencryptedSksKeyEntity(String clientKeyHash) {
        SksKeyEntity sksKeyEntity = null;

        // 先从缓存中获取
        if (KmConfigWrapper.getKmConf().isSksKeyCacheEnable()) {
            sksKeyEntity = sysKeyCache.get(clientKeyHash);
        }

        if (sksKeyEntity == null) {
            // 查询sks密钥对
            sksKeyEntity = SksKeyEntity.checkCaEntityExist(clientKeyHash);
            // 添加到缓存中
            if (KmConfigWrapper.getKmConf().isSksKeyCacheEnable()) {
                sysKeyCache.put(sksKeyEntity);
            }
        }

        return sksKeyEntity;
    }

    @Override
    public SksKeyListInfo batchGetPrivateKey(SksKeyBatchSearchInfo sksKeyBatchSearchInfo) {

        // 1. 获取批量同步最大阈值，检测是否超过阈值
        long countNumber = RepositoryHelper.getSksKeyRepository().countPrivateKey(sksKeyBatchSearchInfo);
        int sksKeyBatchMaxData = KmConfigWrapper.getKmConf().getSksKeyBatchMaxData();
        if (countNumber > sksKeyBatchMaxData) {
            String msg = String.format("当前的时间范围【start_time:%s】【end_time:%s】获取的数据量已经超过配置的阈值:%s,请缩小时间范围",
                sksKeyBatchSearchInfo.getStartTime(), sksKeyBatchSearchInfo.getEndTime(), sksKeyBatchMaxData);
            log.error(msg);
            throw KmValidationError.BATCH_NUMBER_EXCESS_ERROR.toException(msg);
        }
        SksKeyListInfo sksKeyListInfo = new SksKeyListInfo();
        if (countNumber == 0) {
            if (log.isDebugEnabled()) {
                log.debug("未查询到需要同步的协同密钥,当前的时间范围【start_time:{}}】【end_time:{}}】", sksKeyBatchSearchInfo.getStartTime(),
                    sksKeyBatchSearchInfo.getEndTime());
            }
            return sksKeyListInfo;
        }

        // 2. 批量查询密钥
        List<SksKeyEntity> sksKeyEntities =
            RepositoryHelper.getSksKeyRepository().listSksKeyEntity(sksKeyBatchSearchInfo);

        if (CollectionUtils.isEmpty(sksKeyEntities)) {
            return sksKeyListInfo;
        }

        // 3. 解密密钥，构造响应
        List<BatchSksKeyInfo> batchSksKeyInfoList = new ArrayList<>(sksKeyEntities.size());
        sksKeyEntities.forEach(sksKeyEntity -> {
            try {
                sksKeyEntity.decryptKey();
                batchSksKeyInfoList.add(ConvertServiceHelper.getConvertService().convert(sksKeyEntity,
                    BatchSksKeyInfo.class));
            } catch (Exception e) {
                log.error("批量同步协同密钥解密异常【key_hash:{}】", sksKeyEntity.getClientKeyHash(), e);
            }

        });

        sksKeyListInfo.setDataSize(batchSksKeyInfoList.size());
        sksKeyListInfo.setData(batchSksKeyInfoList);
        return sksKeyListInfo;

    }

    @Override
    public boolean updatePrivateKey(SksUpdateInfo sksUpdateInfo) {
        // 1. 先查询，必须查询，因为涉及到完整性hash值变更
        SksKeyEntity keyEntity = getUnencryptedSksKeyEntity(sksUpdateInfo.getClientKeyHash());
        // 2. 校验sks密钥状态
        if (Objects.equals(keyEntity.getStatus(), sksUpdateInfo.getStatus())) {
            log.warn("The threshold key status is the same as the expected status, no need to update,clientHash = 【{}】", keyEntity.getClientKeyHash());
            return true;
        }
        // 如果sks密钥已经废除，则不允许再更新为其他状态
        if (EntityStatus.REVOKED.getId().equals(keyEntity.getStatus())) {
            log.error("The threshold key is already revoked, no need to update,clientHash = 【{}】", keyEntity.getClientKeyHash());
            throw KmValidationError.SKS_KEY_STATUS_REVOKED_ERROR.toException(SKS_KEY_STATUS_ABOLISHED, keyEntity.getClientKeyHash());
        }
        // 3. 更新sks密钥状态
        keyEntity.setStatus(sksUpdateInfo.getStatus());
        boolean result = keyEntity.updateStatus();
        // 4. 更新成功
        if (result) {
            // 删除缓存中对应记录
            if (KmConfigWrapper.getKmConf().isSksKeyCacheEnable()) {
                sysKeyCache.clear(keyEntity.getClientKeyHash());
            }
            if (log.isDebugEnabled()) {
                log.debug("The threshold key status updated successfully，clientHash = 【{}】", keyEntity.getClientKeyHash());
            }
            // 发送实体变更事件通知
            SksKeyEntity sksKeyEntity = SksKeyEntity.searchSksKeyEntity(keyEntity.getClientKeyHash());
            EntityChangeEventManager.getInstance().publishEvent(EntityChangeEvent.newInstance(sksKeyEntity,
                SksBizConstants.UPDATE_PRIVATE_KEY,
                InvocationType.HTTP_SERVICE_INTERFACE));

            return true;
        }
        throw KmValidationError.SKS_KEY_UPDATE_ERROR.toException();
    }

    @Override
    public boolean removeSelfCheckPrivateKey(String clientKeyHash) {
        SksKeyEntity keyEntity = new SksKeyEntity(clientKeyHash);
        // 1. 删除自检密钥
        boolean result = keyEntity.removeSelfCheck();
        // 2. 删除成功返回删除结果
        if (result) {
            // 删除缓存中对应记录
            if (KmConfigWrapper.getKmConf().isSksKeyCacheEnable()) {
                sysKeyCache.clear(keyEntity.getClientKeyHash());
            }
            if (log.isDebugEnabled()) {
                log.debug("自检协同密钥删除成功，clientHash = 【{}】", clientKeyHash);
            }
            return true;
        }

        // 3. 删除失败，如果不存在不再删除，如果存在表明为用户数据
        if (keyEntity.isExist()) {
            log.error("协同密钥个人片段：{{}}非自检数据，不容许删除", clientKeyHash);
            throw KmValidationError.SKS_KEY_REMOVE_ERROR.toException();
        } else {
            log.warn("自检协同密钥不存在或者已经删除！clientHash = 【{}】", clientKeyHash);
        }

        return false;
    }
}
