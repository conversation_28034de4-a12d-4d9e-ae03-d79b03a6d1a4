package kl.npki.km.core.service.statistic.impl;

import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.km.core.biz.ca.model.CaEntity;
import kl.npki.km.core.biz.ca.service.CaManager;
import kl.npki.km.core.biz.key.service.SpareKeyPool;
import kl.npki.km.core.biz.stat.model.CaCountInfo;
import kl.npki.km.core.biz.stat.model.CaKeyUseResponse;
import kl.npki.km.core.biz.stat.model.KeyCountInfo;
import kl.npki.km.core.biz.stat.model.KeystoreResponse;
import kl.npki.km.core.common.constants.KeyPosition;
import kl.npki.km.core.common.constants.SksKey;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.core.repository.IKeyCurrentRepository;
import kl.npki.km.core.repository.IKeyHistoryRepository;
import kl.npki.km.core.service.statistic.IKeyStatisticService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/4 10:42
 * @Description:
 */
public class KeyStatisticServiceImpl implements IKeyStatisticService {

    /**
     * 密钥库报表的固定列名称
     */
    private static final String[] KEYSTOREREPORT_HEAD = new String[]{"密钥库分类"};

    /**
     * 密钥使用情况报表的固定列名称
     */
    private static final String[] KEY_USE_REPORT_HEAD = new String[]{"CA名称", "当前库数量", "历史库数量"};

    private final IKeyCurrentRepository keyCurrentRepository;
    private final IKeyHistoryRepository keyHistoryRepository;
    private final SpareKeyPool spareKeyPool;
    private final CaManager caManager;

    public KeyStatisticServiceImpl() {
        this.keyCurrentRepository = RepositoryFactory.get(IKeyCurrentRepository.class);
        this.keyHistoryRepository = RepositoryFactory.get(IKeyHistoryRepository.class);
        this.spareKeyPool = SpareKeyPool.getInstance();
        this.caManager = CaManager.getInstance();
    }

    @Override
    public List<KeystoreResponse> statisticKeystoreReport() {
        List<KeystoreResponse> keystoreResponseList = new ArrayList<>(4);
        // 获取初始值
        Set<String> keyTypeSet = getStatisticKeyType();

        // 1. 查询在用库
        List<KeyCountInfo> currentKeyCounts = keyCurrentRepository.countKeyType();
        keystoreResponseList.add(buildKeyTypeCount(keyTypeSet, currentKeyCounts, KeyPosition.POS_CURRENT));

        // 2. 查询历史库
        List<KeyCountInfo> historyKeyCounts = keyHistoryRepository.countKeyType();
        keystoreResponseList.add(buildKeyTypeCount(keyTypeSet, historyKeyCounts, KeyPosition.POS_HISTORY));

        // 3. 查询备用库
        List<KeyCountInfo> poolKeyCounts = spareKeyPool.getTotalCount();
        keystoreResponseList.add(buildKeyTypeCount(keyTypeSet, poolKeyCounts, KeyPosition.POS_POOL));

        return keystoreResponseList;
    }

    private KeystoreResponse buildKeyTypeCount(Set<String> keyTypeSet, List<KeyCountInfo> keyCountInfoList,
                                               KeyPosition keyPosition) {
        // 初始化统计数据
        keyTypeSet.forEach(keyType -> {
            if (CollectionUtils.isNotEmpty(keyCountInfoList)) {
                for (KeyCountInfo keyCountInfo : keyCountInfoList) {
                    if (keyType.equals(keyCountInfo.getKeyType())) {
                        return;
                    }
                }
            }

            keyCountInfoList.add(new KeyCountInfo(keyType, 0));
        });

        processSksKeyTypes(keyCountInfoList);
        return new KeystoreResponse(keyPosition.tr(), keyCountInfoList);
    }

    /**
     * 处理SKS密钥类型相关逻辑：过滤和映射
     * @param keyCountInfoList 密钥统计信息列表
     */
    private void processSksKeyTypes(List<KeyCountInfo> keyCountInfoList) {
        if (CollectionUtils.isEmpty(keyCountInfoList)) {
            return;
        }

        boolean isEnableSks = KmConfigWrapper.getKmFeatureConf().isEnableSks();
        String sksDbName = SksKey.SKS.getName();
        String sksDisplayName = SksKey.SKS.tr();

        if (!isEnableSks) {
            // 如果SKS功能未启用，过滤掉任何SKS相关的统计结果
            keyCountInfoList.removeIf(keyCountInfo -> sksDbName.equals(keyCountInfo.getKeyType()));
        } else {
            // 如果SKS功能启用，将数据库名称映射为显示名称
            for (KeyCountInfo keyCountInfo : keyCountInfoList) {
                if (sksDbName.equals(keyCountInfo.getKeyType())) {
                    keyCountInfo.setKeyType(sksDisplayName);
                    break;
                }
            }
        }
    }

    private Set<String> getStatisticKeyType() {
        Set<String> keyTypeSet = KmConfigWrapper.getAsymmetricAndPostQuantumKeyTypesWithoutSM9Set();
        if (KmConfigWrapper.getKmFeatureConf().isEnableSks()) {
            keyTypeSet.add(SksKey.SKS.getName());
        }
        return keyTypeSet;
    }

    @Override
    public List<CaKeyUseResponse> statisticKeyUseReport() {
        List<CaEntity> caEntityList = caManager.getCaEntityList();
        List<String> supportAsymAlgo = KmConfigWrapper.getAsymmetricAndPostQuantumKeyTypesWithoutSM9List();
        // 获取所有 CA 的 ID
        List<Long> caIds = caEntityList.stream().map(CaEntity::getId).collect(Collectors.toList());

        // 一次性查询所有 CA 的当前密钥使用数量
        Map<Long, Integer> currentCountMap = keyCurrentRepository.countByCaIds(caIds).stream()
            .collect(Collectors.toMap(CaCountInfo::getCaId, CaCountInfo::getCount));

        // 一次性查询所有 CA 的历史密钥使用数量
        Map<Long, Integer> historyCountMap = keyHistoryRepository.countByCaIds(caIds).stream()
            .collect(Collectors.toMap(CaCountInfo::getCaId, CaCountInfo::getCount));

        // 一次性查询所有 CA 的所有算法类型的使用数量
        Map<Long, Map<String, Integer>> keyUseNumMap = getUseNumForAllCaAndAlgos(caEntityList, supportAsymAlgo);

        List<CaKeyUseResponse> caKeyUseResponseList = new ArrayList<>(caEntityList.size());

        for (CaEntity caEntity : caEntityList) {
            CaKeyUseResponse caKeyUseResponse = new CaKeyUseResponse();
            caKeyUseResponse.setCaName(caEntity.getCaName());
            caKeyUseResponse.setCurrentCount(currentCountMap.getOrDefault(caEntity.getId(), 0));
            caKeyUseResponse.setHistoryCount(historyCountMap.getOrDefault(caEntity.getId(), 0));

            List<KeyCountInfo> keyCountInfoList = supportAsymAlgo.stream()
                .map(keyType -> new KeyCountInfo(keyType,
                    keyUseNumMap.getOrDefault(caEntity.getId(), Collections.emptyMap()).getOrDefault(keyType, 0)))
                .collect(Collectors.toList());

            caKeyUseResponse.setKeyCountInfoList(keyCountInfoList);
            caKeyUseResponseList.add(caKeyUseResponse);
        }

        return caKeyUseResponseList;
    }

    @Override
    public List<String> statisticKeyServiceReport() {
        return Collections.emptyList();
    }

    @Override
    public void exportKeystoreReport(OutputStream outputStream) {
        ExcelWriterBuilder excelWriterBuilder = new ExcelWriterBuilder();
        excelWriterBuilder.file(outputStream);

        // 构建标题列，密钥类型
        Set<String> statisticKeyType = getStatisticKeyType();
        excelWriterBuilder.head(buildKeystoreReportHead(statisticKeyType));
        // 构建数据
        excelWriterBuilder.sheet().doWrite(buildKeystoreReportData(statisticKeyType));
    }

    @Override
    public void exportKeyUseReport(OutputStream outputStream) {
        ExcelWriterBuilder excelWriterBuilder = new ExcelWriterBuilder();
        excelWriterBuilder.file(outputStream);

        // 构建标题列
        Set<String> statisticKeyType = getStatisticKeyType();
        excelWriterBuilder.head(buildKeyUseReportHead(statisticKeyType));
        // 构建数据
        excelWriterBuilder.sheet().doWrite(buildKeyUseReportData());
    }

    /**
     * 获取所有 CA 的所有算法类型的使用数量
     *
     * @param caEntities
     * @param algoTypes
     * @return
     */
    public Map<Long, Map<String, Integer>> getUseNumForAllCaAndAlgos(List<CaEntity> caEntities, List<String> algoTypes) {
        Map<Long, Map<String, Integer>> result = new HashMap<>();

        for (CaEntity caEntity : caEntities) {
            Long caId = caEntity.getId();
            String keyHash = caEntity.getKeyIndex();
            String certSn = caEntity.getCertSn();
            Map<String, Integer> algoUseNumMap = new HashMap<>();

            for (String algoType : algoTypes) {
                AsymAlgo asymAlgo = new AsymAlgo(algoType);
                Integer useNum = caManager.getUseNum(asymAlgo, certSn, keyHash);
                algoUseNumMap.put(algoType, useNum);
            }

            result.put(caId, algoUseNumMap);
        }

        return result;
    }

    private List<List<String>> buildKeyUseReportHead(Set<String> statisticKeyType) {
        List<List<String>> headList = new ArrayList<>(KEY_USE_REPORT_HEAD.length + statisticKeyType.size());
        // 固定的三个列名
        for (String headStr : KEY_USE_REPORT_HEAD) {
            List<String> head = new ArrayList<>();
            head.add(headStr);
            headList.add(head);
        }
        // 构建算法类型的列名
        for (String headStr : statisticKeyType) {
            // SKS类型不统计
            if (SksKey.SKS.tr().equals(headStr)) {
                continue;
            }
            List<String> head = new ArrayList<>();
            head.add(headStr);
            headList.add(head);
        }
        return headList;
    }

    private List<List<String>> buildKeyUseReportData() {
        List<CaEntity> caEntityList = caManager.getCaEntityList();
        List<String> supportAsymAlgo = KmConfigWrapper.getAsymmetricAndPostQuantumKeyTypesWithoutSM9List();
        List<List<String>> result = new ArrayList<>(caEntityList.size());
        // 构建行数据
        for (CaEntity caEntity : caEntityList) {
            List<String> line = new ArrayList<>();

            // 第1列，ca名称
            line.add(caEntity.getCaName());
            // 第2列，当前库数量
            line.add(String.valueOf(keyCurrentRepository.countByCa(caEntity.getId())));
            // 第3列，历史库数量
            line.add(String.valueOf(keyHistoryRepository.countByCa(caEntity.getId())));

            // 一个算法类型构建一列
            for (String keyType : supportAsymAlgo) {
                Integer useNum = caManager.getUseNum(new AsymAlgo(keyType), caEntity.getCertSn(), caEntity.getKeyIndex());
                line.add(String.valueOf(useNum));
            }

            result.add(line);
        }

        return result;
    }

    private List<List<String>> buildKeystoreReportHead(Set<String> statisticKeyType) {
        List<List<String>> headList = new ArrayList<>(KEYSTOREREPORT_HEAD.length + statisticKeyType.size());
        // 设置固定列头
        for (String headStr : KEYSTOREREPORT_HEAD) {
            List<String> head = new ArrayList<>();
            head.add(headStr);
            headList.add(head);
        }
        // 构建算法类型的列名
        for (String headStr : statisticKeyType) {
            List<String> head = new ArrayList<>();
            head.add(headStr);
            headList.add(head);
        }
        return headList;
    }

    private List<List<String>> buildKeystoreReportData(Set<String> statisticKeyType) {
        List<List<String>> result = new ArrayList<>(3);

        // 第1行数据集合
        List<String> line0 = new ArrayList<>();
        // 第1行第1列数据，在用库
        line0.add(KeyPosition.POS_CURRENT.getDesc());
        Map<String, Integer> line0Map = toMap(keyCurrentRepository.countKeyType());

        // 第2行数据集合
        List<String> line1 = new ArrayList<>();
        // 第1行第1列数据，历史库
        line1.add(KeyPosition.POS_HISTORY.getDesc());
        Map<String, Integer> line1Map = toMap(keyHistoryRepository.countKeyType());

        // 第3行数据集合
        List<String> line2 = new ArrayList<>();
        // 第1行第1列数据，备用库
        line2.add(KeyPosition.POS_POOL.getDesc());
        Map<String, Integer> line2Map = toMap(spareKeyPool.getTotalCount());

        for (String keyType : statisticKeyType) {
            Integer line0Count = line0Map.get(keyType);
            if (ObjectUtils.isEmpty(line0Count)) {
                line0Count = 0;
            }
            Integer line1Count = line1Map.get(keyType);
            if (ObjectUtils.isEmpty(line1Count)) {
                line1Count = 0;
            }
            Integer line2Count = line2Map.get(keyType);
            if (ObjectUtils.isEmpty(line2Count)) {
                line2Count = 0;
            }
            // 第1行，第n列的数据，密钥类型对应的数量
            line0.add(String.valueOf(line0Count));
            // 第2行，第n列的数据，密钥类型对应的数量
            line1.add(String.valueOf(line1Count));
            // 第3行，第n列的数据，密钥类型对应的数量
            line2.add(String.valueOf(line2Count));
        }

        result.add(line0);
        result.add(line1);
        result.add(line2);

        return result;
    }

    private Map<String, Integer> toMap(List<KeyCountInfo> currentKeyCounts) {
        Map<String, Integer> data = new HashMap<>();
        for (KeyCountInfo keyCountInfo : currentKeyCounts) {
            String keyType = mapSksKeyType(keyCountInfo.getKeyType());

            if (keyType != null) {
                data.put(keyType, keyCountInfo.getCount());
            }
        }
        return data;
    }

    /**
     * 映射SKS密钥类型：根据SKS功能开关状态决定如何处理SKS类型
     * @param keyType 原始密钥类型
     * @return 映射后的密钥类型，如果返回null表示需要过滤掉
     */
    private String mapSksKeyType(String keyType) {
        boolean isEnableSks = KmConfigWrapper.getKmFeatureConf().isEnableSks();
        String sksDbName = SksKey.SKS.getName();
        String sksDisplayName = SksKey.SKS.tr();

        if (sksDbName.equals(keyType)) {
            if (isEnableSks) {
                return sksDisplayName;
            } else {
                return null;
            }
        } else if (sksDisplayName.equals(keyType)) {
            if (isEnableSks) {
                return sksDisplayName;
            } else {
                return null;
            }
        } else {
            return keyType;
        }
    }

}
