package kl.npki.km.core.service.ca.impl;

import kl.npki.base.core.biz.dataprotect.service.impl.DataProtectServiceHashMacSm3Impl;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.km.core.biz.trans.service.IProtocolProcessor;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.core.exception.KmValidationError;
import org.apache.commons.lang3.StringUtils;

/**
 * 用于KM快速接入CA的抽象服务
 *
 * <AUTHOR>
 */
public abstract class AbstractCaFastService implements IProtocolProcessor {

    protected final DataProtectServiceHashMacSm3Impl hmacService;

    protected AbstractCaFastService() {
        String systemAuthToken = BaseConfigWrapper.getSysConfig().getSystemAuthToken();
        if (StringUtils.isBlank(systemAuthToken)) {
            throw KmValidationError.SYSTEM_AUTH_TOKEN_NOT_CONFIG.toException();
        }
        this.hmacService = new DataProtectServiceHashMacSm3Impl(systemAuthToken.getBytes());
    }

    public static void checkCaFastApi() {
        if (!KmConfigWrapper.getKmConf().isEnableCaFastApi()) {
            throw KmValidationError.CA_FAST_API_NOT_ENABLED.toException();
        }
    }
}
