package kl.npki.km.core.service.statistic;

import kl.npki.base.core.biz.log.model.ApiLogCountServiceResult;
import kl.npki.km.core.biz.stat.model.CaBizStatisticInfo;
import kl.npki.km.core.biz.stat.model.CaBizStatisticResponse;
import kl.npki.km.core.biz.statistic.BizStatisticInfo;
import kl.npki.km.core.common.FixedSizeCircularQueue;

import java.io.OutputStream;
import java.time.LocalDate;
import java.util.List;

/**
 * CA服务统计业务接口
 * <AUTHOR>
 */
public interface ICaBizStatisticService {

    /**
     * 更新首页报表缓存数据
     */
    void updateAllCaCache();

    /**
     * 获取首页报表数据
     * @param caName
     * @return
     */
    FixedSizeCircularQueue<CaBizStatisticInfo> getHomePageData(String caName);
    /**
     * 统计ca业务报表
     * @param conditionInfo 统计条件
     * @return
     */
    CaBizStatisticResponse statisticCaBizReport(BizStatisticInfo conditionInfo);

    /**
     * 统计指定日期(某天)的结果
     * @param caList
     * @param localDate
     * @return
     */
    List<ApiLogCountServiceResult> statisticOneDay(List<String> caList, LocalDate localDate);

    /**
     * 导出报表
     * @param conditionInfo 统计条件
     * @param outputStream 响应流
     */
    void exportReport(BizStatisticInfo conditionInfo, OutputStream outputStream);
}
