package kl.npki.km.core.service.home.impl;

import kl.nbase.cache.client.ICacheClient;
import kl.nbase.cache.key.CacheKey;
import kl.nbase.cache.lock.ILock;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.tenantholders.CacheClientHolder;
import kl.npki.km.core.biz.ca.model.CaEntity;
import kl.npki.km.core.biz.home.model.CaKeyStatisticInfo;
import kl.npki.km.core.biz.home.service.KmHomePageDataCache;
import kl.npki.km.core.biz.stat.model.CaCountInfo;
import kl.npki.km.core.biz.stat.model.KeyCountInfo;
import kl.npki.km.core.common.RepositoryHelper;
import kl.npki.km.core.exception.KmValidationError;
import kl.npki.km.core.repository.ICaRepository;
import kl.npki.km.core.repository.IKeyCurrentRepository;
import kl.npki.km.core.repository.IKeyHistoryRepository;
import kl.npki.km.core.service.home.CaKeyStatisticService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

/**
 * 根据CA，统计密钥数据业务
 *
 * <AUTHOR>
 */
public class CaKeyStatisticServiceImpl implements CaKeyStatisticService {

    private static final Logger log = LoggerFactory.getLogger(CaKeyStatisticServiceImpl.class);

    private final IKeyCurrentRepository keyCurrentRepository;
    private final IKeyHistoryRepository keyHistoryRepository;
    private final ICaRepository caRepository;

    public CaKeyStatisticServiceImpl() {
        this.keyCurrentRepository = RepositoryFactory.get(IKeyCurrentRepository.class);
        this.keyHistoryRepository = RepositoryFactory.get(IKeyHistoryRepository.class);
        this.caRepository = RepositoryHelper.getCaRepository();
    }

    @Override
    public void updateAllCaKeyStatisticCache() {
        ILock lock = getLock();
        try {
            if (lock.tryLock()) {
                // 列举系统中所有已接入的CA
                List<CaEntity> caEntities = caRepository.searchCaEntityList();
                for (CaEntity caEntity : caEntities) {
                    updateCache(caEntity);
                }
            }
        } catch (Exception e) {
            log.error("更新统计CA密钥数据缓存失败！", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public CaKeyStatisticInfo getCaKeyStatisticInfo(String caName) {
        CaKeyStatisticInfo caKeyStatisticInfo = KmHomePageDataCache.INSTANCE.getCaKeyStatisticInfo(caName);

        if (caKeyStatisticInfo != null) {
            return caKeyStatisticInfo;
        }

        // 待查询的CA不存在，则直接抛出异常
        CaEntity caEntity = caRepository.searchCaEntityExceptRevoked(caName);
        if (caEntity == null) {
            throw KmValidationError.CA_NOT_EXISTING_ERROR.toException("caName:" + caName);
        }

        // 缓存未准备好, 触发缓存更新
        ILock lock = getLock();
        try {
            if (lock.tryLock() && KmHomePageDataCache.INSTANCE.getCaKeyStatisticInfo(caName) == null) {
                updateCache(caEntity);
            }
        } catch (Exception e) {
            log.error("更新统计CA密钥数据缓存失败！caName:{}", caName, e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        // 从缓存中获取数据
        return KmHomePageDataCache.INSTANCE.getCaKeyStatisticInfo(caName);
    }

    private void updateCache(CaEntity caEntity) {
        Long caId = caEntity.getId();
        // 当前库密钥数量
        Integer keyCurrentCount = keyCurrentRepository.countByCaIds(Collections.singletonList(caId))
            .stream()
            .findFirst()
            .map(CaCountInfo::getCount)
            .orElse(0);
        // 历史库密钥数量
        Integer keyHisCount = keyHistoryRepository.countByCaIds(Collections.singletonList(caId))
            .stream()
            .findFirst()
            .map(CaCountInfo::getCount)
            .orElse(0);

        // 在用密钥类型统计
        List<KeyCountInfo> keyCountInfos = keyCurrentRepository.countKeyTypeByCa(caId);
        // 封装结果
        CaKeyStatisticInfo caKeyStatisticInfo = new CaKeyStatisticInfo(keyCurrentCount, keyHisCount, keyCountInfos);

        // 更新缓存
        KmHomePageDataCache.INSTANCE.putCaKeyStatisticCache(caEntity.getCaName(), caKeyStatisticInfo);
    }

    /**
     * 获取锁
     *
     * @return
     */
    private ILock getLock() {
        ICacheClient client = CacheClientHolder.get();
        return client.getLock(new CacheKey(KmHomePageDataCache.CA_KEY_STATISTIC_CACHE_KEY));
    }
}
