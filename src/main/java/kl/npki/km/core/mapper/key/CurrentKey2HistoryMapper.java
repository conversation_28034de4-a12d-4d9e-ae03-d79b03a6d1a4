package kl.npki.km.core.mapper.key;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.biz.key.model.CurrentKeyEntity;
import kl.npki.km.core.biz.key.model.HistoryKeyEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * @Author: guoq
 * @Date: 2022/9/16
 * @description: 当前库密钥转历史库密钥
 */
@Mapper
public interface CurrentKey2HistoryMapper extends BaseMapper<CurrentKeyEntity, HistoryKeyEntity> {

    /**
     * 将 CurrentKeyEntity 映射到 HistoryKeyEntity
     *
     * @param source CurrentKeyEntity对象
     * @return HistoryKeyEntity对象
     */
    @Mapping(source = "createTime", target = "escrowTime")
    @Mapping(target = "archiveReason", ignore = true)
    @Override
    HistoryKeyEntity map(CurrentKeyEntity source);

    /**
     * 将 HistoryKeyEntity 映射到 CurrentKeyEntity
     *
     * @param target HistoryKeyEntity对象
     * @return CurrentKeyEntity对象
     */
    @Mapping(source = "escrowTime", target = "createTime")
    @Mapping(target = "keyStatus", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "escrowMsg", ignore = true)
    @Override
    CurrentKeyEntity reverseMap(HistoryKeyEntity target);
}
