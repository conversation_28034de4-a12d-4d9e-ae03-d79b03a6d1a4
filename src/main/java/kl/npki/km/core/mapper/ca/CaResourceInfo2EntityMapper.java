package kl.npki.km.core.mapper.ca;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.biz.ca.model.CaResourceEntity;
import kl.npki.km.core.biz.ca.model.CaResourceInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * @Author: guoq
 * @Date: 2022/8/31
 * @description: ca资源info 实体转换
 */
@Mapper
public interface CaResourceInfo2EntityMapper extends BaseMapper<CaResourceInfo, CaResourceEntity> {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "caId", ignore = true)
    @Mapping(target = "keyNum", ignore = true)
    @Mapping(target = "resourceStatus", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Override
    CaResourceEntity map(CaResourceInfo source);

    @Override
    CaResourceInfo reverseMap(CaResourceEntity target);
}
