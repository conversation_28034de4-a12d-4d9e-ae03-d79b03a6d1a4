package kl.npki.km.core.mapper.key;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.biz.key.model.CurrentKeyEntity;
import kl.npki.km.core.biz.sks.model.SksKeyEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CurrentKeyEntity2SksKeyEntityMapper extends BaseMapper<CurrentKeyEntity, SksKeyEntity>{


    @Override
    @Mapping(source = "encCertSn", target = "clientKeyHash")
    @Mapping(source = "subject", target = "userId")
    @Mapping(source = "mediumSn", target = "mediaId")
    @Mapping(source = "privateKey", target = "serverKeySection")
    @Mapping(source = "userInfoExs", target = "keySource")
    @Mapping(source = "keyStatus", target = "status")
    SksKeyEntity map(CurrentKeyEntity source);


    @Override
    @Mapping(source = "clientKeyHash", target = "encCertSn")
    @Mapping(source = "userId", target = "subject")
    @Mapping(source = "mediaId", target = "mediumSn")
    @Mapping(source = "serverKeySection", target = "privateKey")
    @Mapping(source = "keySource", target = "userInfoExs")
    @Mapping(source = "status", target = "keyStatus")
    CurrentKeyEntity reverseMap(SksKeyEntity target);
}