package kl.npki.km.core.utils;

import kl.nbase.exception.BaseException;
import kl.nbase.security.asn1.ASN1Integer;
import kl.nbase.security.asn1.cmp.PKIFreeText;
import kl.nbase.security.asn1.custom.gm.gm0014.km.response.ErrorPkgRespond;
import kl.npki.base.core.asn1.custom.KmResponse;
import kl.npki.km.core.common.constants.KmConstants;
import kl.npki.km.core.exception.KmInternalError;

import java.io.IOException;

/**
 * 响应工具类
 * <AUTHOR>
 */
public class KmResponseUtil {

    /**
     * 构造KM异常响应结果
     * @param exception
     * @return
     */
    public static byte[] buildErrorResponse(BaseException exception) {
        // 构建异常响应
        ErrorPkgRespond errorPkgRespond = new ErrorPkgRespond(
                new ASN1Integer(Long.parseLong(exception.getSecondCode())),
                new PKIFreeText(exception.getMessage()));

        try {
            return new KmResponse(errorPkgRespond).getEncoded();
        } catch (IOException e) {
            throw KmInternalError.ASN_ENCODE_ERROR.toException(e);
        }
    }

    public static byte[] buildErrorResponse(Exception exception) {
        Long code = null;
        if (exception instanceof BaseException) {
            code = Long.parseLong(((BaseException) exception).getSecondCode());
        } else {
            code = Long.valueOf(KmConstants.KM_SERVICE_ERROR);
        }
        // 构建异常响应
        ErrorPkgRespond errorPkgRespond = new ErrorPkgRespond(
                new ASN1Integer(code),
                new PKIFreeText(exception.getMessage()));

        try {
            return new KmResponse(errorPkgRespond).getEncoded();
        } catch (IOException e) {
            throw KmInternalError.ASN_ENCODE_ERROR.toException(e);
        }
    }
}
