package kl.npki.km.core.utils;

import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.IntStream;

/**
 * 日期统计工具类
 *
 * <AUTHOR>
 */
public class DateStatisticUtil {

    private DateStatisticUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 拿到指定日期，月份的第一天
     *
     * @param localDateTime
     * @return
     */
    public static LocalDateTime getFirstDayOfMonth(LocalDateTime localDateTime) {
        // 获取月初第一天的起始时刻（00:00:00）
        return localDateTime.with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
    }

    /**
     * 获取指定日期 月末的最后一天
     *
     * @param localDateTime
     * @return
     */
    public static LocalDateTime getLastDayOfMonth(LocalDateTime localDateTime) {
        // 获取当月最后一天的 23:59:59.999999999 时刻
        return localDateTime.with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
    }

    /**
     * 得到近几个月的月份
     */
    public static List<LocalDateTime> getRecentMonths(int mothCount) {
        if (mothCount <= 0) {
            return Collections.emptyList();
        }

        List<LocalDateTime> result = new ArrayList<>();
        YearMonth currentYearMonth = YearMonth.now();

        // 从当前月往前推 (n-1) 个月，包含当前月
        for (int i = 0; i < mothCount; i++) {
            YearMonth targetYearMonth = currentYearMonth.minusMonths(i);
            LocalDateTime monthStart = targetYearMonth.atDay(1).atStartOfDay();
            result.add(monthStart);
        }
        return result;
    }


    /**
     * 计算两个日期间的时间差
     *
     * @param startDate 起始时间
     * @param endDate   结束时间
     * @return
     */
    public static List<LocalDate> calculateDateDifferenceOfDay(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> result = new ArrayList<>();
        Period period = Period.between(startDate, endDate);
        // 相差的天数
        int days = period.getDays();
        if (days < 0) {
            return result;
        }
        // 生成包含时间差内每一天的LocalDate集合
        IntStream.rangeClosed(1, days)
                .mapToObj(startDate::plusDays)
                .forEach(result::add);

        return result;
    }

    /**
     * 是否等于当前月份
     *
     * @param targetDateTime
     * @return
     */
    public static boolean isEqualCurrentMonth(LocalDateTime targetDateTime) {
        // 当前时间
        LocalDateTime now = LocalDateTime.now();
        YearMonth nowMonth = YearMonth.of(now.getYear(), now.getMonth());
        YearMonth targetMonth = YearMonth.of(targetDateTime.getYear(), targetDateTime.getMonth());

        return nowMonth.equals(targetMonth);
    }
}
