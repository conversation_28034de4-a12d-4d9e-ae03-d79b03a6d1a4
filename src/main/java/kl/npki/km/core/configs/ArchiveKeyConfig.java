package kl.npki.km.core.configs;

import kl.nbase.config.SwitchableRefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.km.core.common.constants.KmConstants;

/**
 * @Author: guoq
 * @Date: 2022/10/14
 * @description: 归档密钥配置
 */
public class ArchiveKeyConfig extends SwitchableRefreshableConfigAdaptor {

    private static final long serialVersionUID = 5928142561487721144L;

    private boolean enable = true;

    /**
     * 延迟归档时间(天)
     */
    private int lateArchiveTime = 365;

    /**
     * 归档分页阈值(超过多少条开始分页)
     */
    private int archivePageThreshold = KmConstants.PAGE_THRESHOLD;

    /**
     * 密钥生成执行间隔 cron表达式
     * 默认每天0点执行一次
     */
    private String archiveCronExpression = "0 0 0 * * ?";

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public int getLateArchiveTime() {
        return lateArchiveTime;
    }

    public void setLateArchiveTime(int lateArchiveTime) {
        this.lateArchiveTime = lateArchiveTime;
    }

    public int getArchivePageThreshold() {
        return archivePageThreshold;
    }

    public void setArchivePageThreshold(int archivePageThreshold) {
        this.archivePageThreshold = archivePageThreshold;
    }

    public String getArchiveCronExpression() {
        return archiveCronExpression;
    }

    public void setArchiveCronExpression(String archiveCronExpression) {
        this.archiveCronExpression = archiveCronExpression;
    }

    @Override
    public String id() {
        return BaseConstant.PKI_CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return KmConstants.CONFIG_ARCHIVE_PREFIX;
    }

}
