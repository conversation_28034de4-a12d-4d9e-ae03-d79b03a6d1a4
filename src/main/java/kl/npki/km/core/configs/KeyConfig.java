package kl.npki.km.core.configs;

import kl.nbase.config.SwitchableRefreshableConfigAdaptor;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.km.core.common.constants.KmConstants;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: guoq
 * @Date: 2022/8/19
 * @description: 密钥生成配置
 */
public class KeyConfig extends SwitchableRefreshableConfigAdaptor {

    private static final long serialVersionUID = 2013173390194419317L;

    /**
     * 检查缓存池密钥池是否已经达到需要补充的阈值,间隔多少秒检查一次
     */
    private int cacheCheckInterval = 30;

    /**
     * 密钥池cache的大小
     */
    private int cacheSize = 200;

    /**
     * 密钥池是否自动补充
     */
    private boolean cacheAutoSupply = true;

    /**
     * 缓存补充阈值百分比，取值范围0-1，默认0.5表示50%
     */
    private double cacheSupplyThreshold = 0.5;

    /**
     * 密钥唯一性检查
     */
    private boolean keyUniquenessCheck = true;

    /**
     * 各类型密钥限制
     */
    private List<KeyLimitConfig> keyLimits;

    @Override
    public String id() {
        return BaseConstant.PKI_CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return KmConstants.CONFIG_KEY_PREFIX;
    }

    public int getCacheCheckInterval() {
        return cacheCheckInterval;
    }

    public void setCacheCheckInterval(int cacheCheckInterval) {
        this.cacheCheckInterval = cacheCheckInterval;
    }

    public int getCacheSize() {
        return cacheSize;
    }

    public void setCacheSize(int cacheSize) {
        this.cacheSize = cacheSize;
    }

    /**
     * 获取缓存补充阈值百分比
     * @return 缓存补充阈值百分比，取值范围0-1
     */
    public double getCacheSupplyThreshold() {
        return cacheSupplyThreshold;
    }

    /**
     * 设置缓存补充阈值百分比
     * @param cacheSupplyThreshold 缓存补充阈值百分比，取值范围0-1
     */
    public void setCacheSupplyThreshold(double cacheSupplyThreshold) {
        this.cacheSupplyThreshold = cacheSupplyThreshold;
    }

    public boolean isCacheAutoSupply() {
        return cacheAutoSupply;
    }

    public void setCacheAutoSupply(boolean cacheAutoSupply) {
        this.cacheAutoSupply = cacheAutoSupply;
    }

    public boolean isKeyUniquenessCheck() {
        return keyUniquenessCheck;
    }

    public void setKeyUniquenessCheck(boolean keyUniquenessCheck) {
        this.keyUniquenessCheck = keyUniquenessCheck;
    }

    /**
     * 根据所有支持的keyType初始化配置
     */
    public void initKeyLimitConf() {
        List<String> supportAsymAlgo = KmConfigWrapper.getAsymmetricAndPostQuantumKeyTypesWithoutSM9List();
        // 获取已经配置的密钥类型
        Set<String> existKeyType = CollectionUtils.isNotEmpty(keyLimits) ?
            keyLimits.stream().map(KeyLimitConfig::getKeyType).collect(Collectors.toSet()) : new HashSet<>();
        // 将差集补充到配置中
        supportAsymAlgo.forEach(keyType -> {
                if (!existKeyType.contains(keyType)) {
                    keyLimits.add(new KeyLimitConfig(keyType));
                }
            }
        );

    }

    public List<KeyLimitConfig> getKeyLimits() {
        return keyLimits;
    }

    public void setKeyLimits(List<KeyLimitConfig> keyLimits) {
        this.keyLimits = keyLimits;
    }

    public Optional<KeyLimitConfig> getKeyLimitsByKeyType(AsymAlgo keyType) {
        return keyLimits.stream().filter(keyLimitConf -> keyLimitConf.getKeyType().equals(keyType.getAlgoName())).findFirst();
    }
}
