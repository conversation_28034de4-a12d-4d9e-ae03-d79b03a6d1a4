package kl.npki.km.core.configs.wrapper;

import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.km.core.configs.*;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class KmConfigWrapper extends BaseConfigWrapper {

    private KmConfigWrapper() {
        super();
    }

    public static KmConfig getKmConf() {
        return getHolder().get(KmConfig.class);
    }

    public static KmFeatureConfig getKmFeatureConf() {
        return getHolder().get(KmFeatureConfig.class);
    }

    public static KeyConfig getKeyConf() {
        return getHolder().get(KeyConfig.class);
    }

    public static ArchiveKeyConfig getArchiveKeyConf() {
        return getHolder().get(ArchiveKeyConfig.class);
    }

    public static ApiLogStatisticConfig getApiLogStatisticConfig() {
        return getHolder().get(ApiLogStatisticConfig.class);
    }

    public static Set<String> getAsymmetricAndPostQuantumKeyTypesWithoutSM9Set() {
        Set<String> keyTypeSet = BaseConfigWrapper.getSysAlgoConfig().extractAsymmetricAndPostQuantumSet();
        // 过滤SM9算法
        keyTypeSet.removeIf(keyType -> AsymAlgo.SM9.getAlgoName().equals(keyType));
        return keyTypeSet;
    }

    public static List<String> getAsymmetricAndPostQuantumKeyTypesWithoutSM9List() {
        List<String> supportAsymAlgo = BaseConfigWrapper.getSysAlgoConfig().extractAsymmetricAndPostQuantumList();
        // 过滤SM9算法
        supportAsymAlgo.removeIf(keyType -> AsymAlgo.SM9.getAlgoName().equals(keyType));
        return supportAsymAlgo;
    }
}
