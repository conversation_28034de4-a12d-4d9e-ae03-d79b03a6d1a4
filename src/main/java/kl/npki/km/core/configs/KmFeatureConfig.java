package kl.npki.km.core.configs;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.nbase.config.RefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.km.core.common.constants.KmConstants;

/**
 * KM 模块功能配置
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 * @date 2025/6/6
 */
public class KmFeatureConfig extends RefreshableConfigAdaptor {

    /**
     * 是否启用PKI KM功能
     */
    @Schema(description = "是否启用PKI KM功能", defaultValue = "true")
    private boolean enablePkiKm = true;

    /**
     * 是否启动IBC KM功能
     */
    @Schema(description = "是否启用IBC KM功能", defaultValue = "false")
    private boolean enableIbcKm;

    /**
     * 是否启用SKS协同密钥功能
     */
    @Schema(description = "是否启用SKS协同密钥功能", defaultValue = "false")
    private boolean enableSks;

    public boolean isEnablePkiKm() {
        return enablePkiKm;
    }

    public void setEnablePkiKm(boolean enablePkiKm) {
        this.enablePkiKm = enablePkiKm;
    }

    public boolean isEnableIbcKm() {
        return enableIbcKm;
    }

    public void setEnableIbcKm(boolean enableIbcKm) {
        this.enableIbcKm = enableIbcKm;
    }

    public boolean isEnableSks() {
        return enableSks;
    }

    public void setEnableSks(boolean enableSks) {
        this.enableSks = enableSks;
    }

    @Override
    public String id() {
        return BaseConstant.CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return KmConstants.CONFIG_KM_FEATURE_PREFIX;
    }

}
