package kl.npki.km.core.configs;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;

import java.io.Serializable;

/**
 * @Author: guoq
 * @Date: 2022/8/24
 * @description: 对应密钥类型生产上限
 */
public class KeyLimitConfig implements Serializable {

    private static final long serialVersionUID = 9210486569161837648L;

    @Schema(description = "是否启用该密钥类型", defaultValue = "true")
    private boolean enable = false;

    /**
     * 密钥类型
     */
    @Schema(description = "密钥类型", defaultValue = "SM2")
    @NotBlank(message = "密钥类型不能为空！")
    private String keyType;

    /**
     * 密钥产生上限
     */
    @Schema(description = "密钥产生上限", defaultValue = "1000")
    @Min(value = 0, message = "密钥产生上限不能小于0")
    private int limit = 0;

    /**
     * 密钥生成执行间隔 cron表达式
     * (每种密钥支持单独配置定时任务，如果该配置为空，默认使用外层密钥生成表达式)
     */
    @Schema(description = "密钥生成执行间隔(cron表达式)")
    @NotBlank(message = "密钥生成执行间隔不能为空！")
    private String keyGenCronExpression = "0 0 0/1 * * ?";

    public KeyLimitConfig() {
    }

    public KeyLimitConfig(String keyType) {
        this.keyType = keyType;
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public String getKeyGenCronExpression() {
        return keyGenCronExpression;
    }

    public void setKeyGenCronExpression(String keyGenCronExpression) {
        this.keyGenCronExpression = keyGenCronExpression;
    }
}
