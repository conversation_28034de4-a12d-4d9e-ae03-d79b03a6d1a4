package kl.npki.km.management.check;

import kl.nbase.helper.stopwatch.StopWatch;
import kl.npki.base.core.biz.check.model.*;
import kl.npki.base.core.biz.inspection.InspectionItemTypeEnum;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.km.core.common.constants.CaConstants;
import kl.npki.km.management.constant.KmSelfCheckConstant;
import kl.npki.km.service.repository.entity.CaInfoDO;
import kl.npki.km.service.repository.entity.CaResourceDO;
import kl.npki.km.service.repository.service.ICaInfoService;
import kl.npki.km.service.repository.service.ICaResourceService;
import kl.npki.management.core.biz.check.AbstractSolvableCheckItem;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

import static kl.npki.km.management.constant.KMMgmtI18N.getKmMgmtI18nMessage;

/**
 * CA密钥资源自检
 *
 * <AUTHOR> Guiyu
 * @Date 2024/3/19
 */
public class CaResourceCheckItem extends AbstractSolvableCheckItem {

    @Resource
    private ICaInfoService caInfoService;

    @Resource
    private ICaResourceService caResourceService;

    // region I18N
    /**
     * 密钥资源数量正常
     */
    public static final String CA_RESOURCE_NORMAL_I18N_KEY =  "ca_resource_normal_i18n_key";
    /**
     * 密钥资源数量{0}低于警告值{1}
     */
    public static final String CA_RESOURCE_WARNING_I18N_KEY =  "ca_resource_warning_i18n_key";
    /**
     * 密钥资源已耗尽
     */
    public static final String CA_RESOURCE_EXHAUSTED_I18N_KEY =  "ca_resource_exhausted_i18n_key";
    /**
     * 暂无CA密钥资源记录
     */
    public static final String NO_CA_RESOURCE_RECORD_I18N_KEY =  "no_ca_resource_record_i18n_key";
    /**
     * 所有CA密钥资源数量正常
     */
    public static final String ALL_CA_KEY_RESOURCES_NORMAL_I18N_KEY =  "all_ca_key_resources_normal_i18n_key";
    /**
     * 部分CA的密钥资源数量低于警告值
     */
    public static final String SOME_CA_KEY_RESOURCES_WARNING_I18N_KEY =  "some_ca_key_resources_warning_i18n_key";
    /**
     * 部分CA的密钥资源已耗尽
     */
    public static final String SOME_CA_KEY_RESOURCES_EXHAUSTED_I18N_KEY =  "some_ca_key_resources_exhausted_i18n_key";
    // endregion

    @Override
    public SelfCheckResult check() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        // 设置检查结果通用字段
        SelfCheckResult checkResult = SelfCheckResult.builder()
                .version(ConfigHolder.get().get(SysInfoConfig.class).getEnvironmentSwitchId())
                .code(getFullCode())
                .name(getName())
                .category(getCategory().getName())
                .guidance(KmSelfCheckConstant.CA_RESOURCE_GUIDE_PATH)
                .checkTime(LocalDateTime.now())
                .build();
        Map<String, Object> details = new LinkedHashMap<>();

        // 查询所有CA密钥资源记录
        List<CaResourceDO> caResources = caResourceService.searchAllCaResources();
        // 无ca资源记录
        if (CollectionUtils.isEmpty(caResources)) {
            stopWatch.stop();
            checkResult.setDetails(details);
            checkResult.setStatus(Status.SUCCESS);
            checkResult.setSeverity(Severity.NONE);
            checkResult.setDuration(stopWatch.getTotalTimeMillis());
            checkResult.setMessage(getKmMgmtI18nMessage(NO_CA_RESOURCE_RECORD_I18N_KEY));
            return checkResult;
        }

        // 密钥数量低于警告值的严重性为中级，密钥资源耗尽的严重性为高级
        Severity severity = Severity.NONE;
        // 记录caId对应的caName，减少重复查询
        Map<Long, String> caNameMap = new HashMap<>();
        for (CaResourceDO caResource : caResources) {
            String detailKey = getCaNameByCaId(caNameMap, caResource.getCaId()) + " (" + caResource.getKeyType() + ")";
            // 密钥资源剩余数量
            int surplusNum = caResource.getLimitNum() - caResource.getKeyNum();
            // 不限制数量或剩余数大于警告数的情况均视为正常
            if (CaConstants.UNLIMITED_NUMBER == caResource.getLimitNum() || surplusNum > caResource.getWarningNum()) {
                details.put(detailKey, getKmMgmtI18nMessage(CA_RESOURCE_NORMAL_I18N_KEY));
                continue;
            }

            if (surplusNum > 0) {
                // 密钥资源数量(%s)低于警告值(%s)
                details.put(detailKey, getKmMgmtI18nMessage(CA_RESOURCE_WARNING_I18N_KEY, surplusNum, caResource.getWarningNum()));
                severity = !Severity.HIGH.equals(severity) ? Severity.MEDIUM : severity;
            } else {
                // 密钥资源已耗尽
                details.put(detailKey, getKmMgmtI18nMessage(CA_RESOURCE_EXHAUSTED_I18N_KEY));
                severity = Severity.HIGH;
            }
        }

        stopWatch.stop();
        checkResult.setDetails(details);
        checkResult.setSeverity(severity);
        checkResult.setStatus(Severity.NONE.equals(severity) ? Status.SUCCESS : Status.WARNING);
        checkResult.setMessage(getMessageBySeverity(severity));
        checkResult.setDuration(stopWatch.getTotalTimeMillis());
        return checkResult;
    }

    /**
     * 根据caId获取对应caName
     */
    private String getCaNameByCaId(Map<Long, String> caNameMap, Long caId) {
        String caName = caNameMap.get(caId);
        if (Objects.isNull(caName)) {
            CaInfoDO caInfo = caInfoService.searchCaById(caId);
            caNameMap.put(caInfo.getId(), caInfo.getCaName());
            caName = caInfo.getCaName();
        }
        return caName;
    }

    /**
     * 根据严重性级别获取对应的警告消息
     */
    private String getMessageBySeverity(Severity severity) {
        if (Severity.HIGH.equals(severity)) {
            return getKmMgmtI18nMessage(SOME_CA_KEY_RESOURCES_EXHAUSTED_I18N_KEY);
        }
        if (Severity.MEDIUM.equals(severity)) {
            return getKmMgmtI18nMessage(SOME_CA_KEY_RESOURCES_WARNING_I18N_KEY);
        }
        return getKmMgmtI18nMessage(ALL_CA_KEY_RESOURCES_NORMAL_I18N_KEY);
    }

    @Override
    protected String getGuidePageResourceCode() {
        return KmSelfCheckConstant.CA_RESOURCE_GUIDE_PATH_RESROUCE_CODE;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public String getName() {
        return SelfCheckItemEnum.CA_RESOURCE_CHECK.getName();
    }

    @Override
    public Category getCategory() {
        return Category.CONFIGURATION;
    }

    @Override
    public InspectionItemTypeEnum getInspectionItemType() {
        return InspectionItemTypeEnum.INSPECTION_BUSINESS_DATA;
    }

    @Override
    public String getCode() {
        return Category.CONFIGURATION.getCode() + SelfCheckItemEnum.CA_RESOURCE_CHECK.getCode();
    }

}
