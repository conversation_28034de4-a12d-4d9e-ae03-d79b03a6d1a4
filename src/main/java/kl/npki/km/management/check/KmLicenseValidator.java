package kl.npki.km.management.check;

import kl.npki.base.core.biz.license.service.ILicenseService;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.service.common.license.LicenseValidator;
import kl.npki.km.core.biz.stat.model.KeyCountInfo;
import kl.npki.km.core.exception.KmValidationError;
import kl.npki.km.core.repository.IKeyCurrentRepository;
import kl.security.license.core.LicenseStatus;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * NKM的系统许可校验逻辑实现
 *
 * <AUTHOR>
 * @date 2023/8/24
 */
@Component
public class KmLicenseValidator implements LicenseValidator {

    /**
     * DEMO 环境允许的密钥使用量
     */
    public static final int KEY_USAGE_LIMIT = 200;

    @Resource
    private ILicenseService licenseService;

    @Override
    public void check() {
        if (!SystemUtil.isDeployed()) {
            // 未部署正式环境
            List<KeyCountInfo> currentKeyCounts = RepositoryFactory.get(IKeyCurrentRepository.class).countKeyType();
            boolean isExceeded = currentKeyCounts.stream()
                .mapToInt(KeyCountInfo::getCount)
                .sum() > KEY_USAGE_LIMIT;
            if (isExceeded) {
                throw KmValidationError.DEMO_KEY_USAGE_EXCEEDED.toException();
            }
        } else {
            // 正式环境校验license状态
            LicenseStatus status = licenseService.getLicenseStatus();
            if (!LicenseStatus.FINE.equals(status)) {
                throw KmValidationError.ILLEGAL_LICENSE_STATUS.toException();
            }
        }
    }
}
