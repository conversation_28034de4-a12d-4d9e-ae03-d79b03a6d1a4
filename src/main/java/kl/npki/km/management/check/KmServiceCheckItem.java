package kl.npki.km.management.check;

import kl.nbase.helper.stopwatch.StopWatch;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.biz.check.ISelfCheckItem;
import kl.npki.base.core.biz.check.model.*;
import kl.npki.base.core.biz.inspection.InspectionItemTypeEnum;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.km.core.biz.key.service.SpareKeyPool;
import kl.npki.km.core.configs.KeyLimitConfig;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.service.controller.KmController;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static kl.npki.km.management.constant.KMMgmtI18N.*;

/**
 * KM服务接口自检,对应检测的Controller: {@link KmController}
 *
 * <AUTHOR>
 * @date 2023/10/17
 */
public class KmServiceCheckItem implements ISelfCheckItem {

    @Override
    public SelfCheckResult check() {
        // 记录开始检测的时间
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        try {
            // KM接口暂时无法实现自检，暂通过生成核心生成密钥对逻辑来进行自检
            Set<String> keyTypes = KmConfigWrapper.getKeyConf().getKeyLimits()
                .stream().map(KeyLimitConfig::getKeyType).collect(Collectors.toSet());
            HashMap<String, Object> details = new HashMap<>(keyTypes.size());
            for (String keyType : keyTypes) {
                SpareKeyPool.getInstance().getKeyEntity(new AsymAlgo(keyType));
                details.put(keyType, getKmMgmtI18nMessage(KM_SERVICE_CHECK_SUCCESS_I18N_KEY));
            }
            // 构建CheckResult
            SelfCheckResult checkResult = SelfCheckResult.builder()
                .version(ConfigHolder.get().get(SysInfoConfig.class).getEnvironmentSwitchId())
                .code(getFullCode())
                .name(getName())
                .status(Status.SUCCESS)
                .severity(Severity.NONE)
                .category(getCategory().getName())
                .message(getKmMgmtI18nMessage(KM_SERVICE_NORMAL_I18N_KEY))
                .checkTime(LocalDateTime.now())
                .details(details)
                .build();

            // 计算检测耗时
            stopWatch.stop();
            checkResult.setDuration(stopWatch.getTotalTimeMillis());

            return checkResult;
        } catch (Exception e) {
            stopWatch.stop();
            // 创建一个新的HashMap来存储异常的详细信息
            Map<String, Object> errorDetails = new HashMap<>(2);
            errorDetails.put(getKmMgmtI18nMessage(KM_SERVICE_CHECK_ERROR_MESSAGE_I18N_KEY), e.getMessage());
            errorDetails.put(getKmMgmtI18nMessage(KM_SERVICE_CHECK_STACK_TRACE_I18N_KEY), Arrays.toString(e.getStackTrace()));

            // 若发生异常，构建表示服务不可用的CheckResult
            return SelfCheckResult.builder()
                .version(ConfigHolder.get().get(SysInfoConfig.class).getEnvironmentSwitchId())
                .code(getFullCode())
                .name(SelfCheckItemEnum.KM_SERVICE_AVAILABILITY_CHECK.getName())
                .category(Category.API.getName())
                .status(Status.FAILURE)
                .severity(Severity.HIGH)
                .checkTime(LocalDateTime.now())
                .details(errorDetails)
                .message(getKmMgmtI18nMessage(KM_SERVICE_ABNORMAL_I18N_KEY))
                .duration(stopWatch.getTotalTimeMillis())
                .build();
        }
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public String getName() {
        return SelfCheckItemEnum.KM_SERVICE_AVAILABILITY_CHECK.getName();
    }

    @Override
    public Category getCategory() {
        return Category.API;
    }

    @Override
    public InspectionItemTypeEnum getInspectionItemType() {
        return InspectionItemTypeEnum.INSPECTION_INTERNAL_SERVER_STATUS;
    }

    @Override
    public String getCode() {
        return Category.API.getCode() + SelfCheckItemEnum.KM_SERVICE_AVAILABILITY_CHECK.getCode();
    }
}
