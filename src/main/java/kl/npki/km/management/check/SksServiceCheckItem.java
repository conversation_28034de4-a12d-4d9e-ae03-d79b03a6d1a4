package kl.npki.km.management.check;

import kl.nbase.helper.stopwatch.StopWatch;
import kl.npki.base.core.biz.check.ISelfCheckItem;
import kl.npki.base.core.biz.check.model.*;
import kl.npki.base.core.biz.inspection.InspectionItemTypeEnum;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.km.core.biz.sks.model.SksKeyBatchSearchInfo;
import kl.npki.km.core.biz.sks.model.SksKeyEntity;
import kl.npki.km.core.biz.sks.model.SksUpdateInfo;
import kl.npki.km.core.common.constants.KmConstants;
import kl.npki.km.core.service.sks.ISksService;
import kl.npki.km.service.model.sks.response.SksResponse;

import java.security.SecureRandom;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.UUID;

import static kl.npki.km.management.constant.KMMgmtI18N.*;

/**
 * SKS服务接口自检
 *
 * <AUTHOR>
 * @date 2023/10/17
 */
public class SksServiceCheckItem implements ISelfCheckItem {

    private final ISksService sksService;

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    public SksServiceCheckItem(ISksService sksService) {
        this.sksService = sksService;
    }

    /**
     * 生成测试用的客户端密钥哈希
     *
     * @return 随机生成的客户端密钥哈希
     */
    private String generateRandomKeyHash() {
        byte[] bytes = new byte[32];
        SECURE_RANDOM.nextBytes(bytes);
        return Base64Util.base64Encode(bytes);
    }

    @Override
    public SelfCheckResult check() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        // 每次自检都生成新的随机clientKeyHash
        String clientKeyHash = generateRandomKeyHash();

        Map<String, Object> details = new LinkedHashMap<>(6);
        boolean allServicesUp = true;

        // 测试获取随机数
        SksResponse<?> randomResult = checkGetRandomNumber();
        details.put(getKmMgmtI18nMessage(SKS_GET_RANDOM_NUMBER_API_I18N_KEY), formatCheckResult(randomResult));
        allServicesUp &= isSuccess(randomResult);

        // 测试创建密钥
        SksResponse<?> createResult = checkCreatePrivateKey(clientKeyHash);
        details.put(getKmMgmtI18nMessage(SKS_CREATE_PRIVATE_KEY_API_I18N_KEY), formatCheckResult(createResult));
        allServicesUp &= isSuccess(createResult);

        // 测试获取密钥
        SksResponse<?> getResult = checkGetPrivateKey(clientKeyHash);
        details.put(getKmMgmtI18nMessage(SKS_GET_PRIVATE_KEY_API_I18N_KEY), formatCheckResult(getResult));
        allServicesUp &= isSuccess(getResult);

        // 测试更新密钥状态
        SksResponse<?> updateResult = checkUpdatePrivateKey(clientKeyHash);
        details.put(getKmMgmtI18nMessage(SKS_UPDATE_PRIVATE_KEY_API_I18N_KEY), formatCheckResult(updateResult));
        allServicesUp &= isSuccess(updateResult);

        // 测试批量获取密钥
        SksResponse<?> batchResult = checkBatchGetPrivateKey();
        details.put(getKmMgmtI18nMessage(SKS_BATCH_GET_PRIVATE_KEY_API_I18N_KEY), formatCheckResult(batchResult));
        allServicesUp &= isSuccess(batchResult);

        // 测试删除密钥
        SksResponse<?> removeResult = checkRemovePrivateKey(clientKeyHash);
        details.put(getKmMgmtI18nMessage(SKS_REMOVE_PRIVATE_KEY_API_I18N_KEY), formatCheckResult(removeResult));
        allServicesUp &= isSuccess(removeResult);

        // 构建总体的 CheckResult
        SelfCheckResult checkResult = SelfCheckResult.builder()
            .version(ConfigHolder.get().get(SysInfoConfig.class).getEnvironmentSwitchId())
            .code(getFullCode())
            .name(getName())
            .category(getCategory().getName())
            .checkTime(LocalDateTime.now())
            .details(details)
            .build();

        if (allServicesUp) {
            checkResult.setStatus(Status.SUCCESS);
            checkResult.setSeverity(Severity.NONE);
            checkResult.setMessage(getKmMgmtI18nMessage(ALL_SKS_API_NORMAL_I18N_KEY));
        } else {
            checkResult.setStatus(Status.FAILURE);
            checkResult.setSeverity(Severity.HIGH);
            checkResult.setMessage(getKmMgmtI18nMessage(SOME_SKS_API_ABNORMAL_I18N_KEY));
        }

        stopWatch.stop();
        checkResult.setDuration(stopWatch.getTotalTimeMillis());
        return checkResult;
    }

    /**
     * 格式化检查结果，使其更加友好
     */
    private String formatCheckResult(SksResponse<?> response) {
        if (isSuccess(response)) {
            return getKmMgmtI18nMessage(SKS_API_CHECK_SUCCESS_I18N_KEY);
        } else {
            String errorMsg = response != null ? response.getMsg() : "Unknown error";
            return MessageFormat.format(getKmMgmtI18nMessage(SKS_API_CHECK_FAILURE_I18N_KEY), errorMsg);
        }
    }

    /**
     * 检查获取随机数功能
     */
    private SksResponse<?> checkGetRandomNumber() {
        try {
            String randomNumber = sksService.getRandomNumber(32);
            return SksResponse.success(randomNumber);
        } catch (Exception e) {
            return SksResponse.fail(Status.FAILURE.tr(), e.getMessage());
        }
    }

    /**
     * 检查创建密钥功能
     */
    private SksResponse<?> checkCreatePrivateKey(String clientKeyHash) {
        try {
            SksKeyEntity keyEntity = new SksKeyEntity();
            keyEntity.setUserId(UUID.randomUUID().toString());
            keyEntity.setKeySource("0");
            keyEntity.setClientKeyHash(clientKeyHash);
            keyEntity.setServerKeySection(generateRandomKeyHash());
            keyEntity.setStatus(EntityStatus.NORMAL.getId());
            keyEntity.setMediaId(KmConstants.SELF_CHECKING_MEDIUM_ID);

            sksService.createPrivateKey(keyEntity);
            return SksResponse.success();
        } catch (Exception e) {
            return SksResponse.fail(Status.FAILURE.tr(), e.getMessage());
        }
    }

    /**
     * 检查获取密钥功能
     */
    private SksResponse<?> checkGetPrivateKey(String clientKeyHash) {
        try {
            SksKeyEntity keyEntity = sksService.getPrivateKey(clientKeyHash);
            return SksResponse.success(keyEntity);
        } catch (Exception e) {
            return SksResponse.fail(Status.FAILURE.tr(), e.getMessage());
        }
    }

    /**
     * 检查更新密钥状态功能
     */
    private SksResponse<?> checkUpdatePrivateKey(String clientKeyHash) {
        try {
            SksUpdateInfo sksUpdateInfo = new SksUpdateInfo();
            sksUpdateInfo.setClientKeyHash(clientKeyHash);
            sksUpdateInfo.setStatus(EntityStatus.REVOKED.getId());

            boolean result = sksService.updatePrivateKey(sksUpdateInfo);
            return SksResponse.success(result);
        } catch (Exception e) {
            return SksResponse.fail(Status.FAILURE.tr(), e.getMessage());
        }
    }

    /**
     * 检查批量获取密钥功能
     */
    private SksResponse<?> checkBatchGetPrivateKey() {
        try {
            SksKeyBatchSearchInfo searchInfo = new SksKeyBatchSearchInfo(
                System.currentTimeMillis() - 3600000, // 一小时前
                System.currentTimeMillis()
            );

            return SksResponse.success(sksService.batchGetPrivateKey(searchInfo));
        } catch (Exception e) {
            return SksResponse.fail(Status.FAILURE.tr(), e.getMessage());
        }
    }

    /**
     * 检查删除密钥功能
     */
    private SksResponse<?> checkRemovePrivateKey(String clientKeyHash) {
        try {
            boolean result = sksService.removeSelfCheckPrivateKey(clientKeyHash);
            return SksResponse.success(result);
        } catch (Exception e) {
            return SksResponse.fail(Status.FAILURE.tr(), e.getMessage());
        }
    }

    /**
     * 检查响应是否成功
     */
    private boolean isSuccess(SksResponse<?> response) {
        return response != null &&
            SksResponse.SUCCESS_CODE.equals(response.getCode()) &&
            SksResponse.SUCCESS_MSG.equals(response.getMsg());
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public String getName() {
        return SelfCheckItemEnum.SKS_SERVICE_AVAILABILITY_CHECK.getName();
    }

    @Override
    public Category getCategory() {
        return Category.API;
    }

    @Override
    public InspectionItemTypeEnum getInspectionItemType() {
        return InspectionItemTypeEnum.INSPECTION_INTERNAL_SERVER_STATUS;
    }

    @Override
    public String getCode() {
        return Category.API.getCode() + SelfCheckItemEnum.SKS_SERVICE_AVAILABILITY_CHECK.getCode();
    }
}
