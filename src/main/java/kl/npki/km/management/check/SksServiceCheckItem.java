package kl.npki.km.management.check;

import kl.nbase.helper.stopwatch.StopWatch;
import kl.npki.base.core.biz.check.ISelfCheckItem;
import kl.npki.base.core.biz.check.model.*;
import kl.npki.base.core.biz.inspection.InspectionItemTypeEnum;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.km.core.biz.sks.model.SksKeyBatchSearchInfo;
import kl.npki.km.core.biz.sks.model.SksKeyEntity;
import kl.npki.km.core.common.constants.KmConstants;
import kl.npki.km.core.service.sks.ISksService;
import kl.npki.km.service.model.sks.response.SksResponse;
import org.apache.commons.lang3.RandomUtils;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.UUID;

import static kl.npki.km.management.constant.KMMgmtI18N.*;

/**
 * SKS服务接口自检
 *
 * <AUTHOR>
 * @date 2023/10/17
 */
public class SksServiceCheckItem implements ISelfCheckItem {

    private final ISksService sksService;

    public SksServiceCheckItem(ISksService sksService) {
        this.sksService = sksService;
    }

    /**
     * 测试用的客户端密钥哈希，自检测试用例共享
     */
    private final String clientKeyHash = Base64Util.base64Encode(RandomUtils.nextBytes(32));

    @Override
    public SelfCheckResult check() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        Map<String, Object> details = new LinkedHashMap<>(6);
        boolean allServicesUp = true;

        // 测试获取随机数
        SksResponse<?> randomResult = checkGetRandomNumber();
        details.put(getKmMgmtI18nMessage(SKS_GET_RANDOM_NUMBER_API_I18N_KEY), formatCheckResult(randomResult));
        allServicesUp &= isSuccess(randomResult);

        // 测试创建密钥
        SksResponse<?> createResult = checkCreatePrivateKey();
        details.put(getKmMgmtI18nMessage(SKS_CREATE_PRIVATE_KEY_API_I18N_KEY), formatCheckResult(createResult));
        allServicesUp &= isSuccess(createResult);

        // 测试获取密钥
        SksResponse<?> getResult = checkGetPrivateKey();
        details.put(getKmMgmtI18nMessage(SKS_GET_PRIVATE_KEY_API_I18N_KEY), formatCheckResult(getResult));
        allServicesUp &= isSuccess(getResult);

        // 测试更新密钥状态
        SksResponse<?> updateResult = checkUpdatePrivateKey();
        details.put(getKmMgmtI18nMessage(SKS_UPDATE_PRIVATE_KEY_API_I18N_KEY), formatCheckResult(updateResult));
        allServicesUp &= isSuccess(updateResult);

        // 测试批量获取密钥
        SksResponse<?> batchResult = checkBatchGetPrivateKey();
        details.put(getKmMgmtI18nMessage(SKS_BATCH_GET_PRIVATE_KEY_API_I18N_KEY), formatCheckResult(batchResult));
        allServicesUp &= isSuccess(batchResult);

        // 测试删除密钥
        SksResponse<?> removeResult = checkRemovePrivateKey();
        details.put(getKmMgmtI18nMessage(SKS_REMOVE_PRIVATE_KEY_API_I18N_KEY), formatCheckResult(removeResult));
        allServicesUp &= isSuccess(removeResult);

        // 构建总体的 CheckResult
        SelfCheckResult checkResult = SelfCheckResult.builder()
            .version(ConfigHolder.get().get(SysInfoConfig.class).getEnvironmentSwitchId())
            .code(getFullCode())
            .name(getName())
            .category(getCategory().getName())
            .checkTime(LocalDateTime.now())
            .details(details)
            .build();

        if (allServicesUp) {
            checkResult.setStatus(Status.SUCCESS);
            checkResult.setSeverity(Severity.NONE);
            checkResult.setMessage(getKmMgmtI18nMessage(ALL_SKS_API_NORMAL_I18N_KEY));
        } else {
            checkResult.setStatus(Status.FAILURE);
            checkResult.setSeverity(Severity.HIGH);
            checkResult.setMessage(getKmMgmtI18nMessage(SOME_SKS_API_ABNORMAL_I18N_KEY));
        }

        stopWatch.stop();
        checkResult.setDuration(stopWatch.getTotalTimeMillis());
        return checkResult;
    }

    /**
     * 格式化检查结果，使其更加友好
     */
    private String formatCheckResult(SksResponse<?> response) {
        if (isSuccess(response)) {
            return getKmMgmtI18nMessage(SKS_API_CHECK_SUCCESS_I18N_KEY);
        } else {
            String errorMsg = response != null ? response.getMsg() : "Unknown error";
            return MessageFormat.format(getKmMgmtI18nMessage(SKS_API_CHECK_FAILURE_I18N_KEY), errorMsg);
        }
    }

    /**
     * 检查获取随机数功能
     */
    private SksResponse<?> checkGetRandomNumber() {
        try {
            String randomNumber = sksService.getRandomNumber(32);
            return SksResponse.success(randomNumber);
        } catch (Exception e) {
            return SksResponse.fail(Status.FAILURE.tr(), e.getMessage());
        }
    }

    /**
     * 检查创建密钥功能
     */
    private SksResponse<?> checkCreatePrivateKey() {
        try {
            SksKeyEntity keyEntity = new SksKeyEntity();
            keyEntity.setUserId(UUID.randomUUID().toString());
            keyEntity.setKeySource("0");
            keyEntity.setClientKeyHash(clientKeyHash);
            keyEntity.setServerKeySection(Base64Util.base64Encode(RandomUtils.nextBytes(32)));
            keyEntity.setStatus(EntityStatus.NORMAL.getId());
            keyEntity.setMediaId(KmConstants.SELF_CHECKING_MEDIUM_ID);

            sksService.createPrivateKey(keyEntity);
            return SksResponse.success();
        } catch (Exception e) {
            return SksResponse.fail(Status.FAILURE.tr(), e.getMessage());
        }
    }

    /**
     * 检查获取密钥功能
     */
    private SksResponse<?> checkGetPrivateKey() {
        try {
            SksKeyEntity keyEntity = sksService.getPrivateKey(clientKeyHash);
            return SksResponse.success(keyEntity);
        } catch (Exception e) {
            return SksResponse.fail(Status.FAILURE.tr(), e.getMessage());
        }
    }

    /**
     * 检查更新密钥状态功能
     */
    private SksResponse<?> checkUpdatePrivateKey() {
        try {
            SksKeyEntity keyEntity = new SksKeyEntity(clientKeyHash);
            keyEntity.setStatus(EntityStatus.REVOKED.getId());

            boolean result = sksService.updatePrivateKey(keyEntity);
            return SksResponse.success(result);
        } catch (Exception e) {
            return SksResponse.fail(Status.FAILURE.tr(), e.getMessage());
        }
    }

    /**
     * 检查批量获取密钥功能
     */
    private SksResponse<?> checkBatchGetPrivateKey() {
        try {
            SksKeyBatchSearchInfo searchInfo = new SksKeyBatchSearchInfo(
                System.currentTimeMillis() - 3600000, // 一小时前
                System.currentTimeMillis()
            );

            return SksResponse.success(sksService.batchGetPrivateKey(searchInfo));
        } catch (Exception e) {
            return SksResponse.fail(Status.FAILURE.tr(), e.getMessage());
        }
    }

    /**
     * 检查删除密钥功能
     */
    private SksResponse<?> checkRemovePrivateKey() {
        try {
            boolean result = sksService.removeSelfCheckPrivateKey(clientKeyHash);
            return SksResponse.success(result);
        } catch (Exception e) {
            return SksResponse.fail(Status.FAILURE.tr(), e.getMessage());
        }
    }

    /**
     * 检查响应是否成功
     */
    private boolean isSuccess(SksResponse<?> response) {
        return response != null &&
            SksResponse.SUCCESS_CODE.equals(response.getCode()) &&
            SksResponse.SUCCESS_MSG.equals(response.getMsg());
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public String getName() {
        return SelfCheckItemEnum.SKS_SERVICE_AVAILABILITY_CHECK.getName();
    }

    @Override
    public Category getCategory() {
        return Category.API;
    }

    @Override
    public InspectionItemTypeEnum getInspectionItemType() {
        return InspectionItemTypeEnum.INSPECTION_INTERNAL_SERVER_STATUS;
    }

    @Override
    public String getCode() {
        return Category.API.getCode() + SelfCheckItemEnum.SKS_SERVICE_AVAILABILITY_CHECK.getCode();
    }
}
