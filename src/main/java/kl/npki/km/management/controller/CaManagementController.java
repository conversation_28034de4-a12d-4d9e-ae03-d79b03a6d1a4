package kl.npki.km.management.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.bean.validate.ValidateService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.constant.CaKeyResponseStandardVersionEnum;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.km.core.biz.ca.model.AddResourceInfo;
import kl.npki.km.core.biz.ca.model.CaEntity;
import kl.npki.km.core.biz.ca.model.CaUpdateInfo;
import kl.npki.km.core.common.constants.CaVersionTypeEnum;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.core.exception.KmValidationError;
import kl.npki.km.core.mapper.ConvertServiceHelper;
import kl.npki.km.core.service.ca.ICaService;
import kl.npki.km.management.model.ca.*;
import kl.npki.km.management.repository.service.ICaManagementService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

import static kl.npki.km.management.constant.KMMgmtI18N.*;

/**
 * ca管理
 *
 * <AUTHOR>
 * @date 2022/10/10 11:21
 */
@RestController
@RequestMapping("/ca")
@Tag(name = "CA管理")
@LogCollector(resolver = OperationLogResolver.class)
public class CaManagementController implements BaseController {
    @Resource
    private ICaService caService;

    @Resource
    private ICaManagementService caManagementService;

    @Resource
    private ValidateService validateService;

    @Resource
    private ConvertService convertService;

    /**
     * 查询CA信息列表
     *
     * @param pageInfo      分页信息
     * @param caListRequest 高级查询封装
     * @return
     */
    @GetMapping("/list")
    @Operation(description = CA_INFO_LIST_QUERY, summary = "查询CA信息列表")
    public RestResponse<IPage<CaListResponse>> queryCaPageList(PageInfo pageInfo, CaListRequest caListRequest) {
        IPage<CaListResponse> keyList = caManagementService.queryCaPageList(pageInfo, caListRequest);
        return RestResponse.success(keyList);
    }

    @GetMapping("/all")
    @Operation(description = QUERY_ALL_CAS, summary = "查询所有CA")
    public RestResponse<List<CaListResponse>> queryAllCa() {
        return RestResponse.success(caManagementService.queryAllCa());
    }

    /**
     * 查询CA信息
     *
     * @param caId
     * @return
     */
    @GetMapping("/detail/{caId}")
    @Operation(description = QUERY_CA_INFO_DETAILS, summary = "查询CA信息详情")
    public RestResponse<CaDetailResponse> getCaDetail(@PathVariable(value = "caId") Long caId) {
        CaDetailResponse caDetailResponse = caManagementService.getCaDetail(caId);
        return RestResponse.success(caDetailResponse);
    }

    /**
     * ca接入
     *
     * @param caAccessRequest ca信息
     * @return
     */
    @PostMapping("/access")
    @Operation(description = CA_ACCESS_SERVICE, summary = "CA接入")
    public RestResponse<Long> caAccess(@RequestBody CaAccessRequest caAccessRequest) {
        validateService.validate(caAccessRequest);
        CaEntity caEntity = convertService.convert(caAccessRequest, CaEntity.class);
        return RestResponse.success(caService.caAccess(caEntity));
    }

    /**
     * 增加各类型密钥数量
     *
     * @param caId         ca编号信息
     * @param resourceList 增加密钥数量请求信息
     * @return
     */
    @PostMapping("/addKeyNum/{caId}")
    @Operation(description = CA_RESOURCE_ADDITION, summary = "CA资源添加")
    public RestResponse<Boolean> addKeyNum(@PathVariable(value = "caId") @NotNull Long caId,
                                           @RequestBody List<AddResourceRequest> resourceList) {
        List<String> asymAlgoList = KmConfigWrapper.getAsymmetricAndPostQuantumKeyTypesWithoutSM9List();
        for (AddResourceRequest resourceRequest : resourceList) {
            validateService.validate(resourceRequest);
            // 判断密钥类型合法性
            if (!asymAlgoList.contains(resourceRequest.getKeyType())) {
                throw KmValidationError.KEY_TYPE_NONSUPPORT_ERROR.toException(resourceRequest.getKeyType());
            }
        }
        List<AddResourceInfo> addResourceInfos = convertService.convert(resourceList, AddResourceInfo.class);
        return RestResponse.success(caService.addKeyNum(caId, addResourceInfos));
    }

    /**
     * 更新CA
     *
     * @param caId            ca编号信息
     * @param caUpdateRequest ca更新信息
     * @return
     */
    @PutMapping(value = "/update/{caId}")
    @Operation(description = CA_UPDATE, summary = "CA更新")
    public RestResponse<Boolean> caUpdate(@PathVariable(value = "caId") @NotNull Long caId,
                                          @RequestBody CaUpdateRequest caUpdateRequest) {
        validateService.validate(caUpdateRequest);
        CaUpdateInfo caUpdateInfo = ConvertServiceHelper.getConvertService().convert(caUpdateRequest,
            CaUpdateInfo.class);
        return RestResponse.success(caService.caUpdate(caId, caUpdateInfo));
    }

    /**
     * 冻结CA
     *
     * @return
     */
    @PutMapping("/freeze/{caId}")
    @Operation(description = CA_FREEZE, summary = "CA冻结")
    public RestResponse<Boolean> caFreeze(@PathVariable(value = "caId") @NotNull Long caId) {
        return RestResponse.success(caService.caFreeze(caId));
    }

    /**
     * 解冻CA
     *
     * @return
     */
    @PutMapping("/unfreeze/{caId}")
    @Operation(description = CA_UNFREEZE, summary = "CA解冻")
    public RestResponse<Boolean> caUnfreeze(@PathVariable(value = "caId") @NotNull Long caId) {
        return RestResponse.success(caService.caUnfreeze(caId));
    }

    /**
     * 废除CA
     *
     * @param caId ca编号信息
     * @return
     */
    @PutMapping("/revoke/{caId}")
    @Operation(description = CA_REVOKE, summary = "CA废除")
    public RestResponse<Boolean> caRevoke(@PathVariable(value = "caId") @NotNull Long caId) {
        return RestResponse.success(caService.caRevoke(caId));
    }

    /**
     * 恢复废除CA
     *
     * @param caId ca编号信息
     * @return
     */
    @PutMapping("/recovery/{caId}")
    @Operation(description = CA_RECOVERY, summary = "CA恢复")
    public RestResponse<Boolean> caRecovery(@PathVariable(value = "caId") @NotNull Long caId) {
        return RestResponse.success(caService.caRecovery(caId));
    }

    /**
     * 获取所有CA版本信息
     */
    @GetMapping("/version/list")
    @Operation(description = CA_VERSION_LIST, summary = "获取所有CA版本信息")
    public RestResponse<Map<String, Object>> getAllCaVersion() {
        return RestResponse.success(CaVersionTypeEnum.getAllCAVersion());
    }

    /**
     * 获取所有CA密钥服务标准规范列表
     */
    @GetMapping("/standard-version/list")
    @Operation(description = CA_KEY_RESPONSE_STANDARD_VERSION_LIST, summary = "获取所有CA密钥服务标准规范版本信息")
    public RestResponse<Map<Integer, Object>> getAllCaKeyResponseStandardVersion() {
        return RestResponse.success(CaKeyResponseStandardVersionEnum.valuesMap());
    }
}
