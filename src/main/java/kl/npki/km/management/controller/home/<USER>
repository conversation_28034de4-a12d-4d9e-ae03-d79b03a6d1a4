package kl.npki.km.management.controller.home;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.km.core.biz.home.model.CaKeyStatisticInfo;
import kl.npki.km.core.biz.home.model.KeyStashDistributionInfo;
import kl.npki.km.core.biz.home.model.KeyStatusStatisticInfo;
import kl.npki.km.core.biz.stat.model.CaBizStatisticInfo;
import kl.npki.km.core.common.FixedSizeCircularQueue;
import kl.npki.km.core.service.home.CaKeyStatisticService;
import kl.npki.km.core.service.home.KeyDistributionStatisticService;
import kl.npki.km.core.service.home.KeyStatusStatisticService;
import kl.npki.km.core.service.statistic.ICaBizStatisticService;
import kl.npki.km.management.controller.BaseController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.LinkedList;

import static kl.npki.km.management.constant.KMMgmtI18N.*;

/**
 * 首页数据展示
 * <AUTHOR>
 */
@RestController
@RequestMapping("/home")
@Tag(name = "首页数据展示")
@LogCollector(resolver = OperationLogResolver.class)
public class HomePageController implements BaseController {

    @Resource
    private KeyStatusStatisticService keyStatusStatisticService;

    @Resource
    private KeyDistributionStatisticService keyDistributionStatisticService;

    @Resource
    private CaKeyStatisticService caKeyStatisticService;

    @Resource
    private ICaBizStatisticService caBizStatisticService;


    @GetMapping("/keyStatus")
    @Operation(description = KEY_STATUS_DATA_STATS, summary = "密钥状态数据统计")
    public RestResponse<KeyStatusStatisticInfo> showKeyStatus() {
        KeyStatusStatisticInfo keyStatusStatisticInfo = keyStatusStatisticService.getKeyStatusStatisticInfo();
        return RestResponse.success(keyStatusStatisticInfo);
    }

    @GetMapping("/keyStash")
    @Operation(description = KEY_VAULT_DATA_STATS, summary = "密钥库数据统计")
    public RestResponse<KeyStashDistributionInfo> showKeyStash() {
        KeyStashDistributionInfo keyStashDistributionInfo = keyDistributionStatisticService.getKeyStashDistributionInfo();
        return RestResponse.success(keyStashDistributionInfo);
    }

    @GetMapping("/caKeyData/{caName}")
    @Operation(description = KEY_USAGE_DATA_STATS, summary = "CA密钥使用数据统计")
    public RestResponse<CaKeyStatisticInfo> showCaKeyData(@PathVariable("caName") String caName) {
        CaKeyStatisticInfo caKeyStatisticInfo = caKeyStatisticService.getCaKeyStatisticInfo(caName);
        return RestResponse.success(caKeyStatisticInfo);
    }

    @GetMapping("/caBizStatistic/{caName}")
    @Operation(description = CA_KEY_SERVICE_INVOCATION_STATS, summary = "CA密钥服务调用情况")
    public RestResponse<LinkedList<CaBizStatisticInfo>> showCaBizStatistic(@PathVariable("caName") String caName) {
        FixedSizeCircularQueue<CaBizStatisticInfo> homePageData = caBizStatisticService.getHomePageData(caName);
        return RestResponse.success(homePageData.getQueue());
    }

}
