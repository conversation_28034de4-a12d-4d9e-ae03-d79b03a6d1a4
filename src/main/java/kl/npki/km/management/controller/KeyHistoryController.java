package kl.npki.km.management.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.helper.utils.CheckUtils;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.km.management.exception.KmManagementValidationError;
import kl.npki.km.management.model.key.KeyHistoryDetailResponse;
import kl.npki.km.management.model.key.KeyHistoryListRequest;
import kl.npki.km.management.model.key.KeyHistoryListResponse;
import kl.npki.km.management.repository.service.IKeyManagementService;
import kl.npki.km.service.repository.entity.KeyHistoryDO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static kl.npki.km.management.constant.KMMgmtI18N.HIS_ADMIN_LIST;
import static kl.npki.km.management.constant.KMMgmtI18N.HIS_QUERY_CURRENT_KEY_DETAIL;

/**
 * @Author: guoq
 * @Date: 2022/10/9
 * @description: 历史库管理
 * @apiDefine FailResponse
 * @apiErrorExample RestResponse (fail):
 * {
 * "code": "30000510B000", // 错误码
 * "message": "远端服务错误", // 错误描述
 * "traceId": "a101a65e-e19a-4bb7-ace1-79ac61f3ed35", // 链路id
 * "timestamp": 1661321323225 // 服务器的时间戳
 * }
 * @apiDefine SuccessResponse
 * @apiSuccessExample RestResponse (success):
 * {
 * "code": "0", // 正常响应code=0
 * "traceId": "85bdb539-02e3-41ae-9d95-82eadf3a820f", // 链路id
 * "timestamp": 1661310184772, // 服务器的时间戳
 * "data": "success" // 具体对象
 * }
 * @apiDefine FailResponse
 * @apiErrorExample RestResponse (fail):
 * {
 * "code": "30000510B000", // 错误码
 * "message": "远端服务错误", // 错误描述
 * "traceId": "a101a65e-e19a-4bb7-ace1-79ac61f3ed35", // 链路id
 * "timestamp": 1661321323225 // 服务器的时间戳
 * }
 * @apiDefine SuccessResponse
 * @apiSuccessExample RestResponse (success):
 * {
 * "code": "0", // 正常响应code=0
 * "traceId": "85bdb539-02e3-41ae-9d95-82eadf3a820f", // 链路id
 * "timestamp": 1661310184772, // 服务器的时间戳
 * "data": "success" // 具体对象
 * }
 */
/**
 * @apiDefine FailResponse
 * @apiErrorExample RestResponse (fail):
 *    {
 *     "code": "30000510B000", // 错误码
 *     "message": "远端服务错误", // 错误描述
 *     "traceId": "a101a65e-e19a-4bb7-ace1-79ac61f3ed35", // 链路id
 *     "timestamp": 1661321323225 // 服务器的时间戳
 *    }
 */

/**
 * @apiDefine SuccessResponse
 * @apiSuccessExample RestResponse (success):
 *    {
 *     "code": "0", // 正常响应code=0
 *     "traceId": "85bdb539-02e3-41ae-9d95-82eadf3a820f", // 链路id
 *     "timestamp": 1661310184772, // 服务器的时间戳
 *     "data": "success" // 具体对象
 *    }
 *
 */

/**
 * @apiDefine page
 * @apiParam {Number} currentPage 当前页
 * @apiParam {Number} pageSize 页大小
 */
@RestController
@RequestMapping("keyHistory")
@Tag(name = "密钥库管理")
@LogCollector(resolver = OperationLogResolver.class)
public class KeyHistoryController implements BaseController {

    @Resource
    private IKeyManagementService keyManagementService;

    /**
     * 查询密钥历史库()
     *
     * @api {GET} /keyHistory/list 查询密钥在用库
     * @apiDescription 查询密钥在用库列表
     * @apiPermission 业务操作员
     * @apiGroup keyCurrent
     * @apiName adminList
     * @apiUse page
     * @apiParam {String} [encCertSn] 加密证书序列号
     * @apiParam {String} [subject] 主题名称
     * @apiParam {Number} [keyStatus] 密钥状态,0: 正常, 1：废除, 2：冻结, 3：过期, 4：未知
     * @apiParam {Number} [keyType] 密钥类型
     * @apiParam {Date} [startValidStart] 起始生效时间
     * @apiParam {Date} [endValidStart] 结束生效时间
     * @apiParamExample {json} Request-Example:
     * {
     * "page":{
     * "currentPage": 1,
     * "pageSize": 10
     * },
     * "keyCurrentListRequest":{
     * "encCertSn": "3246673412373732",
     * "subject": "CN=小明,O=sh,C=CN",
     * "keyStatus": 0,
     * "keyType": "SM2",
     * "leValidStart": "2022-10-09 10:00:50",
     * "geValidStart": "2022-10-10 10:00:50",
     * }
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @GetMapping("/list")
    @Operation(description = HIS_ADMIN_LIST, summary = "查询历史密钥库")
    public RestResponse<IPage<KeyHistoryListResponse>> adminList(
        PageInfo pageInfo,
        KeyHistoryListRequest keyHistoryListRequest,
        Boolean isLawRecovery) {

        IPage<KeyHistoryDO> page = pageInfo.toPage();
        IPage<KeyHistoryListResponse> pageList = keyManagementService.queryHistoryPageList(page,
            keyHistoryListRequest, isLawRecovery);
        return RestResponse.success(pageList);
    }

    @GetMapping("/detail/{keyId}")
    @Operation(description = HIS_QUERY_CURRENT_KEY_DETAIL, summary = "获取历史密钥详情")
    public RestResponse<KeyHistoryDetailResponse> queryCurrentKeyDetail(
        @PathVariable(value = "keyId") Long keyId) {
        KeyHistoryDetailResponse detailResponse = keyManagementService.queryHistoryDetail(keyId);
        CheckUtils.notNull(detailResponse, KmManagementValidationError.CURRENT_KEY_NOT_FOUND_ERROR.toException());
        return RestResponse.success(detailResponse);
    }
}
