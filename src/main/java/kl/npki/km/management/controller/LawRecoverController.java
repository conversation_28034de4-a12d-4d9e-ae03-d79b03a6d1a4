package kl.npki.km.management.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.bean.validate.ValidateService;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.constant.CertStatus;
import kl.npki.base.core.constant.UserStatus;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.km.core.biz.law.model.LawRecoverInfo;
import kl.npki.km.core.biz.law.model.LawRecoverResult;
import kl.npki.km.core.biz.law.service.LawRecoverService;
import kl.npki.km.management.model.law.LawRecoverRequest;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import kl.npki.management.core.constants.RoleCodeEnum;
import kl.npki.management.core.repository.IAdminCertRepository;
import kl.npki.management.core.repository.IAdminInfoRepository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static kl.npki.km.management.constant.KMMgmtI18N.LAW_RECOVER;

/**
 * @Author: guoq
 * @Date: 2022/10/11
 * @description: 司法恢复
 */
@RestController
@RequestMapping("law")
@Tag(name = "司法恢复")
@LogCollector(resolver = OperationLogResolver.class)
public class LawRecoverController implements BaseController {

    @Resource
    private LawRecoverService lawRecoverService;

    @Resource
    private ConvertService convertService;

    @Resource
    private ValidateService validateService;

    @Resource
    private IAdminInfoRepository adminInfoRepository;

    @Resource
    private IAdminCertRepository adminCertRepository;

    @PostMapping("/recover")
    @Operation(description = LAW_RECOVER, summary = "司法恢复")
    public RestResponse<LawRecoverResult> lawRecover(@RequestBody LawRecoverRequest lawRecoverRequest) {

        validateService.validate(lawRecoverRequest);
        LawRecoverInfo lawRecoverInfo = convertService.convert(lawRecoverRequest, LawRecoverInfo.class);
        Long lawId = lawRecoverInfo.getLawId();
        // 根据 id 查询管理员实体
        AdminEntity adminEntity = adminInfoRepository.getById(lawId);
        // 检查管理员状态和类型
        adminEntity.checkStatus(UserStatus.NORMAL);
        adminEntity.checkRole(RoleCodeEnum.LAW_ADMIN);
        // 检查司法管理员证书有效性
        AdminCertEntity adminCertEntity = adminCertRepository.getByAdminInfoId(adminEntity.getId());
        adminCertEntity.checkStatus(CertStatus.ISSUED);
        lawRecoverInfo.setLawSignCert(adminCertEntity.getSignCertValue());
        return RestResponse.success(lawRecoverService.doLawRecover(lawRecoverInfo));
    }

}
