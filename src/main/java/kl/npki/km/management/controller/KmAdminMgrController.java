package kl.npki.km.management.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.common.manager.MgrHolder;
import kl.npki.base.core.common.org.OrgCache;
import kl.npki.base.core.constant.CertStatus;
import kl.npki.base.core.constant.UserStatus;
import kl.npki.base.management.model.admin.request.AdminInfoListRequest;
import kl.npki.base.management.model.admin.request.ExtendAdminCertRequest;
import kl.npki.base.management.model.admin.request.IssueAdminCertForDeployRequest;
import kl.npki.base.management.model.admin.request.IssueAdminCertRequest;
import kl.npki.base.management.model.admin.response.AdminCertIssueResponse;
import kl.npki.base.management.model.admin.response.AdminInfoListResponse;
import kl.npki.base.management.utils.ValidateHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.common.log.resolver.SecurityOperationLogResolver;
import kl.npki.management.core.biz.admin.model.dto.AdminInfoListDTO;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import kl.npki.management.core.biz.admin.model.info.AdminCertIssueInfo;
import kl.npki.management.core.biz.admin.model.info.AdminCertIssueParamInfo;
import kl.npki.management.core.biz.admin.model.info.AdminInfoListInfo;
import kl.npki.management.core.constants.RoleCodeEnum;
import kl.npki.management.core.repository.IAdminCertRepository;
import kl.npki.management.core.repository.IAdminInfoMgrRepository;
import kl.npki.management.core.repository.IAdminInfoRepository;
import kl.npki.management.core.service.AdminMgrService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;

import static kl.npki.km.management.constant.KMMgmtI18N.*;

/**
 * KM 管理员controller
 *
 * <AUTHOR>
 * @date 2023/2/2
 */
@RestController
@RequestMapping("/kmAdmin")
@Tag(name = "KM管理员管理")
@LogCollector(resolver = SecurityOperationLogResolver.class, maskFields = {"pfxKey", "p10CertReq"})
public class KmAdminMgrController implements BaseController {

    @Resource
    private AdminMgrService adminMgrService;

    @Resource
    private ConvertService convertService;

    @Resource
    private IAdminInfoRepository adminInfoRepository;

    @Resource
    private IAdminCertRepository adminCertRepository;

    @Resource
    private IAdminInfoMgrRepository adminInfoMgrRepository;

    @Resource
    private ValidateHelper validateHelper;

    /**
     * 签发证书
     *
     * @api {POST} /issueCert 签发证书
     * @apiDescription 签发证书
     * @apiPermission 部署时
     * @apiGroup deploy
     * @apiName issueCert
     * @apiBody {Number} roleId 角色id
     * @apiBody {String} subjectCn 用户名
     * @apiBody {String} [organization] 组织
     * @apiBody {String} [organizationUnit] 机构
     * @apiBody {String} [province] 省
     * @apiBody {String} [city] 市
     * @apiBody {String} email 邮箱
     * @apiBody {String} p10CertReq p10请求
     * @apiParamExample {json} Request-Example:
     * {
     * "roleId": 1,
     * "subjectCn": "张三",
     * "organization": "koal",
     * "organizationUnit": "koal",
     * "province": "shanghai",
     * "city": "shanghai",
     * "email": "<EMAIL>"
     * "p10CertReq": "MIIBDzCBtAIBADBSMQswCQYDVQQGEwJDTjELMAkGA1UECAwCc2gxCzAJBgNVBAcM
     * * AnNoMQwwCgYDVQQKDANvcmcxCzAJBgNVBAsMAmF1MQ4wDAYDVQQDDAVzbTJjYTBZ
     * * MBMGByqGSM49AgEGCCqBHM9VAYItA0IABGbgAtYchxs/zRjWBUHtR3p/eUi/J44o
     * * WkQCcHOPjgkITb9tN/0LC0oQhM+SA2YarRxhj4wPUpmMTP612bxBDVCgADAMBggq
     * * gRzPVQGDdQUAA0gAMEUCICotbigViNXazo42qvExm8QonG1b9kZLIEKCv9yb3Dkk
     * * AiEAieLVwYPKRuHutYXZoiDasZMulKtA6wKvZvABPEloIAE="
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @PostMapping("/init/issueAdmin")
    @Operation(description = INIT_ADMIN_CERT, summary = "管理员初始化签发证书")
    public RestResponse<AdminCertIssueResponse> issueCert(@RequestBody IssueAdminCertForDeployRequest issueAdminCertForDeployRequest) {
        // 参数检查
        validateHelper.validate(issueAdminCertForDeployRequest, issueAdminCertForDeployRequest.getRoleCode());

        AdminEntity issueAdminForDeploy = convertService.convert(issueAdminCertForDeployRequest, AdminEntity.class);
        AdminCertEntity issueAdminCertForDeploy = convertService.convert(issueAdminCertForDeployRequest, AdminCertEntity.class);
        // 获取管理根来进行管理员证书的签发
        String b64RootCert = MgrHolder.getManageCertMgr().getManageCertEntity().getCertValue();
        // 构建签发参数
        String pqEncKeyType = issueAdminCertForDeployRequest.getPqEncKeyType();
        AdminCertIssueParamInfo adminCertIssueParamInfo = new AdminCertIssueParamInfo(issueAdminForDeploy, issueAdminCertForDeploy, b64RootCert, pqEncKeyType);

        AdminCertIssueInfo adminCertIssueInfo = adminMgrService.initAdminCert(adminCertIssueParamInfo);
        AdminCertIssueResponse adminCert = convertService.convert(adminCertIssueInfo, AdminCertIssueResponse.class);
        return RestResponse.success(adminCert);
    }


    @PostMapping("/backup/issueAdmin")
    @Operation(description = BACKUP_ADMIN_CERT, summary = "备份管理员证书")
    public RestResponse<AdminCertIssueResponse> backupAdminCert(@RequestBody IssueAdminCertForDeployRequest issueAdminCertForDeployRequest) {
        // 参数检查
        validateHelper.validate(issueAdminCertForDeployRequest, issueAdminCertForDeployRequest.getRoleCode());

        String loginUserId = RequestContextHolder.getLoginUserId();
        AdminEntity issueAdminForDeploy = convertService.convert(issueAdminCertForDeployRequest, AdminEntity.class);
        AdminCertEntity issueAdminCertForDeploy = convertService.convert(issueAdminCertForDeployRequest, AdminCertEntity.class);
        // 获取管理根来进行管理员证书的签发
        String b64RootCert = MgrHolder.getManageCertMgr().getManageCertEntity().getCertValue();
        // 构建签发参数
        String pqEncKeyType = issueAdminCertForDeployRequest.getPqEncKeyType();
        AdminCertIssueParamInfo adminCertIssueParamInfo = new AdminCertIssueParamInfo(loginUserId, issueAdminForDeploy,
            issueAdminCertForDeploy, b64RootCert, pqEncKeyType);

        AdminCertIssueInfo adminCertIssueInfo = adminMgrService.backupAdminCert(adminCertIssueParamInfo);
        AdminCertIssueResponse adminCert = convertService.convert(adminCertIssueInfo, AdminCertIssueResponse.class);
        return RestResponse.success(adminCert);
    }

    /**
     * 签发管理员证书
     *
     * @api {POST} /admin/issueAdminCert 签发管理员证书
     * @apiDescription 签发管理员证书
     * @apiPermission 超级管理员、业务管理员、审计管理员
     * @apiGroup admin
     * @apiName issueAdminCert
     * @apiBody {Number} adminInfoId 管理员信息id
     * @apiBody {String} p10CertReq p10请求
     * @apiParamExample {IssueAdminCertRequest}  管理员证书签发请求信息  Request-Example:
     * {
     * "adminInfoId": 1,
     * "p10CertReq": "MIIBDzCBtAIBADBSMQswCQYDVQQGEwJDTjELMAkGA1UECAwCc2gxCzAJBgNVBAcM
     * AnNoMQwwCgYDVQQKDANvcmcxCzAJBgNVBAsMAmF1MQ4wDAYDVQQDDAVzbTJjYTBZ
     * MBMGByqGSM49AgEGCCqBHM9VAYItA0IABGbgAtYchxs/zRjWBUHtR3p/eUi/J44o
     * WkQCcHOPjgkITb9tN/0LC0oQhM+SA2YarRxhj4wPUpmMTP612bxBDVCgADAMBggq
     * gRzPVQGDdQUAA0gAMEUCICotbigViNXazo42qvExm8QonG1b9kZLIEKCv9yb3Dkk
     * AiEAieLVwYPKRuHutYXZoiDasZMulKtA6wKvZvABPEloIAE="
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */

    @PostMapping("/issueAdminCert")
    @Operation(description = ISSUE_ADMIN_CERT, summary = "签发管理员证书")
    public RestResponse<AdminCertIssueResponse> issueAdminCert(@RequestBody IssueAdminCertRequest issueAdminCertRequest) {
        AdminEntity adminEntity = adminInfoRepository.getById(issueAdminCertRequest.getAdminInfoId());
        adminEntity.checkStatus(UserStatus.NORMAL);
        adminEntity.setValidDays(issueAdminCertRequest.getValidDays());
        adminEntity.setInitAdmin(false);
        AdminCertEntity adminCertEntity = adminCertRepository.getByAdminInfoId(adminEntity.getId());
        adminCertEntity.setTwinCert(issueAdminCertRequest.getTwinCert());
        adminCertEntity.checkStatus(CertStatus.TO_BE_ISSUED);
        adminCertEntity.setPfxKey(issueAdminCertRequest.getPfxKey());
        adminCertEntity.setP10CertReq(issueAdminCertRequest.getP10CertReq());
        // 获取管理根来进行管理员证书的签发
        String b64RootCert = MgrHolder.getManageCertMgr().getManageCertEntity().getCertValue();
        // 构建签发参数
        String pqEncKeyType = issueAdminCertRequest.getPqEncKeyType();
        AdminCertIssueParamInfo adminCertIssueParamInfo = new AdminCertIssueParamInfo(adminEntity, adminCertEntity, b64RootCert, pqEncKeyType);
        AdminCertIssueInfo adminCertIssueInfo = adminMgrService.issueAdminCert(adminCertIssueParamInfo);
        AdminCertIssueResponse adminCert = convertService.convert(adminCertIssueInfo, AdminCertIssueResponse.class);
        return RestResponse.success(adminCert);
    }


    @PostMapping("/extendAdminCert")
    @Operation(description = POSTPONE_ADMIN_CERT, summary = "延期管理员证书")
    public RestResponse<AdminCertIssueResponse> extendAdminCert(@RequestBody ExtendAdminCertRequest issueAdminCertRequest) {
        validateHelper.validate(issueAdminCertRequest);
        Set<String> loginUserRoles = RequestContextHolder.getLoginUserRoles();
        // 获取管理根来进行管理员证书的签发
        String b64RootCert = MgrHolder.getManageCertMgr().getManageCertEntity().getCertValue();
        AdminCertIssueInfo adminCertIssueInfo = adminMgrService.extendAdminCert(loginUserRoles,
            issueAdminCertRequest.getAdminInfoId(), issueAdminCertRequest.getExtendDays(), b64RootCert);
        AdminCertIssueResponse adminCert = convertService.convert(adminCertIssueInfo, AdminCertIssueResponse.class);
        return RestResponse.success(adminCert);
    }

    @GetMapping("/lawRecover/adminList")
    @Operation(summary = "查询司法取证管理员列表", description = QUERY_LAW_RECOVER_ADMIN_LIST)
    public RestResponse<IPage<AdminInfoListResponse>> adminList(PageInfo pageInfo, AdminInfoListRequest adminInfoListRequest) {
        AdminInfoListDTO convert = convertService.convert(adminInfoListRequest, AdminInfoListDTO.class);
        IPage<AdminInfoListInfo> page = pageInfo.toPage();
        Set<String> roleCodeSet = new HashSet<>();
        roleCodeSet.add(RoleCodeEnum.LAW_ADMIN.getRoleCode());
        IPage<AdminInfoListInfo> adminInfoPages = adminInfoMgrRepository.queryForList(page, convert, roleCodeSet);
        IPage<AdminInfoListResponse> listResponseIPage = adminInfoPages.convert(this::convertAdminInfoList);
        return RestResponse.success(listResponseIPage);
    }

    private AdminInfoListResponse convertAdminInfoList(AdminInfoListInfo adminInfo) {
        AdminInfoListResponse response = convertService.convert(adminInfo, AdminInfoListResponse.class);
        response.setOrgName(OrgCache.INSTANCE.getOrgById(adminInfo.getOrgId()).getOrgName());
        return response;
    }
}
