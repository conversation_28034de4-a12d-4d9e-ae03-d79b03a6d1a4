package kl.npki.km.management.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.km.management.model.key.KeyPoolListRequest;
import kl.npki.km.management.model.key.KeyPoolListResponse;
import kl.npki.km.management.repository.service.IKeyManagementService;
import kl.npki.km.service.repository.entity.KeyPoolDO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static kl.npki.km.management.constant.KMMgmtI18N.BAK_CURRENT_KEY_LIST;

/**
 * <AUTHOR>
 * @date 2022/11/9 16:13
 * @Description: 备用库管理
 */
@RestController
@RequestMapping("keyPool")
@Tag(name = "密钥库管理")
@LogCollector(resolver = OperationLogResolver.class)
public class KeyPoolController implements BaseController {

    @Resource
    private IKeyManagementService keyManagementService;

    @GetMapping("/list")
    @Operation(description = BAK_CURRENT_KEY_LIST, summary = "查询备用密钥库")
    public RestResponse<IPage<KeyPoolListResponse>> currentKeyList(
        PageInfo pageInfo,
        KeyPoolListRequest keyPoolListRequest) {
        IPage<KeyPoolDO> page = pageInfo.toPage();
        IPage<KeyPoolListResponse> keyList = keyManagementService.queryPoolPageList(page, keyPoolListRequest);
        return RestResponse.success(keyList);
    }
}
