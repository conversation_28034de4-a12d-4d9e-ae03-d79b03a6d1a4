package kl.npki.km.management.controller.statistic;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.ibc.kgs.core.common.RepositoryHelper;
import kl.ibc.kgs.core.repository.IIdKeyCurrentRepository;
import kl.ibc.kgs.core.repository.IIdKeyHistoryRepository;
import kl.nbase.log.collect.LogCollector;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.management.utils.FileDownloadHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.km.core.biz.stat.model.CaKeyUseResponse;
import kl.npki.km.core.biz.stat.model.KeyCountInfo;
import kl.npki.km.core.biz.stat.model.KeystoreResponse;
import kl.npki.km.core.common.constants.KeyPosition;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.core.service.statistic.IKeyStatisticService;
import kl.npki.km.management.controller.BaseController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

import static kl.npki.km.management.constant.KMMgmtI18N.*;

/**
 * <AUTHOR>
 * @date 2022/11/3 11:14
 * @Description: 统计报表接口
 */
@RestController
@RequestMapping("statistic")
@Tag(name = "统计报表")
@LogCollector(resolver = OperationLogResolver.class)
public class KeyStatisticController implements BaseController {

    /**
     * 密钥库统计报表文件名
     */
    private static final String KEYSTORE_REPORT_FILE_NAME = "keystore_report.xlsx";
    /**
     * 密钥使用情况报表文件名
     */
    private static final String KEYSUSE_REPORT_FILE_NAME = "keyuse_report.xlsx";

    @Resource
    private IKeyStatisticService statisticService;

    @GetMapping("/keystore")
    @Operation(description = KEY_VAULT_STATS, summary = "密钥库统计")
    public RestResponse<List<KeystoreResponse>> statisticKeystoreReport() {
        List<KeystoreResponse> keystoreResponseList = statisticService.statisticKeystoreReport();
        statisticSm9KeystoreReport(keystoreResponseList);
        return RestResponse.success(keystoreResponseList);
    }

    @GetMapping("/keystore/export")
    @LogCollector
    @Operation(description = EXPORTED_KEY_VAULT_STATISTICAL_REPORT, summary = "导出密钥库统计报表")
    public void exportKeystoreReport(HttpServletResponse response) throws IOException {
        // 设置返回格式是excel形式
        response.setHeader(FileDownloadHelper.CONTENT_DISPOSITION, FileDownloadHelper.buildAttachmentVal(KEYSTORE_REPORT_FILE_NAME));
        statisticService.exportKeystoreReport(response.getOutputStream());
    }

    @GetMapping("/keyUse")
    @Operation(description = TOTAL_KEY_USAGE_COUNT, summary = "密钥使用情况统计")
    public RestResponse<List<CaKeyUseResponse>> statisticKeyUseReport() {
        List<CaKeyUseResponse> caKeyUseResponses = statisticService.statisticKeyUseReport();

        return RestResponse.success(caKeyUseResponses);
    }

    @GetMapping("/keyUse/export")
    @LogCollector
    @Operation(description = EXPORTED_KEY_USAGE_STAT_REPORT, summary = "导出密钥使用情况统计报表")
    public void exportKeyUseReport(HttpServletResponse response) throws IOException {
        // 设置返回格式是excel形式
        response.setHeader(FileDownloadHelper.CONTENT_DISPOSITION, FileDownloadHelper.buildAttachmentVal(KEYSUSE_REPORT_FILE_NAME));
        statisticService.exportKeyUseReport(response.getOutputStream());
    }

    @GetMapping("/keyService")
    @Operation(description = KEY_SERVICE_STATS_NOT_IMPLEMENTED, summary = "密钥服务统计(未实现)")
    public RestResponse<List<String>> statisticKeyServiceReport() {
        List<String> keyService = statisticService.statisticKeyServiceReport();
        return RestResponse.success(keyService);
    }

    @GetMapping("/keyLog")
    @Operation(description = KEY_LOG_ANALYSIS_STATS_NOT_IMPLEMENTED, summary = "密钥日志分析统计(未实现)")
    public RestResponse<String> statisticKeyServiceErrorReport() {
        return RestResponse.success();
    }

    private static void statisticSm9KeystoreReport(List<KeystoreResponse> keystoreResponseList) {
        if (KmConfigWrapper.getKmFeatureConf().isEnableIbcKm()) {
            // SM9密钥统计
            keystoreResponseList.forEach(keystoreResponse -> {
                String name = keystoreResponse.getName();
                if (KeyPosition.POS_CURRENT.tr().equals(name)) {
                    // 当前库统计
                    IIdKeyCurrentRepository keyCurrentRepository = RepositoryHelper.getKeyCurrentRepository();
                    if (keyCurrentRepository != null) {
                        keystoreResponse.getKeyCountInfoList().add(
                            new KeyCountInfo(AsymAlgo.SM9.getAlgoName(), (int) keyCurrentRepository.countKeyNum())
                        );
                    }
                } else if (KeyPosition.POS_HISTORY.tr().equals(name)) {
                    // 历史库统计
                    IIdKeyHistoryRepository keyHistoryRepository = RepositoryHelper.getKeyHistoryRepository();
                    if (keyHistoryRepository != null) {
                        keystoreResponse.getKeyCountInfoList().add(
                            new KeyCountInfo(AsymAlgo.SM9.getAlgoName(), (int) keyHistoryRepository.countKeyNum())
                        );
                    }
                }
            });
        }
    }

}
