package kl.npki.km.management.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.helper.utils.CheckUtils;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.km.core.biz.key.model.KeyTraceEntity;
import kl.npki.km.core.repository.IKeyTraceRepositoryImpl;
import kl.npki.km.management.exception.KmManagementValidationError;
import kl.npki.km.management.model.key.KeyTraceResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static kl.npki.km.management.constant.KMMgmtI18N.QUERY_KEY_TRACE_DETAIL;

/**
 * 密钥轨迹controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("keyTrace")
@Tag(name = "密钥轨迹")
@LogCollector(resolver = OperationLogResolver.class)
public class KeyTraceController implements BaseController {


    @Resource
    private IKeyTraceRepositoryImpl keyTraceRepository;

    @Resource
    private ConvertService convertService;

    @GetMapping("/detail/{entitySn}")
    @Operation(description = QUERY_KEY_TRACE_DETAIL, summary = "获取密钥轨迹详情")
    public RestResponse<List<KeyTraceResponse>> queryKeyTraceDetail(@PathVariable(value = "entitySn") String entitySn,
                                                                    String keyType) {
        List<KeyTraceEntity> keyTraceEntities = keyTraceRepository.searchByEntitySn(entitySn, keyType);
        CheckUtils.notNull(keyTraceEntities, KmManagementValidationError.KEY_TRACE_NOT_FOUND_ERROR.toException());
        List<KeyTraceResponse> result = convertService.convert(keyTraceEntities, KeyTraceResponse.class);
        return RestResponse.success(result);
    }
}
