package kl.npki.km.management.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.bean.validate.ValidateService;
import kl.nbase.log.collect.LogCollector;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.timer.client.ITimerClient;
import kl.nbase.timer.job.ITimerJob;
import kl.nbase.timer.job.TimerJobFactory;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.km.core.common.constants.KmJobEnum;
import kl.npki.km.core.common.constants.SksKey;
import kl.npki.km.core.configs.*;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.management.exception.KmManagementInternalError;
import kl.npki.km.management.model.config.*;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static kl.npki.km.management.constant.KMMgmtI18N.*;

/**
 * @Author: guoq
 * @Date: 2022/10/13
 * @description:
 */
@RestController
@RequestMapping("/config")
@Tag(name = "KM配置管理")
@LogCollector(resolver = OperationLogResolver.class)
public class ConfigController implements BaseController {

    @Resource
    private ValidateService validateService;

    @Resource
    private ConvertService convertService;

    @Resource
    private ITimerClient timerClient;

    @GetMapping("/archive")
    @Operation(description = GET_ARCHIVE_KEY_CONFIG, summary = "获取归档配置")
    public RestResponse<ArchiveKeyConfigResponse> getArchiveKeyConf() {
        ArchiveKeyConfig archiveKeyConf = KmConfigWrapper.getArchiveKeyConf();
        ArchiveKeyConfigResponse configResponse = convertService.convert(archiveKeyConf,
            ArchiveKeyConfigResponse.class);
        configResponse.setScheduledStatus(timerClient.getStatus(KmJobEnum.KEY_ARCHIVE.getId()));
        configResponse.setLastExecuteTime(timerClient.getJob(KmJobEnum.KEY_ARCHIVE.getId()).getLastExecuteTime());
        return RestResponse.success(configResponse);
    }

    /**
     * @api {PUT} /config/archive 修改归档配置
     * @apiDescription 修改归档配置
     * @apiGroup config
     * @apiName updateArchiveKeyConf
     * @apiBody {Number} lateArchiveTime 延迟归档时间(天)
     * @apiBody {Number} archivePageThreshold 归档分页阈值
     * @apiBody {String} archiveCronExpression 密钥生成执行间隔 cron表达式
     * @apiParamExample {ArchiveKeyConf}  归档配置  Request-Example:
     * {
     * "lateArchiveTime": 365,
     * "archivePageThreshold": 200,
     * "archiveCronExpression": "0 0 0 * * ?",
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @PutMapping("/archive")
    @Operation(description = UPDATE_ARCHIVE_KEY_CONF, summary = "修改归档配置")
    public RestResponse<String> updateArchiveKeyConf(@RequestBody ArchiveKeyConfigRequest archiveKeyConfig) {
        //参数校验
        validateService.validate(archiveKeyConfig);
        ArchiveKeyConfig keyConfig = convertService.convert(archiveKeyConfig, ArchiveKeyConfig.class);
        ConfigHolder.get().save(keyConfig);
        // 刷新定时任务
        timerClient.refresh(KmJobEnum.KEY_ARCHIVE.getId());
        return RestResponse.success();
    }

    /**
     * 触发手动归档
     */
    @GetMapping("/archive/trigger")
    @Operation(description = TRIGGER_KEY_ARCHIVE_JOB, summary = "手动归档触发")
    public RestResponse<String> triggerKeyArchiveJob() {
        //todo 通过定时任务管理类触发
        //已经再运行过程中，跳过
        return RestResponse.success();
    }

    /**
     * @return
     */
    @GetMapping("/key")
    @Operation(description = GET_KEY_CONF, summary = "获取密钥配置")
    public RestResponse<KeyConfigResponse> getKeyConf() {
        KeyConfig keyConfig = KmConfigWrapper.getKeyConf();
        keyConfig.initKeyLimitConf();
        KeyConfigResponse keyConfigResponse = convertService.convert(keyConfig, KeyConfigResponse.class);
        keyConfigResponse.getKeyLimits().forEach(limitConfig -> {
            String jobId = KmJobEnum.KEY_GEN.getId() + limitConfig.getKeyType();
            limitConfig.setScheduledStatus(timerClient.getStatus(jobId));
            ITimerJob iTimerJob = TimerJobFactory.get(jobId);
            limitConfig.setLastExecuteTime(ObjectUtils.isEmpty(iTimerJob) ? null : iTimerJob.getLastExecuteTime());
        });

        return RestResponse.success(keyConfigResponse);
    }

    @PutMapping("/key")
    @Operation(description = UPDATE_KEY_CONF, summary = "修改密钥配置")
    public RestResponse<String> updateKeyConf(@RequestBody KeyConfigRequest keyConfig) {
        //参数校验
        validateService.validate(keyConfig);
        KeyConfig convert = convertService.convert(keyConfig, KeyConfig.class);
        ConfigHolder.get().save(convert);
        // 修改定时任务启停
        List<KeyLimitConfig> keyLimits = convert.getKeyLimits();

        for (KeyLimitConfig keyLimitConfig : keyLimits) {
            String jobId = KmJobEnum.KEY_GEN.getId() + keyLimitConfig.getKeyType();

            if (keyLimitConfig.isEnable()) {
                // 刷新定时任务，开启更新配置，未开启则开启
                timerClient.refresh(jobId);
            } else {
                // 关闭定时任务
                timerClient.stop(jobId);
            }
        }
        if (KmConfigWrapper.getKeyConf().isCacheAutoSupply()) {
            // 刷新缓存填充任务
            timerClient.refresh(KmJobEnum.KEY_CACHE_ADD.getId());
        }
        return RestResponse.success();
    }

    @GetMapping("/km")
    @Operation(description = GET_KM_CONF, summary = "获取KM配置")
    public RestResponse<KmConfigResponse> getKmConf() {
        KmConfigResponse kmConfigResponse = convertService.convert(KmConfigWrapper.getKmConf(), KmConfigResponse.class);
        return RestResponse.success(kmConfigResponse);
    }

    @PutMapping("/km")
    @Operation(description = UPDATE_KM_CONF, summary = "修改KM配置")
    public RestResponse<KmConfigResponse> updateKmConf(@RequestBody KmConfigRequest kmConfigRequest) {
        //参数校验
        validateService.validate(kmConfigRequest);
        // 仅当请求开启SM9服务时，校验加密机是否支持SM9算法
        if (Boolean.TRUE.equals(kmConfigRequest.isEnabledSm9Service())) {
            boolean engineSupportSm9 = BaseConfigWrapper.getClusterEmConfig()
                .queryEmEngineSupportedAlgo()
                .getAsymAlgAbility()
                .contains(AsymAlgo.SM9);
            if (!engineSupportSm9) {
                throw KmManagementInternalError.SM9_KEY_SERVICE_ENABLED_ERROR.toException(ENGINE_NOT_SUPPORT_SM9);
            }
        }
        // 为防止convert转换时，直接将原有boolean值替换，使用set方式
        KmConfig kmConf = KmConfigWrapper.getKmConf();
        KmFeatureConfig featureConfig = KmConfigWrapper.getKmFeatureConf();
        kmConf.setCheckSnExist(kmConfigRequest.isCheckSnExist());
        kmConf.setSignResponse(kmConfigRequest.isSignResponse());
        kmConf.setVerifyRequest(kmConfigRequest.isVerifyRequest());
        kmConf.setRestoreCA(kmConfigRequest.isRestoreCA());
        featureConfig.setEnableIbcKm(Boolean.TRUE.equals(kmConfigRequest.isEnabledSm9Service()));
        ConfigHolder.get().save(featureConfig);
        ConfigHolder.get().save(kmConf);
        KmConfigResponse kmConfigResponse = convertService.convert(kmConf, KmConfigResponse.class);
        return RestResponse.success(kmConfigResponse);
    }

    @GetMapping("/keyType")
    @Operation(description = KEY_TYPE_LIST, summary = "获取支持的非对称密钥类型(包括抗量子算法)")
    public RestResponse<List<String>> keyTypeList(
        @RequestParam(required = false) boolean includeSKS,
        @RequestParam(required = false) boolean includePostQuantum
    ) {
        List<String> supportAsymAlgo = BaseConfigWrapper.getSysAlgoConfig().extractAsymmetricList();
        List<String> supportPostQuantumAlgo = BaseConfigWrapper.getSysAlgoConfig().extractPostQuantumList();
        List<String> supportedKeyTypes = new ArrayList<>(supportAsymAlgo);

        if (includePostQuantum) {
            supportedKeyTypes.addAll(supportPostQuantumAlgo);
        }

        if (includeSKS && KmConfigWrapper.getKmFeatureConf().isEnableSks()) {
            supportedKeyTypes.add(SksKey.SKS.tr());
        }
        supportedKeyTypes.removeIf(keyType -> AsymAlgo.SM9.getAlgoName().equals(keyType));
        return RestResponse.success(supportedKeyTypes);
    }

}
