package kl.npki.km.management.mapper.key;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.common.constants.SksKey;
import kl.npki.km.management.model.key.KeyCurrentListResponse;
import kl.npki.km.service.repository.entity.KeyCurrentDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

/**
 * @Author: guoq
 * @Date: 2022/10/9
 * @description: 密钥当前库对象 to 列表响应
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KeyCurrentDO2ListResponseMapper extends BaseMapper<KeyCurrentDO, KeyCurrentListResponse> {

    @Override
    @Mapping(target = "keyTypeDesc", expression = "java(mapKeyType(source.getKeyType()))")
    KeyCurrentListResponse map(KeyCurrentDO source);

    @Override
    KeyCurrentDO reverseMap(KeyCurrentListResponse target);

    /**
     * 映射密钥类型，如果是SKS类型则使用SksKey.SKS.tr()方法转换
     *
     * @param keyType 原始密钥类型
     * @return 转换后的密钥类型
     */
    @Named("mapKeyType")
    default String mapKeyType(String keyType) {
        if (SksKey.SKS.getName().equals(keyType)) {
            return SksKey.SKS.tr();
        }
        return keyType;
    }
}
