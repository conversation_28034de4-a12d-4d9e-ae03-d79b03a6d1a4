package kl.npki.km.management.mapper.config;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.configs.KmConfig;
import kl.npki.km.management.model.config.KmConfigRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * KmConfig与KmConfigRequest的mapper映射
 *
 * <AUTHOR>
 * @date 2023/9/1
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KmConfig2RequestMapper extends BaseMapper<KmConfig, KmConfigRequest> {

    @Override
    KmConfigRequest map(KmConfig source);

    @Override
    KmConfig reverseMap(KmConfigRequest target);
}
