package kl.npki.km.management.mapper.ca;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.management.model.ca.CaResourceResponse;
import kl.npki.km.service.repository.entity.CaResourceDO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/11/14 15:45
 * @Description:
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CaResourceDO2ResponseMapper extends BaseMapper<CaResourceDO, CaResourceResponse> {

    @Override
    CaResourceResponse map(CaResourceDO source);

    @Override
    CaResourceDO reverseMap(CaResourceResponse target);
}
