package kl.npki.km.management.mapper.ssl;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.ssl.SslClientCertEntity;
import kl.npki.km.management.repository.entity.SSLCertDO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022-12-28
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SSLCertDO2EntityMapper extends BaseMapper<SSLCertDO, SslClientCertEntity> {
}
