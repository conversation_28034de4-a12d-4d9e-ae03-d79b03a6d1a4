package kl.npki.km.management.mapper.statistic;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.biz.statistic.SksStatisticResultEntity;
import kl.npki.km.management.repository.entity.SksStatisticResultDO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SksStatisticResultDO2EntityMapper extends BaseMapper<SksStatisticResultDO, SksStatisticResultEntity> {
}
