package kl.npki.km.management.mapper.config;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.configs.KmConfig;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.management.model.config.KmConfigResponse;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @since 2025/6/27
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KmConfig2ResponseMapper extends BaseMapper<KmConfig, KmConfigResponse> {

    @Override
    KmConfigResponse map(KmConfig source);

    @Override
    KmConfig reverseMap(KmConfigResponse target);

    @AfterMapping
    default void setEnabledSm9Service(KmConfig source, @MappingTarget KmConfigResponse target) {
        target.setEnabledSm9Service(KmConfigWrapper.getKmFeatureConf().isEnableIbcKm());
    }

}
