package kl.npki.km.management.mapper.config;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.configs.ArchiveKeyConfig;
import kl.npki.km.management.model.config.ArchiveKeyConfigRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * @Author: guoq
 * @Date: 2023/5/19
 * @description:
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ArchiveKeyConfig2RequestMapper extends BaseMapper<ArchiveKeyConfig, ArchiveKeyConfigRequest> {

    @Override
    ArchiveKeyConfigRequest map(ArchiveKeyConfig source);

    @Override
    ArchiveKeyConfig reverseMap(ArchiveKeyConfigRequest target);
}
