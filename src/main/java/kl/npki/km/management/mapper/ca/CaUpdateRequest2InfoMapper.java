package kl.npki.km.management.mapper.ca;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.biz.ca.model.CaUpdateInfo;
import kl.npki.km.management.model.ca.CaUpdateRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * @Author: guoq
 * @Date: 2023/5/19
 * @description:
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CaUpdateRequest2InfoMapper extends BaseMapper<CaUpdateRequest, CaUpdateInfo> {

    @Override
    CaUpdateInfo map(CaUpdateRequest source);

    @Override
    CaUpdateRequest reverseMap(CaUpdateInfo target);
}
