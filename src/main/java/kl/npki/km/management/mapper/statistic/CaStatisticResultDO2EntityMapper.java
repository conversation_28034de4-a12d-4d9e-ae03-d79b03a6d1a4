package kl.npki.km.management.mapper.statistic;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.biz.statistic.CaStatisticResultEntity;
import kl.npki.km.management.repository.entity.CaStatisticResultDO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CaStatisticResultDO2EntityMapper extends BaseMapper<CaStatisticResultDO, CaStatisticResultEntity> {
}
