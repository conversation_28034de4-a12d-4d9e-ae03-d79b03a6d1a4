package kl.npki.km.management.mapper.ca;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.constant.CaKeyResponseStandardVersionEnum;
import kl.npki.km.core.biz.ca.model.CaEntity;
import kl.npki.km.management.model.ca.CaRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * @Author: guoq
 * @Date: 2022/8/30
 * @description: ca info转实体 mapper
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {CaKeyResponseStandardVersionEnum.class})
public interface CaRequest2EntityMapper extends BaseMapper<CaRequest, CaEntity> {

    @Override
    @Mapping(target = "sm2KeyStandardVersion", expression = "java(CaKeyResponseStandardVersionEnum.valueOfCode(source.getSm2KeyStandardVersion()))")
    CaEntity map(CaRequest source);

    @Override
    @Mapping(target = "sm2KeyStandardVersion", expression = "java(target.getSm2KeyStandardVersion().getCode())")
    CaRequest reverseMap(CaEntity target);
}
