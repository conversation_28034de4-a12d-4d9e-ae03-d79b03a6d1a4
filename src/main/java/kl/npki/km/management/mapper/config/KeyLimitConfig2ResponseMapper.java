package kl.npki.km.management.mapper.config;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.configs.KeyLimitConfig;
import kl.npki.km.management.model.config.KeyLimitConfigResponse;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * @Author: guoq
 * @Date: 2023/5/19
 * @description:
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KeyLimitConfig2ResponseMapper extends BaseMapper<KeyLimitConfig, KeyLimitConfigResponse> {

    @Override
    KeyLimitConfigResponse map(KeyLimitConfig source);

    @Override
    KeyLimitConfig reverseMap(KeyLimitConfigResponse target);
}
