package kl.npki.km.management.mapper.ca;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.constant.CaKeyResponseStandardVersionEnum;
import kl.npki.km.core.biz.ca.model.CaEntity;
import kl.npki.km.core.common.constants.CaAccessTypeEnum;
import kl.npki.km.core.common.constants.CaVersionTypeEnum;
import kl.npki.km.management.model.ca.CaDetailResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/11/17 13:52
 * @Description:
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {CaVersionTypeEnum.class, CaAccessTypeEnum.class, CaKeyResponseStandardVersionEnum.class})
public interface CaEntity2DetailMapper extends BaseMapper<CaEntity, CaDetailResponse> {

    @Override
    @Mapping(source = "id", target = "caId")
    @Mapping(target = "idCertTrusted", expression = "java(source.isIdCertTrusted())")
    @Mapping(target = "caVersion", expression = "java(String.format(\"%s(%s)\",source.getCaVersion().tr(), source.getCaVersion().getCharset()))")
    @Mapping(target = "caAccessType", expression = "java(source.getCaAccessType().tr())")
    @Mapping(target = "caStatus", expression = "java(source.getCaStatus().getId())")
    @Mapping(target = "sm2KeyStandardVersion", expression = "java(source.getSm2KeyStandardVersion().tr())")
    CaDetailResponse map(CaEntity source);

    @Override
    @Mapping(target = "caStatus", ignore = true)
    CaEntity reverseMap(CaDetailResponse target);
}
