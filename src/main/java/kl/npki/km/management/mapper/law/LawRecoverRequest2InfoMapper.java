package kl.npki.km.management.mapper.law;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.biz.law.model.LawRecoverInfo;
import kl.npki.km.management.model.law.LawRecoverRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * @Author: guoq
 * @Date: 2022/10/12
 * @description:
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LawRecoverRequest2InfoMapper extends BaseMapper<LawRecoverRequest, LawRecoverInfo> {

    @Override
    LawRecoverInfo map(LawRecoverRequest source);

    @Override
    LawRecoverRequest reverseMap(LawRecoverInfo target);
}
