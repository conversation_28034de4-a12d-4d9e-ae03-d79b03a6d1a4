package kl.npki.km.management.mapper.key;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.biz.key.model.CurrentKeyEntity;
import kl.npki.km.management.model.key.KeyCurrentDetailResponse;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * @Author: guoq
 * @Date: 2022/10/9
 * @description: 密钥当前库对象to详情响应
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KeyCurrentEntity2DetailResponseMapper extends BaseMapper<CurrentKeyEntity, KeyCurrentDetailResponse> {

    @Override
    KeyCurrentDetailResponse map(CurrentKeyEntity source);

    @Override
    CurrentKeyEntity reverseMap(KeyCurrentDetailResponse target);
}
