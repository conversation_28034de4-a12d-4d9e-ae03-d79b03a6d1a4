package kl.npki.km.management.mapper.key;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.biz.key.model.CurrentKeyEntity;
import kl.npki.km.core.common.constants.SksKey;
import kl.npki.km.management.model.key.KeyCurrentDetailResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

/**
 * @Author: guoq
 * @Date: 2022/10/9
 * @description: 密钥当前库对象to详情响应
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KeyCurrentEntity2DetailResponseMapper extends BaseMapper<CurrentKeyEntity, KeyCurrentDetailResponse> {

    @Override
    @Mapping(target = "keyTypeDesc", expression = "java(mapKeyType(source.getKeyType()))")
    @Mapping(target = "keyIndex", expression = "java(mapKeyIndex(source.getKeyType(), source.getEncCertSn(), source.getKeyIndex()))")
    KeyCurrentDetailResponse map(CurrentKeyEntity source);

    @Override
    CurrentKeyEntity reverseMap(KeyCurrentDetailResponse target);

    /**
     * 映射密钥类型，如果是SKS类型则使用SksKey.SKS.tr()方法转换
     *
     * @param keyType 原始密钥类型
     * @return 转换后的密钥类型
     */
    @Named("mapKeyType")
    default String mapKeyType(String keyType) {
        if (SksKey.SKS.getName().equals(keyType)) {
            return SksKey.SKS.tr();
        }
        return keyType;
    }
    
    /**
     * 映射密钥索引
     * - 如果是SKS类型，则使用encCertSn作为keyIndex
     * - 对于其他类型，使用原始keyIndex值
     *
     * @param keyType 密钥类型
     * @param encCertSn 加密证书序列号
     * @param keyIndex 原始密钥索引
     * @return 映射后的密钥索引
     */
    @Named("mapKeyIndex")
    default String mapKeyIndex(String keyType, String encCertSn, String keyIndex) {
        if (SksKey.SKS.getName().equals(keyType)) {
            return encCertSn;
        }
        return keyIndex;
    }
}
