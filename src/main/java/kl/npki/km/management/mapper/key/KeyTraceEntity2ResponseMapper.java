package kl.npki.km.management.mapper.key;


import kl.nbase.bean.convert.BaseMapper;
import kl.nbase.i18n.i18n.I18nUtil;
import kl.npki.base.core.common.trace.InvocationType;
import kl.npki.km.core.biz.key.model.KeyTraceEntity;
import kl.npki.km.core.common.constants.CaKeyServiceType;
import kl.npki.km.core.common.constants.SksKey;
import kl.npki.km.management.model.key.KeyTraceResponse;
import kl.npki.km.service.constant.KMServiceI18N;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {CaKeyServiceType.class, InvocationType.class, KMServiceI18N.class, I18nUtil.class})
public interface KeyTraceEntity2ResponseMapper extends BaseMapper<KeyTraceEntity, KeyTraceResponse> {

    @Override
    @Mapping(target = "bizName", expression = "java(CaKeyServiceType.getDescByName(source.getBizName()))")
    @Mapping(target = "invocationType", expression = "java(InvocationType.getDescByName(source.getInvocationType()))")
    @Mapping(target = "operatorName", expression = "java(KMServiceI18N.SYSTEM_OPERATOR.equals(source.getOperatorName()) ? I18nUtil.tr(KMServiceI18N.SYSTEM_OPERATOR) : source.getOperatorName())")
    @Mapping(target = "keyType", expression = "java(mapKeyType(source.getKeyType()))")
    KeyTraceResponse map(KeyTraceEntity source);

    /**
     * 映射密钥类型，如果是SKS类型则使用SksKey.SKS.tr()方法转换
     *
     * @param keyType 原始密钥类型
     * @return 转换后的密钥类型
     */
    default String mapKeyType(String keyType) {
        if (SksKey.SKS.getName().equals(keyType)) {
            return SksKey.SKS.tr();
        }
        return keyType;
    }

}
