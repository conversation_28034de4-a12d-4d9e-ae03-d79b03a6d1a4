package kl.npki.km.management.mapper.key;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.common.constants.ArchiveReason;
import kl.npki.km.core.common.constants.SksKey;
import kl.npki.km.management.model.key.KeyHistoryDetailResponse;
import kl.npki.km.service.repository.entity.KeyHistoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

/**
 * @Author: guoq
 * @Date: 2022/10/9
 * @description: 密钥历史库对象 to 详情响应
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KeyHistoryDO2DetailResponseMapper extends BaseMapper<KeyHistoryDO, KeyHistoryDetailResponse> {

    @Override
    @Mapping(target = "archiveReason", expression = "java(translateArchiveReason(source.getArchiveReason()))")
    @Mapping(target = "keyTypeDesc", expression = "java(mapKeyType(source.getKeyType()))")
    @Mapping(target = "keyIndex", expression = "java(mapKeyIndex(source.getKeyType(), source.getEncCertSn(), source.getKeyIndex()))")
    KeyHistoryDetailResponse map(KeyHistoryDO source);

    @Override
    KeyHistoryDO reverseMap(KeyHistoryDetailResponse target);
    
    /**
     * 安全地转换归档原因，处理自定义归档原因
     * 
     * @param archiveReason 归档原因字符串
     * @return 翻译后的归档原因
     */
    @Named("translateArchiveReason")
    default String translateArchiveReason(String archiveReason) {
        if (archiveReason == null) {
            return null;
        }
        
        try {
            return ArchiveReason.valueOf(archiveReason).tr();
        } catch (IllegalArgumentException e) {
            // 如果不是预定义的枚举值，直接返回原始字符串
            return archiveReason;
        }
    }

    /**
     * 映射密钥类型，如果是SKS类型则使用SksKey.SKS.tr()方法转换
     *
     * @param keyType 原始密钥类型
     * @return 转换后的密钥类型
     */
    @Named("mapKeyType")
    default String mapKeyType(String keyType) {
        if (SksKey.SKS.getName().equals(keyType)) {
            return SksKey.SKS.tr();
        }
        return keyType;
    }

    /**
     * 映射密钥索引
     * - 如果是SKS类型，则使用encCertSn作为keyIndex
     * - 对于其他类型，使用原始keyIndex值
     *
     * @param keyType 密钥类型
     * @param encCertSn 加密证书序列号
     * @param keyIndex 原始密钥索引
     * @return 映射后的密钥索引
     */
    @Named("mapKeyIndex")
    default String mapKeyIndex(String keyType, String encCertSn, String keyIndex) {
        if (SksKey.SKS.getName().equals(keyType)) {
            return encCertSn;
        }
        return keyIndex;
    }
}
