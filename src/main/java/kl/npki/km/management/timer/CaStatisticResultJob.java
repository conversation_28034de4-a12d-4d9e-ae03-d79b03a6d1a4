package kl.npki.km.management.timer;

import kl.nbase.cache.client.ICacheClient;
import kl.nbase.cache.key.CacheKey;
import kl.nbase.cache.lock.ILock;
import kl.nbase.helper.utils.DateUtils;
import kl.nbase.helper.utils.LocalDateTimeUtils;
import kl.nbase.timer.job.AbstractTimerJob;
import kl.npki.base.core.biz.log.model.ApiLogCountServiceResult;
import kl.npki.base.core.constant.StatisticDimensionsEnum;
import kl.npki.base.core.tenantholders.CacheClientHolder;
import kl.npki.km.core.biz.ca.model.CaEntity;
import kl.npki.km.core.biz.stat.model.CaBizStatisticInfo;
import kl.npki.km.core.biz.statistic.CaStatisticResultEntity;
import kl.npki.km.core.common.RepositoryHelper;
import kl.npki.km.core.common.constants.KmJobEnum;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.core.repository.ICaStatisticResultRepository;
import kl.npki.km.core.service.statistic.ICaBizStatisticService;
import kl.npki.km.core.utils.DateStatisticUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class CaStatisticResultJob extends AbstractTimerJob implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(CaStatisticResultJob.class);

    private static final String LOCK_KEY = "ca_statistic_result_job";

    @Resource
    private ICaBizStatisticService caBizStatisticService;


    @Resource
    private ICaStatisticResultRepository caStatisticResultRepository;

    /**
     * 系统启动后，也执行一次
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(String... args) throws Exception {
        ILock lock = getLock();
        try {
            if (lock.tryLock()) {
                taskExecute();
            }
        } catch (Exception e) {
            log.error("CA报表结果定时统计任务出现异常！", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    protected void taskExecute() {
        // 查询所有ca名称
        List<String> caNameList = RepositoryHelper.getCaRepository().getCaNameListExceptRevoked();

        for (String caName : caNameList) {
            // 计算该ca需要统计哪几天的数据
            List<LocalDate> needStatisticsDates = getNeedStatisticsDates(caName);
            for (LocalDate localDate : needStatisticsDates) {
                // 统计这一天的结果数据
                List<ApiLogCountServiceResult> results = caBizStatisticService.statisticOneDay(Collections.singletonList(caName), localDate);
                // 保存结果
                saveResult(caName, localDate, results);
            }
        }
    }

    /**
     * 获得对应ca需要统计的日期
     *
     * @param caName
     * @return
     */
    private List<LocalDate> getNeedStatisticsDates(String caName) {
        // 查询每一个ca最新更新的报表结果
        Optional<CaStatisticResultEntity> latestResultByDay = caStatisticResultRepository.searchLatestResultByDay(caName);

        // 上次统计时间
        LocalDateTime lastStatisticDate;

        if (latestResultByDay.isPresent()) {
            lastStatisticDate = latestResultByDay.get().getStatisticDate();
        } else {
            // 为空说明一直未统计，找到该ca的接入时间，作为起始统计时间
            CaEntity caEntity = RepositoryHelper.getCaRepository().searchCaEntityExceptRevoked(caName);
            LocalDateTime createTime = caEntity.getCreateTime();
            // 记录的时间减一天，否则该天的数据不会进行统计
            lastStatisticDate = createTime.minusDays(1);
        }
        if (!log.isDebugEnabled()) {
            log.debug("ca:{}，最新更新的报表结果统计时间:{}", caName, DateUtils.formatByDateTimePattern(LocalDateTimeUtils.parseDate(lastStatisticDate)));
        }
        // 计算需要统计哪几天的数据
        return DateStatisticUtil.calculateDateDifferenceOfDay(lastStatisticDate.toLocalDate(), LocalDate.now().minusDays(1));
    }

    /**
     * 保存统计结果至结果表
     *
     * @param caName
     * @param localDate
     * @param apiLogCountServiceResults
     */
    private void saveResult(String caName, LocalDate localDate, List<ApiLogCountServiceResult> apiLogCountServiceResults) {
        if (CollectionUtils.isNotEmpty(apiLogCountServiceResults)) {
            // 转化查询结果为报表
            List<CaBizStatisticInfo> caBizStatisticInfos = CaBizStatisticInfo.convert2StatisticInfoList(apiLogCountServiceResults);

            // 结果进行入库
            CaStatisticResultEntity tobeSaveEntity = CaStatisticResultEntity.convertCaBizStatisticInfo(caBizStatisticInfos.get(0));
            tobeSaveEntity.save(localDate, StatisticDimensionsEnum.BY_DAY.getCode());
        } else {
            // 保存当天的空结果，避免下次重新计算
            CaStatisticResultEntity.saveEmptyResult(caName, localDate, StatisticDimensionsEnum.BY_DAY.getCode());
        }
    }

    @Override
    public boolean getEnable() {
        return KmConfigWrapper.getApiLogStatisticConfig().isEnable();
    }

    @Override
    public String getCron() {
        return KmConfigWrapper.getApiLogStatisticConfig().getStatisticResultGenCronExpression();
    }

    @Override
    public String getId() {
        return KmJobEnum.CA_STATISTIC_RESULT_ADD.getId();
    }

    @Override
    public String getName() {
        return KmJobEnum.CA_STATISTIC_RESULT_ADD.getName();
    }

    /**
     * 获取锁
     *
     * @return
     */
    private ILock getLock() {
        ICacheClient client = CacheClientHolder.get();
        return client.getLock(new CacheKey(LOCK_KEY));
    }
}
