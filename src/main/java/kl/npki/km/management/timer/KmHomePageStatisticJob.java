package kl.npki.km.management.timer;

import kl.npki.base.management.timer.HomePageStatisticJob;
import kl.npki.km.core.service.home.CaKeyStatisticService;
import kl.npki.km.core.service.home.KeyDistributionStatisticService;
import kl.npki.km.core.service.home.KeyStatusStatisticService;
import kl.npki.km.core.service.statistic.ICaBizStatisticService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 首页数据统计，缓存更新任务
 *
 * <AUTHOR>
 */
@Component
public class KmHomePageStatisticJob extends HomePageStatisticJob {

    @Resource
    private KeyStatusStatisticService keyStatusStatisticService;

    @Resource
    private KeyDistributionStatisticService keyDistributionStatisticService;

    @Resource
    private CaKeyStatisticService caKeyStatisticService;

    @Resource
    private ICaBizStatisticService caBizStatisticService;

    @Override
    protected void taskExecute() {
        //1. 更新 密钥状态统计情况缓存
        keyStatusStatisticService.updateCache();
        //2. 更新 密钥库统计情况缓存
        keyDistributionStatisticService.updateCache();
        //3. 更新 CA密钥使用情况
        caKeyStatisticService.updateAllCaKeyStatisticCache();
        //4. 更新CA业务统计情况
        caBizStatisticService.updateAllCaCache();
        super.taskExecute();
    }
}
