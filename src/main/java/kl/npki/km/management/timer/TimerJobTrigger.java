package kl.npki.km.management.timer;

import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.timer.client.ITimerClient;
import kl.nbase.timer.job.TimerJobFactory;
import kl.npki.base.core.biz.check.SelfCheckManager;
import kl.npki.base.service.common.SpringBeanRegister;
import kl.npki.km.core.common.constants.KmJobEnum;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.management.core.biz.check.TimerJobCheckItem;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/16 18:54
 * @Description: 系统启动自动触发定时任务
 */
@Component
public class TimerJobTrigger implements CommandLineRunner {

    @Resource
    private ITimerClient timerClient;

    @Resource
    private TimerJobCheckItem timerJobCheckItem;

    @Override
    public void run(String... args) throws Exception {
        List<String> supportAsymAlgo = KmConfigWrapper.getAsymmetricAndPostQuantumKeyTypesWithoutSM9List();
        // 根据配置动态初始化各个密钥类型定时任务
        supportAsymAlgo.forEach(asymAlgo -> {
            String beanName = KmJobEnum.KEY_GEN.getId() + asymAlgo;
            SpringBeanRegister.registerBean(beanName, KeyGenJob.class, new AsymAlgo(asymAlgo));
            TimerJobFactory.putJob((KeyGenJob) SpringBeanRegister.getBean(beanName));
            timerClient.run(beanName);
        });
        // 刷新定时任务自检项的检测数据
        SelfCheckManager.getInstance().checkItem(timerJobCheckItem.getFullCode());
    }
}
