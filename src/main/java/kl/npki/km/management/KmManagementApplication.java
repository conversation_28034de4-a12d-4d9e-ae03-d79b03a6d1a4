package kl.npki.km.management;

import kl.nbase.exception.context.ExceptionContext;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.service.util.PortPrintUtil;
import kl.npki.km.management.exception.ErrorCode;
import kl.npki.management.core.constants.SysInitConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.ApplicationPidFileWriter;

/**
 * <AUTHOR>
 * Created on 2022/07/27 17:49
 */
@SpringBootApplication(scanBasePackages = BaseConstant.BASE_PACKAGE)
public class KmManagementApplication {

    /**
     * logger
     **/
    private static final Logger logger = LoggerFactory.getLogger(KmManagementApplication.class);

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(KmManagementApplication.class);
        app.addListeners(new ApplicationPidFileWriter(SysInitConstant.APP_PID_FILE));
        app.addInitializers(applicationContext -> ExceptionContext.getInstance().init(ErrorCode.SYSTEM_CODE, ErrorCode.SERVICE_CODE));
        app.run();
        logger.info("\n" +
            " _  __  __  __             _____   ______   _____   __      __  _____    _____   ______ \n" +
            "| |/ / |  \\/  |           / ____| |  ____| |  __ \\  \\ \\    / / |_   _|  / ____| |  ____|\n" +
            "| ' /  | \\  / |  ______  | (___   | |__    | |__) |  \\ \\  / /    | |   | |      | |__   \n" +
            "|  <   | |\\/| | |______|  \\___ \\  |  __|   |  _  /    \\ \\/ /     | |   | |      |  __|  \n" +
            "| . \\  | |  | |           ____) | | |____  | | \\ \\     \\  /     _| |_  | |____  | |____ \n" +
            "|_|\\_\\ |_|  |_|          |_____/  |______| |_|  \\_\\     \\/     |_____|  \\_____| |______|\n");

        PortPrintUtil.logPrint();
        if (SystemUtil.isAccessKcsp()) {
            logger.info("系统已成功接入KCSP平台，现在可以正常使用KCSP平台提供的服务和功能。");
        }
    }

}
