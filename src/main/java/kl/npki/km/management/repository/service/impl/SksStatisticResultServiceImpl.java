package kl.npki.km.management.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.npki.km.management.repository.entity.SksStatisticResultDO;
import kl.npki.km.management.repository.mapper.SksStatisticResultMapper;
import kl.npki.km.management.repository.service.ISksStatisticResultService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class SksStatisticResultServiceImpl extends KlServiceImpl<SksStatisticResultMapper, SksStatisticResultDO> implements ISksStatisticResultService {
    @Override
    public IPage<SksStatisticResultDO> searchSksLogByPage(IPage<SksStatisticResultDO> page, LambdaQueryWrapper<SksStatisticResultDO> lambdaQueryWrapper) {
        return page(page, lambdaQueryWrapper);
    }

    @Override
    public void insertEntity(SksStatisticResultDO sksStatisticResultDO) {
        getBaseMapper().insert(sksStatisticResultDO);
    }

    @Override
    public List<SksStatisticResultDO> countGroupByBizName(List<String> toSearchBizList, LocalDateTime startTime, LocalDateTime endTime) {
        return getBaseMapper().countGroupByBizName(toSearchBizList, startTime, endTime);
    }
}
