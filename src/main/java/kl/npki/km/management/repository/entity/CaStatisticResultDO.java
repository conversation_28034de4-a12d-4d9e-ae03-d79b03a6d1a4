package kl.npki.km.management.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.constant.StatisticDimensionsEnum;
import kl.npki.base.service.repository.entity.BaseDO;
import kl.tools.dbtool.constant.DataType;

import java.time.LocalDateTime;

/**
 * ca服务统计结果表
 *
 * <AUTHOR>
 */
@TableName("t_ca_statistic_result")
@KlDbTable(tbName = "t_ca_statistic_result",
    indexes = {@Index(name = "idx_ca_date_dim", columnList = {"ca_name","statistic_date", "statistic_dimensions"}, unique = true)})
public class CaStatisticResultDO extends BaseDO {

    /**
     * CA名称
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String caName;

    /**
     * 统计所属年月日
     */
    @KlDbField(type = DataType.DATE, size = 6)
    private LocalDateTime statisticDate;

    /**
     * @see StatisticDimensionsEnum
     * 统计维度(按天还是按月)
     */
    @KlDbField(type = DataType.INTEGER)
    private Integer statisticDimensions;

    /**
     * 密钥申请成功次数
     */
    @KlDbField(type = DataType.INTEGER)
    private Integer keyApplySuccess;

    /**
     * 密钥申请失败次数
     */
    @KlDbField(type = DataType.INTEGER)
    private Integer keyApplyFail;

    /**
     * 密钥销毁成功次数
     */
    @KlDbField(type = DataType.INTEGER)
    private Integer keyDestroySuccess;

    /**
     * 密钥销毁失败次数
     */
    @KlDbField(type = DataType.INTEGER)
    private String keyDestroyFail;

    /**
     * 密钥恢复成功次数
     */
    @KlDbField(type = DataType.INTEGER)
    private String keyRestoreSuccess;

    /**
     * 密钥恢复失败次数
     */
    @KlDbField(type = DataType.INTEGER)
    private String keyRestoreFail;

    public String getCaName() {
        return caName;
    }

    public void setCaName(String caName) {
        this.caName = caName;
    }

    public LocalDateTime getStatisticDate() {
        return statisticDate;
    }

    public void setStatisticDate(LocalDateTime statisticDate) {
        this.statisticDate = statisticDate;
    }

    public Integer getStatisticDimensions() {
        return statisticDimensions;
    }

    public void setStatisticDimensions(Integer statisticDimensions) {
        this.statisticDimensions = statisticDimensions;
    }

    public Integer getKeyApplySuccess() {
        return keyApplySuccess;
    }

    public void setKeyApplySuccess(Integer keyApplySuccess) {
        this.keyApplySuccess = keyApplySuccess;
    }

    public Integer getKeyApplyFail() {
        return keyApplyFail;
    }

    public void setKeyApplyFail(Integer keyApplyFail) {
        this.keyApplyFail = keyApplyFail;
    }

    public Integer getKeyDestroySuccess() {
        return keyDestroySuccess;
    }

    public void setKeyDestroySuccess(Integer keyDestroySuccess) {
        this.keyDestroySuccess = keyDestroySuccess;
    }

    public String getKeyDestroyFail() {
        return keyDestroyFail;
    }

    public void setKeyDestroyFail(String keyDestroyFail) {
        this.keyDestroyFail = keyDestroyFail;
    }

    public String getKeyRestoreSuccess() {
        return keyRestoreSuccess;
    }

    public void setKeyRestoreSuccess(String keyRestoreSuccess) {
        this.keyRestoreSuccess = keyRestoreSuccess;
    }

    public String getKeyRestoreFail() {
        return keyRestoreFail;
    }

    public void setKeyRestoreFail(String keyRestoreFail) {
        this.keyRestoreFail = keyRestoreFail;
    }

    @Override
    public String toString() {
        return "CaStatisticResultDO{" +
            "caName='" + caName + '\'' +
            ", statisticDate=" + statisticDate +
            ", statisticDimensions=" + statisticDimensions +
            ", keyApplySuccess=" + keyApplySuccess +
            ", keyApplyFail=" + keyApplyFail +
            ", keyDestroySuccess=" + keyDestroySuccess +
            ", keyDestroyFail='" + keyDestroyFail + '\'' +
            ", keyRestoreSuccess='" + keyRestoreSuccess + '\'' +
            ", keyRestoreFail='" + keyRestoreFail + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
