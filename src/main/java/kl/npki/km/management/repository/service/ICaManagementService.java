package kl.npki.km.management.repository.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.npki.km.management.model.ca.CaListRequest;
import kl.npki.km.management.model.ca.CaDetailResponse;
import kl.npki.km.management.model.ca.CaListResponse;

import java.util.List;

/**
 * @Author: guoq
 * @Date: 2023/4/4
 * @description:
 */
public interface ICaManagementService {

    /**
     * 查询ca列表
     * @param pageInfo
     * @param caListRequest
     * @return
     */
    IPage<CaListResponse> queryCaPageList(PageInfo pageInfo, CaListRequest caListRequest);

    List<CaListResponse> queryAllCa();

    /**
     * 查询ca详情
     * @param caId
     * @return
     */
    CaDetailResponse getCaDetail( Long caId);
}
