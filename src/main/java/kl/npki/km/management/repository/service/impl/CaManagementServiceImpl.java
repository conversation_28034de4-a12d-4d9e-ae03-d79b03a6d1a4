package kl.npki.km.management.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.biz.ssl.SslClientCertEntity;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.km.core.biz.ca.model.CaEntity;
import kl.npki.km.core.common.constants.CaConstants;
import kl.npki.km.core.exception.KmValidationError;
import kl.npki.km.management.model.ca.CaDetailResponse;
import kl.npki.km.management.model.ca.CaListRequest;
import kl.npki.km.management.model.ca.CaListResponse;
import kl.npki.km.management.model.ca.CaResourceResponse;
import kl.npki.km.management.repository.service.ICaManagementService;
import kl.npki.km.management.repository.service.ISSLClientCertService;
import kl.npki.km.service.repository.entity.CaInfoDO;
import kl.npki.km.service.repository.entity.CaResourceDO;
import kl.npki.km.service.repository.service.ICaInfoService;
import kl.npki.km.service.repository.service.ICaResourceService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: guoq
 * @Date: 2023/4/4
 * @description:
 */
@Service
public class CaManagementServiceImpl implements ICaManagementService {
    @Resource
    private ICaInfoService caInfoService;

    @Resource
    private ConvertService convertService;

    @Resource
    private ICaResourceService resourceService;

    @Resource
    private ISSLClientCertService caCertService;

    @Override
    public IPage<CaListResponse> queryCaPageList(PageInfo pageInfo, CaListRequest caListRequest) {
        IPage<CaInfoDO> page = pageInfo.toPage();
        QueryWrapper<CaInfoDO> queryWrapper = caListRequest.toQueryWrapper();
        IPage<CaInfoDO> caInfoPage = caInfoService.page(page, queryWrapper);
        return caInfoPage.convert(this::convertCaListResponse);

    }

    @Override
    public List<CaListResponse> queryAllCa() {
        LambdaQueryWrapper<CaInfoDO> wrapper  = new LambdaQueryWrapper<>();
        // 只查询caId和CaName字段
        wrapper.select(Arrays.asList(CaInfoDO::getId, CaInfoDO::getCaName));
        wrapper.eq(CaInfoDO::getCaStatus, EntityStatus.NORMAL.getId());
        List<CaInfoDO> caList = caInfoService.list(wrapper);
        return convertService.convert(caList, CaListResponse.class);
    }

    private CaListResponse convertCaListResponse(CaInfoDO caInfoDO) {
        // 转换CaDO对象为listResponse
        CaListResponse response = convertService.convert(caInfoDO, CaListResponse.class);
        // 查询CA资源
        response.setResourceList(getResource(caInfoDO.getId()));
        response.setSignClientCert(isExistSslCaCert(caInfoDO.getId()));
        return response;
    }

    /**
     * 获取ca资源
     * @param caId
     * @return
     */
    private List<CaResourceResponse> getResource(Long caId) {
        List<CaResourceDO> caResourceDOList = resourceService.searchCaResources(caId);
        if (CollectionUtils.isNotEmpty(caResourceDOList)) {
            return caResourceDOList.stream().map(caResourceDO -> {
                CaResourceResponse convert = convertService.convert(caResourceDO, CaResourceResponse.class);
                int limitNum = caResourceDO.getLimitNum();
                // 如果是不限制，则设置为无限
                if (limitNum == CaConstants.UNLIMITED_NUMBER) {
                    convert.setSurplusNum(limitNum);
                } else {
                    int keyNum = caResourceDO.getKeyNum();
                    convert.setSurplusNum(Math.max(limitNum - keyNum, 0));
                }
                return convert;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 检查该ca是否已经签发了站点证书
     * @param caId
     * @return
     */
    private Boolean isExistSslCaCert(Long caId) {
        return caCertService.isExistCaCert(caId);
    }

    @Override
    public CaDetailResponse getCaDetail(Long caId) {
        CaInfoDO infoDO = caInfoService.getById(caId);
        CheckUtils.notNull(infoDO, KmValidationError.CA_NOT_EXISTING_ERROR.toException("CA ID = " + caId));
        CaEntity caEntity = convertService.convert(infoDO, CaEntity.class);
        CaDetailResponse caDetailResponse = convertService.convert(caEntity, CaDetailResponse.class);
        caDetailResponse.setResourceList(getResource(caId));
        SslClientCertEntity sslCaCert = caCertService.getSslCaCert(caId);
        if (ObjectUtils.isNotEmpty(sslCaCert)) {
            caDetailResponse.setSslCaCert(sslCaCert.getCertValue());
            caDetailResponse.setSslEncCaCert(sslCaCert.getEncCertValue());
        }
        return caDetailResponse;
    }
}
