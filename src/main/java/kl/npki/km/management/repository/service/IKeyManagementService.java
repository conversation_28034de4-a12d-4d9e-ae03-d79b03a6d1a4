package kl.npki.km.management.repository.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import kl.npki.base.service.common.query.CursorPageInfo;
import kl.npki.km.management.model.key.*;
import kl.npki.km.service.repository.entity.KeyHistoryDO;
import kl.npki.km.service.repository.entity.KeyPoolDO;

/**
 * @Author: guoq
 * @Date: 2023/4/4
 * @description:
 */
public interface IKeyManagementService {

    /**
     * 密钥详情查询
     * @param keyId
     * @return
     */
    KeyCurrentDetailResponse queryCurrentDetail(Long keyId);

    /**
     * 分页查询密钥在用库
     *
     * @param pageInfo
     * @param keyListRequest
     * @param isLawRecovery
     * @return
     */
    IPage<KeyCurrentListResponse> queryCurrentPageList(CursorPageInfo pageInfo, KeyCurrentListRequest keyListRequest, Boolean isLawRecovery);

    /**
     * 分页查询密钥在用库
     *
     * @param pageInfo
     * @param keyArchiveListRequest
     * @return
     */
    IPage<KeyCurrentListResponse> queryArchivePageList(CursorPageInfo pageInfo, KeyArchiveListRequest keyArchiveListRequest);

    /**
     * 密钥详情查询
     *
     * @param keyId
     * @return
     */
    KeyHistoryDetailResponse queryHistoryDetail(Long keyId);

    /**
     * 分页查询密钥历史库
     *
     * @param page
     * @param keyListRequest
     * @return
     */
    IPage<KeyHistoryListResponse> queryHistoryPageList(IPage<KeyHistoryDO> page, KeyHistoryListRequest keyListRequest, Boolean isLawRecovery);

    /**
     * 分页查询密钥备用库
     *
     * @param page
     * @param keyPoolListRequest
     * @return
     */
    IPage<KeyPoolListResponse> queryPoolPageList(IPage<KeyPoolDO> page, KeyPoolListRequest keyPoolListRequest);
}
