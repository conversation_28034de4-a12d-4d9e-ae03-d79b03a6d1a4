package kl.npki.km.management.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.tools.dbtool.constant.DataType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * SKS服务统计结果表
 *
 * <AUTHOR>
 */
@TableName("T_SKS_STATISTIC_RESULT")
@KlDbTable(tbName = "T_SKS_STATISTIC_RESULT")
public class SksStatisticResultDO implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @KlDbField(type = DataType.BIGINT, size = 20, isPrimary = true)
    private Long id;

    /**
     * 业务名称
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String bizName;

    /**
     * 统计所属年月日
     */
    @KlDbField(type = DataType.DATE)
    private LocalDateTime statisticDate;

    /**
     * 统计维度(按天还是按月)
     */
    @KlDbField(type = DataType.INTEGER)
    private Integer statisticDimensions;

    /**
     * 成功次数
     */
    @KlDbField(type = DataType.INTEGER)
    private Integer successTimes;

    /**
     * 失败次数
     */
    @KlDbField(type = DataType.INTEGER)
    private Integer failTimes;

    /**
     * 创建时间(统计时间)
     */
    @KlDbField(type = DataType.DATE)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizName() {
        return bizName;
    }

    public void setBizName(String bizName) {
        this.bizName = bizName;
    }

    public LocalDateTime getStatisticDate() {
        return statisticDate;
    }

    public void setStatisticDate(LocalDateTime statisticDate) {
        this.statisticDate = statisticDate;
    }

    public Integer getStatisticDimensions() {
        return statisticDimensions;
    }

    public void setStatisticDimensions(Integer statisticDimensions) {
        this.statisticDimensions = statisticDimensions;
    }

    public Integer getSuccessTimes() {
        return successTimes;
    }

    public void setSuccessTimes(Integer successTimes) {
        this.successTimes = successTimes;
    }

    public Integer getFailTimes() {
        return failTimes;
    }

    public void setFailTimes(Integer failTimes) {
        this.failTimes = failTimes;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "SksStatisticResultDO{" +
            "id=" + id +
            ", bizName='" + bizName + '\'' +
            ", statisticDate=" + statisticDate +
            ", statisticDimensions=" + statisticDimensions +
            ", successTimes=" + successTimes +
            ", failTimes=" + failTimes +
            ", createTime=" + createTime +
            '}';
    }
}
