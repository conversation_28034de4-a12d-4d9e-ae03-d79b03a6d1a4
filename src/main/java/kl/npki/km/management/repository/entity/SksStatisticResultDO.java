package kl.npki.km.management.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.service.repository.entity.BaseDO;
import kl.tools.dbtool.constant.DataType;

import java.time.LocalDateTime;

/**
 * SKS服务统计结果表
 *
 * <AUTHOR>
 */
@TableName("T_SKS_STATISTIC_RESULT")
@KlDbTable(tbName = "T_SKS_STATISTIC_RESULT")
public class SksStatisticResultDO extends BaseDO {

    /**
     * 业务名称
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String bizName;

    /**
     * 统计所属年月日
     */
    @KlDbField(type = DataType.DATE, size = 6)
    private LocalDateTime statisticDate;

    /**
     * 统计维度(按天还是按月)
     */
    @KlDbField(type = DataType.INTEGER)
    private Integer statisticDimensions;

    /**
     * 成功次数
     */
    @KlDbField(type = DataType.INTEGER)
    private Integer successTimes;

    /**
     * 失败次数
     */
    @KlDbField(type = DataType.INTEGER)
    private Integer failTimes;

    public String getBizName() {
        return bizName;
    }

    public void setBizName(String bizName) {
        this.bizName = bizName;
    }

    public LocalDateTime getStatisticDate() {
        return statisticDate;
    }

    public void setStatisticDate(LocalDateTime statisticDate) {
        this.statisticDate = statisticDate;
    }

    public Integer getStatisticDimensions() {
        return statisticDimensions;
    }

    public void setStatisticDimensions(Integer statisticDimensions) {
        this.statisticDimensions = statisticDimensions;
    }

    public Integer getSuccessTimes() {
        return successTimes;
    }

    public void setSuccessTimes(Integer successTimes) {
        this.successTimes = successTimes;
    }

    public Integer getFailTimes() {
        return failTimes;
    }

    public void setFailTimes(Integer failTimes) {
        this.failTimes = failTimes;
    }

    @Override
    public String toString() {
        return "SksStatisticResultDO{" +
            "bizName='" + bizName + '\'' +
            ", statisticDate=" + statisticDate +
            ", statisticDimensions=" + statisticDimensions +
            ", successTimes=" + successTimes +
            ", failTimes=" + failTimes +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
