package kl.npki.km.management.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.npki.base.core.constant.StatisticDimensionsEnum;
import kl.npki.km.core.biz.statistic.CaStatisticResultEntity;
import kl.npki.km.core.repository.ICaStatisticResultRepository;
import kl.npki.km.management.repository.entity.CaStatisticResultDO;
import kl.npki.km.management.repository.service.ICaStatisticResultService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class CaStatisticResultRepositoryImpl implements ICaStatisticResultRepository {

    @Resource
    private ICaStatisticResultService caStatisticResultService;

    @Resource
    private ConvertService convertService;

    @Override
    public Optional<CaStatisticResultEntity> searchLatestResultByDay(String caName) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setCurrentPage(1);
        pageInfo.setPageSize(1);

        LambdaQueryWrapper<CaStatisticResultDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CaStatisticResultDO::getCaName, caName)
            .eq(CaStatisticResultDO::getStatisticDimensions, StatisticDimensionsEnum.BY_DAY.getCode())
            .orderByDesc(CaStatisticResultDO::getStatisticDate);

        IPage<CaStatisticResultDO> pageData = caStatisticResultService.searchCaLogByPage(pageInfo.toPage(), queryWrapper);
        List<CaStatisticResultDO> records = pageData.getRecords();

        if (CollectionUtils.isEmpty(records)) {
            return Optional.empty();
        }

        CaStatisticResultDO caStatisticResultDO = records.get(0);
        CaStatisticResultEntity entity = convertService.convert(caStatisticResultDO, CaStatisticResultEntity.class);
        return Optional.of(entity);
    }

    @Override
    public void insert(CaStatisticResultEntity caStatisticResultEntity) {
        CaStatisticResultDO resultDO = convertService.convert(caStatisticResultEntity, CaStatisticResultDO.class);
        caStatisticResultService.insert(resultDO);
    }

    @Override
    public List<CaStatisticResultEntity> countGroupByCaName(List<String> toSearchCaList, LocalDateTime startTime, LocalDateTime endTime) {
        List<CaStatisticResultDO> list = caStatisticResultService.countGroupByCaName(toSearchCaList, startTime, endTime);
        return convertService.convert(list, CaStatisticResultEntity.class);
    }

    @Override
    public Optional<CaStatisticResultEntity> searchMonthDataByCaName(String caName, LocalDateTime firstDayOfMonth) {
        CaStatisticResultDO caStatisticResultDO = caStatisticResultService.searchMonthDataByCaName(caName, firstDayOfMonth);

        if (ObjectUtils.isEmpty(caStatisticResultDO)) {
            return Optional.empty();
        }
        CaStatisticResultEntity entity = convertService.convert(caStatisticResultDO, CaStatisticResultEntity.class);

        return Optional.of(entity);
    }
}
