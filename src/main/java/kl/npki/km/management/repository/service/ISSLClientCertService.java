package kl.npki.km.management.repository.service;

import kl.npki.base.core.biz.ssl.SslClientCertEntity;
import kl.npki.km.management.repository.entity.SSLCertDO;

/**
 * 站点证书service接口
 *
 * <AUTHOR>
 * @date 2022/12/21
 */
public interface ISSLClientCertService {

    /**
     * 签发站点证书 导入站点证书请求直接对其进行签发，不用审核后在对其进行签发
     * @param sslCertDO
     */
    Long saveSSLCaCert(SSLCertDO sslCertDO);

    /**
     * 通过子系统ID更新站点证书
     *
     * @param sslCertDO
     */
    boolean updateSSLCaCertBySysId(SSLCertDO sslCertDO);

    /**
     * 查询ca站点证书的值
     * @param caId
     * @return
     */
    SslClientCertEntity getSslCaCert(Long caId);

    /**
     * 判断是否签发了ca站点证书
     * @param caId
     * @return
     */
    Boolean isExistCaCert(Long caId);

    /**
     * 废除ca站点证书
     * @param id
     */
    void revokeSiteCert(Long id);

    /**
     * 根据子系统ID废除该子系统下所有站点证书
     * @param sysId
     */
    void revokeSiteCertBySysId(Long sysId);

}
