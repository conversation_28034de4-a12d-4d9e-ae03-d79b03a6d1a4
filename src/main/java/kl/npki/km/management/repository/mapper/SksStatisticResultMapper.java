package kl.npki.km.management.repository.mapper;

import kl.nbase.db.support.mybatis.mapper.KlBaseMapper;
import kl.npki.km.management.repository.entity.SksStatisticResultDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface SksStatisticResultMapper extends KlBaseMapper<SksStatisticResultDO> {
    /**
     * 统计结果
     *
     * @param bizNames
     * @param startTime
     * @param endTime
     * @return
     */
    List<SksStatisticResultDO> countGroupByBizName(@Param("bizNames") List<String> bizNames, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
