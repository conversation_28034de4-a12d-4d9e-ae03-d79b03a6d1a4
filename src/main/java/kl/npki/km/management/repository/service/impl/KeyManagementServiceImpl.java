package kl.npki.km.management.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.base.service.common.query.CursorPageHelper;
import kl.npki.base.service.common.query.CursorPageInfo;
import kl.npki.km.core.biz.key.model.CurrentKeyEntity;
import kl.npki.km.core.common.constants.SksKey;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.management.exception.KmManagementValidationError;
import kl.npki.km.management.model.key.*;
import kl.npki.km.management.repository.service.IKeyManagementService;
import kl.npki.km.service.repository.KeyCurrentRepositoryImpl;
import kl.npki.km.service.repository.entity.CaInfoDO;
import kl.npki.km.service.repository.entity.KeyCurrentDO;
import kl.npki.km.service.repository.entity.KeyHistoryDO;
import kl.npki.km.service.repository.entity.KeyPoolDO;
import kl.npki.km.service.repository.service.ICaInfoService;
import kl.npki.km.service.repository.service.IKeyCurrentService;
import kl.npki.km.service.repository.service.IKeyHistoryService;
import kl.npki.km.service.repository.service.IKeyPoolService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Service
public class KeyManagementServiceImpl implements IKeyManagementService {

    @Resource
    private ConvertService convertService;

    @Resource
    private IKeyCurrentService keyCurrentService;

    @Resource
    private KeyCurrentRepositoryImpl keyCurrentRepository;

    @Resource
    private IKeyHistoryService keyHistoryService;

    @Resource
    private IKeyPoolService keyPoolService;

    @Resource
    private ICaInfoService caInfoService;

    @Override
    public KeyCurrentDetailResponse queryCurrentDetail(Long keyId) {
        CurrentKeyEntity currentKeyEntity = keyCurrentRepository.searchKeyById(keyId);
        CheckUtils.notNull(currentKeyEntity, KmManagementValidationError.CURRENT_KEY_NOT_FOUND_ERROR
            .toException(String.format("Current key data keyId = 【%s】 not found！", keyId)));
        KeyCurrentDetailResponse detailResponse = convertService.convert(currentKeyEntity, KeyCurrentDetailResponse.class);
        detailResponse.setCaName(searchCaNameById(detailResponse.getCaId()));
        if (SksKey.SKS.getName().equals(currentKeyEntity.getKeyType())) {
            detailResponse.setKeyIndex(currentKeyEntity.getEncCertSn());
        }
        detailResponse.setDecimalEncCertSn(convertEncCertSnToDecimal(detailResponse.getKeyType(), detailResponse.getEncCertSn()));
        return detailResponse;
    }

    @Override
    public IPage<KeyCurrentListResponse> queryCurrentPageList(CursorPageInfo pageInfo,
                                                              KeyCurrentListRequest keyListRequest,
                                                              Boolean isLawRecovery) {
        QueryWrapper<KeyCurrentDO> queryWrapper = keyListRequest.toQueryWrapper();
        queryWrapper.lambda()
            .in(Boolean.TRUE.equals(isLawRecovery) && keyListRequest.getKeyType() == null, KeyCurrentDO::getKeyType, KmConfigWrapper.getAsymmetricAndPostQuantumKeyTypesWithoutSM9Set());
        return doQueryCurrentPageList(pageInfo, queryWrapper, isLawRecovery);
    }

    private IPage<KeyCurrentListResponse> doQueryCurrentPageList(CursorPageInfo pageInfo,
                                                                 QueryWrapper<KeyCurrentDO> queryWrapper,
                                                                 Boolean isLawRecovery) {
        // 添加游标条件
        CursorPageHelper.applyCursorCondition(queryWrapper, pageInfo, KeyCurrentDO::getId);
        // 查询指定字段
        if (Boolean.TRUE.equals(isLawRecovery)) {
            queryWrapper.lambda()
                .select(KeyCurrentDO::getId, KeyCurrentDO::getEncCertSn, KeyCurrentDO::getSubject,
                    KeyCurrentDO::getValidStart, KeyCurrentDO::getValidEnd, KeyCurrentDO::getKeyType);
        } else {
            queryWrapper.lambda()
                .select(KeyCurrentDO::getId, KeyCurrentDO::getEncCertSn, KeyCurrentDO::getKeyType, KeyCurrentDO::getCaId,
                    KeyCurrentDO::getSubject, KeyCurrentDO::getValidStart, KeyCurrentDO::getValidEnd, KeyCurrentDO::getKeyStatus,
                    KeyCurrentDO::getCreateTime);
        }

        // 查询分页数据
        IPage<KeyCurrentDO> keyCurrentPage = keyCurrentService.page(pageInfo.toPage(), queryWrapper);
        // 收集所有的 caId
        Set<Long> caIds = keyCurrentPage.getRecords().stream()
            .map(KeyCurrentDO::getCaId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        // 批量查询 CA 信息
        Map<Long, String> caNameMap = caInfoService.batchGetCaNames(caIds);
        // 转换填充
        IPage<KeyCurrentListResponse> currentListResponsePage = keyCurrentPage.convert(k -> {
            KeyCurrentListResponse response = convertService.convert(k, KeyCurrentListResponse.class);
            response.setCaName(caNameMap.get(k.getCaId()));
            return response;
        });
        // 封装分页结果
        return CursorPageHelper.buildPagedResult(currentListResponsePage, pageInfo, KeyCurrentListResponse::getId);
    }

    @Override
    public IPage<KeyCurrentListResponse> queryArchivePageList(CursorPageInfo cursorPageInfo, KeyArchiveListRequest keyArchiveListRequest) {
        QueryWrapper<KeyCurrentDO> queryWrapper = keyArchiveListRequest.toQueryWrapper();
        queryWrapper.lambda().in(KeyCurrentDO::getKeyStatus,
            Arrays.asList(EntityStatus.REVOKED.getId(), EntityStatus.EXPIRED.getId()));
        return doQueryCurrentPageList(cursorPageInfo, queryWrapper, false);
    }

    private String searchCaNameById(Long caId) {
        if (ObjectUtils.isEmpty(caId)) {
            return null;
        }
        CaInfoDO caInfoDO = caInfoService.getById(caId);
        if (ObjectUtils.isEmpty(caInfoDO)) {
            return null;
        }
        return caInfoDO.getCaName();
    }


    @Override
    public KeyHistoryDetailResponse queryHistoryDetail(Long keyId) {
        KeyHistoryDO keyHistoryDO = keyHistoryService.getById(keyId);
        CheckUtils.notNull(keyHistoryDO, KmManagementValidationError.HISTORY_KEY_NOT_FOUND_ERROR
            .toException(String.format("history key data keyId = 【%s】 not found！", keyId)));
        KeyHistoryDetailResponse detailResponse = convertService.convert(keyHistoryDO, KeyHistoryDetailResponse.class);
        detailResponse.setCaName(searchCaNameById(detailResponse.getCaId()));
        detailResponse.setDecimalEncCertSn(convertEncCertSnToDecimal(detailResponse.getKeyType(), detailResponse.getEncCertSn()));
        return detailResponse;
    }

    @Override
    public IPage<KeyHistoryListResponse> queryHistoryPageList(IPage<KeyHistoryDO> page,
                                                              KeyHistoryListRequest keyListRequest,
                                                              Boolean isLawRecovery) {
        QueryWrapper<KeyHistoryDO> queryWrapper = keyListRequest.toQueryWrapper();
        queryWrapper.lambda()
            .ne(Boolean.TRUE.equals(isLawRecovery), KeyHistoryDO::getKeyType, SksKey.SKS.getName())
            .orderByDesc(KeyHistoryDO::getCreateTime);
        IPage<KeyHistoryDO> keyHistoryPage = keyHistoryService.page(page, queryWrapper);
        // 收集所有的 caId
        Set<Long> caIds = keyHistoryPage.getRecords().stream()
            .map(KeyHistoryDO::getCaId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        // 批量查询 CA 信息
        Map<Long, String> caNameMap = caInfoService.batchGetCaNames(caIds);
        // 转换并填充 CA 名称
        return keyHistoryPage.convert(k -> {
            KeyHistoryListResponse response = convertService.convert(k, KeyHistoryListResponse.class);
            response.setCaName(caNameMap.get(k.getCaId()));
            return response;
        });
    }

    @Override
    public IPage<KeyPoolListResponse> queryPoolPageList(IPage<KeyPoolDO> page, KeyPoolListRequest keyPoolListRequest) {
        QueryWrapper<KeyPoolDO> queryWrapper = keyPoolListRequest.toQueryWrapper();
        IPage<KeyPoolDO> keyPoolDOPage = keyPoolService.page(page, queryWrapper);
        return keyPoolDOPage.convert(k -> convertService.convert(k, KeyPoolListResponse.class));
    }

    /**
     * 当密钥类型不是SKS时，将16进制加密证书序列号转为10进制
     */
    private String convertEncCertSnToDecimal(String keyType, String encCertSn) {
        String decimalEncCertSn = null;
        if (!SksKey.SKS.getName().equals(keyType)) {
            decimalEncCertSn = new BigInteger(encCertSn, 16).toString();
        }
        return decimalEncCertSn;
    }
}
