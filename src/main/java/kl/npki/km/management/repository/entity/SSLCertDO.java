package kl.npki.km.management.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.service.repository.entity.BaseDO;
import kl.tools.dbtool.constant.DataType;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-12-27
 */
@TableName("t_ssl_cert")
@KlDbTable(tbName = "t_ssl_cert")
public class SSLCertDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @KlDbField(type = DataType.BIGINT, size = 20)
    private Long sysId;

    /**
     * 签名证书序列号
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String sn;

    /**
     * 加密证书序列号
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String encSn;

    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String cn;

    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String dn;

    /**
     * 签名证书值
     */
    @KlDbField(type = DataType.CLOB)
    private String certValue;

    /**
     * 加密证书值
     */
    @KlDbField(type = DataType.CLOB)
    private String encCertValue;
    /**
     * 站点加密证书的私钥(数字信封格式)
     */
    @KlDbField(type = DataType.CLOB)
    private String envelopedPriKeyData;

    @KlDbField(type = DataType.DATE, size = 6)
    private LocalDateTime notBefore;

    @KlDbField(type = DataType.DATE, size = 6)
    private LocalDateTime notAfter;

    @KlDbField(type = DataType.TINYINT, size = 1)
    private Integer status;

    /**
     * 扩展项1
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String ext1;

    /**
     * 扩展项2
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String ext2;

    /**
     * 扩展项3
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String ext3;

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getCn() {
        return cn;
    }

    public void setCn(String cn) {
        this.cn = cn;
    }

    public String getEncSn() {
        return encSn;
    }

    public SSLCertDO setEncSn(String encSn) {
        this.encSn = encSn;
        return this;
    }

    public String getEncCertValue() {
        return encCertValue;
    }

    public SSLCertDO setEncCertValue(String encCertValue) {
        this.encCertValue = encCertValue;
        return this;
    }

    public String getEnvelopedPriKeyData() {
        return envelopedPriKeyData;
    }

    public SSLCertDO setEnvelopedPriKeyData(String envelopedPriKeyData) {
        this.envelopedPriKeyData = envelopedPriKeyData;
        return this;
    }

    public String getDn() {
        return dn;
    }

    public void setDn(String dn) {
        this.dn = dn;
    }

    public String getCertValue() {
        return certValue;
    }

    public void setCertValue(String certValue) {
        this.certValue = certValue;
    }

    public LocalDateTime getNotBefore() {
        return notBefore;
    }

    public void setNotBefore(LocalDateTime notBefore) {
        this.notBefore = notBefore;
    }

    public LocalDateTime getNotAfter() {
        return notAfter;
    }

    public void setNotAfter(LocalDateTime notAfter) {
        this.notAfter = notAfter;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Long getSysId() {
        return sysId;
    }

    public void setSysId(Long sysId) {
        this.sysId = sysId;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    @Override
    public String toString() {
        return "SSLCertDO{" +
            "sysId=" + sysId +
            ", sn='" + sn + '\'' +
            ", encSn='" + encSn + '\'' +
            ", cn='" + cn + '\'' +
            ", dn='" + dn + '\'' +
            ", certValue='" + certValue + '\'' +
            ", encCertValue='" + encCertValue + '\'' +
            ", envelopedPriKeyData='" + envelopedPriKeyData + '\'' +
            ", notBefore=" + notBefore +
            ", notAfter=" + notAfter +
            ", status=" + status +
            ", ext1='" + ext1 + '\'' +
            ", ext2='" + ext2 + '\'' +
            ", ext3='" + ext3 + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
