package kl.npki.km.management.constant;

import kl.nbase.i18n.i18n.I18nUtil;

/**
 * <AUTHOR> on 2025/01/17
 **/
public class KMMgmtI18N {


    public static final String KM_MGR_I18N_MESSAGE_KEY_PREFIX = "kl.npki.km.management.i18n" + I18nUtil.SEPARATOR;

    // region operation

    // region kl.npki.km.management.controller.home.HomePageController
    public static final String KEY_STATUS_DATA_STATS = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "key_status_data_stats";
    public static final String KEY_VAULT_DATA_STATS = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "key_vault_data_stats";
    public static final String KEY_USAGE_DATA_STATS = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "key_usage_data_stats";
    public static final String CA_KEY_SERVICE_INVOCATION_STATS = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "ca_key_service_invocation_stats";
    // endregion

    // region kl.npki.km.management.controller.statistic.CaBizStatisticController
    public static final String CA_SERVICE_STATISTICAL_REPORT = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "ca_service_statistical_report";
    public static final String EXPORTED_CA_SERVICE_REPORT = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "exported_ca_service_report";
    // endregion

    // region kl.npki.km.management.controller.statistic.KeyStatisticController
    public static final String KEY_VAULT_STATS = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "key_vault_stats";
    public static final String EXPORTED_KEY_VAULT_STATISTICAL_REPORT = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "exported_key_vault_statistical_report";
    public static final String TOTAL_KEY_USAGE_COUNT = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "total_key_usage_count";
    public static final String EXPORTED_KEY_USAGE_STAT_REPORT = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "exported_key_usage_stat_report";
    public static final String KEY_SERVICE_STATS_NOT_IMPLEMENTED = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "key_service_stats_not_implemented";
    public static final String KEY_LOG_ANALYSIS_STATS_NOT_IMPLEMENTED = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "key_log_analysis_stats_not_implemented";
    // endregion

    // region kl.npki.km.management.controller.statistic.SksBizStatisticController
    public static final String SKS_SERVICE_STAT_REPORT = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "sks_service_stat_report";
    public static final String EXPORTED_SKS_SERVICE_STAT_REPORT = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "exported_sks_service_stat_report";
    // endregion

    // region kl.npki.km.management.controller.CaManagementController
    public static final String CA_INFO_LIST_QUERY = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "ca_info_list_query";
    public static final String QUERY_ALL_CAS = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "query_all_cas";
    public static final String QUERY_CA_INFO_DETAILS = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "query_ca_info_details";
    public static final String CA_ACCESS_SERVICE = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "ca_access_service";
    public static final String CA_RESOURCE_ADDITION = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "ca_resource_addition";
    public static final String CA_UPDATE = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "ca_update";
    public static final String CA_FREEZE = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "ca_freeze";
    public static final String CA_UNFREEZE = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "ca_unfreeze";
    public static final String CA_REVOKE = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "ca_revoke";
    public static final String CA_RECOVERY = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "ca_recovery";
    public static final String CA_VERSION_LIST = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "ca_version_list";
    public static final String CA_KEY_RESPONSE_STANDARD_VERSION_LIST = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "ca_key_response_standard_version_list";
    // endregion

    // region kl.npki.km.management.controller.ConfigController
    public static final String GET_ARCHIVE_KEY_CONFIG = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "get_archive_key_config";
    public static final String UPDATE_ARCHIVE_KEY_CONF = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "update_archive_key_conf";
    public static final String TRIGGER_KEY_ARCHIVE_JOB = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "trigger_key_archive_job";
    public static final String GET_KEY_CONF = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "get_key_conf";
    public static final String UPDATE_KEY_CONF = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "update_key_conf";
    public static final String GET_KM_CONF = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "get_km_conf";
    public static final String UPDATE_KM_CONF = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "update_km_conf";
    public static final String KEY_TYPE_LIST = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "key_type_list";
    // endregion

    // region kl.npki.km.management.controller.KeyCurrentController
    public static final String CURRENT_KEY_LIST = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "current_key_list";
    public static final String ARCHIVE_KEY_LIST = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "archive_key_list";
    public static final String ARCHIVE_KEY = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "archive_key";
    public static final String QUERY_CURRENT_KEY_DETAIL = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "query_current_key_detail";
    // endregion

    // region kl.npki.km.management.controller.KeyHistoryController
    public static final String HIS_ADMIN_LIST = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "his_admin_list";
    public static final String HIS_QUERY_CURRENT_KEY_DETAIL = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "his_query_current_key_detail";
    // endregion

    // region kl.npki.km.management.controller.KeyPoolController
    public static final String BAK_CURRENT_KEY_LIST = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "bak_current_key_list";
    // endregion

    // region kl.npki.km.management.controller.KeyTraceController
    public static final String QUERY_KEY_TRACE_DETAIL = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "query_key_trace_detail";
    // endregion

    // region kl.npki.km.management.controller.KmAdminMgrController
    public static final String INIT_ADMIN_CERT = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "init_admin_cert";
    public static final String BACKUP_ADMIN_CERT = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "backup_admin_cert";
    public static final String ISSUE_ADMIN_CERT = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "issue_admin_cert";
    public static final String POSTPONE_ADMIN_CERT = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "postpone_admin_cert";
    public static final String QUERY_LAW_RECOVER_ADMIN_LIST = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "query_law_recover_admin_list";
    // endregion

    // region kl.npki.km.management.controller.LawRecoverController
    public static final String LAW_RECOVER = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "law_recover";
    // endregion

    // region kl.npki.km.management.controller.SslClientCertController
    public static final String SIGN_CA_SSL_CERT = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "sign_ca_ssl_cert";
    public static final String UPDATE_CA_SSL_CERT = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "update_ca_ssl_cert";
    public static final String EXPORT_CA_SSL_CERT = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "export_ca_ssl_cert";
    // endregion

    // region kl.npki.km.management.controller.ConfigController
    public static final String ENGINE_NOT_SUPPORT_SM9 = KM_MGR_I18N_MESSAGE_KEY_PREFIX + "engine_not_support_sm9";
    // endregion

    // region kl.npki.km.management.check.KmServiceCheckItem
    /**
     * KM服务运行正常
     */
    public static final String KM_SERVICE_NORMAL_I18N_KEY =  "km_service_normal_i18n_key";
    /**
     * KM服务运行异常
     */
    public static final String KM_SERVICE_ABNORMAL_I18N_KEY =  "km_service_abnormal_i18n_key";
    /**
     * 正常
     */
    public static final String KM_SERVICE_CHECK_SUCCESS_I18N_KEY =  "km_service_check_success_i18n_key";
    /**
     * 错误信息
     */
    public static final String KM_SERVICE_CHECK_ERROR_MESSAGE_I18N_KEY =  "km_service_check_error_message_i18n_key";
    /**
     * 堆栈跟踪
     */
    public static final String KM_SERVICE_CHECK_STACK_TRACE_I18N_KEY =  "km_service_check_stack_trace_i18n_key";
    // endregion


    // region kl.npki.km.management.check.SksServiceCheckItem
    /**
     * 所有SKS服务接口正常
     */
    public static final String ALL_SKS_API_NORMAL_I18N_KEY = "all_sks_api_normal_i18n_key";
    /**
     * 部分SKS服务接口异常
     */
    public static final String SOME_SKS_API_ABNORMAL_I18N_KEY = "some_sks_api_abnormal_i18n_key";
    /**
     * SKS获取随机数接口
     */
    public static final String SKS_GET_RANDOM_NUMBER_API_I18N_KEY = "sks_get_random_number_api_i18n_key";
    /**
     * SKS创建密钥接口
     */
    public static final String SKS_CREATE_PRIVATE_KEY_API_I18N_KEY = "sks_create_private_key_api_i18n_key";
    /**
     * SKS获取密钥接口
     */
    public static final String SKS_GET_PRIVATE_KEY_API_I18N_KEY = "sks_get_private_key_api_i18n_key";
    /**
     * SKS更新密钥状态接口
     */
    public static final String SKS_UPDATE_PRIVATE_KEY_API_I18N_KEY = "sks_update_private_key_api_i18n_key";
    /**
     * SKS批量获取密钥接口
     */
    public static final String SKS_BATCH_GET_PRIVATE_KEY_API_I18N_KEY = "sks_batch_get_private_key_api_i18n_key";
    /**
     * SKS删除密钥接口
     */
    public static final String SKS_REMOVE_PRIVATE_KEY_API_I18N_KEY = "sks_remove_private_key_api_i18n_key";
    /**
     * SKS接口检测成功
     */
    public static final String SKS_API_CHECK_SUCCESS_I18N_KEY = "sks_api_check_success_i18n_key";
    /**
     * SKS接口检测失败
     */
    public static final String SKS_API_CHECK_FAILURE_I18N_KEY = "sks_api_check_failure_i18n_key";
    // endregion

    /**
     * 获取国际化后的信息
     *
     * @param key  国际化资源的键（不包含资源路径）
     * @param args 占位填充参数
     * @return 国际化结果
     */
    public static String getKmMgmtI18nMessage(String key, Object... args) {
        return I18nUtil.tr(KM_MGR_I18N_MESSAGE_KEY_PREFIX + key, args);
    }

}
