package kl.npki.km.management.model.key;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.time.LocalDateTime;

/**
 * 密钥历史轨迹响应
 * <AUTHOR>
 */
public class KeyTraceResponse {
    /**
     * 操作者名称
     */
    @Schema(description =  "操作者名称")
    private String operatorName;

    /**
     * 密钥类型
     */
    @Schema(description =  "密钥类型")
    private String keyType;

    /**
     * 发起者IP
     */
    @Schema(description =  "发起者IP")
    private String sourceIp;

    /**
     * 调用方式
     */
    @Schema(description =  "调用方式")
    private String invocationType;

    /**
     * 创建时间
     */
    @Schema(description =  "轨迹创建时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 变更事件/动作/业务类型
     */
    @Schema(description =  "变更事件/动作/业务类型")
    private String bizName;

    /**
     * 业务流水号
     */
    @Schema(description =  "业务流水号")
    private String bizId;

    /**
     * json格式的实体数据内容
     */
    @Schema(description =  "json格式的实体数据内容")
    private String entityInfo;

    /**
     * 操作结果
     */
    @Schema(description =  "操作结果")
    private String operatorResult;

    /**
     * 实体状态
     */
    @Schema(description =  "实体状态, 0 正常、-1 已冻结、-2 已废除、-3 已过期、 -4 未知")
    private int entityStatus;

    public String getOperatorName() {
        return operatorName;
    }

    public KeyTraceResponse setOperatorName(String operatorName) {
        this.operatorName = operatorName;
        return this;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public String getSourceIp() {
        return sourceIp;
    }

    public KeyTraceResponse setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
        return this;
    }

    public String getInvocationType() {
        return invocationType;
    }

    public KeyTraceResponse setInvocationType(String invocationType) {
        this.invocationType = invocationType;
        return this;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public KeyTraceResponse setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getBizName() {
        return bizName;
    }

    public KeyTraceResponse setBizName(String bizName) {
        this.bizName = bizName;
        return this;
    }

    public String getBizId() {
        return bizId;
    }

    public KeyTraceResponse setBizId(String bizId) {
        this.bizId = bizId;
        return this;
    }

    public String getEntityInfo() {
        return entityInfo;
    }

    public KeyTraceResponse setEntityInfo(String entityInfo) {
        this.entityInfo = entityInfo;
        return this;
    }

    public String getOperatorResult() {
        return operatorResult;
    }

    public KeyTraceResponse setOperatorResult(String operatorResult) {
        this.operatorResult = operatorResult;
        return this;
    }

    public int getEntityStatus() {
        return entityStatus;
    }

    public KeyTraceResponse setEntityStatus(int entityStatus) {
        this.entityStatus = entityStatus;
        return this;
    }
}
