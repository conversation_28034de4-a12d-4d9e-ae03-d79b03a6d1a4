package kl.npki.km.management.model.ca;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.util.encoders.Base64;
import kl.nbase.security.utils.PEMUtil;
import kl.npki.base.core.biz.cert.model.parser.x509.CertificateInfo;
import kl.npki.base.core.biz.cert.parser.CertificateParserFactory;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;
import kl.npki.base.management.exception.ManagementInternalError;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/17 11:45
 * @Description:
 */
public class CaDetailResponse {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "CA标识")
    private Long caId;

    @Schema(description = "CA名称")
    private String caName;

    @Schema(description = "CA版本")
    private String caVersion;

    @Schema(description = "SM2密钥类型时使用的标准规范版本")
    private String sm2KeyStandardVersion;

    @Schema(description = "CA接入方式")
    private String caAccessType;

    @Schema(description = "CA身份证书")
    private String idCert;

    @Schema(description = "CA身份证书是否可信")
    private Boolean idCertTrusted;

    @Schema(description = "CA SSL客户端签名证书")
    private String sslCaCert;

    @Schema(description = "CA SSL客户端加密证书")
    private String sslEncCaCert;

    /**
     * 状态
     */
    @Schema(description = "CA状态， 0 正常、1 废除、2 冻结")
    private Integer caStatus;

    @Schema(description = "密钥默认有效期")
    private int limitYear;

    /**
     * 状态
     */
    @Schema(description = "介质管理， 启用：1 禁用：0")
    private Integer useMediumSn;

    @Schema(description = "创建时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    @Schema(description = "CA资源列表")
    private List<CaResourceResponse> resourceList;

    @Schema(description = "证书信息")
    private Map<String, CertificateInfo> certInfosMap = new HashMap();

    @Schema(description = "备注")
    private String remark;

    public Long getCaId() {
        return caId;
    }

    public void setCaId(Long caId) {
        this.caId = caId;
    }

    public String getCaName() {
        return caName;
    }

    public void setCaName(String caName) {
        this.caName = caName;
    }

    public String getCaVersion() {
        return caVersion;
    }

    public void setCaVersion(String caVersion) {
        this.caVersion = caVersion;
    }

    public String getCaAccessType() {
        return caAccessType;
    }

    public void setCaAccessType(String caAccessType) {
        this.caAccessType = caAccessType;
    }

    public String getIdCert() {
        return idCert;
    }

    public void setIdCert(String idCert) {
        if (StringUtils.isBlank(idCert)) {
            return;
        }
        this.idCert = idCert;
        dealCertificateInfosMap("idCert", idCert);
    }

    public Integer getCaStatus() {
        return caStatus;
    }

    public String getSm2KeyStandardVersion() {
        return sm2KeyStandardVersion;
    }

    public void setSm2KeyStandardVersion(String sm2KeyStandardVersion) {
        this.sm2KeyStandardVersion = sm2KeyStandardVersion;
    }

    public void setCaStatus(Integer caStatus) {
        this.caStatus = caStatus;
    }

    public int getLimitYear() {
        return limitYear;
    }

    public void setLimitYear(int limitYear) {
        this.limitYear = limitYear;
    }

    public Integer getUseMediumSn() {
        return useMediumSn;
    }

    public void setUseMediumSn(Integer useMediumSn) {
        this.useMediumSn = useMediumSn;
    }

    public List<CaResourceResponse> getResourceList() {
        return resourceList;
    }

    public void setResourceList(List<CaResourceResponse> resourceList) {
        this.resourceList = resourceList;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getSslEncCaCert() {
        return sslEncCaCert;
    }

    public CaDetailResponse setSslEncCaCert(String sslEncCaCert) {
        this.sslEncCaCert = sslEncCaCert;
        dealCertificateInfosMap("sslEncCaCert", sslEncCaCert);
        return this;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getIdCertTrusted() {
        return idCertTrusted;
    }

    public void setIdCertTrusted(Boolean idCertTrusted) {
        this.idCertTrusted = idCertTrusted;
    }

    public String getSslCaCert() {
        return sslCaCert;
    }

    public void setSslCaCert(String sslCaCert) {
        this.sslCaCert = sslCaCert;
        dealCertificateInfosMap("sslCaCert", sslCaCert);
    }

    public Map<String, CertificateInfo> getCertInfosMap() {
        return certInfosMap;
    }

    public void setCertInfosMap(Map<String, CertificateInfo> certInfosMap) {
        this.certInfosMap = certInfosMap;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void dealCertificateInfosMap(String key, String certValue) {
        if (StringUtils.isNotBlank(certValue)) {
            try {
                Certificate certificateObj = Certificate.getInstance(Base64.decode(PEMUtil.unFormatCert(certValue)));
                CertificateInfo certificateInfo = CertificateParserFactory.getX509CertificateParser().parse(certificateObj);
                certInfosMap.put(key, certificateInfo);
            } catch (Exception e) {
                throw ManagementInternalError.CERT_PREVIEW_ERROR.toException(e);
            }
        }
    }
}
