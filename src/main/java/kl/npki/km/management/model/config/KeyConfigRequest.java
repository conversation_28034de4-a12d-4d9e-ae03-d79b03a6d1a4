package kl.npki.km.management.model.config;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * @Author: guoq
 * @Date: 2023/5/19
 * @description:
 */
public class KeyConfigRequest {

    /**
     * 检查缓存池密钥池是否已经达到需要补充的阈值,间隔多少秒检查一次
     */
    @Schema(description = "缓存检查时间(S/秒)", defaultValue = "5")
    @Min(value = 5, message = "kl.npki.km.management.i18n_cache_check_time_ss_cannot_be_less_than_5")
    @Max(value = 86400, message = "kl.npki.km.management.i18n_cache_check_time_ss_cannot_exceed_86400")
    private int cacheCheckInterval = 30;

    /**
     * 密钥池cache的大小
     */
    @Schema(description = "密钥缓存池大小", defaultValue = "200")
    @Min(value = 100, message = "kl.npki.km.management.i18n_the_size_of_the_key_pool_cache_cannot_be_less_than_100")
    @Max(value = 100000, message = "kl.npki.km.management.i18n_the_size_of_the_key_pool_cache_cannot_exceed_100000")
    private int cacheSize = 200;

    /**
     * 密钥池cache的补充阈值
     */
    @Schema(description = "密钥缓存池补充阈值百分比，取值范围0-1，默认0.5表示50%", defaultValue = "05")
    @Min(value = 0, message = "kl.npki.km.management.i18n_the_threshold_for_supplementing_the_key_cache_pool_cannot_be_less_than_0")
    @Max(value = 1, message = "kl.npki.km.management.i18n_the_key_cache_pool_replenishment_threshold_cannot_exceed_1")
    private double cacheSupplyThreshold = 0.5;

    /**
     * 密钥唯一性检查
     */
    @Schema(description = "密钥唯一性检查", defaultValue = "true")
    private boolean keyUniquenessCheck = true;

    /**
     * 各类型密钥限制
     */
    @Schema(description = "各类型密钥限制")
    @Valid
    @NotNull(message = "kl.npki.km.management.i18n_the_key_generation_type_configuration_cannot_be_empty")
    private List<KeyLimitConfigRequest> keyLimits;

    public int getCacheCheckInterval() {
        return cacheCheckInterval;
    }

    public void setCacheCheckInterval(int cacheCheckInterval) {
        this.cacheCheckInterval = cacheCheckInterval;
    }

    public int getCacheSize() {
        return cacheSize;
    }

    public void setCacheSize(int cacheSize) {
        this.cacheSize = cacheSize;
    }

    public double getCacheSupplyThreshold() {
        return cacheSupplyThreshold;
    }

    public void setCacheSupplyThreshold(double cacheSupplyThreshold) {
        this.cacheSupplyThreshold = cacheSupplyThreshold;
    }

    public boolean isKeyUniquenessCheck() {
        return keyUniquenessCheck;
    }

    public void setKeyUniquenessCheck(boolean keyUniquenessCheck) {
        this.keyUniquenessCheck = keyUniquenessCheck;
    }

    public List<KeyLimitConfigRequest> getKeyLimits() {
        return keyLimits;
    }

    public void setKeyLimits(List<KeyLimitConfigRequest> keyLimits) {
        this.keyLimits = keyLimits;
    }
}
