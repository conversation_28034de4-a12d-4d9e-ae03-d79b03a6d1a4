package kl.npki.km.management.model.config;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.time.LocalDateTime;

/**
 * @Author: guoq
 * @Date: 2023/5/19
 * @description:
 */
public class ArchiveKeyConfigResponse {

    /**
     * 延迟归档时间(天)
     */
    @Schema(description = "延迟归档时间(天)", defaultValue = "365")
    @Min(value = 0, message = "kl.npki.km.management.i18n_the_delayed_archiving_time_days_cannot_be_less_than_0")
    private int lateArchiveTime = 365;

    /**
     * 归档分页阈值(超过多少条开始分页)
     */
    @Schema(description = "归档分页阈值(超过多少条开始分页)", defaultValue = "100")
    @Min(value = 100, message = "kl.npki.km.management.i18n_archive_paging_threshold_cannot_be_less_than_100")
    private int archivePageThreshold = 200;

    /**
     * 密钥生成执行间隔 cron表达式
     * 默认每天0点执行一次
     */
    @Schema(description = "密钥生成执行间隔 cron表达式", defaultValue = "0 0 0 * * ?")
    @NotBlank(message = "kl.npki.km.management.i18n_the_cron_expression_for_key_generation_execution_interval_cannot_be_empty")
    private String archiveCronExpression = "0 0 0 * * ?";

    @Schema(description = "定时任务执行状态", defaultValue = "true")
    private boolean scheduledStatus;

    @Schema(description = "定时任务上次执行时间", defaultValue = "")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime lastExecuteTime;

    public int getLateArchiveTime() {
        return lateArchiveTime;
    }

    public void setLateArchiveTime(int lateArchiveTime) {
        this.lateArchiveTime = lateArchiveTime;
    }

    public int getArchivePageThreshold() {
        return archivePageThreshold;
    }

    public void setArchivePageThreshold(int archivePageThreshold) {
        this.archivePageThreshold = archivePageThreshold;
    }

    public String getArchiveCronExpression() {
        return archiveCronExpression;
    }

    public void setArchiveCronExpression(String archiveCronExpression) {
        this.archiveCronExpression = archiveCronExpression;
    }

    public boolean isScheduledStatus() {
        return scheduledStatus;
    }

    public void setScheduledStatus(boolean scheduledStatus) {
        this.scheduledStatus = scheduledStatus;
    }

    public LocalDateTime getLastExecuteTime() {
        return lastExecuteTime;
    }

    public void setLastExecuteTime(LocalDateTime lastExecuteTime) {
        this.lastExecuteTime = lastExecuteTime;
    }
}
