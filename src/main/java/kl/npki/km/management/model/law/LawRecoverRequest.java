package kl.npki.km.management.model.law;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

/**
 * @Author: guoq
 * @Date: 2022/10/11
 * @description:
 */
public class LawRecoverRequest {

    /**
     * 待恢复的用户密钥ID
     */
    @NotNull(message = "kl.npki.km.management.i18n_the_user_key_id_to_be_restored_cannot_be_empty")
    private Long keyId;

    /**
     * 密钥位置
     */
    @NotNull(message = "kl.npki.km.management.i18n_the_key_location_cannot_be_empty")
    private Integer keyPosition;

    /**
     * 司法恢复管理员证书主题CN
     */
    private String subjectCn;

    /**
     * 司法恢复管理员id
     */
    @NotNull(message = "kl.npki.km.management.i18n_judicial_recovery_administrator_id_cannot_be_empty")
    private Long lawId;

    /**
     * 待恢复介质中的证书请求
     */
    @NotEmpty(message = "kl.npki.km.management.i18n_the_certificate_request_in_the_medium_to_be_restored_cannot_be_empty")
    private String recoverCertReq;

    /**
     * 司法管理员的签名值
     */
    @NotEmpty(message = "kl.npki.km.management.i18n_the_signature_value_of_the_judicial_administrator_cannot_be_empty")
    private String lawSignData;

    /**
     * 司法管理员签名原文
     */
    @NotEmpty(message = "kl.npki.km.management.i18n_the_original_signature_cannot_be_empty")
    private String lawSignText;

    public Long getKeyId() {
        return keyId;
    }

    public void setKeyId(Long keyId) {
        this.keyId = keyId;
    }

    public Integer getKeyPosition() {
        return keyPosition;
    }

    public void setKeyPosition(Integer keyPosition) {
        this.keyPosition = keyPosition;
    }

    public String getSubjectCn() {
        return subjectCn;
    }

    public void setSubjectCn(String subjectCn) {
        this.subjectCn = subjectCn;
    }

    public Long getLawId() {
        return lawId;
    }

    public void setLawId(Long lawId) {
        this.lawId = lawId;
    }

    public String getRecoverCertReq() {
        return recoverCertReq;
    }

    public void setRecoverCertReq(String recoverCertReq) {
        this.recoverCertReq = recoverCertReq;
    }

    public String getLawSignData() {
        return lawSignData;
    }

    public void setLawSignData(String lawSignData) {
        this.lawSignData = lawSignData;
    }

    public String getLawSignText() {
        return lawSignText;
    }

    public void setLawSignText(String lawSignText) {
        this.lawSignText = lawSignText;
    }
}
