package kl.npki.km.management.model.ca;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.nbase.db.support.mybatis.query.QueryField;
import kl.nbase.db.support.mybatis.query.QueryFieldOpType;
import kl.nbase.db.support.mybatis.query.QueryInfo;

/**
 * <AUTHOR>
 * @date 2022/10/11 13:40
 */
public class CaListRequest extends QueryInfo {

    /**
     * ca别名
     */
    @QueryField(value = "ca_name", op = QueryFieldOpType.LIKE)
    @Schema(description = "CA别名")
    private String caName;

    @QueryField(value = "ca_status", op = QueryFieldOpType.EQ)
    @Schema(description = "CA状态")
    private Integer caStatus;

    /**
     * CA接入方式
     */
    @QueryField(value = "ca_access_type", op = QueryFieldOpType.EQ)
    @Schema(description = "CA接入方式，1-手动接入，2-在线接入")
    private Integer caAccessType;

    public String getCaName() {
        return caName;
    }

    public void setCaName(String caName) {
        this.caName = caName;
    }

    public Integer getCaStatus() {
        return caStatus;
    }

    public void setCaStatus(Integer caStatus) {
        this.caStatus = caStatus;
    }

    public Integer getCaAccessType() {
        return caAccessType;
    }

    public void setCaAccessType(Integer caAccessType) {
        this.caAccessType = caAccessType;
    }
}
