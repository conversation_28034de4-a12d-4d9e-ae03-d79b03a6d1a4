package kl.npki.km.management.model.key;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.nbase.db.support.mybatis.query.QueryField;
import kl.nbase.db.support.mybatis.query.QueryFieldOpType;
import kl.nbase.db.support.mybatis.query.QueryInfo;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * @Author: guoq
 * @Date: 2023/4/12
 * @description: 归档请求
 */
public class KeyArchiveListRequest extends QueryInfo {

    /**
     * 加密证书序列号
     */
    @Schema(description = "加密证书序列号")
    @QueryField(value = "enc_cert_sn", op = QueryFieldOpType.LIKE)
    private String encCertSn;

    /**
     * 主体名称
     */
    @Schema(description = "主题项")
    @QueryField(op = QueryFieldOpType.LIKE)
    private String subject;

    /**
     * 密钥类型
     */
    @Schema(description = "密钥类型", allowableValues = "示例【 SM2, RSA2048, RSA4096, ECC】")
    @QueryField(value = "key_type", op = QueryFieldOpType.EQ)
    private String keyType;

    /**
     * caId
     */
    @Schema(description = "caId")
    @QueryField(value = "ca_id", op = QueryFieldOpType.EQ)
    private Long caId;

    /**
     * 密钥对 起始更新时间 (update_time >= startData)
     */
    @Schema(description = "密钥对起始更新时间")
    @QueryField(value = "update_time", op = QueryFieldOpType.GE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime leValidStart;

    /**
     * 密钥对 结束更新时间 (update_time <= endData)
     */
    @Schema(description = "密钥对结束更新时间")
    @QueryField(value = "update_time", op = QueryFieldOpType.LE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime geValidStart;

    public String getEncCertSn() {
        return encCertSn;
    }

    public void setEncCertSn(String encCertSn) {
        this.encCertSn = encCertSn;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public LocalDateTime getLeValidStart() {
        return leValidStart;
    }

    public void setLeValidStart(LocalDateTime leValidStart) {
        this.leValidStart = leValidStart;
    }

    public LocalDateTime getGeValidStart() {
        return geValidStart;
    }

    public Long getCaId() {
        return caId;
    }

    public void setCaId(Long caId) {
        this.caId = caId;
    }

    public void setGeValidStart(LocalDateTime geValidStart) {
        this.geValidStart = geValidStart;
    }
}
