package kl.npki.km.management.model.config;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * KM配置请求模型
 *
 * <AUTHOR>
 * @date 2023/8/31
 */
public class KmConfigRequest {

    /**
     * 请求验证
     */
    @Schema(description = "验证请求")
    private Boolean verifyRequest;

    /**
     * 签名响应
     */
    @Schema(description = "签名响应", defaultValue = "true")
    private Boolean signResponse;

    /**
     * 检查SN是否存在
     */
    @Schema(description = "检查SN是否存在", defaultValue = "true")
    private Boolean checkSnExist;

    /**
     * 恢复异常状态的CA
     */
    @Schema(description = "恢复异常状态的CA", defaultValue = "true")
    private Boolean restoreCA;

    /**
     * 是否开启SM9密钥服务
     */
    @Schema(description = "是否开启SM9密钥服务", defaultValue = "false")
    private Boolean enabledSm9Service;

    public Boolean isSignResponse() {
        return signResponse;
    }

    public void setSignResponse(Boolean signResponse) {
        this.signResponse = signResponse;
    }

    public Boolean isCheckSnExist() {
        return checkSnExist;
    }

    public void setCheckSnExist(Boolean checkSnExist) {
        this.checkSnExist = checkSnExist;
    }

    public Boolean isRestoreCA() {
        return restoreCA;
    }

    public void setRestoreCA(Boolean restoreCA) {
        this.restoreCA = restoreCA;
    }

    public Boolean isVerifyRequest() {
        return verifyRequest;
    }

    public void setVerifyRequest(Boolean verifyRequest) {
        this.verifyRequest = verifyRequest;
    }

    public Boolean isEnabledSm9Service() {
        return enabledSm9Service;
    }

    public void setEnabledSm9Service(Boolean enabledSm9Service) {
        this.enabledSm9Service = enabledSm9Service;
    }
}
