package kl.npki.km.management.model.key;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.time.LocalDateTime;

/**
 * @Author: guoq
 * @Date: 2022/10/9
 * @description: 密钥当前库响应
 */
public class KeyCurrentListResponse {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "密钥类型")
    private String keyType;

    @Schema(description = "密钥类型描述")
    private String keyTypeDesc;

    @Schema(description = "加密证书序列号")
    private String encCertSn;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "CA主键id")
    private Long caId;

    @Schema(description = "CA别名")
    private String caName;

    @Schema(description = "主题项")
    private String subject;

    @Schema(description = "密钥生效时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime validStart;

    @Schema(description = "密钥失效时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime validEnd;

    @Schema(description = "密钥状态")
    private Integer keyStatus;

    @Schema(description = "密钥托管时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public String getKeyTypeDesc() {
        return keyTypeDesc;
    }

    public void setKeyTypeDesc(String keyTypeDesc) {
        this.keyTypeDesc = keyTypeDesc;
    }

    public String getEncCertSn() {
        return encCertSn;
    }

    public void setEncCertSn(String encCertSn) {
        this.encCertSn = encCertSn;
    }

    public Long getCaId() {
        return caId;
    }

    public void setCaId(Long caId) {
        this.caId = caId;
    }

    public String getCaName() {
        return caName;
    }

    public void setCaName(String caName) {
        this.caName = caName;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public LocalDateTime getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(LocalDateTime validEnd) {
        this.validEnd = validEnd;
    }

    public LocalDateTime getValidStart() {
        return validStart;
    }

    public void setValidStart(LocalDateTime validStart) {
        this.validStart = validStart;
    }

    public Integer getKeyStatus() {
        return keyStatus;
    }

    public void setKeyStatus(Integer keyStatus) {
        this.keyStatus = keyStatus;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

}
