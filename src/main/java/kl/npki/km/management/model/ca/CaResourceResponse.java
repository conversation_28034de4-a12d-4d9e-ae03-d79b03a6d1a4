package kl.npki.km.management.model.ca;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 2022/11/14 15:12
 * @Description:
 */
public class CaResourceResponse {

    @Schema(description = "密钥类型")
    private String keyType;

    @Schema(description = "当前密钥剩余授权个数")
    private Integer surplusNum;

    @Schema(description = "最少密钥警告个数")
    private Integer warningNum;

    @Schema(description = "已授权密钥总数")
    private Integer limitNum;

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public Integer getSurplusNum() {
        return surplusNum;
    }

    public void setSurplusNum(Integer surplusNum) {
        this.surplusNum = surplusNum;
    }

    public Integer getWarningNum() {
        return warningNum;
    }

    public void setWarningNum(Integer warningNum) {
        this.warningNum = warningNum;
    }

    public Integer getLimitNum() {
        return limitNum;
    }

    public void setLimitNum(Integer limitNum) {
        this.limitNum = limitNum;
    }
}
