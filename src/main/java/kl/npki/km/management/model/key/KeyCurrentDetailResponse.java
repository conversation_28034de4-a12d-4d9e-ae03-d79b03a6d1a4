package kl.npki.km.management.model.key;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.time.LocalDateTime;

/**
 * @Author: guoq
 * @Date: 2022/10/9
 * @description: 当前库密钥详情
 */
public class KeyCurrentDetailResponse {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "主键id")
    private Long id;

    /**
     * 密钥类型
     */
    @Schema(description = "密钥类型")
    private String keyType;

    /**
     * 密钥类型描述
     */
    @Schema(description = "密钥类型描述")
    private String keyTypeDesc;

    /**
     * 用户加密证书序列号
     */
    @Schema(description = "加密证书序列号")
    private String encCertSn;

    @Schema(description = "加密证书序列号（十进制）")
    private String decimalEncCertSn;

    /**
     * ca唯一标识
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "CA主键id")
    private Long caId;

    /**
     * ca名称
     */
    @Schema(description = "CA别名")
    private String caName;

    /**
     * 介质序列号
     */
    @Schema(description = "介质序列号")
    private String mediumSn;

    @Schema(description = "主题项")
    private String subject;

    @Schema(description = "密钥生效时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime validStart;

    @Schema(description = "密钥失效时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime validEnd;

    /**
     * 用户扩展信息
     */
    @Schema(description = "用户扩展信息")
    private String userInfoEx;

    /**
     * 公钥值Hash
     */
    @Schema(description = "公钥值Hash")
    private String keyIndex;

    @Schema(description = "密钥状态")
    private Integer keyStatus;

    @Schema(description = "密钥托管时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "密钥更新时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public String getKeyTypeDesc() {
        return keyTypeDesc;
    }

    public void setKeyTypeDesc(String keyTypeDesc) {
        this.keyTypeDesc = keyTypeDesc;
    }

    public String getEncCertSn() {
        return encCertSn;
    }

    public void setEncCertSn(String encCertSn) {
        this.encCertSn = encCertSn;
    }

    public String getDecimalEncCertSn() {
        return decimalEncCertSn;
    }

    public void setDecimalEncCertSn(String decimalEncCertSn) {
        this.decimalEncCertSn = decimalEncCertSn;
    }

    public void setCaId(Long caId) {
        this.caId = caId;
    }

    public Long getCaId() {
        return caId;
    }

    public String getCaName() {
        return caName;
    }

    public void setCaName(String caName) {
        this.caName = caName;
    }

    public String getMediumSn() {
        return mediumSn;
    }

    public void setMediumSn(String mediumSn) {
        this.mediumSn = mediumSn;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public LocalDateTime getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(LocalDateTime validEnd) {
        this.validEnd = validEnd;
    }

    public LocalDateTime getValidStart() {
        return validStart;
    }

    public void setValidStart(LocalDateTime validStart) {
        this.validStart = validStart;
    }

    public String getUserInfoEx() {
        return userInfoEx;
    }

    public void setUserInfoEx(String userInfoEx) {
        this.userInfoEx = userInfoEx;
    }

    public String getKeyIndex() {
        return keyIndex;
    }

    public void setKeyIndex(String keyIndex) {
        this.keyIndex = keyIndex;
    }

    public Integer getKeyStatus() {
        return keyStatus;
    }

    public void setKeyStatus(Integer keyStatus) {
        this.keyStatus = keyStatus;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
