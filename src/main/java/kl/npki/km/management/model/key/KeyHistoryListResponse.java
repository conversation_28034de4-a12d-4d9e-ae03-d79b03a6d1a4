package kl.npki.km.management.model.key;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.time.LocalDateTime;

/**
 * @Author: guoq
 * @Date: 2022/10/9
 * @description: 历史密钥列表
 */
public class KeyHistoryListResponse {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "主键id")
    private Long id;

    /**
     * 密钥类型
     */
    @Schema(description = "密钥类型")
    private String keyType;

    /**
     * 用户加密证书序列号
     */
    @Schema(description = "加密证书序列号")
    private String encCertSn;

    /**
     * ca唯一标识
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "CA主键id")
    private Long caId;

    /**
     * ca名称
     */
    @Schema(description = "CA别名")
    private String caName;

    /**
     * 主题项
     */
    @Schema(description = "主题项")
    private String subject;

    /**
     * 密钥对生效时间
     */
    @Schema(description = "密钥生效时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime validStart;

    /**
     * 密钥对失效时间
     */
    @Schema(description = "密钥失效时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime validEnd;

    @Schema(description = "密钥状态")
    private Integer keyStatus;

    /**
     * 归档时间
     */
    @Schema(description = "归档时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public String getEncCertSn() {
        return encCertSn;
    }

    public void setEncCertSn(String encCertSn) {
        this.encCertSn = encCertSn;
    }

    public Long getCaId() {
        return caId;
    }

    public void setCaId(Long caId) {
        this.caId = caId;
    }

    public String getCaName() {
        return caName;
    }

    public void setCaName(String caName) {
        this.caName = caName;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public LocalDateTime getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(LocalDateTime validEnd) {
        this.validEnd = validEnd;
    }

    public LocalDateTime getValidStart() {
        return validStart;
    }

    public void setValidStart(LocalDateTime validStart) {
        this.validStart = validStart;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public Integer getKeyStatus() {
        return keyStatus;
    }

    public void setKeyStatus(Integer keyStatus) {
        this.keyStatus = keyStatus;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
