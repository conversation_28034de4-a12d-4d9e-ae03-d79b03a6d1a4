package kl.npki.km.management.model.config;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.time.LocalDateTime;

/**
 * @Author: guoq
 * @Date: 2023/5/19
 * @description:
 */
public class KeyLimitConfigResponse {

    @Schema(description = "是否启用该密钥类型", defaultValue = "true")
    private boolean enable;
    /**
     * 密钥类型
     */
    @Schema(description = "密钥类型", defaultValue = "SM2")
    private String keyType;

    /**
     * 密钥产生上限
     */
    @Schema(description = "密钥产生上限", defaultValue = "1000")
    private int limit;

    /**
     * 密钥生成执行间隔 cron表达式
     * (每种密钥支持单独配置定时任务，如果该配置为空，默认使用外层密钥生成表达式)
     */
    @Schema(description = "密钥生成执行间隔(cron表达式)")
    private String keyGenCronExpression;

    @Schema(description = "定时任务执行状态", defaultValue = "true")
    private boolean scheduledStatus;

    @Schema(description = "定时任务上次执行时间", defaultValue = "")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime lastExecuteTime;

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public String getKeyGenCronExpression() {
        return keyGenCronExpression;
    }

    public void setKeyGenCronExpression(String keyGenCronExpression) {
        this.keyGenCronExpression = keyGenCronExpression;
    }

    public boolean isScheduledStatus() {
        return scheduledStatus;
    }

    public void setScheduledStatus(boolean scheduledStatus) {
        this.scheduledStatus = scheduledStatus;
    }

    public LocalDateTime getLastExecuteTime() {
        return lastExecuteTime;
    }

    public void setLastExecuteTime(LocalDateTime lastExecuteTime) {
        this.lastExecuteTime = lastExecuteTime;
    }
}
