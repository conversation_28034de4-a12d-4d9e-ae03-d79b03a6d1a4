package kl.npki.km.management.model.config;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * KM配置响应
 *
 * <AUTHOR>
 * @since 2025/6/26
 */
public class KmConfigResponse implements Serializable {

    /**
     * 请求验证
     */
    @Schema(description = "验证请求")
    private boolean verifyRequest;

    /**
     * 签名响应
     */
    @Schema(description = "签名响应", defaultValue = "true")
    private boolean signResponse;

    /**
     * 检查SN是否存在
     */
    @Schema(description = "检查SN是否存在", defaultValue = "true")
    private boolean checkSnExist;

    /**
     * 恢复异常状态的CA
     */
    @Schema(description = "恢复异常状态的CA", defaultValue = "true")
    private boolean restoreCA;

    /**
     * 是否开启SM9密钥服务
     */
    @Schema(description = "是否开启SM9密钥服务", defaultValue = "false")
    private boolean enabledSm9Service;

    public boolean isVerifyRequest() {
        return verifyRequest;
    }

    public void setVerifyRequest(boolean verifyRequest) {
        this.verifyRequest = verifyRequest;
    }

    public boolean isSignResponse() {
        return signResponse;
    }

    public void setSignResponse(boolean signResponse) {
        this.signResponse = signResponse;
    }

    public boolean isCheckSnExist() {
        return checkSnExist;
    }

    public void setCheckSnExist(boolean checkSnExist) {
        this.checkSnExist = checkSnExist;
    }

    public boolean isRestoreCA() {
        return restoreCA;
    }

    public void setRestoreCA(boolean restoreCA) {
        this.restoreCA = restoreCA;
    }

    public boolean isEnabledSm9Service() {
        return enabledSm9Service;
    }

    public void setEnabledSm9Service(boolean enabledSm9Service) {
        this.enabledSm9Service = enabledSm9Service;
    }
}
