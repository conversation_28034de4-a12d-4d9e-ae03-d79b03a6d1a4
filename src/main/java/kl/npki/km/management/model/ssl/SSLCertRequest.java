package kl.npki.km.management.model.ssl;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.nbase.db.support.mybatis.query.QueryField;
import kl.nbase.db.support.mybatis.query.QueryFieldOpType;
import kl.nbase.db.support.mybatis.query.QueryInfo;

import java.io.Serializable;
import java.util.Date;

/**
 * 站点证书查询请求体
 * <AUTHOR>
 * @date 2022/12/21
 */
public class SSLCertRequest extends QueryInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @QueryField(value = "cn", op = QueryFieldOpType.EQ)
    @Schema(description = "证书通用名")
    private String cn;

    @QueryField(value = "dn", op = QueryFieldOpType.EQ)
    @Schema(description = "证书签发者")
    private String dn;

    @QueryField(value = "sn", op = QueryFieldOpType.EQ)
    @Schema(description = "证书序列号")
    private String sn;

    @QueryField(value = "status", op = QueryFieldOpType.EQ)
    @Schema(description = "证书状态")
    private String status;

    @QueryField(value = "not_before", op = QueryFieldOpType.GE)
    @Schema(description = "notBefore")
    private Date notBefore;

    @QueryField(value = "not_after", op = QueryFieldOpType.LE)
    @Schema(description = "notAfter")
    private Date notAfter;

    public String getCn() {
        return cn;
    }

    public void setCn(String cn) {
        this.cn = cn;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDn() {
        return dn;
    }

    public void setDn(String dn) {
        this.dn = dn;
    }

    public Date getNotBefore() {
        return notBefore;
    }

    public void setNotBefore(Date notBefore) {
        this.notBefore = notBefore;
    }

    public Date getNotAfter() {
        return notAfter;
    }

    public void setNotAfter(Date notAfter) {
        this.notAfter = notAfter;
    }
}
