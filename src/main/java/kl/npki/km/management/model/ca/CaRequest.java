package kl.npki.km.management.model.ca;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import kl.npki.km.core.common.constants.CaAccessTypeEnum;

/**
 * @Author: guoq
 * @Date: 2023/5/19
 * @description:
 */
public class CaRequest {

    @NotBlank(message = "kl.npki.km.management.i18n_ca_name_cannot_be_empty")
    @Size(max = 128, message = "kl.npki.km.management.i18n_the_length_of_ca_name_cannot_exceed_128_characters")
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = "kl.npki.km.management.i18n_ca_name_cannot_contain_spaces_20_starting_or_ending_with_0a_or_00", flags = Pattern.Flag.DOTALL)
    @Schema(description = "CA别名")
    private String caName;

    @NotBlank(message = "kl.npki.km.management.i18n_ca_version_cannot_be_empty")
    @Size(max = 128, message = "kl.npki.km.management.i18n_the_length_of_ca_version_cannot_exceed_128_characters")
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = "kl.npki.km.management.i18n_ca_version_cannot_use_spaces_20_starting_or_ending_with_0a_or_00", flags = Pattern.Flag.DOTALL)
    @Schema(description = "CA版本")
    private String caVersion;

    @Min(value = 1, message = "kl.npki.km.management.i18n_the_sm2_key_standard_version_cannot_be_less_than_1")
    @Max(value = 2, message = "kl.npki.km.management.i18n_the_sm2_key_standard_version_cannot_exceed_2")
    @Schema(description = "SM2密钥服务响应所使用的规范版本")
    private int sm2KeyStandardVersion;

    @Schema(description = "CA接入方式,默认为手动接入")
    private CaAccessTypeEnum caAccessType = CaAccessTypeEnum.MANUAL;

    /**
     * 身份证书
     */
    @NotBlank(message = "kl.npki.km.management.i18n_ca_identity_certificate_cannot_be_empty")
    @Size(max = 9216, message = "kl.npki.km.management.i18n_the_length_of_ca_identity_certificate_cannot_exceed_9216_characters")
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = "kl.npki.km.management.i18n_ca_identity_certificate_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00", flags = Pattern.Flag.DOTALL)
    @Schema(description = "CA身份证书")
    private String idCert;

    @Min(value = 1, message = "kl.npki.km.management.i18n_the_default_validity_period_of_the_key_cannot_be_less_than_1_year")
    @Max(value = 100, message = "kl.npki.km.management.i18n_the_default_validity_period_of_the_key_cannot_exceed_100_years")
    @Schema(description = "密钥默认有效期")
    private int limitYear;

    @Schema(description = "备注")
    private String remark;

    public String getCaName() {
        return caName;
    }

    public void setCaName(String caName) {
        this.caName = caName;
    }

    public String getCaVersion() {
        return caVersion;
    }

    public void setCaVersion(String caVersion) {
        this.caVersion = caVersion;
    }

    public CaAccessTypeEnum getCaAccessType() {
        return caAccessType;
    }

    public void setCaAccessType(CaAccessTypeEnum caAccessType) {
        this.caAccessType = caAccessType;
    }

    public String getIdCert() {
        return idCert;
    }

    public void setIdCert(String idCert) {
        this.idCert = idCert;
    }

    public int getSm2KeyStandardVersion() {
        return sm2KeyStandardVersion;
    }

    public void setSm2KeyStandardVersion(int sm2KeyStandardVersion) {
        this.sm2KeyStandardVersion = sm2KeyStandardVersion;
    }

    public int getLimitYear() {
        return limitYear;
    }

    public void setLimitYear(int limitYear) {
        this.limitYear = limitYear;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
