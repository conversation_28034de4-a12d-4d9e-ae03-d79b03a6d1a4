package kl.npki.km.management.model.key;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.nbase.db.support.mybatis.query.QueryField;
import kl.nbase.db.support.mybatis.query.QueryFieldOpType;
import kl.nbase.db.support.mybatis.query.QueryInfo;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/11/9 17:44
 * @Description:
 */
public class KeyPoolListRequest extends QueryInfo {

    /**
     * 密钥类型
     */
    @Schema(description = "密钥类型", allowableValues = "示例【 SM2, RSA2048, RSA4096, ECC】")
    @QueryField(value = "key_type", op = QueryFieldOpType.EQ)
    private String keyType;

    /**
     * 密钥对 起始生效时间 (valid_start >= startData)
     */
    @Schema(description = "密钥对起始生成时间")
    @QueryField(value = "create_time", op = QueryFieldOpType.GE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime leCreateTime;

    /**
     * 密钥对 结束生效时间 (valid_start <= endData)
     */
    @Schema(description = "密钥对最晚生成时间")
    @QueryField(value = "create_time", op = QueryFieldOpType.LE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime geCreateTime;

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public LocalDateTime getLeCreateTime() {
        return leCreateTime;
    }

    public void setLeCreateTime(LocalDateTime leCreateTime) {
        this.leCreateTime = leCreateTime;
    }

    public LocalDateTime getGeCreateTime() {
        return geCreateTime;
    }

    public void setGeCreateTime(LocalDateTime geCreateTime) {
        this.geCreateTime = geCreateTime;
    }
}
