package kl.npki.km.management.kgs;

import kl.ibc.base.core.biz.worker.SysWorkerIdAssigner;
import kl.ibc.base.core.configs.LogConfig;
import kl.ibc.base.core.configs.SysInfoConfig;
import kl.ibc.base.core.constant.BaseConstant;
import kl.ibc.base.core.repository.*;
import kl.ibc.base.core.tenantholders.*;
import kl.ibc.base.core.tenantholders.common.CommonTenantHolder;
import kl.ibc.base.service.exception.BaseServiceInternalError;
import kl.ibc.base.service.listener.CacheConfigListener;
import kl.ibc.base.service.listener.LogConfigListener;
import kl.ibc.base.service.listener.TrafficConfigListener;
import kl.ibc.base.service.repository.*;
import kl.ibc.base.service.repository.service.IApiLogService;
import kl.ibc.base.service.repository.service.IPublicParametersService;
import kl.ibc.base.service.repository.service.impl.ApiLogServiceImpl;
import kl.ibc.base.service.repository.service.impl.PublicParametersServiceImpl;
import kl.ibc.management.core.service.AdminMgrService;
import kl.ibc.management.core.service.access.IAccessKgsService;
import kl.ibc.management.core.service.access.impl.AccessKgsServiceImpl;
import kl.ibc.management.core.service.params.IPublicParamsManageService;
import kl.ibc.management.core.service.params.impl.PublicParamsManageServiceImpl;
import kl.nbase.cache.config.CacheConfig;
import kl.nbase.config.RefreshableConfigWrapper;
import kl.nbase.config.constant.ConfigConstantsEnum;
import kl.nbase.config.holder.RefreshableConfigHolder;
import kl.nbase.config.listener.ConfigRefreshListener;
import kl.nbase.db.core.DataSourceContext;
import kl.nbase.db.support.id.config.UIdConfig;
import kl.nbase.db.support.id.worker.WorkerIdAssigner;
import kl.nbase.db.support.id.worker.entity.WorkerNodeEntity;
import kl.nbase.db.support.sharding.config.ShardingConfig;
import kl.nbase.db.support.slot.SlotDataSourceConfig;
import kl.nbase.log.config.LogExtConfig;
import kl.nbase.netty.conf.NettyHttpServerConfig;
import kl.nbase.netty.conf.NettyTcpServerConfig;
import kl.nbase.timer.config.TimerConfig;
import kl.nbase.traffic.config.TrafficConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.core.env.Environment;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 集成KGS业务模块的bean配置
 *
 * <AUTHOR> href="mailto:<EMAIL>">Liang Shi</a>
 * @date 2025/6/24
 */
@Configuration
// 扫描KGS模块的bean、确保IBC模块的Repository注册、确保IBC公共模块的公共参数、系统标识密钥Mapper映射正常使用
@ComponentScan(
        basePackages = {
                "kl.ibc.kgs.service",
                "kl.ibc.kgs.management.configuration",
                "kl.ibc.kgs.management.controller",
                "kl.ibc.kgs.management.mapper",
                "kl.ibc.kgs.management.repository.service",
                "kl.ibc.base.service.repository.collector",
                "kl.ibc.base.service.mapper"
        },
        // 由于KgsAdminMgrController依赖IBC-Base模块Bean，集成KGS不扫描IBC-Base模块bean，所以需要移除
        excludeFilters = @ComponentScan.Filter(
                type = FilterType.ASSIGNABLE_TYPE,
                classes = kl.ibc.kgs.management.controller.KgsAdminMgrController.class
        )
)
@ConditionalOnProperty(prefix = "kl.km.feature", name = "enableIbcKm", havingValue = "true")
public class KgsFeatureManageBeanConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(KgsFeatureManageBeanConfiguration.class);

    @PostConstruct
    public void init() {
        kgsTenantContextHolderInit();
    }

    /**
     * 在全局配置初始化完成后，初始化租户托管对象 <br/>
     * 总体顺序：配置中心初始化 ——> 全局配置初始化 ——> 租户托管对象初始化 ——> 租户配置、全局配置使用 <br/>
     *
     * @return {@link  TenantContextHolder}
     */
    public void kgsTenantContextHolderInit() {
        // 从全局配置中获取租户列表，注册租户列表
        registerTenants();
        // 注册托管类型
        TenantContextHolder.INSTANCE.registerEscrowObj(ConfigHolder.class);
        TenantContextHolder.INSTANCE.registerEscrowObj(CacheClientHolder.class);
        TenantContextHolder.INSTANCE.registerEscrowObj(CommonTenantHolder.class);
        TenantContextHolder.INSTANCE.registerEscrowObj(KGSClientHolder.class);
        TenantContextHolder.INSTANCE.registerEscrowObj(PSClientHolder.class);
        // 初始化configHolder管理的配置类
        registerConfigHolderWrappers();
        // 注册加密机引擎,保证IBC体系的加密机引擎在租户托管对象初始化后被注册,不然加密机会被初始化两次
        EngineHolder.putIfAbsent(kl.npki.base.core.tenantholders.EngineHolder.get());
        // 初始化租户托管对象
        try {
            TenantContextHolder.INSTANCE.initTenants();
        } catch (Exception e) {
            throw BaseServiceInternalError.TENANT_HOLDER_INIT_FAILED.toException(e);
        }
    }

    public void registerTenants() {
        SysInfoConfig sysInfoConfig = RefreshableConfigHolder.getGlobalConfig(SysInfoConfig.class);
        if (sysInfoConfig == null) {
            throw BaseServiceInternalError.SYSTEM_ERROR.toException("未从配置中心读取到系统配置SysInfoConfig");
        }
        String defaultTenantId = null;
        try {
            defaultTenantId = sysInfoConfig.getEnvironmentSwitchId();
        } catch (Exception e) {
            logger.info("未从系统配置SysInfoConfig中读取到初始化租户id");
        }
        if (StringUtils.isBlank(defaultTenantId)) {
            throw BaseServiceInternalError.SYSTEM_ERROR.toException("未从系统配置SysInfoConfig中读取到默认租户id");
        }
        TenantContextHolder.setDefaultTenantId(defaultTenantId);
        DataSourceContext.setDefaultTenantId(defaultTenantId);
        // 注册默认租户
        TenantContextHolder.INSTANCE.registerTenant(defaultTenantId);

        Set<String> tenantIdSet = null;
        try {
            tenantIdSet = Arrays.stream(sysInfoConfig.getTenantIds().split(",")).filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            logger.error("未从spring配置中读取到初始化租户id列表", e);
        }
        if (CollectionUtils.isEmpty(tenantIdSet)) {
            return;
        }
        // 注册初始化租户列表
        TenantContextHolder.INSTANCE.registerTenants(tenantIdSet);
    }

    private void registerConfigHolderWrappers() {
        List<RefreshableConfigWrapper> configWrapperList = new ArrayList<>();
        List<ConfigRefreshListener> cacheListeners = new ArrayList<>(4);
        cacheListeners.add(new CacheConfigListener());
        RefreshableConfigWrapper cacheConfigWrapper = new RefreshableConfigWrapper(new CacheConfig(),
                BaseConstant.PKI_CONFIG_FILE_NAME, "kl.base.cache", cacheListeners,
                false);
        configWrapperList.add(cacheConfigWrapper);

        RefreshableConfigWrapper timerConfigWrapper = new RefreshableConfigWrapper(new TimerConfig(),
                BaseConstant.CONFIG_FILE_NAME, "kl.base.timer");
        configWrapperList.add(timerConfigWrapper);

        RefreshableConfigWrapper logExtConfigWrapper = new RefreshableConfigWrapper(new LogExtConfig(),
                BaseConstant.CONFIG_FILE_NAME, "kl.base.log");
        configWrapperList.add(logExtConfigWrapper);

        List<ConfigRefreshListener> logListeners = new ArrayList<>(4);
        logListeners.add(new LogConfigListener());
        RefreshableConfigWrapper logConfigWrapper = new RefreshableConfigWrapper(new LogConfig(),
                BaseConstant.CONFIG_FILE_NAME, "kl.base.log", logListeners);
        configWrapperList.add(logConfigWrapper);

        // 加入限流组件的配置
        List<ConfigRefreshListener> trafficListeners = new ArrayList<>(4);
        trafficListeners.add(new TrafficConfigListener());
        RefreshableConfigWrapper trafficConfigWrapper = new RefreshableConfigWrapper(new TrafficConfig(),
                BaseConstant.CONFIG_FILE_NAME, "kl.base.traffic", trafficListeners);
        configWrapperList.add(trafficConfigWrapper);

        // 初始化租户数据源配置
        RefreshableConfigWrapper dbWrapper = new RefreshableConfigWrapper(new SlotDataSourceConfig(),
                BaseConstant.PKI_CONFIG_FILE_NAME, "kl.base.datasource", false);
        configWrapperList.add(dbWrapper);

        // sharding 全局配置
        RefreshableConfigWrapper shardingConfigWrapper = new RefreshableConfigWrapper(new ShardingConfig(),
                BaseConstant.CONFIG_FILE_NAME, "kl.base.sharding");
        configWrapperList.add(shardingConfigWrapper);

        // netty 配置
        RefreshableConfigWrapper nettyHttpConfigWrapper = new RefreshableConfigWrapper(new NettyHttpServerConfig(),
                BaseConstant.CONFIG_FILE_NAME, "kl.base.service.http");
        configWrapperList.add(nettyHttpConfigWrapper);

        RefreshableConfigWrapper nettyTcpConfigWrapper = new RefreshableConfigWrapper(new NettyTcpServerConfig(),
                BaseConstant.CONFIG_FILE_NAME, "kl.base.service.tcp");
        configWrapperList.add(nettyTcpConfigWrapper);

        RefreshableConfigWrapper idConfigWrapper = new RefreshableConfigWrapper(new UIdConfig(),
                BaseConstant.CONFIG_FILE_NAME, "kl.base.id");
        configWrapperList.add(idConfigWrapper);

        ConfigHolder.registConfigWrappers(configWrapperList);
    }

    @Bean
    public ISystemKeyRepository systemKeyRepository() {
        return new SystemKeyRepositoryImpl();
    }

    @Bean
    public IMainKeyRepository ibcMainKeyRepository() {
        return new MainKeyRepositoryImpl();
    }

    @Bean
    public IWorkerNodeRepository workerNodeRepository() {
        return new WorkerNodeRepositoryImpl();
    }

    @Bean
    public PublicParametersRepository ibcPublicParametersRepository() {
        return new PublicParameterRepositoryImpl();
    }

    @Bean
    public IApiLogRepository ibcApiLogRepository() {
        return new ApiLogRepositoryImpl();
    }

    @Bean
    public IApiLogService ibcApiLogService() {
        return new ApiLogServiceImpl();
    }

    @Bean
    public IPublicParametersService publicParametersRepository() {
        return new PublicParametersServiceImpl();
    }

    @Bean
    @ConditionalOnMissingBean(WorkerIdAssigner.class)
    public SysWorkerIdAssigner sysWorkerIdAssigner(Environment env, DataSource dataSource, WorkerNodeEntity workerNodeEntity) {
        // 从系统变量中获取当前设备的serviceId
        String serviceId = env.getProperty(ConfigConstantsEnum.CONFIG_DB_SITE_NAME.getKey());
        workerNodeEntity.setServiceId(serviceId);
        SysWorkerIdAssigner caWorkerIdAssigner = new SysWorkerIdAssigner(dataSource, workerNodeEntity);
        caWorkerIdAssigner.assignWorkerId();
        return caWorkerIdAssigner;
    }

    @Bean
    public IPublicParamsManageService getPublicParamsManageService() {
        return new PublicParamsManageServiceImpl();
    }

    @Bean
    public IAccessKgsService getAccessKgsService() {
        return new AccessKgsServiceImpl();
    }

    @Bean
    public AdminMgrService ibcAdminMgrService() {
        return new AdminMgrService();
    }

}
