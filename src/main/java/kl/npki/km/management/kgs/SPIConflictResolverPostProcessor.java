package kl.npki.km.management.kgs;

import kl.ibc.base.service.common.log.store.DbLogStoreService;
import kl.ibc.base.service.common.log.store.FileLogStoreService;
import kl.nbase.auth.authority.AuthorityHandler;
import kl.nbase.auth.core.SpiMgr;
import kl.nbase.auth.identifier.CredentialIdentifier;
import kl.nbase.auth.token.store.TokenStore;
import kl.nbase.log.store.LogStoreFactory;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 移除由于SPI机制扫描的IBC体系下的日志存储服务和登录认证SPI实现，避免与PKI体系的日志存储服务重复
 *
 * <AUTHOR>
 * @since 2025/6/26
 */
@Component
public class SPIConflictResolverPostProcessor implements SmartInitializingSingleton {

    @Override
    public void afterSingletonsInstantiated() {
        // 由于日志SPI机制扫描，必须移除IBC体系的日志存储服务，不然一条记录会被DB、File重复记录
        LogStoreFactory.getInstance().unregisterLogStore(DbLogStoreService.class);
        LogStoreFactory.getInstance().unregisterLogStore(FileLogStoreService.class);

        // 解决认证组件SPI冲突
        List<CredentialIdentifier> credentialIdentifiers = SpiMgr.getInstance().getServices(CredentialIdentifier.class);
        List<TokenStore> tokenStores = SpiMgr.getInstance().getServices(TokenStore.class);
        List<AuthorityHandler> authorityHandlers = SpiMgr.getInstance().getServices(AuthorityHandler.class);

        Set<String> excludedCredentialIdentifiers = new HashSet<>();
        excludedCredentialIdentifiers.add("kl.ibc.base.management.common.auth.IdentifierSignIdentifierImpl");
        excludedCredentialIdentifiers.add("kl.ibc.base.management.common.auth.UsernamePasswordIdentifierImpl");
        excludedCredentialIdentifiers.add("kl.ibc.base.management.common.auth.SslIdentifierImpl");
        excludedCredentialIdentifiers.add("kl.ibc.base.management.common.auth.MultiIdentifierSignIdentifierImpl");

        Set<String> excludedTokenStores = new HashSet<>();
        excludedTokenStores.add("kl.ibc.base.management.common.auth.JwtTokenStoreImpl");

        Set<String> excludedAuthorityHandlers = new HashSet<>();
        excludedAuthorityHandlers.add("kl.ibc.base.management.common.auth.AuthorityHandlerImpl");

        // 过滤CredentialIdentifier
        List<CredentialIdentifier> filteredCredentialIdentifiers = credentialIdentifiers.stream()
                .filter(impl -> !excludedCredentialIdentifiers.contains(impl.getClass().getName()))
                .collect(Collectors.toList());

        // 过滤TokenStore
        List<TokenStore> filteredTokenStores = tokenStores.stream()
                .filter(impl -> !excludedTokenStores.contains(impl.getClass().getName()))
                .collect(Collectors.toList());

        // 过滤AuthorityHandler
        List<AuthorityHandler> filteredAuthorityHandlers = authorityHandlers.stream()
                .filter(impl -> !excludedAuthorityHandlers.contains(impl.getClass().getName()))
                .collect(Collectors.toList());

        // 重新注册过滤后的服务
        SpiMgr.getInstance().put(CredentialIdentifier.class, filteredCredentialIdentifiers);
        SpiMgr.getInstance().put(TokenStore.class, filteredTokenStores);
        SpiMgr.getInstance().put(AuthorityHandler.class, filteredAuthorityHandlers);
    }
}
