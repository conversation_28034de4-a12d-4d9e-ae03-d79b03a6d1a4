package kl.npki.km.management.kgs;

import kl.ibc.base.service.interceptor.RequestInterceptor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


/**
 * 为了保证SM9服务接口调用能正常日志记录
 *
 * <AUTHOR>
 * @since 2025/6/26
 */
@Configuration
@ConditionalOnProperty(prefix = "kl.km.feature", name = "enableIbcKm", havingValue = "true")
public class KgsWebMvcConfiguration implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry
                .addInterceptor(new RequestInterceptor())
                .addPathPatterns("/ibc/**");
    }
}