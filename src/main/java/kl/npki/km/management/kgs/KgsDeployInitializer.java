package kl.npki.km.management.kgs;

import kl.npki.base.service.common.SpringBeanRegister;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.management.core.biz.deploy.service.IDeployInitSpi;

/**
 * 如果KGS功能开启，则自动初始化KGS需要的正式环境数据库表
 *
 * <AUTHOR>
 * @since 2025/6/27
 */
public class KgsDeployInitializer implements IDeployInitSpi {

    private final KgsServiceInitializer kgsServiceInitializer;

    public KgsDeployInitializer() {
        this.kgsServiceInitializer = SpringBeanRegister.getBean(KgsServiceInitializer.class);
    }

    @Override
    public void init() {
        // 如果开关是打开的（Demo环境打开切换过来），则自动初始化KGS需要的正式环境数据库表
        if (KmConfigWrapper.getKmFeatureConf().isEnableIbcKm()) {
            // 数据库、服务初始化
            kgsServiceInitializer.enableSm9ServiceWhenDeployComplete();
        }
    }

    @Override
    public int getOrder() {
        // 保证在DeployCompleteBaseInit之后执行
        return 1000;
    }

}
