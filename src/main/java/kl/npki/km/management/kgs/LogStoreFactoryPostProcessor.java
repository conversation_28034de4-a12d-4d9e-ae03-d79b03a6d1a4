package kl.npki.km.management.kgs;

import kl.ibc.base.service.common.log.store.DbLogStoreService;
import kl.ibc.base.service.common.log.store.FileLogStoreService;
import kl.nbase.log.store.LogStoreFactory;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.stereotype.Component;

/**
 * 移除由于SPI机制扫描的IBC体系下的日志存储服务，避免与PKI体系的日志存储服务重复
 *
 * <AUTHOR>
 * @since 2025/6/26
 */
@Component
public class LogStoreFactoryPostProcessor implements SmartInitializingSingleton {
    @Override
    public void afterSingletonsInstantiated() {
        // 由于日志SPI机制扫描，必须移除IBC体系的日志存储服务，不然一条记录会被DB、File重复记录
        LogStoreFactory.getInstance().unregisterLogStore(DbLogStoreService.class);
        LogStoreFactory.getInstance().unregisterLogStore(FileLogStoreService.class);
    }
}
