package kl.npki.km.management.kgs;

import com.alibaba.druid.pool.DruidDataSource;
import kl.nbase.db.support.druid.DruidDataSourceProperties;
import kl.nbase.db.support.slot.SlotDataSourceConfig;
import kl.nbase.db.utils.DBScriptRunner;
import kl.nbase.db.utils.DataSourceUtil;
import kl.npki.base.core.biz.db.service.IDbSwitchConfigService;
import kl.npki.base.core.constant.DbConfigType;
import kl.npki.base.core.constant.EnvironmentEnum;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.service.util.MonthShardingUtil;
import kl.npki.km.management.exception.KmManagementInternalError;
import kl.npki.management.core.exception.ManagementInternalError;
import org.apache.ibatis.jdbc.ScriptRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import static kl.npki.base.core.constant.BaseConstant.WEB_ROOT;

/**
 * KGS 数据库初始化
 *
 * <AUTHOR>
 * @since 2025/6/27
 */
public class KgsDatabaseInitializer {

    private static final Logger log = LoggerFactory.getLogger(KgsDatabaseInitializer.class);

    public static final String TABLE_NAME_CHECK = "T_SERVICE_KEY";
    private static final String DML_SQL_FILE_PATH = "/kgssql/data/npki.sql";
    private static final String KGS_SQL_ID = "kgssql";

    private final IDbSwitchConfigService dbSwitchConfigService;

    public KgsDatabaseInitializer(IDbSwitchConfigService dbSwitchConfigService) {
        this.dbSwitchConfigService = dbSwitchConfigService;
    }

    /**
     * 初始化KGS数据库环境（根据部署状态自动判断使用Demo还是生产环境）
     */
    public void initializeKgsDatabase() {
        try {
            if (SystemUtil.isDeployed()) {
                initializeProductionDatabase();
            } else {
                initializeDemoDatabase();
            }
        } catch (Exception e) {
            throw KmManagementInternalError.SM9_KEY_SERVICE_ENABLED_ERROR.toException(e);
        }
    }

    public void initializeDemoDatabase() throws IOException,
        SQLException, ClassNotFoundException {
        SlotDataSourceConfig slotDataSourceConfig = ConfigHolder.get(EnvironmentEnum.DEMO.getId()).get(SlotDataSourceConfig.class);
        DruidDataSource dataSource = MonthShardingUtil.getMasterDataSource(slotDataSourceConfig);
        if (DataSourceUtil.isTableExist(dataSource, TABLE_NAME_CHECK)) {
            log.warn("The KGS business table already exists, initialization script will no longer be executed");
            return;
        }
        String sqlPath = WEB_ROOT + File.separator + KGS_SQL_ID;
        DruidDataSourceProperties dbProp = slotDataSourceConfig.getDatasource().get(0);

        Class.forName(DbConfigType.H2.getDriver());
        // 建立数据库连接
        try (Connection h2Connection = DriverManager.getConnection(dbProp.getUrl(), dbProp.getUsername(),
            dbProp.getPassword())) {

            ScriptRunner scriptRunner = new ScriptRunner(h2Connection);

            // 1. 执行建表语句
            try (FileReader h2SqlFileReader = new FileReader(Paths.get(sqlPath, "h2.sql").toString())) {
                scriptRunner.runScript(h2SqlFileReader);
            }

            // 2. 执行data目录下的sql
            try (FileReader npkiSqlFileReader = new FileReader(Paths.get(sqlPath, "data", "npki.sql").toString())) {
                scriptRunner.runScript(npkiSqlFileReader);
            }

            // 3. 执行demo目录下的sql
            try (FileReader demoSqlFileReader =
                     new FileReader(Paths.get(sqlPath, "data", "npki-demo.sql").toString())) {
                scriptRunner.runScript(demoSqlFileReader);
            }
        }
    }

    public void initializeProductionDatabase() {
        SlotDataSourceConfig slotDataSourceConfig = ConfigHolder.get(EnvironmentEnum.DEFAULT.getId()).get(SlotDataSourceConfig.class);
        try (DruidDataSource dataSource = MonthShardingUtil.getMasterDataSource(slotDataSourceConfig)) {
            // 获取数据库脚本(根据数据库类型)
            List<String> sqlFilePathList = new ArrayList<>(2);
            String driverClassName = slotDataSourceConfig.getDruid().getDriverClassName();
            DbConfigType dbConfigType = DbConfigType.getDbConfigTypeByDriver(driverClassName);
            // 添加sql脚本路径
            // 设置sql脚本路径为绝对路径
            String sqlFilePath = dbConfigType.getSqlFilePath();
            if (sqlFilePath.contains("/")) {
                String[] parts = sqlFilePath.split("/", 2);
                sqlFilePath = KGS_SQL_ID + File.separator + parts[1];
            }
            sqlFilePathList.add(WEB_ROOT + File.separator + sqlFilePath);
            sqlFilePathList.add(WEB_ROOT + File.separator + DML_SQL_FILE_PATH);

            // 执行数据库创建表脚本&执行资源数据脚本
            try {
                if (!DataSourceUtil.isTableExist(dataSource, TABLE_NAME_CHECK)) {
                    DBScriptRunner.runScript(dataSource, sqlFilePathList);
                } else {
                    log.warn("The KGS business table already exists, initialization script will no longer be executed");
                }
            } catch (Exception e) {
                throw ManagementInternalError.SQL_SCRIPT_RUN_ERROR.toException(e);
            }
            // 更新数据源
            dbSwitchConfigService.saveOrUpdateDataSourceConfig(slotDataSourceConfig.getDatasource(), slotDataSourceConfig.getDruid(),
                slotDataSourceConfig.getSharding().isReadWriteEnabled());
        }
    }
}
