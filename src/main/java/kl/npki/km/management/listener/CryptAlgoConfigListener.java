package kl.npki.km.management.listener;

import kl.nbase.config.RefreshableConfig;
import kl.nbase.config.listener.ConfigRefreshListener;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.timer.client.ITimerClient;
import kl.nbase.timer.job.ITimerJob;
import kl.nbase.timer.job.TimerJobFactory;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.service.common.SpringBeanRegister;
import kl.npki.km.core.common.constants.KmJobEnum;
import kl.npki.km.core.configs.KeyLimitConfig;
import kl.npki.km.core.configs.wrapper.KmConfigWrapper;
import kl.npki.km.management.timer.KeyGenJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 密码算法配置变更监听器
 *
 * <AUTHOR>
 * @date 2024/4/9
 */
public class CryptAlgoConfigListener implements ConfigRefreshListener {

    private static final Logger log = LoggerFactory.getLogger(CryptAlgoConfigListener.class);

    @Resource
    private ITimerClient timerClient;

    @Override
    public void onChange(RefreshableConfig refreshableConfig, RefreshableConfig refreshableConfig1, Set<String> set) {
        Set<String> supportedKeyTypes = KmConfigWrapper.getAsymmetricAndPostQuantumKeyTypesWithoutSM9Set();
        // 停止所有密钥生成定时任务
        stopAllKeyGenJobs();
        // 初始化密钥配置
        KmConfigWrapper.getKeyConf().initKeyLimitConf();
        List<KeyLimitConfig> keyLimits = KmConfigWrapper.getKeyConf().getKeyLimits();

        // 过滤出支持的密钥类型
        filterSupportedKeyTypes(keyLimits, supportedKeyTypes);
        // 根据配置动态初始化各个密钥类型定时任务
        registerKeyGenJobsIfNotExist(supportedKeyTypes);
        // 启动已配置的密钥生成定时任务
        startKeyGenJobs(keyLimits);
        // 保存配置
        ConfigHolder.get().save(KmConfigWrapper.getKeyConf(), false);
    }

    private void stopAllKeyGenJobs() {
        Set<String> keyGenJobSet = TimerJobFactory.getAllJobId().stream()
            .filter(jobId -> jobId.startsWith(KmJobEnum.KEY_GEN.getId()))
            .collect(Collectors.toSet());
        if (!keyGenJobSet.isEmpty()) {
            keyGenJobSet.forEach(jobId -> {
                try {
                    timerClient.stop(jobId);
                } catch (Exception e) {
                    log.error("定时任务停止失败，任务id={}", jobId, e);
                }
            });
        }
    }

    private void filterSupportedKeyTypes(List<KeyLimitConfig> keyLimits, Set<String> supportedKeyTypes) {
        // 通过流式操作过滤出不在支持列表中的项
        keyLimits.removeIf(keyLimit -> !supportedKeyTypes.contains(keyLimit.getKeyType()));
    }

    private void registerKeyGenJobsIfNotExist(Set<String> supportedAlgorithms) {
        supportedAlgorithms.forEach(algo -> {
            String beanName = KmJobEnum.KEY_GEN.getId() + algo;
            ITimerJob iTimerJob = TimerJobFactory.get(beanName);
            if (iTimerJob == null) {
                SpringBeanRegister.registerBean(beanName, KeyGenJob.class, new AsymAlgo(algo));
                TimerJobFactory.putJob((KeyGenJob) SpringBeanRegister.getBean(beanName));
            }
        });
    }

    private void startKeyGenJobs(List<KeyLimitConfig> keyLimits) {
        for (KeyLimitConfig keyLimit : keyLimits) {
            if (!keyLimit.isEnable()) {
                keyLimit.setEnable(true);
                keyLimit.setLimit(SystemUtil.isDeployed() ? 1000 : 100);
            }
            String jobId = KmJobEnum.KEY_GEN.getId() + keyLimit.getKeyType();
            timerClient.run(jobId);
        }
        if (KmConfigWrapper.getKeyConf().isCacheAutoSupply()) {
            // 刷新缓存填充任务
            timerClient.refresh(KmJobEnum.KEY_CACHE_ADD.getId());
        }
    }

}
