package kl.npki.km.management.listener;

import kl.nbase.config.RefreshableConfig;
import kl.nbase.config.listener.ConfigRefreshListener;
import kl.npki.km.core.configs.KmFeatureConfig;
import kl.npki.km.management.kgs.KgsServiceInitializer;

import java.util.Set;

/**
 * KM功能配置监听器
 *
 * <AUTHOR>
 * @since 2025/6/28
 */
public class KmFeatureConfigListener implements ConfigRefreshListener {

    private final KgsServiceInitializer kgsServiceInitializer;

    public KmFeatureConfigListener(KgsServiceInitializer kgsServiceInitializer) {
        this.kgsServiceInitializer = kgsServiceInitializer;
    }

    @Override
    public void onChange(RefreshableConfig before, RefreshableConfig after, Set<String> changedFields) {
        KmFeatureConfig oldConfig = (KmFeatureConfig) before;
        KmFeatureConfig newConfig = (KmFeatureConfig) after;
        handleSm9ServiceToggle(oldConfig.isEnableIbcKm(), newConfig.isEnableIbcKm());
    }

    private void handleSm9ServiceToggle(boolean wasEnabled, boolean isEnabled) {
        if (!wasEnabled && isEnabled) {
            kgsServiceInitializer.enableSm9Service();
        } else if (wasEnabled && !isEnabled) {
            kgsServiceInitializer.disableSm9Service();
        }
    }

}
