package kl.npki.km.management.exception;

import kl.nbase.exception.interfaces.IValidationError;
import kl.npki.km.management.constant.KMMgmtI18N;

/**
 * <AUTHOR>
 * Created on 2022/08/24 11:16
 */
public enum KmManagementValidationError implements IValidationError, ErrorCode {

    PARAM_ERROR("000", "参数错误"),
    CURRENT_KEY_NOT_FOUND_ERROR("101","此ID当前库密钥未找到！"),
    HISTORY_KEY_NOT_FOUND_ERROR("102","此ID历史库密钥未找到！"),
    KEY_TRACE_NOT_FOUND_ERROR("103","此ID的密钥轨迹未找到！"),
    ;

    private final String code;
    private final String desc;

    KmManagementValidationError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getMessageKey() {
        // 在当前的异常枚举定义的包下面，找LocalStrings，key为异常枚举的名称
        return KMMgmtI18N.KM_MGR_I18N_MESSAGE_KEY_PREFIX + this.name();
    }

}
