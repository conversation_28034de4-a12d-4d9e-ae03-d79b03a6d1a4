package kl.npki.km.management.exception;

import kl.nbase.exception.interfaces.IInternalError;
import kl.npki.km.management.constant.KMMgmtI18N;

/**
 * <AUTHOR>
 * Created on 2022/08/24 11:17
 */
public enum KmManagementInternalError implements IInternalError, ErrorCode {
    SYSTEM_ERROR("000", "系统错误"),
    SM9_KEY_SERVICE_ENABLED_ERROR("001", "SM9密钥服务启用失败"),
    ;

    private final String code;
    private final String desc;

    KmManagementInternalError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getMessageKey() {
        // 在当前的异常枚举定义的包下面，找LocalStrings，key为异常枚举的名称
        return KMMgmtI18N.KM_MGR_I18N_MESSAGE_KEY_PREFIX + this.name();
    }
}
