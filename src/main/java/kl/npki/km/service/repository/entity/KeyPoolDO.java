package kl.npki.km.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.npki.base.service.repository.entity.IntegrityProtectedDO;
import kl.tools.dbtool.constant.DataType;


/**
 * <p>
 * 密钥备用库
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@TableName("T_KEY_POOL")
@KlDbTable(tbName = "T_KEY_POOL",
        indexes = {@Index(name = "IDX_TKP_TYPE", columnList = {"KEY_TYPE"})}
)
public class KeyPoolDO extends IntegrityProtectedDO {

    private static final long serialVersionUID = 1L;

    /**
     * 密钥类型
     */
    @KlDbField(type = DataType.VARCHAR, size = 64, notNull = true)
    private String keyType;

    /**
     * 公钥值
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 4000, notNull = true)
    private String publicKey;

    /**
     * 加密私钥值
     */
    @DataFullField
    @KlDbField(type = DataType.CLOB, notNull = true)
    private String privateKey;

    /**
     * 主密钥标识
     */
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20, notNull = true)
    private Long mainKeyId;

    /**
     * 公钥hash
     */
    @KlDbField(type = DataType.VARCHAR, size = 512, notNull = true)
    private String keyIndex;

    /**
     * 扩展项1
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String ext1;

    /**
     * 扩展项2
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String ext2;

    /**
     * 扩展项3
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String ext3;

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public Long getMainKeyId() {
        return mainKeyId;
    }

    public void setMainKeyId(Long mainKeyId) {
        this.mainKeyId = mainKeyId;
    }

    public String getKeyIndex() {
        return keyIndex;
    }

    public void setKeyIndex(String keyIndex) {
        this.keyIndex = keyIndex;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    @Override
    public String toString() {
        return "KeyPoolDO{" +
            "keyType='" + keyType + '\'' +
            ", publicKey='" + publicKey + '\'' +
            ", privateKey='" + privateKey + '\'' +
            ", mainKeyId=" + mainKeyId +
            ", keyIndex='" + keyIndex + '\'' +
            ", ext1='" + ext1 + '\'' +
            ", ext2='" + ext2 + '\'' +
            ", ext3='" + ext3 + '\'' +
            ", fullDataHash='" + fullDataHash + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
