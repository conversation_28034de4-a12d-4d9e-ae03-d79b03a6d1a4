package kl.npki.km.service.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.bean.convert.ConvertService;
import kl.npki.km.core.biz.sks.model.SksKeyBatchSearchInfo;
import kl.npki.km.core.biz.sks.model.SksKeyEntity;
import kl.npki.km.core.common.constants.SksKey;
import kl.npki.km.core.repository.ISksKeyRepository;
import kl.npki.km.service.repository.entity.KeyCurrentDO;
import kl.npki.km.service.repository.service.IKeyCurrentService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/11/25 15:28
 * @Description: sks密钥仓库
 */
@Component
public class SksKeyRepositoryImpl implements ISksKeyRepository {

    private static final Logger log = LoggerFactory.getLogger(SksKeyRepositoryImpl.class);

    @Resource
    private IKeyCurrentService keyCurrentService;

    @Resource
    private ConvertService convertService;

    @Override
    public boolean isExist(String pubKeyIndex) {
        // km使用sn作为分表键，目前sksKeyHash也会存入sn字段，使用分表键查询，提高效率
        return keyCurrentService.isExistBySN(pubKeyIndex);
    }

    @Override
    public SksKeyEntity searchEntityByClientKeyHash(String clientKeyHash) {
        KeyCurrentDO keyCurrentDO = keyCurrentService.searchKeyBySnAndType(clientKeyHash, SksKey.SKS.getName());
        if (ObjectUtils.isEmpty(keyCurrentDO)) {
            return null;
        }
        return convertService.convert(keyCurrentDO, SksKeyEntity.class);
    }

    @Override
    public long countPrivateKey(SksKeyBatchSearchInfo sksKeyBatchSearchInfo) {
        return keyCurrentService.countSksKey(sksKeyBatchSearchInfo);
    }

    @Override
    public List<SksKeyEntity> listSksKeyEntity(SksKeyBatchSearchInfo batchSearchInfo) {
        List<KeyCurrentDO> keyCurrentDOList = keyCurrentService.listSksKeyEntity(batchSearchInfo);
        if (CollectionUtils.isEmpty(keyCurrentDOList)) {
            return Collections.emptyList();
        }
        return convertService.convert(keyCurrentDOList, SksKeyEntity.class);
    }

    @Override
    public Optional<Long> saveSksKey(SksKeyEntity sksKeyEntity) {
        KeyCurrentDO keyCurrentDO = convertService.convert(sksKeyEntity, KeyCurrentDO.class);
        try {
            Long id = keyCurrentService.escrowKey(keyCurrentDO);
            return Optional.of(id);
        } catch (DuplicateKeyException e) {
            boolean exist = isExist(keyCurrentDO.getKeyIndex());
            if (exist) {
                log.warn("当前保存的sks密钥已经存在，clientHash = 【{}】", sksKeyEntity.getClientKeyHash());
                return Optional.empty();
            }
            throw e;
        }
    }


    @Override
    public boolean updateStatus(SksKeyEntity sksKeyEntity) {
        LambdaUpdateWrapper<KeyCurrentDO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(KeyCurrentDO::getEncCertSn, sksKeyEntity.getClientKeyHash());
        updateWrapper.eq(KeyCurrentDO::getKeyType, SksKey.SKS.getName());

        updateWrapper.set(KeyCurrentDO::getKeyStatus, sksKeyEntity.getStatus());
        updateWrapper.set(KeyCurrentDO::getUpdateTime, sksKeyEntity.getUpdateTime());
        updateWrapper.set(KeyCurrentDO::getValidEnd, sksKeyEntity.getValidEnd());

        KeyCurrentDO currentDO = convertService.convert(sksKeyEntity, KeyCurrentDO.class);
        // 手动更新完整性保护值
        updateWrapper.set(KeyCurrentDO::getFullDataHash, genDataHashValue(currentDO));
        return keyCurrentService.update(updateWrapper);
    }

    @Override
    public boolean removeSelfCheckPrivateKey(SksKeyEntity sksKeyEntity) {
        LambdaQueryWrapper<KeyCurrentDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KeyCurrentDO::getEncCertSn, sksKeyEntity.getClientKeyHash());
        queryWrapper.eq(KeyCurrentDO::getMediumSn, sksKeyEntity.getMediaId());

        return keyCurrentService.remove(queryWrapper);
    }
}
