package kl.npki.km.service.repository.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.km.service.repository.entity.CaInfoDO;
import kl.npki.km.service.repository.mapper.CaInfoMapper;
import kl.npki.km.service.repository.service.ICaInfoService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Service
public class CaInfoServiceImpl extends KlServiceImpl<CaInfoMapper, CaInfoDO> implements ICaInfoService {
    @Override
    public CaInfoDO searchCaById(Long caId) {
        return getById(caId);
    }

    @Override
    public CaInfoDO searchCaExceptRevoked(String name) {
        LambdaQueryWrapper<CaInfoDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CaInfoDO::getCaName, name);
        queryWrapper.ne(CaInfoDO::getCaStatus, EntityStatus.REVOKED.getId());
        return getOne(queryWrapper);
    }

    @Override
    public CaInfoDO searchCa(String certSn, String keyHash) {
        LambdaQueryWrapper<CaInfoDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CaInfoDO::getKeyIndex, keyHash);
        queryWrapper.eq(CaInfoDO::getCertSn, certSn);
        return getOne(queryWrapper);
    }

    @Override
    public List<CaInfoDO> searchCaList() {
        return list();
    }

    @Override
    public Long insertCA(CaInfoDO caInfoDO) {
        save(caInfoDO);
        return caInfoDO.getId();
    }

    @Override
    public boolean updateCA(CaInfoDO caInfoDO) {
        return updateById(caInfoDO);
    }

    @Override
    public Long countByCaStatus(int caStatus) {
        LambdaQueryWrapper<CaInfoDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CaInfoDO::getCaStatus, caStatus);
        return count(queryWrapper);
    }

    @Override
    public List<String> getCaNameListExceptRevoked() {
        LambdaQueryWrapper<CaInfoDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ne(CaInfoDO::getCaStatus, EntityStatus.REVOKED.getId());
        queryWrapper.select(CaInfoDO::getCaName);

        return listObjs(queryWrapper, Object::toString);
    }

    @Override
    public Map<Long, String> batchGetCaNames(Set<Long> caIds) {
        if (caIds.isEmpty()) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<CaInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CaInfoDO::getId, CaInfoDO::getCaName)
            .in(CaInfoDO::getId, caIds);
        List<CaInfoDO> caInfoList = this.list(queryWrapper);

        return caInfoList.stream()
            .collect(Collectors.toMap(CaInfoDO::getId, CaInfoDO::getCaName));
    }

    @Override
    public boolean checkExist(String sn, String keyHash, String caName) {
        LambdaQueryWrapper<CaInfoDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CaInfoDO::getCertSn, sn)
                .or()
                .eq(CaInfoDO::getKeyIndex, keyHash)
                .or()
                .eq(CaInfoDO::getCaName, caName);
        return count(queryWrapper) != 0;
    }
}
