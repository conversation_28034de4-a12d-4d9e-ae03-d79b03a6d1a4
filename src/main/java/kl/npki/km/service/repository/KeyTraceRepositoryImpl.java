package kl.npki.km.service.repository;

import kl.nbase.bean.convert.ConvertService;
import kl.npki.km.core.biz.key.model.KeyTraceEntity;
import kl.npki.km.core.repository.IKeyTraceRepositoryImpl;
import kl.npki.km.service.repository.entity.KeyTraceDO;
import kl.npki.km.service.repository.service.IKeyTraceService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class KeyTraceRepositoryImpl implements IKeyTraceRepositoryImpl {

    @Resource
    private IKeyTraceService keyTraceService;

    @Resource
    private ConvertService convertService;

    @Override
    public void insert(KeyTraceEntity keyTraceEntity) {
        KeyTraceDO keyTraceDO = convertService.convert(keyTraceEntity, KeyTraceDO.class);
        keyTraceService.insert(keyTraceDO);
    }

    @Override
    public List<KeyTraceEntity> searchByEntitySn(String entitySn, String keyType) {
        List<KeyTraceDO> keyTraceDOList = keyTraceService.searchByEntitySn(entitySn, keyType);
        return convertService.convert(keyTraceDOList, KeyTraceEntity.class);
    }
}
