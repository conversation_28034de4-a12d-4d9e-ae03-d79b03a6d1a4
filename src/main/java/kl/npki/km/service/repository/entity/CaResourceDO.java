package kl.npki.km.service.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.npki.base.service.repository.entity.IntegrityProtectedDO;
import kl.tools.dbtool.constant.DataType;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@TableName("t_ca_resource")
@KlDbTable(tbName = "t_ca_resource")
public class CaResourceDO extends IntegrityProtectedDO {

    private static final long serialVersionUID = 1L;

    /**
     * ca标识
     */
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20)
    private Long caId;

    /**
     * 密钥类型
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String keyType;

    /**
     * 剩余密钥数量
     */
    @KlDbField(type = DataType.INTEGER, size = 10)
    private Integer keyNum;

    /**
     * 总数量限制
     */
    @KlDbField(type = DataType.INTEGER, size = 10)
    private Integer limitNum;

    /**
     * 警告数量
     */
    @KlDbField(type = DataType.INTEGER, size = 10)
    private Integer warningNum;

    /**
     * 资源状态
     */
    @KlDbField(type = DataType.TINYINT, size = 1, notNull = true)
    private Integer resourceStatus;

    public Long getCaId() {
        return caId;
    }

    public void setCaId(Long caId) {
        this.caId = caId;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public Integer getKeyNum() {
        return keyNum;
    }

    public void setKeyNum(Integer keyNum) {
        this.keyNum = keyNum;
    }

    public Integer getLimitNum() {
        return limitNum;
    }

    public void setLimitNum(Integer limitNum) {
        this.limitNum = limitNum;
    }

    public Integer getWarningNum() {
        return warningNum;
    }

    public void setWarningNum(Integer warningNum) {
        this.warningNum = warningNum;
    }

    public Integer getResourceStatus() {
        return resourceStatus;
    }

    public void setResourceStatus(Integer resourceStatus) {
        this.resourceStatus = resourceStatus;
    }

    @Override
    public String toString() {
        return "CaResourceDO{" +
            "caId=" + caId +
            ", keyType='" + keyType + '\'' +
            ", keyNum=" + keyNum +
            ", limitNum=" + limitNum +
            ", warningNum=" + warningNum +
            ", resourceStatus=" + resourceStatus +
            ", fullDataHash='" + fullDataHash + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
