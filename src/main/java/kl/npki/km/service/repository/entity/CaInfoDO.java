package kl.npki.km.service.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.npki.base.service.repository.entity.IntegrityProtectedDO;
import kl.tools.dbtool.constant.DataType;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@TableName("t_ca_info")
@KlDbTable(tbName = "t_ca_info")
public class CaInfoDO extends IntegrityProtectedDO {

    private static final long serialVersionUID = 1L;

    /**
     * ca名称
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128)
    private String caName;

    /**
     * ca版本
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String caVersion;

    /**
     * CA接入方式
     * 1-手动接入
     * 2-在线接入
     */
    @DataFullField
    @KlDbField(type = DataType.TINYINT, size = 1)
    private Integer caAccessType;

    /**
     * SM2密钥类型时使用的标准规范版本
     */
    @DataFullField
    @KlDbField(type = DataType.TINYINT, size = 1)
    private Integer sm2KeyStandardVersion;

    /**
     * 证书值
     */
    @DataFullField
    @KlDbField(type = DataType.CLOB)
    private String idCert;

    /**
     * 证书sn
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128)
    private String certSn;

    /**
     * 证书公钥hash
     */
    @KlDbField(type = DataType.VARCHAR, size = 128)
    private String keyIndex;

    /**
     * 使用介质序列号
     */
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1)
    private Integer useMediumSn;

    /**
     * ca状态
     */
    @KlDbField(type = DataType.TINYINT, size = 1, notNull = true)
    private Integer caStatus;

    /**
     * 密钥有效期
     */
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 10)
    private Integer limitYear;

    /**
     * IP白名单
     */
    @KlDbField(type = DataType.VARCHAR, size = 128)
    private String ipAllowList;

    /**
     * 备注
     */
    @KlDbField(type = DataType.VARCHAR, size = 512)
    private String remark;

    /**
     * 扩展项1
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String ext1;

    /**
     * 扩展项2
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String ext2;

    /**
     * 扩展项3
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String ext3;

    public String getCaName() {
        return caName;
    }

    public void setCaName(String caName) {
        this.caName = caName;
    }

    public String getCaVersion() {
        return caVersion;
    }

    public void setCaVersion(String caVersion) {
        this.caVersion = caVersion;
    }

    public String getIdCert() {
        return idCert;
    }

    public void setIdCert(String idCert) {
        this.idCert = idCert;
    }

    public String getCertSn() {
        return certSn;
    }

    public void setCertSn(String certSn) {
        this.certSn = certSn;
    }

    public String getKeyIndex() {
        return keyIndex;
    }

    public void setKeyIndex(String keyIndex) {
        this.keyIndex = keyIndex;
    }

    public Integer getUseMediumSn() {
        return useMediumSn;
    }

    public void setUseMediumSn(Integer useMediumSn) {
        this.useMediumSn = useMediumSn;
    }

    public Integer getCaStatus() {
        return caStatus;
    }

    public void setCaStatus(Integer caStatus) {
        this.caStatus = caStatus;
    }

    public Integer getLimitYear() {
        return limitYear;
    }

    public void setLimitYear(Integer limitYear) {
        this.limitYear = limitYear;
    }

    public String getIpAllowList() {
        return ipAllowList;
    }

    public void setIpAllowList(String ipAllowList) {
        this.ipAllowList = ipAllowList;
    }

    public Integer getCaAccessType() {
        return caAccessType;
    }

    public void setCaAccessType(Integer caAccessType) {
        this.caAccessType = caAccessType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    public Integer getSm2KeyStandardVersion() {
        return sm2KeyStandardVersion;
    }

    public void setSm2KeyStandardVersion(Integer sm2KeyStandardVersion) {
        this.sm2KeyStandardVersion = sm2KeyStandardVersion;
    }

    @Override
    public String toString() {
        return "CaInfoDO{" +
            "caName='" + caName + '\'' +
            ", caVersion='" + caVersion + '\'' +
            ", caAccessType=" + caAccessType +
            ", sm2KeyStandardVersion=" + sm2KeyStandardVersion +
            ", idCert='" + idCert + '\'' +
            ", certSn='" + certSn + '\'' +
            ", keyIndex='" + keyIndex + '\'' +
            ", useMediumSn=" + useMediumSn +
            ", caStatus=" + caStatus +
            ", limitYear=" + limitYear +
            ", ipAllowList='" + ipAllowList + '\'' +
            ", remark='" + remark + '\'' +
            ", ext1='" + ext1 + '\'' +
            ", ext2='" + ext2 + '\'' +
            ", ext3='" + ext3 + '\'' +
            ", fullDataHash='" + fullDataHash + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
