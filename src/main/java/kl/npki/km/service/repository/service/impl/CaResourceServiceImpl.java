package kl.npki.km.service.repository.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.km.service.repository.entity.CaResourceDO;
import kl.npki.km.service.repository.mapper.CaResourceMapper;
import kl.npki.km.service.repository.service.ICaResourceService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Service
public class CaResourceServiceImpl extends KlServiceImpl<CaResourceMapper, CaResourceDO> implements ICaResourceService {

    @Override
    public Long insertCaResources(CaResourceDO caResource) {
        save(caResource);
        return caResource.getId();
    }

    @Override
    public boolean updateCaResources(CaResourceDO caResource) {
        return updateById(caResource);
    }

    @Override
    public boolean deleteCaResources(Long caId) {
        LambdaUpdateWrapper<CaResourceDO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(CaResourceDO::getCaId, caId);
        updateWrapper.eq(CaResourceDO::getResourceStatus, EntityStatus.NORMAL.getId());
        updateWrapper.set(CaResourceDO::getResourceStatus, EntityStatus.REVOKED.getId());
        updateWrapper.set(CaResourceDO::getUpdateTime, LocalDateTime.now());
        return update(updateWrapper);
    }

    @Override
    public List<CaResourceDO> searchCaResources(Long caId) {
        LambdaQueryWrapper<CaResourceDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CaResourceDO::getCaId, caId);
        queryWrapper.eq(CaResourceDO::getResourceStatus, EntityStatus.NORMAL.getId());

        return list(queryWrapper);
    }

    @Override
    public List<CaResourceDO> searchAllCaResources() {
        LambdaQueryWrapper<CaResourceDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CaResourceDO::getResourceStatus, EntityStatus.NORMAL.getId());
        queryWrapper.orderByAsc(CaResourceDO::getCaId);
        return list(queryWrapper);
    }

    @Override
    public boolean spendKeyNum(Long id, Integer num) {
        LambdaUpdateWrapper<CaResourceDO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(CaResourceDO::getId, id);
        updateWrapper.eq(CaResourceDO::getResourceStatus, EntityStatus.NORMAL.getId());
        updateWrapper.setSql("key_num = key_num + " + num);
        return update(updateWrapper);
    }

}
