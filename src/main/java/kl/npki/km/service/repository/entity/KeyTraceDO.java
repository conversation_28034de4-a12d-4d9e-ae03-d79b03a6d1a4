package kl.npki.km.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.npki.base.service.repository.entity.IntegrityProtectedDO;
import kl.tools.dbtool.constant.DataType;


/**
 * 密钥历史轨迹表
 * <AUTHOR>
 */
@TableName("T_KEY_TRACE")
@KlDbTable(tbName = "T_KEY_TRACE",
    indexes = {@Index(name = "IDX_TKT_SN_KEY", columnList = {"ENTITY_SN","KEY_TYPE"})},
    multipleTableId = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15"}
)
public class KeyTraceDO extends IntegrityProtectedDO {

    private static final long serialVersionUID = 1882852729421211693L;

    /**
     * 操作者的用户id
     */
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20)
    private Long userId;

    /**
     * 操作者名称
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String operatorName;

    /**
     * 发起者IP
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String sourceIp;

    /**
     * 调用方式
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String invocationType;

    /**
     * 变更事件/动作/业务类型
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String bizName;

    /**
     * 业务流水号
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String bizId;

    /**
     * 实体序列号，用于分表
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128, notNull = true)
    private String entitySn;

    /**
     * 密钥类型
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, notNull = true)
    private String keyType;
    /**
     * json格式的实体数据内容
     */
    @DataFullField
    @KlDbField(type = DataType.CLOB)
    private String entityInfo;

    /**
     * 操作结果
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String operatorResult;

    /**
     * 实体状态
     */
    @DataFullField
    @KlDbField(type = DataType.TINYINT, size = 1)
    private int entityStatus;

    /**
     * 扩展项1
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String ext1;

    /**
     * 扩展项2
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String ext2;

    /**
     * 扩展项3
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String ext3;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getSourceIp() {
        return sourceIp;
    }

    public void setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
    }

    public String getInvocationType() {
        return invocationType;
    }

    public void setInvocationType(String invocationType) {
        this.invocationType = invocationType;
    }

    public String getBizName() {
        return bizName;
    }

    public void setBizName(String bizName) {
        this.bizName = bizName;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getEntitySn() {
        return entitySn;
    }

    public void setEntitySn(String entitySn) {
        this.entitySn = entitySn;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public String getEntityInfo() {
        return entityInfo;
    }

    public void setEntityInfo(String entityInfo) {
        this.entityInfo = entityInfo;
    }

    public String getOperatorResult() {
        return operatorResult;
    }

    public void setOperatorResult(String operatorResult) {
        this.operatorResult = operatorResult;
    }

    public int getEntityStatus() {
        return entityStatus;
    }

    public void setEntityStatus(int entityStatus) {
        this.entityStatus = entityStatus;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    @Override
    public String toString() {
        return "KeyTraceDO{" +
            "userId=" + userId +
            ", operatorName='" + operatorName + '\'' +
            ", sourceIp='" + sourceIp + '\'' +
            ", invocationType='" + invocationType + '\'' +
            ", bizName='" + bizName + '\'' +
            ", bizId='" + bizId + '\'' +
            ", entitySn='" + entitySn + '\'' +
            ", keyType='" + keyType + '\'' +
            ", entityInfo='" + entityInfo + '\'' +
            ", operatorResult='" + operatorResult + '\'' +
            ", entityStatus=" + entityStatus +
            ", ext1='" + ext1 + '\'' +
            ", ext2='" + ext2 + '\'' +
            ", ext3='" + ext3 + '\'' +
            ", fullDataHash='" + fullDataHash + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
