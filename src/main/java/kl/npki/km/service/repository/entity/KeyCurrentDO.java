package kl.npki.km.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.npki.base.service.repository.entity.IntegrityProtectedDO;
import kl.tools.dbtool.constant.DataType;

import java.time.LocalDateTime;


/**
 * <p>
 * 当前密钥库
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@TableName("t_key_current")
@KlDbTable(tbName = "t_key_current",
        // 业务唯一索引，必须建设
        indexes = {@Index(name = "idx_tkc_sn",columnList = {"enc_cert_sn","key_type"}, unique = true),
                // 查询待归档密钥场景使用到
                @Index(name = "idx_tkc_key_status", columnList = {"key_status"}),
                // 密钥查询、使用统计场景使用到
                @Index(name = "idx_tkc_ca_key_type", columnList = {"ca_id,key_type"}),
                // 密钥库查询字段使用
                @Index(name = "idx_tkc_subject", columnList = {"subject"}),
                // 密钥类型统计场景、批量同步SKS密钥场景会用到
                @Index(name = "idx_tkc_key_update", columnList = {"key_type,update_time"})},
    multipleTableId = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15"}
)
public class KeyCurrentDO extends IntegrityProtectedDO {

    private static final long serialVersionUID = 1L;

    /**
     * 密钥类型
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, notNull = true)
    private String keyType;
    /**
     * 用户加密证书序列号
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128, notNull = true)
    private String encCertSn;
    /**
     * 公钥数据
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 4000)
    private String publicKey;
    /**
     * 主密钥加密的私钥数据
     */
    @DataFullField
    @KlDbField(type = DataType.CLOB, notNull = true)
    private String privateKey;
    /**
     * 主密钥唯一标识
     */
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20)
    private Long mainKeyId;
    /**
     * ca唯一标识
     */
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20)
    private Long caId;
    /**
     * 介质序列号
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128)
    private String mediumSn;
    /**
     * 主题项
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String subject;
    /**
     * 密钥对生效时间
     */
    @KlDbField(type = DataType.DATE, size = 6)
    private LocalDateTime validStart;
    /**
     * 密钥对失效时间
     */
    @KlDbField(type = DataType.DATE, size = 6)
    private LocalDateTime validEnd;

    /**
     * 用户扩展信息
     */
    @KlDbField(type = DataType.VARCHAR, size = 1024)
    private String userInfoEx;
    /**
     * 公钥值Hash
     */
    @KlDbField(type = DataType.VARCHAR, size = 512)
    private String keyIndex;

    /**
     * 密钥状态
     */
    @DataFullField
    @KlDbField(type = DataType.TINYINT, size = 1, notNull = true)
    private Integer keyStatus;

    /**
     * 扩展项1
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String ext1;

    /**
     * 扩展项2
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String ext2;

    /**
     * 扩展项3
     */
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String ext3;

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public String getEncCertSn() {
        return encCertSn;
    }

    public void setEncCertSn(String encCertSn) {
        this.encCertSn = encCertSn;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public Long getMainKeyId() {
        return mainKeyId;
    }

    public void setMainKeyId(Long mainKeyId) {
        this.mainKeyId = mainKeyId;
    }

    public Long getCaId() {
        return caId;
    }

    public void setCaId(Long caId) {
        this.caId = caId;
    }

    public String getMediumSn() {
        return mediumSn;
    }

    public void setMediumSn(String mediumSn) {
        this.mediumSn = mediumSn;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getKeyIndex() {
        return keyIndex;
    }

    public void setKeyIndex(String keyIndex) {
        this.keyIndex = keyIndex;
    }

    public LocalDateTime getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(LocalDateTime validEnd) {
        this.validEnd = validEnd;
    }

    public LocalDateTime getValidStart() {
        return validStart;
    }

    public void setValidStart(LocalDateTime validStart) {
        this.validStart = validStart;
    }

    public String getUserInfoEx() {
        return userInfoEx;
    }

    public void setUserInfoEx(String userInfoEx) {
        this.userInfoEx = userInfoEx;
    }

    public Integer getKeyStatus() {
        return keyStatus;
    }

    public void setKeyStatus(Integer keyStatus) {
        this.keyStatus = keyStatus;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    @Override
    public String toString() {
        return "KeyCurrentDO{" +
            "keyType='" + keyType + '\'' +
            ", encCertSn='" + encCertSn + '\'' +
            ", publicKey='" + publicKey + '\'' +
            ", privateKey='" + privateKey + '\'' +
            ", mainKeyId=" + mainKeyId +
            ", caId=" + caId +
            ", mediumSn='" + mediumSn + '\'' +
            ", subject='" + subject + '\'' +
            ", validStart=" + validStart +
            ", validEnd=" + validEnd +
            ", userInfoEx='" + userInfoEx + '\'' +
            ", keyIndex='" + keyIndex + '\'' +
            ", keyStatus=" + keyStatus +
            ", ext1='" + ext1 + '\'' +
            ", ext2='" + ext2 + '\'' +
            ", ext3='" + ext3 + '\'' +
            ", fullDataHash='" + fullDataHash + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
