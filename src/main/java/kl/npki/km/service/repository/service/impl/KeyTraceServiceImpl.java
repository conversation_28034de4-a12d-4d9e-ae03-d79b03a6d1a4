package kl.npki.km.service.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.npki.km.service.repository.entity.KeyTraceDO;
import kl.npki.km.service.repository.mapper.KeyTraceMapper;
import kl.npki.km.service.repository.service.IKeyTraceService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class KeyTraceServiceImpl extends KlServiceImpl<KeyTraceMapper, KeyTraceDO> implements IKeyTraceService {
    @Override
    public void insert(KeyTraceDO keyTraceDO) {
        save(keyTraceDO);
    }

    @Override
    public List<KeyTraceDO> searchByEntitySn(String entitySn, String keyType) {
        LambdaQueryWrapper<KeyTraceDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KeyTraceDO::getEntitySn, entitySn);
        if(StringUtils.isNotBlank(keyType)){
            queryWrapper.eq(KeyTraceDO::getKeyType, keyType);
        }
        queryWrapper.orderByDesc(KeyTraceDO::getCreateTime);
        return list(queryWrapper);
    }

}
