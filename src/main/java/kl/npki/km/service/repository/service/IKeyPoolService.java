package kl.npki.km.service.repository.service;

import com.baomidou.mybatisplus.extension.service.IService;
import kl.npki.km.service.repository.entity.KeyPoolDO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
public interface IKeyPoolService extends IService<KeyPoolDO> {
    /**
     * 获取对应类型备用密钥数量
     *
     * @param keyType
     * @return
     */
    Integer count(String keyType);

    /**
     * 保存备用密钥
     *
     * @param keyEntity
     * @return
     */
    Long insertKeyPair(KeyPoolDO keyEntity);

    /**
     * 查询指定数量的备用密钥
     *
     * @param count
     * @param keyType
     * @return
     */
    List<KeyPoolDO> searchKeyEntityList(int count, String keyType);


    /**
     * 批量删除加载到缓存中的备用密钥
     *
     * @param keyIds
     */
    boolean deleteUsedKeys(List<Long> keyIds);


    /**
     * 查询密钥对是否存在
     *
     * @param pubKeyIndex 公钥索引
     * @return
     */
    boolean isExist(String pubKeyIndex);
}
