package kl.npki.km.service.repository;

import kl.nbase.bean.convert.ConvertService;
import kl.npki.base.core.annotation.KlTransactional;
import kl.npki.km.core.biz.key.model.SpareKeyEntity;
import kl.npki.km.core.repository.IKeyPoolRepository;
import kl.npki.km.service.repository.entity.KeyPoolDO;
import kl.npki.km.service.repository.service.IKeyPoolService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/25 19:15
 * @Description:
 */
@Component
public class KeyPoolRepositoryImpl implements IKeyPoolRepository {

    @Resource
    private IKeyPoolService keyPoolService;

    @Resource
    private ConvertService convertService;

    @Override
    public Integer count(String keyType) {
        return keyPoolService.count(keyType);
    }

    @Override
    public Long insertKeyPair(SpareKeyEntity keyEntity) {
        KeyPoolDO keyPool = convertService.convert(keyEntity, KeyPoolDO.class);
        return keyPoolService.insertKeyPair(keyPool);
    }

    @Override
    public List<SpareKeyEntity> searchKeyEntityList(int count, String keyType) {
        List<KeyPoolDO> keyPoolDOList = keyPoolService.searchKeyEntityList(count, keyType);
        if(CollectionUtils.isEmpty(keyPoolDOList)){
            return Collections.emptyList();
        }
        return convertService.convert(keyPoolDOList, SpareKeyEntity.class);
    }

    @KlTransactional
    @Override
    public boolean deleteUsedKeys(List<Long> keyIds) {
        return keyPoolService.deleteUsedKeys(keyIds);
    }

    @Override
    public boolean deleteById(Long keyId) {
        return keyPoolService.removeById(keyId);
    }

    @Override
    public boolean isExist(String pubKeyIndex) {
        return keyPoolService.isExist(pubKeyIndex);
    }

    @Override
    public boolean checkDataIntegrity(Object dataObj) {
        if (dataObj instanceof SpareKeyEntity) {
            dataObj = convertService.convert(dataObj, KeyPoolDO.class);
        }
        return IKeyPoolRepository.super.checkDataIntegrity(dataObj);
    }
}
