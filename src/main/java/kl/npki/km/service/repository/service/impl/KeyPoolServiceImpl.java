package kl.npki.km.service.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.npki.km.service.repository.entity.KeyPoolDO;
import kl.npki.km.service.repository.mapper.KeyPoolMapper;
import kl.npki.km.service.repository.service.IKeyPoolService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Service
public class KeyPoolServiceImpl extends KlServiceImpl<KeyPoolMapper, KeyPoolDO> implements IKeyPoolService {

    @Override
    public Integer count(String keyType) {
        LambdaQueryWrapper<KeyPoolDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KeyPoolDO::getKeyType, keyType);
        return (int) count(queryWrapper);
    }

    @Override
    public Long insertKeyPair(KeyPoolDO keyEntity) {
        save(keyEntity);
        return keyEntity.getId();
    }

    @Override
    public List<KeyPoolDO> searchKeyEntityList(int count, String keyType) {
        LambdaQueryWrapper<KeyPoolDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KeyPoolDO::getKeyType, keyType);
        queryWrapper.orderByAsc(KeyPoolDO::getCreateTime);
        //获取前Count条数据，为避免数据库方言直接使用分页
        IPage<KeyPoolDO> pageInfo = new Page<>(1, count);
        IPage<KeyPoolDO> page = page(pageInfo, queryWrapper);

        return page.getRecords();
    }

    @Override
    public boolean deleteUsedKeys(List<Long> keyIds) {
        return removeBatchByIds(keyIds);
    }

    @Override
    public boolean isExist(String pubKeyIndex) {
        LambdaQueryWrapper<KeyPoolDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KeyPoolDO::getKeyIndex, pubKeyIndex);
        return count(queryWrapper) > 0;
    }
}
