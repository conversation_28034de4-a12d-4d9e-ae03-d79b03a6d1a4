package kl.npki.km.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.tools.dbtool.constant.DataType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 历史库
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@TableName("T_KEY_HISTORY")
@KlDbTable(tbName = "T_KEY_HISTORY",
        indexes = {@Index(name = "IDX_TKH_SN", columnList = {"ENC_CERT_SN","KEY_TYPE"}, unique = true),
            @Index(name = "IDX_TKH_CA_ID", columnList = {"CA_ID"}),
            @Index(name = "IDX_TKH_SUBJECT", columnList = {"SUBJECT"}),
            @Index(name = "IDX_TKH_KEY_TYPE", columnList = {"KEY_TYPE"})},
    multipleTableId = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15"}
)
public class KeyHistoryDO implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    @KlDbField(type = DataType.BIGINT, size = 20, isPrimary = true)
    private Long id;

    /**
     * 租户id
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String tenantId;

    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, notNull = true)
    private String keyType;

    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128, notNull = true)
    private String encCertSn;

    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 4000)
    private String publicKey;

    @DataFullField
    @KlDbField(type = DataType.CLOB, notNull = true)
    private String privateKey;

    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20)
    private Long mainKeyId;

    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20)
    private Long caId;

    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128)
    private String mediumSn;

    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String subject;

    @KlDbField(type = DataType.DATE)
    private LocalDateTime validEnd;

    @KlDbField(type = DataType.DATE)
    private LocalDateTime validStart;

    @KlDbField(type = DataType.VARCHAR, size = 1024)
    private String userInfoEx;
    @KlDbField(type = DataType.VARCHAR, size = 512)
    private String keyIndex;

    @KlDbField(type = DataType.VARCHAR, size = 128)
    private String archiveReason;

    @KlDbField(type = DataType.DATE)
    private LocalDateTime escrowTime;

    @DataFullField
    @KlDbField(type = DataType.DATE)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 完整性值
     */
    @TableField(value = "FULL_DATA_HASH", fill = FieldFill.INSERT)
    @KlDbField(type = DataType.VARCHAR, size = 1024)
    private String fullDataHash;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public String getEncCertSn() {
        return encCertSn;
    }

    public void setEncCertSn(String encCertSn) {
        this.encCertSn = encCertSn;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public Long getMainKeyId() {
        return mainKeyId;
    }

    public void setMainKeyId(Long mainKeyId) {
        this.mainKeyId = mainKeyId;
    }

    public Long getCaId() {
        return caId;
    }

    public void setCaId(Long caId) {
        this.caId = caId;
    }

    public String getMediumSn() {
        return mediumSn;
    }

    public void setMediumSn(String mediumSn) {
        this.mediumSn = mediumSn;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public LocalDateTime getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(LocalDateTime validEnd) {
        this.validEnd = validEnd;
    }

    public LocalDateTime getValidStart() {
        return validStart;
    }

    public void setValidStart(LocalDateTime validStart) {
        this.validStart = validStart;
    }

    public String getUserInfoEx() {
        return userInfoEx;
    }

    public void setUserInfoEx(String userInfoEx) {
        this.userInfoEx = userInfoEx;
    }

    public String getKeyIndex() {
        return keyIndex;
    }

    public void setKeyIndex(String keyIndex) {
        this.keyIndex = keyIndex;
    }

    public String getFullDataHash() {
        return fullDataHash;
    }

    public void setFullDataHash(String fullDataHash) {
        this.fullDataHash = fullDataHash;
    }

    public String getArchiveReason() {
        return archiveReason;
    }

    public void setArchiveReason(String archiveReason) {
        this.archiveReason = archiveReason;
    }

    public LocalDateTime getEscrowTime() {
        return escrowTime;
    }

    public void setEscrowTime(LocalDateTime escrowTime) {
        this.escrowTime = escrowTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "KeyHistoryDO{" +
                "id=" + id +
                ", keyType=" + keyType +
                ", encCertSn='" + encCertSn + '\'' +
                ", publicKey='" + publicKey + '\'' +
                ", privateKey='" + privateKey + '\'' +
                ", mainKeyId=" + mainKeyId +
                ", caId=" + caId +
                ", mediumSn='" + mediumSn + '\'' +
                ", subject='" + subject + '\'' +
                ", validEnd=" + validEnd +
                ", validStart=" + validStart +
                ", userInfoEx='" + userInfoEx + '\'' +
                ", keyIndex='" + keyIndex + '\'' +
                ", fullDataHash='" + fullDataHash + '\'' +
                ", archiveReason='" + archiveReason + '\'' +
                ", escrowTime=" + escrowTime +
                ", createTime=" + createTime +
                '}';
    }
}
