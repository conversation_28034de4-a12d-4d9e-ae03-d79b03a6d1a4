package kl.npki.km.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.npki.base.service.repository.entity.IntegrityProtectedDO;
import kl.tools.dbtool.constant.DataType;

import java.time.LocalDateTime;

/**
 * <p>
 * 历史库
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@TableName("t_key_history")
@KlDbTable(tbName = "t_key_history",
        indexes = {@Index(name = "idx_tkh_sn", columnList = {"enc_cert_sn","key_type"}, unique = true),
            @Index(name = "idx_tkh_ca_id", columnList = {"ca_id"}),
            @Index(name = "idx_tkh_subject", columnList = {"subject"}),
            @Index(name = "idx_tkh_key_type", columnList = {"key_type"})},
    multipleTableId = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15"}
)
public class KeyHistoryDO extends IntegrityProtectedDO {

    private static final long serialVersionUID = 1L;

    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, notNull = true)
    private String keyType;

    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128, notNull = true)
    private String encCertSn;

    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 4000)
    private String publicKey;

    @DataFullField
    @KlDbField(type = DataType.CLOB, notNull = true)
    private String privateKey;

    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20)
    private Long mainKeyId;

    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20)
    private Long caId;

    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128)
    private String mediumSn;

    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String subject;

    @KlDbField(type = DataType.DATE, size = 6)
    private LocalDateTime validEnd;

    @KlDbField(type = DataType.DATE, size = 6)
    private LocalDateTime validStart;

    @KlDbField(type = DataType.VARCHAR, size = 1024)
    private String userInfoEx;

    @KlDbField(type = DataType.VARCHAR, size = 512)
    private String keyIndex;

    @KlDbField(type = DataType.VARCHAR, size = 128)
    private String archiveReason;

    @KlDbField(type = DataType.DATE)
    private LocalDateTime escrowTime;

    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public String getEncCertSn() {
        return encCertSn;
    }

    public void setEncCertSn(String encCertSn) {
        this.encCertSn = encCertSn;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public Long getMainKeyId() {
        return mainKeyId;
    }

    public void setMainKeyId(Long mainKeyId) {
        this.mainKeyId = mainKeyId;
    }

    public Long getCaId() {
        return caId;
    }

    public void setCaId(Long caId) {
        this.caId = caId;
    }

    public String getMediumSn() {
        return mediumSn;
    }

    public void setMediumSn(String mediumSn) {
        this.mediumSn = mediumSn;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public LocalDateTime getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(LocalDateTime validEnd) {
        this.validEnd = validEnd;
    }

    public LocalDateTime getValidStart() {
        return validStart;
    }

    public void setValidStart(LocalDateTime validStart) {
        this.validStart = validStart;
    }

    public String getUserInfoEx() {
        return userInfoEx;
    }

    public void setUserInfoEx(String userInfoEx) {
        this.userInfoEx = userInfoEx;
    }

    public String getKeyIndex() {
        return keyIndex;
    }

    public void setKeyIndex(String keyIndex) {
        this.keyIndex = keyIndex;
    }

    public String getArchiveReason() {
        return archiveReason;
    }

    public void setArchiveReason(String archiveReason) {
        this.archiveReason = archiveReason;
    }

    public LocalDateTime getEscrowTime() {
        return escrowTime;
    }

    public void setEscrowTime(LocalDateTime escrowTime) {
        this.escrowTime = escrowTime;
    }

    @Override
    public String toString() {
        return "KeyHistoryDO{" +
            "keyType='" + keyType + '\'' +
            ", encCertSn='" + encCertSn + '\'' +
            ", publicKey='" + publicKey + '\'' +
            ", privateKey='" + privateKey + '\'' +
            ", mainKeyId=" + mainKeyId +
            ", caId=" + caId +
            ", mediumSn='" + mediumSn + '\'' +
            ", subject='" + subject + '\'' +
            ", validEnd=" + validEnd +
            ", validStart=" + validStart +
            ", userInfoEx='" + userInfoEx + '\'' +
            ", keyIndex='" + keyIndex + '\'' +
            ", archiveReason='" + archiveReason + '\'' +
            ", escrowTime=" + escrowTime +
            ", fullDataHash='" + fullDataHash + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
