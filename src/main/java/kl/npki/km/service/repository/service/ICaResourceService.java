package kl.npki.km.service.repository.service;

import com.baomidou.mybatisplus.extension.service.IService;
import kl.npki.km.service.repository.entity.CaResourceDO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
public interface ICaResourceService extends IService<CaResourceDO> {

    /**
     * 保存ca资源记录
     *
     * @param caResource
     * @return
     */
    Long insertCaResources(CaResourceDO caResource);



    /**
     * 更新ca资源记录
     *
     * @param caResource
     * @return
     */
    boolean updateCaResources(CaResourceDO caResource);


    /**
     * 删除ca资源记录
     *
     * @param caId
     * @return
     */
    boolean deleteCaResources(Long caId);


    /**
     * 查询对应CA所有密钥资源
     *
     * @param caId
     * @return
     */
    List<CaResourceDO> searchCaResources(Long caId);

    /**
     * 查询所有密钥资源
     */
    List<CaResourceDO> searchAllCaResources();

    /**
     * 消耗密钥数量 keynum - num
     * @param id 资源id
     * @param num 资源数量
     * @return 是否更新成功
     */
    boolean spendKeyNum(Long id, Integer num);

}
