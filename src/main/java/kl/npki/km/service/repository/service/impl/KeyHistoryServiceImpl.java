package kl.npki.km.service.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.npki.km.core.biz.stat.model.CaCountInfo;
import kl.npki.km.core.biz.stat.model.KeyCountInfo;
import kl.npki.km.service.repository.entity.KeyHistoryDO;
import kl.npki.km.service.repository.mapper.KeyHistoryMapper;
import kl.npki.km.service.repository.service.IKeyHistoryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Service
public class KeyHistoryServiceImpl extends KlServiceImpl<KeyHistoryMapper, KeyHistoryDO> implements IKeyHistoryService {

    @Override
    public boolean isExist(String pubKeyIndex) {
        LambdaQueryWrapper<KeyHistoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KeyHistoryDO::getKeyIndex, pubKeyIndex);
        return count(queryWrapper) > 0;
    }

    @Override
    public KeyHistoryDO searchKeyBySnAndType(String sn, String type) {
        LambdaQueryWrapper<KeyHistoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KeyHistoryDO::getEncCertSn, sn);
        queryWrapper.eq(KeyHistoryDO::getKeyType, type);
        return getOne(queryWrapper);
    }

    @Override
    public List<KeyHistoryDO> searchKeyBySn(String sn) {
        LambdaQueryWrapper<KeyHistoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KeyHistoryDO::getEncCertSn, sn);
        return list(queryWrapper);
    }

    @Override
    public KeyHistoryDO searchKeyById(Long keyId) {
        return getById(keyId);
    }

    @Override
    public Long insertKey(KeyHistoryDO keyEntity) {
        save(keyEntity);
        return keyEntity.getId();
    }

    @Override
    public List<KeyCountInfo> countKeyType() {
        return getBaseMapper().countKeyType();
    }

    @Override
    public Integer countByCa(Long caId) {
        LambdaQueryWrapper<KeyHistoryDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KeyHistoryDO::getCaId,caId);
        return Math.toIntExact(count(queryWrapper));
    }

    @Override
    public boolean deleteKey(Long id, String encCertSn) {
        LambdaUpdateWrapper<KeyHistoryDO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(KeyHistoryDO::getId, id);
        updateWrapper.eq(KeyHistoryDO::getEncCertSn, encCertSn);
        return remove(updateWrapper);
    }

    @Override
    public List<CaCountInfo> countByCaIds(List<Long> caIds) {
        if (CollectionUtils.isEmpty(caIds)) {
            return Collections.emptyList();
        }
        return getBaseMapper().countByCaIds(caIds);
    }
}
