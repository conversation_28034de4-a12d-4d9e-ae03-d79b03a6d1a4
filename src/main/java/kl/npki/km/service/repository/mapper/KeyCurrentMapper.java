package kl.npki.km.service.repository.mapper;

import kl.nbase.db.support.mybatis.mapper.KlBaseMapper;
import kl.npki.km.core.biz.stat.model.CaCountInfo;
import kl.npki.km.core.biz.stat.model.KeyCountInfo;
import kl.npki.km.core.biz.stat.model.KeyStatusCountInfo;
import kl.npki.km.service.repository.entity.KeyCurrentDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
public interface KeyCurrentMapper extends KlBaseMapper<KeyCurrentDO> {

    /**
     * 通过keyTypeCount
     * @return
     */
    List<KeyCountInfo> countKeyType();

    /**
     * 统计每一状态的密钥数量
     * @return
     */
    List<KeyStatusCountInfo> countStatus();

    /**
     * 通过CA统计KeyCountInfo
     * @param caId
     * @return
     */
    List<KeyCountInfo> countKeyTypeByCa(@Param("caId") Long caId);

    /**
     * 统计CA使用密钥报表
     *
     * @param caIds
     * @return
     */
    List<CaCountInfo> countByCaIds(@Param("caIds") List<Long> caIds);
}
