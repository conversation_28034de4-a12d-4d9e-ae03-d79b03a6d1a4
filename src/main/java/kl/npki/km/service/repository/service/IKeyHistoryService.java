package kl.npki.km.service.repository.service;

import com.baomidou.mybatisplus.extension.service.IService;
import kl.npki.km.core.biz.stat.model.CaCountInfo;
import kl.npki.km.core.biz.stat.model.KeyCountInfo;
import kl.npki.km.service.repository.entity.KeyHistoryDO;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
public interface IKeyHistoryService extends IService<KeyHistoryDO> {
    /**
     * 查询密钥对是否存在
     *
     * @param pubKeyIndex 公钥索引
     * @return
     */
    boolean isExist(String pubKeyIndex);

    /**
     * 根据SN和密钥类型查询密钥实体
     *
     * @param sn 证书序列号
     * @param type 密钥类型
     * @return 密钥实体
     */
    KeyHistoryDO searchKeyBySnAndType(String sn, String type);

    /**
     * 根据SN查询密钥实体集合
     *
     * @param sn
     * @return
     */
    List<KeyHistoryDO> searchKeyBySn(String sn);


    /**
     * 根据Id查询密钥实体
     *
     * @param keyId
     * @return
     */
    KeyHistoryDO searchKeyById(Long keyId);

    /**
     * 保持历史密钥
     *
     * @param keyEntity
     * @return 主键id
     */
    Long insertKey(KeyHistoryDO keyEntity);

    /**
     * 查询keyTypeCount
     * @return
     */
    List<KeyCountInfo> countKeyType();


    /**
     * 通过CAId查询keyCount
     * @param caId ca标识
     * @return
     */
    Integer countByCa(Long caId);

    /**
     * 删除密钥
     * @param id
     * @param encCertSn
     * @return
     */
    boolean deleteKey(Long id, String encCertSn);

    /**
     * 通过CA ID批量查询密钥数量
     * @param caIds
     * @return
     */
    List<CaCountInfo> countByCaIds(List<Long> caIds);
}
