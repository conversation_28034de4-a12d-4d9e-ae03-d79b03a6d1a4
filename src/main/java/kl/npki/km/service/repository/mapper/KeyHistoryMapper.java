package kl.npki.km.service.repository.mapper;

import kl.nbase.db.support.mybatis.mapper.KlBaseMapper;
import kl.npki.km.core.biz.stat.model.CaCountInfo;
import kl.npki.km.core.biz.stat.model.KeyCountInfo;
import kl.npki.km.service.repository.entity.KeyHistoryDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
public interface KeyHistoryMapper extends KlBaseMapper<KeyHistoryDO> {

    /**
     * 通过keyTypeCount
     * @return
     */
    List<KeyCountInfo> countKeyType();

    /**
     * CA使用密钥数量统计
     *
     * @param caIds
     * @return
     */
    List<CaCountInfo> countByCaIds(@Param("caIds") List<Long> caIds);
}
