package kl.npki.km.service.handler;

import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.common.service.MsgResult;
import kl.npki.base.service.common.SpringBeanRegister;
import kl.npki.base.service.common.context.MsgContext;
import kl.npki.base.service.common.context.MsgContextHolder;
import kl.npki.base.service.common.log.resolver.AsnApiLogResolver;
import kl.npki.base.service.common.netty.BaseTcpServerHandler;
import kl.npki.km.core.service.key.IkeyService;
import kl.npki.km.core.utils.KmResponseUtil;

/**
 * @Author: guoq
 * @Date: 2023/5/8
 * @description: km服务handler
 */
@LogCollector(resolver = AsnApiLogResolver.class)
public class KmServerHandler extends BaseTcpServerHandler {
    private final IkeyService ikeyService;

    public KmServerHandler() {
        this.ikeyService = SpringBeanRegister.getBean(IkeyService.class);
    }

    @Override
    public byte[] process(byte[] msg) {
        MsgContext context = MsgContextHolder.getContext();
        try {
            MsgResult<byte[]> msgResult = ikeyService.processReq(msg);
            context.setServiceResult(msgResult);
            return msgResult.getResult();
        } catch (Exception e) {
            MsgResult<Object> result = new MsgResult<>();
            result.setErrorMessage(e.getMessage());
            context.setServiceResult(result);
            return KmResponseUtil.buildErrorResponse(e);
        }

    }
}
