package kl.npki.km.service.config;

import kl.npki.base.core.biz.license.service.IExternalLicenseCheck;
import kl.npki.base.core.biz.mainkey.service.MainKeyManager;
import kl.npki.km.core.service.ca.ICaService;
import kl.npki.km.core.service.ca.impl.CaServiceImpl;
import kl.npki.km.core.service.key.IkeyService;
import kl.npki.km.core.service.key.impl.KeyServiceImpl;
import kl.npki.km.core.service.license.impl.KmExternalLicenseCheck;
import kl.npki.km.core.service.sks.ISksService;
import kl.npki.km.core.service.sks.impl.SksServiceImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

/**
 * <AUTHOR>
 * @date 2022/11/1 16:00
 * @Description: 手动注册coreBean容器
 */
@Configuration
public class CoreBeanConfiguration {

    /**
     * ICaService 需要依赖这两类，会优先注入
     *
     * @return
     */
    @Bean
    @DependsOn({"springRepositoryCollector"})
    public ICaService getCaService() {
        return new CaServiceImpl();
    }

    @Bean
    public ISksService getSksService() {
        return new SksServiceImpl();
    }

    @Bean
    public IkeyService getIkeyService() {
        return new KeyServiceImpl();
    }
    @Bean
    @ConditionalOnMissingBean
    public MainKeyManager getMainKeyManager() {
        return MainKeyManager.getInstance();
    }

    @Bean
    public IExternalLicenseCheck kmExternalLicenseCheck() {
        return new KmExternalLicenseCheck();
    }
}
