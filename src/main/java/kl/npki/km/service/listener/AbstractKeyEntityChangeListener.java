package kl.npki.km.service.listener;

import kl.npki.base.core.biz.trace.model.EntityChangeParam;
import kl.npki.base.core.common.trace.InvocationType;
import kl.npki.base.service.common.context.MsgContext;
import kl.npki.base.service.common.context.MsgContextHolder;
import kl.npki.base.service.common.context.RequestContext;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.listener.AbstractEntityChangeListener;
import kl.npki.km.core.biz.key.model.KeyTraceEntity;

import static kl.npki.km.service.constant.KMServiceI18N.SYSTEM_OPERATOR;

/**
 * 密钥实体变更的监听抽象类
 * <AUTHOR>
 */
public abstract class AbstractKeyEntityChangeListener extends AbstractEntityChangeListener {

    /**
     * 解析公共参数
     *
     * @param param
     */
    public void fillData2TraceEntity(KeyTraceEntity entity, EntityChangeParam param) {
        // 设置调用方式
        InvocationType invocationType = param.getInvocationType();
        // 返回给前端时再翻译
        entity.setInvocationType(invocationType.name());
        // 设置业务变更名称
        entity.setBizName(param.getBizName());

        // 根据不通的调用方式，填充轨迹实体数据
        switch (invocationType){
            case ADMINISTRATOR_OPERATION:
                RequestContext context = RequestContextHolder.getContext();
                entity.setBizId(context.getBizId());
                entity.setSourceIp(context.getIp());
                entity.setUserId(context.getUserId());
                entity.setOperatorName(context.getUsername());
                break;
            case SYSTEM_SCHEDULED_TASK:
                entity.setOperatorName(SYSTEM_OPERATOR);
                entity.setSourceIp(getLocalIp());
                break;
            case TCP_SERVICE_INTERFACE:
                MsgContext msgContext = MsgContextHolder.getContext();
                entity.setSourceIp(msgContext.getIp());
                entity.setBizId(msgContext.getBizId());
                break;
            case HTTP_SERVICE_INTERFACE:
                RequestContext context2 = RequestContextHolder.getContext();
                entity.setBizId(context2.getBizId());
                entity.setSourceIp(context2.getIp());
                break;
            default:
                break;
        }
    }
}
