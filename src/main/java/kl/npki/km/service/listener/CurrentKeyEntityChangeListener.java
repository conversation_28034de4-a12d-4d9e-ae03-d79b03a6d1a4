package kl.npki.km.service.listener;

import kl.npki.base.core.biz.trace.model.EntityChangeParam;
import kl.npki.base.core.event.EntityChangeEvent;
import kl.npki.km.core.biz.key.model.CurrentKeyEntity;
import kl.npki.km.core.biz.key.model.KeyTraceEntity;
import org.springframework.stereotype.Component;

/**
 * 当前库密钥实体变更事件监听器
 *
 * <AUTHOR>
 */
@Component
public class CurrentKeyEntityChangeListener extends AbstractKeyEntityChangeListener {

    @Override
    public void onChange(EntityChangeEvent event) {
        // 获取事件参数
        EntityChangeParam param = (EntityChangeParam) event.getSource();
        CurrentKeyEntity keyEntity = (CurrentKeyEntity) param.getEntity();

        // 转化为密钥轨迹实体
        KeyTraceEntity keyTraceEntity = new KeyTraceEntity();
        keyTraceEntity.parseCurrentKeyEntity(keyEntity);
        // 填充其它参数
        fillData2TraceEntity(keyTraceEntity, param);
        // 保存轨迹
        keyTraceEntity.save();
    }

    @Override
    public String getEntityClass() {
        return CurrentKeyEntity.class.getName();
    }
}
