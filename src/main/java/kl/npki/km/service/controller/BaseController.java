package kl.npki.km.service.controller;

import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

/**
 * 通用后端控制器
 *
 * <AUTHOR>
 * @date 2023/9/1
 */
public interface BaseController {

    /**
     * 用于解决Fortify 扫描的漏洞: Mass Assignment: Insecure Binder Configuration
     * (用于将 HTTP 请求参数绑定到模型类的框架绑定器未显式配置为允许或禁止特定属性)
     *
     * @param binder Web数据绑定器
     */
    @InitBinder
    default void initBinder(WebDataBinder binder) {
        binder.setDisallowedFields();
    }
}
