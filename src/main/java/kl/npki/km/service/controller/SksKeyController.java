package kl.npki.km.service.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.bean.validate.ValidateService;
import kl.nbase.health.spring.measure.annotation.MeasureExecutionTime;
import kl.nbase.log.collect.LogCollector;
import kl.nbase.traffic.annotation.TrafficResource;
import kl.npki.base.core.annotation.CheckLicense;
import kl.npki.base.service.common.log.resolver.ApiLogResolver;
import kl.npki.km.core.biz.sks.model.*;
import kl.npki.km.core.service.sks.ISksService;
import kl.npki.km.service.model.sks.request.*;
import kl.npki.km.service.model.sks.response.SksResponse;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static kl.npki.km.core.common.constants.KMCoreI18N.*;
import static kl.npki.km.core.common.constants.KmConstants.CONFIG_KM_FEATURE_PREFIX;
import static kl.npki.km.service.constant.SksConstants.SKSUrl.*;

/**
 * <AUTHOR>
 * @date 2022/11/25 13:24
 * @Description:
 */
@RestController
@RequestMapping(SKS_BASE_URL)
@Tag(name = "协同密钥管理")
@LogCollector(resolver = ApiLogResolver.class)
@ConditionalOnProperty(prefix = CONFIG_KM_FEATURE_PREFIX, name = "enableSks", havingValue = "true", matchIfMissing = false)
public class SksKeyController implements BaseController {

    @Resource
    private ISksService sksService;

    @Resource
    private ValidateService validateService;

    @Resource
    private ConvertService convertService;

    /**
     * 获取随机数
     * kms接口为post
     *
     * @param randomNumberRequest
     * @return
     */
    @PostMapping(SKS_GET_RANDOM_URL)
    @MeasureExecutionTime("getRandomNumber")
    @Operation(description = GET_RANDOM_NUMBER, summary = "获取随机数")
    @CheckLicense
    @TrafficResource("getRandomNumber")
    public SksResponse<String> getRandomNumber(@RequestBody RandomNumberRequest randomNumberRequest) {
        validateService.validate(randomNumberRequest);
        return SksResponse.success(sksService.getRandomNumber(randomNumberRequest.getSizeInByte()));
    }

    @PostMapping(SKS_CREATE_PRIVATE_KEY_URL)
    @MeasureExecutionTime("createPrivateKey")
    @Operation(description = CREATE_PRIVATE_KEY, summary = "保存协同密钥")
    @CheckLicense
    @TrafficResource("createPrivateKey")
    public SksResponse<Void> createPrivateKey(@RequestBody SksKeyInfo sksKeyInfo) {
        validateService.validate(sksKeyInfo);
        //转换sks实体对象
        SksKeyEntity sksKeyEntity = convertService.convert(sksKeyInfo, SksKeyEntity.class);
        sksService.createPrivateKey(sksKeyEntity);
        return SksResponse.success();
    }

    /**
     * kms接口为post
     *
     * @param sksKeySelectRequest
     * @return
     */
    @PostMapping(SKS_GET_PRIVATE_KEY_URL)
    @MeasureExecutionTime("getPrivateKey")
    @Operation(description = GET_PRIVATE_KEY, summary = "查询协同密钥")
    @CheckLicense
    @TrafficResource("getPrivateKey")
    public SksResponse<SksKeyInfo> getPrivateKey(@RequestBody SksKeySelectRequest sksKeySelectRequest) {
        validateService.validate(sksKeySelectRequest);
        SksKeyEntity keyEntity = sksService.getPrivateKey(sksKeySelectRequest.getClientKeyHash());
        return SksResponse.success(convertService.convert(keyEntity, SksKeyInfo.class));
    }

    /**
     * kms接口为post
     *
     * @param updateRequest
     * @return
     */
    @PostMapping(SKS_UPDATE_PRIVATE_KEY_URL)
    @MeasureExecutionTime("updatePrivateKey")
    @Operation(description = UPDATE_PRIVATE_KEY, summary = "更新协同密钥状态")
    @CheckLicense
    @TrafficResource("updatePrivateKey")
    public SksResponse<Void> updatePrivateKey(@RequestBody SksKeyUpdateRequest updateRequest) {
        validateService.validate(updateRequest);
        SksUpdateInfo sksUpdateInfo = convertService.convert(updateRequest, SksUpdateInfo.class);
        sksService.updatePrivateKey(sksUpdateInfo);
        return SksResponse.success();
    }

    /**
     * kms接口为post
     *
     * @param batchSearchRequest
     * @return
     */
    @PostMapping(SKS_BATCH_GET_PRIVATE_KEY_URL)
    @MeasureExecutionTime("batchGetPrivateKey")
    @Operation(description = BATCH_GET_PRIVATE_KEY, summary = "批量获取协同密钥")
    @CheckLicense
    @TrafficResource("batchGetPrivateKey")
    public SksResponse<List<BatchSksKeyInfo>> batchGetPrivateKey(@RequestBody SksKeyBatchSearchRequest batchSearchRequest) {

        validateService.validate(batchSearchRequest);
        batchSearchRequest.checkTime();
        SksKeyBatchSearchInfo sksKeyBatchSearchInfo =
            new SksKeyBatchSearchInfo(batchSearchRequest.getStartTime(), batchSearchRequest.getEndTime());
        SksKeyListInfo sksKeyListInfo = sksService.batchGetPrivateKey(sksKeyBatchSearchInfo);
        return SksResponse.success(sksKeyListInfo.getDataSize(), sksKeyListInfo.getData());
    }

    /**
     * kms接口为post
     *
     * @param sksKeySelectRequest
     * @return
     */
    @PostMapping(SKS_REMOVE_PRIVATE_KEY_URL)
    @MeasureExecutionTime("removePrivateKey")
    @Operation(description = REMOVE_PRIVATE_KEY, summary = "删除自检协同密钥")
    @CheckLicense
    @TrafficResource("removePrivateKey")
    public SksResponse<Void> removePrivateKey(@RequestBody SksKeySelectRequest sksKeySelectRequest) {
        validateService.validate(sksKeySelectRequest);
        sksService.removeSelfCheckPrivateKey(sksKeySelectRequest.getClientKeyHash());
        return SksResponse.success();
    }

}
