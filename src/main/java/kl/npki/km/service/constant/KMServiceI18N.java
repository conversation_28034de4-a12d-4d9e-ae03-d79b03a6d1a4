package kl.npki.km.service.constant;

import kl.nbase.i18n.i18n.I18nUtil;

/**
 * <AUTHOR> on 2025/01/16
 **/
public class KMServiceI18N {

    public static final String KM_SERVICE_I18N_MESSAGE_KEY_PREFIX = "kl.npki.km.service.i18n" + I18nUtil.SEPARATOR;

    // region operation

    // region  kl.npki.km.service.controller.KmController
    public static final String KEY_SERVICE = KM_SERVICE_I18N_MESSAGE_KEY_PREFIX + "key_service";
    // endregion

    // region  kl.npki.km.service.listener.AbstractKeyEntityChangeListener
    public static final String SYSTEM_OPERATOR = KM_SERVICE_I18N_MESSAGE_KEY_PREFIX + "system_operator";
    // endregion

    // endregion

    /**
     * 获取国际化后的信息
     *
     * @param key  国际化资源的键（不包含资源路径）
     * @param args 占位填充参数
     * @return 国际化结果
     */
    public static String getKmServiceI18nMessage(String key, Object... args) {
        return I18nUtil.tr(KM_SERVICE_I18N_MESSAGE_KEY_PREFIX + key, args);
    }

}
