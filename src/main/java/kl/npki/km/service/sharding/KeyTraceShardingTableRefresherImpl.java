package kl.npki.km.service.sharding;

import kl.npki.base.service.sharding.DynamicShardingTableRefresher;
import org.springframework.stereotype.Component;

/**
 * 密钥历史轨迹表分表刷新
 *
 * <AUTHOR>
 * @date 2025/6/6 9:39
 **/
@Component
public class KeyTraceShardingTableRefresherImpl implements DynamicShardingTableRefresher {

    /**
     * 密钥轨迹表
     */
    private static final String KEY_TRACE_TABLE_NAME = "T_KEY_TRACE";

    /**
     * 密钥轨迹表分表正则，最大到1000个分表
     */
    private static final String KEY_TRACE_SHARDING_TABLE_NAME_REGEX = "^(t_key_trace|T_KEY_TRACE)_(0|[1-9][0-9]{0,2}|1000)$";

    @Override
    public String getLogicalTableName() {
        return KEY_TRACE_TABLE_NAME;
    }

    @Override
    public String getShardingTableNameRegex() {
        return KEY_TRACE_SHARDING_TABLE_NAME_REGEX;
    }
}
