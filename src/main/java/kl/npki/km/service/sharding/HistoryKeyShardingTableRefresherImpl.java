package kl.npki.km.service.sharding;

import kl.npki.base.service.sharding.DynamicShardingTableRefresher;
import org.springframework.stereotype.Component;

/**
 * 历史密钥分表刷新器
 *
 * <AUTHOR>
 * @date 2025/6/6 9:39
 **/
@Component
public class HistoryKeyShardingTableRefresherImpl implements DynamicShardingTableRefresher {

    /**
     * 密钥历史表
     */
    private static final String HISTORY_KEY_TABLE_NAME = "T_KEY_HISTORY";

    /**
     * 密钥历史表分表正则，最大到1000个分表
     */
    private static final String HISTORY_KEY_SHARDING_TABLE_NAME_REGEX = "^(t_key_history|T_KEY_HISTORY)_(0|[1-9][0-9]{0,2}|1000)$";

    @Override
    public String getLogicalTableName() {
        return HISTORY_KEY_TABLE_NAME;
    }

    @Override
    public String getShardingTableNameRegex() {
        return HISTORY_KEY_SHARDING_TABLE_NAME_REGEX;
    }
}
