package kl.npki.km.service.mapper.sks;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.biz.sks.model.SksKeyEntity;
import kl.npki.km.service.model.sks.request.SksKeyInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/11/25 15:07
 * @Description:
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SksKeyInfo2EntityMapper extends BaseMapper<SksKeyInfo, SksKeyEntity> {

    @Override
    SksKeyEntity map(SksKeyInfo source);


    @Override
    SksKeyInfo reverseMap(SksKeyEntity target);
}
