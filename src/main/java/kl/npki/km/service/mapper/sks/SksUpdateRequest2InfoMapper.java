package kl.npki.km.service.mapper.sks;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.biz.sks.model.SksUpdateInfo;
import kl.npki.km.service.model.sks.request.SksKeyUpdateRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * SksKeyUpdateRequest SksUpdateInfo转换
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 * @date 2025/7/9
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SksUpdateRequest2InfoMapper extends BaseMapper<SksKeyUpdateRequest, SksUpdateInfo> {

    @Override
    SksUpdateInfo map(SksKeyUpdateRequest source);

    @Override
    SksKeyUpdateRequest reverseMap(SksUpdateInfo target);
}
