package kl.npki.km.service.mapper.key;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.biz.sks.model.SksKeyEntity;
import kl.npki.km.service.repository.entity.KeyCurrentDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/11/25 17:18
 * @Description:
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KeyCurrentDO2SksKeyEntityMapper extends BaseMapper<KeyCurrentDO, SksKeyEntity> {


    @Override
    @Mapping(source = "encCertSn", target = "clientKeyHash")
    @Mapping(source = "subject", target = "userId")
    @Mapping(source = "mediumSn", target = "mediaId")
    @Mapping(source = "privateKey", target = "serverKeySection")
    @Mapping(source = "userInfoEx", target = "keySource")
    @Mapping(source = "keyStatus", target = "status")
    SksKeyEntity map(KeyCurrentDO source);


    @Override
    @Mapping(source = "clientKeyHash", target = "encCertSn")
    @Mapping(source = "userId", target = "subject")
    @Mapping(source = "mediaId", target = "mediumSn")
    @Mapping(source = "serverKeySection", target = "privateKey")
    @Mapping(source = "keySource", target = "userInfoEx")
    @Mapping(source = "status", target = "keyStatus")
    KeyCurrentDO reverseMap(SksKeyEntity target);
}
