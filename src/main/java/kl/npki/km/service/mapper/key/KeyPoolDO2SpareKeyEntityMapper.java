package kl.npki.km.service.mapper.key;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.biz.key.model.SpareKeyEntity;
import kl.npki.km.service.repository.entity.KeyPoolDO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/10/27 15:15
 * @Description:
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KeyPoolDO2SpareKeyEntityMapper extends BaseMapper<KeyPoolDO, SpareKeyEntity> {

    @Override
    SpareKeyEntity map(KeyPoolDO source);


    @Override
    KeyPoolDO reverseMap(SpareKeyEntity target);
}
