package kl.npki.km.service.mapper.key;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.km.core.biz.key.model.KeyTraceEntity;
import kl.npki.km.service.repository.entity.KeyTraceDO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KeyTraceDO2EntityMapper extends BaseMapper<KeyTraceDO, KeyTraceEntity> {
    @Override
    KeyTraceEntity map(KeyTraceDO source);

    @Override
    KeyTraceDO reverseMap(KeyTraceEntity target);
}
