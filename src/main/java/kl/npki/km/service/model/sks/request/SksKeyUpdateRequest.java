package kl.npki.km.service.model.sks.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;

/**
 * @Author: guoq
 * @Date: 2023/3/21
 * @description:
 */
public class SksKeyUpdateRequest implements Serializable {

    private static final long serialVersionUID = 3162436718213108997L;
    /**
     * 密钥个人片段sm3Hash经Base64编码
     */
    @NotBlank(message = "密钥个人片段不能为空")
    private String clientKeyHash;

    /**
     * 期望更新的状态
     */
    @Min(value = -2, message = "不允许的状态值，有效值为-2,-1,0")
    @Max(value = 0, message = "不允许的状态值，有效值为-2,-1,0")
    @NotNull(message = "密钥的状态不能为空")
    private Integer status;

    public String getClientKeyHash() {
        return clientKeyHash;
    }

    public void setClientKeyHash(String clientKeyHash) {
        this.clientKeyHash = clientKeyHash;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
