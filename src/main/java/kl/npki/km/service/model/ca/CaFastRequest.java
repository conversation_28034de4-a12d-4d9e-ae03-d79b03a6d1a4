package kl.npki.km.service.model.ca;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;

/**
 * @Author: guoq
 * @Date: 2023/5/19
 * @description:
 */
public class CaFastRequest {
    @NotBlank(message = "Ca名称不能为空")
    @Size(max = 128, message = "Ca名称长度不能大于128字符")
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = "Ca别名不能以空格、%20、%0a、%00开头或结尾", flags = Pattern.Flag.DOTALL)
    @Schema(description = "Ca别名")
    private String caName;
    @NotBlank(message = "Ca版本不能为空")
    @Size(max = 128, message = "Ca版本长度不能大于128字符")
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = "Ca版本不能以空格、%20、%0a、%00开头或结尾", flags = Pattern.Flag.DOTALL)
    @Schema(description = "Ca版本")
    private String caVersion;
    /**
     * 身份证书
     */
    @NotBlank(message = "Ca身份证书不能为空")
    @Size(max = 4096, message = "Ca身份证书长度不能大于4096字符")
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = "Ca身份证书不能以空格、%20、%0a、%00开头或结尾", flags = Pattern.Flag.DOTALL)
    @Schema(description = "Ca身份证书")
    private String idCert;

    @Min(value = 1, message = "密钥默认有效期不能小于1年")
    @Max(value = 100, message = "密钥默认有效期不能大于100年")
    @Schema(description = "密钥默认有效期")
    private int limitYear;


    public String getCaName() {
        return caName;
    }

    public void setCaName(String caName) {
        this.caName = caName;
    }

    public String getCaVersion() {
        return caVersion;
    }

    public void setCaVersion(String caVersion) {
        this.caVersion = caVersion;
    }

    public String getIdCert() {
        return idCert;
    }

    public void setIdCert(String idCert) {
        this.idCert = idCert;
    }

    public int getLimitYear() {
        return limitYear;
    }

    public void setLimitYear(int limitYear) {
        this.limitYear = limitYear;
    }
}
