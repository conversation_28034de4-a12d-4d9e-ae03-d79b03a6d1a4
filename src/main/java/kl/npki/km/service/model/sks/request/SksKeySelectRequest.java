package kl.npki.km.service.model.sks.request;

import jakarta.validation.constraints.NotBlank;

import java.io.Serializable;

/**
 * @Author: guoq
 * @Date: 2023/3/21
 * @description:
 */
public class SksKeySelectRequest implements Serializable {

    private static final long serialVersionUID = -5222871889877306321L;
    /**
     * 密钥个人片段sm3Hash经Base64编码
     */
    @NotBlank(message = "密钥个人片段不能为空")
    private String clientKeyHash;

    public String getClientKeyHash() {
        return clientKeyHash;
    }

    public void setClientKeyHash(String clientKeyHash) {
        this.clientKeyHash = clientKeyHash;
    }

    @Override
    public String toString() {
        return "SksKeySelectRequest{" +
            "clientKeyHash='" + clientKeyHash + '\'' +
            '}';
    }
}
