package kl.npki.km.service.model.sks.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/25 18:00
 * @Description: 随机数请求
 */
public class RandomNumberRequest implements Serializable {
    private static final long serialVersionUID = -3568291640644752888L;

    @Schema(description = "随机数长度（以字节为单位）")
    @Max(value = 64, message = "最大只能为64")
    @Min(value = 1, message = "最小不能小于为1")
    private int sizeInByte;

    public int getSizeInByte() {
        return sizeInByte;
    }

    public void setSizeInByte(int sizeInByte) {
        this.sizeInByte = sizeInByte;
    }

    @Override
    public String toString() {
        return "RandomNumberRequest{" +
            "sizeInByte=" + sizeInByte +
            '}';
    }
}
