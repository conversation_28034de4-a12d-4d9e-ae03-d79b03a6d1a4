package kl.npki.km.service.model.sks.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import kl.nbase.exception.BaseException;
import kl.npki.km.service.constant.SksConstants;

/**
 * @Author: guoq
 * @Date: 2023/3/24
 * @description:
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SksResponse<T> {

    public static final String SUCCESS_CODE = "0";
    public static final String SUCCESS_MSG = "success";

    private String code;
    private String msg;
    private Integer dataSize;
    private T data;

    public static <T> SksResponse<T> success() {
        SksResponse<T> response = new SksResponse<>();
        response.code = SUCCESS_CODE;
        response.msg = SUCCESS_MSG;
        return response;
    }

    public static <T> SksResponse<T> success(T t) {
        SksResponse<T> response = new SksResponse<>();
        response.code = SUCCESS_CODE;
        response.msg = SUCCESS_MSG;
        response.data = t;
        return response;
    }

    public static <T> SksResponse<T> success(int dataSize, T t) {
        SksResponse<T> response = success(t);
        response.dataSize = dataSize;
        return response;
    }

    public static <T> SksResponse<T> fail(SksConstants.KmsError error) {

        SksResponse<T> response = new SksResponse<>();
        response.code = error.getCode();
        response.msg = error.getMsg();
        return response;
    }

    public static <T> SksResponse<T> fail(String code, String msg) {

        SksResponse<T> response = new SksResponse<>();
        response.code = code;
        response.msg = msg;
        return response;
    }

    public static <T> SksResponse<T> fail(BaseException baseException) {
        SksResponse<T> response = new SksResponse<>();
        response.code = baseException.getCode();
        response.msg = baseException.getMessage();
        return response;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public Integer getDataSize() {
        return dataSize;
    }

    public T getData() {
        return data;
    }
}
