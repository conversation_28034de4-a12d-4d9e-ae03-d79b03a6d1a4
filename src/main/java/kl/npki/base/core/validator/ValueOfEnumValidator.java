package kl.npki.base.core.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import kl.nbase.i18n.i18n.I18nUtil;
import kl.npki.base.core.annotation.ValueOfEnum;
import kl.npki.base.core.constant.ValueOfDefaultEnum;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.core.utils.ValidatorConstraintUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.ILLEGAL_ENUM_TYPE_I18N_KEY;

/**
 * 枚举字段自定义校验器
 *
 * <AUTHOR> by niugang on 2025-06-05 13:18
 */
public class ValueOfEnumValidator implements ConstraintValidator<ValueOfEnum, CharSequence> {
    public static final String KL_NPKI_BASE_CORE_ANNOTATION_VALUE_OF_ENUM_MESSAGE = "{kl.npki.base.core.annotation.ValueOfEnum.message}";


    private final List<String> acceptedValues = new ArrayList<>();

    private String message;

    @Override
    public void initialize(ValueOfEnum annotation) {
        String[] values = annotation.values();
        if (ArrayUtils.isEmpty(values) && annotation.enumClass() == ValueOfDefaultEnum.class) {
            throw BaseValidationError.ENUM_VALIDATOR_DEFINITION_ERROR.toException();
        }
        if (ArrayUtils.isNotEmpty(values)) {
            acceptedValues.addAll(Arrays.asList(values));
        } else {
            acceptedValues.addAll(Stream.of(annotation.enumClass().getEnumConstants()).map(Enum::name).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(acceptedValues)) {
            throw BaseValidationError.ENUM_VALIDATOR_DEFINITION_ERROR.toException();
        }
        this.message = annotation.message();
    }

    @Override
    public boolean isValid(CharSequence value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }

        if (!acceptedValues.contains(value.toString())) {
            if (Objects.equals(message, KL_NPKI_BASE_CORE_ANNOTATION_VALUE_OF_ENUM_MESSAGE)) {
                context.disableDefaultConstraintViolation();
                String tr = I18nUtil.tr(ILLEGAL_ENUM_TYPE_I18N_KEY, ValidatorConstraintUtil.extractFieldName(context), acceptedValues);
                context.buildConstraintViolationWithTemplate(tr)
                    .addConstraintViolation();
            }
            return false;
        }
        return true;
    }

}
