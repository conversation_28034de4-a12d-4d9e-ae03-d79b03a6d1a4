package kl.npki.base.core.asn1.custom;

import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.security.asn1.ASN1ObjectIdentifier;
import kl.nbase.security.asn1.custom.gm.pqc.kem.PqKEMEnvelopedKey;
import kl.nbase.security.asn1.x509.AlgorithmIdentifier;
import kl.nbase.security.constants.BlockEnum;
import kl.nbase.security.constants.PaddingEnum;
import kl.nbase.security.constants.SymAlgoEnum;
import kl.nbase.security.entity.algo.BlockSymAlgo;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.nbase.security.entity.data.impl.KDFData;
import kl.nbase.security.entity.data.impl.KEMData;
import kl.nbase.security.entity.key.asym.pub.impl.SecurityPublicKey;
import kl.nbase.security.entity.param.crypto.kdf.impl.HKDFParam;
import kl.nbase.security.entity.param.crypto.kem.impl.KEMEncapsParam;
import kl.npki.base.core.tenantholders.EngineHolder;
import org.apache.commons.lang3.ObjectUtils;

import java.security.PublicKey;

/**
 * 抗量子数字信封构造帮助类
 * <AUTHOR>
 */
public class PqKemEnvelopedKeyFactory {
    /**
     * SM2密钥对保护数据格式
     *
     * @param contentEncryptionAlgorithm 内容加密算法（对称算法）
     * @param protectPubKey              保护公钥
     * @param encPublicKey               用户加密证书公钥
     * @param content                    用户加密证书明文私钥
     * @return
     */
    public static PqKEMEnvelopedKey genPqKemEnvelopedKey(final AlgorithmIdentifier contentEncryptionAlgorithm,
                                                         final PublicKey protectPubKey,
                                                         final PublicKey encPublicKey,
                                                         final byte[] content) {
        // 构造对称加密算法
        ASN1ObjectIdentifier symAlgoOid = contentEncryptionAlgorithm.getAlgorithm();
        BlockSymAlgo symAlgo;
        if (ObjectUtils.isEmpty(symAlgoOid)) {
            // 客户端不预置算法默认使用SM4
            symAlgo = new BlockSymAlgo(SymAlgoEnum.SM4, BlockEnum.CBC, PaddingEnum.NO_PADDING);
        } else {
            symAlgo = BlockSymAlgo.valueOf(symAlgoOid.getId());
        }

        // 获取加密引擎
        ClusterEngine engine = EngineHolder.get();

        SecurityPublicKey securityPublicKey = new SecurityPublicKey(protectPubKey);
        // 调用PQC密钥协商算法，生成结果的密钥长度无法确定
        KEMData kemData = engine.kemEncaps(securityPublicKey, new KEMEncapsParam());
        byte[] sharedKey = kemData.getSharedKey();
        // fixme HKDF目前oid未定义，默认写死SM3
        HashAlgo hashAlgo = HashAlgo.SM3;
        // 使用HKDF生成需要的对称密钥长度
        HKDFParam hkdfParam = new HKDFParam(hashAlgo, sharedKey, symAlgo.getKeyLength() + symAlgo.getIvLength());
        KDFData kdfData = engine.kdf(hkdfParam);
        byte[] kdfKey = kdfData.getValue();
        // 生成明文的对称密钥
        byte[] plainSymKey = new byte[symAlgo.getKeyLength()];
        byte[] iv = null;
        System.arraycopy(kdfKey, 0, plainSymKey, 0, symAlgo.getKeyLength());
        if (symAlgo.isNeedIv()) {
            iv = new byte[symAlgo.getIvLength()];
            System.arraycopy(kdfKey, kdfKey.length - symAlgo.getIvLength(), iv, 0, symAlgo.getIvLength());
        }
        byte[] pqEncryptedPrivateKey = engine.symmetricEnc(symAlgo, plainSymKey, iv, content);

        return new PqKEMEnvelopedKey(new AlgorithmIdentifier(symAlgo.getOid()), kemData.getCipherText(),
            encPublicKey.getEncoded(),
            pqEncryptedPrivateKey);

    }

    /**
     * 生成裸密钥结构返回, 用于ca未开启密钥加密的情况
     *
     * @param contentEncryptionAlgorithm
     * @param encPublicKey
     * @param content
     * @return
     */
    public static PqKEMEnvelopedKey genBarePqKemKey(final AlgorithmIdentifier contentEncryptionAlgorithm,
                                                    final PublicKey encPublicKey,
                                                    final byte[] content) {
        return new PqKEMEnvelopedKey(contentEncryptionAlgorithm, new byte[0],
                encPublicKey.getEncoded(),
                content);
    }
}
