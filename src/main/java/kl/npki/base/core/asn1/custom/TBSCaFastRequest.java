package kl.npki.base.core.asn1.custom;

import kl.nbase.security.asn1.*;

/**
 * 待签名的ca快速接入请求数据
 *  TBSCaFastRequest ::= SEQUENCE {
 *      version                 ASN1Integer DEFAULT v2,- 协议版本信息
 *      caInfo                  CaInfo,                - ca接入信息
 *      assignDefaultResources  ASN1Boolean,           - 是否分配默认资源
 *      ASN1Integer             limitYear,             - 密钥默认有效期，单位: 年
 *      ASN1Integer             sm2KeyStandardVersion  - SM2密钥类型时的响应封装格式版本
 *   }
 *
 *   Version ::= INTEGER { v2(99) }
 *
 * <AUTHOR>
 */
public class TBSCaFastRequest extends ASN1Object{
    private ASN1Integer version ;
    private CaInfo  caInfo;
    private ASN1Boolean assignDefaultResources;
    private ASN1Integer limitYear;
    private ASN1Integer sm2KeyStandardVersion;

    private static final ASN1Integer DEFAULT_VERSION = new ASN1Integer(99);

    public static TBSCaFastRequest getInstance(Object obj) {
        if (obj instanceof TBSCaFastRequest) {
            return (TBSCaFastRequest) obj;
        } else if (obj != null) {
            return new TBSCaFastRequest(ASN1Sequence.getInstance(obj));
        }
        return null;
    }

    public TBSCaFastRequest(CaInfo caInfo, ASN1Boolean assignDefaultResources, ASN1Integer limitYear, ASN1Integer sm2KeyStandardVersion) {
        this.version = DEFAULT_VERSION;
        this.caInfo = caInfo;
        this.assignDefaultResources = assignDefaultResources;
        this.limitYear = limitYear;
        this.sm2KeyStandardVersion = sm2KeyStandardVersion;
    }

    private TBSCaFastRequest(ASN1Sequence seq) {
        if (seq.size() != 5) {
            throw new IllegalArgumentException("Invalid TBSCaFastRequest sequence size: " + seq.size());
        }
        this.version = ASN1Integer.getInstance(seq.getObjectAt(0));
        this.caInfo = CaInfo.getInstance(seq.getObjectAt(1));
        this.assignDefaultResources = ASN1Boolean.getInstance(seq.getObjectAt(2));
        this.limitYear = ASN1Integer.getInstance(seq.getObjectAt(3));
        this.sm2KeyStandardVersion = ASN1Integer.getInstance(seq.getObjectAt(4));
    }

    @Override
    public ASN1Primitive toASN1Primitive() {
        ASN1EncodableVector v = new ASN1EncodableVector(4);
        v.add(version);
        v.add(caInfo);
        v.add(assignDefaultResources);
        v.add(limitYear);
        v.add(sm2KeyStandardVersion);

        return new DERSequence(v);
    }

    public ASN1Integer getVersion() {
        return version;
    }

    public CaInfo getCaInfo() {
        return caInfo;
    }

    public ASN1Integer getSm2KeyStandardVersion() {
        return sm2KeyStandardVersion;
    }

    public ASN1Boolean getAssignDefaultResources() {
        return assignDefaultResources;
    }

    public ASN1Integer getLimitYear() {
        return limitYear;
    }
}
