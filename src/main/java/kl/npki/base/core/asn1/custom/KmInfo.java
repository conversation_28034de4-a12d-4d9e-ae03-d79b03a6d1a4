package kl.npki.base.core.asn1.custom;

import kl.nbase.security.asn1.*;
import kl.nbase.security.asn1.x509.Certificate;

/**
 * KM信息
 *
 * KmInfo ::= SEQUENCE {
 *      version     ASN1OctetString,
 *      name        ASN1OctetString,
 *      idCert      Certificate,
 *      hmac        ASN1BitString          - 对name做的hmac值
 *   }
 * <AUTHOR>
 */
public class KmInfo extends ASN1Object {
    private ASN1OctetString version;
    private ASN1OctetString name;
    private Certificate idCert;
    private ASN1BitString hmac;

    public KmInfo(ASN1OctetString version, ASN1OctetString name, Certificate idCert, ASN1BitString hmac) {
        this.version = version;
        this.name = name;
        this.idCert = idCert;
        this.hmac = hmac;
    }

    public static KmInfo getInstance(Object obj) {
        if (obj instanceof KmInfo) {
            return (KmInfo) obj;
        } else if (obj != null) {
            return new KmInfo(ASN1Sequence.getInstance(obj));
        }
        return null;
    }

    private KmInfo(ASN1Sequence seq) {
        if (seq.size() != 4) {
            throw new IllegalArgumentException("Invalid KmInfo sequence size: " + seq.size());
        }
        this.version = ASN1OctetString.getInstance(seq.getObjectAt(0));
        this.name = ASN1OctetString.getInstance(seq.getObjectAt(1));
        this.idCert = Certificate.getInstance(seq.getObjectAt(2));
        this.hmac = ASN1BitString.getInstance(seq.getObjectAt(3));
    }

    @Override
    public ASN1Primitive toASN1Primitive() {
        ASN1EncodableVector v = new ASN1EncodableVector(4);
        v.add(version);
        v.add(name);
        v.add(idCert);
        v.add(hmac);

        return new DERSequence(v);
    }

    public ASN1OctetString getVersion() {
        return version;
    }

    public ASN1OctetString getName() {
        return name;
    }

    public Certificate getIdCert() {
        return idCert;
    }

    public ASN1BitString getHmac() {
        return hmac;
    }
}
