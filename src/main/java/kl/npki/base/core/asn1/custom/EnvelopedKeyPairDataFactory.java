package kl.npki.base.core.asn1.custom;

import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.security.asn1.DEROctetString;
import kl.nbase.security.asn1.custom.pkcs.EnvelopedKeyPairData;
import kl.nbase.security.asn1.x509.AlgorithmIdentifier;
import kl.nbase.security.constants.BlockEnum;
import kl.nbase.security.constants.PaddingEnum;
import kl.nbase.security.constants.SymAlgoEnum;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.entity.algo.BlockSymAlgo;
import kl.npki.base.core.asn1.gb.SkfEnvelopedKeyBlobFactory;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.core.tenantholders.EngineHolder;

import java.io.IOException;
import java.security.PublicKey;

import static kl.nbase.security.entity.algo.AsymAlgo.*;
import static kl.npki.base.core.constant.I18nExceptionInfoConstant.UNSUPPORTED_KEY_TYPE_TYPE_IS_I18N_KEY;

/**
 * <AUTHOR> Yang
 * @date 2023-04-03 14:16
 */
public class EnvelopedKeyPairDataFactory {

    public static final Integer SESSION_KEY_LENGTH = 16;

    private EnvelopedKeyPairDataFactory() {
    }

    /**
     * 生成 {@link EnvelopedKeyPairData}, 用于介质
     *
     * @param plainPriKey      明文待恢复私钥
     * @param plainPubKey      明文待恢复公钥
     * @param toRecoverAsyAlgo {@link AsymAlgo} 待恢复密钥对（plainPriKey 和 plainPubKey）类型
     * @param recoverPubKey    恢复介质公钥（用于加密对称密钥）
     * @return {@link EnvelopedKeyPairData} to byte[]
     * @throws IOException
     */
    public static byte[] genSkfEnvelopedKeyPairData(byte[] plainPriKey, byte[] plainPubKey, AsymAlgo toRecoverAsyAlgo, PublicKey recoverPubKey) throws IOException {
        ClusterEngine clusterEngine = EngineHolder.get();

        // 生成对称密钥
        byte[] sessionKey = clusterEngine.getRandom(SESSION_KEY_LENGTH);
        // 根据待恢复密钥对类型获取对称加密算法（用于加密私钥）
        BlockSymAlgo symAlgo = getPriKeySymEncAlgo(toRecoverAsyAlgo);
        // 加密私钥
        byte[] encryptedPrvKey = null;
        // 构造数字信封
        byte[] symmetricCipherOidBytes = new byte[0];
        byte[] encryptedSessionKey = new byte[0];
        if (RSA_1024.equals(toRecoverAsyAlgo) || RSA_2048.equals(toRecoverAsyAlgo) || RSA_4096.equals(toRecoverAsyAlgo)) {
            // RSA 时直接装进 EnvelopedKeyPairData
            // RSA 时填写实际 oid
            symmetricCipherOidBytes = symAlgo.getOid().getId().getBytes();
            // 使用公钥加密对称密钥
            encryptedSessionKey = clusterEngine.pubEnc(recoverPubKey, sessionKey);
            encryptedPrvKey = clusterEngine.symmetricEnc(symAlgo, sessionKey, null, plainPriKey);
        } else if (SM2.equals(toRecoverAsyAlgo)) {
            // SM2 时构造 SkfEnvelopedKeyBlob
            // encryptedSessionKey 和 symmetricCipherOIDBytes 留空值
            // 使用公钥加密对称密钥
            encryptedPrvKey = clusterEngine.symmetricEnc(symAlgo, sessionKey, null, plainPriKey);
            encryptedPrvKey = SkfEnvelopedKeyBlobFactory.genSkfEnvelopedKeyBlob(encryptedPrvKey, plainPubKey,
                    sessionKey, recoverPubKey, symAlgo);
        } else {
            //ECC 暂不支持
            throw BaseValidationError.ALGORITHM_NOT_SUPPORTED.toException(UNSUPPORTED_KEY_TYPE_TYPE_IS_I18N_KEY, toRecoverAsyAlgo.getAlgoName());
        }

        EnvelopedKeyPairData envelopedKeyPairData = new EnvelopedKeyPairData(encryptedSessionKey,
                encryptedPrvKey,
                symmetricCipherOidBytes);
        return envelopedKeyPairData.getEncoded();
    }

    /**
     * 生成 {@link EnvelopedKeyPairData}
     *
     * @param plainPriKey       明文待恢复私钥
     * @param receiverPublicKey 接收者公钥
     * @return {@link EnvelopedKeyPairData}
     * @throws IOException
     */
    public static EnvelopedKeyPairData genEnvelopedKeyPair(byte[] plainPriKey, PublicKey receiverPublicKey) {
        ClusterEngine clusterEngine = EngineHolder.get();
        AsymAlgo receiverAsymAlgo = AsymAlgo.valueOf(receiverPublicKey);
        if (SM2.equals(receiverAsymAlgo) || RSA_1024.equals(receiverAsymAlgo) || RSA_2048.equals(receiverAsymAlgo) || RSA_4096.equals(receiverAsymAlgo)) {
            // 使用 SM4_CBC_PKCS7_PADDING 加密私钥
            BlockSymAlgo symAlgo = new BlockSymAlgo(SymAlgoEnum.SM4.getDesc(), BlockEnum.CBC.getDesc(), PaddingEnum.PKCS7_PADDING.getDesc());
            byte[] iv = clusterEngine.getRandom(symAlgo.getIvLength());
            // 生成对称密钥
            byte[] sessionKey = clusterEngine.getRandom(SESSION_KEY_LENGTH);
            // 使用公钥加密对称密钥
            byte[] encryptedSessionKey = clusterEngine.pubEnc(receiverPublicKey, sessionKey);
            byte[] encryptedPrvKey = clusterEngine.symmetricEnc(symAlgo, sessionKey, iv, plainPriKey);

            // 包装对称算法
            byte[] symmetricCipherOIDBytes;
            try {
                AlgorithmIdentifier blockAlgo = new AlgorithmIdentifier(symAlgo.getOid(), new DEROctetString(iv));
                symmetricCipherOIDBytes = blockAlgo.getEncoded();
            } catch (IOException e) {
                throw BaseInternalError.ALGORITHM_IDENTIFIER_ENCODE_ERROR.toException(e);
            }

            return new EnvelopedKeyPairData(encryptedSessionKey,
                    encryptedPrvKey,
                    symmetricCipherOIDBytes);
        } else {
            //ECC 暂不支持
            throw BaseValidationError.ALGORITHM_NOT_SUPPORTED.toException(UNSUPPORTED_KEY_TYPE_TYPE_IS_I18N_KEY, receiverAsymAlgo.getAlgoName());
        }
    }

    /**
     * 获取私钥的对称加密算法
     *
     * @param asymAlgo 非对称算法类型
     * @return
     */
    public static BlockSymAlgo getPriKeySymEncAlgo(AsymAlgo asymAlgo) {
        if (SM2.equals(asymAlgo)) {
            return BlockSymAlgo.SM4_ECB_NOPADDING;
        } else if (RSA_1024.equals(asymAlgo) || RSA_2048.equals(asymAlgo) || RSA_4096.equals(asymAlgo)) {
            return BlockSymAlgo.SM4_ECB_PADDING5;
        }
        // ECC 暂不支持
        throw BaseValidationError.ALGORITHM_NOT_SUPPORTED.toException(UNSUPPORTED_KEY_TYPE_TYPE_IS_I18N_KEY, asymAlgo.getAlgoName());
    }
}
