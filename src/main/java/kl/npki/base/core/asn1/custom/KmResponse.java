package kl.npki.base.core.asn1.custom;

import kl.nbase.security.asn1.*;
import kl.nbase.security.asn1.custom.gm.gm0014.km.response.ErrorPkgRespond;

/**
 * KM 响应结构
 *
 * KmResponse ::= CHOICE {
 *     caFastAccessResponse      [0] IMPLICIT CaFastAccessResponse,
 *     caSiteCertResponse        [1] IMPLICIT CaSiteCertResponse,
 *     errorPkgRespond           [2] IMPLICIT ErrorPkgRespond
 * }
 *
 * <AUTHOR>
 */
public class KmResponse extends ASN1Object implements ASN1Choice {

    private final int tagNo;
    private final ASN1Encodable value;


    public KmResponse(CaFastAccessResponse caFastAccessResponse) {
        this.tagNo = KmResponse.Choice.CA_FAST_ACCESS_RESPONSE.getTagNo();
        this.value = caFastAccessResponse;
    }

    public KmResponse(CaSiteCertResponse caSiteCertResponse) {
        this.tagNo = KmResponse.Choice.CA_SITE_CERT_RESPONSE.getTagNo();
        this.value = caSiteCertResponse;
    }

    public KmResponse(ErrorPkgRespond errorPkgRespond) {
        this.tagNo = KmResponse.Choice.ERROR_PKG_RESPOND.getTagNo();
        this.value = errorPkgRespond;
    }

    private KmResponse(ASN1TaggedObject choice) {
        int tmpTagNo = choice.getTagNo();

        switch (tmpTagNo) {
            case 0:
                value = CaFastAccessResponse.getInstance(choice, false);
                break;
            case 1:
                value = CaSiteCertResponse.getInstance(choice, false);
                break;
            case 2:
                value = ErrorPkgRespond.getInstance(choice, false);
                break;
            default:
                throw new IllegalArgumentException("Unknown tag encountered: " + ASN1Util.getTagText(choice));
        }

        this.tagNo = tmpTagNo;
    }

    public static KmResponse getInstance(Object obj) {
        if (obj instanceof KmResponse) {
            return (KmResponse) obj;
        }

        if (obj != null) {
            return new KmResponse(ASN1TaggedObject.getInstance(obj));
        }

        return null;
    }

    public int getTagNo() {
        return tagNo;
    }

    public ASN1Encodable getValue() {
        return value;
    }

    @Override
    public ASN1Primitive toASN1Primitive() {
        return new DERTaggedObject(false, tagNo, value);
    }

    public enum Choice {
        CA_FAST_ACCESS_RESPONSE(0),
        CA_SITE_CERT_RESPONSE(1),
        ERROR_PKG_RESPOND(2);
        private final int tagNo;

        Choice(int tagNo) {
            this.tagNo = tagNo;
        }

        public int getTagNo() {
            return tagNo;
        }
    }
}
