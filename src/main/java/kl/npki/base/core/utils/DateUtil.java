package kl.npki.base.core.utils;

import kl.nbase.helper.utils.LocalDateTimeUtils;
import kl.nbase.i18n.locale.LocaleContext;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;

/**
 * @Author: guoq
 * @Date: 2023/10/10
 * @description: 日期工具类， 后续业务可以继承该类实现日期格式化
 */
public class DateUtil {

    private DateUtil() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }


    /**
     * （年月日） 日期格式 - 读取配置后通过base-service赋值
     */
    public static String dayPattern = "yyyy-MM-dd";


    /**
     * （年月日时分秒） 日期格式 - 读取配置后通过base-service赋值
     * 该配置适用于前端界面展示字段序列化
     */
    public static String dateTimePattern = "yyyy-MM-dd HH:mm:ss";

    /**
     * （年月日时分秒、毫秒） 日期格式 - 读取配置后通过base-service赋值
     */
    public static String dateTimeMsPattern = "yyyy-MM-dd HH:mm:ss.SSS";


    /**
     * （年月日时分秒） 文件日期格式  - 读取配置后通过base-service赋值
     */
    public static String fileDateTimePattern = "yyyyMMddHHmmss";



    /**
     * 格式化日志 前端格式
     * @param date 时间
     * @return yyyy年MM月dd日 HH时mm分ss秒
     */
    public static String formatFrontDate(Date date){
        /// String dateFormat = getBaseCoreI18nMessage("date_format"); yyyy年MM月dd日 HH时mm分ss秒
        SimpleDateFormat front = new SimpleDateFormat(dateTimePattern, LocaleContext.get());
        return front.format(date);
    }

    /**
     * 格式化日期 文件名格式
     * @param date 时间
     * @return yyyy年MM月dd日 HH时mm分ss秒
     */
    public static String formatFileDate(Date date){
        SimpleDateFormat front = new SimpleDateFormat(fileDateTimePattern);
        return front.format(date);
    }

    /**
     * 格式化日期 KCSP上传时间格式
     * @param date 时间
     * @return yyyy-MM-dd日 HH:mm:ss
     */
    public static String formatKcspDate(Date date){
        SimpleDateFormat front = new SimpleDateFormat(dateTimePattern);
        return front.format(date);
    }

    /**
     * 自定义日期时间格式化
     *
     * @param date    时间
     * @param pattern 日期格式
     * @return String
     */
    public static String formatDateByPattern(Date date, String pattern) {
        SimpleDateFormat front = new SimpleDateFormat(pattern);
        return front.format(date);
    }

    /**
     * 自定义日期时间格式化
     *
     * @param localDateTime localDateTime
     * @param pattern       pattern
     * @return String
     */
    public static String formatLocalDateTimeByPattern(LocalDateTime localDateTime, String pattern) {
        Date date = LocalDateTimeUtils.parseDate(localDateTime);
        return formatDateByPattern(date, pattern);
    }

    /**
     * Parses an ISO 8601 formatted timestamp string into a LocalDateTime object.
     *
     * @param timestamp the ISO 8601 formatted timestamp string to parse
     * @return the parsed LocalDateTime object
     * @throws DateTimeParseException if the timestamp cannot be parsed
     * @throws IllegalArgumentException if the timestamp is null or its length is less than 20 characters
     */
    public static LocalDateTime parseISOTimestamp(String timestamp) throws DateTimeParseException {
        if (timestamp == null || timestamp.length() < 20) {
            throw new IllegalArgumentException("invalid timestamp '" + timestamp + "'");
        }

        try {
            return LocalDateTime.parse(timestamp, DateTimeFormatter.ISO_DATE_TIME);
        } catch (DateTimeParseException ex) {
            throw new IllegalArgumentException("invalid timestamp '" + timestamp + "': " + ex.getMessage());
        }
    }
}
