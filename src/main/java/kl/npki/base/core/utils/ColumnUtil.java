package kl.npki.base.core.utils;

import kl.nbase.helper.utils.ReflectionUtils;
import kl.npki.base.core.exception.BaseInternalError;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Method;
import java.util.function.Function;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.FAILED_TO_OBTAIN_FIELD_NAME_I18N_KEY;
/**
 * <AUTHOR>
 * @date 2022/11/28 11:05
 * @Description: Java8通过Function函数获取字段名称(获取实体类的字段名称)
 */
public class ColumnUtil {

    private static final String GET_METHOD_PREFIX = "get";
    private static final String IS_METHOD_PREFIX = "is";

    /**
     * 获取实体类的字段名称(实体声明的字段名称)
     */
    public static <T> String getFieldName(SFunction<T, ?> fn) {
        SerializedLambda serializedLambda = getSerializedLambda(fn);
        // 获取getter方法
        String getter = serializedLambda.getImplMethodName();
        // 解析方法名称
        return resolveFieldName(getter);
    }

    /**
     * 通过反射调用实现序列化接口函数对象的writeReplace方法，从而拿到{@link SerializedLambda}<br>
     * 该对象中包含了lambda表达式的所有信息。
     * @param lambda
     * @return
     */
    private static SerializedLambda getSerializedLambda(Serializable lambda) {
        // 从function取出序列化方法
        try {
            Method writeReplaceMethod = lambda.getClass().getDeclaredMethod("writeReplace");
            // 从序列化方法取出序列化的lambda信息
            boolean isAccessible = writeReplaceMethod.isAccessible();
            ReflectionUtils.makeAccessible(writeReplaceMethod);
            SerializedLambda serializedLambda = (SerializedLambda) writeReplaceMethod.invoke(lambda);
            writeReplaceMethod.setAccessible(isAccessible);
            return serializedLambda;
        } catch (Exception e) {
            throw BaseInternalError.LAMBDA_COLUMN_NAME_ERROR.toException(FAILED_TO_OBTAIN_FIELD_NAME_I18N_KEY, e);
        }

    }

    private static String resolveFieldName(String getMethodName) {
        if (getMethodName.startsWith(GET_METHOD_PREFIX)) {
            getMethodName = getMethodName.substring(3);
        } else if (getMethodName.startsWith(IS_METHOD_PREFIX)) {
            getMethodName = getMethodName.substring(2);
        }
        // 小写第一个字母
        return firstToLowerCase(getMethodName);
    }

    private static String firstToLowerCase(String param) {
        if (StringUtils.isBlank(param)) {
            return "";
        }
        return param.substring(0, 1).toLowerCase() + param.substring(1);
    }

    /**
     * 使Function获取序列化能力
     */
    @FunctionalInterface
    public interface SFunction<T, R> extends Function<T, R>, Serializable {
    }
}
