package kl.npki.base.core.utils;

import kl.nbase.emengine.entity.EngineAlgoInfo;
import kl.nbase.emengine.manager.engine.EngineFactory;
import kl.nbase.rpc.core.utils.StringUtils;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.configs.ClusterEmConfig;
import kl.npki.base.core.configs.EmConfig;
import kl.npki.base.core.configs.EmLoadBalancerConfig;
import kl.npki.base.core.configs.GroupEmConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.EmTypeEnum;
import kl.npki.base.core.constant.EngineLoadBalancerEnum;
import kl.npki.base.core.exception.BaseInternalError;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;

import static kl.npki.base.core.constant.BaseConstant.WEB_ROOT;
import static kl.npki.base.core.constant.I18nExceptionInfoConstant.COULD_NOT_CREATE_KEYSTORE_DIRECTORY_I18N_KEY;
/**
 * <AUTHOR>
 */
public class EngineUtil {

    private EngineUtil() {
    }

    private static final Random RANDOM = new SecureRandom();

    /**
     * 默认加密机名称格式
     */
    public static final String DEFAULT_ENGINE_NAME_FORMAT = "engine_%s";

    /**
     * 高性能密码机密钥信息数据库的目录名称格式：emulator_${engineName}_keystore
     */
    public static final String EMULATOR_ENGINE_DB_DIR_FORMAT = "emulator_%s_keystore";

    /**
     * 密码设备默认权重值
     */
    public static int ENGINE_DEFAULT_WEIGHT = 1;

    /**
     * 获取所有的加密机引擎算法信息
     *
     * @return
     */
    public static List<EngineAlgoInfo> getAllEngineAlgorithm() {
        List<EngineAlgoInfo> allEngine = EngineFactory.INSTANCE.getAllEngine();
        return allEngine;
    }

    public static EngineAlgoInfo getEngineAlgoByType(EmTypeEnum emTypeEnum) {
        EngineAlgoInfo engineAlgoInfo = new EngineAlgoInfo(emTypeEnum.getEngineTypeEnum());
        if (EmTypeEnum.isFileEngine(emTypeEnum.getEngineType())) {
            engineAlgoInfo.setFileAlgoAbility();
        } else {
            engineAlgoInfo.setFusionAlgoAbility(emTypeEnum.getFusionDeviceTypeEnum().getJavaHandlerName());
        }
        return engineAlgoInfo;
    }

    /**
     * 判断是否为文件类型的加密机
     *
     * @return
     */
    public static boolean isFileEngine(String engineType) {
        return EmTypeEnum.isFileEngine(engineType);
    }

    /**
     * 构建文件类型的加密机配置
     *
     * @return 文件加密机配置
     */
    public static GroupEmConfig buildGenericFileGroupEngineConfig(String groupName) {
        GroupEmConfig groupFileEmConfig = new GroupEmConfig();
        List<EmConfig> fielEngines = new ArrayList<>();
        String keystoreFilePath = WEB_ROOT + "keystore";

        EmConfig fielEmConfig = new EmConfig();
        fielEmConfig.setEngineName(String.format(DEFAULT_ENGINE_NAME_FORMAT, RANDOM.nextInt(5)));
        fielEmConfig.setEncIndexes(buildKeyIndex());
        fielEmConfig.setSignIndexes(buildKeyIndex());
        fielEmConfig.setDefaultIndexCred(null);
        fielEmConfig.setUsername("root");
        fielEmConfig.setPath(keystoreFilePath);
        fielEmConfig.setEngineType(EmTypeEnum.FILE_ENGINE.getEngineType());

        fielEngines.add(fielEmConfig);
        groupFileEmConfig.setEngines(fielEngines);
        groupFileEmConfig.setGroupName(groupName);
        // 负载策略，默认使用轮询策略
        EmLoadBalancerConfig emLoadBalancerConfig = new EmLoadBalancerConfig();
        emLoadBalancerConfig.setType(EngineLoadBalancerEnum.ROUND_ROBIN.name());
        // 预置权重值配置以便修改，权重策略下才会生效
        Map<String, Integer> engineWeightMap = new HashMap<>();
        engineWeightMap.put(fielEmConfig.getEngineName(), ENGINE_DEFAULT_WEIGHT);
        emLoadBalancerConfig.setWeight(engineWeightMap);
        groupFileEmConfig.setLoadBalancer(emLoadBalancerConfig);
        // 初始化keystore目录
        File keystoreFile = new File(keystoreFilePath);
        if (keystoreFile.exists()) {
            try {
                FileUtils.deleteDirectory(keystoreFile);
            } catch (IOException e) {
                // ignore
            }
        }
        boolean success = keystoreFile.mkdir();
        if (!success) {
            throw BaseInternalError.FILE_CREATE_FAIL.toException(COULD_NOT_CREATE_KEYSTORE_DIRECTORY_I18N_KEY, keystoreFile.getAbsolutePath());
        }
        return groupFileEmConfig;
    }

    private static Map<String, String> buildKeyIndex() {
        Map<String, String> keyIndexMap = new LinkedHashMap<>();
        keyIndexMap.put(AsymAlgo.SM2.getAlgoName(), buildIndexPair(1, 3));
        keyIndexMap.put(AsymAlgo.RSA_2048.getAlgoName(), buildIndexPair(1, 3));
        return keyIndexMap;
    }

    private static String buildIndexPair(int startIndex, int endIndex) {
        StringBuilder indexPair = new StringBuilder();
        for (int i = startIndex; i <= endIndex; i++) {
            indexPair.append(i);
            if (i != endIndex) {
                indexPair.append(",");
            }
        }
        return indexPair.toString();
    }

    /**
     * 获取所有加密机名称
     *
     * @return {@link List }<{@link String }>
     */
    public static Set<String> getAllEngineNames() {
        // 获取所有加密机名称
        ClusterEmConfig clusterEmConfig = BaseConfigWrapper.getClusterEmConfig();
        Set<String> engineNames = clusterEmConfig.getGroupEngine().getEngines().stream().map(EmConfig::getEngineName).collect(Collectors.toSet());
        // 获取辅助加密机名称
        EmConfig backupEngine = clusterEmConfig.getGroupEngine().getBackupEngine();
        if (backupEngine != null && StringUtils.isNotBlank(backupEngine.getEngineType()) && clusterEmConfig.getGroupEngine().isEnableBackupEngine()) {
            engineNames.add(backupEngine.getEngineName());
        }
        return engineNames;
    }

}
