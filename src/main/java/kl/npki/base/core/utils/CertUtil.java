package kl.npki.base.core.utils;

import kl.nbase.security.asn1.*;
import kl.nbase.security.asn1.pkcs.PrivateKeyInfo;
import kl.nbase.security.asn1.x500.RDN;
import kl.nbase.security.asn1.x500.X500Name;
import kl.nbase.security.asn1.x500.style.BCStyle;
import kl.nbase.security.asn1.x500.style.RFC4519Style;
import kl.nbase.security.asn1.x509.*;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.cert.jcajce.JcaCertStore;
import kl.nbase.security.cms.CMSProcessableByteArray;
import kl.nbase.security.cms.CMSSignedData;
import kl.nbase.security.cms.CMSSignedDataGenerator;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.nbase.security.jce.provider.BouncyCastleProvider;
import kl.nbase.security.openssl.jcajce.JcaPEMWriter;
import kl.nbase.security.pkix.custom.cms.sign.SignedDataParse;
import kl.nbase.security.util.Arrays;
import kl.nbase.security.util.encoders.Base64;
import kl.nbase.security.util.encoders.Hex;
import kl.nbase.security.utils.AsymKeyUtil;
import kl.nbase.security.utils.PEMUtil;
import kl.nbase.security.utils.SignatureAlgoUtil;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.exception.BaseValidationError;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.math.BigInteger;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.*;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAKey;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.*;

/**
 * 证书工具类
 *
 * <AUTHOR>
 * @date 2022/8/10
 */
public class CertUtil {

    private static final Logger logger = LoggerFactory.getLogger(CertUtil.class);

    private static final SecureRandom RAND = new SecureRandom();

    public static final String CERT_TYPE_CA = "CA";

    public static final String CERT_TYPE_ENTITY = "ENTITY";


    static {
        Provider bc = Security.getProvider(BouncyCastleProvider.PROVIDER_NAME);
        if (bc == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    private CertUtil() {
        throw new IllegalStateException("CertUtils");
    }


    /**
     * 根据证书获取颁发者 KeyId
     *
     * @param cert 证书
     * @return 16进制
     */
    public static String getAuthorityKeyId(Certificate cert) {
        Extension authorityKeyIdExtension = cert
            .getTBSCertificate()
            .getExtensions()
            .getExtension(Extension.authorityKeyIdentifier);

        if (ObjectUtils.isEmpty(authorityKeyIdExtension)) {
            if (cert.getSubject().equals(cert.getIssuer())) {
                // 根证书的授权者密钥标识符允许为空
                return null;
            } else {
                throw BaseValidationError.ISSUER_AUTHORITY_KEY_ID_IS_NULL.toException(CERTIFICATE_DATA_FORMAT_ABNORMALITY_I18N_KEY);
            }
        }
        byte[] octets = authorityKeyIdExtension.getExtnValue().getOctets();
        AuthorityKeyIdentifier instance = AuthorityKeyIdentifier.getInstance(octets);

        return HexUtil.encodeHexString(instance.getKeyIdentifier());
    }

    /**
     * 判断证书的密钥用法是否包含了keyUsages，如果需要对多个密钥用法进行判断，则将它们进行或运算后通过keyUsages参数传入。
     *
     * @param cert
     * @param keyUsages
     */
    public static boolean hasKeyUsages(Certificate cert, int keyUsages) {
        if (ObjectUtils.isEmpty(cert)) {
            return false;
        }
        Extension keyUsageExt = cert
            .getTBSCertificate()
            .getExtensions()
            .getExtension(Extension.keyUsage);
        if (ObjectUtils.isEmpty(keyUsageExt)) {
            return false;
        }
        KeyUsage keyUsage = KeyUsage.getInstance(keyUsageExt.getExtnValue().getOctets());
        return keyUsage.hasUsages(keyUsages);
    }

    /**
     * 根据证书获取KeyId
     *
     * @param cert 证书
     * @return 16进制
     */
    public static String getKeyId(Certificate cert) {
        Extension subjectKeyIdExtension = cert
            .getTBSCertificate()
            .getExtensions()
            .getExtension(Extension.subjectKeyIdentifier);

        if (ObjectUtils.isEmpty(subjectKeyIdExtension)) {
            throw BaseValidationError.SUBJECT_KEY_ID_IS_NULL.toException(CERTIFICATE_DATA_FORMAT_ABNORMALITY_I18N_KEY);
        }
        byte[] octets = subjectKeyIdExtension.getExtnValue().getOctets();
        SubjectKeyIdentifier instance = SubjectKeyIdentifier.getInstance(octets);

        return HexUtil.encodeHexString(instance.getKeyIdentifier());
    }

    /**
     * 使用Base64编码证书
     *
     * @param cert 证书
     * @return base64编码的证书值
     */
    public static String encodeCertWithBase64(Certificate cert) {
        try {
            return Base64Util.base64Encode(cert.getEncoded());
        } catch (IOException e) {
            logger.error("Certificate encodeCertWithBase64 error", e);
            throw BaseValidationError.PARAM_ERROR.toException(CERTIFICATE_ENCODING_EXCEPTION_I18N_KEY);
        }
    }


    /**
     * 获取X500Name的CN项
     *
     * @param name X500Name
     * @return CommonName
     */
    public static String getCommonName(X500Name name) {
        return getRdnValue(name, BCStyle.CN);
    }

    /**
     * 获取X500Name的C项
     *
     * @param name
     * @return
     */
    public static String getCountry(X500Name name) {
        return getRdnValue(name, BCStyle.C);
    }

    /**
     * 获取X500Name的指定项
     *
     * @param name X500Name
     * @param name BCStyle类中的常量
     * @return CommonName
     */
    public static String getRdnValue(X500Name name, ASN1ObjectIdentifier attributeType) {
        RDN[] rdns = name.getRDNs(attributeType);
        if (Arrays.isNullOrEmpty(rdns)) {
            return null;
        }
        return ((ASN1String) rdns[0].getFirst().getValue()).getString();
    }

    /**
     * 获取证书开始生效时间
     *
     * @param cert
     * @return
     */
    public static LocalDateTime getValidateFrom(Certificate cert) {
        return cert.getStartDate().getDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * 获取证书到期时间
     *
     * @param cert
     * @return
     */
    public static LocalDateTime getValidateTo(Certificate cert) {
        return cert.getEndDate().getDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * 将日期修改为当天的0时0分0秒0纳秒
     *
     * @param beforeDate 待修改的日期
     * @return 日期
     */
    public static Date getZeroEffectiveBeforeDate(Date beforeDate) {
        ZoneId zoneId = ZoneId.systemDefault();
        Instant beforeDateIns = beforeDate.toInstant();
        LocalDateTime beforeDateTime = beforeDateIns.atZone(zoneId).toLocalDateTime();
        beforeDateTime = beforeDateTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
        return Date.from(beforeDateTime.atZone(zoneId).toInstant());
    }

    /**
     * 将日期修改为当天的23时59分59秒0纳秒
     *
     * @param afterDate 待修改的日期
     * @return 日期
     */
    public static Date getZeroEffectiveAfterDate(Date afterDate) {
        ZoneId zoneId = ZoneId.systemDefault();
        Instant afterDateIns = afterDate.toInstant();
        LocalDateTime afterDateTime = afterDateIns.atZone(zoneId).toLocalDateTime();
        afterDateTime = afterDateTime.withHour(23).withMinute(59).withSecond(59).withNano(0);
        return Date.from(afterDateTime.atZone(zoneId).toInstant());
    }

    /**
     * 解析证书
     *
     * @param certBytes 证书字节数组
     * @return 证书
     */
    public static Certificate parseCert(byte[] certBytes) {
        Certificate cert;
        try {
            cert = Certificate.getInstance(certBytes);
        } catch (Exception e) {
            List<Certificate> certs = parseCertFromP7(certBytes);
            cert = certs.get(0);
        }
        return cert;
    }

    /**
     * 解析证书
     *
     * @param certStr 证书字节数组
     * @return 证书
     */
    public static X509Certificate parseX509Cert(String certStr) {

        try {
            String b64Cert = PEMUtil.unFormatCert(certStr);
            byte[] certBytes = Base64.decode(b64Cert);
            return kl.nbase.security.utils.CertUtil.derToX509(certBytes);
        } catch (Exception e) {
            logger.error("Certificate parseX509Cert error", e);
            throw BaseInternalError.CERT_PARSE_FAILED.toException(CERTIFICATE_PARSE_ERROR_TIPS_I18N_KEY);
        }
    }

    /**
     * 解析证书
     *
     * @param certBytes 证书字节数组
     * @return 证书
     */
    public static List<Certificate> parseCertFromP7(byte[] certBytes) {
        try {
            // 使用PKCS7解析证书
            SignedDataParse signedDataParse = new SignedDataParse(certBytes);
            return signedDataParse.getCertificates();
        } catch (Exception e) {
            logger.error("Parsing cert from P7 error", e);
            throw BaseValidationError.PARAM_ERROR.toException(CERTIFICATE_ENCODING_EXCEPTION_I18N_KEY);
        }
    }

    /**
     * 解析证书
     *
     * @param b64Cert base64编码的证书
     * @return 证书
     */
    public static Certificate parseCert(String b64Cert) {
        return parseCert(Base64.decode(b64Cert));
    }

    /**
     * 解析证书
     *
     * @param pemCert base64编码的证书
     * @return 证书
     */
    public static Certificate parsePemCert(String pemCert) {
        try {
            String b64Cert = PEMUtil.unFormatCert(pemCert);
            return parseCert(Base64.decode(b64Cert));
        } catch (Exception e) {
            logger.error("Certificate parsePemCert error", e);
            throw BaseInternalError.CERT_PARSE_FAILED.toException(CERTIFICATE_PARSE_ERROR_TIPS_I18N_KEY);
        }
    }

    /**
     * 解析Base64编码的证书（不包括DER二进制），支持一个文件包括多个证书的解析
     *
     * @param multipleB64Cert 证书值
     * @return Map中的key是证书类型，value则是对应类型的证书列表。key的可选值有"CA"和“ENTITY”（本类有定义相关常量），用于指明证书列表中的是CA证书还是实体证书。
     */
    public static Map<String, List<Certificate>> parseMultipleCert(String multipleB64Cert) {
        try (StringReader reader = new StringReader(multipleB64Cert);
             MultipleCertReader certReader = new MultipleCertReader(reader)) {
            List<String> b64CertList = certReader.readObject();

            Map<String, List<Certificate>> certMap = new HashMap<>();
            List<Certificate> entityCerts = new ArrayList<>();
            List<Certificate> caCerts = new ArrayList<>();

            Certificate cert;
            for (String b64Cert : b64CertList) {
                cert = parseCert(b64Cert);
                if (isCaCert(cert)) {
                    caCerts.add(cert);
                } else {
                    entityCerts.add(cert);
                }
            }
            certMap.put(CERT_TYPE_ENTITY, entityCerts);
            certMap.put(CERT_TYPE_CA, caCerts);

            return certMap;
        } catch (Exception e) {
            logger.error("Certificate parseMultipleCert error", e);
            throw BaseValidationError.CERT_PARSE_FAILED.toException(CERTIFICATE_PARSE_ERROR_TIPS_I18N_KEY);
        }
    }

    /**
     * 判断证书是否为CA证书
     * <p>
     * 判断依据：基本限制扩展项中的SubjectType为CA
     *
     * @param certificate 证书对象 {@link Certificate}
     * @return true:CA证书，false:实体证书
     */
    public static boolean isCaCert(Certificate certificate) {
        if (certificate == null) {
            return false;
        }

        Extensions extensions = certificate.getTBSCertificate().getExtensions();
        if (extensions == null) {
            return false;
        }

        Extension extension = extensions.getExtension(Extension.basicConstraints);
        if (extension == null) {
            return false;
        } else {
            ASN1Encodable asn1 = extension.getParsedValue();
            return BasicConstraints.getInstance(asn1).isCA();
        }
    }

    public static String getHexSnOfCert(Certificate cert) {
        return getHexSnOfCert(cert.getSerialNumber());
    }

    public static String getCertIssuerCn(Certificate cert) {
        return cert.getIssuer().getRDNs(BCStyle.CN)[0].getFirst().getValue().toString();
    }

    public static String getHexSnOfCert(ASN1Integer certSn) {
        return Hex.toHexString(certSn.getValue().toByteArray()).toUpperCase();
    }

    /**
     * 以当前时间为起始，计算证书有效期
     *
     * @param validDays 证书有效天数
     * @return Date[0] 有效期-开始时间 Date[1] 有效期-结束时间
     */
    public static Date[] getCertValidTimeFromNow(long validDays) {
        return getCertValidTime(new Date(), validDays);
    }

    /**
     * 以startTime为起始，计算证书有效期
     *
     * @param validDays 证书有效天数
     * @return Date[0] 有效期-开始时间 Date[1] 有效期-结束时间
     */
    public static Date[] getCertValidTime(Date startTime, long validDays) {
        ZoneId zoneId = ZoneId.systemDefault();

        Instant startIns = startTime.toInstant();
        LocalDateTime beforeTime = startIns.atZone(zoneId).toLocalDateTime().withNano(0);

        LocalDateTime afterTime = beforeTime.plusDays(validDays);

        Date certValidityBefore = Date.from(beforeTime.atZone(zoneId).toInstant());
        Date certValidityAfter = Date.from(afterTime.atZone(zoneId).toInstant());

        return new Date[]{certValidityBefore, certValidityAfter};
    }

    /**
     * 生成证书序列号
     *
     * @return 证书序列号
     */
    public static BigInteger genCertSn() {

        // 生成证书序列号
        // 用随机数的方式生成
        final byte[] hexByte = new byte[16];
        RAND.nextBytes(hexByte);
        // 为了防止产生的序列号以00开头，这里将hexByte的首字节设置成'1'，对应ASCII的十六进制为0x31
        hexByte[0] = 0x31;
        return new BigInteger(1, hexByte);
    }

    public static PublicKey getPublicKey(SubjectPublicKeyInfo publicKeyInfo) throws IOException {
        return BouncyCastleProvider.getPublicKey(publicKeyInfo);
    }

    public static PrivateKey getPrivateKey(PrivateKeyInfo privateKeyInfo) throws IOException {
        return BouncyCastleProvider.getPrivateKey(privateKeyInfo);
    }

    public static HashAlgo getDefaultHashAlgo(PublicKey key) {
        return (key instanceof RSAKey) ? HashAlgo.SHA256 : HashAlgo.SM3;
    }

    public static AsymAlgo getDefaultAsyAlgo(PublicKey key) {
        return (key instanceof RSAKey) ? AsymAlgo.RSA_4096 : AsymAlgo.SM2;
    }

    public static AsymAlgo getAsymAlgo(Certificate cert) {
        // 获取颁发者证书算法类型
        SubjectPublicKeyInfo publicKeyInfo = cert.getSubjectPublicKeyInfo();
        PublicKey publicKey = AsymKeyUtil.x509Bytes2PublicKey(publicKeyInfo);
        return AsymAlgo.valueOf(publicKey);
    }

    /**
     * 获取默认的签名算法OID
     *
     * @param key
     * @return
     */
    public static String getDefaultSignAlgoOid(PublicKey key) {
        HashAlgo defaultHashAlgo = getDefaultHashAlgo(key);
        AsymAlgo asymmetricAlgorithm = getDefaultAsyAlgo(key);
        // 根据摘要算法和非对称算法获取到对应的签名算法OID
        return SignatureAlgoUtil.getOid(asymmetricAlgorithm, defaultHashAlgo);
    }

    /**
     * 从证书中获取 sansList
     *
     * @param certificate 证书
     * @return 包含 sans 的列表
     */
    public static List<String> getSansListFromCertificate(Certificate certificate) {
        Extensions extensions = certificate.getTBSCertificate()
            .getExtensions();
        // 没有 extensions 时返回空
        if (extensions == null || extensions.getExtensionOIDs().length == 0) {
            return Collections.emptyList();
        }
        int extensionCount = extensions.getExtensionOIDs().length;
        for (int i = 0; i < extensionCount; i++) {
            Extension extension = extensions.getExtension(extensions.getExtensionOIDs()[i]);
            // 找到 Subject Alternative Name
            if (Extension.subjectAlternativeName.toString()
                .equals(extension.getExtnId()
                    .toString())) {
                byte[] data = extension.getExtnValue()
                    .getOctets();
                return getAlternativeNames(data);
            }
        }
        return Collections.emptyList();
    }

    private static List<String> getAlternativeNames(byte[] extVal) {
        if (extVal == null) {
            return Collections.emptyList();
        }
        try {
            ASN1Null.getInstance(extVal);
            return Collections.emptyList();
        } catch (Exception e) {
            // ignore
        }
        try {
            ArrayList<String> sansList = new ArrayList<>();
            Iterator<ASN1Encodable> it = ASN1Sequence.getInstance(extVal).iterator();
            while (it.hasNext()) {
                GeneralName genName = GeneralName.getInstance(it.next());
                switch (genName.getTagNo()) {
                    case GeneralName.ediPartyName:
                    case GeneralName.x400Address:
                    case GeneralName.otherName:
                        sansList.add(java.util.Arrays.toString(genName.getEncoded()));
                        break;
                    case GeneralName.directoryName:
                        sansList.add(X500Name.getInstance(RFC4519Style.INSTANCE, genName.getName()).toString());
                        break;
                    case GeneralName.dNSName:
                    case GeneralName.rfc822Name:
                    case GeneralName.uniformResourceIdentifier:
                        sansList.add(((ASN1String) genName.getName()).getString());
                        break;
                    case GeneralName.registeredID:
                        sansList.add(ASN1ObjectIdentifier.getInstance(genName.getName()).getId());
                        break;
                    case GeneralName.iPAddress:
                        byte[] addrBytes = ASN1OctetString.getInstance(genName.getName()).getOctets();
                        String addr = getHostAddress(addrBytes);
                        if (addr != null) {
                            sansList.add(addr);
                        }
                        break;
                    default:
                        throw new IOException("Bad tag number: " + genName.getTagNo());
                }
            }
            if (CollectionUtils.isEmpty(sansList)) {
                return Collections.emptyList();
            }
            return sansList;
        } catch (Exception e) {
            logger.error("Encoding or decoding failed while extracting SANs", e);
            throw BaseValidationError.SANS_PARSING_ERROR_FROM_EXT.toException(ENCODING_OR_DECODING_FAILED_WHILE_EXTRACTING_SANS_I18N_KEY);
        }
    }

    /**
     * 获取IP地址
     *
     * @param addrBytes
     * @return
     */
    private static String getHostAddress(byte[] addrBytes) {
        try {
            return InetAddress.getByAddress(addrBytes).getHostAddress();
        } catch (UnknownHostException e) {
            // 返回 null 表示遇到异常时跳过
            return null;
        }
    }

    /**
     * 将X509证书转换为PEM格式
     *
     * @param certificate
     * @return
     */
    public static String convertX509CertToPEM(X509Certificate certificate) {
        StringWriter stringWriter = new StringWriter();
        try (JcaPEMWriter pemWriter = new JcaPEMWriter(stringWriter)) {
            pemWriter.writeObject(certificate);
        } catch (IOException e) {
            throw BaseInternalError.FILE_WRITE_FAIL.toException("PEM file generation failed", e);
        }
        return stringWriter.toString();
    }

    /**
     * 生成p7b证书链
     *
     * @param certList 证书链
     * @return p7b证书内容
     */
    public static byte[] genP7b(List<X509Certificate> certList) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 使用Bouncy Castle创建证书存储
            JcaCertStore certs = new JcaCertStore(certList);

            // 创建CMS签名数据生成器
            CMSSignedDataGenerator gen = new CMSSignedDataGenerator();

            // 添加证书到生成器
            gen.addCertificates(certs);

            // 创建CMS签名数据，不带签名信息
            CMSSignedData signedData = gen.generate(new CMSProcessableByteArray(new byte[0]), true);

            // 输出P7B文件
            outputStream.write(signedData.getEncoded());
            return outputStream.toByteArray();
        } catch (Exception e) {
            throw BaseInternalError.CERT_P7B_ERROR.toException(e);
        }
    }
}
