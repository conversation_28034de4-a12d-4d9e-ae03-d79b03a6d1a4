package kl.npki.base.core.utils;

import kl.nbase.security.asn1.*;
import kl.nbase.security.asn1.cms.CMSObjectIdentifiers;
import kl.nbase.security.asn1.cms.EnvelopedData;
import kl.nbase.security.asn1.custom.cms.EncryptedContentInfo;
import kl.nbase.security.asn1.custom.pkcs.ExtendedCertificateOrCertificate;
import kl.nbase.security.asn1.custom.pkcs.RecipientInfo;
import kl.nbase.security.asn1.custom.pkcs.SignedAndEnvelopedData;
import kl.nbase.security.asn1.pkcs.IssuerAndSerialNumber;
import kl.nbase.security.asn1.pkcs.SignerInfo;
import kl.nbase.security.asn1.x500.X500Name;
import kl.nbase.security.asn1.x509.AlgorithmIdentifier;
import kl.nbase.security.asn1.x509.Certificate;
import kl.npki.base.core.exception.BaseValidationError;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> Yang
 * @date 2023-01-03 11:45
 */
public class PkcsUtils {

    private PkcsUtils() {
    }

    /**
     * 创建 {@link RecipientInfo}
     *
     * @param recipientName          接收者 DN
     * @param recipientSerialNumber  接收者序列号
     * @param keyEncryptionAlgorithm 密钥加密算法
     * @param encryptedKey           加密后的对称密钥（使用接收者的公钥加密）
     * @return {@link RecipientInfo}
     */
    public static RecipientInfo createRecipientInfo(final X500Name recipientName,
                                                    final BigInteger recipientSerialNumber,
                                                    final AlgorithmIdentifier keyEncryptionAlgorithm,
                                                    final byte[] encryptedKey) {
        return createRecipientInfo(
            new IssuerAndSerialNumber(recipientName, recipientSerialNumber),
            keyEncryptionAlgorithm,
            encryptedKey
        );
    }

    /**
     * 创建 {@link RecipientInfo}
     *
     * @param recipientName          接收者 DN
     * @param recipientSerialNumber  接收者序列号
     * @param keyEncryptionAlgorithm 密钥加密算法
     * @param encryptedKey           加密后的对称密钥（使用接收者的公钥加密）
     * @return {@link RecipientInfo}
     */
    public static RecipientInfo createRecipientInfo(final X500Name recipientName,
                                                    final BigInteger recipientSerialNumber,
                                                    final AlgorithmIdentifier keyEncryptionAlgorithm,
                                                    final ASN1OctetString encryptedKey) {
        return createRecipientInfo(
            new IssuerAndSerialNumber(recipientName, recipientSerialNumber),
            keyEncryptionAlgorithm,
            encryptedKey
        );
    }

    /**
     * 创建 {@link RecipientInfo}
     *
     * @param issuerAndSerialNumber  证书签发者的 DN 和序列号
     * @param keyEncryptionAlgorithm 密钥加密算法
     * @param encryptedKey           加密后的对称密钥（使用接收者的公钥加密）
     * @return {@link RecipientInfo}
     */
    public static RecipientInfo createRecipientInfo(final IssuerAndSerialNumber issuerAndSerialNumber,
                                                     final AlgorithmIdentifier keyEncryptionAlgorithm,
                                                     final ASN1OctetString encryptedKey) {

        return new RecipientInfo(
            new ASN1Integer(0),
            issuerAndSerialNumber,
            keyEncryptionAlgorithm,
            encryptedKey
        );
    }

    /**
     * 创建 {@link RecipientInfo}
     *
     * @param issuerAndSerialNumber  证书签发者的 DN 和序列号
     * @param keyEncryptionAlgorithm 密钥加密算法
     * @param encryptedKey           加密后的对称密钥（使用接收者的公钥加密）
     * @return {@link RecipientInfo}
     */
    private static RecipientInfo createRecipientInfo(final IssuerAndSerialNumber issuerAndSerialNumber,
                                                     final AlgorithmIdentifier keyEncryptionAlgorithm,
                                                     final byte[] encryptedKey) {

        return new RecipientInfo(
            new ASN1Integer(0),
            issuerAndSerialNumber,
            keyEncryptionAlgorithm,
            new DEROctetString(encryptedKey)
        );
    }

    /**
     * 创建 {@link EncryptedContentInfo}
     *
     * @param contentEncryptionAlgorithm 内容加密算法
     * @param encryptedContent           使用对称密钥加密后的内容
     * @return {@link EncryptedContentInfo}
     */
    public static EncryptedContentInfo createEncryptedContentInfo(final AlgorithmIdentifier contentEncryptionAlgorithm,
                                                                  final byte[] encryptedContent) {
        return createEncryptedContentInfo(contentEncryptionAlgorithm, new DEROctetString(encryptedContent));
    }

    public static EncryptedContentInfo createEncryptedContentInfo(final AlgorithmIdentifier contentEncryptionAlgorithm,
                                                                  final ASN1OctetString encryptedContent) {

        ASN1ObjectIdentifier asn1ObjectIdentifier = CMSObjectIdentifiers.data;
        // 判断是否为国密算法
        if (GMUtils.isGMAlgorithm(contentEncryptionAlgorithm)) {
            // 0010-2012_SM2密码算法加密签名消息语法规范#5_OID定义
            asn1ObjectIdentifier = new ASN1ObjectIdentifier("1.2.156.10197.6.1.4.2.1");
        }

        return new EncryptedContentInfo(
            asn1ObjectIdentifier,
            contentEncryptionAlgorithm,
            encryptedContent
        );
    }

    /**
     * 创建 {@link SignerInfo}
     *
     * @param signerName                  签名证书的颁发者 DN
     * @param signerSerialNumber          签名者序列号
     * @param digestAlgorithmIdentifier   摘要算法
     * @param digEncryptionAlgorithm      签名算法
     * @param signedAndEncryptedKeyDigest 加密用的对称密钥的摘要，签名（使用 signer 的私钥）和加密（使用对称密钥）后的值
     * @return {@link SignerInfo}
     */
    public static SignerInfo createSignerInfo(final X500Name signerName,
                                              final BigInteger signerSerialNumber,
                                              final AlgorithmIdentifier digestAlgorithmIdentifier,
                                              final AlgorithmIdentifier digEncryptionAlgorithm,
                                              final byte[] signedAndEncryptedKeyDigest) {
        return new SignerInfo(
            new ASN1Integer(1),
            new IssuerAndSerialNumber(signerName, signerSerialNumber),
            digestAlgorithmIdentifier,
            // [rfc2315#section-11] the signed-and-enveloped-data content type does not have authenticated or unauthenticated attributes
            null,
            digEncryptionAlgorithm,
            new DEROctetString(signedAndEncryptedKeyDigest),
            null
        );
    }

    /**
     * 创建 {@link SignedAndEnvelopedData}
     *
     * @param recipientInfos       {@link RecipientInfo} 数组
     * @param encryptedContentInfo {@link EncryptedContentInfo}
     * @param signerInfos          {@link SignerInfo} 数组
     * @return {@link SignedAndEnvelopedData}
     */
    public static SignedAndEnvelopedData createSignedAndEnvelopedData(final ASN1Object[] recipientInfos,
                                                                      final EncryptedContentInfo encryptedContentInfo,
                                                                      final Certificate[] certificates,
                                                                      final SignerInfo[] signerInfos) {
        // 校验入参
        // RFC2315 规定至少有一个 RecipientInfo
        if (recipientInfos == null || recipientInfos.length < 1) {
            throw BaseValidationError.INCORRECT_RECIPIENT_INFO_NUMBER.toException();
        }

        if (encryptedContentInfo == null) {
            throw BaseValidationError.ENCRYPTED_CONTENT_INFO_IS_NULL.toException();
        }

        // RFC2315 未强制规定证书数量
        ASN1Set certificateAsn1Set = null;
        if (certificates != null && certificates.length > 0) {
            List<ExtendedCertificateOrCertificate> extendedCertificateOrCertificateList = new ArrayList<>(certificates.length);
            for (Certificate certificate : certificates) {
                extendedCertificateOrCertificateList.add(new ExtendedCertificateOrCertificate(certificate));
            }
            certificateAsn1Set = new DLSet(extendedCertificateOrCertificateList.toArray(new ExtendedCertificateOrCertificate[0]));
        }

        // RFC2315 规定至少有一个 SignerInfo
        if (signerInfos == null || signerInfos.length < 1) {
            throw BaseValidationError.INCORRECT_SIGNER_INFO_NUMBER.toException();
        }

        // [rfc2315#section-9.1] The collection is intended to list the message-digest algorithms employed by all of the signers, in any order, to facilitate one-pass signature verification.
        AlgorithmIdentifier[] digestAlgorithms = Arrays.stream(signerInfos)
            .map(SignerInfo::getDigestAlgorithm)
            .distinct()
            .toArray(AlgorithmIdentifier[]::new);

        return new SignedAndEnvelopedData(
            new ASN1Integer(1),
            new DLSet(recipientInfos),
            new DLSet(digestAlgorithms),
            encryptedContentInfo,
            certificateAsn1Set,
            null,
            new DLSet(signerInfos)
        );
    }

    /**
     * 创建 {@link EnvelopedData}
     *
     * @param recipientInfos       {@link RecipientInfo} 数组
     * @param encryptedContentInfo {@link EncryptedContentInfo}
     * @return {@link EnvelopedData}
     */
    public static EnvelopedData createEnvelopedData(final RecipientInfo[] recipientInfos,
                                                    final EncryptedContentInfo encryptedContentInfo) {
        // 校验入参
        // RFC2315 规定至少有一个 RecipientInfo
        if (recipientInfos == null || recipientInfos.length < 1) {
            throw BaseValidationError.INCORRECT_RECIPIENT_INFO_NUMBER.toException();
        }

        if (encryptedContentInfo == null) {
            throw BaseValidationError.ENCRYPTED_CONTENT_INFO_IS_NULL.toException();
        }

        ASN1Sequence seq = new DERSequence(new ASN1Encodable[]{
            new ASN1Integer(0),
            new DLSet(recipientInfos),
            encryptedContentInfo});

        return EnvelopedData.getInstance(seq);
    }
}
