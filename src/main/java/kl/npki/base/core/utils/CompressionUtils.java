package kl.npki.base.core.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Objects;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * 压缩、解压工具类
 *
 * <AUTHOR>
 * @since 2025/7/10 9:50
 */
public class CompressionUtils {

    private static final Logger logger = LoggerFactory.getLogger(CompressionUtils.class);

    private CompressionUtils() {
        throw new IllegalStateException("CompressionUtils");
    }

    /**
     * 压缩字节数组
     *
     * @param data 待压缩的数据
     * @return byte[]
     */
    public static byte[] compress(byte[] data) {
        Objects.requireNonNull(data, "Input data cannot be null");
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
             GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream)) {
            gzipOutputStream.write(data);
            gzipOutputStream.finish();
            return byteArrayOutputStream.toByteArray();
        } catch (IOException e) {
            logger.error("Error compressing data", e);
        }
        return data;
    }

    /**
     * 将字节数组压缩后再转成Base64字符串
     *
     * @param data Base64字符串
     * @return String
     */
    public static String compressToBase64String(byte[] data) {
        byte[] compress = compress(data);
        return Base64.getEncoder().encodeToString(compress);
    }


    /**
     * 解压缩字节数组
     *
     * @param compressedData 压缩的字节数组
     * @return byte[]
     */
    public static byte[] decompress(byte[] compressedData) {
        Objects.requireNonNull(compressedData, "Compressed data cannot be null");
        try (ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(compressedData);
             GZIPInputStream gzipInputStream = new GZIPInputStream(byteArrayInputStream);
             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzipInputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, len);
            }
            return byteArrayOutputStream.toByteArray();
        } catch (IOException e) {
            logger.error("Error decompressing data", e);
        }
        return compressedData;
    }

    /**
     * 将Base64字符串解压成字节数组
     *
     * @param base64Str Base64字符串
     * @return byte[]
     */
    public static byte[] decompressFromBase64String(String base64Str) {
        Objects.requireNonNull(base64Str, "base64Str cannot be null");
        byte[] decodeBytes = Base64.getDecoder().decode(base64Str);
        return decompress(decodeBytes);
    }

}