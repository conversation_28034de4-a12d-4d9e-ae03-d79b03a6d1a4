package kl.npki.base.core.utils;

import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.net.SyslogAppender;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.Appender;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.log.appender.SysLogAppenderTCP;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.*;
import java.util.Iterator;

/**
 * SysLog发送工具类
 * <AUTHOR>
 * @since 2025/1/21 15:26
 */
public class SysLogSendUtil {

    /**
     * 服务检测 最大重试次数
     */
    public static final int MAX_RETRY_TIMES = 3;

    /**
     * 服务检测 判定超时时间
     */
    public static final int TIMEOUT_TIMES = 3000;

    /**
     * 服务检测 重试间隔时间
     */
    public static final int RETRY_INTERVAL_TIME = 500;

    /**
     * syslog发送协议：UDP
     */
    public static final String SYSLOG_SEND_PROTOCOL_UDP = "UDP";

    /**
     * syslog发送协议：TCP
     */
    public static final String SYSLOG_SEND_PROTOCOL_TCP = "TCP";

    private static final String APPENDER_DEFAULT_FACILITY = "LOCAL0";

    private static final String APPENDER_DEFAULT_SUFFIX = "%-4relative [%thread]  [%d{yyyy-MM-dd HH:mm:ss:SSS}] - %msg";

    private SysLogSendUtil() {
    }


    private static final Logger log = LoggerFactory.getLogger(SysLogSendUtil.class);



    public static void syslogSend(String msg) {

        // 日志外发
        log.info(msg);
    }

    /**
     * 根据指定的ip端口配置切换appender
     * @param host ip
     * @param port 端口
     * @param protocol 协议 tcp或者udp
     */
    public static void switchAppender(String host, int port, String protocol) {

        // 1.获取当前工具类logger
        ch.qos.logback.classic.Logger logger = (ch.qos.logback.classic.Logger) LoggerFactory.getLogger(SysLogSendUtil.class);

        // 2.找到当前logger下的指定类型appender
        String facility;
        String suffixPattern;
        SyslogAppender oldAppender = findExistingSyslogAppender(logger);
        // 3.旧appender不为空则复用旧appender的配置参数,为空则使用默认配置
        if (oldAppender != null) {
            facility = oldAppender.getFacility();
            suffixPattern = oldAppender.getSuffixPattern();
        } else {
            facility = APPENDER_DEFAULT_FACILITY;
            suffixPattern = APPENDER_DEFAULT_SUFFIX;
        }

        // 4.使用传入的ip+端口+协议新建appender
        SyslogAppender newAppender = getAppender(host, port, protocol);
        newAppender.setFacility(facility);
        newAppender.setSuffixPattern(suffixPattern);

        // 5.获取一个LoggerContext对象与新appender关联
        LoggerContext lc = (LoggerContext) LoggerFactory.getILoggerFactory();

        newAppender.setContext(lc);
        newAppender.start();


        // 6.先添加新 Appender，再移除旧 Appender
        logger.addAppender(newAppender);
        if (oldAppender != null) {
            oldAppender.stop();
            logger.detachAppender(oldAppender);
        }
    }


    /**
     * 获取当前在用的appender
     * @param logger logger
     * @return 当前appender
     */
    public static SyslogAppender findExistingSyslogAppender(ch.qos.logback.classic.Logger logger) {

        Iterator<Appender<ILoggingEvent>> iterator = logger.iteratorForAppenders();
        while (iterator.hasNext()) {
            Appender<ILoggingEvent> appender = iterator.next();
            if (appender instanceof SyslogAppender) {
                return (SyslogAppender) appender;
            }
        }
        return null;
    }

    /**
     * 根据ip+端口+协议新建appender
     * @param host ip
     * @param port 端口
     * @param protocol 协议
     * @return 新建appender
     * @throws Exception
     */
    private static SyslogAppender getAppender(String host, int port, String protocol) {
        // 统一转大写，后边字符判断
        if (StringUtils.isNotBlank(protocol)) {
            protocol = protocol.toUpperCase();
        }
        SyslogAppender syslogAppender;
        switch (protocol) {
            case SYSLOG_SEND_PROTOCOL_UDP:
                syslogAppender = new SyslogAppender();
                syslogAppender.setSyslogHost(host);
                syslogAppender.setPort(port);
                break;
            case SYSLOG_SEND_PROTOCOL_TCP:
                // 提前检查ip端口是否可用
                judgeServerReachable(host, port);
                syslogAppender = new SysLogAppenderTCP(host, port, 2, 2, 100);
                break;
            default:
                throw BaseInternalError.AUDIT_SWITCH_UNKNOWN_PROTOCOL_ERROR.toException();

        }

        return syslogAppender;
    }

    /**
     * 目标服务器可用性检测
     * 不过由于UDP是无响应的　UDP的检测无用
     * @param host　host
     * @param port　port
     */
    public static void judgeServerReachable(String host, int port) {
        boolean serverReachable = NetworkValidationUtils.ipDetection(host, port, TIMEOUT_TIMES);
        // 服务不可用时提示异常信息
        if (!serverReachable) {
            throw BaseInternalError.AUDIT_CONNECTION_FAILED.toException();
        }
    }


    /**
     * 检测 UDP 端口是否可发送数据（无响应验证）
     * @param host    目标IP/域名
     * @param port    目标端口
     * @param timeout 超时时间（毫秒）
     * @return true=数据包成功发送，false=发送失败
     */
    public static boolean isUdpPortReachable(String host, int port, int timeout) {
        try (DatagramSocket socket = new DatagramSocket()) {
            socket.setSoTimeout(timeout);
            InetAddress address = InetAddress.getByName(host);

            // 发送测试数据
            byte[] sendData = "PING".getBytes();
            DatagramPacket sendPacket = new DatagramPacket(sendData, sendData.length, address, port);
            socket.send(sendPacket);

            return true; // 仅表示发送成功，不保证服务接收
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 检测 TCP 端口是否可达
     * @param ip      目标IP
     * @param port    目标端口
     * @param timeout 超时时间（毫秒）
     * @return true=可达，false=不可达
     */
    public static boolean isTcpPortReachable(String ip, int port, int timeout) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(ip, port), timeout);
            return true;
        } catch (IOException e) {
            return false;
        }
    }

}

