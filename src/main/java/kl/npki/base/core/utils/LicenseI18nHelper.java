package kl.npki.base.core.utils;

import kl.security.license.core.LicenseStatus;

import static kl.npki.base.core.constant.I18nConstant.*;

/**
 *  License国际化帮助类
 *
 * <AUTHOR> Shi
 * @date 2025/4/14
 */
public class LicenseI18nHelper {

    private LicenseI18nHelper() {
    }

    /**
     * 根据License状态获取国际化描述
     */
    public static String getLicenseStatusI18nMsg(LicenseStatus licenseStatus) {
        switch (licenseStatus) {
            case FINE:
                return getBaseCoreI18nMessage(LICENSE_STATUS_VALID_I18N_KEY);
            case WRONGSERIAL:
                return getBaseCoreI18nMessage(LICENSE_STATUS_WRONG_SERIAL_I18N_KEY);
            case EXPIRE:
                return getBaseCoreI18nMessage(LICENSE_STATUS_EXPIRED_I18N_KEY);
            case WRONGFORMAT:
                return getBaseCoreI18nMessage(LICENSE_STATUS_WRONG_FORMAT_I18N_KEY);
            case WRONGSIGN:
                return getBaseCoreI18nMessage(LICENSE_STATUS_WRONG_SIGN_I18N_KEY);
            case LACK:
                return getBaseCoreI18nMessage(LICENSE_STATUS_NOT_IMPORTED_I18N_KEY);
            default:
                return getBaseCoreI18nMessage(LICENSE_STATUS_OTHER_I18N_KEY);
        }
    }

}
