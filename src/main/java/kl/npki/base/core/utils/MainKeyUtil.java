package kl.npki.base.core.utils;

import kl.nbase.emengine.entity.EngineIndexPrivateKey;
import kl.nbase.emengine.entity.param.CommonAsymEncParam;
import kl.nbase.emengine.exception.EmInternalError;
import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.emengine.service.engine.AbstractEnhancedCompatibleEngine;
import kl.nbase.emengine.utils.EMUtils;
import kl.nbase.security.constants.BlockEnum;
import kl.nbase.security.constants.PaddingEnum;
import kl.nbase.security.constants.SymAlgoEnum;
import kl.nbase.security.crypto.provider.symmetric.BlockSymmetricAlgorithmProvider;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.entity.algo.BlockSymAlgo;
import kl.nbase.security.entity.data.IOriginalData;
import kl.nbase.security.entity.data.impl.EncryptedData;
import kl.nbase.security.entity.data.impl.OriginalData;
import kl.nbase.security.entity.key.asym.keypair.impl.SecurityKeyPair;
import kl.nbase.security.entity.param.crypto.random.impl.RandomCryptoParam;
import kl.nbase.security.entity.param.keymgr.gen.keypair.impl.GenSecurityKeyPairPairParam;
import kl.nbase.security.util.encoders.Base64;
import kl.nbase.security.utils.AsymKeyUtil;
import kl.nbase.security.utils.BlobUtil;
import kl.npki.base.core.biz.mainkey.model.EmMainSecretKey;
import kl.npki.base.core.exception.BaseInternalError;
import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import java.nio.ByteBuffer;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Arrays;

/**
 * 主密钥工具类，用于替代 {@link AbstractEnhancedCompatibleEngine} 中的相应方法
 *
 * <AUTHOR> Yang
 */
public final class MainKeyUtil {

    private static final Logger log = LoggerFactory.getLogger(MainKeyUtil.class);

    /**
     * 主密钥长度
     */
    private static final int MAIN_KEY_LENGTH = 16;

    private MainKeyUtil() {
    }

    /**
     * 导出主密钥
     * <p>
     * 遍历指定算法，获取内部密钥对，并使用内部私钥对主密钥进行加密
     *
     * @param clusterEngine 加密机引擎
     * @return
     */
    public static EmMainSecretKey exportMainKey(ClusterEngine clusterEngine) {
        byte[] mainKeyValue = clusterEngine.generateRandom(new RandomCryptoParam(MAIN_KEY_LENGTH)).getData();
        /*
        1）RSA -》签名
        2）SM2-》加密
        其他不支持
         */
        KeyPair keyPair = null;
        AsymAlgo[] algoPriorityList = {AsymAlgo.SM2, AsymAlgo.RSA_2048, AsymAlgo.RSA_1024, AsymAlgo.RSA_4096};

        for (AsymAlgo asymAlgo : algoPriorityList) {
            try {
                // RSA用于签名
                boolean isSignOnly = asymAlgo.isRSA();
                // SM2用于加密
                boolean isEncOnly = asymAlgo.isSM2();
                if (!isSignOnly && !isEncOnly) {
                    // 忽略不支持的算法
                    continue;
                }
                KeyPair[] keyPairs = getAllKeyPair(clusterEngine, asymAlgo, isSignOnly);
                if (keyPairs != null && keyPairs.length > 0) {
                    keyPair = keyPairs[RandomUtils.secure().randomInt(0, keyPairs.length)];
                    break;
                }
            } catch (Exception e) {
                throw BaseInternalError.MAINKEY_EXPORT_ERROR.toException("Failed to get key pair for algo: " + asymAlgo, e);
            }
        }
        // 如果找不到密钥对，抛出异常
        if (keyPair == null) {
            throw BaseInternalError.MAINKEY_EXPORT_ERROR.toException("Cannot find a suitable key pair for encrypting the master key");
        }
        PublicKey publicKey = keyPair.getPublic();
        EngineIndexPrivateKey privateKey = (EngineIndexPrivateKey) keyPair.getPrivate();
        String keyId = EMUtils.makeKeyId(publicKey).toLowerCase();
        byte[] encryptedMainKey = clusterEngine.pubEnc(publicKey, mainKeyValue);
        String refValue = buildRefValue(clusterEngine, mainKeyValue, privateKey.getAlgo());
        return new EmMainSecretKey(Base64.toBase64String(encryptedMainKey), keyId, refValue);
    }

    /**
     * 导入主密钥
     *
     * @param clusterEngine 加密机引擎
     * @param mainKeyValue  主密钥密文
     * @param refValue      ref 值
     * @param keyId         密钥ID
     * @return 解密后的主密钥
     */
    public static byte[] importMainKey(ClusterEngine clusterEngine, String mainKeyValue, String refValue, String keyId) {
        byte[] plainMainKey = null;
        byte[] encryptKeyValue = Base64.decode(mainKeyValue.getBytes());

        // 尝试使用内部私钥解密
        try {
            KeyPair keyPair = clusterEngine.getKeyPair(keyId);
            IOriginalData originalData = clusterEngine.asymDecrypt(keyPair.getPrivate(), EncryptedData.wrap(encryptKeyValue),
                CommonAsymEncParam.getInstance());
            plainMainKey = originalData.getOriData();
        } catch (Exception e) {
            log.warn("failed to import main key with internal private key", e);
        }

        // 私钥解密失败，尝试使用 refValue 解密
        if (plainMainKey == null) {
            plainMainKey = resolveRefValue(clusterEngine, refValue);
        }

        return plainMainKey;
    }

    /**
     * 获取指定算法和用途的所有内部密钥对
     *
     * @param clusterEngine 加密机引擎
     * @param asymAlgo      非对称算法
     * @param isSign        是否为签名密钥，true 为签名密钥，false 为加密密钥
     * @return 密钥对列表
     */
    private static KeyPair[] getAllKeyPair(ClusterEngine clusterEngine, AsymAlgo asymAlgo, boolean isSign) {
        try {
            if (isSign) {
                return clusterEngine.getAllSignKeyPair(asymAlgo);
            } else {
                return clusterEngine.getAllEncKeyPair(asymAlgo);
            }
        } catch (Exception e) {
            // 接口在未找到密钥对时会抛出异常，此处忽略异常，返回空列表
            return new KeyPair[0];
        }
    }

    /**
     * 构建 ref 值
     *
     * @param clusterEngine 加密机引擎
     * @param mainKeyValue  主密钥值
     * @param asymAlgo      非对称算法
     * @return ref 值
     */
    private static String buildRefValue(ClusterEngine clusterEngine, byte[] mainKeyValue, AsymAlgo asymAlgo) {
        // 生成随机字节数组作为对称密钥
        byte[] symKey = clusterEngine.generateRandom(new RandomCryptoParam(MAIN_KEY_LENGTH)).getData();
        // 生成随机密钥对
        SecurityKeyPair keyPair = clusterEngine.generateRawAsymKey(new GenSecurityKeyPairPairParam(asymAlgo));
        // 对主密钥进行公钥加密
        byte[] encMainKeyValue = clusterEngine.asymEncrypt(keyPair.getPublic(), OriginalData.wrap(mainKeyValue),
            CommonAsymEncParam.getInstance()).getEncData();

        BlockSymAlgo blockSymAlgo = new BlockSymAlgo(SymAlgoEnum.AES_128.getDesc(),
            BlockEnum.ECB.getDesc(),
            PaddingEnum.PKCS5_PADDING.getDesc());

        // 对称加密相应的私钥（软实现）
        byte[] encPrivateKeyValue;
        try {
            encPrivateKeyValue = BlockSymmetricAlgorithmProvider.cipher(Cipher.ENCRYPT_MODE, blockSymAlgo, symKey, null,
                keyPair.getPrivate().getValue().getEncoded());
        } catch (Exception e) {
            throw EmInternalError.SYMMETRIC_ENCRYPT_ERROR.toException(e);
        }
        int encLength = encMainKeyValue.length;
        byte[] lengthValue = BlobUtil.intToByteArray(encLength);
        // 将主密钥密文长度、公钥加密后的主密钥密文、对称密钥加密后的私钥密文、裸的对称密钥原文，四者拼接
        ByteBuffer byteBuffer = ByteBuffer.allocate(4 + encMainKeyValue.length + encPrivateKeyValue.length + symKey.length)
            .put(lengthValue)
            .put(encMainKeyValue)
            .put(encPrivateKeyValue)
            .put(symKey);
        return Base64.toBase64String(byteBuffer.array());
    }

    /**
     * 解析 ref 值
     *
     * @param clusterEngine  加密机引擎
     * @param base64RefValue base64 编码的 ref 值
     * @return 解密后的主密钥
     */
    private static byte[] resolveRefValue(ClusterEngine clusterEngine, String base64RefValue) {
        // 解析 RefValue，获取公钥加密后的主密钥密文、对称密钥加密后的私钥密文、裸的对称密钥原文
        byte[] refValue = Base64.decode(base64RefValue);
        byte[] encLengthBytes = new byte[4];
        System.arraycopy(refValue, 0, encLengthBytes, 0, encLengthBytes.length);
        int encLength = BlobUtil.byteArrayToInt(encLengthBytes);
        byte[] encMainKeyValue = new byte[encLength];
        byte[] symKey = new byte[MAIN_KEY_LENGTH];
        byte[] encPrivateKeyValue = new byte[refValue.length - MAIN_KEY_LENGTH - 4 - encLength];
        System.arraycopy(refValue, 4, encMainKeyValue, 0, encLength);
        System.arraycopy(refValue, 4 + encLength, encPrivateKeyValue, 0, encPrivateKeyValue.length);
        System.arraycopy(refValue, 4 + encLength + encPrivateKeyValue.length, symKey, 0, symKey.length);
        BlockSymAlgo blockSymAlgo = new BlockSymAlgo(SymAlgoEnum.AES_128.getDesc(),
            BlockEnum.ECB.getDesc(),
            PaddingEnum.PKCS5_PADDING.getDesc());
        // 对称解密获取私钥原文（软实现）
        byte[] privateKeyValue;
        try {
            privateKeyValue = BlockSymmetricAlgorithmProvider.cipher(Cipher.DECRYPT_MODE, blockSymAlgo, symKey, null,
                encPrivateKeyValue);
        } catch (Exception e) {
            throw EmInternalError.SYMMETRIC_DECRYPT_ERROR.toException(e);
        }
        // 获取私钥
        PrivateKey privateKey = AsymKeyUtil.pkcs8Bytes2PrivateKey(privateKeyValue);
        // 非对称解密获取主密钥原文
        return clusterEngine.priDec(privateKey, encMainKeyValue);
    }
}
