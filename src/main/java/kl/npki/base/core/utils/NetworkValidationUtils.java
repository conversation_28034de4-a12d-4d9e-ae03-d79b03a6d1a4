package kl.npki.base.core.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;

/**
 * 网络连通性验证工具
 *
 * <AUTHOR>
 */
public class NetworkValidationUtils {

    private static final Logger logger = LoggerFactory.getLogger(NetworkValidationUtils.class);

    /**
     * 与目标地址的连通性检验
     *
     * @param ipAddress 目标地址（示例： 127.0.0.1）
     * @param timeout   检测超时时间(单位毫秒)
     * @return
     */
    public static boolean ipDetection(String ipAddress, Integer timeout) {
        boolean status = false;
        try {
            status = InetAddress.getByName(ipAddress).isReachable(timeout);
        } catch (Exception e) {
            logger.error("IP connectivity detection failed, ip:{}", ipAddress, e);
        }
        return status;
    }

    /**
     * 与目标地址，端口的连通性校验
     *
     * @param ipAddress 目标地址 （示例： 127.0.0.1）
     * @param port 目标端口
     * @param timeout 检测超时时间(单位毫秒)
     * @return
     */
    public static boolean ipDetection(String ipAddress, Integer port, Integer timeout) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(InetAddress.getByName(ipAddress), port), timeout);
        } catch (IOException e) {
            return false;
        }
        return true;
    }
}
