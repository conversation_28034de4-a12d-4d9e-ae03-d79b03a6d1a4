package kl.npki.base.core.utils;

import kl.npki.base.core.annotation.DataFullField;
import kl.npki.base.core.biz.dataprotect.service.IDataProtectService;
import kl.npki.base.core.exception.BaseInternalError;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据完整性保护处理通用帮助类
 *
 * <AUTHOR>
 */
public class DataProtectUtil {

    private static final Logger log = LoggerFactory.getLogger(DataProtectUtil.class);

    /**
     * 线程安全的日期时间格式器
     * 由于数据库存时间会有四舍五入的情况，故计算原文时 时间只精确到分
     * 例如: 15:00:00.999 存到数据库会变成  15:00:01,造成原文不一致
     */
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    /**
     * 生成完整性计算的值
     *
     * @param dataProcess
     * @param entity
     * @return
     * @throws Exception
     */
    public static String genDataHashValue(IDataProtectService dataProcess, Object entity) throws Exception {
        String oriDataValue = genDataValue(entity);
        byte[] generateData = dataProcess.generateData(oriDataValue.getBytes());
        return Base64Util.base64Encode(generateData);
    }

    /**
     * 验证数据的完整性
     *
     * @param dataProcess
     * @param entity      实体类，该实体类一般为DO类 （通过DO类中的 @DataFullField注解 生成原文）
     * @param b64FullData 完整性Hash值
     * @return
     * @throws Exception
     */
    public static boolean verifyData(IDataProtectService dataProcess, Object entity, String b64FullData) throws Exception {
        String oriDataValue = genDataValue(entity);
        return verifyData(dataProcess, oriDataValue, b64FullData);
    }

    /**
     * 验证数据的完整性
     *
     * @param dataProcess
     * @param oriData     完整性原文值
     * @param b64FullData 完整性Hash值
     * @return
     * @throws Exception
     */
    public static boolean verifyData(IDataProtectService dataProcess, String oriData, String b64FullData) throws Exception {
        return dataProcess.verifyData(oriData.getBytes(), Base64Util.base64Decode(b64FullData));
    }

    /**
     * 生成完整性原文值
     *
     * @param obj 实体类，该实体类一般为DO类 （通过DO类中的 @DataFullField注解 生成原文）
     * @return
     */
    public static String genDataValue(Object obj) {
        StringBuilder oriDataFull = new StringBuilder();
        Class c = obj.getClass();
        List<String> emptyFields = new ArrayList<>();

        Field[] allFields = FieldUtils.getAllFields(c);
        for (Field field : allFields) {
            try {
                Annotation annotation = field.getAnnotation(DataFullField.class);
                if (annotation == null) {
                    continue;
                }

                field.setAccessible(true);
                Object value = field.get(obj);

                if (value == null) {
                    emptyFields.add(field.getName());
                    continue;
                }
                // 开始拼接原文值
                if (value instanceof LocalDateTime) {
                    oriDataFull.append(parseTimeToString((LocalDateTime) value));
                } else {
                    oriDataFull.append(value);
                }

            } catch (IllegalAccessException e) {
                throw BaseInternalError.GEN_FULL_DATA_ORI_DATA_ERROR.toException(e);
            }
        }

        if (!emptyFields.isEmpty()) {
            String warningMessage = String.format(
                "In class %s, the following fields are marked with the @ DataMullField annotation but have empty values:%s." +
                    " This may result in integrity verification failure, " +
                    "as the database may have set default values for these fields. " +
                    "Please ensure that all fields marked with @ DataMullField have non null values to avoid potential validation inconsistency risks.",
                c.getName(),
                String.join(", ", emptyFields)
            );
            if (log.isDebugEnabled()) {
                log.debug(warningMessage);
            }
        }
        return oriDataFull.toString();
    }

    private static String parseTimeToString(LocalDateTime localDateTime) {
        return DATE_FORMATTER.format(localDateTime);
    }
}

