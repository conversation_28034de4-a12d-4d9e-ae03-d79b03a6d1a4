package kl.npki.base.core.utils;

import kl.nbase.security.asn1.DERBMPString;
import kl.nbase.security.asn1.pkcs.PKCSObjectIdentifiers;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.cert.jcajce.JcaX509ExtensionUtils;
import kl.nbase.security.crypto.engines.DESedeEngine;
import kl.nbase.security.crypto.engines.RC2Engine;
import kl.nbase.security.crypto.modes.CBCBlockCipher;
import kl.nbase.security.jce.provider.BouncyCastleProvider;
import kl.nbase.security.pkcs.*;
import kl.nbase.security.pkcs.bc.BcPKCS12MacCalculatorBuilder;
import kl.nbase.security.pkcs.bc.BcPKCS12PBEOutputEncryptorBuilder;
import kl.nbase.security.pkcs.jcajce.JcaPKCS12SafeBagBuilder;
import kl.nbase.security.utils.PEMUtil;
import kl.npki.base.core.exception.BaseValidationError;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.*;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Enumeration;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.PFX_PASSWORD_IS_EMPTY_I18N_KEY;

/**
 * pfx封装工具类
 *
 * <AUTHOR>
 * @date 2023/2/10
 */
public class PfxUtils {

    private PfxUtils() {
    }

    /**
     * 封装pfx
     *
     * @param privKey 用户私钥
     * @param pubKey  用户公钥
     * @param cert    X509证书
     * @param passwd  口令
     * @return
     * @throws NoSuchAlgorithmException
     * @throws IOException
     * @throws PKCSException
     */
    public static PKCS12PfxPdu makePfx(PrivateKey privKey, PublicKey pubKey, X509Certificate cert, String passwd)
            throws NoSuchAlgorithmException, IOException, PKCSException {
        JcaX509ExtensionUtils extUtils = new JcaX509ExtensionUtils();

        PKCS12SafeBagBuilder eeCertBagBuilder = new JcaPKCS12SafeBagBuilder(cert);
        eeCertBagBuilder.addBagAttribute(PKCSObjectIdentifiers.pkcs_9_at_friendlyName,
                new DERBMPString("User Key"));
        eeCertBagBuilder.addBagAttribute(PKCSObjectIdentifiers.pkcs_9_at_localKeyId,
                extUtils.createSubjectKeyIdentifier(pubKey));

        char[] passwdChars = passwd.toCharArray();
        PKCS12SafeBagBuilder keyBagBuilder = new JcaPKCS12SafeBagBuilder(privKey,
                new BcPKCS12PBEOutputEncryptorBuilder(
                        PKCSObjectIdentifiers.pbeWithSHAAnd3_KeyTripleDES_CBC,
                        new CBCBlockCipher(new DESedeEngine())).build(passwdChars));
        keyBagBuilder.addBagAttribute(PKCSObjectIdentifiers.pkcs_9_at_friendlyName,
                new DERBMPString("User Key"));
        keyBagBuilder.addBagAttribute(PKCSObjectIdentifiers.pkcs_9_at_localKeyId,
                extUtils.createSubjectKeyIdentifier(pubKey));

        PKCS12PfxPduBuilder pfxPduBuilder = new PKCS12PfxPduBuilder();
        PKCS12SafeBag[] certs = new PKCS12SafeBag[1];
        certs[0] = eeCertBagBuilder.build();
        pfxPduBuilder.addEncryptedData(new BcPKCS12PBEOutputEncryptorBuilder(
                        PKCSObjectIdentifiers.pbeWithSHAAnd40BitRC2_CBC,
                        new CBCBlockCipher(new RC2Engine())).build(passwdChars),
                certs);
        pfxPduBuilder.addData(keyBagBuilder.build());
        return pfxPduBuilder.build(new BcPKCS12MacCalculatorBuilder(), passwdChars);
    }

    /**
     * 将bc下面的证书转为java包下面的 X509Certificate
     *
     * @param certificate
     * @return
     * @throws IOException
     * @throws CertificateException
     * @throws NoSuchProviderException
     */
    public static X509Certificate bcCertToX509Cert(Certificate certificate) throws IOException, CertificateException, NoSuchProviderException {
        ByteArrayInputStream bais = new ByteArrayInputStream(certificate.getEncoded());
        CertificateFactory cf = CertificateFactory.getInstance("X.509", BouncyCastleProvider.PROVIDER_NAME);
        return (X509Certificate) cf.generateCertificate(bais);
    }

    /**
     * 解析 pfx 证书
     *
     * @param pfxCertBase64 pfx 证书（base64）
     * @param password      密码
     * @return 证书
     */
    public static Certificate parsePfxCert(String pfxCertBase64, String password) {
        if (StringUtils.isBlank(password)) {
            throw BaseValidationError.CERT_PARSE_FAILED.toException(PFX_PASSWORD_IS_EMPTY_I18N_KEY);
        }
        try (ByteArrayInputStream input = new ByteArrayInputStream(Base64.decodeBase64(PEMUtil.unFormatCert(pfxCertBase64)))) {
            KeyStore keyStore = KeyStore.getInstance("PKCS12", new BouncyCastleProvider());
            keyStore.load(input, password.toCharArray());
            String alias = keyStore.aliases().nextElement();
            return Certificate.getInstance(keyStore.getCertificate(alias).getEncoded());
        } catch (Exception e) {
            throw BaseValidationError.CERT_PARSE_FAILED.toException(e);
        }
    }

    /**
     * 解析PFX证书中的私钥， 默认读取第一个私钥
     *
     * @param pfxCertBase64 pfx 证书（base64）
     * @param password      密码
     * @return 私钥
     */
    public static PrivateKey parsePfxPrivateKey(String pfxCertBase64, String password) {
        return parsePfxPrivateKey(pfxCertBase64, password, null);
    }

    /**
     * 根据别名获取PFX证书中的私钥
     *
     * @param pfxCertBase64
     * @param password
     * @param toSearchKeyAlias
     * @return
     */
    public static PrivateKey parsePfxPrivateKey(String pfxCertBase64, String password, String toSearchKeyAlias) {
        if (StringUtils.isBlank(password)) {
            throw BaseValidationError.CERT_PARSE_FAILED.toException(PFX_PASSWORD_IS_EMPTY_I18N_KEY);
        }
        try (ByteArrayInputStream input = new ByteArrayInputStream(Base64.decodeBase64(PEMUtil.unFormatCert(pfxCertBase64)))) {
            KeyStore keyStore = KeyStore.getInstance("PKCS12", new BouncyCastleProvider());
            keyStore.load(input, password.toCharArray());

            // 获取 KeyStore 中所有的别名
            Enumeration<String> aliases = keyStore.aliases();
            while (aliases.hasMoreElements()) {
                String alias = aliases.nextElement();
                if (StringUtils.isBlank(toSearchKeyAlias) || toSearchKeyAlias.equals(alias)) {
                    // 尝试根据别名获取对应的密钥
                    Key key = keyStore.getKey(alias, password.toCharArray());
                    if (key instanceof PrivateKey) {
                        return (PrivateKey) key;
                    }
                }
            }
            return null;
        } catch (Exception e) {
            throw BaseValidationError.CERT_PARSE_FAILED.toException(e);
        }
    }

}
