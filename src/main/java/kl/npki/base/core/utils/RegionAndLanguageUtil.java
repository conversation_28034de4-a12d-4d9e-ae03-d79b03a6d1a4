package kl.npki.base.core.utils;


import kl.nbase.helper.utils.DateUtils;
import kl.nbase.i18n.locale.LocaleContext;
import kl.npki.base.core.common.date.CustomLocalDateFormatRegistry;
import kl.npki.base.core.configs.DateFormatConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.RegionAndLanguageConstant;
import kl.npki.base.core.tenantholders.ConfigHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Locale;
import java.util.Objects;
import java.util.TimeZone;

public class RegionAndLanguageUtil {
    private static final Logger logger = LoggerFactory.getLogger(RegionAndLanguageUtil.class);

    public static void setTimeZone() {
        String region = BaseConfigWrapper.getRegionalLanguageConfig().getRegion();
        String language = BaseConfigWrapper.getRegionalLanguageConfig().getLanguage();
        // 语言若是为en 则Locale设置为en_US
        if (Locale.US.getLanguage().equalsIgnoreCase(language)) {
            LocaleContext.setDefaultLocal(Locale.US);
            LocaleContext.set(Locale.US);
        } else if (Locale.SIMPLIFIED_CHINESE.getLanguage().equalsIgnoreCase(language)) {
            LocaleContext.setDefaultLocal(Locale.SIMPLIFIED_CHINESE);
            LocaleContext.set(Locale.SIMPLIFIED_CHINESE);
        } else {
            Locale locale = new Locale(language, region);
            LocaleContext.setDefaultLocal(locale);
            LocaleContext.set(locale);
        }

        RegionAndLanguageConstant regionAndLanguageConstant = RegionAndLanguageConstant.valueOf(region.toUpperCase());
        String timeZone = regionAndLanguageConstant.getTimeZone();
        logger.info("time zone of the system is : {}", timeZone);
        TimeZone.setDefault(TimeZone.getTimeZone(timeZone));
        DateFormatConfig dateFormatConfig = BaseConfigWrapper.getDateFormatConfig();
        if (!StringUtils.equals(dateFormatConfig.getDateTimePattern(), regionAndLanguageConstant.getDateFormat())) {
            // 增加判断，避免数据库主从切换启动时无法写入导致启动失败，最好是启动时不要写入或调整优先级
            // 通常不是人为修改的配置文件此处无需在启动时修改
            dateFormatConfig.setDateTimePattern(regionAndLanguageConstant.getDateFormat());
            ConfigHolder.get().save(dateFormatConfig);
        }
    }

    /**
     * 根据区域配置获取对应时区信息
     */
    public static String getGMTByRegionConfig() {
        String region = BaseConfigWrapper.getRegionalLanguageConfig().getRegion();
        RegionAndLanguageConstant regionAndLanguageConstant = RegionAndLanguageConstant.valueOf(region.toUpperCase());
        return regionAndLanguageConstant.getGmt();
    }

    public static void setDateFormat() {
        DateFormatConfig dateFormatConfig = BaseConfigWrapper.getDateFormatConfig();
        if (Objects.isNull(dateFormatConfig)) {
            return;
        }
        String dayPattern = dateFormatConfig.getDayPattern();
        if (StringUtils.isNotBlank(dayPattern)) {
            DateUtil.dayPattern = dayPattern;
            CustomLocalDateFormatRegistry.registerFormat(DateUtils.DAY_PATTERN, DateUtil.dayPattern);
        }
        String dateTimePattern = dateFormatConfig.getDateTimePattern();
        if (StringUtils.isNotBlank(dateTimePattern)) {
            DateUtil.dateTimePattern = dateTimePattern;
            CustomLocalDateFormatRegistry.registerFormat(DateUtils.DATETIME_PATTERN, DateUtil.dateTimePattern);
        }
        String dateTimeMsPattern = dateFormatConfig.getDateTimeMsPattern();
        if (StringUtils.isNotBlank(dateTimeMsPattern)) {
            DateUtil.dateTimeMsPattern = dateTimeMsPattern;
            CustomLocalDateFormatRegistry.registerFormat(DateUtils.DATETIME_MS_PATTERN, DateUtil.dateTimeMsPattern);
        }
        String fileDateTimePattern = dateFormatConfig.getFileDateTimePattern();
        if (StringUtils.isNotBlank(fileDateTimePattern)) {
            DateUtil.fileDateTimePattern = fileDateTimePattern;
        }
        logger.info("Initialize the dateFormat: dayPattern->[{}] , dateTimePattern->[{}], dateTimeMsPattern->[{}], " +
                "fileDateTimePattern->[{}]", dayPattern, dateTimePattern, dateTimeMsPattern, fileDateTimePattern);
    }

}
