package kl.npki.base.core.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.*;
import java.util.Enumeration;
import java.util.regex.Pattern;

/**
 * 网络工具类，用于获取本地网络信息和进行网络操作。
 *
 * <AUTHOR>
 * @date 2023/7/4
 */
public class NetUtils {

    private NetUtils() {
        throw new IllegalStateException("NetUtils Utility class");
    }

    private static final Logger logger = LoggerFactory.getLogger(NetUtils.class);
    private static final Pattern IP_PATTERN = Pattern.compile("\\d{1,3}(\\.\\d{1,3}){3,5}$");
    private static final String ANY_HOST_VALUE = "0.0.0.0";
    private static final String LOCALHOST_VALUE = "127.0.0.1";
    private static InetAddress localAddress = null;

    /**
     * 检查是否为有效的IPv4地址。
     *
     * @param address 要检查的InetAddress对象。
     * @return 如果是有效的IPv4地址，则返回true；否则返回false。
     */
    static boolean isValidV4Address(InetAddress address) {
        if (address == null || address.isLoopbackAddress()) {
            return false;
        }
        String name = address.getHostAddress();
        return (name != null
            && IP_PATTERN.matcher(name).matches()
            && !ANY_HOST_VALUE.equals(name)
            && !LOCALHOST_VALUE.equals(name));
    }

    /**
     * 检查是否优先使用IPv6地址。
     *
     * @return 如果优先使用IPv6地址，则返回true；否则返回false。
     */
    static boolean isPreferIpV6Address() {
        return Boolean.getBoolean("java.net.preferIPv6Addresses");
    }

    /**
     * 规范化IPv6地址，将范围名称转换为范围ID。
     *
     * @param address 输入地址。
     * @return 规范化后的地址，包含转换为整数的范围ID。
     */
    static InetAddress normalizeV6Address(Inet6Address address) {
        String hostAddress = address.getHostAddress();
        int i = hostAddress.lastIndexOf('%');
        if (i > 0) {
            try {
                return InetAddress.getByName(hostAddress.substring(0, i) + '%' + address.getScopeId());
            } catch (UnknownHostException e) {
                // 忽略异常
                logger.debug("Unknown IPv6 address:", e);
            }
        }
        return address;
    }

    /**
     * 获取本地第一个有效的IP地址。
     *
     * @return 本地第一个有效的IP地址。
     */
    public static InetAddress getLocalAddress() {
        if (localAddress != null) {
            return localAddress;
        }
        InetAddress localAddress = getLocalAddress0();
        NetUtils.localAddress = localAddress;
        return localAddress;
    }

    /**
     * 将地址转换为有效的地址。
     *
     * @param address 输入地址。
     * @return 如果地址有效，则返回该地址；否则返回null。
     */
    private static InetAddress toValidAddress(InetAddress address) {
        if (address instanceof Inet6Address) {
            Inet6Address v6Address = (Inet6Address) address;
            if (isPreferIpV6Address()) {
                return normalizeV6Address(v6Address);
            }
        }
        if (isValidV4Address(address)) {
            return address;
        }
        return null;
    }

    /**
     * 获取本地地址的实现方法。
     *
     * @return 本地地址。
     */
    private static InetAddress getLocalAddress0() {
        InetAddress localAddress = null;

        try {
            localAddress = InetAddress.getLocalHost();
            InetAddress addressOp = toValidAddress(localAddress);
            if (addressOp != null) {
                return addressOp;
            }
        } catch (Exception e) {
            logger.warn("failed to obtain ip address", e);
        }

        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            if (interfaces != null) {
                while (interfaces.hasMoreElements()) {
                    NetworkInterface network = interfaces.nextElement();
                    if (shouldSkipNetworkInterface(network)) {
                        continue;
                    }
                    InetAddress addressOp = findValidAddressInNetworkInterface(network);
                    if (addressOp != null) {
                        return addressOp;
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("failed to obtain ip address", e);
        }

        return localAddress;
    }

    /**
     * 判断是否应跳过指定的网络接口。
     *
     * @param network 网络接口。
     * @return 如果应跳过该网络接口，则返回true；否则返回false。
     */
    private static boolean shouldSkipNetworkInterface(NetworkInterface network) throws SocketException {
        return network.isLoopback() || network.isVirtual() || !network.isUp();
    }

    /**
     * 在指定的网络接口中查找第一个有效的地址。
     *
     * @param network 网络接口。
     * @return 第一个有效的地址；如果未找到有效地址，则返回null。
     */
    private static InetAddress findValidAddressInNetworkInterface(NetworkInterface network) throws SocketException {
        Enumeration<InetAddress> addresses = network.getInetAddresses();
        while (addresses.hasMoreElements()) {
            InetAddress addressOp = toValidAddress(addresses.nextElement());
            if (addressOp != null && isAddressReachable(addressOp)) {
                return addressOp;
            }
        }
        return null;
    }

    /**
     * 检查地址是否可达。
     *
     * @param address 要检查的地址。
     * @return 如果地址可达，则返回true；否则返回false。
     */
    private static boolean isAddressReachable(InetAddress address) {
        try {
            return address.isReachable(100);
        } catch (IOException e) {
            return false;
        }
    }

}


