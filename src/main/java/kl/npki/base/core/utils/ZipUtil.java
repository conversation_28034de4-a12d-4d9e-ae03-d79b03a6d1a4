package kl.npki.base.core.utils;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.ArchiveInputStream;
import org.apache.commons.compress.archivers.ArchiveOutputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.CipherOutputStream;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.PBEParameterSpec;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.security.Key;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 */
public class ZipUtil {

    private static final String ALGORITHM = "PBEWithHmacSHA512AndAES_256";
    /**
     * 迭代次数
     */
    private static final int ITERATION_COUNT = 10000;

    /**
     * 多个文件打包成zip
     *
     * @param fileNameToContentMap 该Map的key是文件名，value是文件内容
     */
    public static byte[] compress(Map<String, byte[]> fileNameToContentMap) throws IOException {
        // 使用ZipOutputStream将证书打包
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ZipOutputStream zipOut = new ZipOutputStream(baos, StandardCharsets.UTF_8)) {
            for (Map.Entry<String, byte[]> fileNameToContent : fileNameToContentMap.entrySet()) {
                zipOut.putNextEntry(new ZipEntry(fileNameToContent.getKey()));
                zipOut.write(fileNameToContent.getValue());
                zipOut.closeEntry();
            }
            // 完成zip文件的创建
            zipOut.finish();
            return baos.toByteArray();
        }
    }

    /**
     * 将文件压缩成zip
     *
     * @param sourceFiles  待压缩的文件列表 (集合中的元素可以是文件夹，也可以是文件)
     * @param targetFile   输出的压缩文件路径
     * @param password     压缩密码，为空时不进行加密压缩
     * @param isNeedFolder 是否需要打入目录，如果原文件是文件夹，
     *                     为true时代表将该文件夹也压缩进结果文件，如果为false，代表只将该文件夹下的文件或目录压缩进结果文件
     */
    public static void compress(List<String> sourceFiles, String targetFile, String password, boolean isNeedFolder) throws Exception {
        // 加密输出流
        CipherOutputStream cos = null;
        // 文件输出流
        OutputStream fos = null;
        InputStream is = null;
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
             ArchiveOutputStream aos = new ZipArchiveOutputStream(bos)) {

            for (String sourceFile : sourceFiles) {
                addEntry(sourceFile, aos, isNeedFolder);
            }
            // aos一定要在此处主动关闭，否则会导致部分缓存数据没写入到bos中，导致后续加密或写入文件的内容不完整
            aos.close();

            // 创建文件输出流，最终压缩文件写入此流中(fos必须在cos关闭后才允许关闭，故禁止使用try-with-resource方式，手动控制关闭顺序)
            fos = new FileOutputStream(targetFile);
            is = new ByteArrayInputStream(bos.toByteArray());
            byte[] buffer = new byte[1024];

            if (StringUtils.isNotBlank(password)) {
                // 加密
                Cipher cipher = getCipher(Cipher.ENCRYPT_MODE, password);
                cos = new CipherOutputStream(fos, cipher);
                int r;
                while ((r = is.read(buffer)) >= 0) {
                    cos.write(buffer, 0, r);
                }
            } else {
                int r;
                while ((r = is.read(buffer)) >= 0) {
                    fos.write(buffer, 0, r);
                }
            }
        } finally {
            if (cos != null) {
                cos.close();
            }
            if (is != null) {
                is.close();
            }
            if (fos != null) {
                fos.close();
            }
        }
    }

    private static void addEntry(String sourceFile, ArchiveOutputStream aos, boolean isNeedFolder) throws IOException {
        Path dirPath = Paths.get(sourceFile);
        Files.walkFileTree(dirPath, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) throws IOException {
                // 计算压缩文件放在zip文件中的路径
                String relativePath = dirPath.relativize(dir).toString();
                if (!isNeedFolder && StringUtils.isNotBlank(relativePath)) {
                    ArchiveEntry entry = new ZipArchiveEntry(dir.toFile(), relativePath);
                    aos.putArchiveEntry(entry);
                    aos.closeArchiveEntry();
                }
                return super.preVisitDirectory(dir, attrs);
            }

            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                // 计算压缩文件放在zip文件中的路径
                String relativePath = dirPath.relativize(file).toString();
                if (StringUtils.isBlank(relativePath)) {
                    // 为空代表 dirPath 为一个文件
                    relativePath = dirPath.getFileName().toString();
                } else if (isNeedFolder) {
                    String dirPathFileName = dirPath.getFileName().toString();
                    relativePath = dirPathFileName + File.separator + relativePath;
                }
                ArchiveEntry entry = new ZipArchiveEntry(file.toFile(), relativePath);
                aos.putArchiveEntry(entry);
                try (FileInputStream fileInputStream = new FileInputStream(file.toFile())){
                    IOUtils.copy(fileInputStream, aos);
                }
                aos.closeArchiveEntry();
                return super.visitFile(file, attrs);
            }
        });
    }

    /**
     * 将zip文件解压到指定目录
     *
     * @param zipPath 源文件，如：archive.zip
     * @param descDir 解压目录
     */
    public static void uncompress(String zipPath, String descDir, String password) throws Exception {
        try (InputStream fis = Files.newInputStream(Paths.get(zipPath))) {

            // 解密
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int r;
            if (StringUtils.isEmpty(password)) {
                while ((r = fis.read(buffer)) >= 0) {
                    bos.write(buffer, 0, r);
                }
            } else {
                Cipher cipher = getCipher(Cipher.DECRYPT_MODE, password);
                CipherInputStream cis = new CipherInputStream(fis, cipher);
                while ((r = cis.read(buffer)) >= 0) {
                    bos.write(buffer, 0, r);
                }
                cis.close();
            }

            ArchiveInputStream zis = new ZipArchiveInputStream(new ByteArrayInputStream(bos.toByteArray()));

            ArchiveEntry entry;
            while (Objects.nonNull(entry = zis.getNextEntry())) {
                if (!zis.canReadEntryData(entry)) {
                    continue;
                }
                String name = descDir + File.separator + entry.getName();
                File f = new File(name);
                if (entry.isDirectory()) {
                    if (!f.isDirectory() && !f.mkdirs()) {
                        f.mkdirs();
                    }
                } else {
                    File parent = f.getParentFile();
                    if (!parent.isDirectory() && !parent.mkdirs()) {
                        throw new IOException("failed to create directory " + parent);
                    }
                    try (OutputStream o = Files.newOutputStream(f.toPath())) {
                        IOUtils.copy(zis, o);
                    }
                }
            }
        }
    }


    private static Key toKey(String password) throws Exception {
        // 密钥材料
        PBEKeySpec keySpec = new PBEKeySpec(password.toCharArray());
        // 实例化
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
        // 生成密钥
        return keyFactory.generateSecret(keySpec);
    }

    private static Cipher getCipher(int opmode, String password) throws Exception {
        // 转换密钥
        Key key = toKey(password);
        // 根据密码计算iv,长度由AES决定
        String pwdHash = DigestUtils.digestSm3ToHexStr(password.getBytes());
        int ivLenthInBytes = 16;
        IvParameterSpec iv = new IvParameterSpec(pwdHash.substring(0, ivLenthInBytes).getBytes());
        byte[] salt = pwdHash.substring(pwdHash.length() - ivLenthInBytes, pwdHash.length()).getBytes();
        // 实例化PBE参数
        PBEParameterSpec paramSpec = new PBEParameterSpec(salt, ITERATION_COUNT, iv);
        // 实例化
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        // 初始化
        cipher.init(opmode, key, paramSpec);
        return cipher;
    }
}
