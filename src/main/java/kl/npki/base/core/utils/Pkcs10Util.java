package kl.npki.base.core.utils;

import kl.nbase.emengine.entity.param.CommonAsymSignParam;
import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.security.asn1.ASN1ObjectIdentifier;
import kl.nbase.security.asn1.ASN1Set;
import kl.nbase.security.asn1.DERBitString;
import kl.nbase.security.asn1.DERNull;
import kl.nbase.security.asn1.pkcs.CertificationRequest;
import kl.nbase.security.asn1.pkcs.CertificationRequestInfo;
import kl.nbase.security.asn1.x500.X500Name;
import kl.nbase.security.asn1.x509.AlgorithmIdentifier;
import kl.nbase.security.asn1.x509.Extensions;
import kl.nbase.security.asn1.x509.SubjectPublicKeyInfo;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.nbase.security.entity.data.impl.OriginalData;
import kl.nbase.security.pkcs.PKCS10CertificationRequest;
import kl.nbase.security.util.encoders.Base64;
import kl.nbase.security.utils.PEMUtil;
import kl.npki.base.core.constant.SignAlgoEnum;
import kl.npki.base.core.exception.BaseInternalError;

import java.io.IOException;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;

/**
 * PKCS#10证书签发请求相关工具类
 *
 * <AUTHOR>
 * @date 2024-09-04
 */
public class Pkcs10Util {

    private Pkcs10Util() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 生成PEM格式的PKCS#10证书请求
     * <p>
     * 该方法用于根据给定的引擎、主题DN、密钥对、属性和签名算法OID生成一个PKCS#10证书请求
     *
     * @param engine      指定的加密引擎，用于对P10进行签名
     * @param subjectDn   主题DN
     * @param keyPair     密钥对
     * @param signAlgoOid 签名算法OID
     * @param attributes  附加属性
     * @return PEM格式的PKCS#10证书请求
     */
    public static String generateCertSigningRequest(ClusterEngine engine, String subjectDn, KeyPair keyPair,
                                                    ASN1ObjectIdentifier signAlgoOid, ASN1Set attributes) {
        return generateCertSigningRequest(engine, subjectDn, keyPair.getPublic(), keyPair.getPrivate(), signAlgoOid, attributes);
    }

    /**
     * 生成PEM格式的PKCS#10证书请求
     * <p>
     * 该方法用于根据给定的引擎、主题DN、密钥对、属性和签名算法OID生成一个PKCS#10证书请求
     *
     * @param engine      指定的加密引擎，用于对P10进行签名
     * @param subjectDn   主题DN
     * @param publicKey   公钥，用于构造CSR
     * @param privateKey  私钥，用于签名CSR
     * @param signAlgoOid 签名算法OID
     * @param attributes  附加属性
     * @return PEM格式的PKCS#10证书请求
     */
    public static String generateCertSigningRequest(ClusterEngine engine, String subjectDn, PublicKey publicKey,
                                                    PrivateKey privateKey, ASN1ObjectIdentifier signAlgoOid,
                                                    ASN1Set attributes) {

        // 构造请求基本信息
        X500Name subject = new X500Name(subjectDn);
        SubjectPublicKeyInfo subjectPublicKeyInfo = SubjectPublicKeyInfo.getInstance(publicKey.getEncoded());
        CertificationRequestInfo certificationRequestInfo =
            new CertificationRequestInfo(subject, subjectPublicKeyInfo, attributes);

        // 签名原文
        OriginalData originalData;
        try {
            originalData = new OriginalData(certificationRequestInfo.getEncoded());
        } catch (IOException e) {
            throw BaseInternalError.GEN_PKCS10_REQUEST_ERROR.toException(e);
        }
        // 签名参数
        HashAlgo hashAlgo = SignAlgoEnum.getEnumByOid(signAlgoOid).getHashAlgo();
        CommonAsymSignParam signParam = new CommonAsymSignParam(hashAlgo);
        // 执行签名
        byte[] sign = engine.sign(privateKey, originalData, signParam).getSignedData();

        // 签名算法
        AlgorithmIdentifier algorithmIdentifier = new AlgorithmIdentifier(signAlgoOid, DERNull.INSTANCE);

        // 构造PKCS#10证书请求
        CertificationRequest pkcs10 =
            new CertificationRequest(certificationRequestInfo, algorithmIdentifier, new DERBitString(sign));

        // 返回PEM格式的证书请求
        try {
            return PEMUtil.formatRequest(Base64.toBase64String(pkcs10.getEncoded()));
        } catch (IOException e) {
            throw BaseInternalError.GEN_PKCS10_REQUEST_ERROR.toException(e);
        }
    }

    /**
     * 从 PKCS #10 认证请求中提取扩展信息
     *
     * @param certificationRequest PKCS #10 认证请求对象
     * @return 生成的 Extension 对象
     */
    public static Extensions extractExtensions(CertificationRequest certificationRequest) {

        PKCS10CertificationRequest pkcs10CertificationRequest = new PKCS10CertificationRequest(certificationRequest);
        return pkcs10CertificationRequest.getRequestedExtensions();
    }
}