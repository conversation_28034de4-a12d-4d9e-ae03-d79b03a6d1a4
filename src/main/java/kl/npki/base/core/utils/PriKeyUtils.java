package kl.npki.base.core.utils;

import kl.nbase.security.asn1.ASN1Integer;
import kl.nbase.security.asn1.DERBitString;
import kl.nbase.security.asn1.cctc.PrivateKeyInfo4TestCenter;
import kl.nbase.security.asn1.custom.pkcs.SignedAndEnvelopedData;
import kl.nbase.security.asn1.gm.GMObjectIdentifiers;
import kl.nbase.security.asn1.pkcs.PrivateKeyInfo;
import kl.nbase.security.asn1.x509.SubjectPublicKeyInfo;
import kl.nbase.security.crypto.custom.key.KlECPrivateKey;
import kl.nbase.security.crypto.custom.key.KlECPublicKey;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.pqc.custom.key.*;
import kl.nbase.security.utils.AsymKeyUtil;
import kl.nbase.security.utils.ECKeyUtil;
import kl.nbase.security.utils.PEMUtil;
import kl.npki.base.core.exception.BaseInternalError;

import java.io.IOException;
import java.math.BigInteger;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.interfaces.ECPrivateKey;
import java.security.interfaces.ECPublicKey;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.FAILED_TO_OBTAIN_PRIVATE_KEY_I18N_KEY;
import static kl.npki.base.core.constant.I18nExceptionInfoConstant.UNSUPPORTED_KEY_TYPE_TYPE_IS_I18N_KEY;

/**
 * 私钥处理工具类
 *
 * <AUTHOR> Yang
 * @date 2023-02-21 17:50
 */
public class PriKeyUtils {

    public static final int SM2_PRI_LENGTH = 32;
    public static final long VERSION = 1;

    public static final String PRIVATE_KEY_FORMAT_HEAD = "-----BEGIN PRIVATE KEY-----";
    public static final String PRIVATE_KEY_FORMAT_TAIL = "-----END PRIVATE KEY-----";

    public static final String RSA_PRIVATE_KEY_FORMAT_HEAD = "-----BEGIN RSA PRIVATE KEY-----";
    public static final String RSA_PRIVATE_KEY_FORMAT_TAIL = "-----END RSA PRIVATE KEY-----";

    public static final String EC_PRIVATE_KEY_FORMAT_HEAD = "-----BEGIN EC PRIVATE KEY-----";
    public static final String EC_PRIVATE_KEY_FORMAT_TAIL = "-----END EC PRIVATE KEY-----";

    public static final String ENCRYPTED_PRIVATE_KEY_FORMAT_HEAD = "-----BEGIN ENCRYPTED PRIVATE KEY-----";
    public static final String ENCRYPTED_PRIVATE_KEY_FORMAT_TAIL = "-----END ENCRYPTED PRIVATE KEY-----";

    private PriKeyUtils() {
    }

    /**
     * 从后往前拷贝，用于去除或添加前导 0
     *
     * @param x      原始字节数组
     * @param length 拷贝长度
     * @return 拷贝后的字节数组
     */
    public static byte[] cutBytes(byte[] x, int length) {
        byte[] result = new byte[length];
        for (int i = length - 1; i >= 0; i--) {
            if (x.length - length + i >= 0) {
                result[i] = x[x.length - length + i];
            }
        }
        return result;
    }

    /**
     * 预处理私钥，以符合 {@link SignedAndEnvelopedData} 结构的要求
     * RSA 的私钥保持带结构状态；SM2 拿裸的私钥，需要处理前导 0
     *
     * @param privateKeyBytes 私钥字节数组
     * @return 更新后的私钥字节数组
     */
    public static byte[] adaptPrivateKey(byte[] privateKeyBytes, AsymAlgo asymAlgo) {
        // SM2私钥，并且是裸的私钥结构直接返回
        if (asymAlgo.isSM2() && privateKeyBytes.length == SM2_PRI_LENGTH) {
            return privateKeyBytes;
        }
        // RSA ECC私钥直接返回，默认使用PKCS#8结构
        if (asymAlgo.isRSA() || asymAlgo.isECC()) {
            return privateKeyBytes;
        }

        PrivateKey privateKey = AsymKeyUtil.pkcs8Bytes2PrivateKey(privateKeyBytes);
        if (asymAlgo.isKyber()) {
            return ((KlKyberPrivateKey) privateKey).getX();
        } else if (asymAlgo.isCntr() || asymAlgo.isCtru()) {
            return ((KlNtruGmPrivateKey) privateKey).getSk();
        } else if (asymAlgo.isSM2()) {
            int keyLength = asymAlgo.getBitLength() / 8;
            return PriKeyUtils.cutBytes(((ECPrivateKey) privateKey).getS().toByteArray(), keyLength);
        } else if (asymAlgo.isMLKEM()) {
            return ((KlMLKEMPrivateKey) privateKey).getKeyData();
        } else if (asymAlgo.isHQC()) {
            return ((KlHQCPrivateKey) privateKey).getSk();
        }
        throw BaseInternalError.GET_PRIVATE_KEY_ERROR.toException(UNSUPPORTED_KEY_TYPE_TYPE_IS_I18N_KEY, asymAlgo.getAlgoName());
    }

    /**
     * 根据非对称算法类型和私钥字节数组适配并返回相应的私钥对象
     *
     * @param asymAlgo        非对称算法类型
     * @param publicKey       对应的公钥对象，用于计算SM2的私钥
     * @param privateKeyBytes 明文私钥的字节数组，取值来源于 {@link #adaptPrivateKey(byte[], AsymAlgo)}处理后的私钥数据，不全是P8私钥
     * @return 根据算法类型适配后的私钥对象
     */
    public static PrivateKey getPrivateKey(AsymAlgo asymAlgo, PublicKey publicKey, byte[] privateKeyBytes) {
        if (asymAlgo.isRSA() || asymAlgo.isECC()) {
            return AsymKeyUtil.pkcs8Bytes2PrivateKey(privateKeyBytes);
        } else if (asymAlgo.isSM2()) {
            SubjectPublicKeyInfo subjectPublicKeyInfo = SubjectPublicKeyInfo.getInstance(publicKey.getEncoded());
            KlECPublicKey ecPublicKey = new KlECPublicKey(subjectPublicKeyInfo);
            BigInteger d = new BigInteger(privateKeyBytes);
            try {
                return new KlECPrivateKey(d, ecPublicKey);
            } catch (IOException e) {
                throw BaseInternalError.GET_PRIVATE_KEY_ERROR.toException(e);
            }
        } else if (asymAlgo.isKyber()) {
            return new KlKyberPrivateKey(privateKeyBytes);
        } else if (asymAlgo.isMLKEM()) {
            return new KlMLKEMPrivateKey(privateKeyBytes, asymAlgo.getOid());
        } else if (asymAlgo.isHQC()) {
            return new KlHQCPrivateKey(privateKeyBytes, asymAlgo.getOid());
        } else if (asymAlgo.isCntr()) {
            return new KlCntrPrivateKey(privateKeyBytes, asymAlgo.getOid());
        } else if (asymAlgo.isCtru()) {
            return new KlCtruPrivateKey(privateKeyBytes, asymAlgo.getOid());
        } else {
            throw BaseInternalError.GET_PRIVATE_KEY_ERROR.toException(UNSUPPORTED_KEY_TYPE_TYPE_IS_I18N_KEY, asymAlgo.getAlgoName());
        }
    }

    public static byte[] adaptSm2TestCenterPrivateKey(byte[] privateKeyBytes, PublicKey userPubKey) {
        PrivateKey privateKey;
        try {
            privateKey = CertUtil.getPrivateKey(PrivateKeyInfo.getInstance(privateKeyBytes));
        } catch (Exception e) {
            throw BaseInternalError.GET_PRIVATE_KEY_ERROR.toException(FAILED_TO_OBTAIN_PRIVATE_KEY_I18N_KEY, e);
        }
        byte[] publicKeyByte = ECKeyUtil.getNakedPublicKeyBytesByECPublicKey((ECPublicKey) userPubKey);

        PrivateKeyInfo4TestCenter privateKeyInfo = new PrivateKeyInfo4TestCenter(new ASN1Integer(VERSION),
            new ASN1Integer(((ECPrivateKey) privateKey).getS()), GMObjectIdentifiers.sm2p256v1,
            new DERBitString(publicKeyByte));
        try {
            return privateKeyInfo.getEncoded();
        } catch (IOException e) {
            throw BaseInternalError.GET_PRIVATE_KEY_ERROR.toException(e);
        }
    }

    /**
     * 移除私钥的PEM格式头尾，支持多种私钥格式
     *
     * @param value 私钥内容
     * @return 移除头尾后的私钥内容
     */
    public static String unFormatPrivateKey(String value) {
        if (value == null) {
            return null;
        }

        // 处理PKCS#8格式
        if (value.contains(PRIVATE_KEY_FORMAT_HEAD)) {
            return PEMUtil.unFormat(value, PRIVATE_KEY_FORMAT_HEAD, PRIVATE_KEY_FORMAT_TAIL);
        }

        // 处理RSA PKCS#1格式
        if (value.contains(RSA_PRIVATE_KEY_FORMAT_HEAD)) {
            return PEMUtil.unFormat(value, RSA_PRIVATE_KEY_FORMAT_HEAD, RSA_PRIVATE_KEY_FORMAT_TAIL);
        }

        // 处理EC PKCS#1格式
        if (value.contains(EC_PRIVATE_KEY_FORMAT_HEAD)) {
            return PEMUtil.unFormat(value, EC_PRIVATE_KEY_FORMAT_HEAD, EC_PRIVATE_KEY_FORMAT_TAIL);
        }

        // 处理加密的私钥格式
        if (value.contains(ENCRYPTED_PRIVATE_KEY_FORMAT_HEAD)) {
            return PEMUtil.unFormat(value, ENCRYPTED_PRIVATE_KEY_FORMAT_HEAD, ENCRYPTED_PRIVATE_KEY_FORMAT_TAIL);
        }

        return value;
    }
}
