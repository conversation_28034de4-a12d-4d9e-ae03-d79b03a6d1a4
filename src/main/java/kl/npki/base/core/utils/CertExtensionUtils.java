package kl.npki.base.core.utils;

import kl.nbase.security.asn1.ASN1ObjectIdentifier;
import kl.nbase.security.asn1.ASN1String;
import kl.nbase.security.asn1.DEROctetString;
import kl.nbase.security.asn1.misc.MiscObjectIdentifiers;
import kl.nbase.security.asn1.misc.NetscapeCertType;
import kl.nbase.security.asn1.x500.X500Name;
import kl.nbase.security.asn1.x500.style.BCStyle;
import kl.nbase.security.asn1.x509.*;
import kl.nbase.security.cert.bc.BcX509ExtensionUtils;
import kl.nbase.security.util.IPAddress;
import kl.nbase.security.util.encoders.Hex;
import kl.npki.base.core.biz.cert.model.parser.x509.ExtensionInfo;
import kl.npki.base.core.biz.cert.parser.constant.ExtensionType;
import kl.npki.base.core.biz.cert.parser.extension.AbstractX509ExtensionParser;
import kl.npki.base.core.constant.I18nConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.security.PublicKey;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;

import static kl.npki.base.core.biz.cert.parser.extension.AbstractX509ExtensionParser.EQUAL_SIGN;
import static kl.npki.base.core.biz.cert.parser.impl.X509CertificateParserImpl.EXTENSION_DECODER_MAP;
import static kl.npki.base.core.constant.BaseConstant.*;

/**
 * 证书扩展项工具类
 *
 * <AUTHOR>
 * @date 2022/12/22
 */
public class CertExtensionUtils {
    private static final Logger logger = LoggerFactory.getLogger(CertExtensionUtils.class);

    /**
     * CRL序列号（十进制）
     */
    private static final String CRL_PARAM_DECIMAL = "crl_param_decimal";

    /**
     * CRL序列号（十六进制）
     */
    private static final String CRL_PARAM_HEXADECIMAL = "crl_param_hexadecimal";

    /**
     * 构造证书主体备用名扩展项
     *
     * @param subject         证书主体
     * @param subjectAltNames 主体备用列表
     * @return
     */
    public static Extension makeSubjectAltNameOfSSLCert(X500Name subject, List<String> subjectAltNames) throws IOException {
        LinkedHashSet<String> finalSubjectAltNames = CollectionUtils.isEmpty(subjectAltNames) ?
            new LinkedHashSet<>(2) : new LinkedHashSet<>(subjectAltNames);
        String cn = ((ASN1String) subject.getRDNs(BCStyle.CN)[0].getFirst().getValue()).getString();
        if (CollectionUtils.isEmpty(subjectAltNames)) {
            // 如果没有设置主体备用名，则使用CN项作为主体备用名
            finalSubjectAltNames.add(cn);
        }
        List<GeneralName> generalNameList = new ArrayList<>(finalSubjectAltNames.size());
        finalSubjectAltNames.forEach(name -> {
            if (IPAddress.isValidIPv4(name) || IPAddress.isValidIPv4WithNetmask(name) ||
                IPAddress.isValidIPv6(name) || IPAddress.isValidIPv6WithNetmask(name)) {
                generalNameList.add(new GeneralName(GeneralName.iPAddress, name));
            } else {
                generalNameList.add(new GeneralName(GeneralName.dNSName, name));
            }
        });
        GeneralNames generalNames = new GeneralNames(generalNameList.toArray(new GeneralName[generalNameList.size()]));
        return new Extension(Extension.subjectAlternativeName, false, new DEROctetString(generalNames));
    }

    /**
     * 构造站点证书扩展项
     *
     * @param subject
     * @return
     * @throws IOException
     */
    public static Extension makeSubjectAltNameOfSSLCert(X500Name subject) throws IOException {
        return makeSubjectAltNameOfSSLCert(subject, null);
    }

    /**
     * 构造基本约束，CA证书时为关键扩展，终端证书时为非关键扩展
     *
     * @param isCa       是否CA证书
     * @param maxPathLen 最大深度，不设置则认为无限制
     */
    public static Extension makeBasicConstraints(boolean isCa, Integer maxPathLen) throws IOException {
        BasicConstraints basicConstraints;
        if (isCa) {
            // 如果是CA证书且外部指定了证书链深度，则设置深度否则默认深度不限制
            basicConstraints = maxPathLen == null ? new BasicConstraints(true) : new BasicConstraints(maxPathLen);
        } else {
            // 如果是终端证书，忽略证书链长度
            basicConstraints = new BasicConstraints(false);
        }
        // 仅CA证书基本限制为关键扩展项
        return new Extension(Extension.basicConstraints, isCa, new DEROctetString(basicConstraints));
    }

    /**
     * 构造密钥用法
     *
     * @param usage    密钥用法
     * @param critical 是否关键扩展
     */
    public static Extension makeKeyUsage(int usage, boolean critical) throws IOException {
        KeyUsage keyUsage = new KeyUsage(usage);
        return new Extension(Extension.keyUsage, critical, new DEROctetString(keyUsage));
    }

    public static Extension makeExtendedKeyUsage(KeyPurposeId usage) throws IOException {
        return makeExtendedKeyUsage(new KeyPurposeId[]{usage});
    }

    /**
     * 构造扩展密钥用法
     */
    public static Extension makeExtendedKeyUsage(KeyPurposeId[] usageArray) throws IOException {
        ExtendedKeyUsage extKeyUsage = new ExtendedKeyUsage(usageArray);
        return new Extension(Extension.extendedKeyUsage, false, new DEROctetString(extKeyUsage));
    }

    /**
     * 构造NetscapeCertType
     */
    public static Extension makeNetscapeCertType(int usage) throws IOException {
        NetscapeCertType netscapeCertType = new NetscapeCertType(usage);
        return new Extension(MiscObjectIdentifiers.netscapeCertType, false, new DEROctetString(netscapeCertType));
    }

    /**
     * 构造使用者密钥标识符
     */
    public static Extension makeSubjectKeyIdentifier(PublicKey publicKey) throws IOException {
        BcX509ExtensionUtils certExtUtils = new BcX509ExtensionUtils();
        SubjectKeyIdentifier subjectKeyIdentifier = certExtUtils.createSubjectKeyIdentifier(SubjectPublicKeyInfo.getInstance(publicKey.getEncoded()));
        return new Extension(Extension.subjectKeyIdentifier, false, new DEROctetString(subjectKeyIdentifier));
    }

    /**
     * 构造授权密钥标识符
     */
    public static Extension makeAuthorityKeyIdentifier(SubjectPublicKeyInfo issuerSubjectPubKeyInfo) throws IOException {
        BcX509ExtensionUtils certExtUtils = new BcX509ExtensionUtils();
        AuthorityKeyIdentifier authorityKeyIdentifier = certExtUtils.createAuthorityKeyIdentifier(issuerSubjectPubKeyInfo);
        return new Extension(Extension.authorityKeyIdentifier, false, new DEROctetString(authorityKeyIdentifier));
    }

    /**
     * 生成CRL分发点地址扩展
     *
     * @param crlDownloadUrl CRL下载地址
     * @return CDP扩展项
     */
    public static Extension makeCrlDistributionPoints(String crlDownloadUrl) throws IOException {

        GeneralName generalName = new GeneralName(GeneralName.uniformResourceIdentifier, crlDownloadUrl);
        GeneralNamesBuilder generalNamesBuilder = new GeneralNamesBuilder();
        generalNamesBuilder.addName(generalName);
        DistributionPointName distributionPointName = new DistributionPointName(generalNamesBuilder.build());
        DistributionPoint distributionPoint = new DistributionPoint(distributionPointName, null, null);

        CRLDistPoint crlDistPoint = new CRLDistPoint(new DistributionPoint[]{distributionPoint});
        return Extension.create(Extension.cRLDistributionPoints, false, crlDistPoint);
    }

    /**
     * 生成AIA分发点地址扩展
     *
     * @param caCertDownloadUrl CA证书下载地址
     * @return AIA扩展项
     */
    public static Extension makeAuthorityInfoAccess(String caCertDownloadUrl) throws IOException {

        GeneralName generalName = new GeneralName(GeneralName.uniformResourceIdentifier, caCertDownloadUrl);
        AccessDescription accessDescription = new AccessDescription(AccessDescription.id_ad_caIssuers, generalName);
        AuthorityInformationAccess authorityInformationAccess = new AuthorityInformationAccess(new AccessDescription[]{accessDescription});

        return Extension.create(Extension.authorityInfoAccess, false, authorityInformationAccess);
    }

    /**
     * 生成扩展信息
     *
     * @param extensions
     * @return 扩展信息列表
     * @throws IOException
     */
    public static List<ExtensionInfo> generateExtensionInfo(Extensions extensions) throws IOException {
        List<ExtensionInfo> extensionInfoList = new ArrayList<>();
        if (extensions != null && ArrayUtils.isNotEmpty(extensions.getExtensionOIDs())) {
            ASN1ObjectIdentifier[] extensionOidArray = extensions.getExtensionOIDs();
            for (ASN1ObjectIdentifier extensionOid : extensionOidArray) {
                // 使用映射中的解码器或默认解码器进行解码
                ExtensionInfo extensionInfo = EXTENSION_DECODER_MAP.getOrDefault(extensionOid, new AbstractX509ExtensionParser() {
                    @Override
                    protected String generatePrettyValue(byte[] extensionOctets) {
                        // 默认情况下，将扩展内容16进制编码后输出
                        return Hex.toHexString(extensionOctets);
                    }

                    @Override
                    public ExtensionType getExtensionType() {
                        // 获取扩展类型
                        return ExtensionType.getInstance(extensionOid);
                    }
                }).parse(extensions.getExtension(extensionOid));
                extensionInfoList.add(extensionInfo);
            }
        }
        return extensionInfoList;
    }


    /**
     * 格式化数字为指定进制，并确保至少2位，不足时在前面补0
     *
     * @param value 需要格式化的数字
     * @param radix 进制（10或16）
     * @return 格式化后的字符串
     */
    public static String formatNumber(int value, int radix) {
        if (radix == RADIX_DECIMAL) {
            return String.format(FORMAT_DECIMAL, value);
        } else if (radix == RADIX_HEXADECIMAL) {
            return String.format(FORMAT_HEXADECIMAL, value);
        } else {
            logger.error("CrlNumberExtensionParserImpl Invalid radix: {}", radix);
            return String.valueOf(value);
        }
    }

    /**
     * 生成一个易于阅读的扩展值字符串，此方法旨在将字节数组形式的扩展值转换为便于人类阅读和理解的字符串格式
     * <p> 示例： key为 crl_serial_number，则返回字符串为：
     * CRL序列号（十进制）：12345
     * CRL序列号（十六进制）：0x3039
     *
     * @param extensionOctets 扩展值的字节表示形式
     * @param i18nKey         国际化键
     * @return
     */
    public static String generatePrettyValue(byte[] extensionOctets, String i18nKey) {
        CRLNumber crlNumber = CRLNumber.getInstance(extensionOctets);
        String decimalNumber = formatNumber(crlNumber.getCRLNumber().intValue(), RADIX_DECIMAL);
        String hexadecimalNumber = formatNumber(crlNumber.getCRLNumber().intValue(), RADIX_HEXADECIMAL);
        return I18nConstant.getBaseCoreI18nMessage(i18nKey) + I18nConstant.getBaseCoreI18nMessage(CRL_PARAM_DECIMAL) + EQUAL_SIGN + decimalNumber
            + System.lineSeparator()
            + I18nConstant.getBaseCoreI18nMessage(i18nKey) + I18nConstant.getBaseCoreI18nMessage(CRL_PARAM_HEXADECIMAL) + EQUAL_SIGN + hexadecimalNumber;
    }


    /**
     * 生成一个扩展值字符串, 返回为16进制
     *
     * @param extensionOctets 扩展值的字节表示形式
     * @return 字符串格式的扩展值
     */
    public static String generateValue(byte[] extensionOctets) {
        CRLNumber crlNumber = CRLNumber.getInstance(extensionOctets);
        return CertExtensionUtils.formatNumber(crlNumber.getCRLNumber().intValue(), RADIX_HEXADECIMAL);
    }

    /**
     * 获取证书扩展项
     *
     * @param cert 证书
     * @param oid  扩展项OID
     * @return 扩展项，如果不存在则返回null
     */
    public static Extension getExtension(Certificate cert, ASN1ObjectIdentifier oid) {
        if (cert == null || oid == null) {
            return null;
        }

        try {
            Extensions extensions = cert.getTBSCertificate().getExtensions();
            if (extensions == null) {
                return null;
            }

            return extensions.getExtension(oid);
        } catch (Exception e) {
            // 处理异常情况
            return null;
        }
    }

}
