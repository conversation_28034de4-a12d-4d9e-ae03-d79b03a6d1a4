package kl.npki.base.core.utils;

import kl.nbase.config.constant.ConfigConstantsEnum;
import kl.nbase.config.store.consul.ConsulConfigStore;
import kl.nbase.db.core.DataSourceContext;
import kl.nbase.db.core.SwitchableDataSource;
import kl.nbase.db.support.druid.DruidDataSourceProperties;
import kl.nbase.db.support.druid.SlotDruidDataSourceProperties;
import kl.nbase.db.support.sharding.config.ShardingConfig;
import kl.nbase.db.support.slot.SlotDataSourceConfig;
import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.log.config.LogExtConfig;
import kl.npki.base.core.configs.LoginTypeConfig;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.EnvironmentEnum;
import kl.npki.base.core.constant.LoginType;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.npki.base.core.tenantholders.TenantContextHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static kl.npki.base.core.constant.KcspConstant.KCSP_LOG_STORE_TYPE;
import static kl.npki.base.core.constant.KcspConstant.KCSP_SDF_ENGINE_TYPE;

/**
 * 系统工具类
 *
 * <AUTHOR>
 */
public class SystemUtil {

    private SystemUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 判断是否已部署
     *
     * @return boolean true 已部署，false 未部署
     */
    public static boolean isDeployed() {
        SysInfoConfig sysInfoConfig = ConfigHolder.get().get(SysInfoConfig.class);
        String environmentSwitchId = sysInfoConfig.getEnvironmentSwitchId();
        return EnvironmentEnum.isDeployed(environmentSwitchId);
    }

    /**
     * 判断数据源是否已配置
     */
    public static boolean isDataSourceConfigured() {
        return isDataSourceConfigured(TenantContextHolder.getTenantId());
    }

    /**
     * 判断数据源是否已配置
     */
    public static boolean isDataSourceConfigured(String tenantId) {
        SlotDataSourceConfig slotConfig = ConfigHolder.get(tenantId).get(SlotDataSourceConfig.class);
        if (ObjectUtils.isEmpty(slotConfig)) {
            return false;
        }
        List<DruidDataSourceProperties> datasource = slotConfig.getDatasource();
        SlotDruidDataSourceProperties druid = slotConfig.getDruid();
        ShardingConfig sharding = slotConfig.getSharding();
        return CollectionUtils.isNotEmpty(datasource)
            && Objects.nonNull(druid)
            && Objects.nonNull(sharding)
            && StringUtils.isNotBlank(datasource.get(0).getUrl());
    }

    /**
     * 判断数据源是否可用
     *
     * @return boolean
     */
    public static boolean isDataSourceAvailable() {
        return isDataSourceConfigured(TenantContextHolder.getTenantId()) && isExistsDataSource(DataSourceContext.getTenantId());
    }

    /**
     * 判断数据源是否可用
     *
     * @return boolean
     */
    public static boolean isDataSourceAvailable(String tenantId) {
        return isDataSourceConfigured(tenantId) && isExistsDataSource(tenantId);
    }

    private static boolean isExistsDataSource(String tenantId) {
        SwitchableDataSource switchableDataSource = DataSourceContext.getSwitchableDataSource();
        if (switchableDataSource != null) {
            Map<String, DataSource> dataSources = switchableDataSource.getDataSources();
            return MapUtils.isNotEmpty(dataSources) && dataSources.containsKey(tenantId);
        }
        return false;
    }

    /**
     * 判断是否接入KCSP
     *
     * @return 是否接入KCSP
     */
    public static boolean isAccessKcsp() {
        // 判断是否接入KCSP,需要同时满足以下条件
        // 1. 使用KCSP SSO登录
        LoginTypeConfig loginTypeConfig = BaseConfigWrapper.getLoginTypeConfig();
        boolean isUseSsoLogin = loginTypeConfig.getLoginModel().toLowerCase().startsWith(LoginType.SSO.getType());
        // 2. 日志上报KCSP
        LogExtConfig logExtConfig = ConfigHolder.get().get(LogExtConfig.class);
        boolean isLogUploadToKcsp = logExtConfig.getStoreType() != null && logExtConfig.getStoreType().contains(KCSP_LOG_STORE_TYPE);
        // 3. 服务注册到Cosnul，使用Consul配置中心
        String configStoreType = System.getProperty(ConfigConstantsEnum.CONFIG_STORE_TYPE.getKey());
        boolean isUserConsulConfig = StringUtils.isEmpty(configStoreType) || ConsulConfigStore.STORE_TYPE.equals(configStoreType);
        // 4. engine使用KCSP SDF
        ClusterEngine clusterEngine = EngineHolder.get();
        boolean isUseKcspSdfEngine = ObjectUtils.isNotEmpty(clusterEngine) && clusterEngine.getAllEngineType().containsValue(KCSP_SDF_ENGINE_TYPE);
        // 5.KCSP是否激活开通NPKI
        String mgrGatewayIdentifier = BaseConfigWrapper.getKcspConfig().getMgrGatewayIdentifier();
        String svcGatewayIdentifier = BaseConfigWrapper.getKcspConfig().getSvcGatewayIdentifier();
        boolean isKcspActivated = StringUtils.isNotBlank(mgrGatewayIdentifier) && StringUtils.isNotBlank(svcGatewayIdentifier);
        // 最终决策
        return isUseSsoLogin && isLogUploadToKcsp && isUseKcspSdfEngine && isUserConsulConfig && isKcspActivated && SystemUtil.isDeployed();
    }

    /**
     * 判断是否为单点登录
     * @return
     */
    public static boolean isSSO() {
        LoginTypeConfig loginTypeConfig = ConfigHolder.get().get(LoginTypeConfig.class);
        return loginTypeConfig.getLoginModel().toLowerCase().startsWith(LoginType.SSO.getType());
    }
}
