package kl.npki.base.core.utils;

import java.io.File;

/**
 * 获取当前项目根路径作为临时目录
 * <AUTHOR>
 * @since 2025/5/9 9:42
 */
public class ProjectRootUtil {
    private static final String JAR_SUFFIX = ".jar";
    private static final String LIB_NAME = "lib";

    private static final String USER_DIR = "user.dir";
    private ProjectRootUtil() {
    }

    /**
     * 获取当前项目根路径
     * 开发环境-当前项目根路径
     * 服务器环境-部署文件夹下(lib的上层目录)
     * @return 临时文件路径
     */
    public static String getProjectRootPath() {
        String currentClassPath = ProjectRootUtil.class.getProtectionDomain().getCodeSource().getLocation().getPath();
        File currentClassFile = new File(currentClassPath);
        if (currentClassPath.endsWith(JAR_SUFFIX) && currentClassFile.getParent().endsWith(LIB_NAME)) {
            return currentClassFile.getParent().replace(LIB_NAME, "");
        } else {
            return System.getProperty(USER_DIR);
        }

    }
}
