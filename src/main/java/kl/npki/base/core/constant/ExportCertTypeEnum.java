package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;
import kl.npki.base.core.exception.BaseValidationError;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 导出证书格式类型，支持的格式有：DER、PEM、P7B_NO_CHAIN、P7B_WITH_CHAIN、PFX、JKS
 *
 * <AUTHOR>
 * @create 2025/1/7 下午3:33
 */
public enum ExportCertTypeEnum implements EnumI18n {
    DER("DER 编码二进制 X.509", ".der"),

    PEM("Base64 编码 X.509", ".cer"),

    P7B_NO_CHAIN("加密消息语法标准-PKCS #7 证书，不包含证书链", ".p7b"),

    P7B_WITH_CHAIN("加密消息语法标准-PKCS #7 证书，包含证书链", ".p7b"),

    PFX("个人信息交换格式-PKCS #12 证书", ".pfx"),

    JKS("Java密钥库", ".jks"),
    ;

    /**
     * 下载证书类型描述
     */
    private final String desc;

    /**
     * 文件后缀
     */
    private final String suffix;

    ExportCertTypeEnum(String desc, String suffix) {
        this.desc = desc;
        this.suffix = suffix;
    }

    public static ExportCertTypeEnum valueOfByType(String type) {
        for (ExportCertTypeEnum value : values()) {
            if (value.name().equalsIgnoreCase(type)) {
                return value;
            }
        }

        throw BaseValidationError.EXPORT_CERT_TYPE_ERROR.toException(type);
    }

    /**
     * 判断当前导出格式是否支持包含私钥
     */
    public boolean canExportWithPrivateKey() {
        return this == PFX || this == JKS;
    }

    /**
     * 判断当前导出格式是否支持包含证书链
     */
    public boolean canExportWithCertChain() {
        return this == P7B_WITH_CHAIN || canExportWithPrivateKey();
    }

    public String getSuffix() {
        return suffix;
    }

    public String getDesc() {
        return tr();
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}