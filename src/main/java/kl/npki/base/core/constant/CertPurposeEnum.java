package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;
import kl.nbase.i18n.i18n.I18nUtil;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 证书用途类型枚举
 *
 * <AUTHOR> href="mailto:<EMAIL>">shiliang</a>
 * @since 2025/06/20
 */
public enum CertPurposeEnum implements EnumI18n {
    NONE(0, "无用途"),
    SIGN(1, "签名证书"),
    ENCRYPT(2, "加密证书"),
    SIGN_AND_ENCRYPT(3, "签名和加密证书");

    private final int code;
    private final String description;

    CertPurposeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 检查是否包含签名用途
    public boolean hasSigning() {
        return (code & SIGN.code) != 0;
    }

    // 检查是否包含加密用途
    public boolean hasEncryption() {
        return (code & ENCRYPT.code) != 0;
    }

    // 根据值获取枚举
    public static CertPurposeEnum valueOfCode(int code) {
        for (CertPurposeEnum purpose : values()) {
            if (purpose.code == code) {
                return purpose;
            }
        }
        throw new IllegalArgumentException("Unknown certificate purpose value: " + code);
    }

    // 组合用途
    public static CertPurposeEnum combine(boolean signing, boolean encryption) {
        int value = 0;
        if (signing) value |= SIGN.code;
        if (encryption) value |= ENCRYPT.code;
        return valueOfCode(value);
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + getClass().getSimpleName() + I18nUtil.SEPARATOR + this.name().toLowerCase();
    }

}