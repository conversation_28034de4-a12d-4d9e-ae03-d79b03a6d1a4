package kl.npki.base.core.constant;

/**
 * 系统日志类型枚举类
 *
 * <AUTHOR>
 * @since 2025/7/7 15:01
 */
public enum SystemLogEnum {

    /**
     * 安全日志
     */
    SECURITY_LOG("security_log", 1, "安全日志"),

    /**
     * 业务日志
     */
    OPERATION_LOG("operation_log", 2, "业务日志"),

    /**
     * 系统服务日志
     */
    API_LOG("api_log", 3, "系统服务日志"),

    ;

    private final String name;
    private final Integer type;
    private final String desc;

    SystemLogEnum(String name, Integer code, String desc) {
        this.name = name;
        this.type = code;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public Integer getType() {
        return type;
    }
}
