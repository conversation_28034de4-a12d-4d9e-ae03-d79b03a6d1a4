package kl.npki.base.core.constant;

/**
 *
 * <AUTHOR>
 * @date 2022/7/6
 * @desc 数据库类型枚举
 */
public enum DbConfigType {
	/**
	 * 数据源的相关参数配置
	 */
	H2("h2", 8082, "org.h2.Driver", "sql/h2.sql", "jdbc:h2:${path}","sql/h2_multi.sql"),
	ORACLE("oracle", 1521, "oracle.jdbc.OracleDriver", "sql/oracle.sql", "jdbc:oracle:thin:@${ip}:${port}:${path}", "sql/oracle_multi.sql", "select 1 from dual"),
	ORACLE_RAC("oracle", 1521, "oracle.jdbc.OracleDriver", "sql/oracle.sql", "****************************** =(ADDRESS = (PROTOCOL = TCP)(HOST = ${ip})(PORT = ${port}))(ADDRESS = (PROTOCOL = TCP)(HOST = ${ip_rac})(PORT = ${port_rac}))(LOAD_BALANCE = yes)(FAILOVER = ON)(CONNECT_DATA =(SERVER = DEDICATED)(SERVICE_NAME = ${path})(FAILOVER_MODE=(TYPE = SELECT)(METHOD = BASIC)(RETIRES = 20)(DELAY = 15))))", "sql/oracle_multi.sql"),
	DB2("db2",1527, "com.ibm.db2.jcc.DB2Driver", "sql/db2.sql", "jdbc:db2://${ip}:${port}/${path}", "sql/db2_multi.sql"),
	// cp936代表连接是GBK
	SYBASE("sybase", 5000, "com.sybase.jdbc3.jdbc.SybDriver", "sql/sybase.sql", "jdbc:sybase:Tds:${ip}:${port}/${path}?charset=cp936", "sql/sybase_multi.sql"),
	MSSQL("sqlserver", 1433, "com.microsoft.sqlserver.jdbc.SQLServerDriver", "sql/mssql.sql", "jdbc:sqlserver://${ip}:${port};databaseName=${path}", "sql/mssql_multi.sql"),
	GBASE("gbase", 5258, "com.gbase.jdbc.Driver", "sql/gbase.sql", "jdbc:gbase://${ip}:${port}/${path}", "sql/gbase_multi.sql"),
	// GBASE有多种兼容模式 A-Oracle兼容, B-MySQL兼容, PG-postgresql兼容, MSSQL-SQL Server兼容
	GBASE_B("gbase_b", 15400, "com.gbase.Driver", "sql/gbase_b.sql", "jdbc:gbase://${ip}:${port}/${path}", "sql/gbase_b_multi.sql"),
	INFORMIX_GBASE("gbase 8s", 1533, "com.gbasedbt.jdbc.IfxDriver", "sql/gbase.sql", "jdbc:gbasedbt-sqli://${ip}:${port}/${path}:gbasedbtserver=gbaseserver;DB_LOCALE=zh_cn.${charSet};CLIENT_LOCALE=zh_cn.${charSet}", "sql/gbase_multi.sql"),
	FIREBIRD("Firebird", 3050, "org.firebirdsql.jdbc.FBDriver", "sql/firebird.sql", "jdbc:firebirdsql:${ip}/${port}:${path}", "sql/firebird_multi.sql"),
	SQLITE("sqlite", 808, "org.sqlite.JDBC", "sql/sqlite.sql", "jdbc:sqlite:${path}", "sql/sqlite_multi.sql"),
	MYSQL("mysql", 3306, "com.mysql.jdbc.Driver", "sql/mysql.sql", "jdbc:mysql://${ip}:${port}/${path}?useUnicode=true&characterEncoding=${charSet}&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=${time_zone}&useSSL=false&allowPublicKeyRetrieval=true", "sql/mysql_multi.sql"),
	MYSQL8("mysql", 3306, "com.mysql.cj.jdbc.Driver", "sql/mysql.sql", "jdbc:mysql://${ip}:${port}/${path}?useUnicode=true&characterEncoding=${charSet}&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=${time_zone}&useSSL=false&allowPublicKeyRetrieval=true", "sql/mysql_multi.sql"),
	GREATDB("greatdb", 3306, "com.mysql.cj.jdbc.Driver", "sql/greatdb.sql", "jdbc:mysql://${ip}:${port}/${path}?useUnicode=true&characterEncoding=${charSet}&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=${time_zone}&useSSL=false&allowPublicKeyRetrieval=true", "sql/greatdb_multi.sql"),
    OCAEANBASE("mysql", 2881, "com.mysql.cj.jdbc.Driver", "sql/mysql.sql", "jdbc:mysql://${ip}:${port}/${path}?useUnicode" +
        "=true&characterEncoding=${charSet}&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=${time_zone}&useSSL=false&allowPublicKeyRetrieval=true", "sql/mysql_multi.sql"),
	KINGBASE("kingbase", 54321, "com.kingbase8.Driver", "sql/kingbase.sql", "jdbc:kingbase8://${ip}:${port}/${path}", "sql/kingbase8_multi.sql"),
	OSCAR("oscar", 2003, "com.oscar.Driver", "sql/oscar.sql", "jdbc:oscar://${ip}:${port}/${path}", "sql/oscar_multi.sql"),
	DM("dm", 5236, "dm.jdbc.driver.DmDriver", "sql/dm.sql", "jdbc:dm://${ip}:${port}?schema=${path}&characterEncoding=${charSet}", "sql/dm_multi.sql"),
	TBASE("tbase", 1546, "org.postgresql.Driver", "sql/tbase.sql", "jdbc:postgresql://${ip}:${port}/${path}", "sql/tbase_multi.sql"),
	DERBY("derby", -1, "org.apache.derby.jdbc.EmbeddedDriver", "sql/derby.sql", "jdbc:derby:${path};create=true", "sql/derby_multi.sql", "SELECT 1 FROM SYSIBM.SYSDUMMY1"),

	/** 华为高斯数据库 */
	GAUSSDB("gaussdb", 8000, "com.huawei.opengauss.jdbc.Driver", "sql/gaussdb.sql", "jdbc:opengauss://${ip}:${port}/${path}?currentSchema=${schema}", "sql/gaussdb_multi.sql"),
	;

	/**
	 * 数据库类型名称
	 */
	private final String dbTypeName;
	/**
	 * 默认端口
	 */
	private final int defaultPort;
	/**
	 * 驱动
	 */
	private final String driver;
	/**
	 * sql脚本相对resource位置
	 */
	private final String sqlFilePath;
	/**
	 * 数据库连接url
	 */
	private final String jdbcUrl;
	/**
	 * 创建分表的sql脚本相对resource位置
	 */
	private final String multiSqlFilePath;

	/**
	 * 检查连接是否有效的查询语句
	 */
	private final String validationQuery;

	DbConfigType(String dbTypeName, int port, String driver, String sqlFilePath, String url, String multiSqlFilePath) {
		this(dbTypeName, port, driver, sqlFilePath, url, multiSqlFilePath, "SELECT 1");
	}

	DbConfigType(String dbTypeName, int port, String driver, String sqlFilePath,
                 String url, String multiSqlFilePath, String validationQuery) {
		this.dbTypeName = dbTypeName;
		this.defaultPort = port;
		this.driver = driver;
		this.sqlFilePath = sqlFilePath;
		this.jdbcUrl = url;
		this.multiSqlFilePath = multiSqlFilePath;
		this.validationQuery = validationQuery;
	}

	public static DbConfigType getDbConfigTypeByDriver(String driver){
		// 默认为H2
        DbConfigType result = null;
	    for(DbConfigType dbConfigType : DbConfigType.values()){
            if(driver.equals(dbConfigType.getDriver())){
                result = dbConfigType;
                break;
            }
        }
	    return result;
    }

	/**
	 * 判断是否需要时区参数
	 */
	public boolean needTimeZoneParam() {
		return MYSQL.equals(this) || MYSQL8.equals(this) || GREATDB.equals(this) || OCAEANBASE.equals(this);
	}

	public String getDbTypeName() {
		return dbTypeName;
	}

	public int getDefaultPort() {
		return defaultPort;
	}

	public String getJdbcUrl() {
		return jdbcUrl;
	}

	public String getDriver() {
		return driver;
	}

	public String getSqlFilePath() {
		return sqlFilePath;
	}

	public String getMultiSqlFilePath() {
		return multiSqlFilePath;
	}

	public String getValidationQuery() {
		return validationQuery;
	}
}
