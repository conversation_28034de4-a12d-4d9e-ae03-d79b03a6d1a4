package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;
/**
 * @Author: guoq
 * @Date: 2024/8/2
 * @description:
 */
public enum CertSource implements EnumI18n {

    /**
     * 自签发
     */
    SELF_ISSUE(0, "自签发"),
    /**
     * 外部导入
     */
    EXTERNAL_IMPORT(1, "外部导入"),
    ;

    CertSource(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return tr();
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
