package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;
/**
 * 定时任务枚举
 *
 * @<PERSON> <PERSON>
 * @Date 2023/12/25
 */
public enum BaseJobEnum implements EnumI18n {

    TABLE_SHARDING("timer.job.table.sharding", "分表创建定时任务"),
    SELF_CHECK("timer.job.self.check", "服务自检定时任务"),
    HOME_PAGE_DATA_UPDATE("timer.job.home.cache.update", "首页数据缓存更新定时任务"),
    SYSTEM_INSPECTION("timer.job.system.inspection", "系统巡检定时任务"),
    ;

    private String id;

    private String name;

    BaseJobEnum(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return tr();
    }


    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
