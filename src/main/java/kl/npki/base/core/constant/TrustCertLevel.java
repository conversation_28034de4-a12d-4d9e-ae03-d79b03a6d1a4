package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;
/**
 * <AUTHOR>
 * @date 2022/9/7
 * @desc
 */
public enum TrustCertLevel implements EnumI18n {
    /**
     * 上级证书
     */
    SUPER(0, "上级证书"),
    /**
     * 本级证书
     */
    LOCAL(1, "本级证书"),
    ;

    TrustCertLevel(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return tr();
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
