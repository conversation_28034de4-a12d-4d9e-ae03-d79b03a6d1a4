package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;
/**
 * T_TRUST_CERT表中的证书类型
 * <AUTHOR>
 */
public enum TrustCertType implements EnumI18n {

    /**
     * 管理证书
     */
    MANAGE(0, "管理证书"),

    /**
     * SSL证书
     */
    SSL(1, "SSL证书"),

    /**
     * 身份证书
     */
    IDENTITY(2, "身份证书")
    ;

    private final Integer code;

    private final String desc;

    TrustCertType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return tr();
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
