package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.I18nUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/1/17 10:05
 */
public class I18nConstant {
    private I18nConstant() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 国际化资源路径
     */
    public static final String BASE_CORE_I18N_RESOURCE_PATH = "kl.npki.base.core.i18n";
    /**
     * 国际化资源路径前缀
     */
    public static final String BASE_CORE_I18N_MESSAGE_KEY_PREFIX = BASE_CORE_I18N_RESOURCE_PATH + I18nUtil.SEPARATOR;
    /**
     * 服务器认证
     */
    public static final String SERVER_AUTHENTICATION_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "server_authentication";
    /**
     * 客户端认证
     */
    public static final String CLIENT_AUTHENTICATION_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "client_authentication";
    /**
     * 代码签名
     */
    public static final String CODE_SIGNING_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "code_signing";
    /**
     * Email保护
     */
    public static final String EMAIL_PROTECTION_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "email_protection";
    /**
     * 时间戳生成
     */
    public static final String TIMESTAMP_GENERATION_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "timestamp_generation";
    /**
     * OCSP应答签名
     */
    public static final String OCSP_RESPONSE_SIGNING_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "ocsp_response_signing";
    /**
     * KDC身份认证
     */
    public static final String KDC_AUTHENTICATION_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "kdc_authentication";
    /**
     * 智能卡登录
     */
    public static final String SMART_CARD_LOGIN_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "smart_card_login";
    /**
     * 文件系统加密
     */
    public static final String FILE_SYSTEM_ENCRYPTION_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "file_system_encryption";
    /**
     * 密钥用法
     */
    public static final String KEY_USAGE_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "key_usage";
    /**
     * 增强型密钥用法
     */
    public static final String EXTENDED_KEY_USAGE_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "extended_key_usage";
    /**
     * 基本约束
     */
    public static final String BASIC_CONSTRAINTS_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "basic_constraints";
    /**
     * 使用者密钥标识符
     */
    public static final String SUBJECT_KEY_IDENTIFIER_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "subject_key_identifier";
    /**
     * 授权者密钥标识符
     */
    public static final String AUTHORITY_KEY_IDENTIFIER_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "authority_key_identifier";
    /**
     * 使用者备用名称
     */
    public static final String SUBJECT_ALTERNATIVE_NAME_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "subject_alternative_name";
    /**
     * 颁发者备用名称
     */
    public static final String ISSUER_ALTERNATIVE_NAME_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "issuer_alternative_name";
    /**
     * CRL 分发点
     */
    public static final String CRL_DISTRIBUTION_POINTS_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "crl_distribution_points";
    /**
     * 颁发机构信息访问
     */
    public static final String AUTHORITY_INFORMATION_ACCESS_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "authority_information_access";
    /**
     * 证书策略
     */
    public static final String CERTIFICATE_POLICIES_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "certificate_policies";
    /**
     * 策略映射
     */
    public static final String POLICY_MAPPINGS_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "policy_mappings";
    /**
     * 策略约束
     */
    public static final String POLICY_CONSTRAINTS_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "policy_constraints";
    /**
     * CRL 序列号
     */
    public static final String CRL_NUMBER_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "crl_number";
    /**
     * Delta CRL 指示器
     */
    public static final String DELTA_CRL_INDICATOR_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "delta_crl_indicator";
    /**
     * 最新 CRL
     */
    public static final String FRESHEST_CRL_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "freshest_crl";
    /**
     * 证书透明度
     */
    public static final String CERTIFICATE_TRANSPARENCY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "certificate_transparency";
    /**
     * Netscape证书类型
     */
    public static final String NETSCAPE_CERTIFICATE_TYPE_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "netscape_certificate_type";
    /**
     * 后量子公钥
     */
    public static final String POST_QUANTUM_PUBLIC_KEY_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "post_quantum_public_key";
    /**
     * 后量子签名
     */
    public static final String POST_QUANTUM_SIGNATURE_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "post_quantum_signature";
    /**
     * ocsp不撤销检查
     */
    public static final String OCSP_NO_CHECK_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "ocsp_no_check";

    /**
     * 安全/多用途 Internet 邮件扩展 (S/MIME) 功能
     */
    public static final String SMIME_CAPABILITIES_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "smime_capabilities";
    /**
     * 模板名称
     */
    public static final String TEMPLATE_NAME_I18N_KEY = BASE_CORE_I18N_MESSAGE_KEY_PREFIX + "template_name";


    // region License
    /**
     * License状态
     */
    public static final String LICENSE_STATUS_I18N_KEY = "license_status_i18n_key";

    /**
     * License有效
     */
    public static final String LICENSE_STATUS_VALID_I18N_KEY = "license_status_valid_i18n_key";

    /**
     * License中的硬件序列号与本机不匹配
     */
    public static final String LICENSE_STATUS_WRONG_SERIAL_I18N_KEY = "license_status_wrong_serial_i18n_key";

    /**
     * License未生效或已过期
     */
    public static final String LICENSE_STATUS_EXPIRED_I18N_KEY = "license_status_expired_i18n_key";

    /**
     * License格式错误
     */
    public static final String LICENSE_STATUS_WRONG_FORMAT_I18N_KEY = "license_status_wrong_format_i18n_key";

    /**
     * License签名无效
     */
    public static final String LICENSE_STATUS_WRONG_SIGN_I18N_KEY = "license_status_wrong_sign_i18n_key";

    /**
     * 系统尚未导入License
     */
    public static final String LICENSE_STATUS_NOT_IMPORTED_I18N_KEY = "license_status_not_imported_i18n_key";

    /**
     * License异常
     */
    public static final String LICENSE_STATUS_OTHER_I18N_KEY = "license_status_other_i18n_key";

    /**
     * License有效（将在x天后过期）
     */
    public static final String LICENSE_STATUS_ABOUT_TO_EXPIRE_I18N_KEY = "license_status_about_to_expire_i18n_key";

    /**
     * 有效期剩余x天
     */
    public static final String EXPIRES_IN_FEW_DAYS_I18N_KEY = "expires_in_few_days_i18n_key";

    /**
     * 检测License有效期失败
     */
    public static final String FAIL_TO_CHECK_LICENSE_VALIDITY_PERIOD_I18N_KEY = "fail_to_check_license_validity_period_i18n_key";
    // endregion

    /**
     * 未知/Unknown，该项通用
     */
    public static final String COMMON_UNKNOWN = "common_unknown";

    /**
     * 自检过程中发生异常
     */
    public static final String SELF_CHECK_EXCEPTION_OCCURRED_I18N_KEY = "self_check_exception_occurred";

    /**
     * 自检异常类型国际化资源键
     */
    public static final String SELF_CHECK_EXCEPTION_TYPE_I18N_KEY = "self_check_exception_type";

    /**
     * 自检异常信息
     */
    public static final String SELF_CHECK_EXCEPTION_MESSAGE_I18N_KEY = "self_check_exception_message";


    /**
     * 日期时间格式化相关的国际化资源键
     */
    public static final String DAY_PATTERN_MESSAGE_I18N_KEY = "day_pattern";
    public static final String DATE_TIME_PATTERN_MESSAGE_I18N_KEY = "date_time_pattern";
    public static final String DATE_TIME_MS_PATTERN_MESSAGE_I18N_KEY = "date_time_ms_pattern";
    public static final String FILE_DATE_TIME_PATTERN_MESSAGE_I18N_KEY = "file_date_time_pattern";

    /**
     * 获取国际化后的信息
     *
     * @param key  国际化资源的键（不包含资源路径）
     * @param args 占位填充参数
     * @return 国际化结果
     */
    public static String getBaseCoreI18nMessage(String key, Object... args) {
        return I18nUtil.tr(BASE_CORE_I18N_MESSAGE_KEY_PREFIX + key, args);
    }

    /**
     * 获取国际化后的信息
     *
     * @param key  国际化资源的键（不包含资源路径）
     * @param args 占位填充参数
     * @return 国际化结果
     */
    public static String getBaseCoreI18nMessage(String defaultMsg, String key, Object... args) {
        String i18nHeadValue = I18nConstant.getBaseCoreI18nMessage(key, args);
        if (StringUtils.isNotBlank(i18nHeadValue) && !key.equalsIgnoreCase(i18nHeadValue)) {
            return i18nHeadValue;
        }
        return defaultMsg;
    }

}