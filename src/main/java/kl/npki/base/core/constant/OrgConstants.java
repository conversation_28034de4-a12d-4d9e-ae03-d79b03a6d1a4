package kl.npki.base.core.constant;

/**
 * <AUTHOR>
 * @create 2024/3/26 16:47
 */
public class OrgConstants {

    private OrgConstants() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 头行数
     */
    public final static int BATCH_IMPORT_ORG_HEAD_ROW_NUMBER = 2;

    /**
     * 根机构固定ID
     */
    public static final Long ORG_ROOT_ID = 1000L;

    /**
     * 根机构固定编码
     */
    public static final Long ORG_ROOT_CODE = 1000L;

    /**
     * 根机构的父机构编码
     */
    public static final String ORG_ROOT_PARENT_CODE_FLAG = "-1";
    /**
     * 根机构的父机构ID
     */
    public static final Long ORG_ROOT_PARENT_ID_FLAG = -1L;


    /**
     * 根机构级别
     */
    public static final String ORG_ROOT_LEVEL_FLAG = "0";

    /**
     * 签名数据
     */
    public static final String SIGN_DATA = "signData";

    /**
     * 签名算法
     */
    public static final String SIGN_METHOD = "signMethod";

    /**
     * 应用标识
     */
    public static final String APP_KEY = "appKey";

    /**
     * once是一个只被使用一次的任意非重复的随机数值
     */
    public static final String ONCE = "once";

    /**
     * 时间戳 单位为ms
     */
    public static final String TIME_STAMP = "ts";

    /**
     * 同步序列
     */
    public static final String SYNC_SEQUENCE = "syncSequence";


    /**
     * 同步响应码
     */
    public static final String CODE = "code";

    /**
     * 同步响应数据
     */
    public static final String RESULT = "result";

    /**
     * 组织ID
     */
    public static final String ORG_ID = "orgId";

    /**
     * 组织名称
     */
    public static final String ORG_NAME = "orgName";

    /**
     * 组织简称
     */
    public static final String SHORT_NAME = "shortName";

    /**
     * 上级组织id
     */
    public static final String PARENT_ID = "parentId";

    /**
     * 联系电话
     */
    public static final String OFFICE_PHONE = "officePhone";

    /**
     * 邮政编码
     */
    public static final String POST_CODE = "postCode";

    /**
     * 办公地址
     */
    public static final String ADDRESS = "address";

    /**
     * 组织类型
     */
    public static final String ORG_TYPE = "orgType";

    /**
     * 状态 （2000：正常；3000：停⽤； -1000：注销）
     */
    public static final String STATUS = "status";

    /**
     * 排序号
     */
    public static final String SHOW_ORDER = "showOrder";

    /**
     * 同步响应数据
     */
    public static final String COMPANY_ID = "companyId";

    /**
     * 数据的同步状态 2000:正常， 800：撤回
     */
    public static final String SYNC_STATUS = "syncStatus";

    /**
     * 排序路径
     */
    public static final String SHOW_ORDER_FULL = "showOrderFull";


}