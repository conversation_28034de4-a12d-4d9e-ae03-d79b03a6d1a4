package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;
import kl.nbase.i18n.i18n.I18nUtil;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 审计状态枚举
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 * @date 2025/6/11
 */
public enum AuditStatusEnum implements EnumI18n {

    NO_AUDIT(-1, "不审计"),
    PENDING(0, "待审计"),
    SUCCESS(1, "审计成功"),
    FAILED(2, "审计失败");

    private final Integer code;
    private final String desc;

    AuditStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AuditStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AuditStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + getClass().getSimpleName() + I18nUtil.SEPARATOR + this.name().toLowerCase();
    }

}
