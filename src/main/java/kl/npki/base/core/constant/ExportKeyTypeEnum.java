package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;
import kl.npki.base.core.exception.BaseValidationError;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 导出私钥格式类型，支持的格式有：PFX、P8、GM/T0009-2012、GM/T0009-2023、GM/T0016-2023、GM/T0092-2020
 *
 * <AUTHOR>
 * @create 2025/1/7 下午3:33
 */
public enum ExportKeyTypeEnum implements EnumI18n {

    PFX("个人信息交换格式 - PKCS #12 证书", ".pfx"),

    P8("私钥信息语法格式 - PKCS #8", ".p8.key"),

    GMT0009_2012("GM/T 0009-2012 SM2密钥对私钥保护格式", ".GMT0009_2012.key"),

    GMT0009_2023("GM/T 0009-2023 SM2密钥对私钥保护格式", ".GMT0009_2023.key"),

    GMT0016_2023("GM/T 0016-2023 SM2密钥对私钥保护格式", ".GMT0016.key"),

    GMT0092_2020("GM/T 0092-2020 SM2密钥对私钥保护格式", ".GMT0092.key");

    /**
     * 下载证书类型描述
     */
    private final String desc;

    /**
     * 文件后缀
     */
    private final String suffix;


    ExportKeyTypeEnum(String desc, String suffix) {
        this.desc = desc;
        this.suffix = suffix;
    }

    public String getSuffix() {
        return suffix;
    }

    public static ExportKeyTypeEnum valueOfByType(String type) {
        for (ExportKeyTypeEnum value : values()) {
            if (value.name().equalsIgnoreCase(type)) {
                return value;
            }
        }

        throw BaseValidationError.EXPORT_CERT_TYPE_ERROR.toException(type);
    }

    public String getDesc() {
        return tr();
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}