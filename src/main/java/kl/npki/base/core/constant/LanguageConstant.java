package kl.npki.base.core.constant;


import kl.nbase.i18n.i18n.EnumI18n;
import kl.nbase.i18n.i18n.I18nUtil;
import org.apache.commons.lang3.StringUtils;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

public enum LanguageConstant implements EnumI18n {

    ZH( "中文"),
    EN("英文"),
    ;


    private String languageName;

    public String getLanguageName() {
        String i18nValue = tr();
        if(StringUtils.isBlank(i18nValue) || getMessageKey().equalsIgnoreCase(i18nValue)){
            return languageName;
        }
        return i18nValue;
    }


    LanguageConstant(String languageName) {
        this.languageName = languageName;
    }



    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.getClass().getSimpleName() + "." + this.name().toLowerCase();
    }

}
