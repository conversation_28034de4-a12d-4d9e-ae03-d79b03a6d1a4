package kl.npki.base.core.constant;

import kl.nbase.security.entity.algo.BlockSymAlgo;
import kl.npki.base.core.exception.BaseInternalError;
import org.apache.commons.lang3.StringUtils;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.MASTER_KEY_ALGORITHM_CONVERSION_FAILED_I18N_KEY;
/**
 * @Author: guoq
 * @Date: 2023/4/6
 * @description:
 */
public enum MainKeyAlgoEnum {
    /**
     * 算法类型
     */
    SM4, SM1;

    /**
     * 转换主密钥算法
     *
     * @param algoName
     * @return
     */
    public static BlockSymAlgo convertor(String algoName) {
        if (StringUtils.isBlank(algoName)) {
            return null;
        }
        if (StringUtils.containsIgnoreCase(algoName, SM4.name())) {
            return BlockSymAlgo.SM4_GCM_NOPADDING;
        } else if (StringUtils.containsIgnoreCase(algoName, SM1.name())) {
            return BlockSymAlgo.SM1_CBC_PADDING5;
        }
        throw BaseInternalError.ALGORITHM_NOT_SUPPORT_ERROR.toException(MASTER_KEY_ALGORITHM_CONVERSION_FAILED_I18N_KEY);
    }

}
