package kl.npki.base.core.constant;

import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * 参考 org.apache.tomcat.util.net.openssl.ciphers并简化而来
 *
 * @date 2025/4/1 增加国密协议以及对应的算法套件
 *
 * 算法套件枚举
 *
 * <AUTHOR>
 */
public enum CipherEnum {

    TLS_RSA_WITH_NULL_MD5(1, ProtocolEnum.SSLv3, new String[]{"SSL_RSA_WITH_NULL_MD5", "TLS_RSA_WITH_NULL_MD5"}),
    TLS_RSA_WITH_NULL_SHA(2, ProtocolEnum.SSLv3, new String[]{"SSL_RSA_WITH_NULL_SHA", "TLS_RSA_WITH_NULL_SHA"}),
    TLS_RSA_EXPORT_WITH_RC4_40_MD5(3, ProtocolEnum.SSLv3, new String[]{"SSL_RSA_EXPORT_WITH_RC4_40_MD5", "TLS_RSA_EXPORT_WITH_RC4_40_MD5"}),
    TLS_RSA_WITH_RC4_128_MD5(4, ProtocolEnum.SSLv3, new String[]{"SSL_RSA_WITH_RC4_128_MD5", "TLS_RSA_WITH_RC4_128_MD5"}),
    TLS_RSA_WITH_RC4_128_SHA(5, ProtocolEnum.SSLv3, new String[]{"SSL_RSA_WITH_RC4_128_SHA", "TLS_RSA_WITH_RC4_128_SHA"}),
    TLS_RSA_EXPORT_WITH_RC2_CBC_40_MD5(6, ProtocolEnum.SSLv3, new String[]{"SSL_RSA_EXPORT_WITH_RC2_CBC_40_MD5", "TLS_RSA_EXPORT_WITH_RC2_CBC_40_MD5"}),
    TLS_RSA_WITH_IDEA_CBC_SHA(7, ProtocolEnum.SSLv3, new String[]{"SSL_RSA_WITH_IDEA_CBC_SHA", "TLS_RSA_WITH_IDEA_CBC_SHA"}),
    TLS_RSA_EXPORT_WITH_DES40_CBC_SHA(8, ProtocolEnum.SSLv3, new String[]{"SSL_RSA_EXPORT_WITH_DES40_CBC_SHA", "TLS_RSA_EXPORT_WITH_DES40_CBC_SHA"}),
    TLS_RSA_WITH_DES_CBC_SHA(9, ProtocolEnum.SSLv3, new String[]{"SSL_RSA_WITH_DES_CBC_SHA", "TLS_RSA_WITH_DES_CBC_SHA"}),
    TLS_RSA_WITH_3DES_EDE_CBC_SHA(10, ProtocolEnum.SSLv3, new String[]{"SSL_RSA_WITH_3DES_EDE_CBC_SHA", "TLS_RSA_WITH_3DES_EDE_CBC_SHA"}),
    TLS_DH_DSS_EXPORT_WITH_DES40_CBC_SHA(11, ProtocolEnum.SSLv3, new String[]{"SSL_DH_DSS_EXPORT_WITH_DES40_CBC_SHA", "TLS_DH_DSS_EXPORT_WITH_DES40_CBC_SHA"}),
    TLS_DH_DSS_WITH_DES_CBC_SHA(12, ProtocolEnum.SSLv3, new String[]{"SSL_DH_DSS_WITH_DES_CBC_SHA", "TLS_DH_DSS_WITH_DES_CBC_SHA"}),
    TLS_DH_DSS_WITH_3DES_EDE_CBC_SHA(13, ProtocolEnum.SSLv3, new String[]{"SSL_DH_DSS_WITH_3DES_EDE_CBC_SHA", "TLS_DH_DSS_WITH_3DES_EDE_CBC_SHA"}),
    TLS_DH_RSA_EXPORT_WITH_DES40_CBC_SHA(14, ProtocolEnum.SSLv3, new String[]{"SSL_DH_RSA_EXPORT_WITH_DES40_CBC_SHA", "TLS_DH_RSA_EXPORT_WITH_DES40_CBC_SHA"}),
    TLS_DH_RSA_WITH_DES_CBC_SHA(15, ProtocolEnum.SSLv3, new String[]{"SSL_DH_RSA_WITH_DES_CBC_SHA", "TLS_DH_RSA_WITH_DES_CBC_SHA"}),
    TLS_DH_RSA_WITH_3DES_EDE_CBC_SHA(16, ProtocolEnum.SSLv3, new String[]{"SSL_DH_RSA_WITH_3DES_EDE_CBC_SHA", "TLS_DH_RSA_WITH_3DES_EDE_CBC_SHA"}),
    TLS_DHE_DSS_EXPORT_WITH_DES40_CBC_SHA(17, ProtocolEnum.SSLv3, new String[]{"SSL_DHE_DSS_EXPORT_WITH_DES40_CBC_SHA", "TLS_DHE_DSS_EXPORT_WITH_DES40_CBC_SHA"}),
    TLS_DHE_DSS_WITH_DES_CBC_SHA(18, ProtocolEnum.SSLv3, new String[]{"SSL_DHE_DSS_WITH_DES_CBC_SHA", "TLS_DHE_DSS_WITH_DES_CBC_SHA"}),
    TLS_DHE_DSS_WITH_3DES_EDE_CBC_SHA(19, ProtocolEnum.SSLv3, new String[]{"SSL_DHE_DSS_WITH_3DES_EDE_CBC_SHA", "TLS_DHE_DSS_WITH_3DES_EDE_CBC_SHA"}),
    TLS_DHE_RSA_EXPORT_WITH_DES40_CBC_SHA(20, ProtocolEnum.SSLv3, new String[]{"SSL_DHE_RSA_EXPORT_WITH_DES40_CBC_SHA", "TLS_DHE_RSA_EXPORT_WITH_DES40_CBC_SHA"}),
    TLS_DHE_RSA_WITH_DES_CBC_SHA(21, ProtocolEnum.SSLv3, new String[]{"SSL_DHE_RSA_WITH_DES_CBC_SHA", "TLS_DHE_RSA_WITH_DES_CBC_SHA"}),
    TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA(22, ProtocolEnum.SSLv3, new String[]{"SSL_DHE_RSA_WITH_3DES_EDE_CBC_SHA", "TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA"}),
    TLS_DH_anon_EXPORT_WITH_RC4_40_MD5(23, ProtocolEnum.SSLv3, new String[]{"SSL_DH_anon_EXPORT_WITH_RC4_40_MD5", "TLS_DH_anon_EXPORT_WITH_RC4_40_MD5"}),
    TLS_DH_anon_WITH_RC4_128_MD5(24, ProtocolEnum.SSLv3, new String[]{"SSL_DH_anon_WITH_RC4_128_MD5", "TLS_DH_anon_WITH_RC4_128_MD5"}),
    TLS_DH_anon_EXPORT_WITH_DES40_CBC_SHA(25, ProtocolEnum.SSLv3, new String[]{"SSL_DH_anon_EXPORT_WITH_DES40_CBC_SHA", "TLS_DH_anon_EXPORT_WITH_DES40_CBC_SHA"}),
    TLS_DH_anon_WITH_DES_CBC_SHA(26, ProtocolEnum.SSLv3, new String[]{"SSL_DH_anon_WITH_DES_CBC_SHA", "TLS_DH_anon_WITH_DES_CBC_SHA"}),
    TLS_DH_anon_WITH_3DES_EDE_CBC_SHA(27, ProtocolEnum.SSLv3, new String[]{"SSL_DH_anon_WITH_3DES_EDE_CBC_SHA", "TLS_DH_anon_WITH_3DES_EDE_CBC_SHA"}),
    TLS_PSK_WITH_NULL_SHA(44, ProtocolEnum.SSLv3, new String[]{"TLS_PSK_WITH_NULL_SHA"}),
    TLS_DHE_PSK_WITH_NULL_SHA(45, ProtocolEnum.SSLv3, new String[]{"TLS_DHE_PSK_WITH_NULL_SHA"}),
    TLS_RSA_PSK_WITH_NULL_SHA(46, ProtocolEnum.SSLv3, new String[]{"TLS_RSA_PSK_WITH_NULL_SHA"}),
    TLS_RSA_WITH_AES_128_CBC_SHA(47, ProtocolEnum.SSLv3, new String[]{"TLS_RSA_WITH_AES_128_CBC_SHA"}),
    TLS_DH_DSS_WITH_AES_128_CBC_SHA(48, ProtocolEnum.SSLv3, new String[]{"TLS_DH_DSS_WITH_AES_128_CBC_SHA"}),
    TLS_DH_RSA_WITH_AES_128_CBC_SHA(49, ProtocolEnum.SSLv3, new String[]{"TLS_DH_RSA_WITH_AES_128_CBC_SHA"}),
    TLS_DHE_DSS_WITH_AES_128_CBC_SHA(50, ProtocolEnum.SSLv3, new String[]{"TLS_DHE_DSS_WITH_AES_128_CBC_SHA"}),
    TLS_DHE_RSA_WITH_AES_128_CBC_SHA(51, ProtocolEnum.SSLv3, new String[]{"TLS_DHE_RSA_WITH_AES_128_CBC_SHA"}),
    TLS_DH_anon_WITH_AES_128_CBC_SHA(52, ProtocolEnum.SSLv3, new String[]{"TLS_DH_anon_WITH_AES_128_CBC_SHA"}),
    TLS_RSA_WITH_AES_256_CBC_SHA(53, ProtocolEnum.SSLv3, new String[]{"TLS_RSA_WITH_AES_256_CBC_SHA"}),
    TLS_DH_DSS_WITH_AES_256_CBC_SHA(54, ProtocolEnum.SSLv3, new String[]{"TLS_DH_DSS_WITH_AES_256_CBC_SHA"}),
    TLS_DH_RSA_WITH_AES_256_CBC_SHA(55, ProtocolEnum.SSLv3, new String[]{"TLS_DH_RSA_WITH_AES_256_CBC_SHA"}),
    TLS_DHE_DSS_WITH_AES_256_CBC_SHA(56, ProtocolEnum.SSLv3, new String[]{"TLS_DHE_DSS_WITH_AES_256_CBC_SHA"}),
    TLS_DHE_RSA_WITH_AES_256_CBC_SHA(57, ProtocolEnum.SSLv3, new String[]{"TLS_DHE_RSA_WITH_AES_256_CBC_SHA"}),
    TLS_DH_anon_WITH_AES_256_CBC_SHA(58, ProtocolEnum.SSLv3, new String[]{"TLS_DH_anon_WITH_AES_256_CBC_SHA"}),
    TLS_RSA_WITH_NULL_SHA256(59, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_WITH_NULL_SHA256"}),
    TLS_RSA_WITH_AES_128_CBC_SHA256(60, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_WITH_AES_128_CBC_SHA256"}),
    TLS_RSA_WITH_AES_256_CBC_SHA256(61, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_WITH_AES_256_CBC_SHA256"}),
    TLS_DH_DSS_WITH_AES_128_CBC_SHA256(62, ProtocolEnum.TLSv12, new String[]{"TLS_DH_DSS_WITH_AES_128_CBC_SHA256"}),
    TLS_DH_RSA_WITH_AES_128_CBC_SHA256(63, ProtocolEnum.TLSv12, new String[]{"TLS_DH_RSA_WITH_AES_128_CBC_SHA256"}),
    TLS_DHE_DSS_WITH_AES_128_CBC_SHA256(64, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_DSS_WITH_AES_128_CBC_SHA256"}),
    TLS_RSA_WITH_CAMELLIA_128_CBC_SHA(65, ProtocolEnum.SSLv3, new String[]{"TLS_RSA_WITH_CAMELLIA_128_CBC_SHA"}),
    TLS_DH_DSS_WITH_CAMELLIA_128_CBC_SHA(66, ProtocolEnum.SSLv3, new String[]{"TLS_DH_DSS_WITH_CAMELLIA_128_CBC_SHA"}),
    TLS_DH_RSA_WITH_CAMELLIA_128_CBC_SHA(67, ProtocolEnum.SSLv3, new String[]{"TLS_DH_RSA_WITH_CAMELLIA_128_CBC_SHA"}),
    TLS_DHE_DSS_WITH_CAMELLIA_128_CBC_SHA(68, ProtocolEnum.SSLv3, new String[]{"TLS_DHE_DSS_WITH_CAMELLIA_128_CBC_SHA"}),
    TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA(69, ProtocolEnum.SSLv3, new String[]{"TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA"}),
    TLS_DH_anon_WITH_CAMELLIA_128_CBC_SHA(70, ProtocolEnum.SSLv3, new String[]{"TLS_DH_anon_WITH_CAMELLIA_128_CBC_SHA"}),
    TLS_RSA_EXPORT1024_WITH_RC4_56_MD5(96, ProtocolEnum.TLSv10, new String[]{"SSL_RSA_EXPORT1024_WITH_RC4_56_MD5", "TLS_RSA_EXPORT1024_WITH_RC4_56_MD5"}),
    TLS_RSA_EXPORT1024_WITH_RC2_CBC_56_MD5(97, ProtocolEnum.TLSv10, new String[]{"SSL_RSA_EXPORT1024_WITH_RC2_CBC_56_MD5", "TLS_RSA_EXPORT1024_WITH_RC2_CBC_56_MD5"}),
    TLS_RSA_EXPORT1024_WITH_DES_CBC_SHA(98, ProtocolEnum.TLSv10, new String[]{"SSL_RSA_EXPORT1024_WITH_DES_CBC_SHA", "TLS_RSA_EXPORT1024_WITH_DES_CBC_SHA"}),
    TLS_DHE_DSS_EXPORT1024_WITH_DES_CBC_SHA(99, ProtocolEnum.TLSv10, new String[]{"SSL_DHE_DSS_EXPORT1024_WITH_DES_CBC_SHA", "TLS_DHE_DSS_EXPORT1024_WITH_DES_CBC_SHA"}),
    TLS_RSA_EXPORT1024_WITH_RC4_56_SHA(100, ProtocolEnum.TLSv10, new String[]{"SSL_RSA_EXPORT1024_WITH_RC4_56_SHA", "TLS_RSA_EXPORT1024_WITH_RC4_56_SHA"}),
    TLS_DHE_DSS_EXPORT1024_WITH_RC4_56_SHA(101, ProtocolEnum.TLSv10, new String[]{"SSL_DHE_DSS_EXPORT1024_WITH_RC4_56_SHA", "TLS_DHE_DSS_EXPORT1024_WITH_RC4_56_SHA"}),
    TLS_DHE_DSS_WITH_RC4_128_SHA(102, ProtocolEnum.TLSv10, new String[]{"SSL_DHE_DSS_WITH_RC4_128_SHA", "TLS_DHE_DSS_WITH_RC4_128_SHA"}),
    TLS_DHE_RSA_WITH_AES_128_CBC_SHA256(103, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_RSA_WITH_AES_128_CBC_SHA256"}),
    TLS_DH_DSS_WITH_AES_256_CBC_SHA256(104, ProtocolEnum.TLSv12, new String[]{"TLS_DH_DSS_WITH_AES_256_CBC_SHA256"}),
    TLS_DH_RSA_WITH_AES_256_CBC_SHA256(105, ProtocolEnum.TLSv12, new String[]{"TLS_DH_RSA_WITH_AES_256_CBC_SHA256"}),
    TLS_DHE_DSS_WITH_AES_256_CBC_SHA256(106, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_DSS_WITH_AES_256_CBC_SHA256"}),
    TLS_DHE_RSA_WITH_AES_256_CBC_SHA256(107, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_RSA_WITH_AES_256_CBC_SHA256"}),
    TLS_DH_anon_WITH_AES_128_CBC_SHA256(108, ProtocolEnum.TLSv12, new String[]{"TLS_DH_anon_WITH_AES_128_CBC_SHA256"}),
    TLS_DH_anon_WITH_AES_256_CBC_SHA256(109, ProtocolEnum.TLSv12, new String[]{"TLS_DH_anon_WITH_AES_256_CBC_SHA256"}),
    TLS_RSA_WITH_CAMELLIA_256_CBC_SHA(132, ProtocolEnum.SSLv3, new String[]{"TLS_RSA_WITH_CAMELLIA_256_CBC_SHA"}),
    TLS_DH_DSS_WITH_CAMELLIA_256_CBC_SHA(133, ProtocolEnum.SSLv3, new String[]{"TLS_DH_DSS_WITH_CAMELLIA_256_CBC_SHA"}),
    TLS_DH_RSA_WITH_CAMELLIA_256_CBC_SHA(134, ProtocolEnum.SSLv3, new String[]{"TLS_DH_RSA_WITH_CAMELLIA_256_CBC_SHA"}),
    TLS_DHE_DSS_WITH_CAMELLIA_256_CBC_SHA(135, ProtocolEnum.SSLv3, new String[]{"TLS_DHE_DSS_WITH_CAMELLIA_256_CBC_SHA"}),
    TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA(136, ProtocolEnum.SSLv3, new String[]{"TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA"}),
    TLS_DH_anon_WITH_CAMELLIA_256_CBC_SHA(137, ProtocolEnum.SSLv3, new String[]{"TLS_DH_anon_WITH_CAMELLIA_256_CBC_SHA"}),
    TLS_PSK_WITH_RC4_128_SHA(138, ProtocolEnum.SSLv3, new String[]{"TLS_PSK_WITH_RC4_128_SHA"}),
    TLS_PSK_WITH_3DES_EDE_CBC_SHA(139, ProtocolEnum.SSLv3, new String[]{"TLS_PSK_WITH_3DES_EDE_CBC_SHA"}),
    TLS_PSK_WITH_AES_128_CBC_SHA(140, ProtocolEnum.SSLv3, new String[]{"TLS_PSK_WITH_AES_128_CBC_SHA"}),
    TLS_PSK_WITH_AES_256_CBC_SHA(141, ProtocolEnum.SSLv3, new String[]{"TLS_PSK_WITH_AES_256_CBC_SHA"}),
    TLS_DHE_PSK_WITH_RC4_128_SHA(142, ProtocolEnum.SSLv3, new String[]{"TLS_DHE_PSK_WITH_RC4_128_SHA"}),
    TLS_DHE_PSK_WITH_3DES_EDE_CBC_SHA(143, ProtocolEnum.SSLv3, new String[]{"TLS_DHE_PSK_WITH_3DES_EDE_CBC_SHA"}),
    TLS_DHE_PSK_WITH_AES_128_CBC_SHA(144, ProtocolEnum.SSLv3, new String[]{"TLS_DHE_PSK_WITH_AES_128_CBC_SHA"}),
    TLS_DHE_PSK_WITH_AES_256_CBC_SHA(145, ProtocolEnum.SSLv3, new String[]{"TLS_DHE_PSK_WITH_AES_256_CBC_SHA"}),
    TLS_RSA_PSK_WITH_RC4_128_SHA(146, ProtocolEnum.SSLv3, new String[]{"TLS_RSA_PSK_WITH_RC4_128_SHA"}),
    TLS_RSA_PSK_WITH_3DES_EDE_CBC_SHA(147, ProtocolEnum.SSLv3, new String[]{"TLS_RSA_PSK_WITH_3DES_EDE_CBC_SHA"}),
    TLS_RSA_PSK_WITH_AES_128_CBC_SHA(148, ProtocolEnum.SSLv3, new String[]{"TLS_RSA_PSK_WITH_AES_128_CBC_SHA"}),
    TLS_RSA_PSK_WITH_AES_256_CBC_SHA(149, ProtocolEnum.SSLv3, new String[]{"TLS_RSA_PSK_WITH_AES_256_CBC_SHA"}),
    TLS_RSA_WITH_SEED_CBC_SHA(150, ProtocolEnum.SSLv3, new String[]{"TLS_RSA_WITH_SEED_CBC_SHA"}),
    TLS_DH_DSS_WITH_SEED_CBC_SHA(151, ProtocolEnum.SSLv3, new String[]{"TLS_DH_DSS_WITH_SEED_CBC_SHA"}),
    TLS_DH_RSA_WITH_SEED_CBC_SHA(152, ProtocolEnum.SSLv3, new String[]{"TLS_DH_RSA_WITH_SEED_CBC_SHA"}),
    TLS_DHE_DSS_WITH_SEED_CBC_SHA(153, ProtocolEnum.SSLv3, new String[]{"TLS_DHE_DSS_WITH_SEED_CBC_SHA"}),
    TLS_DHE_RSA_WITH_SEED_CBC_SHA(154, ProtocolEnum.SSLv3, new String[]{"TLS_DHE_RSA_WITH_SEED_CBC_SHA"}),
    TLS_DH_anon_WITH_SEED_CBC_SHA(155, ProtocolEnum.SSLv3, new String[]{"TLS_DH_anon_WITH_SEED_CBC_SHA"}),
    TLS_RSA_WITH_AES_128_GCM_SHA256(156, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_WITH_AES_128_GCM_SHA256"}),
    TLS_RSA_WITH_AES_256_GCM_SHA384(157, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_WITH_AES_256_GCM_SHA384"}),
    TLS_DHE_RSA_WITH_AES_128_GCM_SHA256(158, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_RSA_WITH_AES_128_GCM_SHA256"}),
    TLS_DHE_RSA_WITH_AES_256_GCM_SHA384(159, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_RSA_WITH_AES_256_GCM_SHA384"}),
    TLS_DH_RSA_WITH_AES_128_GCM_SHA256(160, ProtocolEnum.TLSv12, new String[]{"TLS_DH_RSA_WITH_AES_128_GCM_SHA256"}),
    TLS_DH_RSA_WITH_AES_256_GCM_SHA384(161, ProtocolEnum.TLSv12, new String[]{"TLS_DH_RSA_WITH_AES_256_GCM_SHA384"}),
    TLS_DHE_DSS_WITH_AES_128_GCM_SHA256(162, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_DSS_WITH_AES_128_GCM_SHA256"}),
    TLS_DHE_DSS_WITH_AES_256_GCM_SHA384(163, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_DSS_WITH_AES_256_GCM_SHA384"}),
    TLS_DH_DSS_WITH_AES_128_GCM_SHA256(164, ProtocolEnum.TLSv12, new String[]{"TLS_DH_DSS_WITH_AES_128_GCM_SHA256"}),
    TLS_DH_DSS_WITH_AES_256_GCM_SHA384(165, ProtocolEnum.TLSv12, new String[]{"TLS_DH_DSS_WITH_AES_256_GCM_SHA384"}),
    TLS_DH_anon_WITH_AES_128_GCM_SHA256(166, ProtocolEnum.TLSv12, new String[]{"TLS_DH_anon_WITH_AES_128_GCM_SHA256"}),
    TLS_DH_anon_WITH_AES_256_GCM_SHA384(167, ProtocolEnum.TLSv12, new String[]{"TLS_DH_anon_WITH_AES_256_GCM_SHA384"}),
    TLS_PSK_WITH_AES_128_GCM_SHA256(168, ProtocolEnum.TLSv12, new String[]{"TLS_PSK_WITH_AES_128_GCM_SHA256"}),
    TLS_PSK_WITH_AES_256_GCM_SHA384(169, ProtocolEnum.TLSv12, new String[]{"TLS_PSK_WITH_AES_256_GCM_SHA384"}),
    TLS_DHE_PSK_WITH_AES_128_GCM_SHA256(170, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_PSK_WITH_AES_128_GCM_SHA256"}),
    TLS_DHE_PSK_WITH_AES_256_GCM_SHA384(171, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_PSK_WITH_AES_256_GCM_SHA384"}),
    TLS_RSA_PSK_WITH_AES_128_GCM_SHA256(172, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_PSK_WITH_AES_128_GCM_SHA256"}),
    TLS_RSA_PSK_WITH_AES_256_GCM_SHA384(173, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_PSK_WITH_AES_256_GCM_SHA384"}),
    TLS_PSK_WITH_AES_128_CBC_SHA256(174, ProtocolEnum.TLSv10, new String[]{"TLS_PSK_WITH_AES_128_CBC_SHA256"}),
    TLS_PSK_WITH_AES_256_CBC_SHA384(175, ProtocolEnum.TLSv10, new String[]{"TLS_PSK_WITH_AES_256_CBC_SHA384"}),
    TLS_PSK_WITH_NULL_SHA256(176, ProtocolEnum.TLSv10, new String[]{"TLS_PSK_WITH_NULL_SHA256"}),
    TLS_PSK_WITH_NULL_SHA384(177, ProtocolEnum.TLSv10, new String[]{"TLS_PSK_WITH_NULL_SHA384"}),
    TLS_DHE_PSK_WITH_AES_128_CBC_SHA256(178, ProtocolEnum.TLSv10, new String[]{"TLS_DHE_PSK_WITH_AES_128_CBC_SHA256"}),
    TLS_DHE_PSK_WITH_AES_256_CBC_SHA384(179, ProtocolEnum.TLSv10, new String[]{"TLS_DHE_PSK_WITH_AES_256_CBC_SHA384"}),
    TLS_DHE_PSK_WITH_NULL_SHA256(180, ProtocolEnum.TLSv10, new String[]{"TLS_DHE_PSK_WITH_NULL_SHA256"}),
    TLS_DHE_PSK_WITH_NULL_SHA384(181, ProtocolEnum.TLSv10, new String[]{"TLS_DHE_PSK_WITH_NULL_SHA384"}),
    TLS_RSA_PSK_WITH_AES_128_CBC_SHA256(182, ProtocolEnum.TLSv10, new String[]{"TLS_RSA_PSK_WITH_AES_128_CBC_SHA256"}),
    TLS_RSA_PSK_WITH_AES_256_CBC_SHA384(183, ProtocolEnum.TLSv10, new String[]{"TLS_RSA_PSK_WITH_AES_256_CBC_SHA384"}),
    TLS_RSA_PSK_WITH_NULL_SHA256(184, ProtocolEnum.TLSv10, new String[]{"TLS_RSA_PSK_WITH_NULL_SHA256"}),
    TLS_RSA_PSK_WITH_NULL_SHA384(185, ProtocolEnum.TLSv10, new String[]{"TLS_RSA_PSK_WITH_NULL_SHA384"}),
    TLS_RSA_WITH_CAMELLIA_128_CBC_SHA256(186, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_WITH_CAMELLIA_128_CBC_SHA256"}),
    TLS_DH_DSS_WITH_CAMELLIA_128_CBC_SHA256(187, ProtocolEnum.TLSv12, new String[]{"TLS_DH_DSS_WITH_CAMELLIA_128_CBC_SHA256"}),
    TLS_DH_RSA_WITH_CAMELLIA_128_CBC_SHA256(188, ProtocolEnum.TLSv12, new String[]{"TLS_DH_RSA_WITH_CAMELLIA_128_CBC_SHA256"}),
    TLS_DHE_DSS_WITH_CAMELLIA_128_CBC_SHA256(189, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_DSS_WITH_CAMELLIA_128_CBC_SHA256"}),
    TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA256(190, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA256"}),
    TLS_DH_anon_WITH_CAMELLIA_128_CBC_SHA256(191, ProtocolEnum.TLSv12, new String[]{"TLS_DH_anon_WITH_CAMELLIA_128_CBC_SHA256"}),
    TLS_RSA_WITH_CAMELLIA_256_CBC_SHA256(192, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_WITH_CAMELLIA_256_CBC_SHA256"}),
    TLS_DH_DSS_WITH_CAMELLIA_256_CBC_SHA256(193, ProtocolEnum.TLSv12, new String[]{"TLS_DH_DSS_WITH_CAMELLIA_256_CBC_SHA256"}),
    TLS_DH_RSA_WITH_CAMELLIA_256_CBC_SHA256(194, ProtocolEnum.TLSv12, new String[]{"TLS_DH_RSA_WITH_CAMELLIA_256_CBC_SHA256"}),
    TLS_DHE_DSS_WITH_CAMELLIA_256_CBC_SHA256(195, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_DSS_WITH_CAMELLIA_256_CBC_SHA256"}),
    TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA256(196, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA256"}),
    TLS_DH_anon_WITH_CAMELLIA_256_CBC_SHA256(197, ProtocolEnum.TLSv12, new String[]{"TLS_DH_anon_WITH_CAMELLIA_256_CBC_SHA256"}),
    TLS_AES_128_GCM_SHA256(4865, ProtocolEnum.TLSv13, new String[]{"TLS_AES_128_GCM_SHA256"}),
    TLS_AES_256_GCM_SHA384(4866, ProtocolEnum.TLSv13, new String[]{"TLS_AES_256_GCM_SHA384"}),
    TLS_CHACHA20_POLY1305_SHA256(4867, ProtocolEnum.TLSv13, new String[]{"TLS_CHACHA20_POLY1305_SHA256"}),
    TLS_AES_128_CCM_SHA256(4868, ProtocolEnum.TLSv13, new String[]{"TLS_AES_128_CCM_SHA256"}),
    TLS_AES_128_CCM_8_SHA256(4869, ProtocolEnum.TLSv13, new String[]{"TLS_AES_128_CCM_8_SHA256"}),
    TLS_ECDH_ECDSA_WITH_NULL_SHA(49153, ProtocolEnum.SSLv3, new String[]{"TLS_ECDH_ECDSA_WITH_NULL_SHA"}),
    TLS_ECDH_ECDSA_WITH_RC4_128_SHA(49154, ProtocolEnum.TLSv10, new String[]{"TLS_ECDH_ECDSA_WITH_RC4_128_SHA"}),
    TLS_ECDH_ECDSA_WITH_3DES_EDE_CBC_SHA(49155, ProtocolEnum.SSLv3, new String[]{"TLS_ECDH_ECDSA_WITH_3DES_EDE_CBC_SHA"}),
    TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA(49156, ProtocolEnum.SSLv3, new String[]{"TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA"}),
    TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA(49157, ProtocolEnum.SSLv3, new String[]{"TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA"}),
    TLS_ECDHE_ECDSA_WITH_NULL_SHA(49158, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_ECDSA_WITH_NULL_SHA"}),
    TLS_ECDHE_ECDSA_WITH_RC4_128_SHA(49159, ProtocolEnum.SSLv3, new String[]{"TLS_ECDHE_ECDSA_WITH_RC4_128_SHA"}),
    TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA(49160, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA"}),
    TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA(49161, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA"}),
    TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA(49162, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA"}),
    TLS_ECDH_RSA_WITH_NULL_SHA(49163, ProtocolEnum.SSLv3, new String[]{"TLS_ECDH_RSA_WITH_NULL_SHA"}),
    TLS_ECDH_RSA_WITH_RC4_128_SHA(49164, ProtocolEnum.SSLv3, new String[]{"TLS_ECDH_RSA_WITH_RC4_128_SHA"}),
    TLS_ECDH_RSA_WITH_3DES_EDE_CBC_SHA(49165, ProtocolEnum.SSLv3, new String[]{"TLS_ECDH_RSA_WITH_3DES_EDE_CBC_SHA"}),
    TLS_ECDH_RSA_WITH_AES_128_CBC_SHA(49166, ProtocolEnum.SSLv3, new String[]{"TLS_ECDH_RSA_WITH_AES_128_CBC_SHA"}),
    TLS_ECDH_RSA_WITH_AES_256_CBC_SHA(49167, ProtocolEnum.SSLv3, new String[]{"TLS_ECDH_RSA_WITH_AES_256_CBC_SHA"}),
    TLS_ECDHE_RSA_WITH_NULL_SHA(49168, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_RSA_WITH_NULL_SHA"}),
    TLS_ECDHE_RSA_WITH_RC4_128_SHA(49169, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_RSA_WITH_RC4_128_SHA"}),
    TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA(49170, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA"}),
    TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA(49171, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA"}),
    TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA(49172, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA"}),
    TLS_ECDH_anon_WITH_NULL_SHA(49173, ProtocolEnum.TLSv10, new String[]{"TLS_ECDH_anon_WITH_NULL_SHA"}),
    TLS_ECDH_anon_WITH_RC4_128_SHA(49174, ProtocolEnum.TLSv10, new String[]{"TLS_ECDH_anon_WITH_RC4_128_SHA"}),
    TLS_ECDH_anon_WITH_3DES_EDE_CBC_SHA(49175, ProtocolEnum.TLSv10, new String[]{"TLS_ECDH_anon_WITH_3DES_EDE_CBC_SHA"}),
    TLS_ECDH_anon_WITH_AES_128_CBC_SHA(49176, ProtocolEnum.TLSv10, new String[]{"TLS_ECDH_anon_WITH_AES_128_CBC_SHA"}),
    TLS_ECDH_anon_WITH_AES_256_CBC_SHA(49177, ProtocolEnum.TLSv10, new String[]{"TLS_ECDH_anon_WITH_AES_256_CBC_SHA"}),
    TLS_SRP_SHA_WITH_3DES_EDE_CBC_SHA(49178, ProtocolEnum.SSLv3, new String[]{"TLS_SRP_SHA_WITH_3DES_EDE_CBC_SHA"}),
    TLS_SRP_SHA_RSA_WITH_3DES_EDE_CBC_SHA(49179, ProtocolEnum.SSLv3, new String[]{"TLS_SRP_SHA_RSA_WITH_3DES_EDE_CBC_SHA"}),
    TLS_SRP_SHA_DSS_WITH_3DES_EDE_CBC_SHA(49180, ProtocolEnum.SSLv3, new String[]{"TLS_SRP_SHA_DSS_WITH_3DES_EDE_CBC_SHA"}),
    TLS_SRP_SHA_WITH_AES_128_CBC_SHA(49181, ProtocolEnum.SSLv3, new String[]{"TLS_SRP_SHA_WITH_AES_128_CBC_SHA"}),
    TLS_SRP_SHA_RSA_WITH_AES_128_CBC_SHA(49182, ProtocolEnum.SSLv3, new String[]{"TLS_SRP_SHA_RSA_WITH_AES_128_CBC_SHA"}),
    TLS_SRP_SHA_DSS_WITH_AES_128_CBC_SHA(49183, ProtocolEnum.SSLv3, new String[]{"TLS_SRP_SHA_DSS_WITH_AES_128_CBC_SHA"}),
    TLS_SRP_SHA_WITH_AES_256_CBC_SHA(49184, ProtocolEnum.SSLv3, new String[]{"TLS_SRP_SHA_WITH_AES_256_CBC_SHA"}),
    TLS_SRP_SHA_RSA_WITH_AES_256_CBC_SHA(49185, ProtocolEnum.SSLv3, new String[]{"TLS_SRP_SHA_RSA_WITH_AES_256_CBC_SHA"}),
    TLS_SRP_SHA_DSS_WITH_AES_256_CBC_SHA(49186, ProtocolEnum.SSLv3, new String[]{"TLS_SRP_SHA_DSS_WITH_AES_256_CBC_SHA"}),
    TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256(49187, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256"}),
    TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384(49188, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384"}),
    TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA256(49189, ProtocolEnum.TLSv12, new String[]{"TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA256"}),
    TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA384(49190, ProtocolEnum.TLSv12, new String[]{"TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA384"}),
    TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256(49191, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256"}),
    TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384(49192, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384"}),
    TLS_ECDH_RSA_WITH_AES_128_CBC_SHA256(49193, ProtocolEnum.TLSv12, new String[]{"TLS_ECDH_RSA_WITH_AES_128_CBC_SHA256"}),
    TLS_ECDH_RSA_WITH_AES_256_CBC_SHA384(49194, ProtocolEnum.TLSv12, new String[]{"TLS_ECDH_RSA_WITH_AES_256_CBC_SHA384"}),
    TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256(49195, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256"}),
    TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384(49196, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384"}),
    TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256(49197, ProtocolEnum.TLSv12, new String[]{"TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256"}),
    TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384(49198, ProtocolEnum.TLSv12, new String[]{"TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384"}),
    TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256(49199, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"}),
    TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384(49200, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"}),
    TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256(49201, ProtocolEnum.TLSv12, new String[]{"TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256"}),
    TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384(49202, ProtocolEnum.TLSv12, new String[]{"TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384"}),
    TLS_ECDHE_PSK_WITH_RC4_128_SHA(49203, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_PSK_WITH_RC4_128_SHA"}),
    TLS_ECDHE_PSK_WITH_3DES_EDE_CBC_SHA(49204, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_PSK_WITH_3DES_EDE_CBC_SHA"}),
    TLS_ECDHE_PSK_WITH_AES_128_CBC_SHA(49205, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_PSK_WITH_AES_128_CBC_SHA"}),
    TLS_ECDHE_PSK_WITH_AES_256_CBC_SHA(49206, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_PSK_WITH_AES_256_CBC_SHA"}),
    TLS_ECDHE_PSK_WITH_AES_128_CBC_SHA256(49207, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_PSK_WITH_AES_128_CBC_SHA256"}),
    TLS_ECDHE_PSK_WITH_AES_256_CBC_SHA384(49208, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_PSK_WITH_AES_256_CBC_SHA384"}),
    TLS_ECDHE_PSK_WITH_NULL_SHA(49209, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_PSK_WITH_NULL_SHA"}),
    TLS_ECDHE_PSK_WITH_NULL_SHA256(49210, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_PSK_WITH_NULL_SHA256"}),
    TLS_ECDHE_PSK_WITH_NULL_SHA384(49211, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_PSK_WITH_NULL_SHA384"}),
    TLS_RSA_WITH_ARIA_128_GCM_SHA256(49232, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_WITH_ARIA_128_GCM_SHA256"}),
    TLS_RSA_WITH_ARIA_256_GCM_SHA384(49233, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_WITH_ARIA_256_GCM_SHA384"}),
    TLS_DHE_RSA_WITH_ARIA_128_GCM_SHA256(49234, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_RSA_WITH_ARIA_128_GCM_SHA256"}),
    TLS_DHE_RSA_WITH_ARIA_256_GCM_SHA384(49235, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_RSA_WITH_ARIA_256_GCM_SHA384"}),
    TLS_DHE_DSS_WITH_ARIA_128_GCM_SHA256(49238, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_DSS_WITH_ARIA_128_GCM_SHA256"}),
    TLS_DHE_DSS_WITH_ARIA_256_GCM_SHA384(49239, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_DSS_WITH_ARIA_256_GCM_SHA384"}),
    TLS_ECDHE_ECDSA_WITH_ARIA_128_GCM_SHA256(49244, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_ECDSA_WITH_ARIA_128_GCM_SHA256"}),
    TLS_ECDHE_ECDSA_WITH_ARIA_256_GCM_SHA384(49245, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_ECDSA_WITH_ARIA_256_GCM_SHA384"}),
    TLS_ECDHE_RSA_WITH_ARIA_128_GCM_SHA256(49248, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_RSA_WITH_ARIA_128_GCM_SHA256"}),
    TLS_ECDHE_RSA_WITH_ARIA_256_GCM_SHA384(49249, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_RSA_WITH_ARIA_256_GCM_SHA384"}),
    TLS_PSK_WITH_ARIA_128_GCM_SHA256(49258, ProtocolEnum.TLSv12, new String[]{"TLS_PSK_WITH_ARIA_128_GCM_SHA256"}),
    TLS_PSK_WITH_ARIA_256_GCM_SHA384(49259, ProtocolEnum.TLSv12, new String[]{"TLS_PSK_WITH_ARIA_256_GCM_SHA384"}),
    TLS_DHE_PSK_WITH_ARIA_128_GCM_SHA256(49260, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_PSK_WITH_ARIA_128_GCM_SHA256"}),
    TLS_DHE_PSK_WITH_ARIA_256_GCM_SHA384(49261, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_PSK_WITH_ARIA_256_GCM_SHA384"}),
    TLS_RSA_PSK_WITH_ARIA_128_GCM_SHA256(49262, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_PSK_WITH_ARIA_128_GCM_SHA256"}),
    TLS_RSA_PSK_WITH_ARIA_256_GCM_SHA384(49263, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_PSK_WITH_ARIA_256_GCM_SHA384"}),
    TLS_ECDHE_ECDSA_WITH_CAMELLIA_128_CBC_SHA256(49266, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_ECDSA_WITH_CAMELLIA_128_CBC_SHA256"}),
    TLS_ECDHE_ECDSA_WITH_CAMELLIA_256_CBC_SHA384(49267, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_ECDSA_WITH_CAMELLIA_256_CBC_SHA384"}),
    TLS_ECDH_ECDSA_WITH_CAMELLIA_128_CBC_SHA256(49268, ProtocolEnum.TLSv12, new String[]{"TLS_ECDH_ECDSA_WITH_CAMELLIA_128_CBC_SHA256"}),
    TLS_ECDH_ECDSA_WITH_CAMELLIA_256_CBC_SHA384(49269, ProtocolEnum.TLSv12, new String[]{"TLS_ECDH_ECDSA_WITH_CAMELLIA_256_CBC_SHA384"}),
    TLS_ECDHE_RSA_WITH_CAMELLIA_128_CBC_SHA256(49270, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_RSA_WITH_CAMELLIA_128_CBC_SHA256"}),
    TLS_ECDHE_RSA_WITH_CAMELLIA_256_CBC_SHA384(49271, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_RSA_WITH_CAMELLIA_256_CBC_SHA384"}),
    TLS_ECDH_RSA_WITH_CAMELLIA_128_CBC_SHA256(49272, ProtocolEnum.TLSv12, new String[]{"TLS_ECDH_RSA_WITH_CAMELLIA_128_CBC_SHA256"}),
    TLS_ECDH_RSA_WITH_CAMELLIA_256_CBC_SHA384(49273, ProtocolEnum.TLSv12, new String[]{"TLS_ECDH_RSA_WITH_CAMELLIA_256_CBC_SHA384"}),
    TLS_PSK_WITH_CAMELLIA_128_CBC_SHA256(49300, ProtocolEnum.TLSv10, new String[]{"TLS_PSK_WITH_CAMELLIA_128_CBC_SHA256"}),
    TLS_PSK_WITH_CAMELLIA_256_CBC_SHA384(49301, ProtocolEnum.TLSv10, new String[]{"TLS_PSK_WITH_CAMELLIA_256_CBC_SHA384"}),
    TLS_DHE_PSK_WITH_CAMELLIA_128_CBC_SHA256(49302, ProtocolEnum.TLSv10, new String[]{"TLS_DHE_PSK_WITH_CAMELLIA_128_CBC_SHA256"}),
    TLS_DHE_PSK_WITH_CAMELLIA_256_CBC_SHA384(49303, ProtocolEnum.TLSv10, new String[]{"TLS_DHE_PSK_WITH_CAMELLIA_256_CBC_SHA384"}),
    TLS_RSA_PSK_WITH_CAMELLIA_128_CBC_SHA256(49304, ProtocolEnum.TLSv10, new String[]{"TLS_RSA_PSK_WITH_CAMELLIA_128_CBC_SHA256"}),
    TLS_RSA_PSK_WITH_CAMELLIA_256_CBC_SHA384(49305, ProtocolEnum.TLSv10, new String[]{"TLS_RSA_PSK_WITH_CAMELLIA_256_CBC_SHA384"}),
    TLS_ECDHE_PSK_WITH_CAMELLIA_128_CBC_SHA256(49306, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_PSK_WITH_CAMELLIA_128_CBC_SHA256"}),
    TLS_ECDHE_PSK_WITH_CAMELLIA_256_CBC_SHA384(49307, ProtocolEnum.TLSv10, new String[]{"TLS_ECDHE_PSK_WITH_CAMELLIA_256_CBC_SHA384"}),
    TLS_RSA_WITH_AES_128_CCM(49308, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_WITH_AES_128_CCM"}),
    TLS_RSA_WITH_AES_256_CCM(49309, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_WITH_AES_256_CCM"}),
    TLS_DHE_RSA_WITH_AES_128_CCM(49310, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_RSA_WITH_AES_128_CCM"}),
    TLS_DHE_RSA_WITH_AES_256_CCM(49311, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_RSA_WITH_AES_256_CCM"}),
    TLS_RSA_WITH_AES_128_CCM_8(49312, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_WITH_AES_128_CCM_8"}),
    TLS_RSA_WITH_AES_256_CCM_8(49313, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_WITH_AES_256_CCM_8"}),
    TLS_DHE_RSA_WITH_AES_128_CCM_8(49314, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_RSA_WITH_AES_128_CCM_8"}),
    TLS_DHE_RSA_WITH_AES_256_CCM_8(49315, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_RSA_WITH_AES_256_CCM_8"}),
    TLS_PSK_WITH_AES_128_CCM(49316, ProtocolEnum.TLSv12, new String[]{"TLS_PSK_WITH_AES_128_CCM"}),
    TLS_PSK_WITH_AES_256_CCM(49317, ProtocolEnum.TLSv12, new String[]{"TLS_PSK_WITH_AES_256_CCM"}),
    TLS_DHE_PSK_WITH_AES_128_CCM(49318, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_PSK_WITH_AES_128_CCM"}),
    TLS_DHE_PSK_WITH_AES_256_CCM(49319, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_PSK_WITH_AES_256_CCM"}),
    TLS_PSK_WITH_AES_128_CCM_8(49320, ProtocolEnum.TLSv12, new String[]{"TLS_PSK_WITH_AES_128_CCM_8"}),
    TLS_PSK_WITH_AES_256_CCM_8(49321, ProtocolEnum.TLSv12, new String[]{"TLS_PSK_WITH_AES_256_CCM_8"}),
    TLS_PSK_DHE_WITH_AES_128_CCM_8(49322, ProtocolEnum.TLSv12, new String[]{"TLS_PSK_DHE_WITH_AES_128_CCM_8"}),
    TLS_PSK_DHE_WITH_AES_256_CCM_8(49323, ProtocolEnum.TLSv12, new String[]{"TLS_PSK_DHE_WITH_AES_256_CCM_8"}),
    TLS_ECDHE_ECDSA_WITH_AES_128_CCM(49324, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_ECDSA_WITH_AES_128_CCM"}),
    TLS_ECDHE_ECDSA_WITH_AES_256_CCM(49325, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_ECDSA_WITH_AES_256_CCM"}),
    TLS_ECDHE_ECDSA_WITH_AES_128_CCM_8(49326, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_ECDSA_WITH_AES_128_CCM_8"}),
    TLS_ECDHE_ECDSA_WITH_AES_256_CCM_8(49327, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_ECDSA_WITH_AES_256_CCM_8"}),
    TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256(52392, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256"}),
    TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256(52393, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256"}),
    TLS_DHE_RSA_WITH_CHACHA20_POLY1305_SHA256(52394, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_RSA_WITH_CHACHA20_POLY1305_SHA256"}),
    TLS_PSK_WITH_CHACHA20_POLY1305_SHA256(52395, ProtocolEnum.TLSv12, new String[]{"TLS_PSK_WITH_CHACHA20_POLY1305_SHA256"}),
    TLS_ECDHE_PSK_WITH_CHACHA20_POLY1305_SHA256(52396, ProtocolEnum.TLSv12, new String[]{"TLS_ECDHE_PSK_WITH_CHACHA20_POLY1305_SHA256"}),
    TLS_DHE_PSK_WITH_CHACHA20_POLY1305_SHA256(52397, ProtocolEnum.TLSv12, new String[]{"TLS_DHE_PSK_WITH_CHACHA20_POLY1305_SHA256"}),
    TLS_RSA_PSK_WITH_CHACHA20_POLY1305_SHA256(52398, ProtocolEnum.TLSv12, new String[]{"TLS_RSA_PSK_WITH_CHACHA20_POLY1305_SHA256"}),
    SSL_CK_RC4_128_WITH_MD5(-1, ProtocolEnum.SSLv2, new String[]{"SSL_CK_RC4_128_WITH_MD5"}),
    SSL2_RC4_128_EXPORT40_WITH_MD5(-1, ProtocolEnum.SSLv2, new String[]{"SSL_RC4_128_EXPORT40_WITH_MD5", "SSL2_RC4_128_EXPORT40_WITH_MD5"}),
    SSL_CK_RC2_128_CBC_WITH_MD5(-1, ProtocolEnum.SSLv2, new String[]{"SSL_CK_RC2_128_CBC_WITH_MD5"}),
    SSL_CK_RC2_128_CBC_EXPORT40_WITH_MD5(-1, ProtocolEnum.SSLv2, new String[]{"SSL_CK_RC2_128_CBC_EXPORT40_WITH_MD5"}),
    SSL2_IDEA_128_CBC_WITH_MD5(-1, ProtocolEnum.SSLv2, new String[]{"SSL_CK_IDEA_128_CBC_WITH_MD5", "SSL2_IDEA_128_CBC_WITH_MD5"}),
    SSL2_DES_64_CBC_WITH_MD5(-1, ProtocolEnum.SSLv2, new String[]{"SSL_CK_DES_64_CBC_WITH_MD5", "SSL2_DES_64_CBC_WITH_MD5"}),
    SSL2_DES_192_EDE3_CBC_WITH_MD5(-1, ProtocolEnum.SSLv2, new String[]{"SSL_CK_DES_192_EDE3_CBC_WITH_MD5", "SSL2_DES_192_EDE3_CBC_WITH_MD5"}),

    /**
     * 国密算法套件
     */
    GMVPN_SM2_WITH_SM4_CBC_SM3(-1, ProtocolEnum.GMVPNv11, new String[]{"GMVPN_SM2_WITH_SM4_CBC_SM3"}),
    GMVPN_SM2_WITH_SM4_GCM_SM3(-1, ProtocolEnum.GMVPNv11, new String[]{"GMVPN_SM2_WITH_SM4_GCM_SM3"}),
    ;

    private final int id;
    private final ProtocolEnum protocol;
    private final Set<String> jsseNames;

    CipherEnum(int id, ProtocolEnum protocol, String[] jsseNameArr) {
        this.id = id;
        this.protocol = protocol;
        this.jsseNames = new LinkedHashSet(Arrays.asList(jsseNameArr));
    }

    public int getId() {
        return id;
    }

    public ProtocolEnum getProtocol() {
        return protocol;
    }

    public Set<String> getJsseNames() {
        return jsseNames;
    }
}
