package kl.npki.base.core.constant;


import kl.nbase.i18n.i18n.EnumI18n;
import org.apache.commons.lang3.StringUtils;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

public enum RegionAndLanguageConstant  implements EnumI18n {

    CN("zh", "yyyy-MM-dd HH:mm:ss", "GMT+8", "中国", "Asia/Shanghai"),

    DZ("en", "dd/MM/yyyy HH:mm:ss", "GMT+1", "阿尔及利亚", "Africa/Algiers"),

    ;

    public String getLanguage() {
        return language;
    }



    public String getDateFormat() {
        return dateFormat;
    }



    private String language;
    private String dateFormat;
    private String gmt;
    private String timeZone;

    public String getRegionName() {
        String i18nValue = tr();
        if(StringUtils.isBlank(i18nValue) || getMessageKey().equalsIgnoreCase(i18nValue)){
            return regionName;
        }
        return i18nValue;
    }


    private String regionName;

    public String getGmt() {
        return gmt;
    }

    public String getTimeZone() {
        return timeZone;
    }

    RegionAndLanguageConstant(String language, String dateFormat, String gmt, String regionName, String timeZone) {
        this.language = language;
        this.dateFormat = dateFormat;
        this.gmt = gmt;
        this.regionName = regionName;
        this.timeZone = timeZone;
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.getClass().getSimpleName() + "." + this.name().toLowerCase();
    }

}
