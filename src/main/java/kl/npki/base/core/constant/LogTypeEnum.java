package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;
/**
 * 日志类型枚举
 *
 * <AUTHOR>
 * @date 2023/3/1
 */
public enum LogTypeEnum implements EnumI18n {

    API_LOG("apiLog", "服务日志"),
    OP_LOG("opLog", "操作日志"),

    ;
    
    private String logType;

    private String desc;

    LogTypeEnum(String logType, String desc) {
        this.logType = logType;
        this.desc = desc;
    }

    public String getLogType() {
        return logType;
    }

    public String getDesc() {
        return tr();
    }
    
    public static LogTypeEnum getLogType(String logType) {
        for (LogTypeEnum logTypeEnum : LogTypeEnum.values()) {
            if (logTypeEnum.getLogType().equalsIgnoreCase(logType)) {
                return logTypeEnum;
            }
        }
        return null;
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
