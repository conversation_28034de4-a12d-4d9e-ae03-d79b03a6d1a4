package kl.npki.base.core.constant;

import kl.nbase.security.asn1.ASN1ObjectIdentifier;
import kl.nbase.security.constants.AsymAlgoTypeEnum;
import kl.nbase.security.entity.algo.HashAlgo;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public enum SignAlgoEnum {

    /**
     * RSA签名算法
     */
    MD5WithRSA("1.2.840.113549.1.1.4", "MD5withRSA", AsymAlgoTypeEnum.RSA, HashAlgo.MD5),
    SHA1WithRSA("1.2.840.113549.1.1.5", "SHA1withRSA", AsymAlgoTypeEnum.RSA, HashAlgo.SHA1),
    SHA256WithRSA("1.2.840.113549.1.1.11", "SHA256withRSA", AsymAlgoTypeEnum.RSA, HashAlgo.SHA256),
    SHA384WithRSA("1.2.840.113549.1.1.12", "SHA384withRSA", AsymAlgoTypeEnum.RSA, HashAlgo.SHA384),
    SHA224WithRSA("1.2.840.113549.1.1.14", "SHA224withRSA", AsymAlgoTypeEnum.RSA, HashAlgo.SHA224),
    SHA512WithRSA("1.2.840.113549.1.1.13", "SHA512withRSA", AsymAlgoTypeEnum.RSA, HashAlgo.SHA512),

    /**
     * SM2签名算法
     */
    SM3WithSM2("1.2.156.10197.1.501", "SM3withSM2", AsymAlgoTypeEnum.SM2, HashAlgo.SM3),
    SHA1WithSM2("1.2.156.10197.1.502", "SHA1withSM2", AsymAlgoTypeEnum.SM2, HashAlgo.SHA1),
    SHA256WithSM2("1.2.156.10197.1.503", "SHA256withSM2", AsymAlgoTypeEnum.SM2, HashAlgo.SHA256),
    // TODO 此处存在争议，目前以国密0006规范为准
    // BC 中的 1.2.156.10197.1.504 对应的算法 SHA512WithSM2
    // 国密0006规范中的 1.2.156.10197.1.504 对应的算法 SM3WithRSA
    SM3WithRSA("1.2.156.10197.1.504", "SM3WithRSA", AsymAlgoTypeEnum.RSA, HashAlgo.SM3),
    SHA224WithSM2("1.2.156.10197.1.505", "SHA224withSM2", AsymAlgoTypeEnum.SM2, HashAlgo.SHA224),
    SHA384WithSM2("1.2.156.10197.1.506", "SHA384withSM2", AsymAlgoTypeEnum.SM2, HashAlgo.SHA384),

    /**
     * ECC签名算法
     */
    SHA1WithECDSA("1.2.840.10045.4.1", "SHA1WithECDSA", AsymAlgoTypeEnum.ECC, HashAlgo.SHA1),
    SHA224WithECDSA("1.2.840.10045.4.3.1", "SHA224WithECDSA", AsymAlgoTypeEnum.DILITHIUM, HashAlgo.SHA224),
    SHA256WithECDSA("1.2.840.10045.4.3.2", "SHA256WithECDSA", AsymAlgoTypeEnum.ECC, HashAlgo.SHA256),
    SHA384WithECDSA("1.2.840.10045.4.3.3", "SHA384WithECDSA", AsymAlgoTypeEnum.ECC, HashAlgo.SHA384),
    SHA512WithECDSA("1.2.840.10045.4.3.4", "SHA512WithECDSA", AsymAlgoTypeEnum.ECC, HashAlgo.SHA512),

    /**
     * 后量子签名算法-DILITHIUM，由于后量子签名算法中的摘要算法为特殊算法，且实际计算过程中并不需要传递给KJCC组件，故均设置为SHA1
     */
    DILITHIUM2("*******.4.1.2.267.7.4.4", "DILITHIUM2", AsymAlgoTypeEnum.DILITHIUM, HashAlgo.SHA1),
    DILITHIUM2_AES("*******.4.1.2.267.11.4.4", "DILITHIUM2_AES", AsymAlgoTypeEnum.DILITHIUM, HashAlgo.SHA1),
    DILITHIUM3("*******.4.1.2.267.7.6.5", "DILITHIUM3", AsymAlgoTypeEnum.DILITHIUM, HashAlgo.SHA1),
    DILITHIUM3_AES("*******.4.1.2.267.11.6.5", "DILITHIUM3_AES", AsymAlgoTypeEnum.DILITHIUM, HashAlgo.SHA1),
    DILITHIUM5("*******.4.1.2.267.7.8.7", "DILITHIUM5", AsymAlgoTypeEnum.DILITHIUM, HashAlgo.SHA1),
    DILITHIUM5_AES("*******.4.1.2.267.11.8.7", "DILITHIUM5_AES", AsymAlgoTypeEnum.DILITHIUM, HashAlgo.SHA1),
    /**
     * 后量子签名算法-FALCON，由于后量子签名算法中的摘要算法为特殊算法，且实际计算过程中并不需要传递给KJCC组件，故均设置为SHA1
     */
    FALCON_512("1.3.9999.3.12", "FALCON_512", AsymAlgoTypeEnum.FALCON, HashAlgo.SHA1),
    FALCON_1024("1.3.9999.3.15", "FALCON_1024", AsymAlgoTypeEnum.FALCON, HashAlgo.SHA1),
    /**
     * 后量子签名算法-SPHINCSPLUS，由于后量子签名算法中的摘要算法为特殊算法，且实际计算过程中并不需要传递给KJCC组件，故均设置为SHA1
     */
    SPHINCSPLUS_SHA2_128F("*******.4.1.22554.2.5.2", "SPHINCSPLUS_SHA2_128F", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHA2_128S("*******.4.1.22554.2.5.1", "SPHINCSPLUS_SHA2_128S", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHA2_192F("*******.4.1.22554.2.5.8", "SPHINCSPLUS_SHA2_192F", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHA2_192S("*******.4.1.22554.2.5.7", "SPHINCSPLUS_SHA2_192S", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHA2_256F("*******.4.1.22554.2.5.14", "SPHINCSPLUS_SHA2_256F", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHA2_256S("*******.4.1.22554.2.5.13", "SPHINCSPLUS_SHA2_256S", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHA2_128F_SIMPLE("*******.4.1.22554.2.5.20", "SPHINCSPLUS_SHA2_128F_SIMPLE", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHA2_128S_SIMPLE("*******.4.1.22554.2.5.19", "SPHINCSPLUS_SHA2_128S_SIMPLE", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHA2_192F_SIMPLE("*******.4.1.22554.2.5.26", "SPHINCSPLUS_SHA2_192F_SIMPLE", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHA2_192S_SIMPLE("*******.4.1.22554.2.5.25", "SPHINCSPLUS_SHA2_192S_SIMPLE", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHA2_256F_SIMPLE("*******.4.1.22554.2.5.32", "SPHINCSPLUS_SHA2_256F_SIMPLE", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHA2_256S_SIMPLE("*******.4.1.22554.2.5.31", "SPHINCSPLUS_SHA2_256S_SIMPLE", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHAKE_128F("*******.4.1.22554.2.5.4", "SPHINCSPLUS_SHAKE_128F", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHAKE_128S("*******.4.1.22554.2.5.3", "SPHINCSPLUS_SHAKE_128S", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHAKE_192F("*******.4.1.22554.2.5.10", "SPHINCSPLUS_SHAKE_192F", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHAKE_192S("*******.4.1.22554.2.5.9", "SPHINCSPLUS_SHAKE_192S", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHAKE_256F("*******.4.1.22554.2.5.16", "SPHINCSPLUS_SHAKE_256F", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHAKE_256S("*******.4.1.22554.2.5.15", "SPHINCSPLUS_SHAKE_256S", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHAKE_128F_SIMPLE("*******.4.1.22554.2.5.22", "SPHINCSPLUS_SHAKE_128F_SIMPLE", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHAKE_128S_SIMPLE("*******.4.1.22554.2.5.21", "SPHINCSPLUS_SHAKE_128S_SIMPLE", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHAKE_192F_SIMPLE("*******.4.1.22554.2.5.28", "SPHINCSPLUS_SHAKE_192F_SIMPLE", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHAKE_192S_SIMPLE("*******.4.1.22554.2.5.27", "SPHINCSPLUS_SHAKE_192S_SIMPLE", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHAKE_256F_SIMPLE("*******.4.1.22554.2.5.34", "SPHINCSPLUS_SHAKE_256F_SIMPLE", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_SHAKE_256S_SIMPLE("*******.4.1.22554.2.5.33", "SPHINCSPLUS_SHAKE_256S_SIMPLE", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_HARAKA_128F("*******.4.1.22554.2.5.6", "SPHINCSPLUS_HARAKA_128F", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_HARAKA_128S("*******.4.1.22554.2.5.5", "SPHINCSPLUS_HARAKA_128S", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_HARAKA_192F("*******.4.1.22554.2.5.12", "SPHINCSPLUS_HARAKA_192F", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),
    SPHINCSPLUS_HARAKA_192S("*******.4.1.22554.2.5.11", "SPHINCSPLUS_HARAKA_192S", AsymAlgoTypeEnum.SPHINCSPLUS, HashAlgo.SHA1),

    /**
     * 国产后量子签名算法
     */
    HSS_SM3_W1_H5("*******.4.1.46210.1.902.1", "HSS_SM3_W1_H5", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W1_H10("*******.4.1.46210.1.902.2", "HSS_SM3_W1_H10", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W1_H15("*******.4.1.46210.1.902.3", "HSS_SM3_W1_H15", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W1_H20("*******.4.1.46210.1.902.4", "HSS_SM3_W1_H20", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W1_H25("*******.4.1.46210.1.902.5", "HSS_SM3_W1_H25", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W2_H5("*******.4.1.46210.1.902.6", "HSS_SM3_W2_H5", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W2_H10("*******.4.1.46210.1.902.7", "HSS_SM3_W2_H10", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W2_H15("*******.4.1.46210.1.902.8", "HSS_SM3_W2_H15", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W2_H20("*******.4.1.46210.1.902.9", "HSS_SM3_W2_H20", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W2_H25("*******.4.1.46210.1.902.10", "HSS_SM3_W2_H25", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W4_H5("*******.4.1.46210.1.902.11", "HSS_SM3_W4_H5", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W4_H10("*******.4.1.46210.1.902.12", "HSS_SM3_W4_H10", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W4_H15("*******.4.1.46210.1.902.13", "HSS_SM3_W4_H15", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W4_H20("*******.4.1.46210.1.902.14", "HSS_SM3_W4_H20", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W4_H25("*******.4.1.46210.1.902.15", "HSS_SM3_W4_H25", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W8_H5("*******.4.1.46210.1.902.16", "HSS_SM3_W8_H5", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W8_H10("*******.4.1.46210.1.902.17", "HSS_SM3_W8_H10", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W8_H15("*******.4.1.46210.1.902.18", "HSS_SM3_W8_H15", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W8_H20("*******.4.1.46210.1.902.19", "HSS_SM3_W8_H20", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    HSS_SM3_W8_H25("*******.4.1.46210.1.902.20", "HSS_SM3_W8_H25", AsymAlgoTypeEnum.HSS_SM3_R1, HashAlgo.SM3),
    AIGIS_SIG1("*******.4.1.46210.1.904.1", "AIGIS_SIG1", AsymAlgoTypeEnum.AIGIS_SIG_R1, HashAlgo.SM3),
    AIGIS_SIG2("*******.4.1.46210.1.904.2", "AIGIS_SIG2", AsymAlgoTypeEnum.AIGIS_SIG_R1, HashAlgo.SM3),
    AIGIS_SIG3("*******.4.1.46210.1.904.3", "AIGIS_SIG3", AsymAlgoTypeEnum.AIGIS_SIG_R1, HashAlgo.SM3),

    /**
     * 后量子算法FIPS 204，由于后量子签名算法中的摘要算法为特殊算法，且实际计算过程中并不需要传递给KJCC组件，故均设置为SHA1
     */
    ML_DSA_44("2.16.840.*********.3.17", "ML_DSA_44", AsymAlgoTypeEnum.ML_DSA, HashAlgo.SHA1),
    ML_DSA_65("2.16.840.*********.3.18", "ML_DSA_65", AsymAlgoTypeEnum.ML_DSA, HashAlgo.SHA1),
    ML_DSA_87("2.16.840.*********.3.19", "ML_DSA_87", AsymAlgoTypeEnum.ML_DSA, HashAlgo.SHA1),
    ML_DSA_44_SHA512("2.16.840.*********.3.32", "ML_DSA_44_SHA512", AsymAlgoTypeEnum.ML_DSA, HashAlgo.SHA1),
    ML_DSA_65_SHA512("2.16.840.*********.3.33", "ML_DSA_65_SHA512", AsymAlgoTypeEnum.ML_DSA, HashAlgo.SHA1),
    ML_DSA_87_SHA512("2.16.840.*********.3.34", "ML_DSA_87_SHA512", AsymAlgoTypeEnum.ML_DSA, HashAlgo.SHA1),

    /**
     * 后量子算法FIPS 205，由于后量子签名算法中的摘要算法为特殊算法，且实际计算过程中并不需要传递给KJCC组件，故均设置为SHA1
     */
    SLH_DSA_SHA2_128S("2.16.840.*********.3.20", "SLH_DSA_SHA2_128S", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHA2_128F("2.16.840.*********.3.21", "SLH_DSA_SHA2_128F", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHA2_192S("2.16.840.*********.3.22", "SLH_DSA_SHA2_192S", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHA2_192F("2.16.840.*********.3.23", "SLH_DSA_SHA2_192F", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHA2_256S("2.16.840.*********.3.24", "SLH_DSA_SHA2_256S", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHA2_256F("2.16.840.*********.3.25", "SLH_DSA_SHA2_256F", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHAKE_128S("2.16.840.*********.3.26", "SLH_DSA_SHAKE_128S", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHAKE_128F("2.16.840.*********.3.27", "SLH_DSA_SHAKE_128F", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHAKE_192S("2.16.840.*********.3.28", "SLH_DSA_SHAKE_192S", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHAKE_192F("2.16.840.*********.3.29", "SLH_DSA_SHAKE_192F", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHAKE_256S("2.16.840.*********.3.30", "SLH_DSA_SHAKE_256S", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHAKE_256F("2.16.840.*********.3.31", "SLH_DSA_SHAKE_256F", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHA2_128S_SHA256("2.16.840.*********.3.35", "SLH_DSA_SHA2_128S_SHA256", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHA2_128F_SHA256("2.16.840.*********.3.36", "SLH_DSA_SHA2_128F_SHA256", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHA2_192S_SHA512("2.16.840.*********.3.37", "SLH_DSA_SHA2_192S_SHA512", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHA2_192F_SHA512("2.16.840.*********.3.38", "SLH_DSA_SHA2_192F_SHA512", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHA2_256S_SHA512("2.16.840.*********.3.39", "SLH_DSA_SHA2_256S_SHA512", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHA2_256F_SHA512("2.16.840.*********.3.40", "SLH_DSA_SHA2_256F_SHA512", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHAKE_128S_SHAKE128("2.16.840.*********.3.41", "SLH_DSA_SHAKE_128S_SHAKE128", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHAKE_128F_SHAKE128("2.16.840.*********.3.42", "SLH_DSA_SHAKE_128F_SHAKE128", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHAKE_192S_SHAKE256("2.16.840.*********.3.43", "SLH_DSA_SHAKE_192S_SHAKE256", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHAKE_192F_SHAKE256("2.16.840.*********.3.44", "SLH_DSA_SHAKE_192F_SHAKE256", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHAKE_256S_SHAKE256("2.16.840.*********.3.45", "SLH_DSA_SHAKE_256S_SHAKE256", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1),
    SLH_DSA_SHAKE_256F_SHAKE256("2.16.840.*********.3.46", "SLH_DSA_SHAKE_256F_SHAKE256", AsymAlgoTypeEnum.SLH_DSA, HashAlgo.SHA1);

    private static final Map<String, SignAlgoEnum> OID2_ENUM_MAP = new HashMap<>();
    private static final Map<String, SignAlgoEnum> SIGN_ALGO_NAME2_ENUM_MAP = new HashMap<>();

    static {
        for (SignAlgoEnum value : SignAlgoEnum.values()) {
            OID2_ENUM_MAP.put(value.oid, value);
            SIGN_ALGO_NAME2_ENUM_MAP.put(value.signAlgoName.toLowerCase(), value);
        }
    }

    private final String oid;
    private final String signAlgoName;
    private final AsymAlgoTypeEnum asymAlgo;
    private final HashAlgo hashAlgo;

    SignAlgoEnum(String oid, String signAlgoName, AsymAlgoTypeEnum asymAlgo, HashAlgo hashAlgo) {
        this.oid = oid;
        this.signAlgoName = signAlgoName;
        this.asymAlgo = asymAlgo;
        this.hashAlgo = hashAlgo;
    }

    public static SignAlgoEnum getEnumByOid(String oid) {
        return OID2_ENUM_MAP.get(oid);
    }

    public static ASN1ObjectIdentifier getOidByEnum(SignAlgoEnum signAlgoEnum) {
        return new ASN1ObjectIdentifier(signAlgoEnum.oid);
    }

    public static SignAlgoEnum getEnumByOid(ASN1ObjectIdentifier oid) {
        return OID2_ENUM_MAP.get(oid.getId());
    }

    public static SignAlgoEnum getEnumByAsymAlgoAndHashAlgo(AsymAlgoTypeEnum asymAlgo, HashAlgo hashAlgo) {
        return Arrays.stream(values())
            .filter(v -> v.asymAlgo.equals(asymAlgo) && v.hashAlgo.equals(hashAlgo))
            .findAny()
            .orElse(null);
    }

    public static SignAlgoEnum getEnumBySignAlgoName(String signAlgoName) {
        return SIGN_ALGO_NAME2_ENUM_MAP.get(signAlgoName.toLowerCase());
    }

    public static String getSignAlgoNameByOid(String oid) {
        SignAlgoEnum signAlgoEnum = getEnumByOid(oid);
        return signAlgoEnum == null ? null : signAlgoEnum.signAlgoName;
    }

    public static AsymAlgoTypeEnum getAsymNameByOid(String oid) {
        SignAlgoEnum signAlgoEnum = getEnumByOid(oid);
        return signAlgoEnum == null ? null : signAlgoEnum.asymAlgo;
    }

    public static AsymAlgoTypeEnum getAsymNameBySignAlgoName(String signAlgoName) {
        SignAlgoEnum signAlgoEnum = getEnumBySignAlgoName(signAlgoName);
        return signAlgoEnum == null ? null : signAlgoEnum.asymAlgo;
    }

    public static String getOidBySignAlgoName(String signAlgoName) {
        SignAlgoEnum signAlgoEnum = getEnumBySignAlgoName(signAlgoName);
        return signAlgoEnum == null ? null : signAlgoEnum.oid;
    }

    public static ASN1ObjectIdentifier getAsnOidBySignAlgoName(String signAlgoName) {
        SignAlgoEnum signAlgoEnum = getEnumBySignAlgoName(signAlgoName);
        return signAlgoEnum == null ? null : getOidByEnum(signAlgoEnum);
    }

    public String getOid() {
        return oid;
    }

    public String getSignAlgoName() {
        return signAlgoName;
    }

    public AsymAlgoTypeEnum getAsymAlgo() {
        return asymAlgo;
    }

    public HashAlgo getHashAlgo() {
        return hashAlgo;
    }
}
