package kl.npki.base.core.constant;

import kl.npki.base.core.exception.BaseInternalError;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.LOGIN_TYPE_IS_NULL_I18N_KEY;
/**
 * 多选登录模式下，证书数量要求的枚举类
 *
 * <AUTHOR>
 */
public enum MultiSignLoginCertCountEnum {
    /**
     * 单选登录
     */
    SINGLE_CHOICE(1, 1),

    /**
     * 5选3登录模式
     */
    CHOICE_3_OF_5(5, 3),

    /**
     * 3选2登录模式
     */
    CHOICE_2_OF_3(3, 2);

    /**
     * 三员最少个数
     */
    private Integer minNum;

    /**
     * 登录时所需的管理员个数
     */
    private Integer loginNum;

    MultiSignLoginCertCountEnum(Integer minNum, Integer loginNum) {
        this.minNum = minNum;
        this.loginNum = loginNum;
    }

    public Integer getMinNum() {
        return minNum;
    }

    public Integer getLoginNum() {
        return loginNum;
    }

    public static MultiSignLoginCertCountEnum valueOfLoginType(String loginType) {
        if (StringUtils.isEmpty(loginType)) {
            throw BaseInternalError.LOGIN_TYPE_CONVERSION_ERROR.toException(LOGIN_TYPE_IS_NULL_I18N_KEY);
        }
        return valueOfLoginType(LoginType.getLoginTypeByType(loginType));
    }

    public static MultiSignLoginCertCountEnum valueOfLoginType(LoginType loginType) {
        if (ObjectUtils.isEmpty(loginType)) {
            throw BaseInternalError.LOGIN_TYPE_CONVERSION_ERROR.toException(LOGIN_TYPE_IS_NULL_I18N_KEY);
        }
        switch (loginType) {
            case MULTI_SIGN_2_OF_3:
                return CHOICE_2_OF_3;
            case MULTI_SIGN_3_OF_5:
                return CHOICE_3_OF_5;
            default:
                return SINGLE_CHOICE;
        }
    }
}
