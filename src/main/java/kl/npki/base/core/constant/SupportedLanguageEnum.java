package kl.npki.base.core.constant;

import kl.npki.base.core.exception.BaseInternalError;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统支持的国际化语言
 *
 * <AUTHOR>
 * @date 2025/1/10 14:52
 */
public enum SupportedLanguageEnum {
    ZH_CH("zh_CN", "中文"),
    EN_US("en_US", "English"),
    ;

    /**
     * 对应语言的LocalString
     */
    private final String value;

    /**
     * 对应语言的翻译
     */
    private final String desc;
    // 存储支持语言的键值
    private static final Map<String, String> SUPPORTED_LANGUAGES = new HashMap<>();

    SupportedLanguageEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static void checkSupported(String value) {
        if (SUPPORTED_LANGUAGES.isEmpty()) {
            getSupportedLanguages();
        }
        if (StringUtils.isBlank(value) || !SUPPORTED_LANGUAGES.containsKey(value)) {
            throw BaseInternalError.UNSUPPORTED_LANGUAGE_ERROR.toException(value);
        }
    }

    public static Map<String, String> getSupportedLanguages() {
        if (SUPPORTED_LANGUAGES.isEmpty()) {
            SupportedLanguageEnum[] languageEnums = SupportedLanguageEnum.values();
            for (SupportedLanguageEnum languageEnum : languageEnums) {
                SUPPORTED_LANGUAGES.put(languageEnum.getValue(), languageEnum.getDesc());
            }
        }
        return Collections.unmodifiableMap(SUPPORTED_LANGUAGES);
    }
}