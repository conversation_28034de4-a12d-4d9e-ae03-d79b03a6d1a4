package kl.npki.base.core.constant;

import java.io.File;

import static kl.npki.base.core.constant.BaseConstant.WEB_ROOT;

/**
 * KeyStore常量池
 *
 * <AUTHOR>
 * @date 2023/6/29
 */
public final class KeyStoreConstants {

    private KeyStoreConstants() {

    }


    /**
     * keystore文件在系统中存放的位置
     * <pre>
     * ./WEB-INF/config/xxx.p12
     * </pre>
     */
    public static final String KEY_STORE_ABSOLUTE_DIR = WEB_ROOT + "/config";

    /**
     * Default SSL server certificate keystore filename.
     */
    public static final String DEFAULT_KEY_STORE_FILE_NAME = "ssl-site.p12";

    /**
     * Default trust store filename.
     */
    public static final String DEFAULT_TRUST_STORE_FILE_NAME = "ssl-trust.p12";

    /**
     * 默认SSL的KeyStore文件路径为config文件夹下，此处使用基于ROOT_PATH的相对路径
     */
    public static final String DEFAULT_KEY_STORE_FILE_RELATIVE_PATH = "config/" + DEFAULT_KEY_STORE_FILE_NAME;

    /**
     * 默认SSL的trustStore文件路径为config文件夹下，此处使用基于ROOT_PATH的相对路径
     */
    public static final String DEFAULT_TRUST_STORE_FILE_RELATIVE_PATH = "config/" + DEFAULT_TRUST_STORE_FILE_NAME;

    /**
     * 默认SSL的KeyStore文件路径，此处为绝对路径
     */
    public static final String DEFAULT_KEY_STORE_FILE_ABSOLUTE_PATH = WEB_ROOT + File.separator +
            DEFAULT_KEY_STORE_FILE_RELATIVE_PATH;

    /**
     * 默认SSL的trustStore文件路径，此处为绝对路径
     */
    public static final String DEFAULT_TRUST_STORE_FILE_ABSOLUTE_PATH = WEB_ROOT + File.separator +
            DEFAULT_TRUST_STORE_FILE_RELATIVE_PATH;

    /**
     * Default KeyStore type.
     */
    public static final String DEFAULT_KEYSTORE_TYPE = "PKCS12";

    /**
     * JKS
     */
    public static final String JKS_KEY_STORE_TYPE = "JKS";

    /**
     * 默认的管理根CA证书别名
     */
    public static final String DEFAULT_MANAGE_ROOT_CA_ALIAS = "manage-root-ca";

    /**
     * 默认的下级CA证书别名
     */
    public static final String DEFAULT_MANAGE_SUB_CA_ALIAS = "manage-sub-ca";

}
