package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * @Author: guoq
 * @Date: 2022/9/1
 * @description: 实体状态
 */
public enum EntityStatus implements EnumI18n {
    /**
     * 正常
     */
    NORMAL(0, "正常"),

    /**
     * 待审核
     */
    TO_BE_AUDIT(1, "待审核"),

    REVOKED(-2, "已废除"),
    /**
     * 冻结的
     */
    FREEZE(-1, "已冻结"),
    /**
     * 过期
     */
    EXPIRED(-3, "已过期"),

    UNKNOWN(-4, "未知");
    private final Integer id;
    private final String desc;

    EntityStatus(Integer id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    public static EntityStatus valueOfById(Integer id) {

        EntityStatus[] entityStatuses = EntityStatus.values();
        for (EntityStatus entityStatus : entityStatuses) {
            if (id.equals(entityStatus.getId())) {
                return entityStatus;
            }
        }

        // 默认返回
        return UNKNOWN;
    }

    public String getDesc() {
        return tr();
    }

    public Integer getId() {
        return id;
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.getClass().getSimpleName() + "." + this.name().toLowerCase();
    }
}
