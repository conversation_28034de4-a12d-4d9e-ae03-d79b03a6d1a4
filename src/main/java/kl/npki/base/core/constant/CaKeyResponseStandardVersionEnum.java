package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import java.util.LinkedHashMap;
import java.util.Map;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * CA密钥服务响应中，使用的标准规范版本枚举
 */
public enum CaKeyResponseStandardVersionEnum implements EnumI18n {
    /**
     * GBT-2023
     */
    GMT00142023(1, "GM/T 0014-2023 CA密钥服务响应封装格式"),
    /**
     * GBT-2012
     */
    GMT00142012(2, "GM/T 0014-2012 CA密钥服务响应封装格式");

    private final int code;
    private final String desc;

    CaKeyResponseStandardVersionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return tr();
    }

    public static CaKeyResponseStandardVersionEnum valueOfCode(int code) {
        for (CaKeyResponseStandardVersionEnum version : CaKeyResponseStandardVersionEnum.values()) {
            if (version.getCode() == code) {
                return version;
            }
        }
        return null;
    }

    public static Map<Integer, Object> valuesMap() {

        Map<Integer, Object> map = new LinkedHashMap<>(16);

        CaKeyResponseStandardVersionEnum[] enums = CaKeyResponseStandardVersionEnum.values();
        for (CaKeyResponseStandardVersionEnum caKeyResponseStandardVersion : enums) {
            map.put(caKeyResponseStandardVersion.getCode(), caKeyResponseStandardVersion.getDesc());
        }

        return map;
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
