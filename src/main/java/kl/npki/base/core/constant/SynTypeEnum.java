package kl.npki.base.core.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 同步类型
 *
 * <AUTHOR>
 * @date 2024/07/12
 */
public enum SynTypeEnum implements Serializable, EnumI18n {
    IAM_SYN("IAM_SYN", "IAM同步"),

    RA_SYN("RA_SYN", "RA同步"),

    UUM_SYN("UUM_SYN", "UUM同步"),

    ;

    private final String name;
    private final String desc;

    SynTypeEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }


    public static Map<String, String> getValuesMap() {
        return Arrays.stream(values()).collect(Collectors.toMap(Enum::name, SynTypeEnum::getDesc));
    }
    public String getDesc() {
        return tr();
    }

    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX +  this.name().toLowerCase() ;
    }
}
