package kl.npki.base.core.constant;

/**
 * 根证书部署模式
 *
 * <AUTHOR>
 */
public enum RootCertificateDeployModeEnum {

    SELF_SIGNED("selfSigned", "自签发"),

    ONLINE_ISSUANCE("onlineIssuance", "在线签发"),

    OFFLINE_ISSUANCE("offlineIssuance", "离线签发"),

    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;


    RootCertificateDeployModeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

}
