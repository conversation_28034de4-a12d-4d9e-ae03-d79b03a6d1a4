package kl.npki.base.core.biz.license.service;

import kl.npki.base.core.biz.license.model.LicenseInfo;
import kl.npki.base.core.configs.LicenseConfig;
import kl.security.license.core.LicenseStatus;

/**
 * <AUTHOR>
 * @since 2023/1/31
 */
public interface ILicenseService {

    /**
     * 导入License
     *
     * @param licenseConfig License配置
     */
    void importLicense(LicenseConfig licenseConfig);

    /**
     * 导入License
     *
     * @param license License原文内容
     * @return 是否成功
     */
    boolean importLicense(String license);

    /**
     * 获取License信息
     *
     * @return License信息
     */
    LicenseInfo getLicense();

    /**
     * 解析License信息
     *
     * @return License信息
     */
    LicenseInfo parseLicense(String license);

    /**
     * 获取License状态
     *
     * @return
     */
    LicenseStatus getLicenseStatus();

    /**
     * 销毁License监控等资源，Spring在关闭容器时会自动调用该方法
     */
    void shutdown();
}
