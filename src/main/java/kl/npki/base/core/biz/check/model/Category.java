package kl.npki.base.core.biz.check.model;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 服务自检项分类,定义一些通用的检测项分类
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
public enum Category implements EnumI18n {
    /**
     * 包括但不限于数据库连接状态、数据库查询性能、数据完整性检查，以及数据库备份和恢复能力的检查等。
     */
    DATABASE("01", "数据库"),
    /**
     * 包括但不限于证书的有效性、证书的过期时间、证书的配置状态等。这些证书可能包括SSL证书、管理员证书、身份证书等
     */
    CERT("02", "证书"),
    /**
     * 包括但不限于管理员账户的状态（例如是否签发）、管理员权限的配置（例如是否符合最小权限原则）、管理员操作的审计等
     */
    ADMIN("03", "管理员"),
    /**
     * 包括但不限于API的可用性、API的响应时间、API的返回结果是否符合预期、API的安全性
     */
    API("04", "API接口"),
    /**
     * 包括但不限于缓存的命中率、缓存的大小、缓存的过期策略、缓存的更新策略等
     */
    CACHE("05", "缓存"),
    /**
     * 包括但不限于日志是否正在正确生成和记录、日志存储空间是否足够、日志级别设置是否正确、日志格式是否符合预期、错误和异常日志是否被正确处理等
     */
    LOGGING("06", "日志系统"),
    /**
     * 包括但不限于检查网络连接状态、网络速度、网络延迟、网络设备状态等
     */
    NETWORK("07", "网络"),
    /**
     * 包括但不限于检查身份验证系统、权限控制系统、防火墙、防病毒软件、数据加密等
     */
    SECURITY("08", "安全"),
    /**
     * 包括但不限于CPU占用率、内存使用情况、硬盘空间、硬盘I/O速度、系统负载等
     */
    PERFORMANCE("09", "性能"),
    /**
     * 包括但不限于系统配置、应用配置、数据库配置、网络配置等是否符合预期
     */
    CONFIGURATION("10", "配置"),
    /**
     * 包括但不限于检查数据库备份、文件系统备份、备份完整性、备份恢复能力等
     */
    BACKUP_RECOVERY("11", "备份与恢复"),
    /**
     * 包括但不限于密钥生成、密钥归档、密钥缓存补充、密钥资源异步减少等定时任务
     */
    TIMER_JOB("12", "定时任务"),
    ;

    private final String code;
    private final String name;

    Category(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return tr();
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}