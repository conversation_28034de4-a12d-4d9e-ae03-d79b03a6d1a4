package kl.npki.base.core.biz.cert.parser.extension.impl;

import kl.nbase.security.asn1.custom.ct.v1.DigitallySigned;
import kl.nbase.security.asn1.custom.ct.v1.SerializedSCT;
import kl.nbase.security.asn1.custom.ct.v1.SignedCertificateTimestamp;
import kl.nbase.security.asn1.custom.ct.v1.SignedCertificateTimestampList;
import kl.nbase.security.util.Arrays;
import kl.nbase.security.util.encoders.Hex;
import kl.npki.base.core.biz.cert.parser.constant.CertTransparencyEnum;
import kl.npki.base.core.biz.cert.parser.constant.ExtensionType;
import kl.npki.base.core.biz.cert.parser.extension.AbstractX509ExtensionParser;
import kl.npki.base.core.constant.I18nConstant;
import kl.npki.base.core.utils.DateUtil;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/15 10:48
 */
public class CertTransparencyExtensionParserImpl extends AbstractX509ExtensionParser {

    /**
     * 证书透明度扩展项解析失败,目前仅支持V1版本解析
     */
    private static final String ERR_MESSAGE = "currently_only_v1_version_parsing_is_supported";
 /**
     * 未知版本
     */
    private static final String UNKNOWN_VERSION = "unknown_version";


    /**
     * 生成一个易于阅读的扩展值字符串，此方法旨在将字节数组形式的扩展值转换为便于人类阅读和理解的字符串格式
     * <p>
     * 具体的转换逻辑由子类实现，因为不同的场景可能需要不同的格式化规则
     *
     * @param extensionOctets 扩展值的字节表示形式
     * @return 格式化后的字符串
     */
    @Override
    protected String generatePrettyValue(byte[] extensionOctets) {
        // 获取证书透明度扩展项内容
        // 先按照v1版本解析，如果解析失败，则尝试解析为v2版本(V2暂未实现)
        try {
            SignedCertificateTimestampList signedCertificateTimestampList = SignedCertificateTimestampList.getInstance(extensionOctets);
            return getV1SignedCertificateTimestampInfo(signedCertificateTimestampList);
        } catch (Exception e) {
            return I18nConstant.getBaseCoreI18nMessage(ERR_MESSAGE);
        }
    }


    /**
     * 获取v1签名证书时间戳信息
     *
     * @param signedCertificateTimestampList 签名证书时间戳列表
     * @return {@link String }
     * @date 2024/11/05 10:22
     * <AUTHOR>
     */
    private String getV1SignedCertificateTimestampInfo(SignedCertificateTimestampList signedCertificateTimestampList) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.dateTimePattern);
        SerializedSCT sctList = signedCertificateTimestampList.getSctList();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < sctList.size(); i++) {
            SignedCertificateTimestamp signedCertificateTimestamp = sctList.get(i);
            // 拼接版本、CT日志服务器标识、时间戳、签名等信息
            // rfc 9162 10.2.3. VersionedTransTypes  和 4.8. Signed Certificate Timestamp (SCT)
            // rfc 6962 3.2.  Structure of the Signed Certificate Timestamp  和 4.1.  Add Chain to Log
            // todo 目前版本先不兼容V2版本的CT服务响应
            int version = signedCertificateTimestamp.getVersion();
            String versionStr;
            if (0 == version) {
                versionStr = "v1";
            } else {
                return I18nConstant.getBaseCoreI18nMessage(UNKNOWN_VERSION);
            }
            sb.append(CertTransparencyEnum.SctEnum.SCT_VERSION.getDesc()).append(COLON).append(" ").append(versionStr).append(LINE_SEPARATOR);
            sb.append(CertTransparencyEnum.SctEnum.SCT_LOG_ID.getDesc()).append(COLON).append(" ").append(Hex.toHexString(signedCertificateTimestamp.getLogId())).append(LINE_SEPARATOR);
            sb.append(CertTransparencyEnum.SctEnum.SCT_TIMESTAMP.getDesc()).append(COLON).append(" ").append(dateFormat.format(new Date(signedCertificateTimestamp.getTimestamp()))).append(LINE_SEPARATOR);
            if (!Arrays.isNullOrEmpty(signedCertificateTimestamp.getExtensions())) {
                sb.append(CertTransparencyEnum.SctEnum.SCT_EXTENSIONS.getDesc()).append(COLON).append(" ").append(Hex.toHexString(signedCertificateTimestamp.getExtensions())).append(LINE_SEPARATOR);
            }

            // 获取摘要算法、签名算法、签名信息
            DigitallySigned digitallySigned = signedCertificateTimestamp.getDigitallySigned();
            sb.append(CertTransparencyEnum.SctEnum.SCT_HASH_ALGORITHM.getDesc()).append(COLON).append(" ").append(
                Objects.requireNonNull(CertTransparencyEnum.HashAlgorithm.ofCode(digitallySigned.getSignatureAndHashAlgorithm().getHash())).name()
            ).append(LINE_SEPARATOR);
            sb.append(CertTransparencyEnum.SctEnum.SCT_SIGNATURE_ALGORITHM.getDesc()).append(COLON).append(" ").append(
                Objects.requireNonNull(CertTransparencyEnum.SignatureAlgorithm.ofCode(digitallySigned.getSignatureAndHashAlgorithm().getSignatureAlgorithm())).name()
            ).append(LINE_SEPARATOR);
            sb.append(CertTransparencyEnum.SctEnum.SCT_SIGNATURE.getDesc()).append(COLON).append(" ").append(Hex.toHexString(digitallySigned.getSignature())).append(LINE_SEPARATOR).append(LINE_SEPARATOR);
        }
        return sb.toString();
    }

    /**
     * 获取扩展项类型
     *
     * @return 返回当前对象扩展项类型 {@link ExtensionType}
     */
    @Override
    public ExtensionType getExtensionType() {
        return ExtensionType.CERT_TRANSPARENCY;
    }
}
