package kl.npki.base.core.biz.org.service.impl;

import kl.npki.base.core.biz.org.model.OrgEntity;
import kl.npki.base.core.biz.org.service.IOrgCheckService;
import kl.npki.base.core.constant.UserStatus;
import kl.npki.base.core.exception.BaseValidationError;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public class OrgCheckDefaultServiceImpl implements IOrgCheckService {
    /**
     * 是否可以废除机构
     *
     * @param orgId
     * @return
     */
    @Override
    public boolean canRevoke(Long orgId) {
        return true;
    }

    /**
     * 是否可以删除机构
     *
     * @param orgId
     * @return
     */
    @Override
    public boolean canDelete(Long orgId) {
        return true;
    }

    @Override
    public void check(OrgEntity orgEntity) {
        // 检查机构名称是否为空
        if (StringUtils.isEmpty(orgEntity.getOrgName())) {
            throw BaseValidationError.ORG_NAME_NULL_ERROR.toException();
        }

        // 检查机构编码是否为空
        if (StringUtils.isEmpty(orgEntity.getOrgCode())) {
            throw BaseValidationError.ORG_CODE_NULL_ERROR.toException();
        }

        // 检查机构状态是否正确
        if (UserStatus.NORMAL.getCode() != orgEntity.getOrgStatus() &&
                UserStatus.CANCELLED.getCode() != orgEntity.getOrgStatus()) {
            throw BaseValidationError.ORG_STATUS_ERROR.toException("orgStatus: " + orgEntity.getOrgStatus());
        }

        // 检查机构编码是否与父机构编码相同
        if (orgEntity.getOrgCode().equals(orgEntity.getParentCode())) {
            throw BaseValidationError.ORG_ADD_EQ_ERROR.toException();
        }
    }
}
