package kl.npki.base.core.biz.check.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 服务自检结果
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
public class SelfCheckResult implements Serializable, Cloneable {

    /**
     * 自检项唯一标识
     */
    private String id;

    /**
     * 自检项编码
     */
    private String code;

    /**
     * 自检项名称
     */
    private String name;

    /**
     * 检测项的版本或环境标识
     */
    private String version;

    /**
     * 自检项分类
     */
    private String category;

    /**
     * 检测结果状态
     */
    private Status status;

    /**
     * 检测失败的严重性
     */
    private Severity severity;

    /**
     * 简洁地描述自检的情况,可能只包含简单的错误消息或失败的总结
     */
    private String message;

    /**
     * 检测耗时
     */
    private long duration;

    /**
     * 用于引导用户解决自检失败的字段，可以是前端页面路径或其他引导提示信息,没有可不填
     */
    private String guidance;

    /**
     * 检测的详细信息,提供了更详细的上下文信息,可以包含任何相关的详细信息，比如异常的堆栈跟踪、失败的详细说明、自检的过程记录
     */
    private Map<String, Object> details;

    /**
     * 检测时间
     */
    private LocalDateTime checkTime;

    /**
     * 检测人
     */
    private String checker;

    public SelfCheckResult() {
        // No args constructor
    }

    private SelfCheckResult(CheckResultBuilder builder) {
        this.id = builder.id;
        this.code = builder.code;
        this.name = builder.name;
        this.version = builder.version;
        this.category = builder.category;
        this.status = builder.status;
        this.severity = builder.severity;
        if (builder.details == null) {
            this.details = new HashMap<>();
        } else {
            this.details = builder.details;
        }
        this.duration = builder.duration;
        this.guidance = builder.guidance;
        this.message = builder.message;
        this.checkTime = builder.checkTime;
        this.checker = builder.checker;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public Severity getSeverity() {
        return severity;
    }

    public void setSeverity(Severity severity) {
        this.severity = severity;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Map<String, Object> getDetails() {
        return details;
    }

    public void setDetails(Map<String, Object> details) {
        this.details = details;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    public String getGuidance() {
        return guidance;
    }

    public void setGuidance(String guidance) {
        this.guidance = guidance;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public String getChecker() {
        return checker;
    }

    public void setChecker(String checker) {
        this.checker = checker;
    }

    public static CheckResultBuilder builder() {
        return new CheckResultBuilder();
    }

    public static class CheckResultBuilder {
        private String id;
        private String code;
        private String name;
        private String version;
        private String category;
        private Status status;
        private Severity severity;
        private Map<String, Object> details;
        private long duration;
        private String guidance;
        private String message;
        private LocalDateTime checkTime;
        private String checker;

        public CheckResultBuilder id(String id) {
            this.id = id;
            return this;
        }

        public CheckResultBuilder code(String code) {
            this.code = code;
            return this;
        }

        public CheckResultBuilder name(String name) {
            this.name = name;
            return this;
        }

        public CheckResultBuilder version(String version) {
            this.version = version;
            return this;
        }

        public CheckResultBuilder category(String category) {
            this.category = category;
            return this;
        }

        public CheckResultBuilder status(Status status) {
            this.status = status;
            return this;
        }

        public CheckResultBuilder checkTime(LocalDateTime checkTime) {
            this.checkTime = checkTime;
            return this;
        }

        public CheckResultBuilder severity(Severity severity) {
            this.severity = severity;
            return this;
        }

        public CheckResultBuilder details(Map<String, Object> details) {
            this.details = details;
            return this;
        }

        public CheckResultBuilder duration(long duration) {
            this.duration = duration;
            return this;
        }

        public CheckResultBuilder guidance(String guidance) {
            this.guidance = guidance;
            return this;
        }

        public CheckResultBuilder message(String message) {
            this.message = message;
            return this;
        }

        public CheckResultBuilder checker(String checker) {
            this.checker = checker;
            return this;
        }

        public SelfCheckResult build() {
            return new SelfCheckResult(this);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SelfCheckResult that = (SelfCheckResult) o;
        return duration == that.duration && Objects.equals(id, that.id) && Objects.equals(code, that.code) && Objects.equals(name, that.name) && Objects.equals(version, that.version) && Objects.equals(category, that.category) && status == that.status && severity == that.severity && Objects.equals(message, that.message) && Objects.equals(details, that.details) && Objects.equals(guidance, that.guidance) && Objects.equals(checkTime, that.checkTime) && Objects.equals(checker, that.checker);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, code, name, version, category, status, severity, message, details, duration, guidance, checkTime, checker);
    }

    @Override
    public SelfCheckResult clone() {
        try {
            return (SelfCheckResult) super.clone();
        } catch (CloneNotSupportedException e) {
            // 实现了Cloneable接口则不会抛出CloneNotSupportedException，不会走到这里
            throw new RuntimeException(e);
        }
    }

}
