package kl.npki.base.core.biz.license.service.impl;

import kl.npki.base.core.biz.config.model.ConfigStatus;
import kl.npki.base.core.biz.config.service.IConfigStatusService;
import kl.npki.base.core.biz.license.LicenseMgr;
import kl.npki.base.core.biz.license.model.LicenseInfo;
import kl.npki.base.core.biz.license.service.IExternalLicenseCheck;
import kl.npki.base.core.biz.license.service.ILicenseCountService;
import kl.npki.base.core.biz.license.service.ILicenseService;
import kl.npki.base.core.configs.LicenseConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.utils.FileCheckUtils;
import kl.npki.base.core.utils.LicenseI18nHelper;
import kl.security.license.bean.CustomLicenseItem;
import kl.security.license.bean.LicConfig;
import kl.security.license.bean.ShareLicenseItem;
import kl.security.license.core.AbsLicenseMgr;
import kl.security.license.core.License;
import kl.security.license.core.LicenseStatus;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.*;
import static kl.npki.base.core.constant.LicenseConstant.*;


/**
 * <AUTHOR>
 * @since 2023/1/16
 */
public class LicenseServiceImpl implements ILicenseService, IConfigStatusService {

    private static final String LICENSE_CONFIG_NAME = "license";
    private static final Logger log = LoggerFactory.getLogger(LicenseServiceImpl.class);

    private final IExternalLicenseCheck externalLicenseCheck;

    private final ILicenseCountService licenseCountService;

    private final LicenseConfig licenseConfig;

    public LicenseServiceImpl(IExternalLicenseCheck externalLicenseCheck,
                              ILicenseCountService licenseCountService,
                              LicenseConfig licenseConfig) {
        this.externalLicenseCheck = externalLicenseCheck;
        this.licenseCountService = licenseCountService;
        this.licenseConfig = licenseConfig;
    }

    @Override
    public void importLicense(LicenseConfig licenseConfig) {
        if (Objects.isNull(licenseConfig) || StringUtils.isBlank(licenseConfig.getLicense())) {
            throw BaseInternalError.LICENSE_VERIFY_ERROR.toException(LICENSE_IS_EMPTY_I18N_KEY);
        }

        String license = new String(Base64.decodeBase64(licenseConfig.getLicense()), StandardCharsets.UTF_8);
        if (StringUtils.isEmpty(license)) {
            throw BaseInternalError.LICENSE_VERIFY_ERROR.toException(LICENSE_IS_EMPTY_I18N_KEY);
        }
        if (!license.startsWith("MIME-Version: 1.0") || !license.contains("content.lic.serial")) {
            throw BaseInternalError.LICENSE_VERIFY_ERROR.toException(LICENSE_CONTENT_IS_INVALID_I18N_KEY);
        }
        License lic;
        try {
            LicenseMgr.getInstance().reload();
            lic = LicenseMgr.getInstance().getLicense();
        } catch (Exception e) {
            throw BaseInternalError.LICENSE_VERIFY_ERROR.toException(LICENSE_CONTENT_IS_INVALID_I18N_KEY, e);
        }
        // 校验License格式
        LicenseStatus licenseStatus = checkLicenseType(lic);
        // 构造并验证License
        switch (licenseStatus) {
            case FINE:
                if (Objects.nonNull(externalLicenseCheck)) {
                    externalLicenseCheck.checkInputLicense(lic);
                }

                break;
            default:
                throw BaseInternalError.LICENSE_VERIFY_ERROR.toException(LICENSE_CONTENT_IS_INVALID_WITH_STATUS_I18N_KEY, LicenseI18nHelper.getLicenseStatusI18nMsg(licenseStatus));

        }
    }

    @Override
    public boolean importLicense(String license) {
        checkUploadString(license);
        License lic;
        try {
            LicConfig lc = LicenseMgr.getLicConfig(license);
            lic = new License(lc);
        } catch (Exception e) {
            throw BaseInternalError.LICENSE_IMPORT_ERROR.toException(e);
        }
        final LicenseStatus status = lic.getStatus();
        // 构造并验证License
        switch (status) {
            case FINE:
                // 有效的License
                if (Objects.nonNull(externalLicenseCheck)) {
                    externalLicenseCheck.checkInputLicense(lic);
                }
                // 存储到配置中
                licenseConfig.setLicense(new String(Base64.encodeBase64(license.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8));
                ConfigHolder.get().save(licenseConfig);
                // 刷新内存中的License
                LicenseMgr.getInstance().reInit();
                break;
            default:
                throw BaseInternalError.LICENSE_VERIFY_ERROR.toException(LICENSE_CONTENT_IS_INVALID_WITH_STATUS_I18N_KEY, LicenseI18nHelper.getLicenseStatusI18nMsg(status));
        }
        return true;
    }

    @Override
    public LicenseInfo getLicense() {
        LicenseInfo response = new LicenseInfo();
        LicenseMgr licenseMgr = LicenseMgr.getInstance();
        License license = licenseMgr.getLicense();
        response.setSerialNumber(licenseMgr.getLocalSerial());
        if (Objects.isNull(license) || MapUtils.isEmpty(license.getLicMap())) {
            return response;
        }

        response.setLicenseStatus(LicenseI18nHelper.getLicenseStatusI18nMsg(getLicenseStatus()));

        response.setCusMap(license.getCusMap());
        if (Objects.nonNull(licenseCountService)) {
            response.setCusUsedMap(licenseCountService.getCusUsedMap(license));
        }

        Map<String, ShareLicenseItem> licMap = license.getLicMap();
        ShareLicenseItem validStartShareLicenseItem = licMap.get(VALID_START);
        String validStartValue = validStartShareLicenseItem.getValue();
        ShareLicenseItem validEndShareLicenseItem = licMap.get(VALID_END);
        String validEndValue = validEndShareLicenseItem.getValue();
        ShareLicenseItem versionShareLicenseItem = licMap.get(VERSION);
        String versionValue = versionShareLicenseItem.getValue();
        ShareLicenseItem purposeShareLicenseItem = licMap.get(PURPOSE);
        String purposeValue = purposeShareLicenseItem.getValue();
        response.setVersion(versionValue);
        response.setPurpose(purposeValue);
        response.setValidStart(validStartValue);
        response.setValidEnd(validEndValue);
        return response;
    }

    @Override
    public LicenseInfo parseLicense(String license) {

        // 授权文件格式检查
        checkUploadString(license);

        // 解析授权文件
        LicConfig licConfig = new LicConfig(license, null);
        License lic = new License(licConfig);

        // 验证授权文件状态、文件格式是否有效
        LicenseStatus status = checkLicenseType(lic);
        if (!LicenseStatus.FINE.equals(status)) {
            throw BaseInternalError.LICENSE_VERIFY_ERROR.toException(LICENSE_CONTENT_IS_INVALID_WITH_STATUS_I18N_KEY, LicenseI18nHelper.getLicenseStatusI18nMsg(status));
        }

        // 获取授权信息
        Map<String, ShareLicenseItem> licMap = lic.getLicMap();
        // 设置通用授权信息
        LicenseInfo response = new LicenseInfo();
        response.setLicenseStatus(LicenseI18nHelper.getLicenseStatusI18nMsg(status));
        response.setSerialNumber(licMap.get(SERIAL).getValue());
        response.setVersion(licMap.get(VERSION).getValue());
        response.setPurpose(licMap.get(PURPOSE).getValue());
        response.setValidStart(licMap.get(VALID_START).getValue());
        response.setValidEnd(licMap.get(VALID_END).getValue());
        // 设置定制授权信息
        response.setCusMap(lic.getCusMap());

        return response;
    }

    private LicenseStatus checkLicenseType(License lic) {
        LicenseStatus licenseStatus = lic.getStatus();
        if (!LicenseStatus.FINE.equals(licenseStatus)) {
            return licenseStatus;
        }
        Map<String, CustomLicenseItem> cusMap = lic.getCusMap();
        if (MapUtils.isEmpty(cusMap)) {
            return licenseStatus;
        }

        CustomLicenseItem customLicenseType = cusMap.get("cus.SYS-NGPKI-TYPE");
        if (Objects.isNull(customLicenseType)) {
            return licenseStatus;
        }
        String sysInfoShortName = BaseConfigWrapper.getSysInfoConfig().getShortName();
        if (StringUtils.isBlank(sysInfoShortName)) {
            return licenseStatus;
        }
        // 校验授权与当前系统是否匹配
        if (!StringUtils.equalsIgnoreCase(customLicenseType.getValue(), sysInfoShortName)) {
            licenseStatus = LicenseStatus.WRONGFORMAT;
        }
        return licenseStatus;
    }

    public static void checkUploadString(String license) {
        if (StringUtils.isEmpty(license)) {
            throw BaseValidationError.LICENSE_IS_EMPTY.toException();
        }
        if (!license.startsWith("MIME-Version: 1.0") || !license.contains("content.lic.serial")) {
            throw BaseValidationError.LICENSE_CONTENT_ERROR.toException();
        }
        if (FileCheckUtils.contains(license, FileCheckUtils.DEFAULT_BLACK_LIST_REG_EXP)) {
            throw BaseValidationError.LICENSE_CONTENT_ERROR.toException(CONTAINS_ILLEGAL_CHARACTERS_I18N_KEY);
        }
    }

    @Override
    public ConfigStatus queryConfigStatus() {
        ConfigStatus configStatus = new ConfigStatus(LICENSE_CONFIG_NAME);
        LicenseStatus status = getLicenseStatus();
        if (LicenseStatus.FINE.equals(status)) {
            configStatus.setStatus(true);
        } else {
            configStatus.setStatus(false);
            configStatus.setWarnInfo(LicenseI18nHelper.getLicenseStatusI18nMsg(status));
        }
        return configStatus;
    }

    /**
     * 导入过后查询License状态
     *
     * @return License状态
     */
    @Override
    public LicenseStatus getLicenseStatus() {
        License lic = LicenseMgr.getInstance().getLicense();
        final LicenseStatus status = lic.getStatus();
        // 重新刷新一次，确认授权是否过期
        lic.refreshDate();
        // 构造并验证License
        if (Objects.requireNonNull(status) == LicenseStatus.FINE && Objects.nonNull(externalLicenseCheck)) {
            try {
                externalLicenseCheck.checkInputLicense(lic);
            } catch (Exception e) {
                // License校验失败
                log.error("License verification failed", e);
                return LicenseStatus.OTHER;
            }
        }
        return status;
    }

    @Override
    public void shutdown() {
        AbsLicenseMgr.destory();
        log.info("License monitor closed Successfully");
    }
}
