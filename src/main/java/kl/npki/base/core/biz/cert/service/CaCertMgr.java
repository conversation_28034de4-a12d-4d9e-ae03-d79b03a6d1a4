package kl.npki.base.core.biz.cert.service;

import kl.nbase.emengine.entity.param.CommonAsymSignParam;
import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.security.asn1.ASN1BitString;
import kl.nbase.security.asn1.ASN1Encoding;
import kl.nbase.security.asn1.DERBitString;
import kl.nbase.security.asn1.pkcs.CertificationRequest;
import kl.nbase.security.asn1.pkcs.CertificationRequestInfo;
import kl.nbase.security.asn1.x500.X500Name;
import kl.nbase.security.asn1.x509.AlgorithmIdentifier;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.asn1.x509.SubjectPublicKeyInfo;
import kl.nbase.security.constants.SM2Content;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.entity.data.IOriginalData;
import kl.nbase.security.entity.data.ISignatureData;
import kl.nbase.security.entity.data.impl.OriginalData;
import kl.nbase.security.utils.AsymKeyUtil;
import kl.npki.base.core.biz.cert.model.CertRequestInfo;
import kl.npki.base.core.biz.cert.model.IssueCertInfo;
import kl.npki.base.core.biz.cert.model.TrustCertEntity;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.SignAlgoEnum;
import kl.npki.base.core.constant.TrustCertType;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.core.repository.ITrustCertRepository;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.npki.base.core.tenantholders.common.AbstractCommonTenant;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.base.core.utils.KeyUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.security.KeyPair;
import java.security.PublicKey;
import java.util.List;
import java.util.ServiceLoader;

/**
 * <AUTHOR> wk
 * @Date 2025/03/11 18:05
 * @Description: 系统证书（站点、身份、管理员等证书）连接CA签发业务实现类
 */
public class CaCertMgr extends AbstractCommonTenant {

    /**
     * logger
     **/
    private static final Logger logger = LoggerFactory.getLogger(CaCertMgr.class);

    /**
     * 管理证书实体的Repository
     */
    ITrustCertRepository trustCertRepository = RepositoryFactory.get(ITrustCertRepository.class);

    /**
     * 证书链
     */
    private List<TrustCertEntity> trustCertList;

    @Override
    public void refresh() {
        trustCertList = trustCertRepository.getAll(TrustCertType.MANAGE);
        if (trustCertList == null || trustCertList.isEmpty()) {
            logger.warn("The ca cert is null.");
        }
    }

    /**
     * 签发站点证书
     * @param certRequestInfo
     * @return
     */
    public IssueCertInfo signSslCert(CertRequestInfo certRequestInfo) {
        IssueCertInfo caIssueCertInfo = null;
        ServiceLoader<ICaCertMgrSpi> loader = ServiceLoader.load(ICaCertMgrSpi.class);
        for (ICaCertMgrSpi iCaCertMgr : loader) {
            if (iCaCertMgr.available()) {
                caIssueCertInfo = iCaCertMgr.signSslCert(certRequestInfo);
                break;
            }
        }
        return caIssueCertInfo;
    }

    /**
     * 签发管理员证书
     * @param p10CertReq
     * @return
     */
    public IssueCertInfo signAdminCert(String p10CertReq, long validDays) {
        IssueCertInfo caIssueCertInfo = null;
        ServiceLoader<ICaCertMgrSpi> loader = ServiceLoader.load(ICaCertMgrSpi.class);
        for (ICaCertMgrSpi iCaCertMgr : loader) {
            if (iCaCertMgr.available()) {
                caIssueCertInfo = iCaCertMgr.signAdminCert(p10CertReq, validDays);
            }
        }
        return caIssueCertInfo;
    }


    /**
     * 签发身份证书
     * @param certRequestInfo
     * @return
     */
    public IssueCertInfo signAuthCert(CertRequestInfo certRequestInfo) {
        // 1. 生成身份证书请求，身份证书使用的是密码机中内置私钥
        CertificationRequest certificationRequest = generateCertificateRequestsUseInternal(
                certRequestInfo.buildDn(),
                BaseConfigWrapper.getSysAlgoConfig().getSystemCertAsymAlgo(),
                certRequestInfo.getKeyIndex());
        certRequestInfo.setCertificationRequest(certificationRequest);

        // 2. 调用具体实现（在RA系统中具体实现是调用CA发证）签发证书
        IssueCertInfo caIssueCertInfo = null;
        ServiceLoader<ICaCertMgrSpi> loader = ServiceLoader.load(ICaCertMgrSpi.class);
        for (ICaCertMgrSpi iCaCertMgr : loader) {
            if (iCaCertMgr.available()) {
                caIssueCertInfo = iCaCertMgr.signAuthCert(certRequestInfo);
                break;
            }
        }
        return caIssueCertInfo;
    }

    /**
     * 获取证书链
     * @return
     */
    public List<TrustCertEntity> getTrustCertList() {
        return trustCertList;
    }

    /**
     * 通过keyType获取颁发者entity
     *
     * @param keyType
     * @return
     */
    public TrustCertEntity getCaCertEntity(String keyType) {
        return trustCertList.stream()
                .filter(trustCertEntity -> StringUtils.isNotBlank(trustCertEntity.getCertValue()))
                .filter(trustCertEntity -> getAlgoType(CertUtil.parseCert(trustCertEntity.getCertValue())).equals(keyType))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取证书的公钥算法
     *
     * @param certificate
     * @return
     */
    private static String getAlgoType(Certificate certificate) {
        return KeyUtils.getAsymAlgo(certificate.getSubjectPublicKeyInfo()).getAlgoType();
    }

    /**
     * 使用内置私钥生成证书请求
     *
     * @param subjectDn 证书主体
     * @param asymAlgo  非对称算法
     * @param keyIndex  内置密钥索引
     * @return PKCS#10 格式的证书请求
     */
    private static CertificationRequest generateCertificateRequestsUseInternal(String subjectDn,
                                                                               AsymAlgo asymAlgo,
                                                                               int keyIndex) {
        ClusterEngine engine = EngineHolder.get();

        // 1. 使用索引和算法获取内置密钥对
        KeyPair signKeyPair = engine.getSignKeyPair(asymAlgo, keyIndex);
        PublicKey publicKey = signKeyPair.getPublic();

        // 2. 构造证书请求体
        SubjectPublicKeyInfo subjectPublicKeyInfo = SubjectPublicKeyInfo.getInstance(publicKey.getEncoded());
        CertificationRequestInfo certificationRequestInfo = new CertificationRequestInfo(
                new X500Name(subjectDn),
                subjectPublicKeyInfo,
                null);

        // 3. 使用内置私钥对证书请求体做签名
        IOriginalData originalData = null;
        try {
            originalData = new OriginalData(certificationRequestInfo.getEncoded(ASN1Encoding.DER));
        } catch (IOException e) {
            throw BaseInternalError.CERT_REQ_GEN_ERROR.toException(e);
        }
        SignAlgoEnum signAlgoEnum;
        switch (asymAlgo.getType()) {
            case RSA: signAlgoEnum = SignAlgoEnum.SHA256WithRSA; break;
            case SM2: signAlgoEnum = SignAlgoEnum.SM3WithSM2; break;
            default:
                throw BaseValidationError.ALGORITHM_NOT_SUPPORTED.toException(asymAlgo.getType().getDesc());
        }
        AlgorithmIdentifier algorithm = new AlgorithmIdentifier(SignAlgoEnum.getOidByEnum(signAlgoEnum));
        CommonAsymSignParam param = new CommonAsymSignParam(signAlgoEnum.getHashAlgo(),
                publicKey, SM2Content.DEFAULT_SM2_USER_ID);
        ISignatureData signatureData = engine.sign(signKeyPair.getPrivate(), originalData, param);
        ASN1BitString signature = new DERBitString(signatureData.getSignedData());

        // 4. 构造PKCS#10证书请求并返回
        return new CertificationRequest(certificationRequestInfo, algorithm, signature);
    }

}
