package kl.npki.base.core.biz.check;

import kl.nbase.exception.context.ExceptionContext;
import kl.npki.base.core.biz.check.model.Category;
import kl.npki.base.core.biz.check.model.SelfCheckResult;
import kl.npki.base.core.biz.inspection.InspectionItemTypeEnum;

/**
 * 服务自检项目接口
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
public interface ISelfCheckItem {

    /**
     * 执行服务自检
     *
     * @return 检测结果
     */
    SelfCheckResult check();

    /**
     * 检查项是否开启
     *
     * @return 如果检查项开启则返回true，否则返回false
     */
    boolean isEnabled();

    /**
     * 获取检测项名称
     *
     * @return 检测项名称
     */
    String getName();

    /**
     * 获取检测项类别
     *
     * @return 检测项类别
     */
    Category getCategory();

    /**
     * 获取巡检类型
     *
     * @return 巡检类型
     */
    InspectionItemTypeEnum getInspectionItemType();

    /**
     * 获取检测项编码,默认约定: 检测类型编码+检测项编码
     *
     * @return 检测项编码
     */
    String getCode();

    /**
     * 获取自检项完整编码
     *
     * @return 自检项完整编码
     */
    default String getFullCode() {
        return ExceptionContext.getInstance().getSystemCode()
            + ExceptionContext.getInstance().getServiceCode()
            + getCode();
    }
}
