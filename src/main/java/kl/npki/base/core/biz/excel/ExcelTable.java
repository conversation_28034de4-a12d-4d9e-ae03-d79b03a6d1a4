package kl.npki.base.core.biz.excel;

import java.util.List;

/**
 * JSON数据构造为适合导入到EXCEL的格式
 */
public class ExcelTable {
    private String tableName;
    private List<String> mergeCells;
    private List<List<String>> data;

    public ExcelTable() {
    }

    public ExcelTable(String tableName, List<List<String>> data, List<String> mergeCells) {
        this.tableName = tableName;
        this.data = data;
        this.mergeCells = mergeCells;
    }

    // 3. 提供 getter 和 setter 方法
    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<List<String>> getData() {
        return data;
    }

    public void setData(List<List<String>> data) {
        this.data = data;
    }

    public List<String> getMergeCells() {
        return mergeCells;
    }

    public void setMergeCells(List<String> mergeCells) {
        this.mergeCells = mergeCells;
    }
}