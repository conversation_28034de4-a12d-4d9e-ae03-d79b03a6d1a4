package kl.npki.base.core.biz.cert.service;

import kl.npki.base.core.biz.cert.model.CertRequestInfo;
import kl.npki.base.core.biz.cert.model.ImportTrustCertInfo;
import kl.npki.base.core.biz.cert.model.ServerCertRequestInfo;
import kl.npki.base.core.biz.cert.model.TrustCertEntity;
import kl.npki.base.core.biz.config.service.IConfigStatusService;
import kl.npki.base.core.constant.ExportCertTypeEnum;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ITrustCertService extends IConfigStatusService {

    /**
     * 自签发根证书
     *
     * @param certRequestInfo
     * @return
     */
    String issueManageCert(CertRequestInfo certRequestInfo);

    /**
     * 自更新根证书
     * @param certRequestInfo
     * @return
     */
    String selfUpdateManageCert(CertRequestInfo certRequestInfo);

    /**
     * 导入可信证书
     *
     * @param importTrustCertInfo
     * @param sslTrustStorePassword
     */
    void importCert(ImportTrustCertInfo importTrustCertInfo, String sslTrustStorePassword);

    /**
     * 生成p10证书请求
     *
     * @param certRequestInfo
     * @return
     */
    String genCertRequest(CertRequestInfo certRequestInfo);


    /**
     * 更新管理根证书请求
     * @param certRequestInfo
     * @return
     */
    String updateCertRequest(CertRequestInfo certRequestInfo);


    /**
     * 删除证书
     *
     * @param id
     * @param sslTrustStorePassword
     */
    void delete(Long id, String sslTrustStorePassword);

    /**
     * 获取所有可信证书
     *
     * @return
     */
    List<TrustCertEntity> getAllTrustCert();

    /**
     * 获取根证书请求信息
     *
     * @return ServerCertRequestInfo
     */
    ServerCertRequestInfo getRootCertRequestInfo();

    /**
     * 导入管理根证书
     *
     * @param baseCertValue
     * @param sslTrustStorePassword
     */
    void importManageCert(String baseCertValue, String sslTrustStorePassword);


    /**
     * 更新导入管理根证书
     * @param baseCertValue
     * @param sslTrustStorePassword
     */
    void importUpdateManageCert(String baseCertValue, String sslTrustStorePassword);

    /**
     * 导出p10请求 (PEM格式)
     *
     * @return
     */
    String exportCertRequest();

    byte[] exportCertAsZip(String hexSn);

    /**
     * 导出可信证书为zip流 支持指定证书格式
     * @param hexSn 证书唯一标识 16进制的sn
     * @param certTypeEnum 指定证书格式
     * @return zip结果文件的字节数组
     */
    byte[] exportTrustCertAsZip(String hexSn, ExportCertTypeEnum certTypeEnum);

    /**
     * 生成对应证书链的pem文件，为后续gmssl通信进行使用
     */
    void genCertChainPem() throws IOException;

    /**
     * 保存和刷新P12TrustFile
     *
     * @param keyStorePwd
     */
    void saveAndRefreshP12TrustFile(String keyStorePwd);

}
