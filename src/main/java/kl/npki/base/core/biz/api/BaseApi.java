package kl.npki.base.core.biz.api;

/**
 * API契约的顶级接口
 *
 * <p>设计说明:</p>
 * <ol>
 *   <li>作为所有业务API接口的顶层契约,定义统一的API规范</li>
 *   <li>服务提供方可通过实现此接口来提供具体服务(如 Controller 实现)</li>
 *   <li>服务消费方可通过继承此接口来调用服务(如 Feign Client)</li>
 * </ol>
 *
 * <p>使用场景:</p>
 * <ul>
 *   <li>服务端: XxxController implements XxxApi</li>
 *   <li>客户端（不局限于Feign）: @FeignClient XxxClient extends XxxApi</li>
 * </ul>
 *
 * <AUTHOR> Shi
 * @date 2024/12/18
 */
public interface BaseApi {
} 