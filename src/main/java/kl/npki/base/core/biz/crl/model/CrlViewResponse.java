package kl.npki.base.core.biz.crl.model;

import kl.npki.base.core.biz.crl.model.parser.x509.CrlInfo;

/**
 * CRL查看
 *
 * <AUTHOR>
 * @date 2025/5/12
 * @since 1.0
 */

public class CrlViewResponse {

    /**
     * CRL信息集合
     */
    private CrlInfo[] crlInfos;

    public CrlViewResponse(CrlInfo... crlInfo) {
        this.crlInfos = crlInfo;
    }


    /**
     * 根据CRL字符串查看X509 CRL信息
     *
     * @param x509CrlValues X509 CRL字符串，采用BASE64编码
     * @return CrlViewResponse，包含解析后的CRL信息
     */
    public static CrlViewResponse viewX509Crl(String... x509CrlValues) {
        return new CrlViewResponse(CrlInfo.fromCrlString(x509CrlValues));
    }


    public CrlInfo[] getCrlInfos() {
        return crlInfos;
    }

    public void setCrlInfos(CrlInfo[] crlInfos) {
        this.crlInfos = crlInfos;
    }

}
