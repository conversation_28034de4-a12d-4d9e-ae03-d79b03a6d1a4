package kl.npki.base.core.biz.cert.model.parser.x509;

/**
 * 扩展信息构建器
 *
 * <AUTHOR>
 * @date 2024-08-09
 */
public final class ExtensionInfoBuilder {

    private boolean critical;
    private String oid;
    private String name;
    private String desc;
    private String asnValue;
    private String value;
    private String prettyValue;

    private ExtensionInfoBuilder() {
        // do nothing
    }

    public static ExtensionInfoBuilder builder() {
        return new ExtensionInfoBuilder();
    }

    public ExtensionInfoBuilder withCritical(boolean critical) {
        this.critical = critical;
        return this;
    }

    public ExtensionInfoBuilder withOid(String oid) {
        this.oid = oid;
        return this;
    }

    public ExtensionInfoBuilder withName(String name) {
        this.name = name;
        return this;
    }

    public ExtensionInfoBuilder withDesc(String desc) {
        this.desc = desc;
        return this;
    }

    public ExtensionInfoBuilder withAsnValue(String asnValue) {
        this.asnValue = asnValue;
        return this;
    }

    public ExtensionInfoBuilder withValue(String prettyValue) {
        this.value = prettyValue;
        return this;
    }

    public ExtensionInfoBuilder withPrettyValue(String prettyValue) {
        this.prettyValue = prettyValue;
        return this;
    }

    public ExtensionInfo build() {
        ExtensionInfo extensionInfo = new ExtensionInfo();
        extensionInfo.setCritical(critical);
        extensionInfo.setOid(oid);
        extensionInfo.setName(name);
        extensionInfo.setDesc(desc);
        extensionInfo.setAsnValue(asnValue);
        extensionInfo.setValue(value);
        extensionInfo.setPrettyValue(prettyValue);
        return extensionInfo;
    }
}
