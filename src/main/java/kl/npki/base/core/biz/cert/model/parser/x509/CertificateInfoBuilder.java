package kl.npki.base.core.biz.cert.model.parser.x509;

import java.util.ArrayList;
import java.util.List;

/**
 * 证书信息构建器
 *
 * <AUTHOR>
 * @date 2024-08-09
 */
public final class CertificateInfoBuilder {

    private BasicInfo version;
    private BasicInfo serialNumber;
    private BasicInfo subject;
    private BasicInfo issuer;
    private BasicInfo notBefore;
    private BasicInfo notAfter;
    private BasicInfo signatureAlgorithm;
    private BasicInfo publicKey;
    private BasicInfo signature;
    private final List<ExtensionInfo> extensionInfo = new ArrayList<>(8);
    private final List<AttributeInfo> attributeInfo = new ArrayList<>(8);

    private CertificateInfoBuilder() {
        // do nothing
    }

    public static CertificateInfoBuilder builder() {
        return new CertificateInfoBuilder();
    }

    public CertificateInfoBuilder withVersion(BasicInfo version) {
        this.version = version;
        return this;
    }

    public CertificateInfoBuilder withSerialNumber(BasicInfo serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public CertificateInfoBuilder withSubject(BasicInfo subject) {
        this.subject = subject;
        return this;
    }

    public CertificateInfoBuilder withIssuer(BasicInfo issuer) {
        this.issuer = issuer;
        return this;
    }

    public CertificateInfoBuilder withNotBefore(BasicInfo notBefore) {
        this.notBefore = notBefore;
        return this;
    }

    public CertificateInfoBuilder withNotAfter(BasicInfo notAfter) {
        this.notAfter = notAfter;
        return this;
    }

    public CertificateInfoBuilder withSignatureAlgorithm(BasicInfo signatureAlgorithm) {
        this.signatureAlgorithm = signatureAlgorithm;
        return this;
    }

    public CertificateInfoBuilder withPublicKey(BasicInfo publicKey) {
        this.publicKey = publicKey;
        return this;
    }

    public CertificateInfoBuilder withSignature(BasicInfo signature) {
        this.signature = signature;
        return this;
    }

    public CertificateInfoBuilder addExtensionInfo(List<ExtensionInfo> extensionInfoList) {
        this.extensionInfo.addAll(extensionInfoList);
        return this;
    }

    public CertificateInfoBuilder addExtensionInfo(ExtensionInfo extensionInfo) {
        this.extensionInfo.add(extensionInfo);
        return this;
    }

    public CertificateInfoBuilder addAllExtensionInfo(List<ExtensionInfo> extensionInfo) {
        this.extensionInfo.addAll(extensionInfo);
        return this;
    }

    public CertificateInfoBuilder addAttributeInfo(List<AttributeInfo> attributeInfoList) {
        this.attributeInfo.addAll(attributeInfoList);
        return this;
    }

    public CertificateInfoBuilder addAttributeInfo(AttributeInfo attributeInfo) {
        this.attributeInfo.add(attributeInfo);
        return this;
    }

    public CertificateInfo build() {
        CertificateInfo certificateInfo = new CertificateInfo();
        certificateInfo.setVersion(version);
        certificateInfo.setSerialNumber(serialNumber);
        certificateInfo.setSubject(subject);
        certificateInfo.setIssuer(issuer);
        certificateInfo.setNotBefore(notBefore);
        certificateInfo.setNotAfter(notAfter);
        certificateInfo.setSignatureAlgorithm(signatureAlgorithm);
        certificateInfo.setPublicKey(publicKey);
        certificateInfo.setSignature(signature);
        certificateInfo.setExtensionInfo(extensionInfo);
        certificateInfo.setAttributeInfo(attributeInfo);
        return certificateInfo;
    }
}
