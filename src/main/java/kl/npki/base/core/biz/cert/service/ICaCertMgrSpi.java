package kl.npki.base.core.biz.cert.service;

import kl.npki.base.core.biz.cert.model.CertRequestInfo;
import kl.npki.base.core.biz.cert.model.IssueCertInfo;

public interface ICaCertMgrSpi {

    /**
     * 接入CA签发站点证书
     *
     * @param certRequestInfo
     * @return
     */
    IssueCertInfo signSslCert(CertRequestInfo certRequestInfo);

    /**
     * 接入CA签发身份证书
     *
     * @param certRequestInfo
     * @return
     */
    IssueCertInfo signAuthCert(CertRequestInfo certRequestInfo);

    /**
     * 接入CA签发管理员证书
     *
     * @param p10CertReq 管理员证书请求
     * @param validDays  证书有效天数
     * @return 管理员证书信息
     */
    IssueCertInfo signAdminCert(String p10CertReq, long validDays);

    boolean available();
}
