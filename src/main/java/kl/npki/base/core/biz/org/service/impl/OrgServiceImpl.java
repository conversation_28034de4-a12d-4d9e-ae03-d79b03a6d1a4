package kl.npki.base.core.biz.org.service.impl;

import kl.nbase.bean.convert.ConvertService;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.annotation.KlTransactional;
import kl.npki.base.core.biz.batch.model.BatchErrorInfo;
import kl.npki.base.core.biz.batch.model.BatchResult;
import kl.npki.base.core.biz.org.model.OrgAddInfo;
import kl.npki.base.core.biz.org.model.OrgEntity;
import kl.npki.base.core.biz.org.model.OrgUpdateInfo;
import kl.npki.base.core.biz.org.service.IOrgCheckService;
import kl.npki.base.core.biz.org.service.IOrgService;
import kl.npki.base.core.common.org.OrgCache;
import kl.npki.base.core.common.trace.InvocationType;
import kl.npki.base.core.configs.OrgConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.base.core.constant.OrgConstants;
import kl.npki.base.core.constant.OrgOperationEnum;
import kl.npki.base.core.event.EntityChangeEvent;
import kl.npki.base.core.event.EntityChangeEventManager;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.core.repository.IOrgRepository;
import kl.npki.base.core.repository.RepositoryFactory;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 机构服务管理
 *
 * <AUTHOR>
 * @create 2024/3/21 15:40
 */
public class OrgServiceImpl implements IOrgService {
    /**
     * 机构存储仓库
     */
    private final IOrgRepository orgRepository = RepositoryFactory.get(IOrgRepository.class);

    private final ConvertService convertService;
    private final IOrgCheckService orgBindCheckService;

    public OrgServiceImpl(ConvertService convertService) {
        this.convertService = convertService;
        Iterator<IOrgCheckService> iterator = ServiceLoader.load(IOrgCheckService.class).iterator();
        if (iterator.hasNext()) {
            orgBindCheckService = iterator.next();
        } else {
            orgBindCheckService = new OrgCheckDefaultServiceImpl();
        }
    }

    @Override
    public OrgEntity checkExists(Long id) {
        OrgEntity orgEntity = orgRepository.searchById(id);
        CheckUtils.notNull(orgEntity, BaseValidationError.ORG_NOT_FOUND.toException("id = " + id));
        return orgEntity;
    }

    @Override
    public boolean exists(String orgCode) {
        if (StringUtils.isEmpty(orgCode)) {
            // 机构编码为空，直接返回
            return false;
        }

        // 从数据仓库中查询不到数据则认为机构不存在
        return null != orgRepository.searchByOrgCode(orgCode);
    }

    @Override
    public boolean exists(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            // id为空，直接返回
            return false;
        }

        // 从数据仓库中查询不到数据则认为机构不存在
        return null != orgRepository.searchById(id);
    }

    @KlTransactional
    @Override
    public boolean add(OrgAddInfo orgAddInfo) {
        OrgEntity orgEntity = convertService.convert(orgAddInfo, OrgEntity.class);
        String parentCode = orgEntity.getParentCode();
        if (!isRootOrg(orgEntity) && !exists(parentCode)) {
            // 当前机构不是根机构，
            // 并且父机构不存在，不允许添加
            throw BaseValidationError.ORG_PARENT_NOT_FOUND.toException("parentCode=" + parentCode);
        }

        //设置机构排序码
        setRootOrgIndexOrder();
        Integer orderIndex = generateIndexOrder();
        orgEntity.setOrderIndex(orderIndex);

        //设置机构排序完整码
        String orderFullIndex = buildFullIndexOrder(orderIndex, parentCode);
        orgEntity.setOrderFullIndex(orderFullIndex);

        // 设置父机构id
        Long parentId = getParentId(parentCode);
        orgEntity.setParentId(parentId);

        // 设置机构级别
        int orgLevel = getOrgLevel(parentCode);
        orgEntity.setOrgLevel(orgLevel);

        // 设置机构状态
        orgEntity.setOrgStatus(EntityStatus.NORMAL.getId());

        // 设置完整机构编码
        String fullOrgCode = getFullOrgCode(orgEntity.getOrgCode(), orgEntity.getParentCode());
        orgEntity.setFullCode(fullOrgCode);

        // 检查父机构编码是否与子机构编码相同，如果相同抛出异常组织增加
        if (orgEntity.getOrgCode().equals(parentCode)) {
            throw BaseValidationError.ORG_ADD_EQ_ERROR.toException();
        }

        orgEntity.save();
        // 设置完整机构ID
        String fullOrgId = getFullOrgId(orgEntity.getId(), orgEntity.getParentId());
        orgEntity.setFullId(fullOrgId);
        // 再次更新机构
        boolean result = orgRepository.update(orgEntity);

        //记录机构轨迹操作
        recordOrgOperationTrace(orgEntity, OrgOperationEnum.ADD);

        // 更新机构缓存
        OrgCache.INSTANCE.updateCache();

        return result;
    }

    @KlTransactional
    @Override
    public boolean update(Long id, OrgUpdateInfo orgUpdateInfo) {
        // 检查待更新的机构是否存在
        checkExists(id);
        OrgEntity orgEntity = convertService.convert(orgUpdateInfo, OrgEntity.class);
        orgEntity.setId(id);
        // 设置更新时间
        orgEntity.setUpdateTime(LocalDateTime.now());
        // 检查机构编码是否正确
        checkOrgCode(orgEntity);
        boolean updateResult = orgRepository.update(orgEntity);
        //记录机构操作
        recordOrgOperationTrace(orgEntity, OrgOperationEnum.UPDATE);
        // 更新机构缓存
        OrgCache.INSTANCE.updateCache();

        return updateResult;
    }

    @Override
    public boolean revoke(Long id) {
        OrgEntity orgEntity = checkExists(id);

        // 检查机构是否已经被废除
        if (EntityStatus.REVOKED.getId().equals(orgEntity.getOrgStatus())) {
            return true;
        }

        //废除机构前先检查是否已经绑定
        if (!orgBindCheckService.canRevoke(orgEntity.getId())) {
            throw BaseInternalError.ORG_DELETE_BINDING_ERROR.toException();
        }

        boolean revokeResult = orgRepository.revoke(id);
        //记录机构操作
        recordOrgOperationTrace(orgEntity, OrgOperationEnum.REVOKE);
        // 更新机构缓存
        OrgCache.INSTANCE.updateCache();
        return revokeResult;
    }

    @Override
    public boolean delete(Long id) {
        OrgEntity orgEntity = OrgCache.INSTANCE.getOrgById(id);
        // 如果查询不到表示可能已经被删
        if (ObjectUtils.isNotEmpty(orgEntity)) {
            // 存在子机构，不允许删除
            if (OrgCache.INSTANCE.hasSubOrg(orgEntity.getId())) {
                throw BaseValidationError.ORG_DELETE_ERROR.toException();
            }
            //删除机构前先检查是否已经绑定
            if (!orgBindCheckService.canDelete(orgEntity.getId())) {
                throw BaseInternalError.ORG_DELETE_BINDING_ERROR.toException();
            }
            boolean deleteResult = orgRepository.delete(id);
            //记录机构操作
            recordOrgOperationTrace(orgEntity, OrgOperationEnum.DELETE);
            // 更新机构缓存
            OrgCache.INSTANCE.updateCache();
            return deleteResult;
        }
        return true;
    }

    @Override
    public BatchResult batchDelete(List<Long> orgIds) {

        BatchResult batchResponse = new BatchResult();
        int errorCount = 0;
        //批量删除校验不通过机构Id集合
        List<Long> checkFailedIds = new ArrayList<>();
        //batchResponse中错误详情信息list
        List<BatchErrorInfo> errorInfoList = new ArrayList<>();

        //构建校验不通过机构集合
        buildCheckFailedOrg(orgIds, checkFailedIds, errorInfoList);

        //批量导入失败数量
        errorCount = checkFailedIds.size();
        //从待删除集合中移除校验不通过机构集合
        orgIds.removeAll(checkFailedIds);
        //数据库批量删除
        boolean result = orgRepository.batchDelete(orgIds);
        if (!result) {
            //数据库批量删除失败，错误信息记录
            errorCount += orgIds.size();
            orgIds.forEach(orgId -> {
                OrgEntity orgEntity = orgRepository.searchById(orgId);
                BatchErrorInfo errorInfo = fullErrorInfo(orgEntity, BaseInternalError.ORG_DELETE_BINDING_ERROR.getDesc());
                errorInfoList.add(errorInfo);
            });
        }
        batchResponse.setOperationName(OrgOperationEnum.DELETE.getDesc());
        batchResponse.setSuccessCount(result ? orgIds.size() : 0);
        batchResponse.setErrorCount(errorCount);
        batchResponse.setBatchErrorInfo(errorInfoList);

        // 更新机构缓存
        OrgCache.INSTANCE.updateCache();

        return batchResponse;
    }

    /**
     * 检查机构编码是否正确
     * <li>检查父机构编码是否与子机构编码相同</li>
     * <li>使用SPI自定义检查</li>
     *
     * @param orgEntity 待检查机构实体
     */
    private void checkOrgCode(OrgEntity orgEntity) {
        if (orgEntity.getOrgCode().equals(orgEntity.getParentCode())) {
            throw BaseValidationError.ORG_ADD_EQ_ERROR.toException();
        }
    }

    /**
     * 获取父机构id
     *
     * @param parentOrgCode 父机构编码
     * @return 机构级别
     */
    private Long getParentId(String parentOrgCode) {
        Long parentId = Long.valueOf(OrgConstants.ORG_ROOT_PARENT_CODE_FLAG);
        OrgEntity parentOrgEntity = orgRepository.searchByOrgCode(parentOrgCode);
        if (null != parentOrgEntity) {
            parentId = parentOrgEntity.getId();
        }
        return parentId;
    }

    /**
     * 获取机构级别
     *
     * @param parentOrgCode 父机构编码
     * @return 机构级别
     */
    private int getOrgLevel(String parentOrgCode) {
        int orgLevel = 0;
        OrgEntity parentOrgEntity = orgRepository.searchByOrgCode(parentOrgCode);
        if (null != parentOrgEntity) {
            orgLevel = parentOrgEntity.getOrgLevel() + 1;
        }

        return orgLevel;
    }

    /**
     * 获取完整机构编码
     *
     * @param orgCode       机构编码
     * @param parentOrgCode 父机构编码
     * @return 完整的机构编码：<code>根机构编码.二级机构编码.三级机构编码....叶子机构编码</code>
     */
    private String getFullOrgCode(String orgCode, String parentOrgCode) {
        if (OrgConstants.ORG_ROOT_PARENT_CODE_FLAG.equals(parentOrgCode)) {
            return orgCode;
        }
        return getFullParentOrgCode(parentOrgCode) + "." + orgCode;
    }

    /**
     * 获取机构的完整机构编码
     *
     * @param orgCode 父机构编码
     * @return 完整机构编码
     */
    private String getFullParentOrgCode(String orgCode) {
        OrgEntity orgEntity = orgRepository.searchByOrgCode(orgCode);
        if (null == orgEntity) {
            return orgCode;
        }

        String parentCode = orgEntity.getParentCode();
        if (OrgConstants.ORG_ROOT_PARENT_CODE_FLAG.equals(parentCode)) {
            return orgCode;
        }
        return getFullParentOrgCode(parentCode) + "." + orgCode;
    }


    /**
     * 获取完整机构ID
     *
     * @param orgId       机构ID
     * @param parentOrgId 父机构ID
     * @return 完整的机构编码：<code>根机构ID.二级机构ID.三级机构ID....叶子机构ID</code>
     */
    private String getFullOrgId(Long orgId, Long parentOrgId) {
        if (Objects.equals(OrgConstants.ORG_ROOT_PARENT_ID_FLAG, parentOrgId)) {
            return String.valueOf(orgId);
        }

        return getFullParentOrgId(parentOrgId) + "." + orgId;
    }

    /**
     * 获取机构的完整机构ID
     *
     * @param orgId 父机构ID
     * @return 完整机构ID
     */
    private String getFullParentOrgId(Long orgId) {
        OrgEntity orgEntity = OrgCache.INSTANCE.getOrgById(orgId);
        if (null == orgEntity) {
            return String.valueOf(orgId);
        }

        Long parentId = orgEntity.getParentId();
        if (Objects.equals(OrgConstants.ORG_ROOT_PARENT_ID_FLAG, parentId)) {
            return String.valueOf(orgId);
        }
        return getFullParentOrgId(parentId) + "." + orgId;
    }

    /**
     * 是否是根机构
     *
     * @param orgEntity 待检查机构实体
     * @return true: 根机构 false: 非根机构
     */
    public boolean isRootOrg(OrgEntity orgEntity) {
        // 父机构编码不存在，则认为是根机构
        if (OrgConstants.ORG_ROOT_PARENT_CODE_FLAG.equals(orgEntity.getParentCode())) {
            return true;
        }

        // 机构级别为0，则认为是根机构
        Integer orgLevel = orgEntity.getOrgLevel();

        return null != orgLevel && OrgConstants.ORG_ROOT_LEVEL_FLAG.equals(String.valueOf(orgLevel));
    }

    /**
     * 记录机构操作
     *
     * @param orgEntity
     * @param orgOperationEnum
     */
    public void recordOrgOperationTrace(OrgEntity orgEntity, OrgOperationEnum orgOperationEnum) {
        EntityChangeEventManager.getInstance().publishEvent(
                EntityChangeEvent.newInstance(
                        orgEntity,
                        orgOperationEnum.getDesc(),
                        InvocationType.ADMINISTRATOR_OPERATION)
        );
    }

    /**
     * 填充BatchErrorInfo信息
     *
     * @param orgEntity
     * @param errorMessage
     * @return
     */
    public BatchErrorInfo fullErrorInfo(OrgEntity orgEntity, String errorMessage) {
        BatchErrorInfo errorInfo = new BatchErrorInfo();
        errorInfo.setId(orgEntity.getId());
        errorInfo.setIdentification(orgEntity.getOrgName());
        errorInfo.setMessage(errorMessage);
        return errorInfo;
    }

    /**
     * @param orgIds         已选中机构ID集合
     * @param checkFailedIds 校验不通过机构Id集合
     * @param errorInfoList  失败详情集合
     */
    public void buildCheckFailedOrg(List<Long> orgIds, List<Long> checkFailedIds, List<BatchErrorInfo> errorInfoList) {
        orgIds.forEach(orgId -> {

            if (checkFailedIds.contains(orgId)) {
                return;
            }
            OrgEntity orgEntity = orgRepository.searchById(orgId);
            if (Objects.isNull(orgEntity)) {
                //机构不存在
                BatchErrorInfo errorInfo = new BatchErrorInfo();
                errorInfo.setId(orgId);
                errorInfo.setMessage(BaseValidationError.ORG_NOT_FOUND.getDesc());
                errorInfoList.add(errorInfo);
                checkFailedIds.add(orgId);
                return;
            }
            //删除机构前先检查是否已经绑定
            if (!orgBindCheckService.canDelete(orgId)) {
                BatchErrorInfo errorInfo = fullErrorInfo(orgEntity, BaseInternalError.ORG_DELETE_BINDING_ERROR
                        .getDesc());
                errorInfoList.add(errorInfo);
                checkFailedIds.add(orgId);
                return;
            }

            //非叶子机构
            if (OrgCache.INSTANCE.hasSubOrg(orgEntity.getId())) {
                //获取当前机构的所有的子、孙等机构
                List<OrgEntity> dbSubOrgList = OrgCache.INSTANCE.getAllSubOrgList(orgEntity.getId());
                List<Long> dbSubOrgIds = dbSubOrgList.stream()
                        .filter(item -> EntityStatus.NORMAL.getId().equals(item.getOrgStatus()))
                        .map(OrgEntity::getId)
                        .collect(Collectors.toList());
                //如果当前层级机构选中，下面层级机构部分选中，则提示错误当前层级机构不能删除
                if (!orgIds.containsAll(dbSubOrgIds)) {
                    BatchErrorInfo errorInfo = fullErrorInfo(orgEntity, BaseValidationError.ORG_BATCH_DELETE_NOT_SELECTED_ALL_SUB_ORG_ERROR.getDesc());
                    errorInfoList.add(errorInfo);
                    checkFailedIds.add(orgId);

                }
            }
        });
    }

    /**
     * 设置根机构排序码
     */
    public void setRootOrgIndexOrder() {
        //设置根机构排序码
        OrgEntity orgEntity = orgRepository.searchById(OrgConstants.ORG_ROOT_ID);
        if (orgEntity.getOrderIndex() != 0) {
            return;
        }
        OrgConfig orgConfig = BaseConfigWrapper.getOrgConfig();
        Integer orderIndexSeq = orgConfig.getOrderIndexSeq();
        orgEntity.setOrderIndex(orderIndexSeq);
        orgEntity.setOrderFullIndex(String.valueOf(orderIndexSeq));
        orgRepository.update(orgEntity);
    }

    /**
     * 获取机构排序码IndexOrder
     *
     * @return
     */
    public Integer generateIndexOrder() {
        OrgConfig orgConfig = BaseConfigWrapper.getOrgConfig();
        Integer orderIndexSeq = orgConfig.getOrderIndexSeq();
        AtomicInteger atomicInteger = new AtomicInteger(orderIndexSeq);
        int orderIndex = atomicInteger.decrementAndGet();
        orgConfig.setOrderIndexSeq(orderIndex);
        return orderIndex;
    }

    /**
     * 机构排序完整码IndexOrder
     *
     * @return
     */
    public String buildFullIndexOrder(Integer orderIndex, String parentCode) {
        OrgEntity orgParentEntity = orgRepository.searchByOrgCode(parentCode);

        if (ObjectUtils.isEmpty(orgParentEntity)) {
            return String.valueOf(orderIndex);
        }

        String orderFullIndex = orgParentEntity.getOrderFullIndex();
        StringBuilder strBuilder = new StringBuilder();

        if (StringUtils.isEmpty(orderFullIndex)) {
            throw BaseValidationError.ORG_ADD_EQ_ERROR.toException();
        }
        strBuilder.append(orderFullIndex).append(">").append(orderIndex);
        return strBuilder.toString();
    }


}