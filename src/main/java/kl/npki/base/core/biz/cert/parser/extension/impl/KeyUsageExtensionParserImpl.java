package kl.npki.base.core.biz.cert.parser.extension.impl;

import kl.nbase.security.asn1.x509.KeyUsage;
import kl.npki.base.core.biz.cert.parser.constant.ExtensionType;
import kl.npki.base.core.biz.cert.parser.constant.KeyUsageEnum;
import kl.npki.base.core.biz.cert.parser.extension.AbstractX509ExtensionParser;

import java.util.Arrays;

/**
 * 密钥用法扩展解码器
 *
 * <AUTHOR>
 * @date 2024-08-08
 */
public class KeyUsageExtensionParserImpl extends AbstractX509ExtensionParser {

    @Override
    public ExtensionType getExtensionType() {
        return ExtensionType.KEY_USAGE;
    }

    @Override
    protected String generatePrettyValue(byte[] extensionOctets) {

        KeyUsage keyUsage = KeyUsage.getInstance(extensionOctets);

        StringBuilder sb = new StringBuilder();
        Arrays.stream(KeyUsageEnum.values()).forEach(keyUsageEnum -> {
            if (keyUsage.hasUsages(keyUsageEnum.getCode())) {
                if (sb.length() > 0) {
                    sb.append(", ");
                }
                sb.append(keyUsageEnum.getDescription());
            }
        });

        return sb.toString();
    }
}
