package kl.npki.base.core.biz.log.service;

import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.helper.office.excel.ExternalPrivateKeyExcelSigner;
import kl.npki.base.core.biz.log.model.SystemLogFileEntity;
import kl.npki.base.core.common.office.excel.ExcelCommonExporter;
import kl.npki.base.core.common.office.excel.ExcelWithKeyExporter;
import kl.npki.base.core.constant.SystemLogEnum;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.repository.ILogBaseRepository;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPrivateKey;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 日志导出服务接口
 *
 * <AUTHOR>
 * @create 2025/2/11 下午5:49
 */
public interface IExportLogService<T> {

    /**
     * 导出Excel格式日志文件
     *
     * @param searchCondition    查询导出数据的条件，如果数据仓库是数据库，则是：QueryWrapper类型，如果是ES，则是SearchSourceBuilder类型
     * @param enableFieldMapping 需要导出的字段映射，key为字段名，value为字段中文名
     * @return 导出结果
     */
    default File getExportFile(Object searchCondition, Map<String, List<String>> enableFieldMapping) {
        ExcelCommonExporter<T> exporter = new ExcelCommonExporter<>(getEntityClass(),
                null,
                enableFieldMapping);
        writeData(searchCondition, exporter);
        return exporter.doFinal();
    }

    /**
     * 导出带签名的Excel日志文件，用于可以传入私钥的场景
     *
     * @param searchCondition    查询导出数据的条件，如果数据仓库是数据库，则是：QueryWrapper类型，如果是ES，则是SearchSourceBuilder类型
     * @param enableFieldMapping 需要导出的字段映射，key为字段名，value为字段中文名
     * @param privateKey         对Excel文件做签名的私钥
     * @param certChain          签名证书的证书链
     * @return 带签名的Excel日志文件
     */
    default File getSignatureWithKeyExportFile(Object searchCondition,
                                               Map<String, List<String>> enableFieldMapping,
                                               PrivateKey privateKey,
                                               List<X509Certificate> certChain) {
        ExcelCommonExporter<T> exporter = new ExcelWithKeyExporter<>(
                getEntityClass(),
                null,
                enableFieldMapping,
                (RSAPrivateKey) privateKey,
                certChain);
        writeData(searchCondition, exporter);
        return exporter.doFinal();
    }

    /**
     * 获取签名原文，用于无法传入私钥的场景中（比如密码机中内置私钥，Ukey中的私钥等）签名是由第三方生成的，
     * 因此此接口用于获取签名原文，然后将签名原文发送给第三方进行签名
     *
     * @param excelFile excel文件
     * @param certChain 证书链
     * @param date      签名时间
     * @return 签名原文
     */
    default byte[] getExcelFilePlaintext(File excelFile, List<X509Certificate> certChain, Date date) {
        ExternalPrivateKeyExcelSigner signer = new ExternalPrivateKeyExcelSigner(certChain);
        try {
            return signer.getSignaturePlaintext(excelFile, date);
        } catch (Exception e) {
            throw BaseInternalError.FILE_PALINTEXT_FAIL.toException(e.getMessage(), e);
        }
    }

    /**
     * 更新Excel文件中的签名值，用于将第三方签名后的签名值更新到Excel文件中。
     * 此接口应该与{@link IExportLogService#getExcelFilePlaintext(File, List, Date)} 配合使用
     * <pre>
     *   <b>代码示例：</b>
     *   byte[] plaintext = getExcelFilePlaintext(excelFile, certChain, date);
     *   byte[] signature = 第三方签名(plaintext);
     *   File signedExcel =
     *             updateExcelFileSignature(excelFile, certChain, date, signature);
     *
     *   注意：
     *   此接口不会对Excel文件进行签名，只是将签名值更新到Excel文件中
     *   此接口传入的文件、证书链、签名时间应该与{@link IExportLogService#getExcelFilePlaintext(File, List, Date)} 接口传入的一致
     * </pre>
     *
     * @param excelFile excel文件
     * @param date      签名时间
     * @param signature 签名值
     */
    default void updateExcelFileSignature(File excelFile, List<X509Certificate> certChain, Date date, byte[] signature) {
        ExternalPrivateKeyExcelSigner signer = new ExternalPrivateKeyExcelSigner(certChain);
        try {
            // 更新Excel文件中的签名值
            byte[] signedExcel = signer.sign(excelFile, date, signature);
            FileUtils.writeByteArrayToFile(excelFile, signedExcel);
        } catch (Exception e) {
            throw BaseInternalError.FILE_SIGNATURE_FAIL.toException(e.getMessage(), e);
        }
    }

    /**
     * 获取日志操作数据仓库
     *
     * @return 日志操作数据仓库
     */
    ILogBaseRepository<T> getLogRepository();

    /**
     * 获取日志实体类型
     *
     * @return Class<T>
     */
    Class<T> getEntityClass();

    default void writeData(Object searchCondition, ExcelCommonExporter<T> exporter) {
        long pageSize = 500;
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(pageSize);
        do {
            // 查询数据
            List<T> records = getLogRepository().page(pageInfo, searchCondition);

            // 写入数据
            exporter.write(records);

            // 检查数据是否查询完成
            if (records.size() < pageSize) {
                break;
            }

            // 查询下一页数据
            pageInfo.setCurrentPage(pageInfo.getCurrentPage() + 1);
        } while (true);
    }

    /**
     * 通过文件id获取文件并写入内容
     *
     * @param fileId 文件id
     * @return 文件对象
     */
    File writeFileContentToFile(Long fileId);

    /**
     * 将查询记录转换成文件并保存到数据库中
     *
     * @param searchCondition    查询条件
     * @param enableFieldMapping 字段映射
     * @param systemLogEnum      文件类型
     * @return 文件id
     */
    Long getExportFileAndSaveDb(Object searchCondition, Map<String, List<String>> enableFieldMapping, SystemLogEnum systemLogEnum);

    /**
     * 将查询记录转换成文件并签名后保存到数据库中，暂时未使用待保留
     *
     * @param searchCondition    查询条件
     * @param enableFieldMapping 字段映射
     * @param systemLogEnum      文件类型
     * @param certChain          证书链
     * @return Long
     */
    Long getExportSignedFileAndSaveDb(Object searchCondition, Map<String, List<String>> enableFieldMapping,
                                      SystemLogEnum systemLogEnum, List<X509Certificate> certChain);
    /**
     * 查询日志文件列表
     *
     * @param fileType 文件类型
     * @return List<SystemLogFileEntity>
     */
    List<SystemLogFileEntity> searchSystemLogFileList(Integer fileType);

    /**
     * 查询日志文件状态
     *
     * @param id 文件id
     * @return SystemLogFileEntity
     */
    SystemLogFileEntity searchSystemLogFileStatus(Long id);

    /**
     * 查询日志文件信息
     *
     * @param id 文件id
     * @return SystemLogFileEntity
     */
    SystemLogFileEntity searchSystemLogFile(Long id);

    /**
     * 删除日志文件
     *
     * @param id 文件id
     */
    void deleteTempFile(Long id);

}