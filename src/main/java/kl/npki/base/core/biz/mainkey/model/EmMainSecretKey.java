package kl.npki.base.core.biz.mainkey.model;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/14 20:28
 * @Description 主密钥导出结构
 */
public class EmMainSecretKey implements Serializable {

    /**
     * b64 对称密钥加密值
     */
    private final String b64EncMainKeyValue;

    /**
     * b64 公钥ID(密码机中加密对称密钥的非对称密钥对)
     */
    private final String b64PublicKeyId;

    /**
     *  b64 对称密钥拼接 加密机私钥
     */
    private final String b64RefValue;


    public EmMainSecretKey(String b64EncMainKeyValue, String b64PublicKeyId, String b64RefValue) {
        this.b64EncMainKeyValue = b64EncMainKeyValue;
        this.b64PublicKeyId = b64PublicKeyId;
        this.b64RefValue = b64RefValue;
    }

    public String getB64EncMainKeyValue() {
        return b64EncMainKeyValue;
    }

    public String getB64PublicKeyId() {
        return b64PublicKeyId;
    }

    public String getB64RefValue() {
        return b64RefValue;
    }
}
