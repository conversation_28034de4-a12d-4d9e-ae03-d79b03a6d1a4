package kl.npki.base.core.biz.cert.parser.extension;

import kl.nbase.security.asn1.x509.Extension;
import kl.nbase.security.util.encoders.Base64;
import kl.npki.base.core.biz.cert.model.parser.x509.ExtensionInfo;
import kl.npki.base.core.biz.cert.model.parser.x509.ExtensionInfoBuilder;
import kl.npki.base.core.biz.cert.parser.constant.ExtensionType;

import java.io.IOException;

/**
 * 扩展项解析基类
 *
 * <AUTHOR>
 * @date 2024-08-08
 */
public abstract class AbstractX509ExtensionParser implements IX509ExtensionParser {

    /**
     * 换行符
     */
    protected static final String LINE_SEPARATOR = System.lineSeparator();

    /**
     * 制表符
     */
    protected static final String TAB = "\t";

    /**
     * 等于符号
     */
    public static final String EQUAL_SIGN = "=";

    /**
     * 冒号
     */
    public static final String COLON = ":";

    @Override
    public ExtensionInfo parse(Extension extension) throws IOException {

        byte[] extensionOctets = extension.getExtnValue().getOctets();

        String asnValue = Base64.toBase64String(extension.getEncoded());
        String prettyValue = generatePrettyValue(extensionOctets);
        String value = generateValue(extensionOctets);

        ExtensionType extensionType = getExtensionType();

        return ExtensionInfoBuilder.builder()
            .withCritical(extension.isCritical())
            .withName(extensionType.getName())
            .withDesc(extensionType.getDesc())
            .withOid(extensionType.getObjectIdentifier().getId())
            .withAsnValue(asnValue)
            .withValue(value)
            .withPrettyValue(prettyValue)
            .build();
    }

    /**
     * 生成一个易于阅读的扩展值字符串，此方法旨在将字节数组形式的扩展值转换为便于人类阅读和理解的字符串格式
     * <p>
     * 具体的转换逻辑由子类实现，因为不同的场景可能需要不同的格式化规则
     *
     * @param extensionOctets 扩展值的字节表示形式
     * @return 格式化后的字符串
     */
    protected abstract String generatePrettyValue(byte[] extensionOctets);


    /**
     * 生成一个扩展值字符
     * <p>
     * 默认等同{@link AbstractX509ExtensionParser#generatePrettyValue(byte[])}，可由子类扩展
     * 因为不同的场景可能需要不同的格式化规则，有的与prettyValue不同，则prettyValue不能满足，需要重写此方法
     *
     * @param extensionOctets 
     * @return 字符串格式的扩展值
     */
    protected String generateValue(byte[] extensionOctets) {
        return generatePrettyValue(extensionOctets);
    }
}
