package kl.npki.base.core.biz.cert.service;

import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.security.asn1.*;
import kl.nbase.security.asn1.pkcs.CertificationRequest;
import kl.nbase.security.asn1.pkcs.CertificationRequestInfo;
import kl.nbase.security.asn1.x500.X500Name;
import kl.nbase.security.asn1.x509.AlgorithmIdentifier;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.asn1.x509.SubjectPublicKeyInfo;
import kl.nbase.security.asn1.x509.TBSCertificate;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.nbase.security.pkcs.PKCS10CertificationRequest;
import kl.nbase.security.utils.SignatureAlgoUtil;
import kl.npki.base.core.biz.cert.model.template.*;
import kl.npki.base.core.constant.CertTypeEnum;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.base.core.utils.KeyUtils;

import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Date;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.*;
/**
 * ֤证书签发帮助类
 *
 * <AUTHOR>
 * @date 2022/12/22
 */
public class CertSignService {

    private CertSignService() {
        throw new UnsupportedOperationException("CertSignService is a utility class and cannot be instantiated");
    }

    /**
     * 证书签发
     *
     * @param clusterEngine
     * @param certType
     * @param subjectDn
     * @param publicKey
     * @param validDays
     * @param issuerCert
     * @return
     * @throws Exception
     */
    public static Certificate issueCert(ClusterEngine clusterEngine, CertTypeEnum certType, String subjectDn,
                                        PublicKey publicKey, long validDays,
                                        Certificate issuerCert, String subjectAlternativeName) throws Exception {
        return issueCert(clusterEngine, certType, subjectDn, publicKey, new Date(),
                validDays, issuerCert, subjectAlternativeName);
    }

    /**
     * 证书签发，适用新签的情况
     *
     * @param clusterEngine 密码机引擎
     * @param certType      证书类型{@link CertTypeEnum}
     * @param subjectDn     证书主题DN
     * @param publicKey     证书公钥
     * @param validateFrom  证书生效时间
     * @param validDays     证书有效天数
     * @param issuerCert    签发者证书
     * @return 签发的证书
     * @throws Exception 签发证书 异常
     */
    public static Certificate issueCert(ClusterEngine clusterEngine, CertTypeEnum certType, String subjectDn,
                                        PublicKey publicKey, Date validateFrom, long validDays,
                                        Certificate issuerCert, String subjectAlternativeName) throws Exception {
        return issueCert(clusterEngine, certType, subjectDn, publicKey, validateFrom,
                validDays, issuerCert, null, subjectAlternativeName);
    }

    /**
     * 证书签发，延期、新签通用
     *
     * @param clusterEngine 密码机引擎
     * @param certType      证书类型{@link CertTypeEnum}
     * @param subjectDn     证书主题DN
     * @param publicKey     证书公钥
     * @param validateFrom  证书生效时间
     * @param validDays     证书有效天数
     * @param issuerCert    签发者证书
     * @param serialNumber  证书序列号(可选)
     * @param subjectAlternativeName  证书主体备用名，仅对SSL证书有效
     * @return 签发的证书
     * @throws Exception 签发证书 异常
     */
    public static Certificate issueCert(ClusterEngine clusterEngine, CertTypeEnum certType, String subjectDn,
                                        PublicKey publicKey, Date validateFrom, long validDays,
                                        Certificate issuerCert, ASN1Integer serialNumber,
                                        String subjectAlternativeName) throws Exception {
        if (validDays < 1) {
            throw BaseInternalError.CERT_ISSUE_ERROR.toException(THE_VALIDDAYS_IS_LESS_THAN_1_I18N_KEY);
        }
        X500Name subject = new X500Name(subjectDn);
        Date[] certValidity = CertUtil.getCertValidTime(validateFrom, validDays);
        ASN1ObjectIdentifier signAlgoOid = issuerCert.getSignatureAlgorithm().getAlgorithm();
        HashAlgo hashAlgo = SignatureAlgoUtil.getHashAlgoByOid(signAlgoOid.getId());
        AbstractCertTemplate certTemp;
        switch (certType) {
            case USER_SIGN_CERT:
                certTemp = new UserCertTemplate(subject, publicKey, certValidity[0], certValidity[1], issuerCert,
                    signAlgoOid.getId());
                break;
            case SSL_SIGN_SERVER_CERT:
                certTemp = new SSLServerCertTemplate(subject, publicKey, certValidity[0], certValidity[1], issuerCert,
                    signAlgoOid.getId(), subjectAlternativeName);
                break;
            case SSL_SIGN_CLIENT_CERT:
                certTemp = new SSLClientCertTemplate(subject, publicKey, certValidity[0], certValidity[1], issuerCert,
                    signAlgoOid.getId());
                break;
            case USER_ENC_CERT:
                certTemp = new EncryptCertTemplate(subject, publicKey, certValidity[0], certValidity[1], issuerCert,
                    signAlgoOid.getId());
                break;
            case SSL_ENC_SERVER_CERT:
                certTemp = new SSLEncServerCertTemplate(subject, publicKey, certValidity[0], certValidity[1], issuerCert,
                    signAlgoOid.getId(), subjectAlternativeName);
                break;
            case SSL_ENC_CLIENT_CERT:
                certTemp = new SSLEncClientCertTemplate(subject, publicKey, certValidity[0], certValidity[1], issuerCert,
                    signAlgoOid.getId());
                break;
            default:
                throw BaseInternalError.CERT_ISSUE_ERROR.toException(THE_CERT_TYPE_IS_NOT_SUPPORTED_I18N_KEY, certType.name());
        }
        // 设置证书序列号，适用证书延期的情况
        certTemp.setSerialNumber(serialNumber);

        PublicKey issueCertpublicKey = KeyUtils.getPublicKey(issuerCert.getSubjectPublicKeyInfo());

        PrivateKey issuerPriKey = clusterEngine.getKeyPair(issueCertpublicKey).getPrivate();

        return certSignature(clusterEngine, issuerPriKey, certTemp.create(), hashAlgo);
    }

    /**
     * 自签发证书
     *
     * @param clusterEngine
     * @param subjectDn
     * @param keyPair
     * @param validDays
     * @return
     * @throws Exception
     */
    public static Certificate issueSelfSignCert(ClusterEngine clusterEngine, String subjectDn, KeyPair keyPair,
                                                long validDays) throws Exception {
        if (validDays < 1) {
            throw BaseInternalError.ROOT_CERT_ISSUER_ERROR.toException(THE_VALIDDAYS_IS_LESS_THAN_1_I18N_KEY);
        }
        if (keyPair == null) {
            throw BaseInternalError.KEY_PAIR_GET_ERROR.toException(THE_KEY_PAIR_IS_NULL_I18N_KEY);
        }
        X500Name subject = new X500Name(subjectDn);
        Date[] certValidity = CertUtil.getCertValidTimeFromNow(validDays);
        HashAlgo hashAlgo = CertUtil.getDefaultHashAlgo(keyPair.getPublic());
        String defaultSignAlgoOid = CertUtil.getDefaultSignAlgoOid(keyPair.getPublic());
        SelfSignCertTemplate selfSignCertTemp = new SelfSignCertTemplate(subject, keyPair.getPublic(), certValidity[0],
            certValidity[1], defaultSignAlgoOid);
        return certSignature(clusterEngine, keyPair.getPrivate(), selfSignCertTemp.create(), hashAlgo);

    }

    /**
     * 签发证书
     *
     * @param clusterEngine
     * @param priKey
     * @param tbsCert
     * @param digestAlgo
     * @return
     */
    public static Certificate certSignature(ClusterEngine clusterEngine, PrivateKey priKey, TBSCertificate tbsCert,
                                            HashAlgo digestAlgo) {
        byte[] sign;
        try {
            sign = clusterEngine.sign(priKey, tbsCert.getEncoded(ASN1Encoding.DER), digestAlgo, null);
        } catch (Exception e) {
            throw BaseInternalError.SIGNING_CERT_DATA_ERROR.toException(e);
        }
        ASN1EncodableVector v = new ASN1EncodableVector();
        v.add(tbsCert);
        v.add(tbsCert.getSignature());
        v.add(new DERBitString(sign));
        return Certificate.getInstance(new DERSequence(v));
    }

    /**
     * 生成证书请求（p10请求）
     *
     * @param clusterEngine
     * @param subjectDn
     * @param keyPair
     * @return
     */
    public static PKCS10CertificationRequest genCertRequest(ClusterEngine clusterEngine, String subjectDn,
                                                            KeyPair keyPair) {
        X500Name subject = new X500Name(subjectDn);
        SubjectPublicKeyInfo subjectPKInfo = SubjectPublicKeyInfo.getInstance(keyPair.getPublic().getEncoded());
        CertificationRequestInfo reqInfo = new CertificationRequestInfo(subject, subjectPKInfo, new DERSet());
        String sigAlgoOid = CertUtil.getDefaultSignAlgoOid(keyPair.getPublic());
        HashAlgo defaultHashAlgo = CertUtil.getDefaultHashAlgo(keyPair.getPublic());
        DERBitString signature = null;
        try {
            // 对请求作签名
            byte[] toBeSigned = reqInfo.getEncoded(ASN1Encoding.DER);
            byte[] sig = clusterEngine.sign(keyPair.getPrivate(), toBeSigned, defaultHashAlgo, null);
            signature = new DERBitString(sig);
        } catch (Exception e) {
            throw BaseInternalError.CERT_REQ_GEN_ERROR.toException(e);
        }
        AlgorithmIdentifier sigAlgId = new AlgorithmIdentifier(new ASN1ObjectIdentifier(sigAlgoOid), DERNull.INSTANCE);
        CertificationRequest certificationRequest = new CertificationRequest(reqInfo, sigAlgId, signature);
        return new PKCS10CertificationRequest(certificationRequest);
    }


}
