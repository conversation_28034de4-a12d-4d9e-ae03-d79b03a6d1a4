package kl.npki.base.core.biz.trace.model;


import kl.npki.base.core.common.trace.InvocationType;

/**
 * 实体轨迹变更参数
 * <AUTHOR>
 */
public class EntityChangeParam {

    /**
     * 实体数据
     */
    private Object entity;
    /**
     * 业务执行名称
     */
    private String bizName;

    /**
     * 业务调用方式
     */
    private InvocationType invocationType;

    /**
     * 扩展参数
     */
    private Object extParams;

    public EntityChangeParam() {
    }

    public EntityChangeParam(Object entity, String bizName, InvocationType invocationType) {
        this.entity = entity;
        this.bizName = bizName;
        this.invocationType = invocationType;
    }

    public EntityChangeParam(Object entity, String bizName, InvocationType invocationType, Object extParams) {
        this.entity = entity;
        this.bizName = bizName;
        this.invocationType = invocationType;
        this.extParams = extParams;
    }

    public Object getEntity() {
        return entity;
    }

    public void setEntity(Object entity) {
        this.entity = entity;
    }

    public String getBizName() {
        return bizName;
    }

    public void setBizName(String bizName) {
        this.bizName = bizName;
    }

    public InvocationType getInvocationType() {
        return invocationType;
    }

    public Object getExtParams() {
        return extParams;
    }

    public void setExtParams(Object extParams) {
        this.extParams = extParams;
    }

    public void setInvocationType(InvocationType invocationType) {
        this.invocationType = invocationType;
    }
}
