package kl.npki.base.core.biz.metrics.service;

import kl.npki.base.core.biz.metrics.model.CertValidInfo;
import kl.npki.base.core.biz.metrics.model.LicenseStatusInfo;
import kl.npki.base.core.common.ICommonCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: guoq
 * @Date: 2024/10/18
 * @description: 指标缓存
 */
public enum GaugeMetricsCache implements ICommonCache {

    /**
     * 单例对象
     */
    INSTANCE;

    private static final String CACHE_ID = "base:metrics";
    private static final String CERT_VALID = "cert";
    private static final String LICENSE_STATUS = "license";
    /**
     * 缓存失效时间默认两个小时
     */
    private static final int TIME_TO_LIVE_MILLISECONDS = 7200000;
    private static final Logger log = LoggerFactory.getLogger(GaugeMetricsCache.class);

    @Override
    public String getId() {
        return CACHE_ID;
    }

    public void putCertMetricsResult(List<CertValidInfo> certValidInfoList) {
        try {
            getClient().set(wrapCacheKey(CERT_VALID), (Serializable) certValidInfoList, TIME_TO_LIVE_MILLISECONDS);
        } catch (Exception e) {
            // 缓存加载不影响业务正常执行，所以直接记录日志
            log.error("cert metrics result put to cache error", e);
        }
    }

    public List<CertValidInfo> getCertMetricsResult() {
        return getClient().get(wrapCacheKey(CERT_VALID));
    }

    public void putLicenseMetricsResult(LicenseStatusInfo licenseMetrics) {
        try {
            getClient().set(wrapCacheKey(LICENSE_STATUS), licenseMetrics, TIME_TO_LIVE_MILLISECONDS);
        } catch (Exception e) {
            // 缓存加载不影响业务正常执行，所以直接记录日志
            log.error("license metrics result put to cache error", e);
        }
    }

    public LicenseStatusInfo getLicenseMetricsResult() {
        return getClient().get(wrapCacheKey(LICENSE_STATUS));
    }
}
