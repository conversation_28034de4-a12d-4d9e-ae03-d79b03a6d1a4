package kl.npki.base.core.biz.cert.service;

import kl.nbase.security.asn1.ASN1Encoding;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.asn1.x509.Extension;
import kl.nbase.security.asn1.x509.KeyUsage;
import kl.npki.base.core.common.crypto.SignatureHelper;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.core.utils.CertExtensionUtils;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.base.core.utils.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Date;
import java.util.Map;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.*;

/**
 * <AUTHOR>
 * @Date 2024/1/23 17:04
 * @Description:
 */
public class CertVerifyHelper {

    private static final Logger logger = LoggerFactory.getLogger(CertVerifyHelper.class);

    private CertVerifyHelper() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 验证证书是否合法
     *
     * @param cert     证书
     * @param trustMap 信任证书 map<证书DN,证书>
     * @return 验证通过
     */
    public static boolean verifyCertChain(Certificate cert,
                                          Map<String, Certificate> trustMap) {

        if (isRoot(cert)) {
            checkValidity(cert);
            return verifyCertSign(cert, cert);
        }
        if (trustMap != null && trustMap.containsKey(cert.getIssuer().toString())) {
            Certificate trustCert = trustMap.get(cert.getIssuer().toString());
            checkValidity(cert);
            return verifyCertSign(trustCert, cert);
        }
        return false;
    }

    private static boolean isRoot(Certificate cert) {
        return cert.getSubject().toString().equals(
            cert.getIssuer().toString());
    }

    /**
     * 验证证书合法性：非自签发证书，必须传issuerCerts参数。只要issuerCerts中有一张证书验证通过，即代表cert（待验证证书）合法
     *
     * @param cert        待验证证书
     * @param issuerCerts 用于验证cert合法性的CA证书
     * @return true 证书合法；false 证书不合法
     * @throws Exception 异常
     */
    public static boolean verifyCertByCertchain(Certificate cert, Certificate... issuerCerts) {
        return java.util.Arrays.stream(issuerCerts).anyMatch(issuerCert ->
            verifyCertSign(issuerCert, cert));
    }

    public static boolean verifyCertSign(Certificate issuerCert, Certificate cert) {
        try {
            return issuerCert.getSubject().toString().equals(cert.getIssuer().toString()) &&
                SignatureHelper.verify(issuerCert, cert.getSignature().getOctets(),
                    cert.getTBSCertificate().getEncoded(ASN1Encoding.DER));
        } catch (IOException e) {
            logger.error(BaseInternalError.CERT_ENCODE_ERROR.toException().toString(), e);
        }
        return false;
    }

    /**
     * 通过检查证书有效期，判断证书是否有效
     *
     * @param certificate 待验证证书
     * @return 信任证书状态（有效：在有效期内，无效：不在有效期）
     */
    public static void checkValidity(Certificate certificate) {
        Date date = new Date();
        if (certificate.getStartDate().getDate().after(date)) {
            throw BaseValidationError.CERT_NOT_YET_VALID_ERROR.toException(
                THE_CERTIFICATE_HAS_NOT_YET_TAKEN_EFFECT_WITH_AN_EFFECTIVE_DATE_OF_I18N_KEY,
                certificate.getSubject().toString(), certificate.getStartDate().getDate());
        }

        if (certificate.getEndDate().getDate().before(date)) {
            throw BaseValidationError.CERT_EXPIRED_ERROR.toException(
                THE_CERTIFICATE_HAS_EXPIRED_WITH_AN_EXPIRATION_DATE_OF_I18N_KEY,
                certificate.getSubject().toString(), certificate.getEndDate().getDate());
        }
    }


    /**
     * 验证证书有效期
     * 有效期不能超过签发者证书的有效期
     *
     * @param validDays
     * @param issueCert
     */
    public static void verifyValidateWithIssuer(Long validDays, Certificate issueCert) {
        Date[] timeFromNow = CertUtil.getCertValidTimeFromNow(validDays);
        // 获取证书截止日期
        Date validateTo = timeFromNow[1];
        if (issueCert.getEndDate().getDate().before(validateTo)) {
            // 抛出证书有效期配置超范围相关的异常
            throw BaseValidationError.VALIDITY_GT_ISSUE_CERT.toException(
                CERTIFICATE_VALIDITY_EXCEEDS_ISSUER_VALIDITY_I18N_KEY, DateUtil.formatFrontDate(validateTo),
                DateUtil.formatFrontDate(issueCert.getEndDate().getDate()));
        }

    }

    /**
     * 验证证书有效期
     * 有效期不能超过签发者证书的有效期
     *
     * @param validFrom 证书生效日期
     * @param validDays 证书有效天数
     * @param issueCert 签发者证书
     */
    public static void verifyValidateWithIssuer(Date validFrom, Long validDays, Certificate issueCert) {
        Date[] validateArr = CertUtil.getCertValidTime(validFrom, validDays);
        // 获取证书截止日期
        Date validateTo = validateArr[1];
        if (issueCert.getEndDate().getDate().before(validateTo)) {
            // 抛出证书有效期配置超范围相关的异常
            throw BaseValidationError.VALIDITY_GT_ISSUE_CERT.toException(
                CERTIFICATE_VALIDITY_EXCEEDS_ISSUER_VALIDITY_I18N_KEY, DateUtil.formatFrontDate(validateTo),
                DateUtil.formatFrontDate(issueCert.getEndDate().getDate()));
        }

    }

    /**
     * 检查证书是否为签名证书
     * 通过检查证书的密钥用途扩展中是否包含数字签名(digitalSignature)或不可否认(nonRepudiation)用途
     *
     * @param cert 待检查的证书
     * @return 如果是签名证书返回true，否则返回false
     */
    public static boolean isSigningCert(Certificate cert) {
        try {
            Extension keyUsageExt = CertExtensionUtils.getExtension(cert, Extension.keyUsage);
            if (keyUsageExt == null) {
                // 如果没有密钥用途扩展，默认返回false
                return false;
            }

            KeyUsage keyUsage = KeyUsage.getInstance(keyUsageExt.getParsedValue());
            // 检查是否有数字签名或不可否认用途
            return keyUsage.hasUsages(KeyUsage.digitalSignature) ||
                keyUsage.hasUsages(KeyUsage.nonRepudiation);
        } catch (Exception e) {
            logger.error("Error occurred while checking if certificate is a signing certificate", e);
            return false;
        }
    }

    /**
     * 检查证书是否为加密证书
     * 通过检查证书的密钥用途扩展中是否包含密钥加密(keyEncipherment)或数据加密(dataEncipherment)用途
     *
     * @param cert 待检查的证书
     * @return 如果是加密证书返回true，否则返回false
     */
    public static boolean isEncryptionCert(Certificate cert) {
        try {
            Extension keyUsageExt = CertExtensionUtils.getExtension(cert, Extension.keyUsage);
            if (keyUsageExt == null) {
                // 如果没有密钥用途扩展，默认返回false
                return false;
            }

            KeyUsage keyUsage = KeyUsage.getInstance(keyUsageExt.getParsedValue());
            // 检查是否有密钥加密或数据加密用途
            return keyUsage.hasUsages(KeyUsage.keyEncipherment) ||
                keyUsage.hasUsages(KeyUsage.dataEncipherment);
        } catch (Exception e) {
            logger.error("Error occurred while checking if certificate is a encryption certificate", e);
            return false;
        }
    }

    /**
     * 获取证书的密钥用途
     * 返回证书的密钥用途扩展的整数值，可以通过位运算判断具体用途
     *
     * @param cert 待检查的证书
     * @return 密钥用途的整数值，如果没有密钥用途扩展则返回0
     */
    public static int getCertKeyUsage(Certificate cert) {
        try {
            Extension keyUsageExt = CertExtensionUtils.getExtension(cert, Extension.keyUsage);
            if (keyUsageExt == null) {
                return 0;
            }

            KeyUsage keyUsage = KeyUsage.getInstance(keyUsageExt.getParsedValue());
            return keyUsage.getBytes()[0] & 0xff;
        } catch (Exception e) {
            logger.error("An error occurred while getting the certificate key usage", e);
            return 0;
        }
    }
}
