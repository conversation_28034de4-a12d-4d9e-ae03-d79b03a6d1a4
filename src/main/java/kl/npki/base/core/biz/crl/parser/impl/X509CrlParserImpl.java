package kl.npki.base.core.biz.crl.parser.impl;

import kl.nbase.security.asn1.ASN1Enumerated;
import kl.nbase.security.asn1.ASN1ObjectIdentifier;
import kl.nbase.security.asn1.ASN1Primitive;
import kl.nbase.security.asn1.x509.*;
import kl.nbase.security.crypto.provider.digest.DigestProvider;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.nbase.security.util.encoders.Hex;
import kl.npki.base.core.biz.cert.model.parser.x509.AttributeInfo;
import kl.npki.base.core.biz.cert.model.parser.x509.AttributeInfoBuilder;
import kl.npki.base.core.biz.cert.model.parser.x509.BasicInfo;
import kl.npki.base.core.biz.cert.model.parser.x509.BasicInfoBuilder;
import kl.npki.base.core.biz.cert.parser.constant.CrlReasonEnum;
import kl.npki.base.core.biz.crl.model.parser.x509.CrlInfo;
import kl.npki.base.core.biz.crl.model.parser.x509.CrlInfoBuilder;
import kl.npki.base.core.biz.crl.model.parser.x509.RevokedInfo;
import kl.npki.base.core.biz.crl.model.parser.x509.RevokedInfoBuilder;
import kl.npki.base.core.biz.crl.parser.ICrlParser;
import kl.npki.base.core.constant.I18nConstant;
import kl.npki.base.core.constant.SignAlgoEnum;
import kl.npki.base.core.utils.CertExtensionUtils;
import kl.npki.base.core.utils.DateUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigInteger;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * X.509 CRL解码器，将一个证书对象{@link CertificateList}解析成{@link CrlInfo}，便于用户查看证书信息
 *
 * <AUTHOR>
 * @date 2025/5/9
 * @since 1.0
 */
public class X509CrlParserImpl implements ICrlParser<CertificateList> {

    private static final Logger LOGGER = LoggerFactory.getLogger(X509CrlParserImpl.class);
    /**
     * CRL版本号
     */
    private static final String VERSION = "version_number";

    /**
     * CRL 的本次发布日期和时间
     */
    private static final String THIS_UPDATE = "this_update";

    /**
     * 下一次预计发布新 CRL 的时间
     */
    private static final String NEXT_UPDATE = "next_update";

    /**
     * 颁发者
     */
    private static final String ISSUER = "issuer";

    /**
     * 签名算法
     */
    private static final String SIGNATURE_ALGORITHMS = "signature_algorithm";

    /**
     * 签名信息
     */
    private static final String SIGNATURE_INFORMATION = "signature_information";

    /**
     * 指纹
     */
    private static final String FINGERPRINT = "fingerprint";

    @Override
    public CrlInfo parse(CertificateList crl) throws IOException {
        CrlInfoBuilder crlInfoBuilder = CrlInfoBuilder.builder();

        // 设置CRL基本信息
        crlInfoBuilder.withVersion(generateVersionInfo(crl));
        crlInfoBuilder.withIssuer(generateIssuerInfo(crl));
        crlInfoBuilder.withThisUpdate(generateThisUpdateInfo(crl));
        crlInfoBuilder.withNextUpdate(generateNextUpdateInfo(crl));
        crlInfoBuilder.withSignatureAlgorithm(generateSignatureAlgorithms(crl));
        crlInfoBuilder.withSignature(generateSignatureInfo(crl));

        // 设置CRL扩展信息
        Extensions extensions = crl.getTBSCertList().getExtensions();
        crlInfoBuilder.addAllExtensionInfo(CertExtensionUtils.generateExtensionInfo(extensions));

        // 增加常见属性信息
        crlInfoBuilder.addAttributeInfo(generateFingerprintsAttributeInfo(crl, HashAlgo.SHA1));
        crlInfoBuilder.addAttributeInfo(generateFingerprintsAttributeInfo(crl, HashAlgo.SHA256));

        // 设置CRL吊销列表
        crlInfoBuilder.addAllRevokedInfoList(parseRevokedInfos(crl));

        return crlInfoBuilder.build();
    }

    /**
     * 生成CRL版本信息
     *
     * @param crl X.509 CRL对象
     * @return 版本信息
     */
    private BasicInfo generateVersionInfo(CertificateList crl) {
        String value = "V" + crl.getVersionNumber();
        return BasicInfoBuilder.builder()
                .withDesc(I18nConstant.getBaseCoreI18nMessage(VERSION))
                .withValue(value)
                .withPrettyValue(value)
                .build();
    }

    /**
     * 生成CRL颁发者信息
     *
     * @param crl X.509 CRL对象
     * @return 颁发者信息
     */
    private BasicInfo generateIssuerInfo(CertificateList crl) {
        String value = crl.getIssuer().toString();
        String prettyValue = formatIssuerName(value);

        return BasicInfoBuilder.builder()
                .withDesc(I18nConstant.getBaseCoreI18nMessage(ISSUER))
                .withValue(value)
                .withPrettyValue(prettyValue)
                .build();
    }

    /**
     * 格式化颁发者名称，将逗号分隔的RDN转换为多行格式
     *
     * @param issuerName 颁发者名称
     * @return 格式化后的颁发者名称
     */
    private String formatIssuerName(String issuerName) {
        return Arrays.stream(issuerName.split(","))
                .collect(Collectors.joining(System.lineSeparator()));
    }

    /**
     * 生成CRL本次更新时间信息
     *
     * @param crl X.509 CRL对象
     * @return 本次更新时间信息
     */
    private BasicInfo generateThisUpdateInfo(CertificateList crl) {
        String value = DateUtil.formatFrontDate(crl.getThisUpdate().getDate());
        return BasicInfoBuilder.builder()
                .withDesc(I18nConstant.getBaseCoreI18nMessage(THIS_UPDATE))
                .withValue(value)
                .withPrettyValue(value)
                .build();
    }

    /**
     * 生成CRL下次更新时间信息
     *
     * @param crl X.509 CRL对象
     * @return 下次更新时间信息
     */
    private BasicInfo generateNextUpdateInfo(CertificateList crl) {
        String value = DateUtil.formatFrontDate(crl.getNextUpdate().getDate());
        return BasicInfoBuilder.builder()
                .withDesc(I18nConstant.getBaseCoreI18nMessage(NEXT_UPDATE))
                .withValue(value)
                .withPrettyValue(value)
                .build();
    }

    /**
     * 生成CRL签名算法信息
     *
     * @param crl X.509 CRL对象
     * @return 签名算法信息
     */
    private BasicInfo generateSignatureAlgorithms(CertificateList crl) {

        ASN1ObjectIdentifier algorithm = crl.getTBSCertList().getSignature().getAlgorithm();
        SignAlgoEnum signAlgoEnum = SignAlgoEnum.getEnumByOid(algorithm);
        String value = signAlgoEnum.getSignAlgoName();
        String prettyValue = value + "(" + signAlgoEnum.getOid() + ")";
        return BasicInfoBuilder.builder()
                .withDesc(I18nConstant.getBaseCoreI18nMessage(SIGNATURE_ALGORITHMS))
                .withValue(value)
                .withPrettyValue(prettyValue)
                .build();
    }

    /**
     * 生成CRL指纹信息
     *
     * @param crl      X.509 CRL对象
     * @param hashAlgo 哈希算法
     * @return 指纹信息
     */
    private AttributeInfo generateFingerprintsAttributeInfo(CertificateList crl, HashAlgo hashAlgo) {
        byte[] digest = new byte[0];
        try {
            digest = DigestProvider.digest(crl.getEncoded(), hashAlgo);
            return AttributeInfoBuilder.builder()
                    .withDesc(hashAlgo.getAlgoName() + " " + I18nConstant.getBaseCoreI18nMessage(FINGERPRINT))
                    .withValue(Hex.toHexString(digest))
                    .withPrettyValue(Hex.toHexString(digest))
                    .build();
        } catch (Exception e) {
            // 记录异常日志
            LOGGER.warn("Failed to generate fingerprint for CRL", e);
        }
        return null;
    }

    /**
     * 解析CRL中的吊销证书列表，生成吊销证书信息列表
     *
     * @param crl X.509 CRL对象
     * @return 吊销证书信息列表
     */
    private List<RevokedInfo> parseRevokedInfos(CertificateList crl) {
        if (ObjectUtils.isEmpty(crl.getRevokedCertificates())) {
            return Collections.emptyList();
        }

        return Arrays.stream(crl.getRevokedCertificates())
                .filter(revokedCertificate -> revokedCertificate.getUserCertificate() != null && revokedCertificate.getRevocationDate() != null)
                .map(revokedCertificate -> {
                    BigInteger value = revokedCertificate.getUserCertificate().getValue();
                    String reason = generateRevokeReason(revokedCertificate);

                    return RevokedInfoBuilder.builder()
                            .withDecimalSn(value.toString(10))
                            .withHexadecimalSn(value.toString(16))
                            .withRevokeTime(revokedCertificate.getRevocationDate().getDate().toInstant()
                                    .atZone(ZoneId.systemDefault()).toLocalDateTime())
                            .withReason(reason)  // 设置吊销原因
                            .build();
                })
                .collect(Collectors.toList());
    }

    @NotNull
    private String generateRevokeReason(TBSCertList.CRLEntry revokedCertificate) {
        return Optional.ofNullable(revokedCertificate.getExtensions())
                .map(extensions -> extensions.getExtension(Extension.reasonCode))
                .map(extension -> {
                    try {
                        return parseCrlReason(extension.getExtnValue().getOctets());
                    } catch (Exception e) {
                        LOGGER.warn("Failed to parse CRL reason", e);
                        return "";
                    }
                })
                .orElse("");
    }

    /**
     * 解析吊销原因
     *
     * @param extnValue 扩展项值
     * @return 吊销原因
     */
    private String parseCrlReason(byte[] extnValue) {
        ASN1Primitive asn1Primitive = null;
        try {
            asn1Primitive = ASN1Primitive.fromByteArray(extnValue);
            ASN1Enumerated enumerated = ASN1Enumerated.getInstance(asn1Primitive);
            int reasonCode = enumerated.getValue().intValue();
            return CrlReasonEnum.getReasonDescriptionByCode(reasonCode) + "(" + reasonCode + ")";
        } catch (IOException e) {
            LOGGER.error("Failed to parse CRL reason", e);
        }
        return "";
    }


    /**
     * 生成CRL签名信息
     *
     * @param crl X.509 CRL对象
     * @return 签名信息
     */
    private BasicInfo generateSignatureInfo(CertificateList crl) {

        ASN1ObjectIdentifier algorithm = crl.getSignatureAlgorithm().getAlgorithm();
        SignAlgoEnum signAlgoEnum = SignAlgoEnum.getEnumByOid(algorithm);
        String value = signAlgoEnum.getSignAlgoName();
        String prettyValue = Hex.toHexString(crl.getSignature().getBytes());

        return BasicInfoBuilder.builder()
                .withDesc(I18nConstant.getBaseCoreI18nMessage(SIGNATURE_INFORMATION))
                .withValue(value)
                .withPrettyValue(prettyValue)
                .build();
    }
}
