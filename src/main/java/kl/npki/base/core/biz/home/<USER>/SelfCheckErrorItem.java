package kl.npki.base.core.biz.home.model;


import kl.npki.base.core.biz.check.model.Severity;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 自检结果中的异常项
 *
 * <AUTHOR>
 */
public class SelfCheckErrorItem implements Serializable {

    private static final long serialVersionUID = 6158354966794419097L;
    /**
     * 自检项名称
     */
    private String itemName;
    /**
     * 自检时间
     */
    private LocalDateTime checkTime;
    /**
     * 检测失败的严重性
     */
    private Severity severity;

    public String getItemName() {
        return itemName;
    }

    public SelfCheckErrorItem setItemName(String itemName) {
        this.itemName = itemName;
        return this;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public SelfCheckErrorItem setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
        return this;
    }

    public Severity getSeverity() {
        return severity;
    }

    public SelfCheckErrorItem setSeverity(Severity severity) {
        this.severity = severity;
        return this;
    }
}
