package kl.npki.base.core.biz.crl.model.parser.x509;

import kl.npki.base.core.biz.cert.model.parser.x509.AttributeInfo;
import kl.npki.base.core.biz.cert.model.parser.x509.BasicInfo;
import kl.npki.base.core.biz.cert.model.parser.x509.ExtensionInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/9
 * @since 1.0
 */
public final class CrlInfoBuilder {
    private BasicInfo version;
    private BasicInfo issuer;
    private BasicInfo thisUpdate;
    private BasicInfo nextUpdate;
    private BasicInfo signatureAlgorithm;
    private BasicInfo signature;
    private List<RevokedInfo> revokedInfoList = new ArrayList<>();
    private List<ExtensionInfo> extensionInfo = new ArrayList<>();
    private List<AttributeInfo> attributeInfo = new ArrayList<>();

    private CrlInfoBuilder() {
    }

    public static CrlInfoBuilder builder() {
        return new CrlInfoBuilder();
    }

    public CrlInfoBuilder withVersion(BasicInfo version) {
        this.version = version;
        return this;
    }

    public CrlInfoBuilder withIssuer(BasicInfo issuer) {
        this.issuer = issuer;
        return this;
    }

    public CrlInfoBuilder withThisUpdate(BasicInfo thisUpdate) {
        this.thisUpdate = thisUpdate;
        return this;
    }

    public CrlInfoBuilder withNextUpdate(BasicInfo nextUpdate) {
        this.nextUpdate = nextUpdate;
        return this;
    }

    public CrlInfoBuilder withSignatureAlgorithm(BasicInfo signatureAlgorithm) {
        this.signatureAlgorithm = signatureAlgorithm;
        return this;
    }

    public CrlInfoBuilder withSignature(BasicInfo signature) {
        this.signature = signature;
        return this;
    }

    public CrlInfoBuilder addAllRevokedInfoList(List<RevokedInfo> revokedInfoList) {
        this.revokedInfoList.addAll(revokedInfoList);
        return this;
    }
    public CrlInfoBuilder addRevokedInfoList(RevokedInfo revokedInfo) {
        this.revokedInfoList.add(revokedInfo);
        return this;
    }

    public CrlInfoBuilder addAllExtensionInfo(List<ExtensionInfo> extensionInfo) {
        this.extensionInfo.addAll(extensionInfo);
        return this;
    }
    public CrlInfoBuilder addExtensionInfo(ExtensionInfo extensionInfo) {
        this.extensionInfo.add(extensionInfo);
        return this;
    }

    public CrlInfoBuilder addAttributeInfo(AttributeInfo attributeInfo) {
        this.attributeInfo.add(attributeInfo);
        return this;
    }

    public CrlInfo build() {
        CrlInfo crlInfo = new CrlInfo();
        crlInfo.setVersion(version);
        crlInfo.setIssuer(issuer);
        crlInfo.setThisUpdate(thisUpdate);
        crlInfo.setNextUpdate(nextUpdate);
        crlInfo.setSignatureAlgorithm(signatureAlgorithm);
        crlInfo.setSignature(signature);
        crlInfo.setRevokedInfoList(revokedInfoList);
        crlInfo.setExtensionInfo(extensionInfo);
        crlInfo.setAttributeInfo(attributeInfo);
        return crlInfo;
    }
}
