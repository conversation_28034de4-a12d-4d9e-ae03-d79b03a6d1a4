package kl.npki.base.core.biz.cert.parser.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * <AUTHOR>
 * @date 2024/8/12 17:52
 */
public enum BasicConstraintsEnum implements EnumI18n {
    IS_CA("是证书授权中心"),
    IS_NOT_CA("不是证书授权中心"),
    PATH_LEN_CONSTRAINT("中级CA证书数目的上限为");

    private final String desc;

    BasicConstraintsEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return tr();
    }

    public static String getCaDesc(boolean isCa) {
        return isCa ? IS_CA.getDesc() : IS_NOT_CA.getDesc();
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
