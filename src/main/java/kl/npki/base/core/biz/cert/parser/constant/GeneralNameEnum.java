package kl.npki.base.core.biz.cert.parser.constant;

import kl.nbase.i18n.i18n.EnumI18n;
import kl.nbase.security.asn1.x509.GeneralName;

import java.util.Arrays;
import java.util.Optional;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 类型名称枚举类，列举所有选项，定义了类型名称的编码以及描述
 *
 * <AUTHOR>
 * @date 2024-08-08
 */
public enum GeneralNameEnum implements EnumI18n {

    /**
     * 其他名称
     */
    OTHER_NAME(GeneralName.otherName, "其他名称"),
    /**
     * 电子邮件地址
     */
    RFC822_NAME(GeneralName.rfc822Name, "电子邮件地址"),
    /**
     * DNS 名称
     */
    DNS_NAME(GeneralName.dNSName, "DNS 名称"),
    /**
     * X.400 地址
     */
    X400_ADDRESS(GeneralName.x400Address, "X.400 地址"),
    /**
     * 目录名称
     */
    DIRECTORY_NAME(GeneralName.directoryName, "目录名称"),
    /**
     * 电子数据交换（EDI）实体名称
     */
    EDI_PARTY_NAME(GeneralName.ediPartyName, "电子数据交换（EDI）实体名称"),
    /**
     * 统一资源标识符 (URI)
     */
    UNIFORM_RESOURCE_IDENTIFIER(GeneralName.uniformResourceIdentifier, "统一资源标识符"),
    /**
     * IP 地址
     */
    IP_ADDRESS(GeneralName.iPAddress, "IP 地址"),
    /**
     * 注册标识符
     */
    REGISTERED_ID(GeneralName.registeredID, "注册标识符");

    private final int code;
    private final String description;

    GeneralNameEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return tr();
    }

    public static String getGeneralNameDescByCode(int code) {
        Optional<GeneralNameEnum> generalName = Arrays.stream(values()).filter(value -> value.getCode() == code).findFirst();
        return generalName.map(GeneralNameEnum::getDescription).orElse(null);
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
