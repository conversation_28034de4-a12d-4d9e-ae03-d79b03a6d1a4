package kl.npki.base.core.biz.dataprotect.service.impl;

import kl.nbase.security.crypto.provider.digest.HMacProvider;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.npki.base.core.biz.dataprotect.service.IDataProtectService;

import java.util.Arrays;

/**
 * 使用HMacWithSM3实现数据完整性
 * <AUTHOR>
 */
public class DataProtectServiceHashMacSm3Impl implements IDataProtectService {

    private final byte[] key;

    public DataProtectServiceHashMacSm3Impl(byte[] key) {
        this.key = key;
    }

    @Override
    public byte[] generateData(byte[] data) throws Exception {
        try {
            return HMacProvider.hMac(key, data, HashAlgo.SM3);
        } catch (Exception e) {
            throw new Exception("生成数据摘要失败", e);
        }
    }

    @Override
    public boolean verifyData(byte[] rawData, byte[] data) {
        byte[] hmac = HMacProvider.hMac(key, rawData, HashAlgo.SM3);
        return Arrays.equals(hmac, data);
    }


}
