package kl.npki.base.core.biz.log.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import kl.npki.base.core.constant.AuditStatusEnum;

/**
 * AuditStatusEnum Excel转换器
 *
 * <AUTHOR> href="mailto:<EMAIL>">Liang Shi</a>
 * @date 2025/6/11
 */
public class AuditStatusEnumConverter implements Converter<AuditStatusEnum> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return AuditStatusEnum.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(AuditStatusEnum value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if (value == null) {
            return new WriteCellData<>("");
        }
        return new WriteCellData<>(value.tr());
    }
}