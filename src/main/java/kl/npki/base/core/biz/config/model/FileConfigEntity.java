package kl.npki.base.core.biz.config.model;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/12/22
 */
public class FileConfigEntity implements Serializable {
    private static final long serialVersionUID = 6504475665989563601L;
    private Long id;

    private String fileName;

    private String filePath;

    private String fileContentBase64;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileContentBase64() {
        return fileContentBase64;
    }

    public void setFileContentBase64(String fileContentBase64) {
        this.fileContentBase64 = fileContentBase64;
    }

    @Override
    public String toString() {
        return "FileConfigEntity{" +
            "id=" + id +
            ", fileName='" + fileName + '\'' +
            ", filePath='" + filePath + '\'' +
            ", fileContentBase64='" + fileContentBase64 + '\'' +
            '}';
    }
}
