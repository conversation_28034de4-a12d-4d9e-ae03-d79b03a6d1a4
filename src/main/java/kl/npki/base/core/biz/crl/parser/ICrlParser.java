package kl.npki.base.core.biz.crl.parser;

import kl.npki.base.core.biz.crl.model.parser.x509.CrlInfo;

import java.io.IOException;

/**
 * CRL解析器接口
 * <p>
 * 该接口定义了如何解析不同类型的CRL数据，通过泛型<T>，可以支持多种数据类型，如X509，C509等
 *
 * <AUTHOR>
 * @date 2025-05-09
 * @since 1.0
 */
public interface ICrlParser<T> {


    /**
     * 解析CRL数据
     *
     * @param crl CRL数据，类型由泛型<T>确定
     * @return CrlInfo CRL信息
     * @throws IOException 如果解析过程中发生IO错误，会抛出此异常
     */
    CrlInfo parse(T crl) throws IOException;
}
