package kl.npki.base.core.biz.cert.parser.extension.impl;

import kl.nbase.security.asn1.x509.PolicyConstraints;
import kl.npki.base.core.biz.cert.parser.constant.ExtensionType;
import kl.npki.base.core.biz.cert.parser.extension.AbstractX509ExtensionParser;
import kl.npki.base.core.constant.I18nConstant;

/**
 * <AUTHOR>
 * @date 2024/8/12 16:07
 */
public class PolicyConstraintsExtensionParserImpl extends AbstractX509ExtensionParser {
    /**
     * 策略约束
     */
    private static final String POLICY_CONSTRAINTS = "strategic_constraints";
    /**
     * 从路径中哪个点开始，证书必须包含明确策略声明，配置数量的证书可跳过策略检查
     * 可跳过策略检查证书数量
     */
    private static final String REQUIRE_EXPLICIT_POLICY = "skip_policy_check_certificate_quantity";
    /**
     * 从路径中哪个点开始，证书禁止使用策略映射，配置数量的证书可使用策略映射
     * 可使用策略映射证书数量
     */
    private static final String INHIBIT_POLICY_MAPPING = "number_of_certificates_that_can_be_mapped_using_policies";

    /**
     * 生成一个易于阅读的扩展值字符串，此方法旨在将字节数组形式的扩展值转换为便于人类阅读和理解的字符串格式
     * <p>
     * 具体的转换逻辑由子类实现，因为不同的场景可能需要不同的格式化规则
     *
     * @param extensionOctets 扩展值的字节表示形式
     * @return 格式化后的字符串
     */
    @Override
    protected String generatePrettyValue(byte[] extensionOctets) {
        StringBuilder sb = new StringBuilder();
        String lineSeparator = System.lineSeparator();
        sb.append(I18nConstant.getBaseCoreI18nMessage(POLICY_CONSTRAINTS)).append(COLON).append(lineSeparator);
        // 解析PolicyConstraints，获取可跳过策略检查证书数量和可使用策略映射证书数量
        PolicyConstraints policyConstraints = PolicyConstraints.getInstance(extensionOctets);
        sb.append(TAB).append(I18nConstant.getBaseCoreI18nMessage(REQUIRE_EXPLICIT_POLICY)).append(COLON).append(policyConstraints.getRequireExplicitPolicyMapping().intValue()).append(lineSeparator);
        sb.append(TAB).append(I18nConstant.getBaseCoreI18nMessage(INHIBIT_POLICY_MAPPING)).append(COLON).append(policyConstraints.getInhibitPolicyMapping().intValue()).append(lineSeparator);
        return sb.toString();
    }

    /**
     * 获取扩展项类型
     *
     * @return 返回当前对象扩展项类型 {@link ExtensionType}
     */
    @Override
    public ExtensionType getExtensionType() {
        return ExtensionType.POLICY_CONSTRAINTS;
    }
}
