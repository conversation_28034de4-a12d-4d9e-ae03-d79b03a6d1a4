package kl.npki.base.core.biz.log.model;


import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import kl.npki.base.core.common.date.CustomLocalDateTimeDeserializer;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 服务日志Info
 *
 * <AUTHOR>
 * @date 2022/12/9
 */
public class ApiLogInfo implements Serializable {


    private static final long serialVersionUID = -5719284858589155413L;
    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 链路id
     */
    @ExcelProperty("链路ID")
    private String traceId;

    /**
     * 跨度id
     */
    @ExcelProperty("跨度ID")
    private String spanId;

    /**
     * 客户端ip
     */
    @ExcelProperty("客户端IP")
    private String clientIp;


    /**
     * 何时请求
     */
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @ExcelProperty("何时请求")
    private LocalDateTime logWhen;

    /**
     * 业务id
     */
    @ExcelProperty("业务ID")
    private String bizId;

    /**
     * 请求业务
     */
    @ExcelProperty("请求业务")
    private String biz;

    /**
     * 操作详细内容
     */
    @ExcelProperty("操作详细内容")
    private String detail;

    /**
     * @see
     * <p>
     * 请求结果
     */
    @ExcelProperty("请求结果")
    private Boolean result;

    private String request;
    
    private String response;

    @ExcelProperty("经过时间")
    private long elapsedTime;

    /**
     * 调用者id
     */
    @ExcelProperty("调用者ID")
    private String callerId;

    /**
     * 调用者名称
     */
    @ExcelProperty("调用者名称")
    private String callerName;

    /**
     * 用户标识
     */
    @ExcelProperty("用户标识")
    private String entityId;


    /**
     * cid
     */
    @ExcelProperty("证书ID")
    private String certId;

    /**
     * 扩展项1
     */
    private String ext1;

    /**
     * 扩展项2
     */
    private String ext2;

    /**
     * 扩展项3
     */
    private String ext3;


    private String signData;


    private Integer signVerify;


    private String originData;


    private String fullDataHash;

    public String getTenantId() {
        return tenantId;
    }

    public ApiLogInfo setTenantId(String tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public String getTraceId() {
        return traceId;
    }

    public ApiLogInfo setTraceId(String traceId) {
        this.traceId = traceId;
        return this;
    }

    public String getSpanId() {
        return spanId;
    }

    public ApiLogInfo setSpanId(String spanId) {
        this.spanId = spanId;
        return this;
    }

    public String getClientIp() {
        return clientIp;
    }

    public ApiLogInfo setClientIp(String clientIp) {
        this.clientIp = clientIp;
        return this;
    }

    public LocalDateTime getLogWhen() {
        return logWhen;
    }

    public ApiLogInfo setLogWhen(LocalDateTime logWhen) {
        this.logWhen = logWhen;
        return this;
    }

    public String getBizId() {
        return bizId;
    }

    public ApiLogInfo setBizId(String bizId) {
        this.bizId = bizId;
        return this;
    }

    public String getBiz() {
        return biz;
    }

    public ApiLogInfo setBiz(String biz) {
        this.biz = biz;
        return this;
    }

    public String getDetail() {
        return detail;
    }

    public ApiLogInfo setDetail(String detail) {
        this.detail = detail;
        return this;
    }

    public Boolean getResult() {
        return result;
    }

    public ApiLogInfo setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public String getRequest() {
        return request;
    }

    public ApiLogInfo setRequest(String request) {
        this.request = request;
        return this;
    }

    public String getResponse() {
        return response;
    }

    public ApiLogInfo setResponse(String response) {
        this.response = response;
        return this;
    }

    public long getElapsedTime() {
        return elapsedTime;
    }

    public ApiLogInfo setElapsedTime(long elapsedTime) {
        this.elapsedTime = elapsedTime;
        return this;
    }

    public String getCallerId() {
        return callerId;
    }

    public ApiLogInfo setCallerId(String callerId) {
        this.callerId = callerId;
        return this;
    }

    public String getCallerName() {
        return callerName;
    }

    public ApiLogInfo setCallerName(String callerName) {
        this.callerName = callerName;
        return this;
    }

    public String getEntityId() {
        return entityId;
    }

    public ApiLogInfo setEntityId(String entityId) {
        this.entityId = entityId;
        return this;
    }

    public String getCertId() {
        return certId;
    }

    public ApiLogInfo setCertId(String certId) {
        this.certId = certId;
        return this;
    }

    public String getExt1() {
        return ext1;
    }

    public ApiLogInfo setExt1(String ext1) {
        this.ext1 = ext1;
        return this;
    }

    public String getExt2() {
        return ext2;
    }

    public ApiLogInfo setExt2(String ext2) {
        this.ext2 = ext2;
        return this;
    }

    public String getExt3() {
        return ext3;
    }

    public ApiLogInfo setExt3(String ext3) {
        this.ext3 = ext3;
        return this;
    }

    public String getSignData() {
        return signData;
    }

    public ApiLogInfo setSignData(String signData) {
        this.signData = signData;
        return this;
    }

    public Integer getSignVerify() {
        return signVerify;
    }

    public ApiLogInfo setSignVerify(Integer signVerify) {
        this.signVerify = signVerify;
        return this;
    }

    public String getOriginData() {
        return originData;
    }

    public ApiLogInfo setOriginData(String originData) {
        this.originData = originData;
        return this;
    }

    public String getFullDataHash() {
        return fullDataHash;
    }

    public ApiLogInfo setFullDataHash(String fullDataHash) {
        this.fullDataHash = fullDataHash;
        return this;
    }
}
