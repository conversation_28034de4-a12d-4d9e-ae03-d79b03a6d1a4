package kl.npki.base.core.biz.log.service;

import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.biz.export.AbstractTempFile;
import kl.npki.base.core.biz.log.model.SystemLogFileEntity;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.SystemLogEnum;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.repository.ISystemLogFileRepository;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.thread.ThreadExecutorFactory;
import kl.npki.base.core.utils.CompressionUtils;
import kl.npki.base.core.utils.DateUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.LongFunction;

/**
 * 抽象导出日志服务
 *
 * <AUTHOR>
 * @since 2025/7/9 9:50
 */
public abstract class AbstractExportLogService<T> extends AbstractTempFile implements IExportLogService<T> {

    private static final Logger logger = LoggerFactory.getLogger(AbstractExportLogService.class);

    private static final ISystemLogFileRepository SYSTEM_LOG_FILE_REPOSITORY = RepositoryFactory.get(ISystemLogFileRepository.class);

    public static final String ASYNC_BIZ_NAME = "exportLogBiz";

    @Override
    public Long getExportFileAndSaveDb(Object searchCondition, Map<String, List<String>> enableFieldMapping, SystemLogEnum systemLogEnum) {
        SystemLogFileEntity systemLogFileEntity = new SystemLogFileEntity();
        systemLogFileEntity.setFileType(systemLogEnum.getType());
        Long fileId = systemLogFileEntity.save();
        systemLogFileEntity.setId(fileId);
        // 异步产生文件并将结果保存到数据库中
        ExecutorService executorService = ThreadExecutorFactory.getExecutorService(ASYNC_BIZ_NAME);
        executorService.execute(() -> {
            try {
                saveFileToDb(searchCondition, enableFieldMapping, systemLogEnum, systemLogFileEntity);
            } catch (Exception e) {
                logger.error("saveFileToDb error，fileId: {}", fileId, e);
            }
        });
        return fileId;
    }


    private void saveFileToDb(Object searchCondition, Map<String, List<String>> enableFieldMapping,
                              SystemLogEnum systemLogEnum, SystemLogFileEntity systemLogFileEntity) {
        File exportFile = getExportFile(searchCondition, enableFieldMapping);
        // 将文件更新到数据库中
        updateFileContentToDb(systemLogEnum, systemLogFileEntity, exportFile);
    }

    /**
     * 将文件内容更新到数据库中
     *
     * @param systemLogEnum       日志类型
     * @param systemLogFileEntity 实体对象
     * @param exportFile          文件
     */
    private static void updateFileContentToDb(SystemLogEnum systemLogEnum, SystemLogFileEntity systemLogFileEntity, File exportFile) {
        if (Objects.isNull(exportFile) || !exportFile.exists()) {
            logger.error("exportFile not exist, fileId:{}", systemLogFileEntity.getId());
            return;
        }
        try {
            byte[] byteArray = FileUtils.readFileToByteArray(exportFile);
            // 字节数组压缩
            String base64Str = CompressionUtils.compressToBase64String(byteArray);
            systemLogFileEntity.setFileContent(base64Str);
            SysInfoConfig sysInfoConfig = BaseConfigWrapper.getSysInfoConfig();
            // 文件超出限制时进行删除
            SYSTEM_LOG_FILE_REPOSITORY.deleteOverLimitRecord(systemLogEnum.getType(), sysInfoConfig.getLogFileLimitSize());
            String fileName = String.format("%s_%s", systemLogEnum.getName(), exportFile.getName());
            systemLogFileEntity.setFileName(fileName);
            // 更新文件内容
            systemLogFileEntity.update();
            // 删除临时文件
            FileUtils.delete(exportFile);
        } catch (IOException e) {
            logger.error("operate file error，filePath: {}", exportFile.getAbsolutePath(), e);
        } catch (Exception e) {
            logger.error("updateFileContentToDb error", e);
        }
    }

    @Override
    public Long getExportSignedFileAndSaveDb(Object searchCondition, Map<String, List<String>> enableFieldMapping,
                                             SystemLogEnum systemLogEnum, List<X509Certificate> certChain) {
        SystemLogFileEntity systemLogFileEntity = new SystemLogFileEntity();
        systemLogFileEntity.setFileType(systemLogEnum.getType());
        Long fileId = systemLogFileEntity.save();
        systemLogFileEntity.setId(fileId);
        // 异步产生文件并将结果保存到数据库中
        ExecutorService executorService = ThreadExecutorFactory.getExecutorService(ASYNC_BIZ_NAME);
        executorService.execute(() -> {
            try {
                saveSignFileToDb(searchCondition, enableFieldMapping, systemLogEnum, certChain, systemLogFileEntity);
            } catch (Exception e) {
                logger.error("saveSignFileToDb fileId: {}", fileId, e);
            }
        });
        return fileId;
    }

    private void saveSignFileToDb(Object searchCondition, Map<String, List<String>> enableFieldMapping,
                                  SystemLogEnum systemLogEnum, List<X509Certificate> certChain,
                                  SystemLogFileEntity systemLogFileEntity) {
        Date date = new Date();
        // 构造Excel文件，然后将查询的数据添加到文件中
        File exportFile = getExportFile(searchCondition, enableFieldMapping);
        // 根据Excel文件获取XAdES签名的签名原文
        byte[] signature = getExcelFilePlaintext(exportFile, certChain, date);
        // 更新文件签名值
        updateExcelFileSignature(exportFile, certChain, date, signature);
        // 将文件更新到数据库中
        updateFileContentToDb(systemLogEnum, systemLogFileEntity, exportFile);
    }


    @Override
    public File writeFileContentToFile(Long fileId) {
        SystemLogFileEntity systemLogFileEntity = searchSystemLogFile(fileId);
        String fileContent = systemLogFileEntity.getFileContent();
        if (StringUtils.isBlank(fileContent)) {
            return null;
        }
        byte[] fileContentBytes = CompressionUtils.decompressFromBase64String(fileContent);
        File tempFile = getTempFile(DateUtil.formatFileDate(new Date()) + ".xlsx");
        String filePath = tempFile.getAbsolutePath();
        try (InputStream is = new ByteArrayInputStream(fileContentBytes);
             BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(filePath))) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
        } catch (FileNotFoundException e) {
            logger.error("file does not exist，filePath: {}", filePath, e);
        } catch (IOException e) {
            logger.error(" writing file error，filePath: {}", filePath, e);
        }
        return tempFile;
    }

    @Override
    public List<SystemLogFileEntity> searchSystemLogFileList(Integer fileType) {
        return SYSTEM_LOG_FILE_REPOSITORY.searchSystemLogFileList(fileType);
    }


    @Override
    public SystemLogFileEntity searchSystemLogFileStatus(Long id) {
        return searchLogFile(id, SYSTEM_LOG_FILE_REPOSITORY::searchStatus);
    }

    @Override
    public SystemLogFileEntity searchSystemLogFile(Long id) {
        SystemLogFileEntity systemLogFileEntity = searchLogFile(id, SYSTEM_LOG_FILE_REPOSITORY::search);
        CheckUtils.notNull(systemLogFileEntity, BaseInternalError.FILE_NO_FOUND_FAIL.toException());
        return systemLogFileEntity;
    }

    @Override
    public void deleteTempFile(Long id) {
        SYSTEM_LOG_FILE_REPOSITORY.delete(id);
    }

    /**
     * @param id               iud
     * @param repositoryMethod repositoryMethod
     * @return SystemLogFileEntity
     */
    private SystemLogFileEntity searchLogFile(Long id, LongFunction<SystemLogFileEntity> repositoryMethod) {
        return repositoryMethod.apply(id);
    }
}

