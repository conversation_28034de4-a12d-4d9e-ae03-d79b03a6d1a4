package kl.npki.base.core.biz.cert.parser.constant;

import kl.nbase.i18n.i18n.EnumI18n;
import kl.nbase.security.asn1.misc.NetscapeCertType;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

public enum NetscapeCertTypeEnum implements EnumI18n {
    /**
     * SSL客户端身份验证证书 : 10000000(8bit)
     */
    SSL_CLIENT(NetscapeCertType.sslClient, "SSL客户端认证"),
    /**
     * SSL服务器身份验证证书 : 01000000(8bit)
     */
    SSL_SERVER(NetscapeCertType.sslServer, "SSL服务器认证"),
    /**
     * 加密电子邮件证书 : 00100000(8bit)
     */
    SMIME(NetscapeCertType.smime, "加密电子邮件"),
    /**
     * 对象签名证书 : 00010000(8bit)
     */
    OBJECT_SIGNING(NetscapeCertType.objectSigning, "对象签名"),
    /**
     * 保留使用 : 00001000(8bit)
     */
    RESERVED(NetscapeCertType.reserved, "保留使用"),
    /**
     * SSL证书颁发机构 : 00000100(8bit)
     */
    SSL_CA(NetscapeCertType.sslCA, "SSL证书颁发机构"),
    /**
     * S/MIME证书的颁发机构 : 00000010(8bit)
     */
    SMIME_CA(NetscapeCertType.smimeCA, "S/MIME证书的颁发机构"),
    /**
     * 对象签名证书颁发机构: 00000001(8bit)
     */
    OBJECT_SIGNING_CA(NetscapeCertType.objectSigningCA, "对象签名证书颁发机构");

    private final int code;
    private final String description;

    NetscapeCertTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return tr();
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }

}
