package kl.npki.base.core.biz.cert.parser.extension.impl;

import kl.nbase.security.asn1.x509.GeneralNames;
import kl.npki.base.core.biz.cert.parser.constant.ExtensionType;
import kl.npki.base.core.biz.cert.parser.constant.GeneralNameEnum;
import kl.npki.base.core.biz.cert.parser.extension.AbstractX509ExtensionParser;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/8/12 16:07
 */
public class IssuerAlternativeNameExtensionParserImpl extends AbstractX509ExtensionParser {
    /**
     * 生成一个易于阅读的扩展值字符串，此方法旨在将字节数组形式的扩展值转换为便于人类阅读和理解的字符串格式
     * <p>
     * 具体的转换逻辑由子类实现，因为不同的场景可能需要不同的格式化规则
     *
     * @param extensionOctets 扩展值的字节表示形式
     * @return 格式化后的字符串
     */
    @Override
    protected String generatePrettyValue(byte[] extensionOctets) {
        GeneralNames generalNames = GeneralNames.getInstance(extensionOctets);
        StringBuilder sb = new StringBuilder();
        Arrays.stream(generalNames.getNames()).forEach(generalName -> {
            String issuerAlternativeNameDesc = GeneralNameEnum.getGeneralNameDescByCode(generalName.getTagNo());
            if (StringUtils.isNotBlank(issuerAlternativeNameDesc)) {
                // 截取tag后数据信息 拼接描述内容
                String issuerAlternativeName = generalName.getName().toString();
                sb.append(issuerAlternativeNameDesc).append(issuerAlternativeName).append(LINE_SEPARATOR);
            }
        });
        return sb.toString();
    }

    /**
     * 获取扩展项类型
     *
     * @return 返回当前对象扩展项类型 {@link ExtensionType}
     */
    @Override
    public ExtensionType getExtensionType() {
        return ExtensionType.ISSUER_ALTERNATIVE_NAME;
    }
}
