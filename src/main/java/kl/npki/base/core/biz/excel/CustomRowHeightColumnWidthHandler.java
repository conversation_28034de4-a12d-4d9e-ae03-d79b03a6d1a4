package kl.npki.base.core.biz.excel;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.Sheet;

/**
 * EXCEL单元格 宽度高度设置
 */
public class CustomRowHeightColumnWidthHandler implements SheetWriteHandler {

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        sheet.setDefaultRowHeightInPoints(20); // 设置默认行高45
        sheet.setColumnWidth(0, 5000); // 第二列宽度
        sheet.setColumnWidth(1, 10000); // 第三列宽度
        sheet.setColumnWidth(2, 10000); // 第三列宽度
    }
}
