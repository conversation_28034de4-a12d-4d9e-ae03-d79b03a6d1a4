package kl.npki.base.core.biz.inspection.report.impl;

import kl.npki.base.core.biz.inspection.model.InspectionItemResult;
import kl.npki.base.core.biz.inspection.report.InspectionReportGenerator;

import java.io.IOException;
import java.util.List;

/**
 * PDF格式的巡检报告生成器
 *
 * <AUTHOR>
 * @date 8/5/2025 下午 7:01
 **/
public class PdfInspectionReportGeneratorImpl implements InspectionReportGenerator {

    @Override
    public byte[] generate(List<InspectionItemResult> inspectionItemResults) throws IOException {
        // 当前由前端生成报告，后端暂不提供实现
        throw new UnsupportedOperationException("Unimplemented Function");
    }
}
