package kl.npki.base.core.biz.cert.service.impl;

import kl.nbase.cache.lock.ILock;
import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.helper.utils.JsonUtils;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.asn1.x509.SubjectPublicKeyInfo;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.nbase.security.pkcs.PKCS10CertificationRequest;
import kl.nbase.security.util.encoders.Base64;
import kl.nbase.security.utils.AsymKeyUtil;
import kl.nbase.security.utils.PEMUtil;
import kl.npki.base.core.annotation.KlTransactional;
import kl.npki.base.core.biz.cert.model.CertRequestInfo;
import kl.npki.base.core.biz.cert.model.ImportTrustCertInfo;
import kl.npki.base.core.biz.cert.model.ServerCertRequestInfo;
import kl.npki.base.core.biz.cert.model.TrustCertEntity;
import kl.npki.base.core.biz.cert.service.*;
import kl.npki.base.core.biz.config.model.ConfigStatus;
import kl.npki.base.core.biz.config.model.FileConfigEntity;
import kl.npki.base.core.common.CommonLock;
import kl.npki.base.core.common.manager.MgrHolder;
import kl.npki.base.core.configs.SysSupportedAlgoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.*;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.core.repository.IFileConfigRepository;
import kl.npki.base.core.repository.ITrustCertRepository;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.base.core.utils.KeyStoreUtil;
import kl.npki.base.core.utils.ZipUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.KeyPair;
import java.security.PublicKey;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.stream.Collectors;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.*;


/**
 * <AUTHOR>
 */
public class TrustCertServiceImpl implements ITrustCertService {

    private static final Logger log = LoggerFactory.getLogger(TrustCertServiceImpl.class);

    /**
     * 文件名称
     */
    private static final String TRUST_CERT_FILE_NAME = "trustCertChain.pem";
    private static final String LOCK_ID = "trust";
    private static final String TRUST_CERT_CONFIG_NAME = "trustCert";
    private static final String SIGN_CERT_FILE_NAME_SUFFIX = ".SignCert.crt";
    private static final String ENC_CERT_FILE_NAME_SUFFIX = ".EncCert.crt";
    private final ITrustCertRepository trustCertRepository;
    private final IFileConfigRepository fileConfigRepository;

    private final ICertExportService certExportService = new CertExportServiceImpl();

    public TrustCertServiceImpl() {
        this.trustCertRepository = RepositoryFactory.get(ITrustCertRepository.class);
        this.fileConfigRepository = RepositoryFactory.get(IFileConfigRepository.class);
    }

    @Override
    public void delete(Long id, String sslTrustStorePassword) {
        boolean delete = trustCertRepository.deleteTrustCert(id);
        if (delete) {
            // 刷新缓存
            MgrHolder.getManageCertMgr().reload();
            // 生成Trust KeyStore P12文件
            saveAndRefreshP12TrustFile(sslTrustStorePassword);
        } else {
            TrustCertEntity cert = trustCertRepository.getCert(id);
            if (ObjectUtils.isEmpty(cert)) {
                throw BaseInternalError.TRUST_CERT_NOT_EXIST_ERROR.toException(TRUST_CERT_ID_I18N_KEY, id);
            }
            if (TrustCertLevel.LOCAL.getCode().equals(cert.getCertLevel())) {
                throw BaseInternalError.LOCAL_TRUST_CERT_DELETER_ERROR.toException(TRUST_CERT_ID_I18N_KEY, id);
            }
        }
    }

    @Override
    public List<TrustCertEntity> getAllTrustCert() {
        return trustCertRepository.getAll(TrustCertType.MANAGE);
    }

    @Override
    public void importManageCert(String baseCertValue, String sslTrustStorePassword) {
        Certificate certificate = CertUtil.parsePemCert(baseCertValue);
        TrustCertEntity manageCertEntity = trustCertRepository.getManageCert();
        if (Objects.isNull(manageCertEntity)) {
            throw BaseInternalError.CERT_REQ_NOT_GEN.toException(PLEASE_GENERATE_CERT_REQUEST_I18N_KEY);
        }

        if (StringUtils.isNotBlank(manageCertEntity.getCertValue())) {
            throw BaseInternalError.ROOT_CERT_ALREADY_EXIST.toException(THE_ROOT_CERT_ALREADY_EXIST_I18N_KEY);
        }

        MgrHolder.getManageCertMgr().checkTrustChain(certificate);

        // 判断公钥是否与证书请求的公钥相同
        manageCertEntity.checkPublicKeyWithImportCert(certificate);

        // 保存至数据库
        manageCertEntity.updateById(certificate);
        MgrHolder.getManageCertMgr().reload();

        // 生成Trust KeyStore P12文件
        saveAndRefreshP12TrustFile(sslTrustStorePassword);

    }


    @Override
    @KlTransactional
    public void importUpdateManageCert(String baseCertValue, String sslTrustStorePassword) {
        Certificate certificate = CertUtil.parsePemCert(baseCertValue);
        TrustCertEntity manageCertEntity = trustCertRepository.getManageCert();
        if (Objects.isNull(manageCertEntity)) {
            throw BaseInternalError.ROOT_NOT_EXIST.toException(PLEASE_IMPORT_ROOT_CERT_I18N_KEY);
        }

        // 判断公钥是否与证书请求的公钥相同
        manageCertEntity.checkPublicKeyWithImportCert(certificate);

        // 保存至数据库
        manageCertEntity.update(certificate);
        MgrHolder.getManageCertMgr().reload();

        // 生成Trust KeyStore P12文件
        saveAndRefreshP12TrustFile(sslTrustStorePassword);
    }

    /**
     * 自签发根证书
     *
     * @param certRequestInfo
     * @return
     */
    @Override
    public String issueManageCert(CertRequestInfo certRequestInfo) {
        ManageCertMgr manageCertMgr = MgrHolder.getManageCertMgr();
        manageCertMgr.reload();
        // 重新reload查询数据库，防止其他服务初始化未同步
        if (ObjectUtils.isNotEmpty(manageCertMgr.getManageCertEntity())) {
            throw BaseInternalError.ROOT_CERT_ALREADY_EXIST.toException(THE_ROOT_CERT_ALREADY_EXIST_I18N_KEY);
        }

        ILock lock = CommonLock.INSTANCE.lock(LOCK_ID);
        if (!lock.tryLock()) {
            throw BaseInternalError.ROOT_CERT_INIT_GET_LOCK_ERROR.toException();
        }
        try {
            // 获取加密机服务
            ClusterEngine engine = EngineHolder.get();
            // 签发管理根证书
            KeyPair keyPair = engine.getSignKeyPair(new AsymAlgo(certRequestInfo.getKeyType()), certRequestInfo.getKeyIndex());
            Certificate rootCert = CertSignService.issueSelfSignCert(engine,
                certRequestInfo.buildDn(), keyPair, certRequestInfo.getValidDays());
            // 保存数据至数据库
            TrustCertEntity trustCertEntity = new TrustCertEntity(rootCert, TrustCertLevel.LOCAL);
            // 设置注册信息
            ServerCertRequestInfo serverCertRequestInfo = certRequestInfo.toServerCertRequestInfo();
            trustCertEntity.setRegisterInfo(JsonUtils.toJson(serverCertRequestInfo));

            trustCertEntity.save();
            // 更新缓存
            MgrHolder.getManageCertMgr().reload();

            // 生成Trust KeyStore P12文件
            saveAndRefreshP12TrustFile(certRequestInfo.getKeyStorePwd());

            // 生成并保存CRL文件
            MgrHolder.getManageCertMgr().asyncGenerateMgrRootCrl(null);

            // KM更新默认证书算法
            SysSupportedAlgoConfig sysAlgoConfig = BaseConfigWrapper.getSysAlgoConfig();
            sysAlgoConfig.setSystemCertAlgo(certRequestInfo.getKeyType());
            ConfigHolder.get().save(sysAlgoConfig);

            return trustCertEntity.getCertValue();
        } catch (Exception e) {
            throw BaseInternalError.CERT_ISSUE_ERROR.toException(ISSUE_MANAGE_ROOT_CERT_ERROR_I18N_KEY, e);
        } finally {
            lock.unlock();
        }

    }


    /**
     * 自己更新根证书
     *
     * @param certRequestInfo
     * @return
     */
    @Override
    @KlTransactional
    public String selfUpdateManageCert(CertRequestInfo certRequestInfo) {
        ManageCertMgr manageCertMgr = MgrHolder.getManageCertMgr();
        TrustCertEntity certEntity = manageCertMgr.getManageCertEntity();
        // 管理根不存在，不支持更新
        if (ObjectUtils.isEmpty(certEntity)) {
            throw BaseInternalError.ROOT_NOT_EXIST.toException(THE_ROOT_CERT_NOT_EXIST_I18N_KEY);
        }

        ILock lock = CommonLock.INSTANCE.lock(LOCK_ID);
        if (!lock.tryLock()) {
            throw BaseInternalError.ROOT_CERT_INIT_GET_LOCK_ERROR.toException();
        }
        try {
            // 获取加密机服务
            ClusterEngine engine = EngineHolder.get();
            // 签发管理根证书
            KeyPair keyPair = engine.getSignKeyPair(new AsymAlgo(certRequestInfo.getKeyType()), certRequestInfo.getKeyIndex());
            Certificate rootCert = CertSignService.issueSelfSignCert(engine,
                certRequestInfo.buildDn(), keyPair, certRequestInfo.getValidDays());
            // 设置注册信息
            ServerCertRequestInfo serverCertRequestInfo = certRequestInfo.toServerCertRequestInfo();
            certEntity.setRegisterInfo(JsonUtils.toJson(serverCertRequestInfo));
            // 保存数据至数据库
            certEntity.update(rootCert);

            // 更新缓存
            MgrHolder.getManageCertMgr().reload();

            // 生成Trust KeyStore P12文件
            saveAndRefreshP12TrustFile(certRequestInfo.getKeyStorePwd());

            // 生成并保存CRL文件
            MgrHolder.getManageCertMgr().asyncGenerateMgrRootCrl(null);

            return certEntity.getCertValue();
        } catch (Exception e) {
            throw BaseInternalError.CERT_ISSUE_ERROR.toException(ISSUE_MANAGE_ROOT_CERT_ERROR_I18N_KEY, e);
        } finally {
            lock.unlock();
        }

    }

    @Override
    public void saveAndRefreshP12TrustFile(String keyStorePwd) {
        List<TrustCertEntity> trustCertList = MgrHolder.getManageCertMgr().getTrustCertList();
        if (CollectionUtils.isNotEmpty(trustCertList)) {
            FileConfigEntity fileConfigEntity = genP12TrustKeyStore(keyStorePwd);
            fileConfigRepository.saveAndRefreshFile(fileConfigEntity);
        } else {
            log.warn("The trusted certificate list is empty, and the certificate chain P12 file will not be updated");
        }
    }

    private FileConfigEntity genP12TrustKeyStore(String keyStorePwd) {
        Certificate[] certChains = MgrHolder.getManageCertMgr().getCertChains();
        // TODO 若未来KJCC支持keyStore.load加载包含后量子证书的P12文件，那就删除这行代码，将后量子也保存到p12文件中 {{
        certChains = Arrays.stream(certChains)
            .filter(certificate -> {
                SubjectPublicKeyInfo subjectPublicKeyInfo = certificate.getSubjectPublicKeyInfo();
                PublicKey publicKey = AsymKeyUtil.x509Bytes2PublicKey(subjectPublicKeyInfo);
                AsymAlgo asymAlgo = AsymAlgo.valueOf(publicKey);
                return !asymAlgo.isPQC();
            })
            .toArray(Certificate[]::new);
        // }}

        // 根据证书生成别名列表
        List<String> certAlias = genCertAlias(certChains);
        byte[] trustKeyStoreBytes = KeyStoreUtil.genP12TrustKeyStoreBytes(Arrays.asList(certChains),
            certAlias,
            keyStorePwd);
        FileConfigEntity fileConfigEntity = new FileConfigEntity();
        fileConfigEntity.setFileName(KeyStoreConstants.DEFAULT_TRUST_STORE_FILE_NAME);
        fileConfigEntity.setFilePath(KeyStoreConstants.DEFAULT_TRUST_STORE_FILE_RELATIVE_PATH);
        fileConfigEntity.setFileContentBase64(Base64Util.base64Encode(trustKeyStoreBytes));
        return fileConfigEntity;
    }

    private List<String> genCertAlias(Certificate[] certChains) {
        List<String> certAlias = new ArrayList<>();

        for (Certificate certificate : certChains) {
            certAlias.add(certificate.getSerialNumber().toString());
        }

        return certAlias;
    }

    /**
     * 导入可信证书
     *
     * @param importTrustCertInfo
     */
    @Override
    public void importCert(ImportTrustCertInfo importTrustCertInfo, String sslTrustStorePassword) {
        List<String> certs = importTrustCertInfo.getCerts();
        List<Certificate> certificateList = certs.stream().map(CertUtil::parsePemCert).collect(Collectors.toList());
        importCert(certificateList, sslTrustStorePassword);
    }

    /**
     * 导入可信证书
     *
     * @param certChains            证书链
     * @param sslTrustStorePassword 存储密码
     */
    public void importCert(Collection<Certificate> certChains, String sslTrustStorePassword) {
        List<TrustCertEntity> trustCertEntityList = new ArrayList<>();
        for (Certificate certificate : certChains) {
            String hexSnOfCert = CertUtil.getHexSnOfCert(certificate);
            String certIssuerCn = CertUtil.getCertIssuerCn(certificate);
            CertVerifyHelper.checkValidity(certificate);
            boolean caCert = CertUtil.isCaCert(certificate);
            if (!caCert) {
                throw BaseInternalError.IMPORT_CERT_CHAIN_ERROR.toException(NOT_CA_CERT_I18N_KEY);
            }
            boolean exist = trustCertRepository.checkCertExist(hexSnOfCert, certIssuerCn);
            //证书已导入则跳过导入操作
            if (exist) {
                log.warn(HEXADECIMAL_CERTIFICATE_SERIAL_NUMBER_I18N_KEY, hexSnOfCert);
                continue;
            }
            TrustCertEntity trustCertEntity = new TrustCertEntity(certificate, TrustCertLevel.SUPER);
            trustCertEntityList.add(trustCertEntity);
        }
        // 保存至数据库
        trustCertRepository.batchSaveTrustCert(trustCertEntityList);
        MgrHolder.getManageCertMgr().reload();

        // 生成Trust KeyStore P12文件
        saveAndRefreshP12TrustFile(sslTrustStorePassword);
    }

    /**
     * 生成p10证书请求
     *
     * @param certRequestInfo
     * @return
     */
    @Override
    public String updateCertRequest(CertRequestInfo certRequestInfo) {
        // 判断管理根是否已签发
        TrustCertEntity manageCert = trustCertRepository.getManageCert();
        if (ObjectUtils.isEmpty(manageCert)) {
            throw BaseInternalError.ROOT_NOT_EXIST.toException(THE_ROOT_CERT_NOT_EXIST_I18N_KEY);
        }
        // 获取加密机服务
        String b64CertReq = genB64P10Request(certRequestInfo);

        // 保存证书请求，dn不变，待导入证书再进行修改
        ServerCertRequestInfo serverCertRequestInfo = certRequestInfo.toServerCertRequestInfo();
        trustCertRepository.saveOrUpdateManageCertReq(manageCert.getSubjectDn(), b64CertReq, JsonUtils.toJson(serverCertRequestInfo));
        MgrHolder.getManageCertMgr().reload();

        // 转换为PEM格式返回
        return PEMUtil.formatRequest(b64CertReq);
    }

    /**
     * 更新p10证书请求
     *
     * @param certRequestInfo
     * @return
     */
    @Override
    public String genCertRequest(CertRequestInfo certRequestInfo) {
        // 判断管理根是否已签发
        TrustCertEntity manageCert = trustCertRepository.getManageCert();
        if (manageCert != null && StringUtils.isNotBlank(manageCert.getCertValue())) {
            throw BaseInternalError.ROOT_CERT_ALREADY_EXIST.toException();
        }
        // 获取加密机服务
        String b64CertReq = genB64P10Request(certRequestInfo);

        // 保存证书请求
        ServerCertRequestInfo serverCertRequestInfo = certRequestInfo.toServerCertRequestInfo();
        trustCertRepository.saveOrUpdateManageCertReq(certRequestInfo.buildDn(), b64CertReq, JsonUtils.toJson(serverCertRequestInfo));
        MgrHolder.getManageCertMgr().reload();

        // 转换为PEM格式返回
        return PEMUtil.formatRequest(b64CertReq);
    }

    @Override
    public String exportCertRequest() {
        TrustCertEntity manageCert = trustCertRepository.getManageCert();
        if (Objects.isNull(manageCert) || StringUtils.isBlank(manageCert.getCertReq())) {
            return null;
        }
        String certReq = manageCert.getCertReq();

        // 转换为PEM格式返回
        return PEMUtil.formatRequest(certReq);
    }

    @Override
    public byte[] exportCertAsZip(String hexSn) {
        TrustCertEntity certEntity = trustCertRepository.getCert(hexSn);
        if (Objects.isNull(certEntity)) {
            throw BaseValidationError.EXPORT_CERT_ERROR.toException(CERT_NOT_FOUND_I18N_KEY);
        }
        if (StringUtils.isBlank(certEntity.getCertValue())) {
            throw BaseValidationError.EXPORT_CERT_ERROR.toException(CERT_IS_EMPTY_I18N_KEY);
        }
        // 要打包到zip中的文件
        Map<String, byte[]> fileNameToContentMap = new HashMap<>();
        // 签名证书
        String certValue = certEntity.getCertValue();
        fileNameToContentMap.put(certEntity.getSubjectCn() + SIGN_CERT_FILE_NAME_SUFFIX, certValue.getBytes());
        // 加密证书
        String encCertValue = certEntity.getEncCertValue();
        if (StringUtils.isNotBlank(encCertValue)) {
            fileNameToContentMap.put(certEntity.getSubjectCn() + ENC_CERT_FILE_NAME_SUFFIX, encCertValue.getBytes());
        }
        try {
            return ZipUtil.compress(fileNameToContentMap);
        } catch (IOException e) {
            throw BaseValidationError.EXPORT_CERT_ERROR.toException(e);
        }
    }

    /**
     * 导出可信证书为zip流 支持指定证书格式
     *
     * @param hexSn        证书唯一标识 16进制的sn
     * @param certTypeEnum 指定证书格式
     * @return zip结果文件的字节数组
     */
    @Override
    public byte[] exportTrustCertAsZip(String hexSn, ExportCertTypeEnum certTypeEnum) {
        TrustCertEntity certEntity = trustCertRepository.getCert(hexSn);
        if (Objects.isNull(certEntity)) {
            throw BaseValidationError.EXPORT_CERT_ERROR.toException(CERT_NOT_FOUND_I18N_KEY);
        }
        if (StringUtils.isBlank(certEntity.getCertValue())) {
            throw BaseValidationError.EXPORT_CERT_ERROR.toException(CERT_IS_EMPTY_I18N_KEY);
        }
        try {
            // 要打包到zip中的文件
            Map<String, byte[]> fileNameToContentMap = new HashMap<>();
            // 签名证书
            String certValue = certEntity.getCertValue();
            X509Certificate sigCert = kl.nbase.security.utils.CertUtil.derToX509(Base64.decode(certValue));
            File exportSignCert = certExportService.exportCert(sigCert, certTypeEnum);
            fileNameToContentMap.put(certEntity.getSubjectCn() + SIGN_CERT_FILE_NAME_SUFFIX, Files.readAllBytes(exportSignCert.toPath()));
            // 加密证书
            String encCertValue = certEntity.getEncCertValue();
            if (StringUtils.isNotBlank(encCertValue)) {
                X509Certificate encCert = kl.nbase.security.utils.CertUtil.derToX509(Base64.decode(encCertValue));
                File exportEncCert = certExportService.exportCert(encCert, certTypeEnum);
                fileNameToContentMap.put(certEntity.getSubjectCn() + ENC_CERT_FILE_NAME_SUFFIX, Files.readAllBytes(exportEncCert.toPath()));
            }
            return ZipUtil.compress(fileNameToContentMap);
        } catch (IOException e) {
            throw BaseValidationError.EXPORT_CERT_ERROR.toException(e);
        }
    }

    /**
     * 生成对应证书链的pem文件，为后续gmssl通信进行使用
     */
    @Override
    public void genCertChainPem() throws IOException {
        List<TrustCertEntity> allTrustCert = this.getAllTrustCert();
        List<String> trustCertValues = allTrustCert.stream().map(TrustCertEntity::getCertValue)
            .map(PEMUtil::formatCert).collect(Collectors.toList());
        // 将 trustCertValues 写入 pem文件
        // 判断文件是否存在，如果存在，则每次先删除原有文件，重新创建新的文件后在进行pem的写入
        URL resource = this.getClass().getClassLoader().getResource(TRUST_CERT_FILE_NAME);
        if (ObjectUtils.isNotEmpty(resource)) {
            try {
                Path path = Paths.get(resource.toURI());
                if (Files.exists(path)) {
                    Files.delete(path);
                }
            } catch (URISyntaxException e) {
                throw BaseValidationError.PARAM_ERROR.toException(e);
            }
        }
        String filePath =
            Objects.requireNonNull(this.getClass().getClassLoader().getResource("")).getPath() + TRUST_CERT_FILE_NAME;
        if (!new File(filePath).createNewFile()) {
            throw BaseInternalError.FILE_CREATE_FAIL.toException(filePath);
        }
        writeFile(trustCertValues, filePath);

    }

    @Override
    public ServerCertRequestInfo getRootCertRequestInfo() {
        ManageCertMgr manageCertMgr = MgrHolder.getManageCertMgr();
        TrustCertEntity certEntity = manageCertMgr.getManageCertEntity();
        if (Objects.isNull(certEntity) || StringUtils.isEmpty(certEntity.getRegisterInfo())) {
            return new ServerCertRequestInfo();
        }
        return JsonUtils.from(certEntity.getRegisterInfo(), ServerCertRequestInfo.class);
    }

    /**
     * 查询证书链的配置状态
     *
     * @return
     */
    @Override
    public ConfigStatus queryConfigStatus() {
        ConfigStatus configStatus = new ConfigStatus(TRUST_CERT_CONFIG_NAME);
        if (BaseConstant.NPKI_RA_SHORT_NAME.equals(
            BaseConfigWrapper.getSysInfoConfig().getShortName())) {
            // 因为RA系统可能没有管理根，所以RA系统的证书链配置不是必须操作，既此处返回配置完成状态
            // todo 未来可能会调整RA系统也必须要管理根，则需要修改此处逻辑，当前按照上述逻辑处理，否则RA部署过程阻塞，无法完成部署
            configStatus.setStatus(true);
            return configStatus;
        }

        TrustCertEntity manageCert = MgrHolder.getManageCertMgr().getManageCertEntity();
        if (Objects.isNull(manageCert) || StringUtils.isBlank(manageCert.getCertValue())) {
            configStatus.setStatus(false);
            return configStatus;
        }
        configStatus.setStatus(true);
        return configStatus;
    }

    @Override
    public ConfigStatus queryConfigStatusWhenDbNotReady() {
        ConfigStatus configStatus = new ConfigStatus(TRUST_CERT_CONFIG_NAME);
        configStatus.setStatus(false);
        configStatus.setWarnInfo(DB_NOT_CONFIGURED);
        return configStatus;
    }

    /**
     * 将证书链的值写入pem文件当中，方便后期gmssl的使用
     *
     * @param contents
     * @param filePath
     */
    private void writeFile(List<String> contents, String filePath) {
        try (FileWriter fileWriter = new FileWriter(filePath)) {
            for (String content : contents) {
                fileWriter.write(content + System.lineSeparator());
            }
        } catch (Exception e) {
            log.error("File write failed", e);
        }
    }

    public String genB64P10Request(CertRequestInfo certRequestInfo) {
        // 获取加密机服务
        ClusterEngine engine = EngineHolder.get();
        // 签发管理根证书
        AsymAlgo asymAlgo = new AsymAlgo(certRequestInfo.getKeyType());
        // 获取密钥对
        KeyPair keyPair = engine.getSignKeyPair(asymAlgo, certRequestInfo.getKeyIndex());
        // 生成证书请求
        PKCS10CertificationRequest pkcs10CertificationRequest = CertSignService.genCertRequest(engine,
            certRequestInfo.buildDn(), keyPair);

        try {
            return Base64Util.base64Encode(pkcs10CertificationRequest.getEncoded());
        } catch (IOException e) {
            throw BaseInternalError.PKCS10_REQUEST_ENCODE_ERROR.toException(e);
        }

    }
}
