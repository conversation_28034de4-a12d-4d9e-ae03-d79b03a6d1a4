package kl.npki.base.core.biz.org.handler;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.bean.validate.ValidateService;
import kl.nbase.exception.BaseException;
import kl.npki.base.core.biz.org.model.OrgAddInfo;
import kl.npki.base.core.biz.org.model.OrgBatchImportInfo;
import kl.npki.base.core.biz.org.model.OrgBatchImportResultInfo;
import kl.npki.base.core.biz.org.service.IOrgService;
import kl.npki.base.core.biz.upload.model.UploadFileEntity;
import kl.npki.base.core.constant.UploadFileProcessStatusEnum;
import kl.npki.base.core.exception.BaseInternalError;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 机构批量导入业务类
 *
 * <AUTHOR>
 * @create 2024/3/26 10:58
 */
public class OrgBatchImportProcessHandler extends AbstractOrgBatchProcessHandler implements ReadListener<OrgBatchImportInfo> {

    private static final Logger logger = LoggerFactory.getLogger(OrgBatchImportProcessHandler.class);
    private final IOrgService orgService;
    private final ConvertService convertService;
    private final ValidateService validateService;
    private final UploadFileEntity fileEntity;

    private static final String SUCCESS_FLAG = "success";
    private static final String FAILED_FLAG = "failed";


    /**
     * 处理个数
     */
    private int processCount;
    /**
     * 成功个数
     */
    private int processSuccessCount;
    /**
     * 失败个数
     */
    private int processErrorCount;

    public OrgBatchImportProcessHandler(IOrgService orgService,
                                        ConvertService convertService,
                                        ValidateService validateService,
                                        UploadFileEntity fileEntity) {
        super();
        this.orgService = orgService;
        this.convertService = convertService;
        this.validateService = validateService;
        this.fileEntity = fileEntity;
    }

    @Override
    public void invoke(OrgBatchImportInfo orgBatchImportInfo, AnalysisContext context) {
        OrgBatchImportResultInfo batchImportResultInfo = convertService.convert(orgBatchImportInfo, OrgBatchImportResultInfo.class);
        OrgAddInfo orgAddInfo = convertService.convert(orgBatchImportInfo, OrgAddInfo.class);
        try {
            // 校验数据内容
            validateService.validate(orgBatchImportInfo);
            // 添加数据到数据仓库中
            orgService.add(orgAddInfo);
            // 设置成功标识
            batchImportResultInfo.setProcessingStatus(SUCCESS_FLAG);//成功
            processSuccessCount++;
        } catch (BaseException e) {
            logger.error("Batch registration exception, [{}] institution registration failed, error code: {} Error message: {}",
                    orgBatchImportInfo.getOrgName(), e.getCode(), e.getMessage(), e);
            batchImportResultInfo.setProcessingStatus(FAILED_FLAG);//失败
            batchImportResultInfo.setExceptionMessage(e.getCode() + ":" + e.getMessage());
            processErrorCount++;
        } catch (Exception e) {
            String message = e.getMessage();
            message = message == null ? e.getLocalizedMessage() : message;
            logger.error("Batch registration exception, [{}] institution registration failed, error message: {}",//批量注册异常，【{}】 机构注册失败，错误信息: {}
                    orgBatchImportInfo.getOrgName(), message, e);
            batchImportResultInfo.setProcessingStatus(FAILED_FLAG);//失败
            batchImportResultInfo.setExceptionMessage(message);
            processErrorCount++;
        }

        // 写入当前行数据到 Excel
        writeData(batchImportResultInfo);

        processCount++;

        // 每插入100条数据更新一次数据库
        if (processCount % 100 == 0) {
            fileEntity.setProcessCount(processCount);
            fileEntity.setProcessSuccessCount(processSuccessCount);
            fileEntity.setProcessErrorCount(processErrorCount);
            fileEntity.setProcessStatus(UploadFileProcessStatusEnum.PROCESSING.getStatus());
            fileEntity.update();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        File resultFile = getResultFile();
        String resultFileDb;
        try {
            byte[] resultBytes = FileUtils.readFileToByteArray(resultFile);
            resultFileDb = Base64.encodeBase64String(resultBytes);
        } catch (IOException e) {
            throw BaseInternalError.ORG_BATCH_IMPORT_FILE_OPERATE_ERROR.toException(e);
        }

        // 更新进度
        fileEntity.setProcessStatus(UploadFileProcessStatusEnum.COMPLETE.getStatus());
        fileEntity.setProcessCount(processCount);
        fileEntity.setProcessSuccessCount(processSuccessCount);
        fileEntity.setProcessErrorCount(processErrorCount);
        fileEntity.setResultFile(resultFileDb);

        // 数据更新
        fileEntity.update();
    }

    private void writeData(OrgBatchImportResultInfo batchImportResultInfo) {
        List<OrgBatchImportResultInfo> rowData = new ArrayList<>();
        rowData.add(batchImportResultInfo);
        excelWriter.write(rowData, writeSheet);
    }

    @Override
    public Class getModeType() {
        return OrgBatchImportResultInfo.class;
    }
}