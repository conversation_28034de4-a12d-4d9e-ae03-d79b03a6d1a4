package kl.npki.base.core.biz.kcsp.model;

import java.io.Serializable;
import java.util.List;

/**
 * KCSP用户详细信息和权限资源数据模型
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
public class KcspUserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户角色列表
     */
    private List<String> roles;

    /**
     * 按钮资源列表
     */
    private List<String> buttonResource;

    /**
     * 菜单资源列表
     */
    private List<String> menuResource;

    /**
     * URL资源列表
     */
    private List<String> urlResource;

    /**
     * 用户ID
     */
    private int userId;

    /**
     * 登录名
     */
    private String loginName;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户手机号
     */
    private String userMobile;

    /**
     * 证书ID（可能为空）
     */
    private String certificateId;

    /**
     * 组织ID（可能为空）
     */
    private String orgId;

    /**
     * 组织名称（可能为空）
     */
    private String orgName;

    /**
     * 用户状态代码
     */
    private int status;

    /**
     * 最后更新时间
     */
    private String lastUpdateTime;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建者用户ID
     */
    private int addBy;

    /**
     * 更新者用户ID
     */
    private int updateBy;

    /**
     * 工号
     */
    private String workNo;

    /**
     * 租户唯一标识（可能为空）
     */
    private String tenantUniqueId;

    /**
     * 租户ID（可能为空）
     */
    private String tenantId;

    /**
     * 用户的唯一标识符
     */
    private String id;

    public List<String> getRoles() {
        return roles;
    }

    public void setRoles(List<String> roles) {
        this.roles = roles;
    }

    public List<String> getButtonResource() {
        return buttonResource;
    }

    public void setButtonResource(List<String> buttonResource) {
        this.buttonResource = buttonResource;
    }

    public List<String> getMenuResource() {
        return menuResource;
    }

    public void setMenuResource(List<String> menuResource) {
        this.menuResource = menuResource;
    }

    public List<String> getUrlResource() {
        return urlResource;
    }

    public void setUrlResource(List<String> urlResource) {
        this.urlResource = urlResource;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getUserMobile() {
        return userMobile;
    }

    public void setUserMobile(String userMobile) {
        this.userMobile = userMobile;
    }

    public String getCertificateId() {
        return certificateId;
    }

    public void setCertificateId(String certificateId) {
        this.certificateId = certificateId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(String lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public int getAddBy() {
        return addBy;
    }

    public void setAddBy(int addBy) {
        this.addBy = addBy;
    }

    public int getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(int updateBy) {
        this.updateBy = updateBy;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public String getTenantUniqueId() {
        return tenantUniqueId;
    }

    public void setTenantUniqueId(String tenantUniqueId) {
        this.tenantUniqueId = tenantUniqueId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "KcspUserInfo{" +
            "roles=" + roles +
            ", buttonResource=" + buttonResource +
            ", menuResource=" + menuResource +
            ", urlResource=" + urlResource +
            ", userId=" + userId +
            ", loginName='" + loginName + '\'' +
            ", userName='" + userName + '\'' +
            ", email='" + email + '\'' +
            ", userMobile='" + userMobile + '\'' +
            ", certificateId='" + certificateId + '\'' +
            ", orgId='" + orgId + '\'' +
            ", orgName='" + orgName + '\'' +
            ", status=" + status +
            ", lastUpdateTime='" + lastUpdateTime + '\'' +
            ", createTime='" + createTime + '\'' +
            ", addBy=" + addBy +
            ", updateBy=" + updateBy +
            ", workNo='" + workNo + '\'' +
            ", tenantUniqueId='" + tenantUniqueId + '\'' +
            ", tenantId='" + tenantId + '\'' +
            ", id='" + id + '\'' +
            '}';
    }
}
