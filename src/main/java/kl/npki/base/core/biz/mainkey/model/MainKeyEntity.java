package kl.npki.base.core.biz.mainkey.model;

import kl.nbase.security.entity.algo.BlockSymAlgo;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.repository.IMainKeyRepository;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.base.core.utils.MainKeyUtil;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * @Author: guoq
 * @Date: 2022/8/22
 * @description: 主密钥对象
 */
public class MainKeyEntity {

    private Long id;

    private String keyAlgo;

    private String pubKeyId;

    private String keyValue;

    private String iv;
    /**
     * 加密私钥与对称密钥拼接
     */
    private String refValue;

    /**
     * 标识所属加密机（目前未用） 
     */
    private Long engineId;

    private boolean beUsing;

    private Integer keyStatus;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    public MainKeyEntity() {
    }

    public MainKeyEntity(EmMainSecretKey eskey, String keyAlgo) {
        this.keyAlgo = keyAlgo;
        this.pubKeyId = eskey.getB64PublicKeyId();
        this.keyValue = eskey.getB64EncMainKeyValue();
        this.refValue = eskey.getB64RefValue();
        this.keyStatus = EntityStatus.NORMAL.getId();
        this.createTime = LocalDateTime.now();
        this.beUsing = true;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getKeyAlgo() {
        return keyAlgo;
    }

    public void setKeyAlgo(String keyAlgo) {
        this.keyAlgo = keyAlgo;
    }

    public String getPubKeyId() {
        return pubKeyId;
    }

    public void setPubKeyId(String pubKeyId) {
        this.pubKeyId = pubKeyId;
    }

    public String getKeyValue() {
        return keyValue;
    }

    public void setKeyValue(String keyValue) {
        this.keyValue = keyValue;
    }

    public String getIv() {
        return iv;
    }

    public void setIv(String iv) {
        this.iv = iv;
    }

    public String getRefValue() {
        return refValue;
    }

    public void setRefValue(String refValue) {
        this.refValue = refValue;
    }

    public Long getEngineId() {
        return engineId;
    }

    public void setEngineId(Long engineId) {
        this.engineId = engineId;
    }

    public boolean isBeUsing() {
        return beUsing;
    }

    public void setBeUsing(boolean beUsing) {
        this.beUsing = beUsing;
    }

    public Integer getKeyStatus() {
        return keyStatus;
    }

    public void setKeyStatus(Integer keyStatus) {
        this.keyStatus = keyStatus;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public NakedMainKeyEntity getNakedMainKey() {
        BlockSymAlgo mainKeyAlgorithm = BlockSymAlgo.valueOfAlgoName(getKeyAlgo());
        //将新主密钥添加到缓存
        byte[] skey;
        try {
            skey = MainKeyUtil.importMainKey(
                EngineHolder.get(),
                getKeyValue(),
                getRefValue(),
                getPubKeyId());
        } catch (Exception e) {
            throw BaseInternalError.MAINKEY_IMPORT_ERROR.toException(e);
        }
        byte[] ivData = null;
        if (StringUtils.isNotBlank(getIv())) {
            ivData =  Base64Util.base64Decode(getIv());
        }
        return new NakedMainKeyEntity(getId(), mainKeyAlgorithm, skey, ivData);
    }

    public Long save() {
        Long keyId = RepositoryFactory.get(IMainKeyRepository.class).insertNewMainKey(this);
        setId(keyId);
        return keyId;
    }

    public boolean update() {
        return RepositoryFactory.get(IMainKeyRepository.class).updateMainKey(this);
    }
}
