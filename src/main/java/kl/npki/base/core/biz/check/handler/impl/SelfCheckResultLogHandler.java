package kl.npki.base.core.biz.check.handler.impl;

import kl.npki.base.core.biz.check.handler.SelfCheckResultObserver;
import kl.npki.base.core.biz.check.handler.SelfCheckResultRepository;
import kl.npki.base.core.biz.check.model.SelfCheckResult;
import kl.npki.base.core.biz.check.model.Status;
import kl.npki.base.core.utils.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.format.DateTimeFormatter;

/**
 * 自检结果日志处理器
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
public class SelfCheckResultLogHandler implements SelfCheckResultObserver {

    private static final Logger log = LoggerFactory.getLogger(SelfCheckResultLogHandler.class);
    private final SelfCheckResultRepository repository;

    // 国际化消息键
    private static class I18nKeys {
        static final String SUCCESS_LOG = "self_check_success_log";
        static final String WARNING_LOG = "self_check_warning_log";
        static final String ERROR_LOG = "self_check_error_log";
    }

    public SelfCheckResultLogHandler(SelfCheckResultRepository repository) {
        this.repository = repository;
    }

    @Override
    public void handle(SelfCheckResult result) {
        // 格式化时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.dateTimePattern);
        String formattedCheckTime = result.getCheckTime().format(formatter);

        // 获取基本日志参数
        Object[] baseParams = new Object[]{
            result.getName(),
            result.getCategory(),
            result.getVersion(),
            formattedCheckTime,
            result.getDuration()
        };

        // 获取详细日志参数（用于警告和错误）
        Object[] detailParams = new Object[]{
            result.getName(),
            result.getCategory(),
            result.getVersion(),
            formattedCheckTime,
            result.getDuration(),
            result.getMessage(),
            result.getDetails()
        };

        // 根据状态获取对应的国际化消息模板
        Status status = result.getStatus();
        String messageTemplate;

        if (Status.SUCCESS.equals(status)) {
            // 日志不考虑国际化，仅使用英文模板
            // messageTemplate = getBaseCoreI18nMessage(I18nKeys.SUCCESS_LOG);
            messageTemplate = "Self-Check Item: [{}] - Category: [{}] - Environment ID: [{}], Self-Check Successful! Check Time: [{}], Duration: [{}]ms";
            log.info(messageTemplate, baseParams);
        } else if (Status.WARNING.equals(status)) {
            // 日志不考虑国际化，仅使用英文模板
            // messageTemplate = getBaseCoreI18nMessage(I18nKeys.WARNING_LOG);
            messageTemplate = "Self-Check Item: [{}] - Category: [{}] - Environment ID: [{}], Self-Check Warning! Check Time: [{}], Duration: [{}]ms, Warning Reason: {}, Detailed Information: {}";
            log.warn(messageTemplate, detailParams);
        } else {
            // 日志不考虑国际化，仅使用英文模板
            // messageTemplate = getBaseCoreI18nMessage(I18nKeys.ERROR_LOG);
            messageTemplate = "Self-Check Item: [{}] - Category: [{}] - Environment ID: [{}], Self-Check Failed! Check Time: [{}], Duration: [{}]ms, Failure Reason: {}, Detailed Information: {}";
            log.error(messageTemplate, detailParams);
        }

        // 保存结果
        repository.save(result);
    }
}
