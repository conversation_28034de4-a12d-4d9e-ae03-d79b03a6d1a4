package kl.npki.base.core.biz.license.model;

import java.io.Serializable;

/**
 * 授权数量信息，包括总数、已使用、可用等信息
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public class LicenseCountInfo implements Serializable {

    /**
     * 授权总数量
     */
    private long totalCount;

    /**
     * 已使用授权数量
     */
    private long usedCount;

    /**
     * 可用授权数量
     */
    private long availableCount;

    public LicenseCountInfo(long totalCount, long usedCount) {
        this.totalCount = totalCount;
        this.usedCount = usedCount;
        this.availableCount = totalCount - usedCount;
    }

    public LicenseCountInfo(long totalCount, long usedCount, long availableCount) {
        this.totalCount = totalCount;
        this.usedCount = usedCount;
        this.availableCount = availableCount;
    }

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public long getUsedCount() {
        return usedCount;
    }

    public void setUsedCount(long usedCount) {
        this.usedCount = usedCount;
    }

    public long getAvailableCount() {
        return availableCount;
    }

    public void setAvailableCount(long availableCount) {
        this.availableCount = availableCount;
    }
}
