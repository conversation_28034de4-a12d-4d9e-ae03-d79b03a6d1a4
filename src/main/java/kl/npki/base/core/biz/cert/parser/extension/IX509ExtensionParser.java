package kl.npki.base.core.biz.cert.parser.extension;

import kl.nbase.security.asn1.x509.Extension;
import kl.npki.base.core.biz.cert.model.parser.x509.ExtensionInfo;
import kl.npki.base.core.biz.cert.parser.constant.ExtensionType;

import java.io.IOException;

/**
 * 扩展项解码接口，负责将扩展信息解码成{@link ExtensionInfo}对象
 *
 * <AUTHOR>
 * @date 2024-08-08
 */
public interface IX509ExtensionParser {

    /**
     * 解码扩展信息，负责将扩展信息解码成ExtensionInfo对象
     *
     * @param extension 扩展信息{@link Extension}
     * @return ExtensionInfo 解码后的{@link ExtensionInfo}对象
     * @throws IOException 扩展项值解码异常
     */
    ExtensionInfo parse(Extension extension) throws IOException;

    /**
     * 获取扩展项类型
     *
     * @return 返回当前对象扩展项类型 {@link ExtensionType}
     */
    ExtensionType getExtensionType();
}
