package kl.npki.base.core.biz.kcsp.model.response;

import java.io.Serializable;

/**
 * KCSP 服务接口日志上报结果
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
public class KcspApiLogReportResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    private int logId;

    /**
     * 接口地址
     */
    private String address;

    /**
     * 返回的HTTP状态码
     */
    private String returnHttpCode;

    /**
     * 请求方Key
     */
    private String requestAppKey;

    /**
     * 请求方ID
     */
    private String requestAppId;

    /**
     * 请求应用名称
     */
    private String requestAppName;

    /**
     * 请求时间
     */
    private String requestTime;

    /**
     * 响应耗时（毫秒）
     */
    private int responseTime;

    /**
     * 响应值（可为空）
     */
    private String responseValue;

    /**
     * 接口编码
     */
    private String interfaceCode;

    /**
     * 请求IP地址
     */
    private String requestIp;

    /**
     * 请求参数（可为空）
     */
    private String requestParam;

    /**
     * 添加时间
     */
    private String addTime;

    /**
     * 请求体（可为空）
     */
    private String requestBody;

    /**
     * 请求方法（如GET, POST）
     */
    private String requestMethod;

    /**
     * 唯一标识符
     */
    private String id;

    public int getLogId() {
        return logId;
    }

    public void setLogId(int logId) {
        this.logId = logId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getReturnHttpCode() {
        return returnHttpCode;
    }

    public void setReturnHttpCode(String returnHttpCode) {
        this.returnHttpCode = returnHttpCode;
    }

    public String getRequestAppKey() {
        return requestAppKey;
    }

    public void setRequestAppKey(String requestAppKey) {
        this.requestAppKey = requestAppKey;
    }

    public String getRequestAppId() {
        return requestAppId;
    }

    public void setRequestAppId(String requestAppId) {
        this.requestAppId = requestAppId;
    }

    public String getRequestAppName() {
        return requestAppName;
    }

    public void setRequestAppName(String requestAppName) {
        this.requestAppName = requestAppName;
    }

    public String getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(String requestTime) {
        this.requestTime = requestTime;
    }

    public int getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(int responseTime) {
        this.responseTime = responseTime;
    }

    public String getResponseValue() {
        return responseValue;
    }

    public void setResponseValue(String responseValue) {
        this.responseValue = responseValue;
    }

    public String getInterfaceCode() {
        return interfaceCode;
    }

    public void setInterfaceCode(String interfaceCode) {
        this.interfaceCode = interfaceCode;
    }

    public String getRequestIp() {
        return requestIp;
    }

    public void setRequestIp(String requestIp) {
        this.requestIp = requestIp;
    }

    public String getRequestParam() {
        return requestParam;
    }

    public void setRequestParam(String requestParam) {
        this.requestParam = requestParam;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime;
    }

    public String getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(String requestBody) {
        this.requestBody = requestBody;
    }

    public String getRequestMethod() {
        return requestMethod;
    }

    public void setRequestMethod(String requestMethod) {
        this.requestMethod = requestMethod;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
