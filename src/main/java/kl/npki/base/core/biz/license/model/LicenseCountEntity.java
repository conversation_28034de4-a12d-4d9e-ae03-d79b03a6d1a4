package kl.npki.base.core.biz.license.model;

import java.io.Serializable;

/**
 * <AUTHOR> by niugang on 2025-05-29 17:02
 */
public class LicenseCountEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务侧License标识
     */
    private String cusKey;

    /**
     * 业务侧License标识对应的数据
     */
    private Long cusValue;


    /**
     * 最大上线
     */
    private Long maxLimit = 0L;

    public LicenseCountEntity() {
    }

    public LicenseCountEntity(String cusKey, Long cusValue) {
        this.cusKey = cusKey;
        this.cusValue = cusValue;
    }

    public String getCusKey() {
        return cusKey;
    }

    public Long getMaxLimit() {
        return maxLimit;
    }

    public void setMaxLimit(Long maxLimit) {
        this.maxLimit = maxLimit;
    }

    public void setCusKey(String cusKey) {
        this.cusKey = cusKey;
    }

    public Long getCusValue() {
        return cusValue;
    }

    public void setCusValue(Long cusValue) {
        this.cusValue = cusValue;
    }
}
