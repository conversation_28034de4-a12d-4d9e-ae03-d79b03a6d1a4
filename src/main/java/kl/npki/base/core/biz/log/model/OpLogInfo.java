package kl.npki.base.core.biz.log.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import kl.npki.base.core.biz.log.convert.AuditStatusEnumConverter;
import kl.npki.base.core.biz.log.convert.BooleanResultConverter;
import kl.npki.base.core.common.date.CustomLocalDateTimeDeserializer;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;
import kl.npki.base.core.constant.AuditStatusEnum;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 操作日志信息
 *
 * <AUTHOR>
 * @date 2022/12/9
 */
public class OpLogInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    private String logId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 链路id
     */
    @ExcelProperty("链路ID")
    private String traceId;

    /**
     * 跨度id
     */
    @ExcelProperty("跨度ID")
    private String spanId;

    /**
     * 操作人员
     */
    @ExcelProperty("操作人员ID")
    private String logWho;

    /**
     * 操作人员用户名
     */
    @ExcelProperty("操作人员用户名")
    private String username;

    /**
     * 操作名称
     */
    @ExcelProperty("操作名称")
    private String logDo;

    /**
     * <p>
     * 操作结果id
     */
    @ExcelProperty(value = "操作结果", converter = BooleanResultConverter.class)
    private Boolean result;

    /**
     * 操作详情
     */
    @ExcelProperty("操作详情")
    private String logWhat;

    /**
     * 审计状态
     */
    @ExcelProperty(value = "审计状态", converter = AuditStatusEnumConverter.class)
    private AuditStatusEnum auditStatus;

    /**
     * 客户端ip
     */
    @ExcelProperty("客户端IP")
    private String clientIp;

    /**
     * 服务端ip
     */
    @ExcelProperty("服务端IP")
    private String serverIp;

    /**
     * 操作时间
     */
    @ExcelProperty("操作时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime logWhen;

    /**
     * 结束时间
     */
    @ExcelProperty("结束时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime logEnd;

    /**
     * 签名值
     */
    @ExcelProperty("签名值")
    private String signData;

    /**
     * 请求
     */
    @ExcelProperty("请求内容")
    private String request;

    /**
     * 审计时间
     */
    @ExcelProperty("审计时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime auditTime;

    /**
     * 签名原文
     */
    @ExcelProperty("签名原文")
    private String originData;

    /**
     * 耗时
     */
    @ExcelProperty("耗时")
    private Integer elapsedTime;

    /**
     * 完整性保护摘要值
     */
    @ExcelProperty("完整性保护摘要值")
    private String fullDataHash;

    /**
     * 是否属于安全日志
     */
    private Boolean securityLog;

    public String getLogId() {
        return logId;
    }

    public void setLogId(String logId) {
        this.logId = logId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public OpLogInfo setTenantId(String tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public String getTraceId() {
        return traceId;
    }

    public OpLogInfo setTraceId(String traceId) {
        this.traceId = traceId;
        return this;
    }

    public String getSpanId() {
        return spanId;
    }

    public OpLogInfo setSpanId(String spanId) {
        this.spanId = spanId;
        return this;
    }

    public String getLogWho() {
        return logWho;
    }

    public OpLogInfo setLogWho(String logWho) {
        this.logWho = logWho;
        return this;
    }

    public String getUsername() {
        return username;
    }

    public OpLogInfo setUsername(String username) {
        this.username = username;
        return this;
    }

    public String getLogDo() {
        return logDo;
    }

    public OpLogInfo setLogDo(String logDo) {
        this.logDo = logDo;
        return this;
    }

    public Boolean getResult() {
        return result;
    }

    public OpLogInfo setResult(Boolean result) {
        this.result = result;
        return this;
    }

    public String getLogWhat() {
        return logWhat;
    }

    public OpLogInfo setLogWhat(String logWhat) {
        this.logWhat = logWhat;
        return this;
    }

    public String getClientIp() {
        return clientIp;
    }

    public OpLogInfo setClientIp(String clientIp) {
        this.clientIp = clientIp;
        return this;
    }

    public String getServerIp() {
        return serverIp;
    }

    public OpLogInfo setServerIp(String serverIp) {
        this.serverIp = serverIp;
        return this;
    }

    public LocalDateTime getLogWhen() {
        return logWhen;
    }

    public OpLogInfo setLogWhen(LocalDateTime logWhen) {
        this.logWhen = logWhen;
        return this;
    }

    public LocalDateTime getLogEnd() {
        return logEnd;
    }

    public OpLogInfo setLogEnd(LocalDateTime logEnd) {
        this.logEnd = logEnd;
        return this;
    }

    public String getSignData() {
        return signData;
    }

    public OpLogInfo setSignData(String signData) {
        this.signData = signData;
        return this;
    }

    public String getRequest() {
        return request;
    }

    public OpLogInfo setRequest(String request) {
        this.request = request;
        return this;
    }

    public AuditStatusEnum getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(AuditStatusEnum auditStatus) {
        this.auditStatus = auditStatus;
    }

    public LocalDateTime getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(LocalDateTime auditTime) {
        this.auditTime = auditTime;
    }

    public String getOriginData() {
        return originData;
    }

    public OpLogInfo setOriginData(String originData) {
        this.originData = originData;
        return this;
    }

    public Integer getElapsedTime() {
        return elapsedTime;
    }

    public OpLogInfo setElapsedTime(Integer elapsedTime) {
        this.elapsedTime = elapsedTime;
        return this;
    }

    public String getFullDataHash() {
        return fullDataHash;
    }

    public OpLogInfo setFullDataHash(String fullDataHash) {
        this.fullDataHash = fullDataHash;
        return this;
    }

    public Boolean getSecurityLog() {
        return securityLog;
    }

    public OpLogInfo setSecurityLog(Boolean securityLog) {
        this.securityLog = securityLog;
        return this;
    }
}
