package kl.npki.base.core.biz.dataprotect.service;

/**
 * 数据完整性保护接口
 * <AUTHOR>
 */
public interface IDataProtectService {

    /**
     * 针对数据完整性处理数据
     * 可以是对数据做摘要、做签名等操作，由具体类实现
     *
     * @param data 待处理的业务数据
     * @return 返回的数据为完整性的数据，可能是签名值或者摘要值
     * @throws Exception
     */
    byte[] generateData(byte[] data) throws Exception;

    /**
     * 验证数据的完整性
     *
     * @param data         数据原文
     * @param generateData 待验证的数据，可以是摘要值也可以是签名值
     * @return 如果数据验证不通过，则返回false，反之返回true
     * @throws Exception
     */
    boolean verifyData(byte[] data, byte[] generateData) throws Exception;

}

