package kl.npki.base.core.biz.cert.model;

import kl.nbase.security.asn1.custom.pkcs.SignedAndEnvelopedData;
import kl.nbase.security.asn1.x509.Certificate;

import java.security.PrivateKey;

/**
 * 用于接收通过CA签发双证书，以及加密证书数字信封
 *
 * <AUTHOR> wk
 */
public class IssueCertInfo {

    private Certificate signCertValue;

    private Certificate encCertValue;

    private PrivateKey signPrivateKey;
    /**
     * 加密证书私钥
     */
    private PrivateKey encPrivateKey;
    /**
     * 加密证书私钥信封
     */
    private SignedAndEnvelopedData encEnvelopedData;

    public Certificate getSignCertValue() {
        return signCertValue;
    }

    public void setSignCertValue(Certificate signCertValue) {
        this.signCertValue = signCertValue;
    }

    public Certificate getEncCertValue() {
        return encCertValue;
    }

    public void setEncCertValue(Certificate encCertValue) {
        this.encCertValue = encCertValue;
    }

    public PrivateKey getSignPrivateKey() {
        return signPrivateKey;
    }

    public void setSignPrivateKey(PrivateKey signPrivateKey) {
        this.signPrivateKey = signPrivateKey;
    }

    public PrivateKey getEncPrivateKey() {
        return encPrivateKey;
    }

    public void setEncPrivateKey(PrivateKey encPrivateKey) {
        this.encPrivateKey = encPrivateKey;
    }

    public SignedAndEnvelopedData getEncEnvelopedData() {
        return encEnvelopedData;
    }

    public IssueCertInfo setEncEnvelopedData(SignedAndEnvelopedData encEnvelopedData) {
        this.encEnvelopedData = encEnvelopedData;
        return this;
    }
}
