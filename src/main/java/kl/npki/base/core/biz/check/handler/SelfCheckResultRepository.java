package kl.npki.base.core.biz.check.handler;

import kl.npki.base.core.biz.check.model.SelfCheckResult;

import java.util.List;

/**
 * 服务自检存储层接口
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
public interface SelfCheckResultRepository {

    /**
     * 保存自检数据
     *
     * @param result
     */
    void save(SelfCheckResult result);

    /**
     * 查询所有自检数据
     *
     * @return 自检数据集合
     */
    List<SelfCheckResult> findAll();

    /**
     * 清除自检数据
     *
     * @param code 自检项编码
     */
    void removeByCode(String code);

    /**
     * 清除所有自检数据
     */
    void removeAll();
}
