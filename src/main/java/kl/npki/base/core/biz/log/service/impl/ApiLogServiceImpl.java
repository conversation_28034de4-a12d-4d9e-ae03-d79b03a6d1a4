package kl.npki.base.core.biz.log.service.impl;

import kl.npki.base.core.biz.log.model.ApiLogInfo;
import kl.npki.base.core.biz.log.service.AbstractExportLogService;
import kl.npki.base.core.biz.log.service.IApiLogService;
import kl.npki.base.core.repository.IApiLogRepository;
import kl.npki.base.core.repository.ILogBaseRepository;
import kl.npki.base.core.repository.RepositoryFactory;

/**
 * <AUTHOR>
 * @create 2025/2/11 下午5:50
 */
public class ApiLogServiceImpl extends AbstractExportLogService<ApiLogInfo> implements IApiLogService {

    private final IApiLogRepository repository = RepositoryFactory.get(IApiLogRepository.class);

    @Override
    public ILogBaseRepository<ApiLogInfo> getLogRepository() {
        return repository;
    }

    @Override
    public Class<ApiLogInfo> getEntityClass() {
        return ApiLogInfo.class;
    }
}