package kl.npki.base.core.biz.cert.parser.constant;

import kl.nbase.i18n.i18n.EnumI18n;
import kl.nbase.security.asn1.x509.CRLReason;

import java.util.Arrays;
import java.util.Optional;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 证书废除原因枚举类
 *
 * <AUTHOR>
 * @date 2024-08-08
 */
public enum CrlReasonEnum implements EnumI18n {

    /**
     * 未指定，没有实际吊销原因（0）
     */
    UNSPECIFIED(CRLReason.unspecified, "unspecified", "未指定"),
    /**
     * 密钥泄露（1）
     */
    KEY_COMPROMISE(CRLReason.keyCompromise, "keyCompromise", "密钥泄露"),
    /**
     * CA密钥泄露（2）
     */
    CA_COMPROMISE(CRLReason.cACompromise, "cACompromise", "CA密钥泄露"),
    /**
     * 隶属关系变化（3）
     */
    AFFILIATION_CHANGED(CRLReason.affiliationChanged, "affiliationChanged", "隶属关系变化"),
    /**
     * 证书被替换（4）
     */
    SUPERSEDED(CRLReason.superseded, "superseded", "证书被替换"),
    /**
     * 操作终止（5）
     */
    CESSATION_OF_OPERATION(CRLReason.cessationOfOperation, "cessationOfOperation", "操作终止"),
    /**
     * 证书冻结（6）
     */
    CERTIFICATE_HOLD(CRLReason.certificateHold, "certificateHold", "证书冻结"),
    /**
     * 证书已解冻（8）
     */
    REMOVE_FROM_CRL(CRLReason.removeFromCRL, "removeFromCRL", "从CRL中删除"),
    /**
     * 权限被撤销（9）
     */
    PRIVILEGE_WITHDRAWN(CRLReason.privilegeWithdrawn, "privilegeWithdrawn", "权限被撤销"),
    /**
     * 属性授权机构泄露（10）
     */
    AA_COMPROMISE(CRLReason.aACompromise, "aACompromise", "属性授权机构泄露");

    private final int code;
    private final String name;
    private final String description;

    CrlReasonEnum(int code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return tr();
    }

    public static String getReasonDescriptionByCode(int code) {
        Optional<CrlReasonEnum> crlReason = Arrays.stream(values()).filter(value -> value.code == code).findFirst();
        return crlReason.map(CrlReasonEnum::getDescription).orElse(null);
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
