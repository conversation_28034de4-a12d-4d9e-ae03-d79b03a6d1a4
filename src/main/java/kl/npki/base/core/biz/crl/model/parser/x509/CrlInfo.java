package kl.npki.base.core.biz.crl.model.parser.x509;

import kl.nbase.security.asn1.x509.CertificateList;
import kl.nbase.security.util.encoders.Base64;
import kl.nbase.security.utils.PEMUtil;
import kl.npki.base.core.biz.cert.model.parser.x509.AttributeInfo;
import kl.npki.base.core.biz.cert.model.parser.x509.BasicInfo;
import kl.npki.base.core.biz.cert.model.parser.x509.ExtensionInfo;
import kl.npki.base.core.biz.crl.parser.CrlParserFactory;
import kl.npki.base.core.biz.crl.parser.ICrlParser;
import kl.npki.base.core.biz.crl.parser.constant.CrlConstant;
import kl.npki.base.core.exception.BaseInternalError;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * CRL信息
 *
 * <AUTHOR>
 * @date 2025/5/8
 * @since 1.0
 */
public class CrlInfo {

    /**
     * CRL 的版本号，通常是 V1 或 V2。V2 支持额外的扩展和撤销原因等信息
     */
    private BasicInfo version;

    /**
     * CRL的签发者
     */
    private BasicInfo issuer;

    /**
     * CRL 的本次发布日期
     */
    private BasicInfo thisUpdate;

    /**
     * 下一次预计发布新 CRL 的时间
     */
    private BasicInfo nextUpdate;


    /**
     * 签名所使用的算法名称
     */
    private BasicInfo signatureAlgorithm;


    /**
     * CRL 的数字签名值，用于验证其完整性与来源
     */
    private BasicInfo signature;


    /**
     * 吊销信息列表，包含吊销的证书信息
     */
    private List<RevokedInfo> revokedInfoList;

    /**
     * 扩展信息列表，包含证书的各种扩展属性信息
     */
    private List<ExtensionInfo> extensionInfo;

    /**
     * 属性信息列表，包含证书的各种属性信息
     */
    private List<AttributeInfo> attributeInfo;


    /**
     * 从CRL字符串中解析CRL信息
     *
     * @param crlValues Base64的CRL字符串
     * @return CRL信息
     */
    public static CrlInfo[] fromCrlString(String... crlValues) {
        if (ArrayUtils.isEmpty(crlValues)) {
            return null;
        }

        ICrlParser<CertificateList> x509CrlParser = CrlParserFactory.getX509CrlParser();
        return Arrays.stream(crlValues).filter(StringUtils::isNotBlank).map(crlValue -> {
            try {
                crlValue = PEMUtil.unFormat(crlValue, CrlConstant.PEM_CRL_BEGIN, CrlConstant.PEM_CRL_END);
                byte[] crlBytes = Base64.decode(crlValue);

                CertificateList certificateList = CertificateList.getInstance(crlBytes);
                return x509CrlParser.parse(certificateList);
            } catch (Exception e) {
                throw BaseInternalError.CRL_PARSE_ERROR.toException(e);
            }
        }).toArray(CrlInfo[]::new);
    }

    public BasicInfo getVersion() {
        return version;
    }

    public void setVersion(BasicInfo version) {
        this.version = version;
    }

    public BasicInfo getIssuer() {
        return issuer;
    }

    public void setIssuer(BasicInfo issuer) {
        this.issuer = issuer;
    }

    public BasicInfo getThisUpdate() {
        return thisUpdate;
    }

    public void setThisUpdate(BasicInfo thisUpdate) {
        this.thisUpdate = thisUpdate;
    }

    public BasicInfo getNextUpdate() {
        return nextUpdate;
    }

    public void setNextUpdate(BasicInfo nextUpdate) {
        this.nextUpdate = nextUpdate;
    }

    public BasicInfo getSignatureAlgorithm() {
        return signatureAlgorithm;
    }

    public void setSignatureAlgorithm(BasicInfo signatureAlgorithm) {
        this.signatureAlgorithm = signatureAlgorithm;
    }

    public BasicInfo getSignature() {
        return signature;
    }

    public void setSignature(BasicInfo signature) {
        this.signature = signature;
    }

    public List<RevokedInfo> getRevokedInfoList() {
        return revokedInfoList;
    }

    public void setRevokedInfoList(List<RevokedInfo> revokedInfoList) {
        this.revokedInfoList = revokedInfoList;
    }

    public List<ExtensionInfo> getExtensionInfo() {
        return extensionInfo;
    }

    public void setExtensionInfo(List<ExtensionInfo> extensionInfo) {
        this.extensionInfo = extensionInfo;
    }

    public List<AttributeInfo> getAttributeInfo() {
        return attributeInfo;
    }

    public void setAttributeInfo(List<AttributeInfo> attributeInfo) {
        this.attributeInfo = attributeInfo;
    }

}
