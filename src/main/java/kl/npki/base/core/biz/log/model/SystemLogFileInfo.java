package kl.npki.base.core.biz.log.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.time.LocalDateTime;

/**
 * 系统日志文件信息
 *
 * <AUTHOR>
 * @since 2025/7/7 16:19
 */
public class SystemLogFileInfo {


    /**
     * 文件id
     */
    private Long id;

    /**
     * 文件类型
     */
    private Integer fileType;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * Excel文件中的数据处理状态
     * <li>-1 尚未开始处理</li>
     * <li>0 正在处理</li>
     * <li>1 处理完成</li>
     */
    private Integer processStatus;

    /**
     * 创建时间
     */
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getFileType() {
        return fileType;
    }

    public void setFileType(Integer fileType) {
        this.fileType = fileType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(Integer processStatus) {
        this.processStatus = processStatus;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}

