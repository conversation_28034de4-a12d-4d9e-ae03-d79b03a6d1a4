package kl.npki.base.core.biz.cert.parser.constant;

import kl.nbase.i18n.i18n.EnumI18n;
import kl.nbase.security.asn1.x509.AccessDescription;

import java.util.Arrays;
import java.util.Optional;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * <AUTHOR>
 * @date 2024/8/15 11:06
 */
public enum AuthorityInfoAccessEnum implements EnumI18n {
    CA_ISSUERS(AccessDescription.id_ad_caIssuers.getId(), "证书颁发机构颁发者"),
    OCSP(AccessDescription.id_ad_ocsp.getId(), "联机证书状态协议"),
    ;

    private String code;
    private String description;

    AuthorityInfoAccessEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return tr();
    }

    public static String getDescriptionByCode(String code) {
        Optional<AuthorityInfoAccessEnum> authorityInfoAccess = Arrays.stream(values()).filter(value -> value.getCode().equals(code)).findFirst();
        return authorityInfoAccess.map(AuthorityInfoAccessEnum::getDescription).orElse(null);
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }

}
