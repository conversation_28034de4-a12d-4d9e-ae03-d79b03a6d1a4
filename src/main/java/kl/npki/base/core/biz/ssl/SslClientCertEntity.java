package kl.npki.base.core.biz.ssl;

import kl.nbase.security.asn1.custom.pkcs.EnvelopedKeyPairData;
import kl.nbase.security.asn1.x500.X500Name;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.util.encoders.Hex;
import kl.npki.base.core.asn1.custom.EnvelopedKeyPairDataFactory;
import kl.npki.base.core.constant.CertStatus;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.utils.CertUtil;
import org.apache.commons.codec.binary.Base64;

import java.io.IOException;
import java.io.Serializable;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
public class SslClientCertEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 子系统id
     */
    private Long sysId;

    /**
     * 签名证书序列号
     */
    private String sn;

    /**
     * 加密证书序列号
     */
    private String encSn;

    private String cn;

    private String dn;

    /**
     * 签名证书值
     */
    private String certValue;

    /**
     * 加密证书值
     */
    private String encCertValue;

    /**
     * 站点加密证书的私钥(数字信封格式)
     */
    private String envelopedPriKeyData;

    private Date notBefore;

    private Date notAfter;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private int status;

    /**
     * 填充加密证书相关数据
     * @param encCert
     * @throws IOException
     */
    public void fillEncCertInfo(Certificate encCert, PrivateKey privateKey) throws IOException {
        // 获取接收者的公钥信息
        PublicKey receiverPublicKey;
        try {
            Certificate signCert = CertUtil.parseCert(this.certValue);
            receiverPublicKey = CertUtil.getPublicKey(signCert.getSubjectPublicKeyInfo());
        } catch (IOException e) {
            throw BaseInternalError.GET_PUBLIC_KEY_ERROR.toException(e);
        }
        // 生成数字信封
        EnvelopedKeyPairData envelopedKeyPairData = EnvelopedKeyPairDataFactory.genEnvelopedKeyPair(privateKey.getEncoded(), receiverPublicKey);

        // 填充加密证书相关数据
        String hexSn = Hex.toHexString(encCert.getSerialNumber().getValue().toByteArray()).toUpperCase();
        this.setEncSn(hexSn);
        this.setEncCertValue(Base64.encodeBase64String(encCert.getEncoded()));
        this.setEnvelopedPriKeyData(Base64.encodeBase64String(envelopedKeyPairData.getEncoded()));
    }

    public static SslClientCertEntity buildSslClientCertEntity(Certificate certificate, Long sysId) throws IOException {
        SslClientCertEntity sslCertEntity = new SslClientCertEntity();
        // 解析证书，将证书相关配置项存入数据库
        String hexSn = Hex.toHexString(certificate.getSerialNumber().getValue().toByteArray()).toUpperCase();
        X500Name subject = certificate.getSubject();
        Date beginDate = certificate.getStartDate().getDate();
        Date endDate = certificate.getEndDate().getDate();
        sslCertEntity.setCertValue(Base64.encodeBase64String(certificate.getEncoded()));
        sslCertEntity.setCn(CertUtil.getCommonName(subject));
        sslCertEntity.setCreateTime(LocalDateTime.now());
        sslCertEntity.setNotBefore(beginDate);
        sslCertEntity.setNotAfter(endDate);
        sslCertEntity.setDn(subject.toString());
        sslCertEntity.setSn(hexSn);
        sslCertEntity.setStatus(CertStatus.ISSUED.getCode());
        sslCertEntity.setSysId(sysId);
        return sslCertEntity;
    }

    public String getEnvelopedPriKeyData() {
        return envelopedPriKeyData;
    }

    public SslClientCertEntity setEnvelopedPriKeyData(String envelopedPriKeyData) {
        this.envelopedPriKeyData = envelopedPriKeyData;
        return this;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getCn() {
        return cn;
    }

    public void setCn(String cn) {
        this.cn = cn;
    }

    public String getDn() {
        return dn;
    }

    public void setDn(String dn) {
        this.dn = dn;
    }

    public String getCertValue() {
        return certValue;
    }

    public void setCertValue(String certValue) {
        this.certValue = certValue;
    }

    public String getEncSn() {
        return encSn;
    }

    public SslClientCertEntity setEncSn(String encSn) {
        this.encSn = encSn;
        return this;
    }

    public String getEncCertValue() {
        return encCertValue;
    }

    public SslClientCertEntity setEncCertValue(String encCertValue) {
        this.encCertValue = encCertValue;
        return this;
    }

    public Date getNotBefore() {
        return notBefore;
    }

    public void setNotBefore(Date notBefore) {
        this.notBefore = notBefore;
    }

    public Date getNotAfter() {
        return notAfter;
    }

    public void setNotAfter(Date notAfter) {
        this.notAfter = notAfter;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Long getSysId() {
        return sysId;
    }

    public void setSysId(Long sysId) {
        this.sysId = sysId;
    }

}

