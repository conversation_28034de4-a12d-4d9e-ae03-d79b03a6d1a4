package kl.npki.base.core.biz.log.model;

import kl.npki.base.core.constant.UploadFileProcessStatusEnum;
import kl.npki.base.core.repository.ISystemLogFileRepository;
import kl.npki.base.core.repository.RepositoryFactory;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统日志文件操作对象
 *
 * <AUTHOR>
 * @since 2025/7/8 13:41
 */
public class SystemLogFileEntity implements Serializable {

    private static final long serialVersionUID = -1432835703670930649L;

    /**
     * 文件id
     */
    private Long id;

    /**
     * 文件类型
     */
    private Integer fileType;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件内容
     */
    private String fileContent;

    /**
     * 文件签名标识：true为已签名
     */
    private Boolean logSignFlag;

    /**
     * Excel文件中的数据处理状态
     * <li>-1 尚未开始处理</li>
     * <li>0 正在处理</li>
     * <li>1 处理完成</li>
     */
    private Integer processStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    private static final ISystemLogFileRepository SYSTEM_LOG_FILE_REPOSITORY = RepositoryFactory.get(ISystemLogFileRepository.class);

    /**
     * 保存CA对象
     *
     * @return ca标识id
     */
    public Long save() {
        setProcessStatus(UploadFileProcessStatusEnum.PROCESSING.getStatus());
        LocalDateTime now = LocalDateTime.now();
        setCreateTime(now);
        setUpdateTime(now);
        return SYSTEM_LOG_FILE_REPOSITORY.importFile(this);
    }

    public boolean update() {
        setUpdateTime(LocalDateTime.now());
        setProcessStatus(UploadFileProcessStatusEnum.COMPLETE.getStatus());
        return SYSTEM_LOG_FILE_REPOSITORY.update(this);
    }

    public void delete() {
        SYSTEM_LOG_FILE_REPOSITORY.delete(this.id);
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getFileType() {
        return fileType;
    }

    public void setFileType(Integer fileType) {
        this.fileType = fileType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileContent() {
        return fileContent;
    }

    public void setFileContent(String fileContent) {
        this.fileContent = fileContent;
    }

    public Integer getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(Integer processStatus) {
        this.processStatus = processStatus;
    }

    public Boolean getLogSignFlag() {
        return logSignFlag;
    }

    public void setLogSignFlag(Boolean logSignFlag) {
        this.logSignFlag = logSignFlag;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
