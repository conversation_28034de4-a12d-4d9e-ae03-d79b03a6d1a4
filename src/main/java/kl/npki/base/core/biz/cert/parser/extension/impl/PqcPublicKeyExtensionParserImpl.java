package kl.npki.base.core.biz.cert.parser.extension.impl;

import kl.nbase.security.util.encoders.Hex;
import kl.npki.base.core.biz.cert.parser.constant.ExtensionType;
import kl.npki.base.core.biz.cert.parser.extension.AbstractX509ExtensionParser;

/**
 * <AUTHOR>
 * @date 2024/8/19 16:46
 */
public class PqcPublicKeyExtensionParserImpl extends AbstractX509ExtensionParser {

    /**
     * 生成一个易于阅读的扩展值字符串，此方法旨在将字节数组形式的扩展值转换为便于人类阅读和理解的字符串格式
     * <p>
     * 具体的转换逻辑由子类实现，因为不同的场景可能需要不同的格式化规则
     *
     * @param extensionOctets 扩展值的字节表示形式
     * @return 格式化后的字符串
     */
    @Override
    protected String generatePrettyValue(byte[] extensionOctets) {
        // 默认情况下，将扩展内容16进制编码后输出
        return Hex.toHexString(extensionOctets);
    }

    /**
     * 获取扩展项类型
     *
     * @return 返回当前对象扩展项类型 {@link ExtensionType}
     */
    @Override
    public ExtensionType getExtensionType() {
        return ExtensionType.PQC_PUBLIC_KEY;
    }
}
