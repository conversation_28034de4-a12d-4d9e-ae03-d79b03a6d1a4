package kl.npki.base.core.biz.cert.parser.extension.impl;

import kl.npki.base.core.biz.cert.parser.constant.ExtensionType;
import kl.npki.base.core.biz.cert.parser.extension.AbstractX509ExtensionParser;
import kl.npki.base.core.utils.CertExtensionUtils;

/**
 * <AUTHOR>
 * @date 2024/8/15 14:46
 */
public class DeltaCrlIndicatorExtensionParserImpl extends AbstractX509ExtensionParser {
    /**
     * 增量CRL:
     */
    private static final String DELTA_CRL = "incremental_crl";

    /**
     * 生成一个易于阅读的扩展值字符串，此方法旨在将字节数组形式的扩展值转换为便于人类阅读和理解的字符串格式
     * <p>
     * 具体的转换逻辑由子类实现，因为不同的场景可能需要不同的格式化规则
     *
     * @param extensionOctets 扩展值的字节表示形式
     * @return 格式化后的字符串
     */
    @Override
    protected String generatePrettyValue(byte[] extensionOctets) {
        return CertExtensionUtils.generatePrettyValue(extensionOctets, DELTA_CRL);
    }


    /**
     * 生成一个扩展值字符
     * <p>
     * 默认等同{@link AbstractX509ExtensionParser#generatePrettyValue(byte[])}，可由子类扩展
     * 因为不同的场景可能需要不同的格式化规则，有的与prettyValue不同，则prettyValue不能满足，需要重写此方法
     *
     * @param extensionOctets
     * @return 字符串格式的扩展值
     */
    @Override
    protected String generateValue(byte[] extensionOctets) {
        return CertExtensionUtils.generateValue(extensionOctets);
    }


    /**
     * 获取扩展项类型
     *
     * @return 返回当前对象扩展项类型 {@link ExtensionType}
     */
    @Override
    public ExtensionType getExtensionType() {
        return ExtensionType.DELTA_CRL_INDICATOR;
    }
}
