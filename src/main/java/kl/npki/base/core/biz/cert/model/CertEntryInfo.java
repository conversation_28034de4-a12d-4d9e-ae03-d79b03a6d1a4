package kl.npki.base.core.biz.cert.model;


import kl.nbase.exception.BaseException;
import kl.nbase.security.asn1.x509.Certificate;
import kl.npki.base.core.biz.cert.service.CertVerifyHelper;
import kl.npki.base.core.constant.CertPurposeEnum;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.core.utils.CertUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static kl.npki.base.core.utils.CertUtil.CERT_TYPE_CA;
import static kl.npki.base.core.utils.CertUtil.CERT_TYPE_ENTITY;

/**
 * 证书条目信息
 *
 * <AUTHOR>
 * @Date 2025/5/26
 */
public class CertEntryInfo {

    private final static Logger logger = LoggerFactory.getLogger(CertEntryInfo.class);

    /**
     * 当前证书
     */
    private X509Certificate certificate;

    /**
     * 证书链（不包含当前证书），允许为空，处理P7/PFX/JKS的场景下才有效
     */
    private List<X509Certificate> certificateChain;

    /**
     * 私钥，允许为空，处理PFX/JKS的场景下才有效
     */
    private PrivateKey privateKey;

    /**
     * 证书条目在KeyStore中的别名，允许为空，处理PFX/JKS的场景下才有效
     */
    private String alias;

    /**
     * 证书用途
     */
    private CertPurposeEnum certPurpose;

    public CertEntryInfo(X509Certificate certificate) {
        this.certificate = certificate;
    }

    public CertEntryInfo(X509Certificate certificate, List<X509Certificate> certificateChain) {
        this.certificate = certificate;
        this.certificateChain = certificateChain;
    }

    public CertEntryInfo(X509Certificate certificate, List<X509Certificate> certificateChain, PrivateKey privateKey) {
        this.certificate = certificate;
        this.certificateChain = certificateChain;
        this.privateKey = privateKey;
    }

    public CertEntryInfo(X509Certificate certificate, List<X509Certificate> certificateChain, PrivateKey privateKey, String alias) {
        this.certificate = certificate;
        this.certificateChain = certificateChain;
        this.privateKey = privateKey;
        this.alias = alias;
    }

    public X509Certificate getCertificate() {
        return certificate;
    }

    public void setCertificate(X509Certificate certificate) {
        this.certificate = certificate;
    }

    public List<X509Certificate> getCertificateChain() {
        return certificateChain;
    }

    public void setCertificateChain(List<X509Certificate> certificateChain) {
        this.certificateChain = certificateChain;
    }

    public PrivateKey getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(PrivateKey privateKey) {
        this.privateKey = privateKey;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public CertPurposeEnum getCertPurpose() {
        return certPurpose;
    }

    public void setCertPurpose(CertPurposeEnum certPurpose) {
        this.certPurpose = certPurpose;
    }

    /**
     * 从PEM格式字符串创建证书条目（支持包含证书链的PEM）
     *
     * @param pemContent PEM格式的证书内容
     * @return 证书条目信息
     */
    public static CertEntryInfo fromPem(String pemContent) {
        try {
            // 解析PEM中的所有证书
            Map<String, List<Certificate>> certMap = CertUtil.parseMultipleCert(pemContent);
            List<Certificate> entityCerts = certMap.getOrDefault(CERT_TYPE_ENTITY, Collections.emptyList());
            List<Certificate> caCerts = certMap.getOrDefault(CERT_TYPE_CA, Collections.emptyList());

            X509Certificate entityX509Cert = null;
            if (!entityCerts.isEmpty()) {
                entityX509Cert = kl.nbase.security.utils.CertUtil.derToX509(entityCerts.get(0).getEncoded());
            }

            // 转换CA证书链
            List<X509Certificate> chainCerts = new ArrayList<>();
            for (Certificate caCert : caCerts) {
                chainCerts.add(kl.nbase.security.utils.CertUtil.derToX509(caCert.getEncoded()));
            }

            return new CertEntryInfo(entityX509Cert, chainCerts);
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Failed to parse PEM content: {}", pemContent, e);
            throw BaseValidationError.CERT_PARSE_FAILED.toException(e);
        }
    }

    /**
     * 确定证书的用途（签名、加密或双用途）
     */
    public void determineCertPurpose() {
        try {
            X509Certificate x509Cert = this.getCertificate();
            if (x509Cert == null) {
                return;
            }
            Certificate cert = kl.nbase.security.utils.CertUtil.derToCert(x509Cert.getEncoded());

            boolean isSignCert = CertVerifyHelper.isSigningCert(cert);
            boolean isEncCert = CertVerifyHelper.isEncryptionCert(cert);

            if (isSignCert && !isEncCert) {
                // 仅具有签名用途
                this.setCertPurpose(CertPurposeEnum.SIGN);
            } else if (!isSignCert && isEncCert) {
                // 仅具有加密用途
                this.setCertPurpose(CertPurposeEnum.ENCRYPT);
            } else if (isSignCert && isEncCert) {
                // 同时具有签名和加密用途
                this.setCertPurpose(CertPurposeEnum.SIGN_AND_ENCRYPT);
            } else {
                // 既不是签名也不是加密证书，设置为无用途
                this.setCertPurpose(CertPurposeEnum.NONE);
            }
        } catch (Exception e) {
            logger.error("Failed to determine the certificate purpose", e);
            this.setCertPurpose(CertPurposeEnum.NONE);
        }
    }

}
