package kl.npki.base.core.biz.cert.model.parser.x509;

import kl.nbase.security.asn1.x509.Extension;

/**
 * 扩展项信息
 *
 * <AUTHOR>
 * @date 2024-08-08
 */
public class ExtensionInfo {

    /**
     * 标识该对象是否为关键对象
     */
    private boolean critical;

    /**
     * 对象的OID（Object Identifier），是一个唯一标识符，用于在网络管理协议中标识对象
     */
    private String oid;

    /**
     * 对象的名称，用于标识对象的唯一性
     */
    private String name;

    /**
     * 对象的描述，用于详细说明对象的用途和特性
     */
    private String desc;

    /**
     * ASN.1编码值，扩展项值{@link Extension#getEncoded()}的Base64编码数据
     */
    private String asnValue;

    private String value;

    /**
     * 对象的易读表示形式，用于在用户界面或日志中显示
     */
    private String prettyValue;

    public boolean isCritical() {
        return critical;
    }

    public void setCritical(boolean critical) {
        this.critical = critical;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getAsnValue() {
        return asnValue;
    }

    public void setAsnValue(String asnValue) {
        this.asnValue = asnValue;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getPrettyValue() {
        return prettyValue;
    }

    public void setPrettyValue(String prettyValue) {
        this.prettyValue = prettyValue;
    }
}
