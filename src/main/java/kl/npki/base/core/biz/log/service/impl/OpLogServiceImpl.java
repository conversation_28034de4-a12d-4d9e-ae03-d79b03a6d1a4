package kl.npki.base.core.biz.log.service.impl;

import kl.npki.base.core.biz.log.model.OpLogInfo;
import kl.npki.base.core.biz.log.service.IOpLogService;
import kl.npki.base.core.repository.ILogBaseRepository;
import kl.npki.base.core.repository.IOpLogRepository;
import kl.npki.base.core.repository.RepositoryFactory;

/**
 * <AUTHOR>
 * @create 2025/2/11 下午5:50
 */
public class OpLogServiceImpl implements IOpLogService {

    private final IOpLogRepository repository = RepositoryFactory.get(IOpLogRepository.class);

    @Override
    public ILogBaseRepository<OpLogInfo> getLogRepository() {
        return repository;
    }

    @Override
    public Class<OpLogInfo> getEntityClass() {
        return OpLogInfo.class;
    }
}