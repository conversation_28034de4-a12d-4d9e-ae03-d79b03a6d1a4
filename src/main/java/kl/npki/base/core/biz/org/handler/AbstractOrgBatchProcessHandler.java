package kl.npki.base.core.biz.org.handler;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.utils.ExcelHeaderUtil;
import org.apache.poi.ss.usermodel.*;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 机构批量业务Excel处理类
 *
 * <AUTHOR>
 * @create 2024/3/27 10:31
 */
abstract class AbstractOrgBatchProcessHandler {

    /**
     * 批量处理结束后处理结果文件名称
     */
    protected final String fileName;
    /**
     * 批量处理结果Excel输出流
     */
    protected final ExcelWriter excelWriter;
    /**
     * Excel的工作表
     */
    protected final WriteSheet writeSheet;
    /**
     * Excel文件中时间格式：yyyyMMddHHmmssSSS
     */
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");

    protected AbstractOrgBatchProcessHandler() {
        this.fileName = LocalDateTime.now().format(formatter) + ".xlsx";

        // 配置表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 设置背景色为浅绿色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
        headWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        // 设置边框样式
        headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        headWriteCellStyle.setBorderTop(BorderStyle.THIN);
        headWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        headWriteCellStyle.setBorderRight(BorderStyle.THIN);

        // 配置内容样式
        WriteCellStyle contentCellStyle = new WriteCellStyle();
        contentCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        contentCellStyle.setVerticalAlignment(VerticalAlignment.TOP);
        // 设置边框样式
        contentCellStyle.setBorderBottom(BorderStyle.THIN);
        contentCellStyle.setBorderTop(BorderStyle.THIN);
        contentCellStyle.setBorderLeft(BorderStyle.THIN);
        contentCellStyle.setBorderRight(BorderStyle.THIN);

        HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentCellStyle);

        // 设置列宽
        SimpleColumnWidthStyleStrategy simpleColumnWidthStyleStrategy = new SimpleColumnWidthStyleStrategy(20);

        try {
            excelWriter = EasyExcelFactory.write(new FileOutputStream(fileName)).build();
        } catch (FileNotFoundException e) {
            throw BaseInternalError.ORG_BATCH_IMPORT_EXCEL_GEN_ERROR.toException(e);
        }
        writeSheet = EasyExcelFactory.writerSheet("Sheet1")
                .head(ExcelHeaderUtil.generateHeader(getModeType(), null))
                .registerWriteHandler(styleStrategy)
                .registerWriteHandler(simpleColumnWidthStyleStrategy)
                .build();
    }

    /**
     * 获取数据类型，用于构造Excel处理结果中的头
     *
     * @return
     */
    public abstract Class<?> getModeType();

    /**
     * 获取处理结果
     *
     * @return
     */
    public File getResultFile() {
        excelWriter.finish();
        return new File(fileName);
    }
}