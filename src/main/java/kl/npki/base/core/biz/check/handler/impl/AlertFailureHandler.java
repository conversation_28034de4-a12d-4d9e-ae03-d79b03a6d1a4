package kl.npki.base.core.biz.check.handler.impl;

import kl.npki.base.core.biz.check.handler.AlertService;
import kl.npki.base.core.biz.check.handler.SelfCheckResultObserver;
import kl.npki.base.core.biz.check.model.SelfCheckResult;
import kl.npki.base.core.biz.check.model.Status;

/**
 * 处理服务自检失败的情况。
 * 当服务自检失败时，它会发送告警
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
public class AlertFailureHandler implements SelfCheckResultObserver {

    private final AlertService alertService;

    public AlertFailureHandler(AlertService alertService) {
        this.alertService = alertService;
    }

    @Override
    public void handle(SelfCheckResult result) {
        if (Status.FAILURE.equals(result.getStatus())) {
            alertService.sendAlert(result);
        }
    }
}
