package kl.npki.base.core.biz.log.model;

/**
 * 服务运行日志统计结果
 *
 * <AUTHOR>
 */
public class ApiLogCountServiceResult {
    private int count;
    private String callerName;
    private boolean result;
    private String biz;

    public int getCount() {
        return count;
    }

    public ApiLogCountServiceResult setCount(int count) {
        this.count = count;
        return this;
    }

    public String getCallerName() {
        return callerName;
    }

    public ApiLogCountServiceResult setCallerName(String callerName) {
        this.callerName = callerName;
        return this;
    }

    public boolean isResult() {
        return result;
    }

    public ApiLogCountServiceResult setResult(boolean result) {
        this.result = result;
        return this;
    }

    public String getBiz() {
        return biz;
    }

    public ApiLogCountServiceResult setBiz(String biz) {
        this.biz = biz;
        return this;
    }
}
