package kl.npki.base.core.biz.config.service;

import kl.npki.base.core.biz.config.model.ConfigStatus;

import static kl.npki.base.core.constant.I18nConstant.getBaseCoreI18nMessage;

/**
 * 系统配置状态服务类接口
 * 此接口的实现类会加入到系统配置的配置进度中
 *
 * <AUTHOR>
 */
public interface IConfigStatusService {
    String DB_NOT_CONFIGURED = getBaseCoreI18nMessage("database_not_configured");

    /**
     * 查询配置状态
     *
     * @return {@link ConfigStatus}
     */
    ConfigStatus queryConfigStatus();

    /**
     * 数据库配置未完成时查询配置状态
     *
     * @return {@link ConfigStatus}
     */
    default ConfigStatus queryConfigStatusWhenDbNotReady() {
        return queryConfigStatus();
    }

    /**
     * 该配置在部署阶段是否是必须的，如果是，则会加入到系统配置的部署配置进度中
     *
     * @return
     */
    default boolean isRequiredConfig() {
        return true;
    }

    /**
     * 是否需要在前端界面中展示该配置项。
     *
     * 某些特定场景下（例如：使用 SSO 登录认证，不涉及管理员证书签发），
     * 可通过重写该方法返回 false，前端将不展示该配置项。
     *
     * @return true 表示需要展示；false 表示前端应隐藏该配置项。
     */
    default boolean isVisible() {
        return true;
    }
}
