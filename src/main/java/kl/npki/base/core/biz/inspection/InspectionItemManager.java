package kl.npki.base.core.biz.inspection;

import kl.npki.base.core.biz.inspection.filter.InspectionItemFilter;
import kl.npki.base.core.biz.inspection.model.InspectionItemResult;
import kl.npki.base.core.thread.ThreadExecutorFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 巡检项目管理器，负责收集巡检项目以及执行巡检
 *
 * <AUTHOR>
 * @date 08/05/2025 16:13
 **/
public class InspectionItemManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(InspectionItemManager.class);

    private static final List<IInspectionItem> ALL_ITEMS = new ArrayList<>(8);
    private static final InspectionItemManager INSTANCE = new InspectionItemManager();
    private static final List<InspectionItemFilter> FILTERS = new CopyOnWriteArrayList<>();

    public static InspectionItemManager getInstance() {
        return INSTANCE;
    }

    static {
        ServiceLoader<InspectionItemFilter> filters = ServiceLoader.load(InspectionItemFilter.class);
        filters.forEach(filter ->
            {
                if (filter != null) {
                    LOGGER.info("Loaded InspectionItemFilter: {}", filter.getClass().getSimpleName());
                    FILTERS.add(filter);
                }
            }
        );
    }

    public void init(InspectionItemCollector collector) {
        List<IInspectionItem> allItems = collector.collect();
        // 本处不做过滤，在使用时过滤，因为有些可能会因为需要进行动态过滤
        allItems.forEach(item -> {
            ALL_ITEMS.add(item);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("InspectionItemManager initialization {} successful", item.getClass().getSimpleName());
            }
        });
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("InspectionItemManager initialization completed, initializing {} IInspectionItem instances in total", allItems.size());
        }
    }

    public Map<String, List<String>> getGroupedInspectionItems() {
        return ALL_ITEMS.stream().filter(item ->
                FILTERS.stream().noneMatch(filter -> filter.filter(item))
            )
            .sorted(Comparator.comparingInt(item -> item.getInspectionItemType().ordinal()))
            .collect(Collectors.groupingBy( item ->
                item.getInspectionItemType().getI18nDesc(), LinkedHashMap::new, Collectors.mapping(IInspectionItem::getName, Collectors.toList())));
    }

    public List<InspectionItemResult> executeInspection() {

        // 获取异步线程执行器
        ExecutorService inspectionExecutor = ThreadExecutorFactory.getExecutorService("Inspection");

        // 过滤巡检项目
        List<IInspectionItem> filteredItems = ALL_ITEMS.stream().filter(item ->
            FILTERS.stream().noneMatch(filter -> filter.filter(item))
        ).collect(Collectors.toList());

        List<InspectionItemResult> inspectionResults = new ArrayList<>(filteredItems.size());
        List<CompletableFuture<InspectionItemResult>> futureList = new ArrayList<>(filteredItems.size());
        filteredItems.forEach(item -> {
            Supplier<InspectionItemResult> supplier = () -> {
                try {
                    return item.execute();
                } catch (Exception e) {
                    LOGGER.error("Exception occurred while executing inspection item: {}", item.getClass().getSimpleName(), e);
                    return InspectionItemResult.fail(item.getName(), item.getInspectionItemType(), e.getMessage());
                }
            };
            CompletableFuture<InspectionItemResult> future = CompletableFuture.supplyAsync(supplier, inspectionExecutor);
            futureList.add(future);
        });

        try {
            // 等待所有任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));

            // 阻塞直到全部完成
            allFutures.join();

            // 收集结果
            for (CompletableFuture<InspectionItemResult> future : futureList) {
                try {
                    inspectionResults.add(future.get());
                } catch (ExecutionException e) {
                    LOGGER.error("Exception occurred while executing inspection item", e);
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while waiting for inspection tasks to complete", e);
            // 恢复中断状态
            Thread.currentThread().interrupt();
        }

        return inspectionResults;
    }
}
