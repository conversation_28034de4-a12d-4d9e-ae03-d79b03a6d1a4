package kl.npki.base.core.biz.check;

import kl.npki.base.core.biz.check.handler.SelfCheckItemCollector;
import kl.npki.base.core.biz.check.handler.SelfCheckResultObserver;
import kl.npki.base.core.biz.check.handler.SelfCheckResultRepository;
import kl.npki.base.core.biz.check.handler.impl.AlertFailureHandler;
import kl.npki.base.core.biz.check.handler.impl.DefaultAlertServiceImpl;
import kl.npki.base.core.biz.check.handler.impl.InMemoryCheckResultRepositoryImpl;
import kl.npki.base.core.biz.check.handler.impl.SelfCheckResultLogHandler;
import kl.npki.base.core.biz.check.model.*;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.I18nConstant;
import kl.npki.base.core.constant.RootCertificateDeployModeEnum;
import kl.npki.base.core.exception.BaseValidationError;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static kl.npki.base.core.constant.I18nConstant.*;
import static kl.npki.base.core.constant.I18nExceptionInfoConstant.THE_CHECK_ITEM_IS_NOT_REGISTERED_I18N_KEY;

/**
 * 服务自检管理
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
public class SelfCheckManager {

    private static final Logger log = LoggerFactory.getLogger(SelfCheckManager.class);
    private static final SelfCheckManager INSTANCE = new SelfCheckManager();
    private final Map<String, ISelfCheckItem> checkItems = new ConcurrentHashMap<>(32);
    private final List<SelfCheckResultObserver> observers = new ArrayList<>();
    private final SelfCheckResultRepository repository;

    private SelfCheckManager() {
        repository = new InMemoryCheckResultRepositoryImpl();
        // 添加默认的ICheckItem

        // 添加默认的CheckResultObServer
        observers.add(new SelfCheckResultLogHandler(repository));
        observers.add(new AlertFailureHandler(new DefaultAlertServiceImpl(repository)));
    }

    public static SelfCheckManager getInstance() {
        return INSTANCE;
    }

    public void init(SelfCheckItemCollector collector) {
        collector.collect().forEach((k, v) -> {
            try {
                checkItems.put(v.getFullCode(), v);
                log.debug("SelfCheckManager initialization {} successful", k.getSimpleName());
            } catch (Exception e) {
                log.error("SelfCheckManager initialization {} failed", k.getSimpleName(), e);
            }
        });
        if (log.isInfoEnabled()) {
            log.info("SelfCheckManager initialization completed, initializing {} ISelfCheckItem instances in total", checkItems.size());
        }
    }

    /**
     * 移除自检项目
     *
     * @param checkItem 自检项目
     */
    public void remove(ISelfCheckItem checkItem) {
        this.checkItems.remove(checkItem.getFullCode());
    }

    /**
     * 添加自检项目
     *
     * @param checkItem 自检项目
     */
    public void addCheckItem(ISelfCheckItem checkItem) {
        this.checkItems.put(checkItem.getFullCode(), checkItem);
    }

    /**
     * 添加自检观察者处理器
     *
     * @param resultObserver 观察者
     */
    public void addObserverHandler(SelfCheckResultObserver resultObserver) {
        this.observers.add(resultObserver);
    }

    /**
     * 移除自检项目
     *
     * @param name 自检项目名称
     */
    public void removeCheckItem(String name) {
        this.checkItems.remove(name);
    }

    /**
     * 移除自检观察者处理器
     *
     * @param resultObserver 观察者
     */
    public void removeObserverHandler(SelfCheckResultObserver resultObserver) {
        observers.remove(resultObserver);
    }

    /**
     * 检测所有的自检项
     */
    public void checkAll() {
        // 检测前清除自检数据
        repository.removeAll();
        for (ISelfCheckItem checkItem : checkItems.values()) {
            doCheck(checkItem);
        }
    }

    /**
     * 检测指定的自检项
     *
     * @param code 自检项编码
     */
    public void checkItem(String code) {
        ISelfCheckItem checkItem = checkItems.get(code);
        if (checkItem != null) {
            repository.removeByCode(code);
            doCheck(checkItem);
        } else {
            throw BaseValidationError.SELF_CHECK_NOT_SUPPORTED.toException(THE_CHECK_ITEM_IS_NOT_REGISTERED_I18N_KEY, code);
        }
    }

    private void doCheck(ISelfCheckItem checkItem) {
        if (checkItem == null || !checkItem.isEnabled()) {
            return;
        }

        SelfCheckResult result;
        try {
            result = checkItem.check();
        } catch (Exception e) {
            log.error("Self check item [{}] detected an exception: ", checkItem.getFullCode(), e);
            result = createErrorResult(checkItem, e);
        }

        notifyObservers(result);
    }

    private SelfCheckResult createErrorResult(ISelfCheckItem checkItem, Exception e) {
        SelfCheckResult errorResult = SelfCheckResult.builder()
            .code(checkItem.getFullCode())
            .name(checkItem.getName())
            .category(checkItem.getCategory().getName())
            .status(Status.FAILURE)
            .severity(Severity.HIGH)
            .checkTime(LocalDateTime.now())
            .message(getBaseCoreI18nMessage(SELF_CHECK_EXCEPTION_OCCURRED_I18N_KEY))
            .build();

        // 只存储异常类型和消息，避免完整堆栈信息
        Map<String, Object> details = new HashMap<>();
        details.put(getBaseCoreI18nMessage(SELF_CHECK_EXCEPTION_TYPE_I18N_KEY), e.getClass().getSimpleName());
        details.put(getBaseCoreI18nMessage(SELF_CHECK_EXCEPTION_MESSAGE_I18N_KEY), e.getMessage());
        errorResult.setDetails(details);
        errorResult.setDetails(details);

        return errorResult;
    }

    private void notifyObservers(SelfCheckResult result) {
        if (result == null) {
            return;
        }

        for (SelfCheckResultObserver observer : observers) {
            try {
                observer.handle(result);
            } catch (Exception e) {
                log.warn("Observer [{}] failed to handle result: ", observer.getClass().getSimpleName(), e);
            }
        }
    }

    public Map<String, ISelfCheckItem> getCheckItems() {
        return checkItems;
    }

    /**
     * 查询所有的自检数据
     *
     * @return 所有的自检数据集合
     */
    public List<SelfCheckResult> getAllCheckResult() {
        return repository.findAll();
    }

    public List<SelfCheckResult> getAllCheckResultByRole(Set<String> roleCodeSet) {
        List<SelfCheckResult> allCheckResult = this.getAllCheckResult();
        for (int i = 0; i < allCheckResult.size(); i++) {
            SelfCheckResult selfCheckResult = allCheckResult.get(i);
            ISelfCheckItem selfCheckItem = checkItems.get(selfCheckResult.getCode());
            if (!(selfCheckItem instanceof SolvableCheckItem)) {
                continue;
            }
            SelfCheckResult selfCheckResultByRole =
                ((SolvableCheckItem) selfCheckItem).updateSelfCheckResultByRole(selfCheckResult, roleCodeSet);
            allCheckResult.set(i, selfCheckResultByRole);
        }
        filterCheckMode(allCheckResult);
        return allCheckResult;
    }

    private void filterCheckMode(List<SelfCheckResult> allCheckResult) {
        // 获取根证书配置模式
        SysInfoConfig sysInfoConf = BaseConfigWrapper.getSysInfoConfig();
        String rootCertificateMode = sysInfoConf.getRootCertificateMode();
        // 获取证书根检测编码
        String rootCheckCode = Category.CERT.getCode() + SelfCheckItemEnum.MGT_ROOT_CERT_VALIDITY_CHECK.getCode();
        Iterator<SelfCheckResult> iterator = allCheckResult.iterator();
        while (iterator.hasNext()) {
            SelfCheckResult selfCheckResult = iterator.next();
            // 过滤根证书自检
            if (RootCertificateDeployModeEnum.SELF_SIGNED.getCode().equals(rootCertificateMode)) {
                if (rootCheckCode.equals(selfCheckResult.getCode())) {
                    iterator.remove();
                }
            }
        }
    }

}
