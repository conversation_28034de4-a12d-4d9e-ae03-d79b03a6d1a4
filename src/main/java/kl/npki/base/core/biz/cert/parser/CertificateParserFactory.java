package kl.npki.base.core.biz.cert.parser;

import kl.nbase.security.asn1.x509.Certificate;
import kl.npki.base.core.biz.cert.parser.impl.X509CertificateParserImpl;

/**
 * 证书解析工厂类，负责获取证书解析实例
 *
 * <AUTHOR>
 * @date 2024-08-09
 */
public class CertificateParserFactory {

    private CertificateParserFactory() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 获取X.509证书解析器实例
     *
     * @return X.509证书解析器实例
     */
    public static ICertificateParser<Certificate> getX509CertificateParser() {
        return new X509CertificateParserImpl();
    }
}

