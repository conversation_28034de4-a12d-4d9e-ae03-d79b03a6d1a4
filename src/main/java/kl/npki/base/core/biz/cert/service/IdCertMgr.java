package kl.npki.base.core.biz.cert.service;

import kl.nbase.security.asn1.ASN1Integer;
import kl.nbase.security.asn1.DERNull;
import kl.nbase.security.asn1.DEROctetString;
import kl.nbase.security.asn1.custom.gm.gm0014.km.EntName;
import kl.nbase.security.asn1.gm.GMObjectIdentifiers;
import kl.nbase.security.asn1.x500.X500Name;
import kl.nbase.security.asn1.x509.AlgorithmIdentifier;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.asn1.x509.GeneralName;
import kl.nbase.security.asn1.x509.SubjectPublicKeyInfo;
import kl.nbase.security.entity.algo.AsymAlgo;
import kl.npki.base.core.biz.cert.model.TrustCertEntity;
import kl.npki.base.core.constant.TrustCertType;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.repository.ITrustCertRepository;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.tenantholders.common.AbstractCommonTenant;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.base.core.utils.DigestUtils;
import kl.npki.base.core.utils.KeyUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.security.cert.X509Certificate;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.ID_CERT_PUBLIC_KEY_CONVERT_ERROR_I18N_KEY;

/**
 * @Author: guoq
 * @Date: 2022/9/7
 * @description: 业务证书管理(身份证书)
 */
public class IdCertMgr extends AbstractCommonTenant {

    /**
     * 管理证书实体的Repository
     */
    private final ITrustCertRepository trustCertRepository;

    private Certificate idCert;

    private String certValue;

    private EntName entName;

    private AsymAlgo asymAlgo;

    private String signatureAlgorithmOid;

    public IdCertMgr() {
        this.trustCertRepository = RepositoryFactory.get(ITrustCertRepository.class);
    }

    @Override
    public void refresh() {
        TrustCertEntity idCertEntity = trustCertRepository.getCert(TrustCertType.IDENTITY);
        if (idCertEntity == null || StringUtils.isBlank(idCertEntity.getCertValue())) {
            return;
        }
        certValue = idCertEntity.getCertValue();
        idCert = CertUtil.parseCert(idCertEntity.getCertValue());

        X500Name x500Name;
        byte[] hashSm3;
        try {
            hashSm3 = DigestUtils.digestBySm3(idCert.getSubjectPublicKeyInfo().getEncoded());
            x500Name = X500Name.getInstance(idCert.getSubject().getEncoded());
        } catch (IOException e) {
            throw BaseInternalError.CERT_PUBLICKEY_ECODE_ERROR.toException(e);
        }

        entName = new EntName(
            new AlgorithmIdentifier(GMObjectIdentifiers.sm3, DERNull.INSTANCE),
            new GeneralName(x500Name),
            new DEROctetString(hashSm3),
            new ASN1Integer(idCert.getSerialNumber().getValue())
        );

        SubjectPublicKeyInfo pubKeyInfo = idCert.getSubjectPublicKeyInfo();

        // 获取非对称算法
        try {
            asymAlgo = AsymAlgo.valueOf(KeyUtils.getPublicKey(pubKeyInfo));
        } catch (Exception e) {
            throw BaseInternalError.PUBLIC_KEY_CONVERT_ERROR.toException(ID_CERT_PUBLIC_KEY_CONVERT_ERROR_I18N_KEY, e);
        }
        signatureAlgorithmOid = idCert.getSignatureAlgorithm().getAlgorithm().toString();
    }

    public Certificate getIdCert() {
        return idCert;
    }

    public X509Certificate getX509Cert() {
        if(StringUtils.isBlank(certValue)){
            return null;
        }
        return CertUtil.parseX509Cert(certValue);
    }

    public String getCertValue() {
        return certValue;
    }

    public EntName getEntName() {
        return entName;
    }

    public AsymAlgo getAsymAlgo() {
        return asymAlgo;
    }

    public String getSignatureAlgorithmOid() {
        return signatureAlgorithmOid;
    }

}
