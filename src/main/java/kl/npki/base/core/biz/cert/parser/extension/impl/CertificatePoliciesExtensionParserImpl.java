package kl.npki.base.core.biz.cert.parser.extension.impl;

import kl.nbase.security.asn1.ASN1Encodable;
import kl.nbase.security.asn1.ASN1Sequence;
import kl.nbase.security.asn1.custom.gb.x509.Qualifier;
import kl.nbase.security.asn1.x509.CertificatePolicies;
import kl.nbase.security.asn1.x509.PolicyQualifierId;
import kl.nbase.security.asn1.x509.PolicyQualifierInfo;
import kl.npki.base.core.biz.cert.parser.constant.CertPoliciesEnum;
import kl.npki.base.core.biz.cert.parser.constant.ExtensionType;
import kl.npki.base.core.biz.cert.parser.extension.AbstractX509ExtensionParser;
import kl.npki.base.core.constant.I18nConstant;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/8/12 16:07
 */
public class CertificatePoliciesExtensionParserImpl extends AbstractX509ExtensionParser {
    /**
     * 未知证书策略
     */
    private static final String UNKNOWN_POLICY = "unknown_certificate_policy";
    private static final String CPS = "CPS: ";
    private static final String USERNOTICE = "UNOTICE: ";

    /**
     * 生成一个易于阅读的扩展值字符串，此方法旨在将字节数组形式的扩展值转换为便于人类阅读和理解的字符串格式
     * <p>
     * 具体的转换逻辑由子类实现，因为不同的场景可能需要不同的格式化规则
     *
     * @param extensionOctets 扩展值的字节表示形式
     * @return 格式化后的字符串
     */
    @Override
    protected String generatePrettyValue(byte[] extensionOctets) {
        CertificatePolicies certificatePolicies = CertificatePolicies.getInstance(extensionOctets);
        StringBuilder sb = new StringBuilder();
        Arrays.stream(certificatePolicies.getPolicyInformation()).forEach(policyInformation -> {
            // 根据oid判断是否为已知oid
            String oid = policyInformation.getPolicyIdentifier().getId();
            String certPoliciesDesc = CertPoliciesEnum.getCertPoliciesDescByOid(oid);
            if (StringUtils.isNotBlank(certPoliciesDesc)) {
                sb.append(certPoliciesDesc).append("（").append(oid).append("）").append(LINE_SEPARATOR);
            } else {
                sb.append(I18nConstant.getBaseCoreI18nMessage(UNKNOWN_POLICY)).append("（").append(oid).append("）").append(LINE_SEPARATOR);
            }

            // 存在具体策略值时添加信息
            ASN1Sequence policyQualifiers = policyInformation.getPolicyQualifiers();
            if (null != policyQualifiers) {
                for (ASN1Encodable policyQualifier : policyQualifiers) {
                    PolicyQualifierInfo policyQualifierInfo = PolicyQualifierInfo.getInstance(policyQualifier);
                    String id = policyQualifierInfo.getPolicyQualifierId().getId();
                    if (PolicyQualifierId.id_qt_cps.getId().equals(id)) {
                        sb.append(TAB).append(CPS);

                        // 获取添加CPS信息
                        Qualifier qualifier = Qualifier.getInstance(policyQualifierInfo.getQualifier());
                        sb.append(qualifier.getcPSuri().toString()).append(LINE_SEPARATOR);
                    } else if (PolicyQualifierId.id_qt_unotice.getId().equals(id)) {
                        sb.append(TAB).append(USERNOTICE);

                        // 获取添加UserNotice信息
                        Qualifier qualifier = Qualifier.getInstance(policyQualifierInfo.getQualifier());
                        sb.append(qualifier.getUserNotice().toString()).append(LINE_SEPARATOR);
                    }
                }
            }
        });
        return sb.toString();
    }

    /**
     * 获取扩展项类型
     *
     * @return 返回当前对象扩展项类型 {@link ExtensionType}
     */
    @Override
    public ExtensionType getExtensionType() {
        return ExtensionType.CERTIFICATE_POLICIES;
    }
}
