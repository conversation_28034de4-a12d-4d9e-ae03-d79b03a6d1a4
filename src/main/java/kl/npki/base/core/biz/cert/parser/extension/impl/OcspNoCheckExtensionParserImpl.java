package kl.npki.base.core.biz.cert.parser.extension.impl;


import kl.nbase.security.util.encoders.Hex;
import kl.npki.base.core.biz.cert.parser.constant.ExtensionType;
import kl.npki.base.core.biz.cert.parser.extension.AbstractX509ExtensionParser;

/**
 * ocsp不撤销检查
 * <AUTHOR> by niuga<PERSON> on 2025-05-23 14:54
 */
public class OcspNoCheckExtensionParserImpl  extends AbstractX509ExtensionParser {
    @Override
    public ExtensionType getExtensionType() {
        return ExtensionType.OCSP_NO_CHECK;
    }

    @Override
    protected String generatePrettyValue(byte[] extensionOctets) {
        return Hex.toHexString(extensionOctets);
    }
}
