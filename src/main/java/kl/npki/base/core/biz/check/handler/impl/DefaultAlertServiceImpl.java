package kl.npki.base.core.biz.check.handler.impl;

import kl.npki.base.core.biz.check.model.SelfCheckResult;
import kl.npki.base.core.biz.check.handler.SelfCheckResultRepository;
import kl.npki.base.core.biz.check.handler.AlertService;

/**
 * 默认的服务自检告警实现
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
public class DefaultAlertServiceImpl implements AlertService {

    private final SelfCheckResultRepository repository;

    public DefaultAlertServiceImpl(SelfCheckResultRepository repository) {
        this.repository = repository;
    }

    @Override
    public void sendAlert(SelfCheckResult result) {
        repository.save(result);
    }
}
