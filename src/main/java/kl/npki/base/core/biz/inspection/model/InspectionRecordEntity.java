package kl.npki.base.core.biz.inspection.model;

import kl.npki.base.core.biz.BaseEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 巡检记录实体
 *
 * <AUTHOR>
 * @date 07/05/2025 16:50
 **/
public class InspectionRecordEntity extends BaseEntity {

    /**
     * 巡检项目ID
     */
    private Long id;

    /**
     * 本次巡检名称
     */
    private String name;

    /**
     * 本次巡检耗时
     */
    private Long costMills;

    /**
     * 本次巡检执行开始时间
     */
    private LocalDateTime startTime;

    /**
     * 本次巡检执行结束时间
     */
    private LocalDateTime endTime;

    /**
     * 巡检项结果
     */
    private List<InspectionItemResult> itemResults;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCostMills() {
        return costMills;
    }

    public void setCostMills(Long costMills) {
        this.costMills = costMills;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public List<InspectionItemResult> getItemResults() {
        return itemResults;
    }

    public void setItemResults(List<InspectionItemResult> itemResults) {
        this.itemResults = itemResults;
    }
}
