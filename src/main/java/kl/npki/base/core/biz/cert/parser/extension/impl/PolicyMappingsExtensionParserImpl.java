package kl.npki.base.core.biz.cert.parser.extension.impl;

import kl.nbase.security.asn1.ASN1Sequence;
import kl.nbase.security.asn1.x509.CertPolicyId;
import kl.nbase.security.asn1.x509.PolicyMappings;
import kl.npki.base.core.biz.cert.parser.constant.ExtensionType;
import kl.npki.base.core.biz.cert.parser.extension.AbstractX509ExtensionParser;
import kl.npki.base.core.constant.I18nConstant;

/**
 * <AUTHOR>
 * @date 2024/8/12 16:07
 */
public class PolicyMappingsExtensionParserImpl extends AbstractX509ExtensionParser {
    /**
     * 策略映射
     */
    private static final String POLICY_MAPPINGS = "strategy_mapping";
    /**
     * 颁发者域策略
     */
    private static final String ISSUER_DOMAIN_POLICY = "issuer_domain_policy";
    /**
     * 主体域策略
     */
    private static final String SUBJECT_DOMAIN_POLICY = "subject_domain_strategy";

    /**
     * 生成一个易于阅读的扩展值字符串，此方法旨在将字节数组形式的扩展值转换为便于人类阅读和理解的字符串格式
     * <p>
     * 具体的转换逻辑由子类实现，因为不同的场景可能需要不同的格式化规则
     *
     * @param extensionOctets 扩展值的字节表示形式
     * @return 格式化后的字符串
     */
    @Override
    protected String generatePrettyValue(byte[] extensionOctets) {
        StringBuilder sb = new StringBuilder();
        sb.append(I18nConstant.getBaseCoreI18nMessage(POLICY_MAPPINGS)).append(COLON).append(LINE_SEPARATOR);
        // 解析PolicyMappings，获取颁发者域策略和主体域策略
        PolicyMappings policyMappings = PolicyMappings.getInstance(extensionOctets);
        ASN1Sequence asn1Sequence = (ASN1Sequence) policyMappings.toASN1Primitive();
        asn1Sequence.forEach(asn1Encodable -> {
            ASN1Sequence oids = (ASN1Sequence) asn1Encodable.toASN1Primitive();
            for (int i = 0; i < oids.size(); i++) {
                CertPolicyId certPolicyId = CertPolicyId.getInstance(oids.getObjectAt(i));
                if (i == oids.size() - 1) {
                    sb.append(TAB).append(I18nConstant.getBaseCoreI18nMessage(SUBJECT_DOMAIN_POLICY)).append(COLON).append(certPolicyId.getId()).append(LINE_SEPARATOR);
                } else {
                    sb.append(TAB).append(I18nConstant.getBaseCoreI18nMessage(ISSUER_DOMAIN_POLICY)).append(COLON).append(certPolicyId.getId()).append(LINE_SEPARATOR);
                }
            }

            // 多个策略映射间添加空行
            sb.append(LINE_SEPARATOR);
        });
        return sb.toString();
    }

    /**
     * 获取扩展项类型
     *
     * @return 返回当前对象扩展项类型 {@link ExtensionType}
     */
    @Override
    public ExtensionType getExtensionType() {
        return ExtensionType.POLICY_MAPPINGS;
    }
}
