package kl.npki.base.core.biz.cert.parser.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import java.util.Arrays;
import java.util.Optional;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * <AUTHOR>
 * @date 2024/8/14 09:47
 */
public enum CertPoliciesEnum implements EnumI18n {
    CABF_ENHANCED_VALIDATION_SSL_CERTIFICATE_POLICY("**********.1", "增强验证SSL证书书策略"),
    CABF_SSL_CERTIFICATE_MINIMUM_REQUIRED_POLICY("**********.2", "SSL证书最低要求策略"),
    CABF_DOMAIN_NAME_VALIDATION_SSL_CERTIFICATE_POLICY("**********.2.1", "域名验证SSL证书策略"),
    CABF_ORGANIZATION_VALIDATION_SSL_CERTIFICATE_POLICY("**********.2.2", "组织验证SSL证书策略"),
    CABF_PERSONAL_VALIDATION_SSL_CERTIFICATE_POLICY("**********.2.3", "个人验证SSL证书策略"),
    CABF_ENHANCED_VERIFICATION_CODE_SIGNING_CERTIFICATE_POLICY("**********.3", "增强验证代码签名证书策略"),
    CABF_CODE_SIGNING_CERTIFICATE_MINIMUM_REQUIRED_POLICY("**********.4.1", "代码签名证书最低要求策略"),
    CABF_CODE_SIGNING_CERTIFICATE_MINIMUM_REQUIRED_TIMESTAMP_POLICY("**********.4.2", "代码签名证书最低要求时间戳策略"),
    CABF_S_MIME_CERTIFICATE_MINIMUM_REQUIRED_POLICY("**********.5", "S/MIME证书最低要求策略"),
    CABF_S_MIME_EMAIL_LEGACY_CERTIFICATE_POLICY("**********.5.1.1", "S/MIME邮箱验证-遗留证书策略"),
    CABF_S_MIME_EMAIL_MULTI_PURPOSE_CERTIFICATE_POLICY("**********.5.1.2", "S/MIME邮箱验证-多用途证书策略"),
    CABF_S_MIME_EMAIL_STRICT_CERTIFICATE_POLICY("**********.5.1.3", "S/MIME邮箱验证-严格证书策略"),
    CABF_S_MIME_ORGANIZATION_LEGACY_CERTIFICATE_POLICY("**********.5.2.1", "S/MIME组织验证-遗留证书策略"),
    CABF_S_MIME_ORGANIZATION_EMAIL_MULTI_PURPOSE_CERTIFICATE_POLICY("**********.5.2.2", "S/MIME组织验证-多用途证书策略"),
    CABF_S_MIME_ORGANIZATION_EMAIL_STRICT_CERTIFICATE_POLICY("**********.5.2.3", "S/MIME组织验证-严格证书策略"),
    CABF_S_MIME_SPONSORS_LEGACY_CERTIFICATE_POLICY("**********.5.3.1", "S/MIME赞助商验证-遗留证书策略"),
    CABF_S_MIME_SPONSORS_ORGANIZATION_EMAIL_MULTI_PURPOSE_CERTIFICATE_POLICY("**********.5.3.2", "S/MIME赞助商验证-多用途证书策略"),
    CABF_S_MIME_SPONSORS_ORGANIZATION_EMAIL_STRICT_CERTIFICATE_POLICY("**********.5.3.3", "S/MIME赞助商验证-严格证书策略"),
    CABF_S_MIME_PERSONAL_LEGACY_CERTIFICATE_POLICY("**********.5.4.1", "S/MIME个人验证-遗留证书策略"),
    CABF_S_MIME_PERSONAL_ORGANIZATION_EMAIL_MULTI_PURPOSE_CERTIFICATE_POLICY("**********.5.4.2", "S/MIME个人验证-多用途证书策略"),
    CABF_S_MIME_PERSONAL_ORGANIZATION_EMAIL_STRICT_CERTIFICATE_POLICY("**********.5.4.3", "S/MIME个人验证-严格证书策略"),
    ;
    private final String oid;
    private final String description;

    CertPoliciesEnum(String oid, String description) {
        this.oid = oid;
        this.description = description;
    }

    public String getOid() {
        return oid;
    }

    public String getDescription() {
        return description;
    }

    public static String getCertPoliciesDescByOid(String oid) {
        Optional<CertPoliciesEnum> certPolicies = Arrays.stream(values()).filter(value -> value.getOid().equals(oid)).findFirst();
        return certPolicies.map(CertPoliciesEnum::getDescription).orElse(null);
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
