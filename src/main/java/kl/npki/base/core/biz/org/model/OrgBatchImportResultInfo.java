package kl.npki.base.core.biz.org.model;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 机构批量导入进度信息
 *
 * <AUTHOR>
 */
@ExcelIgnoreUnannotated
public class OrgBatchImportResultInfo extends OrgBatchImportInfo {
    @ExcelProperty("处理状态")
    private String processingStatus;

    @ExcelProperty("错误原因")
    private String exceptionMessage;

    public String getProcessingStatus() {
        return processingStatus;
    }

    public void setProcessingStatus(String processingStatus) {
        this.processingStatus = processingStatus;
    }

    public String getExceptionMessage() {
        return exceptionMessage;
    }

    public void setExceptionMessage(String exceptionMessage) {
        this.exceptionMessage = exceptionMessage;
    }
}
