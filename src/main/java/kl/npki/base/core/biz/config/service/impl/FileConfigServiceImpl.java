package kl.npki.base.core.biz.config.service.impl;

import kl.npki.base.core.biz.config.model.FileConfigEntity;
import kl.npki.base.core.biz.config.service.IFileConfigService;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.core.repository.IFileConfigRepository;
import kl.npki.base.core.repository.RepositoryFactory;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.*;
/**
 * <AUTHOR>
 * @since 2023/12/22
 */
public class FileConfigServiceImpl implements IFileConfigService {
    private final IFileConfigRepository fileConfigRepository;

    public FileConfigServiceImpl() {
        this.fileConfigRepository = RepositoryFactory.get(IFileConfigRepository.class);
    }

    private static void checkFileConfigEntity(FileConfigEntity fileConfigEntity) {
        if (Objects.isNull(fileConfigEntity)) {
            throw BaseValidationError.FILE_CONFIG_ENTITY_PARAMS_IS_INVALID.toException(FILE_CONFIG_ENTITY_IS_NULL_I18N_KEY);
        }
        if (StringUtils.isEmpty(fileConfigEntity.getFileName())) {
            throw BaseValidationError.FILE_CONFIG_ENTITY_PARAMS_IS_INVALID.toException(FILE_NAME_IS_EMPTY_I18N_KEY);
        }
        if (StringUtils.isEmpty(fileConfigEntity.getFilePath())) {
            throw BaseValidationError.FILE_CONFIG_ENTITY_PARAMS_IS_INVALID.toException(FILE_PATH_IS_EMPTY_I18N_KEY);
        }
        if (StringUtils.isEmpty(fileConfigEntity.getFileContentBase64())) {
            throw BaseValidationError.FILE_CONFIG_ENTITY_PARAMS_IS_INVALID.toException(FILE_CONTENT_BASE64_IS_EMPTY_I18N_KEY);
        }
    }

    @Override
    public void saveAndRefreshFile(FileConfigEntity fileConfigEntity) {
        checkFileConfigEntity(fileConfigEntity);
        fileConfigRepository.saveAndRefreshFile(fileConfigEntity);
    }

    @Override
    public List<FileConfigEntity> getFileConfigList() {
        return fileConfigRepository.getFileConfigList();
    }
}
