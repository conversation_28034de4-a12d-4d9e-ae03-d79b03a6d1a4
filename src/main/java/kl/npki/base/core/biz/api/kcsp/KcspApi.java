package kl.npki.base.core.biz.api.kcsp;

import kl.npki.base.core.biz.api.BaseApi;
import kl.npki.base.core.biz.kcsp.model.KcspRestResponse;
import kl.npki.base.core.biz.kcsp.model.KcspUserInfo;
import kl.npki.base.core.biz.kcsp.model.request.CommercialSecretsLogInfo;
import kl.npki.base.core.biz.kcsp.model.request.KcspAdminOperLogInfo;
import kl.npki.base.core.biz.kcsp.model.request.KcspApiLogInfo;
import kl.npki.base.core.biz.kcsp.model.response.KcspAdminOperLogReportResult;
import kl.npki.base.core.biz.kcsp.model.response.KcspApiLogReportResult;

import java.util.List;

/**
 * KCSP API 接口
 *
 * <AUTHOR>
 * @date 2024/12/18
 */
public interface KcspApi extends BaseApi {

    /**
     * 获取登录用户信息
     *
     * @param workNo 从请求头中获取的用户标识，可通过此标识从本接口中获取用户详情
     * @return 登录的用户信息
     */
    KcspRestResponse<KcspUserInfo> getUserDetails(String workNo);

    /**
     * 管理员操作日志上报接口
     *
     * @param kcspAdminOperLogInfo 管理员操作日志信息
     * @return 上报结果
     */
    KcspRestResponse<KcspAdminOperLogReportResult> reportAdminOperationLog(KcspAdminOperLogInfo kcspAdminOperLogInfo);

    /**
     * 服务日志上报接口
     *
     * @param kcspApiLogInfo 服务日志
     * @return 上报结果
     */
    KcspRestResponse<KcspApiLogReportResult> reportServiceApiLog(KcspApiLogInfo kcspApiLogInfo);

    /**
     * 商密监管日志上报接口
     *
     * @param commercialSecretsLogRequestDetails 商密监管日志
     * @return 上报结果
     */
    KcspRestResponse<String> reportCommercialSecretsMonitoringLog(List<CommercialSecretsLogInfo> commercialSecretsLogRequestDetails);

}
