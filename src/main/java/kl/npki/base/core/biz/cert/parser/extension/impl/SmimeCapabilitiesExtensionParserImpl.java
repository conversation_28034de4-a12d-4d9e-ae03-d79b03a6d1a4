package kl.npki.base.core.biz.cert.parser.extension.impl;


import kl.nbase.security.asn1.*;
import kl.nbase.security.asn1.smime.SMIMECapabilities;
import kl.nbase.security.asn1.smime.SMIMECapability;

import kl.npki.base.core.biz.cert.parser.constant.ExtensionType;

import kl.npki.base.core.biz.cert.parser.extension.AbstractX509ExtensionParser;


import java.util.Vector;


/**
 * 微软证书特有oid
 * 安全/多用途 Internet 邮件扩展 (S/MIME) 功能扩展可用于向电子邮件发件人报告电子邮件收件人的解密功能，以便发件人可以选择双方支持的最安全加密算法
 *
 * <AUTHOR> by niugang on 2025-05-23 14:54
 */
public class SmimeCapabilitiesExtensionParserImpl extends AbstractX509ExtensionParser {
    @Override
    public ExtensionType getExtensionType() {
        return ExtensionType.SMIME_CAPABILITIES;
    }

    @Override
    @SuppressWarnings("unchecked")
    protected String generatePrettyValue(byte[] extensionOctets) {
        ASN1Sequence asn1Encodables = ASN1Sequence.getInstance(extensionOctets);
        SMIMECapabilities smimeCapabilities = new SMIMECapabilities(asn1Encodables);
        Vector<SMIMECapability> capabilities = (Vector<SMIMECapability>) smimeCapabilities.getCapabilities(null);
        if (capabilities == null || capabilities.isEmpty()) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        int index = 1;
        for (SMIMECapability cap : capabilities) {
            sb.append("[").append(index++).append("]SMIME Capability\n");
            sb.append("     Object ID=").append(cap.getCapabilityID()).append("\n");
            if (cap.getParameters() != null) {
                sb.append("     Parameters=").append(cap.getParameters().toString()).append("\n");
            }
        }
        return sb.toString();
    }

}
