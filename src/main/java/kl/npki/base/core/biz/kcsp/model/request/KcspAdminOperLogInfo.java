package kl.npki.base.core.biz.kcsp.model.request;

import kl.npki.base.core.biz.kcsp.model.IKcspLog;

import static kl.npki.base.core.constant.KcspConstant.KCSP_ADMIN_OPERATION_LOG_TYPE;

/**
 * 管理员操作日志请求
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
public class KcspAdminOperLogInfo implements IKcspLog {

    private static final long serialVersionUID = 1L;

    /**
     * 日志类型
     * <p>
     * 0：用户，1：管理员，2：接口，3：服务
     * </p>
     */
    private int logType = 1;

    /**
     * 密码服务名称
     * <p>
     * 如NRA,NCA,NKM
     * </p>
     */
    private String serviceSystem;

    /**
     * 用户ID
     * <p>
     * 和work_no二者必传其一
     * </p>
     */
    private String userId;

    /**
     * 操作名称
     * <p>
     * 通常是操作什么功能，如：用户新增，用户删除，创建密钥等
     * </p>
     */
    private String logName;

    /**
     * 操作内容
     * <p>
     * 操作的内容
     * </p>
     */
    private String logContent;

    /**
     * 操作结果
     * <p>
     * 0：成功，1：失败
     * </p>
     */
    private int logResult;

    /**
     * 操作结果描述
     */
    private String logResultDesc;

    /**
     * 客户端ip
     */
    private String clientIp;

    /**
     * 服务端ip
     */
    private String serverIp;

    /**
     * 开始时间
     */
    private String beginDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 耗时
     */
    private Integer elapsedTime;

    /**
     * 操作时间
     */
    private String logTime;

    /**
     * 是否有轨迹数据
     * <p>
     * 0：没有，1：有（固定传0）
     * </p>
     */
    private int traced = 0;

    public int getLogType() {
        return logType;
    }

    public void setLogType(int logType) {
        this.logType = logType;
    }

    public String getServiceSystem() {
        return serviceSystem;
    }

    public void setServiceSystem(String serviceSystem) {
        this.serviceSystem = serviceSystem;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLogName() {
        return logName;
    }

    public void setLogName(String logName) {
        this.logName = logName;
    }

    public String getLogContent() {
        return logContent;
    }

    public void setLogContent(String logContent) {
        this.logContent = logContent;
    }

    public int getLogResult() {
        return logResult;
    }

    public void setLogResult(int logResult) {
        this.logResult = logResult;
    }

    public String getLogResultDesc() {
        return logResultDesc;
    }

    public void setLogResultDesc(String logResultDesc) {
        this.logResultDesc = logResultDesc;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getServerIp() {
        return serverIp;
    }

    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Integer getElapsedTime() {
        return elapsedTime;
    }

    public void setElapsedTime(Integer elapsedTime) {
        this.elapsedTime = elapsedTime;
    }

    public String getLogTime() {
        return logTime;
    }

    public void setLogTime(String logTime) {
        this.logTime = logTime;
    }

    public int getTraced() {
        return traced;
    }

    public void setTraced(int traced) {
        this.traced = traced;
    }

    @Override
    public String getLogTypeDesc() {
        return KCSP_ADMIN_OPERATION_LOG_TYPE;
    }
}
