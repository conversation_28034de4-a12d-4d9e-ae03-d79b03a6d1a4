package kl.npki.base.core.biz.cert.parser.constant;

import kl.nbase.i18n.i18n.I18nUtil;
import kl.nbase.security.asn1.ASN1ObjectIdentifier;

import java.util.HashMap;
import java.util.Map;

import static kl.npki.base.core.constant.I18nConstant.*;

/**
 * 扩展项类型，一些常用的X509证书扩展类型，定义了扩展项的OID、名称和描述
 * <p>
 * 由于考虑到该项存在很多类型，所以不定义成枚举类
 *
 * <AUTHOR>
 * @date 2024-08-08
 */
public class ExtensionType {

    private static final Map<ASN1ObjectIdentifier, ExtensionType> EXTENSION_TYPE_MAP = new HashMap<>(8);

    /**
     * 密钥用法
     */
    public static final ExtensionType KEY_USAGE = new ExtensionType(
        new ASN1ObjectIdentifier("2.5.29.15"),
        "KeyUsage",
        KEY_USAGE_I18N_KEY
    );

    /**
     * 增强型密钥用法
     */
    public static final ExtensionType EXTENDED_KEY_USAGE = new ExtensionType(
        new ASN1ObjectIdentifier("2.5.29.37"),
        "ExtendedKeyUsage",
        EXTENDED_KEY_USAGE_I18N_KEY
    );

    /**
     * 基本约束
     */
    public static final ExtensionType BASIC_CONSTRAINTS = new ExtensionType(
        new ASN1ObjectIdentifier("2.5.29.19"),
        "BasicConstraints",
        BASIC_CONSTRAINTS_I18N_KEY
    );

    /**
     * 使用者密钥标识符
     */
    public static final ExtensionType SUBJECT_KEY_IDENTIFIER = new ExtensionType(
        new ASN1ObjectIdentifier("2.5.29.14"),
        "SubjectKeyIdentifier",
        SUBJECT_KEY_IDENTIFIER_I18N_KEY
    );

    /**
     * 授权者密钥标识符
     */
    public static final ExtensionType AUTHORITY_KEY_IDENTIFIER = new ExtensionType(
        new ASN1ObjectIdentifier("2.5.29.35"),
        "AuthorityKeyIdentifier",
        AUTHORITY_KEY_IDENTIFIER_I18N_KEY
    );

    /**
     * 使用者备用名称
     */
    public static final ExtensionType SUBJECT_ALTERNATIVE_NAME = new ExtensionType(
        new ASN1ObjectIdentifier("2.5.29.17"),
        "SubjectAlternativeName",
        SUBJECT_ALTERNATIVE_NAME_I18N_KEY
    );

    /**
     * 颁发者备用名称
     */
    public static final ExtensionType ISSUER_ALTERNATIVE_NAME = new ExtensionType(
        new ASN1ObjectIdentifier("2.5.29.18"),
        "IssuerAlternativeName",
        ISSUER_ALTERNATIVE_NAME_I18N_KEY
    );

    /**
     * CRL 分发点
     */
    public static final ExtensionType CRL_DISTRIBUTION_POINTS = new ExtensionType(
        new ASN1ObjectIdentifier("2.5.29.31"),
        "CRLDistributionPoints",
        CRL_DISTRIBUTION_POINTS_I18N_KEY
    );

    /**
     * 颁发机构信息访问
     */
    public static final ExtensionType AUTHORITY_INFO_ACCESS = new ExtensionType(
        new ASN1ObjectIdentifier("1.3.6.1.5.5.7.1.1"),
        "AuthorityInfoAccess",
        AUTHORITY_INFORMATION_ACCESS_I18N_KEY
    );

    /**
     * 证书策略
     */
    public static final ExtensionType CERTIFICATE_POLICIES = new ExtensionType(
        new ASN1ObjectIdentifier("2.5.29.32"),
        "CertificatePolicies",
        CERTIFICATE_POLICIES_I18N_KEY
    );

    /**
     * 策略映射
     */
    public static final ExtensionType POLICY_MAPPINGS = new ExtensionType(
        new ASN1ObjectIdentifier("2.5.29.33"),
        "PolicyMappings",
        POLICY_MAPPINGS_I18N_KEY
    );

    /**
     * 策略约束
     */
    public static final ExtensionType POLICY_CONSTRAINTS = new ExtensionType(
        new ASN1ObjectIdentifier("2.5.29.36"),
        "PolicyConstraints",
        POLICY_CONSTRAINTS_I18N_KEY
    );

    /**
     * CRL 序列号
     */
    public static final ExtensionType CRL_NUMBER = new ExtensionType(
        new ASN1ObjectIdentifier("2.5.29.20"),
        "CRLNumber",
        CRL_NUMBER_I18N_KEY
    );

    /**
     * Delta CRL 指示器
     */
    public static final ExtensionType DELTA_CRL_INDICATOR = new ExtensionType(
        new ASN1ObjectIdentifier("2.5.29.27"),
        "DeltaCRLIndicator",
        DELTA_CRL_INDICATOR_I18N_KEY
    );

    /**
     * 最新 CRL
     */
    public static final ExtensionType FRESHEST_CRL = new ExtensionType(
        new ASN1ObjectIdentifier("2.5.29.46"),
        "FreshestCRL",
        FRESHEST_CRL_I18N_KEY
    );

    /**
     * 证书透明度
     */
    public static final ExtensionType CERT_TRANSPARENCY = new ExtensionType(
        new ASN1ObjectIdentifier("1.3.6.1.4.1.11129.2.4.2"),
        "CertTransparency",
        CERTIFICATE_TRANSPARENCY_I18N_KEY
    );

    /**
     * Netscape证书类型
     */
    public static final ExtensionType NETSCAPE_CERT_TYPE = new ExtensionType(
        new ASN1ObjectIdentifier("2.16.840.1.113730.1.1"),
        "NetscapeCertType",
        NETSCAPE_CERTIFICATE_TYPE_I18N_KEY
    );

    /**
     * 后量子公钥
     */
    public static final ExtensionType PQC_PUBLIC_KEY = new ExtensionType(
        new ASN1ObjectIdentifier("1.3.6.1.4.1.46210.2.1.100.2"),
        "PqcPublicKey",
        POST_QUANTUM_PUBLIC_KEY_I18N_KEY
    );

    /**
     * 后量子签名
     */
    public static final ExtensionType PQC_SIGNATURE = new ExtensionType(
        new ASN1ObjectIdentifier("1.3.6.1.4.1.46210.2.1.100.4"),
        "PqcSignature",
        POST_QUANTUM_SIGNATURE_I18N_KEY
    );
    /**
     * ocsp不撤销检查
     */
    public static final ExtensionType OCSP_NO_CHECK = new ExtensionType(
        new ASN1ObjectIdentifier("1.3.6.1.5.5.7.48.1.5"),
        "OcspNoCheck",
        OCSP_NO_CHECK_I18N_KEY
    );

    /**
     * Microsoft OID
     * 安全/多用途 Internet 邮件扩展 (S/MIME) 功能扩展可用于向电子邮件发件人报告电子邮件收件人的解密功能，
     * 以便发件人可以选择双方支持的最安全加密算法
     */
    public static final ExtensionType SMIME_CAPABILITIES = new ExtensionType(
        new ASN1ObjectIdentifier("1.2.840.113549.1.9.15"),
        "SmimeCapabilities",
        SMIME_CAPABILITIES_I18N_KEY
    );
    /**
     * 微软证书扩展 模版名称
     */
    public static final ExtensionType TEMPLATE_NAME = new ExtensionType(
        new ASN1ObjectIdentifier("1.3.6.1.4.1.311.20.2"),
        "TemplateName",
        TEMPLATE_NAME_I18N_KEY
    );


    public static ExtensionType getInstance(ASN1ObjectIdentifier objectIdentifier) {
        return EXTENSION_TYPE_MAP.values().stream()
            .filter(extensionType -> extensionType.getObjectIdentifier().equals(objectIdentifier))
            .findFirst()
            // 如果没有找到，则返回一个默认的ExtensionType，并使用OID字符串作为名称和描述
            .orElse(new ExtensionType(objectIdentifier, objectIdentifier.getId(), objectIdentifier.getId()));
    }

    private final ASN1ObjectIdentifier objectIdentifier;
    private final String name;
    private final String desc;

    private ExtensionType(ASN1ObjectIdentifier objectIdentifier, String name, String desc) {
        this.objectIdentifier = objectIdentifier;
        this.name = name;
        this.desc = desc;

        EXTENSION_TYPE_MAP.putIfAbsent(objectIdentifier, this);
    }

    public ASN1ObjectIdentifier getObjectIdentifier() {
        return objectIdentifier;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return I18nUtil.tr(desc);
    }
}
