package kl.npki.base.core.biz.org.service;

import kl.npki.base.core.biz.batch.model.BatchResult;
import kl.npki.base.core.biz.org.model.OrgAddInfo;
import kl.npki.base.core.biz.org.model.OrgEntity;
import kl.npki.base.core.biz.org.model.OrgUpdateInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/3/21 15:08
 */
public interface IOrgService {

    /**
     * 检查机构是否存在
     *
     * @param id 待检查机构编码
     * @return 如果机构存在则返回机构实体，如果不存在则抛出异常
     */
    OrgEntity checkExists(Long id);

    /**
     * 判断机构是否存在
     *
     * @param orgCode 待检查机构编码
     * @return true: 存在此机构，false: 不存在
     */
    boolean exists(String orgCode);

    /**
     * 判断机构是否存在，已经被注销的但是没有删除的情况表示机构还存在，如果已经被删除（不管是物理还是逻辑）则表示不存在
     *
     * @param id 待检查机构id
     * @return true: 存在此机构，false: 不存在
     */
    boolean exists(Long id);

    /**
     * 添加机构
     *
     * @param orgAddInfo 添加机构信息
     * @return true：添加成功，false：添加失败
     */
    boolean add(OrgAddInfo orgAddInfo);

    /**
     * 添加机构
     *
     * @param id            机构id
     * @param orgUpdateInfo 机构更新信息
     * @return true：更新成功，false：失败
     */
    boolean update(Long id, OrgUpdateInfo orgUpdateInfo);

    /**
     * 注销机构
     *
     * @param id 注销机构id
     * @return true：注销成功，false：注销失败
     */
    boolean revoke(Long id);

    /**
     * 删除机构
     *
     * @param id 删除机构id
     * @return true：删除成功，false：删除失败
     */
    boolean delete(Long id);

    /**
     * 批量逻辑删除机构实体
     * @param ids
     * @return
     */
    BatchResult batchDelete(List<Long> ids);
}