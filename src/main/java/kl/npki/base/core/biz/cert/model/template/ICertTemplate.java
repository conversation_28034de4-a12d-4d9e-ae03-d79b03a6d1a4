package kl.npki.base.core.biz.cert.model.template;

import kl.nbase.security.asn1.x509.ExtensionsGenerator;
import kl.nbase.security.asn1.x509.V3TBSCertificateGenerator;

/**
 * 证书模板顶级接口
 *
 * <AUTHOR>
 * @date 2022-12-26
 */
public interface ICertTemplate {

    /**
     * 设置签发者
     *
     * @param tbsCertGen 待签发证书生成器
     */
    void setIssuer(V3TBSCertificateGenerator tbsCertGen);

    /**
     * 添加扩展项，可以根据公钥动态添加
     *
     * @param extGen    扩展项生成器
     * @throws Exception
     */
    void addExtensions(ExtensionsGenerator extGen) throws Exception;

}
