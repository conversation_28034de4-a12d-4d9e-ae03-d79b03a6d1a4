package kl.npki.base.core.biz.check.handler.impl;

import kl.npki.base.core.biz.check.handler.SelfCheckResultRepository;
import kl.npki.base.core.biz.check.model.SelfCheckResult;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务自检数据内存存储实现
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
public class InMemoryCheckResultRepositoryImpl implements SelfCheckResultRepository {

    private final Set<SelfCheckResult> results = new HashSet<>();

    @Override
    public void save(SelfCheckResult result) {
        if (StringUtils.isEmpty(result.getId())) {
            result.setId(UUID.randomUUID().toString());
        }
        results.add(result);
    }

    @Override
    public List<SelfCheckResult> findAll() {
        return results.stream().sorted(Comparator.comparing(SelfCheckResult::getCode)).collect(Collectors.toList());
    }

    @Override
    public void removeByCode(String code) {
        if (code != null) {
            results.removeIf(result -> code.equals(result.getCode()));
        }
    }

    @Override
    public void removeAll() {
        results.clear();
    }
}
