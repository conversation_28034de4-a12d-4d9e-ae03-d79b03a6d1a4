package kl.npki.base.core.biz.kcsp.model.request;

import kl.npki.base.core.biz.kcsp.model.IKcspLog;

import static kl.npki.base.core.constant.KcspConstant.KCSP_ALGO_USAGE_LOG_TYPE;

/**
 * 商密监管日志上报请求
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
public class CommercialSecretsLogInfo implements IKcspLog {

    private static final long serialVersionUID = 1L;

    /**
     * 日志类型
     * <p>
     * 固定值取algousage
     * </p>
     */
    private String logtype;

    /**
     * 密码算法应用时间
     */
    private String date;

    /**
     * 密码产品类型
     * <p>
     * 目前为国密商用密码产品委托书中的产品类型项，取值参考《密码应用产品接入密码服务平台（KCSP3.0.0）的规范》6.6小节
     * </p>
     */
    private int secapptype;

    /**
     * 密码服务调用者IP地址
     * <p>
     * 一般是应用服务器
     * </p>
     */
    private String srcip;

    /**
     * 密码服务提供者IP地址
     * <p>
     * 一般是密码产品
     * </p>
     */
    private String provip;

    /**
     * 本次调用密码服务涉及的算法情况
     * <p>
     * 包括什么算法、调用几次、涉及数据量，详细说明参见《密码应用产品接入密码服务平台（KCSP3.0.0）的规范》6.7小节
     * </p>
     */
    private String algo;

    /**
     * 密码服务UUID
     * <p>
     * 由密管系统下发，在密管系统中可 通过这个标识识别出该密码产品的归属者
     * </p>
     */
    private String identity;

    public CommercialSecretsLogInfo(String logtype, String date, int secapptype, String srcip, String provip, String algo, String identity) {
        this.logtype = logtype;
        this.date = date;
        this.secapptype = secapptype;
        this.srcip = srcip;
        this.provip = provip;
        this.algo = algo;
        this.identity = identity;
    }

    public String getLogtype() {
        return logtype;
    }

    public void setLogtype(String logtype) {
        this.logtype = logtype;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getSecapptype() {
        return secapptype;
    }

    public void setSecapptype(int secapptype) {
        this.secapptype = secapptype;
    }

    public String getSrcip() {
        return srcip;
    }

    public void setSrcip(String srcip) {
        this.srcip = srcip;
    }

    public String getProvip() {
        return provip;
    }

    public void setProvip(String provip) {
        this.provip = provip;
    }

    public String getAlgo() {
        return algo;
    }

    public void setAlgo(String algo) {
        this.algo = algo;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    @Override
    public String getLogTypeDesc() {
        return KCSP_ALGO_USAGE_LOG_TYPE;
    }
}
