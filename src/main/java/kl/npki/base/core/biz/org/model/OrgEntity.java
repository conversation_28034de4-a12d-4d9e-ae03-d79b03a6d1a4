package kl.npki.base.core.biz.org.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import kl.npki.base.core.common.date.CustomLocalDateTimeDeserializer;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;
import kl.npki.base.core.repository.IOrgRepository;
import kl.npki.base.core.repository.RepositoryFactory;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 机构实体
 *
 * <AUTHOR>
 * @create 2024/3/13 10:59
 */
public class OrgEntity implements Serializable {

    private static final long serialVersionUID = -2338046611456654313L;

    private final IOrgRepository orgRepository = RepositoryFactory.get(IOrgRepository.class);
    /**
     * 数据标识ID
     */
    private Long id;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 机构全称
     */
    private String fullName;
    /**
     * 机构名称拼音
     */
    private String namePinyin;
    /**
     * 机构状态
     */
    private Integer orgStatus;
    /**
     * 机构编码
     */
    private String orgCode;
    /**
     * 上级机构
     */
    private Long parentId;
    /**
     * 上级机构编码
     */
    private String parentCode;
    /**
     * 机构类型
     */
    private Integer orgType;
    /**
     * 机构完整编码
     */
    private String fullCode;
    /**
     * 机构完整编码
     */
    private String fullId;
    /**
     * 机构排序码
     */
    private int orderIndex;
    /**
     * 机构排序完整编码
     */
    private String orderFullIndex;
    /**
     * 机构级别
     */
    private Integer orgLevel;
    /**
     * 联系人
     */
    private String linkman;
    /**
     * 联系电话
     */
    private String telephone;
    /**
     * 邮政编码
     */
    private String postcode;
    /**
     * 联系地址
     */
    private String address;
    /**
     * 机构描述
     */
    private String orgDesc;
    /**
     * 逻辑删除标志
     */
    private Integer isDelete;
    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 创建时间
     */
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime createdAt;
    /**
     * 更新时间
     */
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime updatedAt;

    public void save() {
        // 设置创建和更新时间
        this.setCreatedAt(LocalDateTime.now());
        this.setUpdatedAt(LocalDateTime.now());
        this.id = orgRepository.save(this);
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getNamePinyin() {
        return namePinyin;
    }

    public void setNamePinyin(String namePinyin) {
        this.namePinyin = namePinyin;
    }

    public Integer getOrgStatus() {
        return orgStatus;
    }

    public void setOrgStatus(Integer orgStatus) {
        this.orgStatus = orgStatus;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public String getFullCode() {
        return fullCode;
    }

    public void setFullCode(String fullCode) {
        this.fullCode = fullCode;
    }

    public int getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(int orderIndex) {
        this.orderIndex = orderIndex;
    }

    public String getOrderFullIndex() {
        return orderFullIndex;
    }

    public void setOrderFullIndex(String orderFullIndex) {
        this.orderFullIndex = orderFullIndex;
    }

    public Integer getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(Integer orgLevel) {
        this.orgLevel = orgLevel;
    }

    public String getLinkman() {
        return linkman;
    }

    public void setLinkman(String linkman) {
        this.linkman = linkman;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getOrgDesc() {
        return orgDesc;
    }

    public void setOrgDesc(String orgDesc) {
        this.orgDesc = orgDesc;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getFullId() {
        return fullId;
    }

    public OrgEntity setFullId(String fullId) {
        this.fullId = fullId;
        return this;
    }
}