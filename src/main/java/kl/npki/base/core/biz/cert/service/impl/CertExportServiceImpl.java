package kl.npki.base.core.biz.cert.service.impl;

import kl.npki.base.core.biz.cert.model.CertEntryInfo;
import kl.npki.base.core.biz.cert.model.TrustCertEntity;
import kl.npki.base.core.biz.cert.service.ICertExportService;
import kl.npki.base.core.biz.export.AbstractTempFile;
import kl.npki.base.core.common.manager.MgrHolder;
import kl.npki.base.core.common.structure.TreeNode;
import kl.npki.base.core.constant.ExportCertTypeEnum;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.base.core.utils.KeyStoreUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.security.cert.CertificateEncodingException;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.stream.Collectors;

import static kl.npki.base.core.constant.ExportCertTypeEnum.PFX;
import static kl.npki.base.core.constant.KeyStoreConstants.DEFAULT_KEYSTORE_TYPE;
import static kl.npki.base.core.constant.KeyStoreConstants.JKS_KEY_STORE_TYPE;

/**
 * <AUTHOR>
 * @create 2025/4/2 下午1:31
 */
public class CertExportServiceImpl extends AbstractTempFile implements ICertExportService {

    @Override
    public File exportCert(X509Certificate certificate, ExportCertTypeEnum exportCertType) {
        List<X509Certificate> certChain = loadCertChainByCaDn(certificate.getIssuerDN().toString());
        return exportCert(new CertEntryInfo(certificate, certChain),
                exportCertType, null, null);
    }

    @Override
    public File exportCert(X509Certificate certificate, List<X509Certificate> certChains, ExportCertTypeEnum exportCertType) {
        return exportCert(new CertEntryInfo(certificate, certChains), exportCertType, null, null);
    }

    @Override
    public File exportCert(CertEntryInfo certEntryInfo, ExportCertTypeEnum exportCertType, String keyStorePassword, String keyPassword) {
        return exportCert(Collections.singletonList(certEntryInfo), exportCertType, keyStorePassword, keyPassword);
    }

    @Override
    public File exportCert(List<CertEntryInfo> certEntryInfos, ExportCertTypeEnum exportCertType, String keyStorePassword, String keyPassword){
        return getCertFile(certEntryInfos, exportCertType, keyStorePassword, keyPassword);
    }

    /**
     * 根据issuerDn获取证书链
     *
     * @param issuerDn 证书颁发者DN
     * @return 证书链
     */
    @Override
    public List<X509Certificate> loadCertChainByCaDn(String issuerDn) {
        List<X509Certificate> certList = new ArrayList<>();
        // 根据颁发者dn查找到证书链
        TreeNode<String, TrustCertEntity> treeNode = null;
        for (TreeNode<String, TrustCertEntity> node : searchAllCertChainGroup()) {
            if (node.anyMatch(trustCertEntity -> trustCertEntity.getSubjectDn().equals(issuerDn))) {
                treeNode = node;
            }
        }
        if (null == treeNode) {
            throw BaseInternalError.TRUST_CERT_NOT_EXIST_ERROR.toException(issuerDn);
        }
        // 将证书链转为List集合
        treeNode.consumer(trustCertEntity -> {
            String certValueBase64 = trustCertEntity.getCertValue();
            X509Certificate certificate = CertUtil.parseX509Cert(certValueBase64);
            certList.add(certificate);
        });
        return certList;
    }

    public List<TreeNode<String, TrustCertEntity>> searchAllCertChainGroup() {
        List<TrustCertEntity> allTrustCert = MgrHolder.getManageCertMgr().getTrustCertList();
        // 构造证书链分组结构
        return allTrustCert
                .stream()
                .filter(trustCertEntity -> trustCertEntity.getSubjectCn().equals(trustCertEntity.getIssuerCn()))
                .map(trustCertEntity -> {
                    TreeNode<String, TrustCertEntity> treeNode = new TreeNode<>();
                    treeNode.setId(trustCertEntity.getHexSn()).setLevel(1).setParentId(null).setData(trustCertEntity);
                    loadTreeNode(allTrustCert, treeNode);
                    return treeNode;
                })
                .collect(Collectors.toList());
    }

    /**
     * 加载所有的证书链
     *
     * @param allTrustCert 所有的证书链集合
     * @param treeNode     证书链树结构
     */
    private void loadTreeNode(List<TrustCertEntity> allTrustCert, TreeNode<String, TrustCertEntity> treeNode) {
        TrustCertEntity trustCertEntity = treeNode.getData();
        String nodeId = treeNode.getId();
        Integer level = treeNode.getLevel();
        String keyId = trustCertEntity.getKeyId();
        String subjectCn = trustCertEntity.getSubjectCn();
        // 查询当前CA的所有下级CA证书
        List<TrustCertEntity> certEntityList = searchSubCaByIssuerCnAndKeyId(allTrustCert, subjectCn, keyId);
        // 将所有下级CA证书放到树结构中
        // 然后继续查找所有下级CA证书的下级CA证书
        List<TreeNode<String, TrustCertEntity>> treeNodeList = new ArrayList<>();
        treeNode.setChildren(treeNodeList);
        for (TrustCertEntity certEntity : certEntityList) {
            TreeNode<String, TrustCertEntity> treeNodeCertEntity = new TreeNode<>();
            treeNodeCertEntity.setId(certEntity.getHexSn()).setLevel(level + 1).setParentId(nodeId).setData(certEntity);
            treeNodeList.add(treeNodeCertEntity);
            loadTreeNode(allTrustCert, treeNodeCertEntity);
        }
    }

    private List<TrustCertEntity> searchSubCaByIssuerCnAndKeyId(List<TrustCertEntity> allTrustCert, String issuerCn, String keyId) {
        List<TrustCertEntity> subCaList = new ArrayList<>();
        for (TrustCertEntity trustCertEntity : allTrustCert) {
            // 找到颁发者名称和证书主题名称相同的证书，并且不是根证书
            if (trustCertEntity.getIssuerCn().equals(issuerCn) && !trustCertEntity.getSubjectCn().equals(issuerCn)) {
                // 由于根据CN可以查找到证书可能是错误的（有重名的CA证书），所以需要再使用KeyId确认一下
                // 注意：第二次确认的前提是证书中有KeyId
                if (StringUtils.isNotBlank(keyId) && !keyId.equals(trustCertEntity.getIssuerKeyId())) {
                    continue;
                }
                subCaList.add(trustCertEntity);
            }
        }
        return subCaList;
    }

    private File getCertFile(List<CertEntryInfo> certEntryInfos, ExportCertTypeEnum exportCertType, String keyStorePassword, String keyPassword) {
        if (CollectionUtils.isEmpty(certEntryInfos) || Objects.isNull(certEntryInfos.get(0).getCertificate())) {
            throw BaseValidationError.EXPORT_CERT_ERROR.toException("no cert");
        }
        // 在临时目录下生成导出文件，文件名称使用证书序列号
        String fileName = certEntryInfos.get(0).getCertificate().getSerialNumber().toString(16) + exportCertType.getSuffix();
        File exportFile = getTempFile(fileName);

        // 按照不同格式生成内容，然后将内容写入文件
        try {
            switch (exportCertType) {
                case PFX:
                case JKS:
                    String keyStoreType = PFX.equals(exportCertType) ? DEFAULT_KEYSTORE_TYPE : JKS_KEY_STORE_TYPE;
                    // 生成PFX/JKS
                    byte[] keyStoreBytes = KeyStoreUtil.genKeyStoreBytes(certEntryInfos, keyStoreType, keyStorePassword, keyPassword);
                    FileUtils.writeByteArrayToFile(exportFile, keyStoreBytes);
                    return exportFile;
                case DER:
                    // 将证书编码后写入文件
                    FileUtils.writeByteArrayToFile(exportFile, certEntryInfos.get(0).getCertificate().getEncoded());
                    return exportFile;
                case PEM:
                    // 将证书转换为PEM格式后写入文件
                    String pemCert = CertUtil.convertX509CertToPEM(certEntryInfos.get(0).getCertificate());
                    FileUtils.writeByteArrayToFile(exportFile, pemCert.getBytes());
                    return exportFile;
                case P7B_NO_CHAIN:
                    List<X509Certificate> noCertChain = new ArrayList<>();
                    // 将当前证书添加到集合中用于生成p7b文件
                    noCertChain.add(certEntryInfos.get(0).getCertificate());
                    // 生成p7b格式的数据并写入文件
                    FileUtils.writeByteArrayToFile(exportFile, CertUtil.genP7b(noCertChain));
                    return exportFile;
                case P7B_WITH_CHAIN:
                    List<X509Certificate> withCertChain = certEntryInfos.get(0).getCertificateChain();
                    if (Objects.isNull(withCertChain)) {
                        withCertChain = new ArrayList<>();
                    }
                    withCertChain.add(certEntryInfos.get(0).getCertificate());
                    // 生成p7b格式的数据并写入文件
                    FileUtils.writeByteArrayToFile(exportFile, CertUtil.genP7b(withCertChain));
                    return exportFile;
                default:
                    throw BaseValidationError.EXPORT_CERT_TYPE_ERROR.toException(exportCertType.name());
            }
        } catch (IOException e) {
            throw BaseInternalError.FILE_WRITE_FAIL.toException(e);
        } catch (CertificateEncodingException e) {
            throw BaseInternalError.CERT_ENCODE_ERROR.toException(e);
        } catch (Exception e) {
            throw BaseValidationError.EXPORT_CERT_ERROR.toException(e);
        }
    }

    @Override
    public String bizName() {
        return "base/cert";
    }
}