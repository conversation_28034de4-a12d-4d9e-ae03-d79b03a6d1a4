package kl.npki.base.core.biz.cert.service;

import kl.nbase.security.util.encoders.Base64;
import kl.nbase.security.utils.CertUtil;
import kl.npki.base.core.biz.cert.model.CertEntryInfo;
import kl.npki.base.core.constant.ExportCertTypeEnum;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 证书导出服务，支持导出的证书格式有：DER、PEM、P7B_NO_CHAIN、P7B_WITH_CHAIN、PFX、JKS
 *
 * <AUTHOR>
 * @create 2025/4/2 下午1:20
 */
public interface ICertExportService {

    /**
     * 导出证书
     *
     * @param certificate    待导出的证书
     * @param exportCertType 需要导出的证书格式
     * @return 导出的证书文件
     */
    File exportCert(X509Certificate certificate, ExportCertTypeEnum exportCertType);

    /**
     * 导出证书
     *
     * @param certificate    待导出的证书
     * @param certChains     证书链信息，当exportCertType为{@link ExportCertTypeEnum#P7B_WITH_CHAIN}时使用
     * @param exportCertType 需要导出的证书格式
     * @return 导出的证书文件
     */
    File exportCert(X509Certificate certificate, List<X509Certificate> certChains, ExportCertTypeEnum exportCertType);

    /**
     * 导出证书
     *
     * @param certEntryInfo    待导出证书
     * @param exportCertType   证书导出格式，详见{@link ExportCertTypeEnum}
     * @param keyStorePassword 文件的保护口令，该参数在导出PFX/JKS时才有效
     * @param keyPassword      私钥的保护口令，该参数在导出PFX/JKS时才有效
     * @return
     */
    File exportCert(CertEntryInfo certEntryInfo, ExportCertTypeEnum exportCertType, String keyStorePassword, String keyPassword);

    /**
     * 导出证书
     */
    File exportCert(List<CertEntryInfo> certEntryInfos, ExportCertTypeEnum exportCertType, String keyStorePassword, String keyPassword);

    /**
     * 导出证书
     *
     * @param baseCertValue  Base64编码的证书
     * @param exportCertType 需要导出的证书格式
     * @return 导出的证书文件
     */
    default File exportCert(String baseCertValue, ExportCertTypeEnum exportCertType) {
        X509Certificate certificate = CertUtil.derToX509(Base64.decode(baseCertValue));
        return exportCert(certificate, exportCertType);
    }

    /**
     * 导出证书
     *
     * @param base64CertValue  Base64编码的证书
     * @param base64CertChains Base64编码的证书链信息，当exportCertType为{@link ExportCertTypeEnum#P7B_WITH_CHAIN}时使用
     * @param exportCertType   需要导出的证书格式
     * @return 导出的证书文件
     */
    default File exportCert(String base64CertValue, List<String> base64CertChains, ExportCertTypeEnum exportCertType) {
        X509Certificate certificate = CertUtil.derToX509(Base64.decode(base64CertValue));
        List<X509Certificate> certChains = ListUtils.emptyIfNull(base64CertChains).stream()
            .filter(StringUtils::isNotBlank)
            .map(cert -> CertUtil.derToX509(Base64.decode(cert)))
            .collect(Collectors.toList());

        return exportCert(certificate, certChains, exportCertType);
    }

    /**
     * 根据issuerDn获取证书链
     *
     * @param issuerDn 证书颁发者DN
     * @return 证书链
     */
    List<X509Certificate> loadCertChainByCaDn(String issuerDn);
}