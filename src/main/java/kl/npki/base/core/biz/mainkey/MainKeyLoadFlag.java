package kl.npki.base.core.biz.mainkey;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 主密钥加载标志
 *
 * <AUTHOR>
 * @date 2023/7/4
 */
public class MainKeyLoadFlag {

    private static MainKeyLoadFlag instance;
    private final AtomicBoolean isReady = new AtomicBoolean(false);

    private MainKeyLoadFlag() {
        // Add shutdown hook
        Runtime.getRuntime().addShutdownHook(new Thread(() -> isReady.set(false)));
    }

    public static synchronized MainKeyLoadFlag getInstance() {
        if (instance == null) {
            instance = new MainKeyLoadFlag();
        }
        return instance;
    }

    public void setReady(boolean ready) {
        isReady.set(ready);
    }

    public boolean isReady() {
        return isReady.get();
    }
}
