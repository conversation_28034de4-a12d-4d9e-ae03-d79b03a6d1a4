package kl.npki.base.core.biz.cert.parser.constant;

import kl.nbase.i18n.i18n.EnumI18n;
import kl.nbase.security.asn1.x509.KeyUsage;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 密钥用法枚举类，列举所有密钥用法，定义了密钥用法的编码、名称以及描述
 *
 * <AUTHOR>
 * @date 2024-08-08
 */
public enum KeyUsageEnum implements EnumI18n {

    /**
     * 数字签名 : 10000000(8bit)
     */
    DIGITAL_SIGNATURE(KeyUsage.digitalSignature, "DigitalSignature", "数字签名"),
    /**
     * 防止抵赖 : 01000000(8bit)
     */
    NON_REPUDIATION(KeyUsage.nonRepudiation, "NonRepudiation", "防止抵赖"),
    /**
     * 密钥加密 : 00100000(8bit)
     */
    KEY_ENCIPHERMENT(KeyUsage.keyEncipherment, "KeyEncipherment", "密钥加密"),
    /**
     * 数据加密 : 00010000(8bit)
     */
    DATA_ENCIPHERMENT(KeyUsage.dataEncipherment, "DataEncipherment", "数据加密"),
    /**
     * 密钥协商 : 00001000(8bit)
     */
    KEY_AGREEMENT(KeyUsage.keyAgreement, "KeyAgreement", "密钥协商"),
    /**
     * 证书签发 : 00000100(8bit)
     */
    KEY_CERT_SIGN(KeyUsage.keyCertSign, "KeyCertSign", "证书签发"),
    /**
     * CRL签发 : 00000010(8bit)
     */
    CRL_SIGN(KeyUsage.cRLSign, "CRLSign", "CRL签发"),
    /**
     * 仅能加密: 00000001(8bit)
     */
    ENCIPHER_ONLY(KeyUsage.encipherOnly, "EncipherOnly", "仅能加密"),
    /**
     * 仅能解密: 1000000000000000(16bit)
     */
    DECIPHER_ONLY(KeyUsage.decipherOnly, "DecipherOnly", "仅能解密");

    private final int code;
    private final String name;
    private final String description;

    KeyUsageEnum(int code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return tr();
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
