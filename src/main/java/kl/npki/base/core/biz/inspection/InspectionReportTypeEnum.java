package kl.npki.base.core.biz.inspection;

import kl.npki.base.core.biz.inspection.report.InspectionReportGenerator;
import kl.npki.base.core.biz.inspection.report.impl.HtmlInspectionReportGeneratorImpl;
import kl.npki.base.core.biz.inspection.report.impl.MarkdownInspectionReportGeneratorImpl;
import kl.npki.base.core.biz.inspection.report.impl.PdfInspectionReportGeneratorImpl;

/**
 * 巡检报告类型
 *
 * <AUTHOR>
 * @date 08/05/2025 16:12
 **/
public enum InspectionReportTypeEnum {

    /**
     * PDF格式的巡检报告
     */
    PDF(".pdf"),

    /**
     * MARKDOWN格式的巡检报告
     */
    MARKDOWN(".md"),

    /**
     * HTML格式的巡检报告
     */
    HTML(".html");

    private final String fileExtension;

    InspectionReportTypeEnum(String fileExtension) {
        this.fileExtension = fileExtension;
    }

    public String getFileExtension() {
        return fileExtension;
    }

    /**
     * 获取文件名
     *
     * @param reportName 文件名称
     * @return 带上文件后缀的文件名称
     */
    public String getFileName(String reportName) {
        return reportName + fileExtension;
    }

    /**
     * 获取报告生成器
     *
     * @return 报告生成器
     */
    public InspectionReportGenerator getReportGenerator() {
        switch (this) {
            case PDF:
                return new PdfInspectionReportGeneratorImpl();
            case MARKDOWN:
                return new MarkdownInspectionReportGeneratorImpl();
            case HTML:
                return new HtmlInspectionReportGeneratorImpl();
            default:
                throw new IllegalArgumentException("Report generator not found because of unsupported report type");
        }
    }
}
