package kl.npki.base.core.biz.license.service.impl;

import kl.nbase.config.RefreshableConfig;
import kl.nbase.config.listener.ConfigRefreshListener;
import kl.npki.base.core.biz.license.LicenseMgr;
import kl.npki.base.core.biz.license.service.IExternalLicenseCheck;
import kl.npki.base.core.configs.LicenseConfig;
import kl.security.license.core.License;
import kl.security.license.core.LicenseStatus;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/2/1
 */
public class LicenseConfigListener implements ConfigRefreshListener {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(LicenseConfigListener.class);

    private static IExternalLicenseCheck externalLicenseCheck;

    public static IExternalLicenseCheck getExternalLicenseCheck() {
        return externalLicenseCheck;
    }

    public static void setExternalLicenseCheck(IExternalLicenseCheck externalLicenseCheck) {
        LicenseConfigListener.externalLicenseCheck = externalLicenseCheck;
    }

    @Override
    public void onChange(RefreshableConfig before, RefreshableConfig after, Set<String> changedFields) {
        LicenseConfig newConfig = (LicenseConfig) after;
        LicenseConfig oldConfig = (LicenseConfig) before;
        if (Objects.isNull(newConfig) || StringUtils.isBlank(newConfig.getLicense())) {
            log.debug("License is empty");
            return;
        }

        if (Objects.nonNull(oldConfig) && newConfig.getLicense().equals(oldConfig.getLicense())) {
            log.debug("License is already set");
            return;
        }

        try {
            importLicense(newConfig);
        } catch (Exception e) {
            log.error("Import license failed", e);
        }
    }

    public static void importLicense(LicenseConfig licenseConfig) {
        if (Objects.isNull(licenseConfig) || StringUtils.isBlank(licenseConfig.getLicense())) {
            log.debug("License is empty");
            return;
        }

        String license = new String(Base64.decodeBase64(licenseConfig.getLicense()), StandardCharsets.UTF_8);
        if (StringUtils.isEmpty(license)) {
            log.debug("License is empty");
            return;
        }
        if (!license.startsWith("MIME-Version: 1.0") || !license.contains("content.lic.serial")) {
            log.debug("License content is invalid");
            return;
        }
        License lic;
        try {
            LicenseMgr.getInstance().reload();
            lic = LicenseMgr.getInstance().getLicense();
        } catch (Exception e) {
            log.debug("License content is invalid", e);
            return;
        }
        final LicenseStatus status = lic.getStatus();
        // 构造并验证License
        switch (status) {
            case FINE:
                if (Objects.nonNull(externalLicenseCheck)) {
                    externalLicenseCheck.checkInputLicense(lic);
                }

                break;
            default:
                log.debug("License is invalid, status: {}", status);
        }
    }
}
