package kl.npki.base.core.biz.cert.parser.extension.impl;

import kl.nbase.security.asn1.x509.ExtendedKeyUsage;
import kl.nbase.security.asn1.x509.KeyPurposeId;
import kl.npki.base.core.biz.cert.parser.constant.ExtendedKeyUsageType;
import kl.npki.base.core.biz.cert.parser.constant.ExtensionType;
import kl.npki.base.core.biz.cert.parser.extension.AbstractX509ExtensionParser;

import java.util.Arrays;

/**
 * 增强型密钥用法扩展解码器
 *
 * <AUTHOR>
 * @date 2024-08-09
 */
public class ExtendedKeyUsageExtensionParserImpl extends AbstractX509ExtensionParser {

    @Override
    public ExtensionType getExtensionType() {
        return ExtensionType.EXTENDED_KEY_USAGE;
    }

    @Override
    protected String generatePrettyValue(byte[] extensionOctets) {

        ExtendedKeyUsage extendedKeyUsage = ExtendedKeyUsage.getInstance(extensionOctets);
        KeyPurposeId[] usages = extendedKeyUsage.getUsages();

        StringBuilder sb = new StringBuilder();
        Arrays.stream(usages).forEach(keyPurposeId -> {
            if (extendedKeyUsage.hasKeyPurposeId(keyPurposeId)) {
                if (sb.length() > 0) {
                    sb.append(", ");
                }
                ExtendedKeyUsageType extendedKeyUsageType = ExtendedKeyUsageType.getInstance(keyPurposeId);
                sb.append(extendedKeyUsageType.getPrettyValue());
            }
        });

        return sb.toString();
    }
}
