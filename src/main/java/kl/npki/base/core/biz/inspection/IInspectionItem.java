package kl.npki.base.core.biz.inspection;

import kl.npki.base.core.biz.check.ISelfCheckItem;
import kl.npki.base.core.biz.inspection.model.InspectionItemResult;

/**
 * 巡检项目，该检查项目会包含{@link ISelfCheckItem}自检项目，且可以扩展一些其他业务数据，仅在系统巡检时执行
 *
 * <AUTHOR>
 * @date 2025/5/8 10:42
 **/
public interface IInspectionItem {

    /**
     * 执行服务巡检
     *
     * @return 检测结果
     */
    InspectionItemResult execute();

    /**
     * 获取检测项名称
     *
     * @return 检测项名称
     */
    String getName();

    /**
     * 获取巡检项目类型
     *
     * @return 巡检项目类型
     */
    InspectionItemTypeEnum getInspectionItemType();

}
