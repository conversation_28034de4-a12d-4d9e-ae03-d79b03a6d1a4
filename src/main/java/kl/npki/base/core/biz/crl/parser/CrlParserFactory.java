package kl.npki.base.core.biz.crl.parser;

import kl.nbase.security.asn1.x509.CertificateList;
import kl.npki.base.core.biz.crl.parser.impl.X509CrlParserImpl;

/**
 * CRL解析工厂类，负责获取CRL解析实例
 *
 * <AUTHOR>
 * @date 2025/5/9
 * @since 1.0
 */

public class CrlParserFactory {

    /**
     * 获取X.509 CRL解析器实例
     *
     * @return X.509 CRL解析器实例
     */
    public static ICrlParser<CertificateList> getX509CrlParser() {
        return new X509CrlParserImpl();
    }
}
