package kl.npki.base.core.biz.check.model;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 服务自检类型,定义一些通用的枚举类型
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
public enum SelfCheckItemEnum implements EnumI18n {

    // 通用检测项
    DATABASE_SERVICE_CHECK("001", "数据库服务自检", "检查系统的数据库是否可以正常连接和交互。确保数据库服务器的运行状态、网络连接以及认证信息的准确性"),
    ADMIN_CERT_VALIDITY_CHECK("002", "管理员证书有效期自检", "检查管理员证书的有效期，确保证书没有过期并且在预期的时间内有效"),
    MGT_ROOT_CERT_VALIDITY_CHECK("003", "管理根证书有效期自检", "检查管理根证书的有效期，确保根证书没有过期并且在预期的时间内有效"),
    IDENTITY_CERT_VALIDITY_CHECK("004", "身份证书有效期自检", "检查身份证书的有效期，确保证书没有过期并且在预期的时间内有效"),
    SSL_CERT_VALIDITY_CHECK("005", "SSL站点证书有效期自检", "检查SSL站点证书的有效期，确保证书没有过期并且在预期的时间内有效"),
    ADMIN_ISSUE_INTEGRITY_CHECK("006", "管理员签发完整性自检", "检查系统管理员是否已经正确签发和配置，以确保系统安全和正常运行"),
    TIMER_JOB_CHECK("007", "定时任务执行情况自检", "检查系统中各定时任务上一次执行时间及执行的结果"),
    LICENSE_VALIDITY_CHECK("008", "License有效期自检", "检查License的有效期"),
    LICENSE_QUOTA_CHECK("009", "License配额检查", "检查授权配额数量是否足够"),
    DISK_SPACE_CHECK("010","磁盘空间自检", "检查系统磁盘空间是否足够"),
    JVM_MEMORY_CHECK("011","JVM内存自检", "检查JVM内存使用情况"),
    EM_ENGINE_SERVICE_CHECK("106", "密码机服务自检", "检查密码机服务是否正常"),

    // KM应用定制检测项
    KM_SERVICE_AVAILABILITY_CHECK("101", "密钥服务接口可用性自检", "检查密钥服务接口的可用性，确保服务接口的正常运行"),
    SKS_SERVICE_AVAILABILITY_CHECK("102", "SKS服务接口可用性自检", "检查SKS服务接口的可用性，确保服务接口的正常运行"),
    CA_RESOURCE_CHECK("103", "CA密钥资源数量自检", "检查CA密钥资源数量是否低于警告值"),

    ;

    private final String code;
    private final String name;
    private final String description;

    SelfCheckItemEnum(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return tr();
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String getMessageKey() {
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
