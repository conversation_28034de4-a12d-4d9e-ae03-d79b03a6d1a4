package kl.npki.base.core.biz.org.model;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 机构批量导入信息
 *
 * <AUTHOR>
 * @create 2024/3/27 9:58
 */
@ExcelIgnoreUnannotated
public class OrgBatchImportInfo {
    @NotBlank(message = "kl.npki.ra.management.i18n_annotation.institution_name")
    @ExcelProperty({"机构批量注册", "机构名称"})
    @Size(max = 256, message = "kl.npki.ra.management.i18n_annotation.the_length_of_the_institution_name_cannot_exceed_256")
    private String orgName;

    @ExcelProperty({"机构批量注册", "机构全称"})
    @Size(max = 256, message = "kl.npki.ra.management.i18n_annotation.the_length_of_the_full_name_of_the_institution_cannot_exceed_256")
    private String fullName;

    @ExcelProperty({"机构批量注册", "机构名称拼音"})
    @Size(max = 256, message = "kl.npki.ra.management.i18n_annotation.the_pinyin_of_the_institution_name_cannot_exceed_256")
    private String namePinyin;

    @NotBlank(message = "kl.npki.ra.management.i18n_annotation.institution_code")
    @ExcelProperty({"机构批量注册", "机构编码"})
    @Size(max = 256, message = "kl.npki.ra.management.i18n_annotation.institution_code_cannot_exceed_256")
    private String orgCode;

    @NotBlank(message = "kl.npki.ra.management.i18n_annotation.superior_institution_code")
    @ExcelProperty({"机构批量注册", "上级机构编码"})
    @Size(max = 256, message = "kl.npki.ra.management.i18n_annotation.the_superior_organization_code_cannot_exceed_256")
    private String parentCode;

    @ExcelProperty({"机构批量注册", "机构类型"})
    private Integer orgType;

    @ExcelProperty({"机构批量注册", "联系人"})
    @Size(max = 256, message = "kl.npki.ra.management.i18n_annotation.contact_person_cannot_exceed_256")
    private String linkman;

    @ExcelProperty({"机构批量注册", "联系电话"})
    @Size(max = 256, message = "kl.npki.ra.management.i18n_annotation.the_contact_phone_number_cannot_exceed_256")
    private String telephone;

    @ExcelProperty({"机构批量注册", "邮政编码"})
    @Size(max = 256, message = "kl.npki.ra.management.i18n_annotation.the_postal_code_cannot_exceed_256")
    private String postcode;

    @ExcelProperty({"机构批量注册", "联系地址"})
    @Size(max = 256, message = "kl.npki.ra.management.i18n_annotation.the_contact_address_cannot_exceed_256")
    private String address;

    @ExcelProperty({"机构批量注册", "机构描述"})
    @Size(max = 256, message = "kl.npki.ra.management.i18n_annotation.institution_description_cannot_exceed_256")
    private String orgDesc;

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getNamePinyin() {
        return namePinyin;
    }

    public void setNamePinyin(String namePinyin) {
        this.namePinyin = namePinyin;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public String getLinkman() {
        return linkman;
    }

    public void setLinkman(String linkman) {
        this.linkman = linkman;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getOrgDesc() {
        return orgDesc;
    }

    public void setOrgDesc(String orgDesc) {
        this.orgDesc = orgDesc;
    }
}