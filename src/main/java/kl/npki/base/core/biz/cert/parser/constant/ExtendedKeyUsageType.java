package kl.npki.base.core.biz.cert.parser.constant;

import kl.nbase.i18n.i18n.I18nUtil;
import kl.nbase.security.asn1.custom.RfcIdentifiers;
import kl.nbase.security.asn1.x509.KeyPurposeId;

import java.util.HashMap;
import java.util.Map;

import static kl.npki.base.core.constant.I18nConstant.*;

/**
 * 扩展密钥用法类型，一些常用的扩展密钥用法类型，定义了oid、名称以及描述信息
 * <p>
 * 由于考虑到该项存在很多类型，所以不定义成枚举类
 *
 * <AUTHOR>
 * @date 2024-08-09
 */
public class ExtendedKeyUsageType {

    private static final Map<KeyPurposeId, ExtendedKeyUsageType> EXTENDED_KEY_USAGE_TYPE_MAP = new HashMap<>(8);

    /**
     * 服务器认证扩展密钥用法。
     */
    public static final ExtendedKeyUsageType SERVER_AUTH = new ExtendedKeyUsageType(
        KeyPurposeId.id_kp_serverAuth, "ServerAuth", SERVER_AUTHENTICATION_I18N_KEY);

    /**
     * 客户端认证扩展密钥用法。
     */
    public static final ExtendedKeyUsageType CLIENT_AUTH = new ExtendedKeyUsageType(
        KeyPurposeId.id_kp_clientAuth, "ClientAuth", CLIENT_AUTHENTICATION_I18N_KEY);

    /**
     * 代码签名扩展密钥用法。
     */
    public static final ExtendedKeyUsageType CODE_SIGNING = new ExtendedKeyUsageType(
        KeyPurposeId.id_kp_codeSigning, "CodeSigning", CODE_SIGNING_I18N_KEY);

    /**
     * Email保护扩展密钥用法。
     */
    public static final ExtendedKeyUsageType EMAIL_PROTECTION = new ExtendedKeyUsageType(
        KeyPurposeId.id_kp_emailProtection, "EmailProtection", EMAIL_PROTECTION_I18N_KEY);

    /**
     * 时间戳绑定扩展密钥用法。
     */
    public static final ExtendedKeyUsageType TIME_STAMPING = new ExtendedKeyUsageType(
        KeyPurposeId.id_kp_timeStamping, "TimeStamping", TIMESTAMP_GENERATION_I18N_KEY);

    /**
     * OCSP签名扩展密钥用法。
     */
    public static final ExtendedKeyUsageType OCSP_SIGNING = new ExtendedKeyUsageType(
        KeyPurposeId.id_kp_OCSPSigning, "OCSPSigning", OCSP_RESPONSE_SIGNING_I18N_KEY);

    /**
     * KDC身份认证扩展密钥用法。
     */
    public static final ExtendedKeyUsageType KDC_AUTHENTICATION = new ExtendedKeyUsageType(
        KeyPurposeId.getInstance(RfcIdentifiers.id_kp_pkinit_KPkdc), "KDCAuthentication", KDC_AUTHENTICATION_I18N_KEY);

    /**
     * 智能卡登录扩展密钥用法。
     */
    public static final ExtendedKeyUsageType SMART_CARD_LOGON = new ExtendedKeyUsageType(
        KeyPurposeId.id_kp_smartcardlogon, "SmartCardLogin", SMART_CARD_LOGIN_I18N_KEY);

    /**
     * 文件系统加密扩展密钥用法。
     */
    public static final ExtendedKeyUsageType SZ_OID_EFS_CRYPTO = new ExtendedKeyUsageType(
        KeyPurposeId.getInstance(RfcIdentifiers.sz_oid_efs_crypto), "SzOidEfsCrypto", FILE_SYSTEM_ENCRYPTION_I18N_KEY);

    public static ExtendedKeyUsageType getInstance(KeyPurposeId keyPurposeId) {

        String oid = keyPurposeId.getId();
        return EXTENDED_KEY_USAGE_TYPE_MAP.values().stream()
            .filter(extendedKeyUsageType -> extendedKeyUsageType.getKeyPurposeId().equals(keyPurposeId))
            .findFirst()
            // 如果没有找到，则返回一个默认的ExtensionType，并使用OID字符串作为名称和描述
            .orElse(new ExtendedKeyUsageType(keyPurposeId, oid, oid));
    }

    private final KeyPurposeId keyPurposeId;
    private final String name;
    private final String desc;

    /**
     * 构造方法。
     *
     * @param keyPurposeId 扩展密钥用法标识符
     * @param name         增强型密钥用法名称，一般是规范中提及的名称
     * @param desc         增强型密钥用法描述信息，一般是中文信息
     */
    public ExtendedKeyUsageType(KeyPurposeId keyPurposeId, String name, String desc) {
        this.keyPurposeId = keyPurposeId;
        this.name = name;
        this.desc = desc;

        EXTENDED_KEY_USAGE_TYPE_MAP.putIfAbsent(keyPurposeId, this);
    }

    public KeyPurposeId getKeyPurposeId() {
        return keyPurposeId;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return I18nUtil.tr(desc);
    }

    public String getPrettyValue() {
        return String.format("%s(%s)", getDesc(), getKeyPurposeId().getId());
    }
}
