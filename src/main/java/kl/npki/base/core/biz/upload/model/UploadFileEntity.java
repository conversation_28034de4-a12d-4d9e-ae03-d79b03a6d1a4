package kl.npki.base.core.biz.upload.model;

import kl.npki.base.core.constant.UploadFileProcessStatusEnum;
import kl.npki.base.core.repository.IUploadFileRepository;
import kl.npki.base.core.repository.RepositoryFactory;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2024/5/8 下午3:33
 */
public class UploadFileEntity implements Serializable {

    private static final long serialVersionUID = -1432835703670830649L;

    private Long id;

    /**
     * 上传文件业务接口名称
     */
    private String interfaceName;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 如果存储方式本次磁盘存储，则次字段表示磁盘存储文件路径
     */
    private String fileUri;

    /**
     * Excel文件中的数据处理状态
     * <li>-1 尚未开始处理</li>
     * <li>0 正在处理</li>
     * <li>1 处理完成</li>
     */
    private Integer processStatus;

    /**
     * 总计条数
     */
    private Integer totalCount;

    /**
     * 已经处理数量
     */
    private Integer processCount;

    /**
     * 处理成功条数
     */
    private Integer processSuccessCount;

    /**
     * 处理失败条数
     */
    private Integer processErrorCount;

    /**
     * 上传的文件
     */
    private String requestFile;

    /**
     * 上传文件内容处理结果
     */
    private String resultFile;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除标志
     */
    private Integer isDelete;


    /**
     * 当前操作人员id
     */
    private String lastOperatorId;


    private static final IUploadFileRepository uploadFileRepository = RepositoryFactory.get(IUploadFileRepository.class);

    /**
     * 保存CA对象
     *
     * @return ca标识id
     */
    public Long save() {
        setProcessStatus(UploadFileProcessStatusEnum.UPLOAD.getStatus());
        setProcessCount(0);
        setProcessSuccessCount(0);
        setProcessErrorCount(0);
        LocalDateTime now = LocalDateTime.now();
        setCreatedAt(now);
        setUpdatedAt(now);
        return uploadFileRepository.importFile(this);
    }

    public boolean delete() {
        return uploadFileRepository.delete(this.id);
    }

    public boolean update() {
        setUpdatedAt(LocalDateTime.now());
        return uploadFileRepository.update(this);
    }

    public Long getId() {
        return id;
    }

    public UploadFileEntity setId(Long id) {
        this.id = id;
        return this;
    }

    public String getInterfaceName() {
        return interfaceName;
    }

    public UploadFileEntity setInterfaceName(String interfaceName) {
        this.interfaceName = interfaceName;
        return this;
    }

    public String getFileName() {
        return fileName;
    }

    public UploadFileEntity setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public String getFileUri() {
        return fileUri;
    }

    public UploadFileEntity setFileUri(String fileUri) {
        this.fileUri = fileUri;
        return this;
    }

    public Integer getProcessStatus() {
        return processStatus;
    }

    public UploadFileEntity setProcessStatus(Integer processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public UploadFileEntity setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
        return this;
    }

    public String getRequestFile() {
        return requestFile;
    }

    public UploadFileEntity setRequestFile(String requestFile) {
        this.requestFile = requestFile;
        return this;
    }

    public String getResultFile() {
        return resultFile;
    }

    public UploadFileEntity setResultFile(String resultFile) {
        this.resultFile = resultFile;
        return this;
    }

    public Integer getProcessCount() {
        return processCount;
    }

    public UploadFileEntity setProcessCount(Integer processCount) {
        this.processCount = processCount;
        return this;
    }

    public Integer getProcessSuccessCount() {
        return processSuccessCount;
    }

    public UploadFileEntity setProcessSuccessCount(Integer processSuccessCount) {
        this.processSuccessCount = processSuccessCount;
        return this;
    }

    public Integer getProcessErrorCount() {
        return processErrorCount;
    }

    public UploadFileEntity setProcessErrorCount(Integer processErrorCount) {
        this.processErrorCount = processErrorCount;
        return this;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public UploadFileEntity setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public UploadFileEntity setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public UploadFileEntity setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    public String getLastOperatorId() {
        return lastOperatorId;
    }

    public void setLastOperatorId(String lastOperatorId) {
        this.lastOperatorId = lastOperatorId;
    }

}