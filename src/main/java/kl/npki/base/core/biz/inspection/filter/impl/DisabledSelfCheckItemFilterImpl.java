package kl.npki.base.core.biz.inspection.filter.impl;

import kl.npki.base.core.biz.inspection.IInspectionItem;
import kl.npki.base.core.biz.inspection.adaptor.SelfCheckItemInspectionAdaptor;
import kl.npki.base.core.biz.inspection.filter.InspectionItemFilter;

/**
 * 未启用的自检项巡检过滤器
 *
 * <AUTHOR>
 * @date 2025/5/26 13:03
 **/
public class DisabledSelfCheckItemFilterImpl implements InspectionItemFilter {

    @Override
    public boolean filter(IInspectionItem inspectionItem) {
        if (inspectionItem == null) {
            return true;
        }

        // 如果是自检适配的巡检且没有启用，则过滤掉
        return inspectionItem instanceof SelfCheckItemInspectionAdaptor && !((SelfCheckItemInspectionAdaptor) inspectionItem).isEnabled();
    }
}
