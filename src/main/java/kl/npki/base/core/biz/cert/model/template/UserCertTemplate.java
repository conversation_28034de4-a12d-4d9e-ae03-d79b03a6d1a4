package kl.npki.base.core.biz.cert.model.template;

import kl.nbase.security.asn1.misc.NetscapeCertType;
import kl.nbase.security.asn1.x500.X500Name;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.asn1.x509.ExtensionsGenerator;
import kl.nbase.security.asn1.x509.KeyPurposeId;
import kl.npki.base.core.utils.CertExtensionUtils;

import java.security.PublicKey;
import java.util.Date;

/**
 * 用户证书模板
 *
 * <AUTHOR>
 * @date 2022/12/22
 */
public class UserCertTemplate extends AbstractCertTemplate {

    public UserCertTemplate(X500Name subject, PublicKey publicKey, Date before, Date after, Certificate issuer, String signAlgoOid) {
        super(subject, publicKey, before, after, issuer, signAlgoOid);
    }

    @Override
    public void addExtensions(ExtensionsGenerator extGen) throws Exception {
        // 添加基本约束
        add(extGen, CertExtensionUtils.makeBasicConstraints(Boolean.FALSE, null));
        // 添加密钥用法
        add(extGen, CertExtensionUtils.makeKeyUsage(getEndEntitySigCertKeyUsage(false), isSm2Algo));
        // 添加扩展密钥用法
        add(extGen, CertExtensionUtils.makeExtendedKeyUsage(KeyPurposeId.id_kp_clientAuth));
        // 添加NetscapeCertType
        add(extGen, CertExtensionUtils.makeNetscapeCertType(NetscapeCertType.sslClient));
        // 添加使用者密钥标识符
        addSubjectKeyIdentifier(extGen);
        // 添加授权密钥标识符
        addAuthorityKeyIdentifier(extGen);
        // 添加AIA地址
        addAuthorityInformationAccess(extGen);
        // 添加CRL地址
        addCrlDistributionPoints(extGen);
    }

}
