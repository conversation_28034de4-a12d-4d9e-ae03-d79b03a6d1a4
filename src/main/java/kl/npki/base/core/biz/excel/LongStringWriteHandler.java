package kl.npki.base.core.biz.excel;

import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import kl.nbase.rpc.core.utils.StringUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.Cell;

/**
 * Excel单元格文本长度超过最大长度时的处理器，对于超出最大长度的文本进行截断，并在末尾添加未读取完成标识“...”
 *
 * <AUTHOR>
 * @create 2025/6/9 下午2:14
 */
public class LongStringWriteHandler implements CellWriteHandler {

    /**
     * 未读取完成标识的后缀
     */
    private static final String TRUNCATE_SUFFIX = "...";

    /**
     * Excel单元格最大文本长度
     */
    private static final int EXCEL_MAX_LENGTH = SpreadsheetVersion.EXCEL2007.getMaxTextLength();

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder,
                                       WriteTableHolder writeTableHolder,
                                       WriteCellData<?> cellData,
                                       Cell cell,
                                       Head head,
                                       Integer relativeRowIndex,
                                       Boolean isHead) {
        if (Boolean.TRUE.equals(isHead) || cellData == null) {
            return;
        }

        if (cellData.getType() == CellDataTypeEnum.STRING) {
            String text = cellData.getStringValue();
            if (StringUtils.isEmpty(text)) {
                return;
            }

            // 超出最大长度处理，对于超出部分截断，然后在最后面增加未读取完成标识“...”
            if (text.length() > EXCEL_MAX_LENGTH) {
                int truncateLength = EXCEL_MAX_LENGTH - TRUNCATE_SUFFIX.length();
                String newText = text.substring(0, truncateLength) + TRUNCATE_SUFFIX;
                cellData.setStringValue(newText);
            }
        }
    }
}