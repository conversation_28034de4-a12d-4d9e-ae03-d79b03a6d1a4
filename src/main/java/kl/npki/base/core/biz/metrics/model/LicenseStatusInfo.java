package kl.npki.base.core.biz.metrics.model;

import kl.npki.base.core.constant.BaseConstant;
import java.io.Serializable;

/**
 * @Author: guoq
 * @Date: 2024/10/17
 * @description: license 状态信息
 */
public class LicenseStatusInfo implements Serializable {

    private static final long serialVersionUID = 6578937190081000309L;
    private long validStart;
    private long validEnd;
    /**
     * 状态是否生效：1代表有效、0代表异常
     */
    private long status;

    public LicenseStatusInfo() {
        this.validStart = BaseConstant.METRICS_UNKNOWN_TIME;
        this.validEnd = BaseConstant.METRICS_UNKNOWN_TIME;
        this.status = BaseConstant.METRICS_UNKNOWN_TIME;
    }

    public LicenseStatusInfo(long validStart, long validEnd, long status) {
        this.validStart = validStart;
        this.validEnd = validEnd;
        this.status = status;
    }

    public long getValidStart() {
        return validStart;
    }

    public void setValidStart(long validStart) {
        this.validStart = validStart;
    }

    public long getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(long validEnd) {
        this.validEnd = validEnd;
    }

    public long getStatus() {
        return status;
    }

    public void setStatus(long status) {
        this.status = status;
    }
}
