package kl.npki.base.core.rpc;

import kl.npki.base.core.biz.api.BaseApi;

import java.util.Map;

/**
 * RPC客户端收集器接口
 *
 * <p>设计说明:</p>
 * <ol>
 *   <li>负责收集系统中所有继承BaseApi的接口</li>
 *   <li>配合RpcClientFactory使用,为工厂提供客户端实例</li>
 *   <li>支持多种客户端实现方式(不局限于Feign)</li>
 * </ol>
 *
 * <AUTHOR> Shi
 * @date 2024/12/18
 */
public interface RpcClientCollector {

    /**
     * 收集所有BaseApi的接口
     *
     * @return Map<Class<?>, BaseApi>
     */
    Map<Class<?>, BaseApi> collect();
}