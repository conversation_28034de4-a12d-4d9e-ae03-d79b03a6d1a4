package kl.npki.base.core.repository;

import kl.npki.base.core.biz.log.model.OpLogInfo;
import kl.npki.base.core.constant.AuditStatusEnum;

/**
 * <AUTHOR>
 * @date 2022/12/14
 */
public interface IOpLogRepository extends ILogBaseRepository<OpLogInfo> {

    /**
     * 存储操作日志
     *
     * @param opLogInfo
     * @return
     */
    void saveOpLog(OpLogInfo opLogInfo);

    /**
     * 操作日志详情展示
     * @param logId
     * @return
     */
    OpLogInfo searchOpLogById(Long logId);

    /**
     * 修改审计状态
     * @param logId
     * @param auditStatus
     */
    void modifyAuditStatus(Long logId, AuditStatusEnum auditStatus);
}
