package kl.npki.base.core.repository;


import kl.npki.base.core.biz.mainkey.model.MainKeyEntity;
import java.util.List;

/**
 * @Author: guoq
 * @Date: 2022/8/22
 * @description: 主密钥保存仓库
 */
public interface IMainKeyRepository extends BaseRepository {

    /**
     * 保存新的主密钥，新主密钥做为系统默认主密钥
     *
     * @param mainKeyEntity
     * @return
     */
    Long insertNewMainKey(MainKeyEntity mainKeyEntity);

    /**
     * 修改主密钥
     *
     * @param mainKeyEntity
     * @return
     */
    boolean updateMainKey(MainKeyEntity mainKeyEntity);

    /**
     * 查询所有状态正常的主密钥
     *
     * @return
     */
    List<MainKeyEntity> searchMainKeyList();

    /**
     * 根据id查询状态正常的主密钥
     * @param keyId 密钥id
     * @return
     */
    MainKeyEntity searchMainKeyById(Long keyId);

    /**
     * 根据算法查询状态正常的主密钥
     * @param algoName 密钥id
     * @return
     */
    MainKeyEntity searchMainKeyByAlgo(String algoName);
}
