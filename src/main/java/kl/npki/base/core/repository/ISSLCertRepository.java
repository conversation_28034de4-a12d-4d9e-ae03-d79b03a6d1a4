package kl.npki.base.core.repository;

import kl.npki.base.core.biz.ssl.SslClientCertEntity;

/**
 * <AUTHOR>
 * @date 2023/1/4
 */
public interface ISSLCertRepository extends BaseRepository {

    /**
     * 保存客户端站点证书
     *
     * @param sslCertEntity
     * @return
     */
    Long saveSSLClientCert(SslClientCertEntity sslCertEntity);

    /**
     * 更新客户端站点证书
     *
     * @param sslCertEntity
     * @return
     */
    boolean updateSSLClientCert(SslClientCertEntity sslCertEntity);

    /**
     * 废除子系统的客户端站点证书
     *
     * @param sysId
     */
    void revokeSSLCertBySysId(Long sysId);

    /**
     * 查询站点证书
     *
     * @param sysId 子系统ID
     * @return 站点证书
     */
    SslClientCertEntity searchSSLClientCert(Long sysId);
}
