package kl.npki.base.core.repository;

import kl.npki.base.core.biz.org.model.OrgTraceEntity;

import java.util.List;

/**
 * 机构历史轨迹数据仓库
 *
 * <AUTHOR>
 * @date 2024/07/04
 */
public interface IOrgTraceRepository extends BaseRepository {
    /**
     * 新增轨迹记录
     *
     * @param traceEntity
     */
    Long insert(OrgTraceEntity traceEntity);

    /**
     * 根据实体ID查找所有轨迹记录
     *
     * @param entityId
     * @return
     */
    List<OrgTraceEntity> searchByEntityId(Long entityId);
}
