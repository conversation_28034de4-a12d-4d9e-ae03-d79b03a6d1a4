package kl.npki.base.core.repository;

import kl.npki.base.core.biz.config.model.FileConfigEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/21
 */
public interface IFileConfigRepository extends BaseRepository{

    /**
     * 保存数据库中的文件配置并刷新本地文件
     * @param fileConfigEntity 文件配置实体
     */
    void saveAndRefreshFile(FileConfigEntity fileConfigEntity);

    /**
     * 获取文件配置列表
     * @return 文件配置列表
     */
    List<FileConfigEntity> getFileConfigList();
}
