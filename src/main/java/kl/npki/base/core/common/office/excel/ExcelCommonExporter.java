package kl.npki.base.core.common.office.excel;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.util.EasyExcelTempFileCreationStrategy;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import kl.npki.base.core.biz.excel.LongStringWriteHandler;
import kl.npki.base.core.biz.export.AbstractTempFile;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.utils.DateUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.util.TempFile;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.lang.reflect.Field;
import java.util.*;

/**
 * 生成 Excel 文件并写入数据
 * <p>
 * <b>代码示例：</b>
 * <pre>
 *    // 1. 构造导出对象
 *    // 使用enableFieldMapping可以指定需要导出的字段，如果enableFieldMapping为null，则导出所有的字段
 *    ExcelCommonExporter<User> exporter = new ExcelCommonExporter<>(User.class, "./", enableFieldMapping);
 *
 *    // 2. 写入数据
 *    exporter.update(userList);
 *
 *    // 3. 完成数据插入，导出生成的Excel文件
 *    File file = exporter.doFinal();
 * </pre>
 * <b>注意：</b><br/>
 * <b>仅支持生成.xlsx格式的Excel</b><br/>
 * <b>数据类型中的属性字段必须使用{@link ExcelProperty}注解标注，只有添加了ExcelProperty注解的属性才会作为Excel表头</b>
 * <p/>
 *
 * <AUTHOR>
 * @create 2025/2/11 下午6:14 普通用户导出
 */
public class ExcelCommonExporter<T> extends AbstractTempFile {
    /**
     * 导出文件
     */
    private final File exportFile;

    /**
     * 批量处理结果Excel输出流
     */
    private final ExcelWriter excelWriter;

    /**
     * Excel的工作表
     */
    private final WriteSheet writeSheet;

    /**
     * 数据类型
     */
    private final Class<T> dataType;

    /**
     * @param dataType           数据类型
     * @param exportDir          导出目录，为空则默认为系统临时目录
     * @param enableFieldMapping 字段映射，key为字段名，value为显示名称.为空则默认为字段名
     */
    public ExcelCommonExporter(Class<T> dataType, String exportDir, Map<String, List<String>> enableFieldMapping) {
        this(dataType, exportDir, enableFieldMapping, null);
    }

    /**
     * @param dataType           数据类型
     * @param exportDir          导出目录，为空则默认为系统临时目录
     * @param enableFieldMapping 字段映射，key为字段名，value为显示名称.为空则默认为字段名
     * @param writeHandlerList   写入处理器，为空则默认为内置的处理器
     */
    public ExcelCommonExporter(Class<T> dataType,
                               String exportDir,
                               Map<String, List<String>> enableFieldMapping,
                               List<WriteHandler> writeHandlerList) {
        this.dataType = dataType;
        String fileName = DateUtil.formatFileDate(new Date()) + ".xlsx";
        if (StringUtils.isEmpty(exportDir)) {
            exportFile = getTempFile(fileName);
        } else {
            exportFile = new File(exportDir + File.separator + fileName);
        }

        try {
            excelWriter = EasyExcelFactory.write(new FileOutputStream(exportFile)).build();
        } catch (FileNotFoundException e) {
            throw BaseInternalError.FILE_NO_FOUND_FAIL.toException(e);
        }

        // 设置动态表头
        ExcelWriterSheetBuilder sheetBuilder = EasyExcelFactory.writerSheet()
                .head(generateHeader(enableFieldMapping))
                .registerConverter(new LongStringConverter())
                .includeColumnFieldNames(generateIncludeColumnFieldNames(enableFieldMapping));

        // 设置写入处理器
        writeHandlerList = (writeHandlerList == null) ? getWriteHandler() : writeHandlerList;
        writeHandlerList.forEach(sheetBuilder::registerWriteHandler);
        writeSheet = sheetBuilder.build();

        // 此处需要指定一下临时文件创建策略
        File poiTempDir = new File(BaseConfigWrapper.getSysConfig().getTempDir() + bizName());
        TempFile.setTempFileCreationStrategy(new EasyExcelTempFileCreationStrategy(poiTempDir));
    }

    /**
     * 将数据写入excel文件中
     *
     * @param data 数据集合
     */
    public void write(List<T> data) {
        excelWriter.write(data, writeSheet);
    }

    /**
     * 完成数据插入，导出生成的Excel文件
     *
     * @return Excel文件
     */
    public File doFinal() {
        excelWriter.finish();
        return exportFile;
    }

    /**
     * 获取写入处理器<br/>
     * 默认内置了两个样式处理器
     *
     * @return
     */
    protected List<WriteHandler> getWriteHandler() {
        ArrayList<WriteHandler> writeHandlerList = new ArrayList<>();

        // 配置表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
        headWriteCellStyle.setFillPatternType(FillPatternType.BRICKS);
        headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        headWriteCellStyle.setBorderTop(BorderStyle.THIN);
        headWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        headWriteCellStyle.setBorderRight(BorderStyle.THIN);

        // 配置内容样式
        WriteCellStyle contentCellStyle = new WriteCellStyle();
        contentCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentCellStyle.setBorderBottom(BorderStyle.THIN);
        contentCellStyle.setBorderTop(BorderStyle.THIN);
        contentCellStyle.setBorderLeft(BorderStyle.THIN);
        contentCellStyle.setBorderRight(BorderStyle.THIN);

        // 添加样式策略
        writeHandlerList.add(new HorizontalCellStyleStrategy(headWriteCellStyle, contentCellStyle));
        writeHandlerList.add(new SimpleColumnWidthStyleStrategy(25));
        writeHandlerList.add(new LongStringWriteHandler());

        return writeHandlerList;
    }

    /**
     * 获取Excel文件头
     *
     * @param enableFieldMapping
     * @return
     */
    private List<List<String>> generateHeader(Map<String, List<String>> enableFieldMapping) {
        List<List<String>> head = new ArrayList<>();
        Field[] declaredFields = FieldUtils.getFieldsWithAnnotation(dataType, ExcelProperty.class);
        boolean existFieldMapping = MapUtils.isNotEmpty(enableFieldMapping);
        Arrays.stream(declaredFields).forEach(declaredField -> {
            String fieldName = declaredField.getName();
            ExcelProperty annotation = declaredField.getAnnotation(ExcelProperty.class);

            // 检查是否允许导出
            boolean isFieldExcluded = existFieldMapping && !enableFieldMapping.containsKey(fieldName);
            boolean isAnnotationEmpty = annotation != null && ObjectUtils.isEmpty(annotation.value());

            // 如果字段被排除或注解为空，则跳过
            if (isFieldExcluded || isAnnotationEmpty) {
                return;
            }

            // 指定名称
            List<String> enableValueList = existFieldMapping ? enableFieldMapping.get(fieldName) : null;
            // 注解中指定的名称
            List<String> annotationValueList = Arrays.asList(annotation.value());
            List<String> value = ObjectUtils.isEmpty(enableValueList) ? annotationValueList : enableValueList;
            head.add(value);
        });
        return head;
    }

    /**
     * 获取动态表头
     *
     * @param enableFieldMapping
     * @return
     */
    private List<String> generateIncludeColumnFieldNames(Map<String, List<String>> enableFieldMapping) {
        List<String> includeColumn = new ArrayList<>();
        Field[] declaredFields = FieldUtils.getFieldsWithAnnotation(dataType, ExcelProperty.class);
        boolean existFieldMapping = MapUtils.isNotEmpty(enableFieldMapping);
        Arrays.stream(declaredFields).forEach(declaredField -> {
            String fieldName = declaredField.getName();
            ExcelProperty annotation = declaredField.getAnnotation(ExcelProperty.class);

            // 检查是否允许导出
            boolean isFieldExcluded = existFieldMapping && !enableFieldMapping.containsKey(fieldName);
            boolean isAnnotationEmpty = annotation != null && ObjectUtils.isEmpty(annotation.value());

            // 如果字段被排除或注解为空，则跳过
            if (isFieldExcluded || isAnnotationEmpty) {
                return;
            }

            includeColumn.add(fieldName);
        });
        return includeColumn;
    }

    @Override
    public String bizName() {
        return "base/excel";
    }
}