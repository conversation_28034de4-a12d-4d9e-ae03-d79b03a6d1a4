package kl.npki.base.core.common.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import kl.npki.base.core.common.date.CustomDateDeserializer;
import kl.npki.base.core.exception.BaseInternalError;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class StringToLongDeserializer extends JsonDeserializer<Long> {
    private static final Logger logger = LoggerFactory.getLogger(CustomDateDeserializer.class);

    @Override
    public Long deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getText(); // 获取 JSON 中的字符串值
        if (StringUtils.isBlank(value)) {
            // 处理空值或空字符串
            return null;
        }
        try {
            // 转换为 Long
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            logger.error("String deserializer to Long Error, String Value:[{}]", value);
            throw BaseInternalError.LONG_PARSE_ERROR.toException(e);
        }
    }
}