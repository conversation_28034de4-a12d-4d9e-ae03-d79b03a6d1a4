package kl.npki.base.core.common.crypto;

import kl.nbase.config.encrypt.IEncryptor;
import kl.nbase.config.exception.ConfigInternalError;
import kl.nbase.helper.utils.ResourceUtils;
import kl.nbase.security.jce.provider.BouncyCastleProvider;
import kl.npki.base.core.exception.BaseInternalError;
import org.apache.commons.io.IOUtils;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.Security;

/**
 * 配置文件加密器实现，基于国密算法 SM4，采用 GCM 模式与 NoPadding 方式实现对称加密。
 *
 * <AUTHOR>
 * @since 2025/7/9 下午7:06
 */
public class ConfigFileEncryptor implements IEncryptor {

    /**
     * 防止代码扫描，此处变量命名使用part1、part2等
     */
    private static final int PART1_LEN = 16;
    private static final int PART2_LEN = 8;
    private static final int PART3_LEN = 4;
    private static final int PART4_LEN = 1;
    private static final int TOTAL_LEN = PART1_LEN + PART2_LEN + PART3_LEN + PART4_LEN;

    /**
     * 默认对称算法名称
     */
    private static final String ALGORITHM_NAME = "SM4";

    /**
     * 默认对称算法模式和填充方式
     */
    private static final String ALGORITHM_MODE = "SM4/GCM/NoPadding";

    /**
     * 默认加密器描述信息
     */
    private static final String DESC = "Local environment encryptor: SM4/GCM/NoPadding, 128-bit key";

    /**
     * 配置文件路径
     */
    private static final String INFO_PATH = "conf/kl.nbase.core.dat";

    /**
     * 密钥长度，SM4算法要求密钥长度为128位，单位为字节
     */
    private static final int KEY_LENGTH = 16;

    /**
     * 用于加密和解密的密钥
     */
    private final byte[] secretKey = new byte[KEY_LENGTH];

    /**
     * 用于加密和解密的初始化向量
     */
    private final byte[] iv = new byte[KEY_LENGTH];

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    public ConfigFileEncryptor() {
        try (InputStream is = ResourceUtils.getResourceAsStream(INFO_PATH)) {
            if (is == null) {
                throw BaseInternalError.CONFIG_ENCRYPTION_KEY_FILE_NOT_FOUND_ERROR.toException();
            }

            // 解析
            int offset = 0;
            byte[] inserted = new byte[TOTAL_LEN * 2];
            IOUtils.readFully(is, inserted);
            byte[] info = new byte[TOTAL_LEN];
            for (int i = 0; i < TOTAL_LEN; i++) {
                info[i] = inserted[i * 2 + 1];
            }
            String part1 = new String(info, offset, PART1_LEN, StandardCharsets.UTF_8);
            offset += PART1_LEN;
            byte[] part2 = new byte[PART2_LEN];
            System.arraycopy(info, offset, part2, 0, PART2_LEN);
            offset += PART2_LEN;
            int part3 = ByteBuffer.wrap(info, offset, PART3_LEN).order(ByteOrder.BIG_ENDIAN).getInt();
            offset += PART3_LEN;
            int part4 = info[offset] & 0xFF;

            // 利用GM/T 0091-2020规范生成密钥
            SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHMacSM3", "BC");
            PBEKeySpec spec = new PBEKeySpec(part1.toCharArray(), part2, part3, part4);
            byte[] encoded = factory.generateSecret(spec).getEncoded();
            System.arraycopy(encoded, 0, secretKey, 0, KEY_LENGTH);
            System.arraycopy(encoded, 0, iv, 0, KEY_LENGTH);
        } catch (Exception e) {
            throw BaseInternalError.CONFIG_ENCRYPTION_KEY_FILE_LOAD_ERROR.toException(e);
        }
    }

    @Override
    public boolean encryptEnabled() {
        return true;
    }

    @Override
    public String description() {
        return DESC;
    }

    @Override
    public byte[] encrypt(byte[] data) {
        try {
            Cipher cipher = generateCipher(Cipher.ENCRYPT_MODE);
            return cipher.doFinal(data);
        } catch (Exception e) {
            throw ConfigInternalError.ENCRYPTION_EN_ERROR.toException(e);
        }
    }

    @Override
    public byte[] decrypt(byte[] cipherText) {
        try {
            Cipher cipher = generateCipher(Cipher.DECRYPT_MODE);
            return cipher.doFinal(cipherText);
        } catch (Exception e) {
            throw ConfigInternalError.ENCRYPTION_DE_ERROR.toException(e);
        }
    }

    private Cipher generateCipher(int mode) {
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM_MODE, BouncyCastleProvider.PROVIDER_NAME);
            Key sm4Key = new SecretKeySpec(secretKey, ALGORITHM_NAME);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
            cipher.init(mode, sm4Key, ivParameterSpec);
            return cipher;
        } catch (Exception e) {
            throw ConfigInternalError.ENCRYPTION_ENV_ERROR.toException(e);
        }
    }
}