package kl.npki.base.core.common.office.excel;

import com.alibaba.excel.write.handler.WriteHandler;
import kl.nbase.helper.office.excel.InternalPrivateKeyExcelSigner;
import kl.npki.base.core.exception.BaseInternalError;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPrivateKey;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 生成带私钥签名的Excel文件，适用于<b>能获取到私钥原文的场景</b>
 * <p>
 * <b>代码示例：</b>
 * <pre>
 *     // 1. 构造导出对象
 *     ExcelWithKeyExporter<User> exporter =
 *          new ExcelWithKeyExporter<>(
 *              User.class,
 *              "./",
 *              enableFieldMapping,
 *              privateKey,
 *              certificateList
 *          );
 *
 *     // 2. 写入数据
 *     exporter.update(userList);
 *
 *    // 3. 完成数据插入，导出生成的Excel文件，导出的Excel文件包含签名值，签名类型是XAdES-EPES类型
 *    File file = exporter.doFinal();
 * </pre>
 * <b>注意：</b><br/>
 * <b>私钥必须是RSA类型</b><br/>
 * <p/>
 *
 * <AUTHOR>
 * @create 2025/2/12 上午11:26
 */
public class ExcelWithKeyExporter<T> extends ExcelCommonExporter<T> {

    /**
     * Excel文件签名实现类
     */
    private final InternalPrivateKeyExcelSigner signer;

    /**
     * @param dataType        数据类型
     * @param exportDir       导出目录
     * @param privateKey      做签名使用的私钥
     * @param certificateList 证书链
     */
    public ExcelWithKeyExporter(Class<T> dataType,
                                String exportDir,
                                Map<String, List<String>> enableFieldMapping,
                                RSAPrivateKey privateKey,
                                List<X509Certificate> certificateList) {
        this(dataType,
                exportDir,
                enableFieldMapping,
                privateKey,
                certificateList,
                null);
    }

    /**
     * @param dataType           数据类型
     * @param exportDir          导出目录
     * @param privateKey         做签名使用的私钥
     * @param certificateList    证书链
     * @param enableFieldMapping 字段映射
     * @param writeHandlerList   写处理器列表
     */
    public ExcelWithKeyExporter(Class<T> dataType,
                                String exportDir,
                                Map<String, List<String>> enableFieldMapping,
                                RSAPrivateKey privateKey,
                                List<X509Certificate> certificateList,
                                List<WriteHandler> writeHandlerList) {
        super(dataType, exportDir, enableFieldMapping, writeHandlerList);
        this.signer = new InternalPrivateKeyExcelSigner(privateKey, certificateList);
    }

    @Override
    public File doFinal() {
        File exportFile = super.doFinal();
        try {
            // 对Excel做签名，生成带签名的Excel字节流
            byte[] excelData = signer.sign(exportFile, new Date());
            // 将字节流写入原Excel文件中
            FileUtils.writeByteArrayToFile(exportFile, excelData);
            // 验证Excel文件中的签名值是否有效
            if (!signer.verify(exportFile)) {
                throw BaseInternalError.FILE_VERIFY_FAIL.toException();
            }
            return exportFile;
        } catch (Exception e) {
            throw BaseInternalError.FILE_SIGNATURE_FAIL.toException(e.getMessage(), e);
        }
    }

    public boolean verify(File excelFile) {
        try {
            return signer.verify(excelFile);
        } catch (Exception e) {
            throw BaseInternalError.FILE_VERIFY_FAIL.toException(e.getMessage(), e);
        }
    }
}