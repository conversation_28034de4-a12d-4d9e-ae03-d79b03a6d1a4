package kl.npki.base.core.common.date;

import com.fasterxml.jackson.annotation.JacksonAnnotation;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 自定义日期格式
 *
 * <AUTHOR>
 * @since 2025/2/25 14:15
 */
@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotation
public @interface CustomLocalDateFormat {

    /**
     * 日期格式字符串
     *
     * @return String
     */
    String value();


}