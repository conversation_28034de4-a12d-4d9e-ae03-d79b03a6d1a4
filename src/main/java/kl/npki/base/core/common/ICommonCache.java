package kl.npki.base.core.common;

import kl.nbase.cache.client.ICacheClient;
import kl.nbase.cache.key.CacheKey;
import kl.nbase.cache.key.ICacheKey;
import kl.npki.base.core.tenantholders.CacheClientHolder;
import kl.npki.base.core.tenantholders.TenantContextHolder;

/**
 * @Author: guoq
 * @Date: 2022/10/13
 * @description: 缓存通用接口
 */
public interface ICommonCache {

    /**
     * 获取缓存ID
     *
     * @return
     */
    String getId();

    /**
     * 包装缓存key
     *
     * @param key
     * @return
     */
    default ICacheKey wrapCacheKey(Object key) {
        return new CacheKey(String.valueOf(key), getId(), TenantContextHolder.getTenantId());
    }

    /**
     * 包装缓存key
     *
     * @param key
     * @param tenantId
     * @return
     */
    default ICacheKey wrapCacheKey(Object key, String tenantId) {
        return new CacheKey(String.valueOf(key), getId(), tenantId);
    }

    /**
     * 获取cacheClient
     *
     * @return
     */
    default ICacheClient getClient() {
        return CacheClientHolder.get();
    }

    /**
     * 获取指定环境的cacheClient
     *
     * @param tenantId
     * @return
     */
    default ICacheClient getClient(String tenantId) {
        return CacheClientHolder.get(tenantId);
    }
}
