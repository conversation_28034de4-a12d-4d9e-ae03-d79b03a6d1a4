package kl.npki.base.core.common.date;

import kl.nbase.helper.utils.DateUtils;
import kl.npki.base.core.utils.DateUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 自定义格式化日期注册类
 *
 * <AUTHOR>
 * @since 2025/2/25 14:21
 */
public class CustomLocalDateFormatRegistry {

    private CustomLocalDateFormatRegistry() {
    }

    private static final Map<String, String> FORMAT_MAP = new HashMap<>(8);

    static {
        // 对应 DateUtil.dayPattern
        FORMAT_MAP.put(DateUtils.DAY_PATTERN, DateUtil.dayPattern);
        // 对应 DateUtil.dateTimePattern
        FORMAT_MAP.put(DateUtils.DATETIME_PATTERN, DateUtil.dateTimePattern);
        // 对应 DateUtil.dateTimeMsPattern，未取到key值的时候，使用此key值
        FORMAT_MAP.put(DateUtils.DATETIME_MS_PATTERN, DateUtil.dateTimeMsPattern);

    }

    /**
     * 重新赋值。比如 DateUtils.DAY_PATTERN 对应 DateUtil.dayPattern
     *
     * @param format  格式日期key
     * @param pattern 格式日期value
     */
    public static void registerFormat(String format, String pattern) {
        FORMAT_MAP.put(format, pattern);
    }

    public static String getPatternByFormat(String format) {
        // 不在范围之内取自定义格式化日期
        return FORMAT_MAP.getOrDefault(format, format);
    }

}

