package kl.npki.base.core.common.manager;

import kl.npki.base.core.biz.cert.service.CaCertMgr;
import kl.npki.base.core.biz.cert.service.IdCertMgr;
import kl.npki.base.core.biz.cert.service.ManageCertMgr;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.tenantholders.common.CommonTenantHolder;
import kl.npki.base.core.tenantholders.common.ICommonTenant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Date 2024/1/31 10:51
 * @Description:
 */
public class MgrHolder {
    protected MgrHolder() {
        throw new IllegalStateException("Utility class");
    }
    /**
     * logger
     **/
    private static final Logger logger = LoggerFactory.getLogger(MgrHolder.class);


    public static ManageCertMgr getManageCertMgr() {
        return getCommonTenantObj(ManageCertMgr.class);
    }

    public static IdCertMgr getIdCertMgr() {
        return getCommonTenantObj(IdCertMgr.class);
    }

    public static CaCertMgr getCaCertMgr() {
        return getCommonTenantObj(CaCertMgr.class);
    }

    public static <T extends ICommonTenant> T getCommonTenantObj(Class<T> clazz) {
        ICommonTenant commonTenantObj = CommonTenantHolder.get().get(clazz.getName());
        if (commonTenantObj == null) {
            try {
                commonTenantObj = clazz.getDeclaredConstructor().newInstance();
                reload(clazz, commonTenantObj);
                CommonTenantHolder.get().put(clazz.getName(), commonTenantObj);
            } catch (Exception e) {
                throw BaseInternalError.CLASS_INIT_METHOD_EMPTY.toException(clazz.getName());
            }
        }else {
            if(!commonTenantObj.alreadyLoaded()){
                reload(clazz, commonTenantObj);
            }
        }
        return (T) commonTenantObj;
    }

    protected static void reload(Class<? extends ICommonTenant> clazz, ICommonTenant commonTenantObj) {
        try {
            commonTenantObj.reload();
        } catch (Exception e) {
            logger.error("Failed to reload {}", clazz.getName(), e);
        }
    }
}
