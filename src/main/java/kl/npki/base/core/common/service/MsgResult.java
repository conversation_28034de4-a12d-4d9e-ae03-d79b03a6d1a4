package kl.npki.base.core.common.service;

import java.io.Serializable;

/**
 * @Author: guoq
 * @Date: 2023/12/26
 * @description: tcp服务通用响应
 */
public class MsgResult<T> implements Serializable {
    private static final long serialVersionUID = 3956566557329773286L;

    /**
     * 调用者id
     */
    private String callerId;

    /**
     * 调用者名称
     */
    private String callerName;

    /**
     * 业务中用户id
     */
    private String userId;

    /**
     * 业务中证书id
     */
    private String certId;

    private String biz;

    private String bizId;

    private boolean isSuccessful;

    private String errorMessage;

    private T result;

    public MsgResult() {
        this.isSuccessful = true;
    }
    public MsgResult(String biz) {
        this();
        this.biz = biz;
    }

    public String getCallerId() {
        return callerId;
    }

    public void setCallerId(String callerId) {
        this.callerId = callerId;
    }

    public String getCallerName() {
        return callerName;
    }

    public void setCallerName(String callerName) {
        this.callerName = callerName;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCertId() {
        return certId;
    }

    public void setCertId(String certId) {
        this.certId = certId;
    }

    public String getBiz() {
        return biz;
    }

    public void setBiz(String biz) {
        this.biz = biz;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public boolean isSuccessful() {
        return isSuccessful;
    }

    public void setSuccessful(boolean successful) {
        isSuccessful = successful;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        this.isSuccessful = false;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }
}
