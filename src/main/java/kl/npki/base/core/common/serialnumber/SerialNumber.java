package kl.npki.base.core.common.serialnumber;

import kl.npki.base.core.utils.ArraysEx;

import java.io.Serializable;
import java.math.BigInteger;
import java.security.SecureRandom;

/**
 * 证书序列号设计,使用一个long来表达，一共8个字节(64bit),表达如下：<br>
 * 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 <br>
 * ----------------- -------- -------- ------------------------------------ <br>
 * Seed_____________ Route___ Reserved Index______________________________ <br>
 * <p>
 * Seed 长度16bit，在CA序列号中表示在上级体系中顺序号，在用户序列号中代表该证书所属的CA<br>
 * Route 长度 8bit, 在CA序列号中默认为0,在用户序列号中表示多路CA集群体系中本单元的标识<br>
 * Reserved 长度 8bit, 保留，默认0
 * Index 长度 32bit, 在CA序列号中默认为随机数，在用户序列号中表示所属CA签发证书的唯一递增标识<br>
 * <p>
 * <p>
 * Seed 长度16bit，它的最大表达数可以达到65535，代表了系统中默认CA的识别序号。<br>
 * 一般部委单位最多签发32个下级，但如果是国密局的根可能签发32个省的CA，另外每 <br>
 * 几年证书都会进行密钥更新，因此会引起序号数量可能较多，因此使用16bit表达。 <br>
 * 根系统的第一个种子值为(当年年份-2000)*100+当前月份，如果是2010年4月，则根 <br>
 * CA的种子就是1004，后面每生成一个CA或签发一个CA，种子依次递增。<br>
 * <p>
 * 在我司自己的根系统中，CA证书的序列号为(16bit种子+8bit1+8bit0+32bit随机数)<br>
 * 二级CA种子配置在CA证书导回到系统中时设定。<br>
 * <p>
 * 对于二级CA签发三级CA的情况，种子也按照依次递增方案即可，<br>
 * 即使某个二级CA中有CA的种子与其它一个二级CA下属的三级CA种子数相同，<br>
 * 首先后面40bit随机数保证了它们的序列号不同，<br>
 * 其次对于出现序列号相同的用户证书而言，由于是在不同的CA证书体系，因此不会冲突。<br>
 * 一般情况下都是只有二级CA，也不需要考虑这种冲突情况。 <br>
 * 如果根由国密局签发，二级CA的种子取用其最开始的两个字节。<br>
 * 由于CA证书可能自己签发或由国密局签发，因此序列号的长度不能规定，<br>
 * 但它至少为8个octetbytes，因此我们可以取用最前两个octet bytes(16bit)作为CA种子<br>
 * 若由国密局签发，该种子应该应用在各个区域CA，因此重复也没关系
 * <p>
 * 对于CA证书更新，种子数变化由根CA决定，我司产品为顺序递增，国密局签发，
 * 二级CA的种子取用其最开始的两个字节(16bit)，
 * 为了避免在同一个CA体系中重复，比如同一个省级CA在国密局两次更新得到的序列号
 * 前面两个字节是一样的，当重复发生时，后面一个CA在发生重复的系统中种子数为得
 * 到的序列号的前两个字节顺序加一，也就是在其它方根CA签发的情况下，CA的种子不
 * 能够直接从它的序列号中获得。
 * <p>
 * 证书签发成功，该种子会属性一部分与序列号一起保存在数据库中，在证书加载时得到。 <br>
 * <p>
 * Route 8bit,并连数，默认为00,当CA为双路，三路或四路为01,10,11，最多256路，从配置中读取
 * Index 隶属某CA种子各某路的下的顺序数，最大表示为2的40次方
 * <p>
 * <p>
 * modify 2010-10-14，福建CA建设时，用户要求一定要16个字节的证书序列号，为了<br>
 * 兼容从前的修改,特提出一个序列号偏移量的配置，在SnGenCfg中的snBytesLenOffset<br>
 * 中配置，改造后的序列号生成后转换为大数时，首先按照原规则生成序列号，然后根据<br>
 * snBytesLenOffset中定义的偏移量进行偏移，最终结果形如：<br>
 * 03 EC 00 00 00 00 10 A1 00 00 00 00 00 00 00 00 <br>
 * 后面的8个字节 00 00 00 00 00 00 00 00 是偏移后生成的<br>
 * 当偏移量设置为0时，仍按照原规则处理
 * <p>
 * modify 2019-03-21 yaodeyi
 * 目前证书有了大容量的需求，index4个字节的大小已经不能满足需求，因此将序列号长度由8个字节扩充到12个字节<br>
 * 前四个字节不变，index长度由4个字节增加到8个字节，同时保留增加padding的功能。<br>
 * 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 <br>
 * 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000 <br>
 * ----------------- -------- -------- ------------------------------------ <br>
 * Seed_____________ Route___ Reserved Index______________________________ <br>
 * Index______________________________ <br>
 *
 * <AUTHOR>
 */
public class SerialNumber implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * SN总长度(字节)
     */
    private static final short SN_BYTES = 12;

    /**
     * 种子值长度
     */
    private static final short SEED_BITS = 16;

    /**
     * 分路值长度
     */
    private static final short ROUTE_BITS = 8;

    /**
     * 保留项长度
     */
    private static final short RESEVERED_BITS = 8;

    /**
     * 索引ID长度
     */
    private static final short INDEX_BITS = 64;

    /**
     * SEED计算位移数
     */
    private static final short SEED_SHIFT_BIT = 80;

    /**
     * Route计算位移数
     */
    private static final short ROUTE_SHIFT_BIT = 72;

    /**
     * RESERVED计算位移数
     */
    private static final short RESEVERED_SHIFT_BIT = 64;

    private static final int HEX_RADIUS = 16;
    private static final SecureRandom RAND = new SecureRandom();
    /**
     * 种子值，和签发者CA相同
     */
    private short seed;
    /**
     * 分路号
     */
    private byte route;
    /**
     * 随机数
     */
    private char reserved = 0;
    /**
     * 递增ID
     */
    private long index;
    /**
     * 随机填充
     */
    private BigInteger padding = BigInteger.ZERO;

    private int paddingBits;

    private BigInteger sn;

    public SerialNumber(short seed, byte route, long index, int paddingBits) {
        this(seed, route, (char) 0, index, paddingBits);
        reserved = nextRandByte();
    }

    public SerialNumber(short seed, byte route, char reserved, long index, int paddingBits) {
        this.seed = seed;
        this.route = route;
        this.reserved = reserved;
        this.index = index;

        if (paddingBits > 0) {
            this.padding = BigInteger.probablePrime(paddingBits, RAND);
        }
        this.sn = this.generateSerialNumber();
    }

    public SerialNumber(BigInteger sn) {
        if (sn.toByteArray().length >= SerialNumber.SN_BYTES) {
            // 格尔自定义SN
            this.parse(sn);
        } else {
            // 从外面导入的CA证书
            this.seed = sn.shiftRight(sn.bitLength() - 16).shortValue();
            this.route = (byte) 0xFF;
            this.reserved = (byte) 0;
            this.index = 0;
            this.padding = BigInteger.ZERO;
        }

        this.sn = sn;
    }

    public SerialNumber(String hexSn) {
        this(new BigInteger(hexSn, HEX_RADIUS));
    }

    private char nextRandByte() {
        return (char) (RAND.nextInt(256));
    }

    /**
     * 解析已处理完偏移的序列号
     *
     * @param sn
     */
    private void parse(BigInteger sn) {

        /**
         * 格式形如: SEED(16bit) | Route(8bit) | Reserved(8bit) | INDEX(32bit) | PADDING
         */
        if (sn.toByteArray().length > SN_BYTES) {
            // 该SN的大于格尔自定义SN约定长度，之后的值视为Padding
            byte[] snBytes = sn.toByteArray();
            int paddingLen = snBytes.length - SN_BYTES;
            this.padding = new BigInteger(1, ArraysEx.copyOfRange(snBytes, SN_BYTES, paddingLen));

            sn = sn.shiftRight(paddingBits);
        }

        this.seed = sn.shiftRight(SEED_SHIFT_BIT).shortValue();
        this.route = sn.shiftRight(ROUTE_SHIFT_BIT).byteValue();
        this.reserved = (char) sn.shiftRight(RESEVERED_SHIFT_BIT).byteValue();
        this.index = sn.intValue();
    }

    private BigInteger generateSerialNumber() {

        BigInteger serialNumber = BigInteger.ZERO;

        serialNumber = serialNumber.shiftLeft(SEED_BITS);
        serialNumber = serialNumber.add(BigInteger.valueOf(seed));

        serialNumber = serialNumber.shiftLeft(ROUTE_BITS);
        serialNumber = serialNumber.add(BigInteger.valueOf(route));

        serialNumber = serialNumber.shiftLeft(RESEVERED_BITS);
        serialNumber = serialNumber.add(BigInteger.valueOf(reserved));

        serialNumber = serialNumber.shiftLeft(INDEX_BITS);
        serialNumber = serialNumber.add(BigInteger.valueOf(index));

        if (paddingBits > 0) {
            serialNumber = serialNumber.shiftLeft(paddingBits);
            serialNumber = serialNumber.add(padding);
        }

        return serialNumber;
    }

    public BigInteger toBigInteger() {
        return this.sn;
    }

    public String toHexString() {
        return toBigInteger().toString(HEX_RADIUS);
    }

    public short getSeed() {
        return seed;
    }

    public byte getRoute() {
        return route;
    }

    public long getIndex() {
        return index;
    }

}
