package kl.npki.base.core.common.date;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Objects;

/**
 * * <AUTHOR>
 * * @since 2025/2/17 10:53
 * 自定义日期时间反序列化类
 * 适用于 LocalDateTime类型，接受前端字段可以【动态反序列化】 字符串转对象
 * 1、使用默认反序列化方法
 *
 * @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
 * <p>
 * 2、自定义日期格式化使用方法
 * @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
 * @CustomLocalDateFormat(DateUtils.DATETIME_PATTERN)
 */
public class CustomLocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> implements ContextualDeserializer {


    private String pattern;


    private static final Logger logger = LoggerFactory.getLogger(CustomLocalDateTimeDeserializer.class);

    public CustomLocalDateTimeDeserializer(String pattern) {
        this.pattern = pattern;
    }

    public CustomLocalDateTimeDeserializer() {
    }

    @Override
    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) {
        try {
            String dateTimeStr = p.getText();
            // 增加判空判断
            if (StringUtils.isBlank(dateTimeStr)) {
                return null;
            }
            DateTimeFormatter formatter;
            if (StringUtils.isBlank(pattern)) {
                formatter = DateTimeFormatter.ofPattern(DateUtil.dateTimePattern).withZone(ZoneId.systemDefault());
            } else {
                // 动态获取自定义格式
                formatter = DateTimeFormatter.ofPattern(pattern).withZone(ZoneId.systemDefault());
            }
            return LocalDateTime.parse(dateTimeStr, formatter);

        } catch (Exception e) {
            logger.error("LocalDateTime Deserializer Error, Data:[{}], Pattern:[{}]", p, DateUtil.dateTimePattern);
            throw BaseInternalError.DATE_PARSE_ERROR.toException(e);
        }

    }

    /**
     * 判断日期时间是否能转换
     *
     * @param dateTimeStr 日期内容
     * @param format      日期格式化
     * @return boolean
     * 例如: 2025-02-17 16:38:35 年MM-dd yyyy HH:mm:ss, 匹配结果失败
     */
    public static boolean isDateTimeStringValid(String dateTimeStr, String format) {
        if (StringUtils.isBlank(format)) {
            return false;
        }
        try {
            LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern(format).withZone(ZoneId.systemDefault()));
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }


    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext ctxt, BeanProperty property) throws JsonMappingException {
        if (Objects.nonNull(property)) {
            // 查找我们的自定义注解
            CustomLocalDateFormat annotation = property.getAnnotation(CustomLocalDateFormat.class);
            if (annotation != null) {
                // 自定义日期格式注解
                pattern = annotation.value();
                return new CustomLocalDateTimeDeserializer(pattern);
            }
            // 默认格式
            return new CustomLocalDateTimeDeserializer();
        }
        return this;
    }


}
