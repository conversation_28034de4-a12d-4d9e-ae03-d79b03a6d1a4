package kl.npki.base.core.common.crypto;

import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.security.asn1.ASN1BitString;
import kl.nbase.security.asn1.ASN1ObjectIdentifier;
import kl.nbase.security.asn1.pkcs.CertificationRequest;
import kl.nbase.security.asn1.x509.AlgorithmIdentifier;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.asn1.x509.SubjectPublicKeyInfo;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.nbase.security.pkcs.PKCS10CertificationRequest;
import kl.nbase.security.utils.AsymKeyUtil;
import kl.nbase.security.utils.SignatureAlgoUtil;
import kl.npki.base.core.common.manager.MgrHolder;
import kl.npki.base.core.exception.BaseInternalError;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.base.core.utils.KeyUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.io.IOException;
import java.security.KeyPair;
import java.security.PublicKey;

import static kl.npki.base.core.constant.I18nExceptionInfoConstant.SIGNATURE_IS_NULL_I18N_KEY;
import static kl.npki.base.core.constant.I18nExceptionInfoConstant.SIGNATURE_VERIFICATION_FAILED_I18N_KEY;
/**
 * <AUTHOR>
 * @date 2022/10/28 18:46
 * @Description: 签名验签帮助类
 */
public class SignatureHelper {
    private SignatureHelper() {
        throw new IllegalStateException("Utility class");
    }

    public static boolean verifyCSR(PKCS10CertificationRequest csr) {
        CertificationRequest certificationRequest = csr.toASN1Structure();
        ASN1BitString signature = certificationRequest.getSignature();
        if (signature == null || signature.getOctets().length == 0) {
            throw  BaseInternalError.SIGNED_VERIFY_ERROR.toException(SIGNATURE_IS_NULL_I18N_KEY);
        }
        // 从CSR中获取公钥
        SubjectPublicKeyInfo publicKeyInfo = csr.getSubjectPublicKeyInfo();
        PublicKey publicKey;
        byte[] tobeSigned;
        try {
            // 签名公钥
            publicKey = AsymKeyUtil.x509Bytes2PublicKey(publicKeyInfo);
            // 签名原文
            tobeSigned = certificationRequest.getCertificationRequestInfo().getEncoded();
        } catch (IOException e) {
            throw BaseInternalError.SIGNED_VERIFY_ERROR.toException(e);
        }

        // 解析摘要算法
        AlgorithmIdentifier signatureAlgorithm = csr.getSignatureAlgorithm();
        HashAlgo hashAlgo = SignatureAlgoUtil.getHashAlgoByOid(signatureAlgorithm.getAlgorithm());

        return EngineHolder.get().verify(publicKey, tobeSigned, signature.getOctets(), hashAlgo, null);
    }

    /**
     * 验证签名。
     *
     * @param cert       证书
     * @param signature  签名值
     * @param toBeSigned 待签名数据
     * @return 签名验证结果
     */
    public static boolean verify(Certificate cert, byte[] signature, byte[] toBeSigned) {
        return verify(cert, signature, toBeSigned, cert.getSignatureAlgorithm().getAlgorithm());
    }

    /**
     * 验证签名。
     *
     * @param cert            证书
     * @param signature       签名值
     * @param toBeSigned      待签名数据
     * @param digestAlgorithm 摘要算法 OID
     * @return 签名验证结果
     */
    public static boolean verify(Certificate cert, byte[] signature, byte[] toBeSigned,
                                 ASN1ObjectIdentifier digestAlgorithm) {
        // 获取摘要算法
        HashAlgo hashAlgo = SignatureAlgoUtil.getHashAlgoByOid(digestAlgorithm);
        if(ObjectUtils.isEmpty(hashAlgo)){
            hashAlgo = HashAlgo.newInstanceByOid(digestAlgorithm);
        }
        return verify(cert, signature, toBeSigned, hashAlgo);
    }

    /**
     * 验证签名。
     *
     * @param cert       证书
     * @param signature  签名值
     * @param toBeSigned 待签名数据
     * @param hashAlgo   摘要算法
     * @return 签名验证结果
     */
    public static boolean verify(Certificate cert, byte[] signature, byte[] toBeSigned,
                                 HashAlgo hashAlgo) {

        boolean verified = false;
        try {
            //根据身份证书算法获取加密机验签
            PublicKey publicKey = AsymKeyUtil.x509Bytes2PublicKey(cert.getSubjectPublicKeyInfo());
            verified = EngineHolder.get().verify(publicKey,
                    toBeSigned,
                    signature,
                    hashAlgo,
                    null);
        } catch (Exception e) {
            throw BaseInternalError.SIGNED_VERIFY_ERROR.toException(SIGNATURE_VERIFICATION_FAILED_I18N_KEY, e);
        }
        return verified;
    }

    public static String signToB64(byte[] toBeSigned) {
        byte[] signature = sign(toBeSigned);
        return Base64Util.base64Encode(signature);
    }


    public static byte[] sign(byte[] toBeSigned) {

        try {
            ClusterEngine engine = EngineHolder.get();
            Certificate cert = MgrHolder.getIdCertMgr().getIdCert();
            HashAlgo hashAlgo = SignatureAlgoUtil.getHashAlgoByOid(cert.getSignatureAlgorithm().getAlgorithm());
            // 获取身份证书私钥签名
            PublicKey publicKey =
                KeyUtils.getPublicKey(cert.getSubjectPublicKeyInfo());
            KeyPair keyPair = engine.getKeyPair(publicKey);
            return engine.sign(keyPair.getPrivate(), toBeSigned, hashAlgo, null);
        } catch (Exception e) {
            throw BaseInternalError.SIGN_ERROR.toException(e);
        }

    }

}
