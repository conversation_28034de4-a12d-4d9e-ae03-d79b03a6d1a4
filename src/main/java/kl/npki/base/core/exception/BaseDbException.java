package kl.npki.base.core.exception;

import kl.nbase.exception.InternalException;

import static kl.npki.base.core.exception.BaseDbError.DB_QUERY_TIME_OUT;

/**
 * 数据库相关异常
 *
 * <AUTHOR>
 */
public class BaseDbException extends InternalException {

    public BaseDbException(BaseDbError error, String appendMessage, Throwable throwable, Object... args) {
        super(error, appendMessage, throwable, args);
    }

    public boolean isQueryTimeOutException() {
        return this.getSecondCode().equals(DB_QUERY_TIME_OUT.getSecondCode());
    }
}
