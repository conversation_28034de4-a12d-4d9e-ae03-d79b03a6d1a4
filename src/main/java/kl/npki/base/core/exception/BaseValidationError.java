package kl.npki.base.core.exception;

import kl.nbase.exception.interfaces.IValidationError;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;

/**
 * @Author: guoq
 * @Date: 2022/8/23
 * @description: 校验错误码
 */
public enum BaseValidationError implements IValidationError, ErrorCode {
    /**
     * 证书类异常
     */
    CERT_NOT_YET_VALID_ERROR("102", "证书未生效"),
    CERT_EXPIRED_ERROR("103", "证书已经过期"),

    LICENSE_IS_EMPTY("110", "License为空!"),
    LICENSE_CONTENT_ERROR("111", "License内容错误"),
    LICENSE_VERIFY_ERROR("112", "License验证失败"),
    VALIDITY_GT_ISSUE_CERT("113", "证书有效期超过签发者证书"),
    ISSUER_AUTHORITY_KEY_ID_IS_NULL("114", "颁发者授权密钥为空"),
    SUBJECT_KEY_ID_IS_NULL("123", "使用者授权密钥为空"),
    MGR_ROOT_KEY_ALGORITHM_MISMATCH("115", "请求的密钥算法与管理根的密钥算法不匹配，请重新生成证书请求，确保使用与管理根相同的密钥算法"),
    NOT_TRUST_CERT_ERROR("116", "证书不被信任"),
    TRUST_CERT_IS_EMPTY("117", "信任证书为空"),
    EXPORT_CERT_ERROR("118", "导出证书失败"),
    PQ_ENC_KEY_TYPE_IS_NULL("119", "抗量子加密证书密钥类型为空"),
    PQ_ENC_KEY_TYPE_PURPOSE_ERROR("120", "抗量子加密证书密钥使用类型需要为【加密】"),
    SANS_PARSING_ERROR_FROM_EXT("121", "从证书扩展项中读取SAN列表失败"),
    CERT_PARSE_FAILED("122", "证书解析失败"),
    EXPORT_CERT_TYPE_ERROR("123", "不支持的证书导出类型"),
    CERT_REQUEST_SIGN_ERROR("124", "证书请求签名验证失败"),
    PRI_KEY_PARSE_FAILED("125", "私钥解析失败"),
    /**
     * 参数类异常
     */
    ALGORITHM_NOT_SUPPORTED("201", "不支持的算法类型"),
    INCORRECT_RECIPIENT_INFO_NUMBER("202", "至少需要有一个 RecipientInfo"),
    ENCRYPTED_CONTENT_INFO_IS_NULL("203", "EncryptedContentInfo 为空"),
    INCORRECT_SIGNER_INFO_NUMBER("204", "至少需要有一个 SignerInfo"),
    SELF_CHECK_NOT_SUPPORTED("205", "不支持的自检类型"),
    USER_STATUS_ERROR("206", "用户状态错误"),
    USER_CERT_STATUS_ERROR("207", "用户证书状态错误"),
    FILE_CONFIG_ENTITY_PARAMS_IS_INVALID("208", "文件配置实体参数不合法"),
    PARAM_ERROR("209", "参数错误"),
    UNSUPPORTED_CHARSET("210","不支持的字符集"),
    ENUM_VALIDATOR_DEFINITION_ERROR("211", "枚举校验器定义错误"),
    /**
     * 数据完整性异常
     */
    INTEGRITY_VERIFICATION_FAILED("301", "数据完整性验证失败，可能存在篡改"),
    GENERAL_VERIFICATION_ERROR("302", "数据完整性验证过程中发生错误"),

    /**
     * 文件上传
     */
    UPLOAD_FILE_FORMAT_ERROR("400", "上传文件格式与接口不匹配"),
    UPLOAD_FILE_HEADER_IS_INCORRECT("401", "上传文件的列头格式不匹配"),
    UPLOAD_FILE_CHECK_DATA_EMPTY_ERROR("402", "上传文件检查模板表头为空"),
    UPLOAD_FILE_SEARCH_STATUS_ERROR("403", "文件内容正在处理无法查询处理结果"),
    UPLOAD_FILE_NOT_FOUND_ERROR("404", "上传文件不存在"),

    /**
     * 组织机构
     */
    ORG_NOT_FOUND("500", "组织机构不存在"),
    ORG_PARENT_NOT_FOUND("501", "父机构不存在"),
    ORG_ADD_EQ_ERROR("502", "子机构编码不能与父机构编码相同"),
    ORG_BATCH_DELETE_NOT_SELECTED_ALL_SUB_ORG_ERROR("503", "删除该机构时，未选中所有子机构，无法删除该机构"),
    ORG_DELETE_ERROR("504", "机构删除失败"),
    ORG_NAME_NULL_ERROR("505", "机构名称不能为空"),
    ORG_CODE_NULL_ERROR("506", "机构编码不能为空"),
    ORG_FULL_CODE_NULL_ERROR("507", "完整机构编码不能为空"),
    ORG_STATUS_ERROR("508", "机构状态不正确"),
    ORG_CODE_REPEAT_ERROR("509", "机构编码重复"),

    /**
     * 登录认证相关异常
     */
    LOGIN_TYPE_NULL_ERROR("600", "登录认证类型为空"),
    LOGIN_TYPE_NOT_SUPPORT_ERROR("601", "不支持的登录认证类型"),

    ;
    private final String code;
    private final String desc;

    BaseValidationError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }

}
