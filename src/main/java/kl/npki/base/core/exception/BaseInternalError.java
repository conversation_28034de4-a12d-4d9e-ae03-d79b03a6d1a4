package kl.npki.base.core.exception;

import kl.nbase.exception.interfaces.IInternalError;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;
/**
 * @Author: guoq
 * @Date: 2022/8/23
 * @description: 内部错误码
 */
public enum BaseInternalError implements IInternalError, ErrorCode {

    /**
     * 01*~05*：证书
     */
    PUBLIC_KEY_CONVERT_ERROR("010", "证书公钥转换失败！"),
    ASYM_ALGO_CONVERT_ERROR("011", "公钥非对称算法转换失败！"),
    SIGNING_CERT_DATA_ERROR("012", "生成证书签名错误"),
    CERT_ISSUE_ERROR("013", "签发证书错误"),
    ROOT_CERT_ISSUER_ERROR("014", "签发管理根错误"),
    CERT_REQ_GEN_ERROR("015", "证书请求生成失败"),
    ROOT_CERT_ALREADY_EXIST("016", "根证书已存在"),
    GEN_PKCS10_REQUEST_ERROR("017", "生成p10请求失败"),
    DN_BUILD_ERROR("018", "DN构造失败"),
    ID_CERT_ALREADY_EXIST("019", "身份证书已存在"),
    CERT_ENCODE_ERROR("020", "证书ASN结构编码失败"),
    CERT_DECODE_ERROR("021", "证书ASN结构解码失败"),
    ROOT_NOT_EXIST("022", "管理根证书不存在"),
    PKCS10_REQUEST_ENCODE_ERROR("023", "p10请求编码失败"),
    CERT_ALREADY_EXIST("024", "证书已存在"),
    CERT_PUB_KEY_NOT_MATCHED("025", "公钥信息不匹配"),
    CERT_REQ_NOT_GEN("026", "证书请求未生成"),
    CERT_PUBLICKEY_ECODE_ERROR("027","证书公钥encode失败"),
    KEYSTORE_ALREADY_EXISTS("028", "Keystore文件已存在"),
    GENERATE_KEYSTORE_ERROR("029", "生成Keystore文件失败"),
    NOT_FOUND_CERT_FROM_KEYSTORE("030", "从keystore文件中未找到目标证书"),
    FOUND_CERT_FROM_KEYSTORE_FAILED("031", "从keystore文件中查找证书出现异常"),
    EXPORT_SSL_SITE_CERT_ERROR("032", "导出SSL站点证书失败"),
    ROOT_CERT_INIT_GET_LOCK_ERROR("033", "管理根初始化获取锁失败"),
    TRUST_CERT_NOT_EXIST_ERROR("034", "删除证书失败，信任证书不存在"),
    SSL_CERT_INIT_GET_LOCK_ERROR("035", "SSL证书初始化获取锁失败"),
    ID_CERT_INIT_GET_LOCK_ERROR("036", "身份证书初始化获取锁失败"),
    LOCAL_TRUST_CERT_DELETER_ERROR("037", "管理根证书不容许删除"),
    CERT_REQUEST_PARSING_ERROR("038", "证书请求解析失败"),
    ALGORITHM_IDENTIFIER_ENCODE_ERROR("039", "算法标识符编码失败"),
    TRUST_CERT_NOT_FOUND("040", "信任证书未找到"),
    CERT_P7B_ERROR("041", "生成P7B证书链异常"),
    CRL_PARSE_ERROR("042", "CRL解析失败"),
    MICROSOFT_CERT_TEMPLATE_PARSE_ERROR("043", "微软证书模版解析失败"),
    IMPORT_CERT_CHAIN_ERROR("043", "导入证书链异常"),
    CERT_PARSE_FAILED("044", "证书解析失败"),

    /**
     * 06*：主密钥功能
     */
    MAINKEY_IMPORT_ERROR("061", "主密钥导入失败"),
    MAINKEY_EXPORT_ERROR("062", "主密钥导出失败"),
    MAINKEY_NOT_FOUND_ERROR("063", "对应ID的主密钥未找到"),
    MAINKEY_CONFIG_NULL_ERROR("064", "主密钥id配置为空"),
    MAINKEY_INIT_ERROR("065", "主密钥缓存初始化失败"),
    MAINKEY_INIT_GET_LOCK_ERROR("066", "主密钥初始化获取锁失败"),

    /**
     * 配置文件加密异常相关，这里不要抛出详细详细，防止被反向编译破解
     */
    CONFIG_ENCRYPTION_KEY_FILE_NOT_FOUND_ERROR("080", "加密环境初始化失败"),
    CONFIG_ENCRYPTION_KEY_FILE_LOAD_ERROR("081", "加密环境初始化失败"),

    /**
     * 1**：加密机 密码服务
     */
    KEY_PAIR_GET_ERROR("101", "获取密钥对错误"),
    SIGN_ALG_GET_ERROR("102", "获取签名算法错误"),
    KEYTYPE_NOT_SUPPORT_ERROR("103", "密码服务不支持该密钥类型"),
    ENGINE_SERVICE_INIT_ERROR("104", "密码服务初始化失败"),
    ENGINE_SERVICE_NOT_FIND_ERROR("105", "加密服务加载失败"),
    ALGORITHM_NOT_SUPPORT_ERROR("106", "密码服务不支持该算法"),
    ASYMMETRIC_ENCRYPT_ERROR("107", "非对称加密失败"),
    ASYMMETRIC_DECRYPT_ERROR("108", "非对称解密失败"),
    SYMMETRIC_ENCRYPT_ERROR("109", "对称加密失败"),
    SYMMETRIC_DECRYPT_ERROR("110", "对称解密失败"),
    ENGINE_INIT_ERROR("111", "加密机初始化失败"),
    DATA_PADDING_ERROR("112", "数据填充失败"),
    DATA_UNPADDED_ERROR("113", "数据去填充失败"),
    ENGINE_CLUSTER_ALREADY_EXIST("114", "该集群名已存在"),
    ENGINE_CLUSTER_NOT_FOUND("115", "该集群名对应的加密机集群不存在"),
    SIGNED_VERIFY_ERROR("116", "签名验签不通过"),
    SIGN_ERROR("117", "签名失败！"),
    ENGINE_LB_CONFIG_ERROR("118", "密码设备负载策略配置有误"),
    ASYMMETRIC_CONFIG_ERROR("120", "对称算法已配置无法变更，如需调整请重新部署系统"),

    /**
     * License
     */
    LICENSE_VERIFY_ERROR("221", "License校验失败"),
    LICENSE_IMPORT_ERROR("222", "License导入失败"),
    LICENSE_IS_NULL("223", "License为空"),
    DATE_PARSE_ERROR("224", "日期转换失败"),
    LONG_PARSE_ERROR("225", "Long类型转换失败"),

    LOGIN_TYPE_CONVERSION_ERROR("230","登录类型转换错误！"),

    GET_PUBLIC_KEY_ERROR("301", "获取公钥失败"),
    GET_PRIVATE_KEY_ERROR("302", "获取私钥失败"),
    BUILD_SM2_CIPHER_BLOB_ERROR("303", "构建SM2密文失败"),

    /**
     * 文件相关
     */
    FILE_CREATE_FAIL("401", "文件创建失败"),
    FILE_NO_FOUND_FAIL("402", "文件不存在"),
    FILE_SIGNATURE_FAIL("404", "Excel文件生成签名失败"),
    FILE_VERIFY_FAIL("405", "Excel文件签名验证失败"),
    FILE_PALINTEXT_FAIL("406", "Excel签名原文生成失败"),
    FILE_UPDATE_SIGNATURE_FAIL("407", "Excel文件更新签名值失败"),
    FILE_WRITE_FAIL("408", "文件写入失败"),
    FILE_READ_ERROR("409", "读取文件失败"),

    /**
     * 其他
     */
    GEN_FULL_DATA_ORI_DATA_ERROR("501", "生成完整性原文值失败"),
    CLASS_INIT_METHOD_EMPTY("502", "缺少默认的类初始化方法"),
    LAMBDA_COLUMN_NAME_ERROR("503", "字段名解析异常"),
    SYSTEM_LANGUAGE_ERROR("504", "系统语言配置错误"),
    UNSUPPORTED_LANGUAGE_ERROR("505", "不支持的语言"),
    SOCKET_CONNECTION_FAILED("506", "Socket连接失败"),
    SOCKET_STOP_FAILED("507", "Socket关闭失败"),
    AUDIT_SWITCH_UNKNOWN_PROTOCOL_ERROR("508", "未知的协议类型"),
    AUDIT_SWITCH_APPENDER_NOT_FOUND_ERROR("509", "当前不存在SyslogAppender"),
    AUDIT_IP_REACH_FAILED("510", "审计服务器IP不可用"),
    AUDIT_CONNECTION_FAILED("511", "审计服务连接失败"),

    /**
     * 组织机构
     */
    ORG_BATCH_IMPORT_EXCEL_GEN_ERROR("600", "机构导入结果生成失败"),
    ORG_BATCH_IMPORT_FILE_OPERATE_ERROR("601", "机构批量导入时结果文件操作错误"),
    ORG_DELETE_BINDING_ERROR("602",  "机构存在绑定关系，无法删除"),
    ORG_NOT_EXIST_ERROR("603", "组织机构不存在")
    ;

    private final String code;
    private final String desc;

    BaseInternalError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }


    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }

}
