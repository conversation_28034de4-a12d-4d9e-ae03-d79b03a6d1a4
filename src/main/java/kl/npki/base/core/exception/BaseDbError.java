package kl.npki.base.core.exception;

import kl.nbase.exception.interfaces.IInternalError;

import static kl.npki.base.core.constant.I18nConstant.BASE_CORE_I18N_MESSAGE_KEY_PREFIX;
/**
 * 数据库相关错误码
 * <AUTHOR>
 */
public enum BaseDbError implements IInternalError, ErrorCode {


    DB_QUERY_TIME_OUT("001", "数据库查询超时");

    private final String code;
    private final String desc;

    BaseDbError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public BaseDbException toException() {
        return new BaseDbException(this, null, null);
    }

    @Override
    public BaseDbException toException(Throwable e) {
        return new BaseDbException(this, null, e);
    }

    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_CORE_I18N_MESSAGE_KEY_PREFIX + this.name();
    }

}
