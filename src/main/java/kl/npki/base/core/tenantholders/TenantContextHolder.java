package kl.npki.base.core.tenantholders;

import com.alibaba.ttl.TransmittableThreadLocal;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * <AUTHOR>
 */
public enum TenantContextHolder {

    /**
     * 单例对象
     */
    INSTANCE;
    private static final TransmittableThreadLocal<String> TENANT_ID = new TransmittableThreadLocal<>();
    /**
     * 默认 tenantId
     */
    private static String defaultTenantId;
    private final CopyOnWriteArraySet<String> tenantIds = new CopyOnWriteArraySet<>();
    private final List<Class<? extends ITenantEscrow>> classToEscrowList = new ArrayList<>();
    private final Map<String, Map<Class<? extends ITenantEscrow>, Object>> tenantEscrowObj = new HashMap<>();

    /**
     * 维护一份所有租户的托管对象，特点是Set的地址不变，但是Set中的内容会变
     */
    private final Map<Class<? extends ITenantEscrow>, Set<Object>> clazzEscrowObjSetMap = new HashMap<>();

    TenantContextHolder() {
    }

    public static String getDefaultTenantId() {
        return defaultTenantId;
    }

    public static void setDefaultTenantId(String defaultTenantId) {
        TenantContextHolder.defaultTenantId = defaultTenantId;
    }

    public static String getTenantId() {
        String tenantId = TENANT_ID.get();
        if (StringUtils.isBlank(tenantId)) {
            return defaultTenantId;
        }
        return tenantId;
    }

    public static void setTenantId(String tenantId) {
        TENANT_ID.set(tenantId);
    }

    public void registerTenant(String tenantId) {
        tenantIds.add(tenantId);
    }

    public void registerTenants(Collection<String> tenantIds) {
        this.tenantIds.addAll(tenantIds);
    }

    public Set<String> getTenantIds() {
        return tenantIds;
    }

    public static void clear() {
        TENANT_ID.remove();
    }

    public void registerEscrowObj(Class<? extends ITenantEscrow> escrowClazz) {
        classToEscrowList.add(escrowClazz);
    }

    public void initTenants(Set<String> tenantIdSet) throws InstantiationException, IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        for (String tenantName : tenantIdSet) {
            // 每个租户初始化一个托管对象
            for (Class<? extends ITenantEscrow> classToEscrow : classToEscrowList) {
                ITenantEscrow escrow = classToEscrow.getDeclaredConstructor().newInstance();
                Map<Class<? extends ITenantEscrow>, Object> classTenantEscrowMap =
                    tenantEscrowObj.computeIfAbsent(tenantName, k -> new HashMap<>());
                Object escrowedObj = escrow.initEscrowedObj(tenantName);
                Object oldObj = classTenantEscrowMap.putIfAbsent(classToEscrow, escrowedObj);
                Set<Object> escrowedObjSet = clazzEscrowObjSetMap.computeIfAbsent(classToEscrow, k -> new HashSet<>());
                if (null != oldObj) {
                    escrowedObjSet.remove(oldObj);
                }
                escrowedObjSet.add(escrowedObj);
            }
        }
        tenantIds.addAll(tenantIdSet);
    }

    public void initTenants() throws InstantiationException, IllegalAccessException, InvocationTargetException, NoSuchMethodException {
        initTenants(tenantIds);
    }

    public Object getEscrowedObj(Class<? extends ITenantEscrow> clazz) {
        String currentTenant = getTenantId();
        Map<Class<? extends ITenantEscrow>, Object> classTenantEscrowMap =
            tenantEscrowObj.computeIfAbsent(currentTenant, k -> new HashMap<>());
        return classTenantEscrowMap.get(clazz);
    }

    public Set<?> getAllEscrowedObj(Class<? extends ITenantEscrow> clazz) {
        return clazzEscrowObjSetMap.computeIfAbsent(clazz, k -> new HashSet<>());
    }

    public Object getEscrowedObj(String tenantId, Class<? extends ITenantEscrow> clazz) {
        Map<Class<? extends ITenantEscrow>, Object> classTenantEscrowMap = tenantEscrowObj.computeIfAbsent(tenantId,
            k -> new HashMap<>());
        return classTenantEscrowMap.get(clazz);
    }

    /**
     * 如果当前租户没有托管对象，则放入，参考 {@link Map#putIfAbsent(Object, Object)}
     *
     * @param clazz 托管对象的class
     * @param escrowedObj 托管对象
     * @return 如果当前租户没有托管对象，则返回null，否则返回当前租户的托管对象(旧值) 参考 {@link Map#putIfAbsent(Object, Object)}
     */
    public Object putEscrowedObjIfAbsent(Class<? extends ITenantEscrow> clazz, Object escrowedObj) {
        String currentTenant = getTenantId();
        Map<Class<? extends ITenantEscrow>, Object> classTenantEscrowMap =
            tenantEscrowObj.computeIfAbsent(currentTenant, k -> new HashMap<>());
        Object oldObj = classTenantEscrowMap.putIfAbsent(clazz, escrowedObj);
        // 如果当前租户没有托管对象，则直接放入escrowedObjSet, 如果当前租户有托管对象，先移除，再放入escrowedObjSet
        Set<Object> escrowedObjSet = clazzEscrowObjSetMap.computeIfAbsent(clazz, k -> new HashSet<>());
        if (null != oldObj) {
            escrowedObjSet.remove(oldObj);
        }
        escrowedObjSet.add(escrowedObj);
        return oldObj;
    }

    /**
     * 更新托管对象
     * @param clazz
     * @param escrowedObj
     * @param tenantId
     * @return
     */
    public Object updateEscrowedObj(Class<? extends ITenantEscrow> clazz, Object escrowedObj, String tenantId) {
        Map<Class<? extends ITenantEscrow>, Object> classTenantEscrowMap =
            tenantEscrowObj.computeIfAbsent(tenantId, k -> new HashMap<>());
        Object obj = classTenantEscrowMap.put(clazz, escrowedObj);
        // 如果当前租户没有托管对象，则直接放入escrowedObjSet, 如果当前租户有托管对象，先移除，再放入escrowedObjSet
        Set<Object> escrowedObjSet = clazzEscrowObjSetMap.computeIfAbsent(clazz, k -> new HashSet<>());
        if (null != obj) {
            escrowedObjSet.remove(obj);
        }
        escrowedObjSet.add(escrowedObj);
        return obj;
    }
}
