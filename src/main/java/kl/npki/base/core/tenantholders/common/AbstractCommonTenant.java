package kl.npki.base.core.tenantholders.common;

import kl.nbase.cache.client.CacheType;
import kl.nbase.cache.config.CacheConfig;
import kl.npki.base.core.configs.CommonMgrListenerConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.tenantholders.ConfigHolder;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * <AUTHOR> href="mailto:<EMAIL>">dingqi</a>
 * @since 2025/07/07 10:44
 */
public abstract class AbstractCommonTenant implements ICommonTenant {

    protected volatile boolean alreadyLoaded = false;

    // 读写锁
    protected final ReentrantReadWriteLock rwLock = new ReentrantReadWriteLock();
    protected final ReentrantReadWriteLock.ReadLock readLock = rwLock.readLock();
    protected final ReentrantReadWriteLock.WriteLock writeLock = rwLock.writeLock();

    @Override
    public void reload() {
        if (!alreadyLoaded) {
            // 重新加载配置
            refreshInternal();
            alreadyLoaded = true;
        }
    }

    /**
     * 强制重新加载配置并通知其他节点，适用修改配置的场景
     */
    @Override
    public void forceReload() {
        refreshInternal();
        notifyChanges();
    }

    @Override
    public void notifyChanges() {
        if (!useLocalCacheInMgr() && clusterCacheEnable()) {
            // 如果mgr中使用了缓存组件并且开启了集群缓存，则不通知其他节点重新加载配置
            return;
        }
        // 通知其他节点重新加载配置
        CommonMgrListenerConfig commonMgrListenerConfig = BaseConfigWrapper.getCommonMgrListenerConfig();
        Map<String, Integer> versionMap = commonMgrListenerConfig.getVersionMap();
        // 对Value进行自增操作，如果不存在则初始化为1
        versionMap.merge(id(), 1, Integer::sum);
        ConfigHolder.get().save(commonMgrListenerConfig);
    }

    /**
     * 是否使用单机缓存，如果使用缓存组件请返回false
     * @return true表示使用内存缓存，false表示使用缓存组件
     */
    protected boolean useLocalCacheInMgr() {
        // 默认使用内存缓存
        return true;
    }

    private boolean clusterCacheEnable() {
        // 根据配置判断是否启用了集群缓存
        CacheConfig cacheConfig = ConfigHolder.get().get(CacheConfig.class);
        return cacheConfig != null && cacheConfig.getType().equals(CacheType.REDIS.getCode());
    }

    @Override
    public boolean alreadyLoaded() {
        return alreadyLoaded;
    }

    public void refreshInternal() {
        // 内部方法，子类可以重写实现具体的刷新逻辑
        writeLock.lock();
        try {
            refresh();
        } finally {
            writeLock.unlock();
        }
    }

    protected abstract void refresh();

    @Override
    public String id() {
        // 默认返回类名作为ID
        return StringUtils.replace(this.getClass().getName(), ".", "_");
    }


}
