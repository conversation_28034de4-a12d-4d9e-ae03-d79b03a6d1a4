package kl.npki.base.core.tenantholders;

import kl.nbase.cache.client.CacheClientFactory;
import kl.nbase.cache.client.CacheType;
import kl.nbase.cache.client.ICacheClient;
import kl.nbase.cache.config.CacheConfig;
import kl.nbase.config.holder.RefreshableConfigHolder;
import kl.npki.base.core.utils.SystemUtil;

/**
 * 由于{@link kl.nbase.cache.client.ICacheClient}初始化依赖{@link CacheConfig}, <br>
 * 所以{@link CacheClientHolder}需要在{@link ConfigHolder}之后调用 #initEscrowedObj(java.lang.String)
 *
 * <AUTHOR>
 */
public class CacheClientHolder implements ITenantEscrow {

    public static ICacheClient get() {
        return (ICacheClient) TenantContextHolder.INSTANCE.getEscrowedObj(CacheClientHolder.class);
    }

    public static ICacheClient get(String tenantId) {
        return (ICacheClient) TenantContextHolder.INSTANCE.getEscrowedObj(tenantId, CacheClientHolder.class);
    }

    public static void reinit(String oldType, String newType) {
        String currentTenant = TenantContextHolder.getTenantId();
        reinit(oldType, newType, currentTenant);
    }

    public static void reinit(String oldType, String newType, String currentTenant) {
        // 如果原本使用非本地缓存，需要进行关闭，本地缓存两套环境使用同一个client，关闭会导致影响另一套
        if (CacheType.REDIS.getCode().equals(oldType)) {
            // 关闭client
            get().close();
        }
        // 重新初始化并更新
        CacheConfig cacheConfig = ConfigHolder.get().get(CacheConfig.class);
        ICacheClient newCacheClient = null;

        if (!SystemUtil.isDeployed() && !CacheType.REDIS.getCode().equals(newType)) {
            // 未部署完成，并且当前配置为本地缓存
            newCacheClient = CacheClientHolder.get(TenantContextHolder.getDefaultTenantId());
        } else {
            newCacheClient = CacheClientFactory.createCacheClient(currentTenant, cacheConfig);
        }
        TenantContextHolder.INSTANCE.updateEscrowedObj(CacheClientHolder.class, newCacheClient, currentTenant);
    }

    public static ICacheClient init(String tenantName, CacheConfig cacheConfig) {
        return CacheClientFactory.createCacheClient(tenantName, cacheConfig);
    }

    @Override
    public Object initEscrowedObj(String tenantName) {
        RefreshableConfigHolder refreshableConfigHolder = ConfigHolder.get(tenantName);
        CacheConfig cacheConfig = null;
        if (refreshableConfigHolder != null) {
            cacheConfig = refreshableConfigHolder.get(CacheConfig.class);
        }
        return init(tenantName, cacheConfig);
    }
}
