package kl.npki.base.core.configs;

import kl.nbase.config.RefreshableConfigAdaptor;
import kl.npki.base.core.biz.config.model.PatchInfo;
import kl.npki.base.core.constant.BaseConstant;

import java.util.Date;
import java.util.List;

/**
 * 系统信息配置
 * <p>
 *     系统信息公共配置，如系统名称，系统版本号，补丁版本号，定制版本号，邮箱，logo 等相关信息
 * </p>
 *
 * <AUTHOR>
 * @date 2022/12/19
 */
public class SysInfoConfig extends RefreshableConfigAdaptor {


    private static final long serialVersionUID = 7801135933507031934L;
    /**
     * 系统名称
     */
    private String name;

    /**
     * 系统版本号
     */
    private String version;

    /**
     * 内核版本
     */
    private String kernelVersion;

    /**
     * 定制版本
     */
    private String customizedVersion;

    /**
     * 电子邮件
     */
    private String email;

    /**
     * 系统logo
     */
    private String logo;
    
    /**
     * 是否开启操作签名
     */
    private boolean clientSign;

    /**
     * 密码分离标记（数据库）
     */
    private boolean passwdSeparateDb;

    /**
     * 环境切换标识
     * 测试环境： TEST  正式环境：PROD  @see EnvironmentEnum
     */
    private String environmentSwitchId;

    private String tenantIds;

    /**
     * 用户是否同意许可协议
     */
    private boolean userLicenseAgreement = true;

    /**
     * 系统部署时间
     */
    private Date deploymentTime;

    /**
     * 系统启动时间
     */
    private Date startTime;

    /**
     * 补丁列表
     */
    private List<PatchInfo> patchList;

    /**
     * 系统简称
     * 各系统对内的名称简写，方便在公共逻辑模块，各系统以此为判断条件区分处理
     * 根据目前系统情况  可选NGPKI-RA、NGPKI-CA、NGPKI-KM，取值不区分大小写
     */
    private String shortName = "NGPKI";

    /**
     * 根证书部署模式
     */
    private String rootCertificateMode;

    /**
     * 日志文件数量限制: 超过数量时，将删除最旧的文件
     */
    private Integer logFileLimitSize = 15;

    public Date getDeploymentTime() {
        return deploymentTime;
    }

    public void setDeploymentTime(Date deploymentTime) {
        this.deploymentTime = deploymentTime;
    }

    /**
     * 标识RefreshableConfig对象和哪个配置文件所绑定
     */
    @Override
    public String id() {
        return BaseConstant.CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return "kl.base.sysInfo";
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getKernelVersion() {
        return kernelVersion;
    }

    public void setKernelVersion(String kernelVersion) {
        this.kernelVersion = kernelVersion;
    }

    public String getCustomizedVersion() {
        return customizedVersion;
    }

    public void setCustomizedVersion(String customizedVersion) {
        this.customizedVersion = customizedVersion;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getEnvironmentSwitchId() {
        return environmentSwitchId;
    }

    public boolean isUserLicenseAgreement() {
        return userLicenseAgreement;
    }

    public void setUserLicenseAgreement(boolean userLicenseAgreement) {
        this.userLicenseAgreement = userLicenseAgreement;
    }

    public void setEnvironmentSwitchId(String environmentSwitchId) {
        this.environmentSwitchId = environmentSwitchId;
    }

    public String getTenantIds() {
        return tenantIds;
    }

    public void setTenantIds(String tenantIds) {
        this.tenantIds = tenantIds;
    }

    public boolean isClientSign() {
        return clientSign;
    }

    public void setClientSign(boolean clientSign) {
        this.clientSign = clientSign;
    }

    public boolean isPasswdSeparateDb() {
        return passwdSeparateDb;
    }

    public void setPasswdSeparateDb(boolean passwdSeparateDb) {
        this.passwdSeparateDb = passwdSeparateDb;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public List<PatchInfo> getPatchList() {
        return patchList;
    }

    public void setPatchList(List<PatchInfo> patchList) {
        this.patchList = patchList;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getRootCertificateMode() {
        return rootCertificateMode;
    }

    public void setRootCertificateMode(String rootCertificateMode) {
        this.rootCertificateMode = rootCertificateMode;
    }

    public Integer getLogFileLimitSize() {
        return logFileLimitSize;
    }

    public void setLogFileLimitSize(Integer logFileLimitSize) {
        this.logFileLimitSize = logFileLimitSize;
    }
}