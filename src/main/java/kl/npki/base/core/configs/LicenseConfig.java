package kl.npki.base.core.configs;

import kl.nbase.config.RefreshableConfigAdaptor;
import kl.nbase.config.listener.ConfigRefreshListener;
import kl.npki.base.core.biz.license.service.impl.LicenseConfigListener;
import kl.npki.base.core.constant.BaseConstant;

/**
 * <AUTHOR>
 * @since 2022/9/20 17:12
 */
public class LicenseConfig extends RefreshableConfigAdaptor {

    private static final long serialVersionUID = 4596352803900482075L;
    /**
     * 授权数据
     */
    public String license = "";
    /**
     * Linux下如果有多个设备，可以指定设备
     */
    public String device = null;
    /**
     * 刷新间隔，即多长时间刷新判断一次，授权许可是否还在有效期内，单位为秒，默认间隔为1小时。
     */
    public int refreshSpan = 3600;

    /**
     * 指定的部署环境
     */
    public String specifiedDeployEnv = "OLD";

    /**
     * 备用的部署环境
     */
    public String standbyDeployEnv = "FILE";

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public int getRefreshSpan() {
        return refreshSpan;
    }

    public void setRefreshSpan(int refreshSpan) {
        this.refreshSpan = refreshSpan;
    }

    public String getSpecifiedDeployEnv() {
        return specifiedDeployEnv;
    }

    public void setSpecifiedDeployEnv(String specifiedDeployEnv) {
        this.specifiedDeployEnv = specifiedDeployEnv;
    }

    public String getStandbyDeployEnv() {
        return standbyDeployEnv;
    }

    public void setStandbyDeployEnv(String standbyDeployEnv) {
        this.standbyDeployEnv = standbyDeployEnv;
    }

    @Override
    public String toString() {
        return "LicenseConfig{" + "license='" + license + '\'' + ", device='" + device + '\'' + ", refreshSpan=" + refreshSpan + ", specifiedDeployEnv='" + specifiedDeployEnv + '\'' + ", standbyDeployEnv='" + standbyDeployEnv + '\'' + '}';
    }

    @Override
    public String id() {
        return BaseConstant.CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return "kl.base.license";
    }

    @Override
    public ConfigRefreshListener configRefreshListener() {
        return new LicenseConfigListener();
    }
}
