package kl.npki.base.core.configs;

import kl.nbase.emengine.conf.ClusterEngineConfig;
import kl.nbase.emengine.conf.EngineConfig;
import kl.nbase.emengine.conf.GroupEngineConfig;
import kl.nbase.emengine.conf.file.FileEngineConfig;
import kl.nbase.emengine.entity.EngineAlgoInfo;
import kl.npki.base.core.constant.EmTypeEnum;
import kl.npki.base.core.utils.EngineUtil;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static kl.npki.base.core.constant.BaseConstant.WEB_ROOT;

/**
 * * 简化的 {@link kl.nbase.emengine.conf.ClusterEngineConfig} 配置类
 *
 * <AUTHOR>
 * @date 2024/5/17
 */
public class ClusterEmConfig implements Serializable, Cloneable {

    private static final long serialVersionUID = -1932678629626524317L;

    /**
     * 加密机集群名称
     */
    private String clusterName = "cluster";

    /**
     * 加密机组配置
     */
    private GroupEmConfig groupEngine;

    public String getClusterName() {
        return clusterName;
    }

    public void setClusterName(String clusterName) {
        this.clusterName = clusterName;
    }

    public GroupEmConfig getGroupEngine() {
        return groupEngine;
    }

    public void setGroupEngine(GroupEmConfig groupEngine) {
        this.groupEngine = groupEngine;
    }

    public ClusterEngineConfig toClusterEngineConfig() {
        ClusterEngineConfig clusterEngineConfig = new ClusterEngineConfig();
        clusterEngineConfig.setClusterName(clusterName);
        if (Objects.nonNull(groupEngine)) {
            clusterEngineConfig.setGroupEngineConfig(groupEngine.toGroupEngineConfig());
        }
        return clusterEngineConfig;
    }

    @Override
    public ClusterEmConfig clone() {
        try {
            // 这里使用super.clone()是浅拷贝，对于复杂对象可能需要进行深拷贝
            return (ClusterEmConfig) super.clone();
        } catch (CloneNotSupportedException e) {
            // 异常处理或返回null
            return null;
        }
    }

    /**
     * 查询集群密码设备所支持的算法
     */
    public EngineAlgoInfo queryEmEngineSupportedAlgo() {

        // 获取配置中所有密码设备类型
        Set<String> engineTypeSet = getGroupEngine()
            .getEngines()
            .stream()
            .map(EmConfig::getEngineType)
            .collect(Collectors.toSet());

        EngineAlgoInfo result = null;
        for (String engineType : engineTypeSet) {
            // 根据密码设备类型获取其支持的算法
            EngineAlgoInfo engineAlgoInfo = EngineUtil.getEngineAlgoByType(EmTypeEnum.getEmTypeEnumByEngineType(engineType));
            if (Objects.nonNull(result)) {
                // 取交集
                result.retain(engineAlgoInfo);
            } else {
                result = engineAlgoInfo;
            }
        }

        return result;
    }

    /**
     * 查询辅助密码设备支持的算法，如未开启辅助密码设备，则返回null。如开启了则可以与{@link #queryEmEngineSupportedAlgo()}返回的算法取并集作为可选的算法。
     */
    public EngineAlgoInfo queryBackupEngineSupportedAlgo() {
        return getGroupEngine().isEnableBackupEngine() ? EngineUtil.getEngineAlgoByType(EmTypeEnum.FILE_ENGINE) : null;
    }

}
