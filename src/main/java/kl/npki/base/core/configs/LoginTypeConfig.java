package kl.npki.base.core.configs;

import kl.nbase.config.SwitchableRefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;

/**
 * 登录模式配置
 *
 * 不属于全局配置的原因:
 *   部署过程中选择多选登录后，用户退出后，没有对应证书进行登录
 *
 * <AUTHOR>
 */
public class LoginTypeConfig extends SwitchableRefreshableConfigAdaptor {
    private static final long serialVersionUID = 6757909489355538501L;
    /**
     * 登录模式，多种登录模式之间采用逗号分割
     * @see kl.npki.base.core.constant.LoginType
     */
    private String loginModel;

    @Override
    public String id() {
        return BaseConstant.PKI_CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return "kl.base.login";
    }

    public String getLoginModel() {
        return loginModel;
    }

    public void setLoginModel(String loginModel) {
        this.loginModel = loginModel;
    }

}
