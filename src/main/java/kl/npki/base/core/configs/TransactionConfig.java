package kl.npki.base.core.configs;

import kl.nbase.config.SwitchableRefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;

/**
 * 事务配置
 *
 * <AUTHOR>
 * @date 2024/12/19 17:10
 */
public class TransactionConfig extends SwitchableRefreshableConfigAdaptor {

    private static final long serialVersionUID = 8169676727236206280L;

    /**
     * 事务超时时间(单位：秒)
     * -1,永不超时
     */
    private int timeoutSeconds = 10;

    @Override
    public String id() {
        return BaseConstant.PKI_CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return BaseConstant.CONFIG_TRANSACTION_PREFIX;
    }

    public int getTimeoutSeconds() {
        return timeoutSeconds;
    }

    public void setTimeoutSeconds(int timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }
}
