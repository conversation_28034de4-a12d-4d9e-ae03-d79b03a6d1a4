package kl.npki.base.core.configs;

import kl.nbase.config.RefreshableConfigAdaptor;
import kl.nbase.config.listener.ConfigRefreshListener;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.core.listener.RegionAndLanguageListener;

/**
 * 区域和语言配置
 *
 * <AUTHOR>
 * @date 2025/03/24
 */
public class RegionAndLanguageConfig extends RefreshableConfigAdaptor {

    private static final long serialVersionUID = 7206574971617266954L;

    /**
     * 区域
     */
    private String region;

    /**
     * 语言
     */
    private String language;


    public String getRegion() {
        return region;
    }

    public RegionAndLanguageConfig setRegion(String region) {
        this.region = region;
        return this;
    }

    public String getLanguage() {
        return language;
    }

    public RegionAndLanguageConfig setLanguage(String language) {
        this.language = language;
        return this;
    }


    @Override
    public String id() {
        return BaseConstant.CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return "kl.base.regionalLanguage";
    }


    @Override
    public ConfigRefreshListener configRefreshListener() {
        return new RegionAndLanguageListener();
    }

}

