package kl.npki.base.core.configs;

import java.io.Serializable;

/**
 * 具体配置规则
 *
 * <AUTHOR> by niugang on 2025-07-02 13:57
 */
public class FieldRuleConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 目标校验字段
     */
    private String target;

    /**
     * 规则
     */
    private String rule;

    /**
     * 错误中文描述
     */
    private String zhMsg;

    /**
     * 错误英文描述
     */
    private String enMsg;


    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }

    public String getZhMsg() {
        return zhMsg;
    }

    public void setZhMsg(String zhMsg) {
        this.zhMsg = zhMsg;
    }

    public String getEnMsg() {
        return enMsg;
    }

    public void setEnMsg(String enMsg) {
        this.enMsg = enMsg;
    }
}
