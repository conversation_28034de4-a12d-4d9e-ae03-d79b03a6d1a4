package kl.npki.base.core.configs;

import kl.nbase.config.RefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;

/**
 * @Author: guoq
 * @Date: 2022/8/24
 * @description: 线程池配置
 */
public class ThreadPoolConf extends RefreshableConfigAdaptor {

    private static final long serialVersionUID = -1108686718146900775L;
    /**
     * 核心线程数（默认线程数）
     */
    private int corePoolSize = 1;
    /**
     * 最大线程数
     */
    private int maxPoolSize = Runtime.getRuntime().availableProcessors();
    /**
     * 允许线程空闲时间（单位：默认为秒）
     */
    private int keepAliveTime = 30;
    /**
     * 缓冲队列大小
     */
    private int queueCapacity = 200;

    private String executorName = "executor";


    public int getCorePoolSize() {
        return corePoolSize;
    }

    public void setCorePoolSize(int corePoolSize) {
        this.corePoolSize = corePoolSize;
    }

    public int getMaxPoolSize() {
        return maxPoolSize;
    }

    public void setMaxPoolSize(int maxPoolSize) {
        this.maxPoolSize = maxPoolSize;
    }

    public int getKeepAliveTime() {
        return keepAliveTime;
    }

    public void setKeepAliveTime(int keepAliveTime) {
        this.keepAliveTime = keepAliveTime;
    }

    public int getQueueCapacity() {
        return queueCapacity;
    }

    public void setQueueCapacity(int queueCapacity) {
        this.queueCapacity = queueCapacity;
    }

    public String getExecutorName() {
        return executorName;
    }

    public void setExecutorName(String executorName) {
        this.executorName = executorName;
    }

    @Override
    public String id() {
        return BaseConstant.CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return "kl.base.thread";
    }
}
