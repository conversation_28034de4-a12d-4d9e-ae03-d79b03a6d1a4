package kl.npki.base.core.configs;

import kl.nbase.config.FieldMapping;
import kl.npki.base.core.constant.BaseConstant;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/30
 */
public class NpkiServerConfig implements Serializable {

    private static final long serialVersionUID = 5761618233501775684L;
    private Integer port;

    @FieldMapping("embed-name")
    private String embedName;

    @FieldMapping("api-prefix")
    private String apiPrefix;

    @FieldMapping("context-path")
    private String contextPath;

    /**
     * 禁止的http方法
     */
    @FieldMapping("http-disallowed")
    private String disallowedMethods;
    /**
     * 是否已完成端口配置
     */
    private Boolean portConfigured;

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getEmbedName() {
        return embedName;
    }

    public void setEmbedName(String embedName) {
        this.embedName = embedName;
    }

    public String getApiPrefix() {
        return apiPrefix;
    }

    public String getRealApiPrefix() {
        if (StringUtils.isNotBlank(contextPath) && !"/".equals(contextPath)) {
            return contextPath + apiPrefix;
        }
        return apiPrefix;
    }

    public void setApiPrefix(String apiPrefix) {
        this.apiPrefix = apiPrefix;
    }

    public String getDisallowedMethods() {
        return disallowedMethods;
    }

    public void setDisallowedMethods(String disallowedMethods) {
        this.disallowedMethods = disallowedMethods;
    }

    public Set<String> getDisallowedMethodSet() {
        if (disallowedMethods == null || disallowedMethods.trim().isEmpty()) {
            return Collections.emptySet();
        }
        return Arrays.stream(disallowedMethods.split(BaseConstant.COMMA))
            .map(String::trim)
            .filter(s -> !s.isEmpty())
            .collect(Collectors.toSet());
    }

    public void setDisallowedMethodSet(Set<String> disallowedMethods) {
        if (disallowedMethods == null || disallowedMethods.isEmpty()) {
            this.disallowedMethods = null;
        } else {
            this.disallowedMethods = String.join(BaseConstant.COMMA, disallowedMethods);
        }
    }

    public Boolean getPortConfigured() {
        return portConfigured;
    }

    public NpkiServerConfig setPortConfigured(Boolean portConfigured) {
        this.portConfigured = portConfigured;
        return this;
    }

    public String getContextPath() {
        return contextPath;
    }

    public NpkiServerConfig setContextPath(String contextPath) {
        this.contextPath = contextPath;
        return this;
    }
}
