package kl.npki.base.core.configs;

import kl.nbase.config.SwitchableRefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;

/**
 * 首页数据缓存定时任务配置类
 *
 * <AUTHOR>
 */
public class HomePageDataCacheJobConfig extends SwitchableRefreshableConfigAdaptor {

    /**
     * 是否启用缓存定时任务
     */
    private boolean enabled = true;

    /**
     * 缓存更新的cron表达式
     * 默认1小时更新一次
     */
    private String cronExpression = "0 0 */1 * * ?";

    @Override
    public String id() {
        return BaseConstant.PKI_CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return BaseConstant.CONFIG_HOMEPAGE_PREFIX;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public HomePageDataCacheJobConfig setEnabled(boolean enabled) {
        this.enabled = enabled;
        return this;
    }

    public String getCronExpression() {
        return cronExpression;
    }

    public HomePageDataCacheJobConfig setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
        return this;
    }
}
