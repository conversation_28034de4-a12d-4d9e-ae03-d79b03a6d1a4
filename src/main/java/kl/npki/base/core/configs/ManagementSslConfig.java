package kl.npki.base.core.configs;

import kl.nbase.config.RefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;

/**
 * <AUTHOR>
 * @date 2023/4/24
 */
public class ManagementSslConfig extends RefreshableConfigAdaptor {

    private static final long serialVersionUID = 7801135933507031934L;

    /**
     * 是否启用ssl
     */
    private Boolean enabled;

    /**
     * 协议类型
     */
    private String protocol;

    /**
     * 密码套件
     */
    private String ciphers;

    public Boolean getEnabled() {
        return enabled;
    }

    public ManagementSslConfig setEnabled(Boolean enabled) {
        this.enabled = enabled;
        return this;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getCiphers() {
        return ciphers;
    }

    public void setCiphers(String ciphers) {
        this.ciphers = ciphers;
    }

    /**
     * 标识RefreshableConfig对象和哪个配置文件所绑定
     */
    @Override
    public String id() {
        return BaseConstant.CONFIG_FILE_NAME;
    }

    /**
     * RefreshableConfig对象字段与配置文件之间的统一前缀
     */
    @Override
    public String prefix() {
        return "server.ssl";
    }
}
