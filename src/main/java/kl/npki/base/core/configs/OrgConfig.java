package kl.npki.base.core.configs;

import kl.nbase.config.SwitchableRefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;

/**
 * 机构配置信息
 *
 * <AUTHOR>
 * @date 2024/07/22
 */
public class OrgConfig extends SwitchableRefreshableConfigAdaptor {
    private static final long serialVersionUID = 4738838655369388629L;


    /**
     * 机构排序码序列号
     */
    private Integer orderIndexSeq = 99999999;

    public Integer getOrderIndexSeq() {
        return orderIndexSeq;
    }

    public void setOrderIndexSeq(Integer orderIndexSeq) {
        this.orderIndexSeq = orderIndexSeq;
    }

    @Override
    public String id() {
        return BaseConstant.PKI_CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return BaseConstant.CONFIG_ORG_PREFIX;
    }
}
