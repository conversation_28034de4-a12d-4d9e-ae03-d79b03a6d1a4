package kl.npki.base.core.configs;

import kl.nbase.emengine.conf.GroupEngineConfig;
import kl.nbase.emengine.conf.strategy.backup.DefaultBackupStrategy;
import kl.npki.base.core.utils.EngineUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 简化的 {@link kl.nbase.emengine.conf.GroupEngineConfig} 配置类
 *
 * <AUTHOR>
 * @since 2024/1/11
 */
public class GroupEmConfig implements Serializable {
    private static final long serialVersionUID = -1932678629626524317L;

    /**
     * 密码机组名称
     */
    private String groupName;

    /**
     * 主密码机配置
     */
    private List<EmConfig> engines;

    /**
     * 辅助密码机配置
     */
    private EmConfig backupEngine;

    /**
     * 是否启用辅助密码机
     */
    private boolean enableBackupEngine = false;

    private EmLoadBalancerConfig loadBalancer;

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public List<EmConfig> getEngines() {
        return engines;
    }

    public void setEngines(List<EmConfig> engines) {
        this.engines = engines;
    }

    public EmConfig getBackupEngine() {
        return backupEngine;
    }

    public GroupEmConfig setBackupEngine(EmConfig backupEngine) {
        this.backupEngine = backupEngine;
        return this;
    }

    public boolean isEnableBackupEngine() {
        return enableBackupEngine;
    }

    public void setEnableBackupEngine(boolean enableBackupEngine) {
        this.enableBackupEngine = enableBackupEngine;
    }

    public EmLoadBalancerConfig getLoadBalancer() {
        return loadBalancer;
    }

    public void setLoadBalancer(EmLoadBalancerConfig loadBalancer) {
        this.loadBalancer = loadBalancer;
    }

    public GroupEngineConfig toGroupEngineConfig() {
        GroupEngineConfig groupEngineConfig = new GroupEngineConfig();
        groupEngineConfig.setGroupName(groupName);
        //  主密码机为空时直接返回
        if (CollectionUtils.isEmpty(engines)) {
            return groupEngineConfig;
        }
        groupEngineConfig.setEngineConfigList(engines.stream().map(EmConfig::toEngineConfig).collect(Collectors.toList()));
        Set<String> allEngineName = engines.stream().map(EmConfig::getEngineName).collect(Collectors.toSet());
        if (Objects.nonNull(loadBalancer)) {
            groupEngineConfig.setLbStrategy(loadBalancer.toLoadBalancerConfig(allEngineName));
        }
        // 主密码机为非文件密码机时才能启用辅助密码机
        boolean masterEngineExistFileEngine = engines.stream().anyMatch(engineConfig -> EngineUtil.isFileEngine(engineConfig.getEngineType()));
        if (masterEngineExistFileEngine) {
            return groupEngineConfig;
        }
        // 启用辅助密码机且存在辅助密码机配置
        if (backupEngine != null && StringUtils.isNotBlank(backupEngine.getPath()) && enableBackupEngine) {
            groupEngineConfig.setBackupEngineConfig(backupEngine.toEngineConfig());
            groupEngineConfig.setBackupStrategy(new DefaultBackupStrategy());
        }
        return groupEngineConfig;
    }

}
