package kl.npki.base.core.configs.wrapper;

import kl.nbase.config.holder.RefreshableConfigHolder;
import kl.npki.base.core.configs.*;
import kl.npki.base.core.tenantholders.ConfigHolder;

/**
 * <AUTHOR>
 */
public class BaseConfigWrapper {

    protected BaseConfigWrapper() {
    }

    public static RefreshableConfigHolder getHolder() {
        return ConfigHolder.get();
    }

    public static SysSupportedAlgoConfig getSysAlgoConfig() {
        return getHolder().get(SysSupportedAlgoConfig.class);
    }

    public static LicenseConfig getLicenseConfig() {
        return getHolder().get(LicenseConfig.class);
    }

    public static LogConfig getLogConfig() {
        return getHolder().get(LogConfig.class);
    }

    public static LoginConfig getLoginConfig() {
        return getHolder().get(LoginConfig.class);
    }

    public static LoginTypeConfig getLoginTypeConfig() {
        return getHolder().get(LoginTypeConfig.class);
    }

    public static SysInfoConfig getSysInfoConfig() {
        return getHolder().get(SysInfoConfig.class);
    }

    public static SysConfig getSysConfig() {
        return getHolder().get(SysConfig.class);
    }

    public static ThreadPoolConf getThreadPoolConf() {
        return getHolder().get(ThreadPoolConf.class);
    }

    public static HealthMetricsConfig getHealthMetricsConfig() {
        return getHolder().get(HealthMetricsConfig.class);
    }

    public static SelfCheckJobConfig getSelfCheckJobConfig() {
        return getHolder().get(SelfCheckJobConfig.class);
    }

    public static KcspConfig getKcspConfig() {
        return getHolder().get(KcspConfig.class);
    }

    public static TransactionConfig getTransactionConfig() {
        return getHolder().get(TransactionConfig.class);
    }

    public static WebConfig getWebConfig() {
        return getHolder().get(WebConfig.class);
    }


    public static HomePageDataCacheJobConfig getHomePageDataCacheJobConfig() {
        return getHolder().get(HomePageDataCacheJobConfig.class);
    }


    public static DateFormatConfig getDateFormatConfig() {
        return getHolder().get(DateFormatConfig.class);
    }

    public static RegionAndLanguageConfig getRegionalLanguageConfig() {
        return getHolder().get(RegionAndLanguageConfig.class);
    }


    public static ResourceControlConfig getResourceControlConfig() {
        return getHolder().get(ResourceControlConfig.class);
    }

    public static SystemInspectionJobConfig getSystemInspectionJobConfig() {
        return getHolder().get(SystemInspectionJobConfig.class);
    }

    public static OrgConfig getOrgConfig() {
        return getHolder().get(OrgConfig.class);
    }
    public static ClusterEmConfig getClusterEmConfig() {
        return getHolder().get(ClusterEmConfig.class);
    }

    public static SysCertConfig getSysCertConfig() {
        return getHolder().get(SysCertConfig.class);
    }

    public static MultipartConfig getMultipartConfig(){
        return getHolder().get(MultipartConfig.class);
    }

    public static FieldValidationRuleConfig getFieldValidationRuleConfig() {
        return getHolder().get(FieldValidationRuleConfig.class);
    }

    public static DbPasswdConfig getDbPasswdConfig() {
        return getHolder().get(DbPasswdConfig.class);
    }

}
