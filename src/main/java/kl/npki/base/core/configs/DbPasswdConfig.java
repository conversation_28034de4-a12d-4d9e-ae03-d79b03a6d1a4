package kl.npki.base.core.configs;

import kl.nbase.config.SwitchableRefreshableConfigAdaptor;
import kl.npki.base.core.constant.BaseConstant;

import java.util.Map;

/**
 * 数据库密码配置类
 * 工行需要从配置中读取数据库密码
 */
public class DbPasswdConfig  extends SwitchableRefreshableConfigAdaptor {

    /**
     * 数据库密码 [密码标识：密码]
     */
    private Map<String,String> dbPasswdMap;

    public Map<String, String> getDbPasswdMap() {
        return dbPasswdMap;
    }

    public void setDbPasswdMap(Map<String, String> dbPasswdMap) {
        this.dbPasswdMap = dbPasswdMap;
    }

    @Override
    public String id() {
        return BaseConstant.PKI_CONFIG_FILE_NAME;
    }

    @Override
    public String prefix() {
        return "kl.base.passwdSeparate.db";
    }
}
