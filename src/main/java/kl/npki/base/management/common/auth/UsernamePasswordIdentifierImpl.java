package kl.npki.base.management.common.auth;

import kl.nbase.auth.core.AuthnType;
import kl.nbase.auth.credential.Credential;
import kl.nbase.auth.credential.UsernamePasswordCredential;
import kl.nbase.auth.principal.Principal;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.constant.CertStatus;
import kl.npki.base.core.constant.UserStatus;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import kl.npki.management.core.biz.admin.model.entity.RoleEntity;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

/**
 * 口令登录spi实现
 *
 * <AUTHOR>
 * @date 2023/2/14
 */
@Service
@DependsOn({"springRepositoryCollector"})
public class UsernamePasswordIdentifierImpl extends AbstractIdentifierImpl {

    @Override
    public Principal identifier(Credential credential) {
        UsernamePasswordCredential usernamePasswordCredential = (UsernamePasswordCredential) credential;
        AdminEntity adminEntity = adminInfoRepository.getByUsername(usernamePasswordCredential.getUsername());
        CheckUtils.notNull(adminEntity, ManagementValidationError.ADMIN_USERNAME_OR_PWD_ERROR.toException());
        if (!adminEntity.getUsername().equals(usernamePasswordCredential.getUsername())) {
            throw ManagementValidationError.ADMIN_USERNAME_OR_PWD_ERROR.toException();
        }

        // 验证该管理员对应的角色以及完整性
        RoleEntity roleEntity = roleRepository.getRoleByCode(adminEntity.getRoleCode());
        CheckUtils.notNull(roleEntity, ManagementValidationError.ADMIN_ROLE_NOT_EXISTS.toException());

        // 判断账号是否锁定
        CheckUtils.isTrue(!adminEntity.isLockedAtNow(), ManagementValidationError.LOGIN_ACCOUNT_HAS_BEEN_LOCKED.toException());
        // 检查当前用户状态是否允许登录
        CheckUtils.isTrue(UserStatus.allowToLogin(adminEntity.getStatus()), BaseValidationError.USER_STATUS_ERROR.toException());

        AdminCertEntity adminCertEntity = adminCertRepository.getByAdminInfoId(adminEntity.getId());
        // 检查当前证书状态是否允许登录
        if (adminCertEntity != null) {
            CheckUtils.isTrue(CertStatus.allowToLogin(adminCertEntity.getStatus()), BaseValidationError.USER_CERT_STATUS_ERROR.toException());
        }
        String passwordHash = usernamePasswordCredential.getPassword();
        // 密码口令错误
        if (!adminEntity.checkPasswordHash(passwordHash)) {
            adminEntity.increaseLoginRetries();
            throw ManagementValidationError.ADMIN_USERNAME_OR_PWD_ERROR.toException();
        }
        // 登录成功时loginRetries大于0，则重置loginRetries
        if (adminEntity.getLoginRetries() > 0) {
            adminEntity.resetLoginRetries();
        }
        return buildPrincipal(adminEntity);
    }

    @Override
    public boolean isSupport(Credential credential) {
        return credential instanceof UsernamePasswordCredential;
    }

    @Override
    protected String getIdType() {
        return AuthnType.PWD.name();
    }
}
