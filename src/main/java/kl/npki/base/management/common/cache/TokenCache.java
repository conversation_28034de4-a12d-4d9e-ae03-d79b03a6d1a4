package kl.npki.base.management.common.cache;

import kl.nbase.auth.token.AuthenticationToken;
import kl.npki.base.core.common.ICommonCache;

/**
 * Token缓存
 *
 * <AUTHOR>
 * @Date 2023/9/13
 */
public enum TokenCache implements ICommonCache {

    /**
     * 单例对象
     */
    INSTANCE;

    private static final String CACHE_ID = "base:token";

    public void set(AuthenticationToken authenticationToken, long timeToLive) {
        getClient().set(wrapCacheKey(authenticationToken.getToken()), authenticationToken, timeToLive);
    }

    /**
     * 写入到指定环境的缓存中
     *
     * @param tenantId
     * @param authenticationToken
     * @param timeToLive
     */
    public void set(String tenantId, AuthenticationToken authenticationToken, long timeToLive) {
        getClient(tenantId).set(wrapCacheKey(authenticationToken.getToken(), tenantId), authenticationToken,
            timeToLive);
    }

    public AuthenticationToken get(String token) {
        return getClient().get(wrapCacheKey(token));
    }

    /**
     * 从指定环境的缓存中获取
     *
     * @param tenantId
     * @param token
     * @return
     */
    public AuthenticationToken get(String tenantId, String token) {
        return getClient(tenantId).get(wrapCacheKey(token));
    }

    public void remove(String token) {
        getClient().delete(wrapCacheKey(token));
    }

    /**
     * 从指定环境的缓存中移除
     *
     * @param tenantId
     * @param token
     */
    public void remove(String tenantId, String token) {
        getClient(tenantId).delete(wrapCacheKey(token));
    }

    @Override
    public String getId() {
        return CACHE_ID;
    }

}
