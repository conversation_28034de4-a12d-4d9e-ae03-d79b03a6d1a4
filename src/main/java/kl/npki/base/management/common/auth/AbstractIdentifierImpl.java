package kl.npki.base.management.common.auth;

import kl.nbase.auth.identifier.CredentialIdentifier;
import kl.nbase.auth.principal.DefaultPrincipal;
import kl.nbase.auth.principal.Principal;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.npki.base.core.common.crypto.SignatureHelper;
import kl.npki.base.core.constant.LoginConstant;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import kl.npki.management.core.common.cache.AuthCache;
import kl.npki.management.core.repository.IAdminCertRepository;
import kl.npki.management.core.repository.IAdminInfoMgrRepository;
import kl.npki.management.core.repository.IRoleRepository;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * 抽象的认证实现
 *
 * <AUTHOR>
 * @date 2023/2/14
 */
public abstract class AbstractIdentifierImpl implements CredentialIdentifier {
    private static final Logger logger = LoggerFactory.getLogger(AbstractIdentifierImpl.class);

    protected final IAdminCertRepository adminCertRepository = RepositoryFactory.get(IAdminCertRepository.class);

    protected final IAdminInfoMgrRepository adminInfoRepository = RepositoryFactory.get(IAdminInfoMgrRepository.class);

    protected final IRoleRepository roleRepository = RepositoryFactory.get(IRoleRepository.class);

    /**
     * 身份认证类型
     *
     * @return
     */
    protected abstract String getIdType();


    protected void verifySigned(String b64SignData, String certValue, String authRefId, String hashAlgo) {
        // 获取请求中的id
        if (StringUtils.isBlank(authRefId)) {
            throw ManagementValidationError.AUTH_REF_ID_ERROR.toException();
        }
        // 通过会话id到缓存查询签名源数据
        String random = AuthCache.INSTANCE.get(authRefId);

        HashAlgo algo = null;
        try {
            algo = new HashAlgo(hashAlgo);
        } catch (Exception e) {
            logger.warn("HashAlgo {} convert error, use default hash algo", hashAlgo, e);
        }
        // 进行验签
        boolean result;
        try {
            if (algo != null) {
                result = SignatureHelper.verify(CertUtil.parseCert(certValue), Base64Util.base64Decode(b64SignData),
                        random.getBytes(StandardCharsets.UTF_8), algo);
            } else {
                result = SignatureHelper.verify(CertUtil.parseCert(certValue), Base64Util.base64Decode(b64SignData),
                        random.getBytes(StandardCharsets.UTF_8));
            }
        } catch (Exception e) {
            throw ManagementValidationError.VERIFY_SIGNED_ERROR.toException(e);
        }
        if (!result) {
            throw ManagementValidationError.VERIFY_SIGNED_ERROR.toException();
        }
    }

    private HttpServletRequest getHttpServletRequest() {
        return ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
    }

    protected Principal buildPrincipal(AdminEntity adminInfoDO) {
        DefaultPrincipal principal = new DefaultPrincipal(adminInfoDO.getId().toString(), getIdType());
        // 认证完成后保存登录者的信息到token中
        principal.addRole(adminInfoDO.getRoleCode());
        principal.addAttributes(LoginConstant.USERNAME, adminInfoDO.getUsername());
        principal.addAttributes(LoginConstant.USER_ID, adminInfoDO.getId());
        return principal;
    }

}
