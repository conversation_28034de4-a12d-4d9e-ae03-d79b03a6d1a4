package kl.npki.base.management.common.cache;

import kl.nbase.security.util.encoders.Base64;
import kl.npki.base.core.common.ICommonCache;
import kl.npki.base.management.exception.ManagementInternalError;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.security.MessageDigest;

/**
 * 导出日志数据缓存
 * <p>
 * 由于导出日志需要使用客户端Ukey中的私钥做签名，为了不重复查库并构造Excel文件，所以使用缓存提高系统导出性能
 * </p>
 *
 * <AUTHOR>
 * @create 2025/2/13 下午4:16
 */
public enum LogExportDataCache implements ICommonCache {

    /**
     * 单例对象
     */
    INSTANCE;

    private static final String CACHE_ID = "base:export:log";

    /**
     * 获取缓存
     *
     * @param hashValue 缓存文件的hash值
     * @return sourceData 签名源数据
     */
    public File get(String hashValue) {
        return getClient().get(wrapCacheKey(hashValue));
    }

    /**
     * 设置缓存
     *
     * @param sourceData 源数据
     * @return
     */
    public String set(File sourceData) {
        // 设置10分钟的缓存时间
        long timeToLive = 10 * 60 * 1000L;
        String hashValue = getHashValue(sourceData);
        getClient().set(wrapCacheKey(hashValue), sourceData, timeToLive);
        return hashValue;
    }

    /**
     * 移除缓存
     *
     * @param sessionId 会话ID
     */
    public void remove(String sessionId) {
        getClient().delete(wrapCacheKey(sessionId));
    }

    @Override
    public String getId() {
        return CACHE_ID;
    }

    private static String getHashValue(File file) {
        try {
            byte[] byteValue = FileUtils.readFileToByteArray(file);
            MessageDigest messageDigest = MessageDigest.getInstance("SHA256");
            byte[] digest = messageDigest.digest(byteValue);
            return Base64.toBase64String(digest);
        } catch (Exception e) {
            throw ManagementInternalError.EXCEL_FILE_DIGEST_ERROR.toException(e.getMessage(), e);
        }
    }
}