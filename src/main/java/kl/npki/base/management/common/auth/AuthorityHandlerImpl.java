package kl.npki.base.management.common.auth;

import kl.nbase.auth.authority.AuthorityHandler;
import kl.nbase.auth.core.AuthnType;
import kl.nbase.auth.principal.Principal;
import kl.nbase.security.asn1.x509.Certificate;
import kl.npki.base.core.configs.LoginConfig;
import kl.npki.base.core.configs.LoginTypeConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.LoginConstant;
import kl.npki.base.core.constant.LoginType;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.core.utils.CertUtil;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import kl.npki.management.core.biz.admin.service.AdminCertMgr;
import kl.npki.management.core.common.BaseMgmtCoreMgrHolder;
import kl.npki.management.core.repository.IRoleResourceLinkMgrRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static kl.npki.base.management.constant.I18nExceptionInfoConstant.PLEASE_LOGIN_TO_SYSTEM_AGAIN_I18N_KEY;
import static kl.npki.base.management.constant.I18nExceptionInfoConstant.THE_RESOURCE_NOT_EXIST_I18N_KEY;

/**
 * <AUTHOR>
 * @date 2022/9/14
 * @desc 鉴权实现类
 */
@Service
public class AuthorityHandlerImpl implements AuthorityHandler {

    private final IRoleResourceLinkMgrRepository resourceLinkMgrRepository = RepositoryFactory.get(IRoleResourceLinkMgrRepository.class);
    private static final Logger log = LoggerFactory.getLogger(AuthorityHandlerImpl.class);

    /**
     * 资源编码放到header头，便于统一获取和校验
     */
    private static final String RESOURCE_CODE = "Resource-Code";

    @Override
    public boolean authorization(String resource, Principal principal) {
        final LoginConfig loginConfig = BaseConfigWrapper.getLoginConfig();
        // 是否配置开启鉴权
        if (Boolean.FALSE.equals(loginConfig.isAccessControlEnabled())) {
            saveAdminInfo(principal);
            return true;
        }
        // 获取用户角色
        Set<String> roles = principal.getRoles();
        // 获取请求对象头资源编码
        String resourceHeader = RequestContextHolder.getContext().getRequest().getHeader(RESOURCE_CODE);
        if (StringUtils.isEmpty(resourceHeader) || CollectionUtils.isEmpty(roles)) {
            saveAdminInfo(principal);
            return true;
        }
        // 根据角色和资源查询是否有权限
        Boolean hasPower = resourceLinkMgrRepository.isExistQueryByRoleAndResourceCode(roles.iterator().next(), resourceHeader);
        if (Boolean.FALSE.equals(hasPower)) {
            // 没有权限
            throw ManagementValidationError.NO_PERMISSION.toException(THE_RESOURCE_NOT_EXIST_I18N_KEY);
        }
        // 设置上下文参数
        saveAdminInfo(principal);
        return true;
    }

    @Override
    public boolean isSupport(Principal principal) {
        return true;
    }

    private void saveAdminInfo(Principal principal) {
        String userId = String.valueOf(principal
            .getAttributes()
            .get(LoginConstant.USER_ID));
        String username = String.valueOf(principal
            .getAttributes()
            .get(LoginConstant.USERNAME));
        // 获取操作员的证书
        Certificate certificate = getCertificate(userId);
        Set<String> roles = principal.getRoles();
        RequestContextHolder
            .getContext()
            .setUsername(username);
        RequestContextHolder
            .getContext()
            .setUserId(Long.parseLong(userId));
        RequestContextHolder
            .getContext()
            .setRoleIds(roles
                .stream()
                .map(Long::parseLong)
                .collect(Collectors.toList()));
        RequestContextHolder
            .getContext()
            .setUserCert(certificate);
        // 校验登录方式
        loginTypeCheck();
    }

    /**
     * 校验登录方式
     */
    private static void loginTypeCheck() {
        // 未部署完成不校验
        if (!SystemUtil.isDeployed()) {
            return;
        }
        // 获取token中的认证方式集合
        Set<String> authnTypesFromToken = RequestContextHolder.getAuthnTypes();
        // 通过配置文件获取的认证方式集合
        Set<String> authnTypesFromConfig = getAuthnTypesFromConfig();
        // 判断配置文件中的认证方式是否与token中的认证方式一致
        boolean allMatch = authnTypesFromConfig.stream().allMatch(authnTypesFromToken::contains);
        // 校验认证方式是否发生变动
        if (!allMatch) {
            //请重新登录系统
            throw ManagementValidationError.LOGIN_METHOD_HAS_BEEN_MODIFIED.toException(PLEASE_LOGIN_TO_SYSTEM_AGAIN_I18N_KEY);
        }
    }


    private static Set<String> getAuthnTypesFromConfig() {
        // 获取系统配置中的登录方式
        LoginTypeConfig config = BaseConfigWrapper.getLoginTypeConfig();
        String loginModel = config.getLoginModel();
        LoginType loginType = LoginType.getLoginTypeByType(loginModel);

        // 单点登录认证类型
        if (LoginType.SSO == loginType) {
            return new HashSet<>(Collections.singletonList(loginModel));
        }

        // 通过登录方式获取到对应的认证方式
        return loginType.getAuthnTypes()
                .stream()
                .map(AuthnType::getCode)
                .collect(Collectors.toSet());
    }


    /**
     * 根据操作员id获取操作员证书
     *
     * @param userId 用户id
     * @return {@link Certificate }
     */
    private Certificate getCertificate(String userId) {
        Certificate certificate = null;
        // 获取认证类型
        LoginTypeConfig loginTypeConfig = BaseConfigWrapper.getLoginTypeConfig();
        String loginModel = loginTypeConfig.getLoginModel();
        if (StringUtils.isBlank(loginModel)) {
            return certificate;
        }
        LoginType loginType = LoginType.getLoginTypeByType(loginModel);
        if (Objects.isNull(loginType)) {
            return certificate;
        }
        // 当前登录方式是否需要证书
        boolean deployed = SystemUtil.isDeployed();
        // 已部署完成 && 登录方式需要证书
        if (deployed) {
            boolean neededCertificate = loginType.needCertificate();
            if (neededCertificate) {
                AdminCertMgr adminCertMgr = BaseMgmtCoreMgrHolder.getAdminCertMgr();
                AdminCertEntity adminCertEntity = adminCertMgr.getAdminCert(Long.parseLong(userId));
                if (adminCertEntity == null) {
                    // 管理员证书不存在
                    throw ManagementValidationError.ADMINISTRATOR_CERTIFICATE_DOES_NOT_EXIST.toException(userId);
                }
                certificate = CertUtil.parseCert(adminCertEntity.getSignCertValue());
            }
        }
        return certificate;
    }

}
