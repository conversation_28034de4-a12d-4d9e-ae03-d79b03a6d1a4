package kl.npki.base.management.common.auth;

import kl.nbase.auth.token.AuthenticationToken;
import kl.nbase.auth.token.store.TokenStore;
import kl.nbase.auth.utils.JwtHelper;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.management.common.cache.AdminIdBlocklistCache;
import kl.npki.base.management.common.cache.DenyJwtCache;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.utils.JwtSecretKeyUtil;
import kl.npki.base.service.common.context.RequestContextHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class JwtTokenStoreImpl implements TokenStore {

    /**
     * logger
     **/
    private static final Logger logger = LoggerFactory.getLogger(JwtTokenStoreImpl.class);

    @Override
    public void save(AuthenticationToken authenticationToken) {
        // jwt服务端无需保存
    }

    @Override
    public AuthenticationToken get(String token) {
        // 检查是否为空或（在黑名单中且不在宽限期）
        CheckUtils.isTrue(StringUtils.isNotBlank(token) && !DenyJwtCache.INSTANCE.isDenylisted(token), ManagementValidationError.LOGIN_STATUS_EXPIRED.toException());
        // 获取token关联的信息
        AuthenticationToken authenticationToken = JwtHelper.verifyToken(JwtSecretKeyUtil.getSecretKey(), token);
        // token缓存中没有该token相关联的信息，则认为登录状态已过期
        CheckUtils.notNull(authenticationToken, ManagementValidationError.LOGIN_STATUS_EXPIRED.toException());
        // 检查管理员是否已被注销
        String adminId = authenticationToken.getPrincipal().getId();
        CheckUtils.isTrue(!AdminIdBlocklistCache.INSTANCE.isBlocklisted(adminId), ManagementValidationError.LOGIN_STATUS_EXPIRED.toException());
        String ipFromRequest = RequestContextHolder.getContext().getIp();
        String ipFromToken = JwtHelper.getIpFromToken(token);
        CheckUtils.isTrue(StringUtils.equals(ipFromRequest, ipFromToken), ManagementValidationError.LOGIN_STATUS_IP_ERROR.toException());
        if (logger.isDebugEnabled()) {
            if (!StringUtils.equals(ipFromRequest, ipFromToken)) {
                logger.debug("ip from request: {}, ip from token: {}", ipFromRequest, ipFromToken);
            }
        }
        return authenticationToken;
    }

    @Override
    public void delete(String token) {
        if (StringUtils.isEmpty(token)) {
            return;
        }
        // 将JWT添加到黑名单
        DenyJwtCache.INSTANCE.addToDenylist(token);
    }
}
