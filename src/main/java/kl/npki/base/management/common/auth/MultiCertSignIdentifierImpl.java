package kl.npki.base.management.common.auth;

import kl.nbase.auth.core.AuthnType;
import kl.nbase.auth.credential.CertSignCredential;
import kl.nbase.auth.credential.Credential;
import kl.nbase.auth.credential.MultiCertSignCredential;
import kl.nbase.auth.principal.Principal;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.constant.CertStatus;
import kl.npki.base.core.constant.UserStatus;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import kl.npki.management.core.biz.admin.model.entity.RoleEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @desc 多选登录认证过程实现类
 */
@Service
@DependsOn({"springRepositoryCollector"})
public class MultiCertSignIdentifierImpl extends AbstractIdentifierImpl {

    @Override
    public Principal identifier(Credential credential) {
        MultiCertSignCredential multiCertSignCredential = (MultiCertSignCredential) credential;
        String authRefId = (String) multiCertSignCredential.getExtending();
        // 主管理员登录者的验证
        AdminEntity adminInfo = this.identifier(multiCertSignCredential.getMainCredential(), null,null, authRefId);

        // 其它管理员的验证
        for(CertSignCredential otherCertSignCredential :multiCertSignCredential.getOtherCredentials()){
            this.identifier(otherCertSignCredential, adminInfo.getRoleCode(), adminInfo.getAdminGroup(), authRefId);
        }

        return buildPrincipal(adminInfo);
    }

    /**
     * 验证
     * @param certSignCredential
     * @param roleCode 主登录者的角色code
     */
    private AdminEntity identifier(CertSignCredential certSignCredential, String roleCode, String adminGroup, String authRefId){
        // 根据sn查找管理员证书实体
        AdminCertEntity adminCertEntity = adminCertRepository.getByCertSn(certSignCredential.getCertSn());
        // 找到管理员信息
        AdminEntity adminInfo = adminInfoRepository.getById(adminCertEntity.getAdminInfoId());

        // 其它登录者角色必须与主登录者角色保持一致
        if (StringUtils.isNotBlank(roleCode) && !roleCode.equals(adminInfo.getRoleCode())) {
            throw ManagementValidationError.OTHER_ADMIN_ROLE_INCONSISTENT.toException();
        }
        // 其它登录者组必须与主登录者组保持一致
        if (StringUtils.isNotBlank(adminGroup) && !adminGroup.equals(adminInfo.getAdminGroup())) {
            throw ManagementValidationError.OTHER_ADMIN_GROUP_INCONSISTENT.toException();
        }
        // 验证该管理员对应的角色以及完整性
        RoleEntity roleEntity = roleRepository.getRoleByCode(adminInfo.getRoleCode());
        CheckUtils.notNull(roleEntity, ManagementValidationError.ADMIN_ROLE_NOT_EXISTS.toException());

        CheckUtils.notNull(adminCertEntity, ManagementValidationError.ADMIN_NOT_EXISTS_ERROR.toException());
        // 检查当前证书状态是否允许登录
        CheckUtils.isTrue(CertStatus.allowToLogin(adminCertEntity.getStatus()), BaseValidationError.USER_CERT_STATUS_ERROR.toException());
        //签名验签
        verifySigned(certSignCredential.getSignData(), adminCertEntity.getSignCertValue(), certSignCredential.getHashAlgo(), authRefId);
        // 检查当前用户状态是否允许登录
        CheckUtils.isTrue(UserStatus.allowToLogin(adminInfo.getStatus()), BaseValidationError.USER_STATUS_ERROR.toException());

        return adminInfo;
    }

    @Override
    public boolean isSupport(Credential credential) {
        return credential instanceof MultiCertSignCredential;
    }

    @Override
    protected String getIdType() {
        return AuthnType.MULTI_CERT_SIGN.name();
    }
}
