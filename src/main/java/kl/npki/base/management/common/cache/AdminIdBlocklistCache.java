package kl.npki.base.management.common.cache;

import kl.npki.base.core.common.ICommonCache;

/**
 * 管理员ID黑名单缓存
 *
 * <AUTHOR> Name
 */
public enum AdminIdBlocklistCache implements ICommonCache {

    /**
     * 单例对象
     */
    INSTANCE;

    private static final String CACHE_ID = "base:adminId:blocklist";

    /**
     * 将管理员ID加入黑名单
     *
     * @param adminId    管理员ID
     * @param timeToLive 生存时间（毫秒）
     */
    public void add(String adminId, long timeToLive) {
        if (timeToLive > 0) {
            getClient().set(wrapCacheKey(adminId), 1, timeToLive);
        }
    }

    /**
     * 检查管理员ID是否在黑名单中
     *
     * @param adminId 管理员ID
     * @return 如果在黑名单中则返回true
     */
    public boolean isBlocklisted(String adminId) {
        return getClient().get(wrapCacheKey(adminId)) != null;
    }

    @Override
    public String getId() {
        return CACHE_ID;
    }
} 