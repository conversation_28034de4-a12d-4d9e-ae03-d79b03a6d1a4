package kl.npki.base.management.common.cache;

import kl.nbase.auth.utils.JwtHelper;
import kl.npki.base.core.common.ICommonCache;

import java.time.Instant;

/**
 * JWT黑名单缓存
 *
 * <AUTHOR> @since 2025/05/09 10:06
 */
public enum DenyJwtCache implements ICommonCache {

    /**
     * 单例对象
     */
    INSTANCE;

    private static final String CACHE_ID = "base:jwt:denylist";

    public void addToDenylist(String token) {
        // 考虑前端同时过来的两个请求，其中一个请求刷新了Token，另一个请求还使用旧的Token的情况

        // 这里需要将旧的Token加入黑名单，避免旧的Token被使用
        long expirationSeconds = JwtHelper.getExpirationFromToken(token) - Instant.now().getEpochSecond();
        if (expirationSeconds > 0) {
            getClient().set(wrapCacheKey(token), 1, expirationSeconds * 1000L);
        }
    }

    public boolean isDenylisted(String token) {
        return getClient().get(wrapCacheKey(token)) != null && !GraceJwtCache.INSTANCE.isInGracePeriod(token);
    }

    @Override
    public String getId() {
        return CACHE_ID;
    }
}
