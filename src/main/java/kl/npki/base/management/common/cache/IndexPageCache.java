package kl.npki.base.management.common.cache;

import kl.nbase.helper.utils.ResourceUtils;
import kl.npki.base.core.common.ICommonCache;
import kl.npki.base.management.exception.ManagementInternalError;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

import static kl.npki.base.management.constant.BaseManagementConstant.*;

/**
 * <AUTHOR>
 * @since 2025/7/3 下午5:05
 */
public enum IndexPageCache implements ICommonCache {

    /**
     * 单例对象
     */
    INSTANCE;

    private static final String CACHE_ID = "base:index.html";
    private static final String INDEX_HTML_KEY = "index.html";

    IndexPageCache() {
        // 初始化时不加载index.html，通过get方法获取时，如果不存在则加载
        // 兼容IDEA和WAR运行环境
    }

    public String get() {
        String indexHtml = getClient().get(wrapCacheKey(INDEX_HTML_KEY));
        if (StringUtils.isEmpty(indexHtml)) {
            // 如果获取不到说明当前租户的index.html未加载
            indexHtml = loadIndexHtml();
            getClient().set(wrapCacheKey(INDEX_HTML_KEY), indexHtml);
        }
        return indexHtml;
    }

    private String loadIndexHtml() {
        try {
            File indexFile = new File(INDEX_HTML_ABSOLUTELY_PATH);
            return indexFile.exists() ?
                    FileUtils.readFileToString(indexFile, StandardCharsets.UTF_8) :
                    ResourceUtils.readResource(INDEX_HTML_RELATIVE_PATH); // 兼容IDEA运行环境
        } catch (IOException e) {
            throw ManagementInternalError.FILE_OPERATE_ERROR.toException(e);
        }
    }

    @Override
    public String getId() {
        return CACHE_ID;
    }
}