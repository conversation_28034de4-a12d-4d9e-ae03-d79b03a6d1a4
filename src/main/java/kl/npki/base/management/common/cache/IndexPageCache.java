package kl.npki.base.management.common.cache;

import kl.nbase.helper.utils.ResourceUtils;
import kl.npki.base.core.common.ICommonCache;
import kl.npki.base.management.exception.ManagementInternalError;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

import static kl.npki.base.management.constant.BaseManagementConstant.*;

/**
 * <AUTHOR>
 * @since 2025/7/3 下午5:05
 */
public enum IndexPageCache implements ICommonCache {

    /**
     * 单例对象
     */
    INSTANCE;

    private static final String CACHE_ID = "base:index.html";
    private static final String INDEX_HTML_KEY = "index.html";

    IndexPageCache() {
        try {
            File indexFile = new File(INDEX_HTML_ABSOLUTELY_PATH);
            String indexHtml = indexFile.exists() ?
                    FileUtils.readFileToString(indexFile, StandardCharsets.UTF_8) :
                    ResourceUtils.readResource(INDEX_HTML_RELATIVE_PATH); // 兼容IDEA运行环境
            getClient().set(wrapCacheKey(INDEX_HTML_KEY), indexHtml);
        } catch (IOException e) {
            throw ManagementInternalError.FILE_OPERATE_ERROR.toException(e);
        }
    }

    public String get() {
        return getClient().get(wrapCacheKey(INDEX_HTML_KEY));
    }

    @Override
    public String getId() {
        return CACHE_ID;
    }
}