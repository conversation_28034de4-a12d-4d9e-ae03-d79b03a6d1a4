package kl.npki.base.management.common.auth;

import kl.nbase.auth.core.AuthnType;
import kl.nbase.auth.credential.Credential;
import kl.nbase.auth.credential.SslCredential;
import kl.nbase.auth.principal.Principal;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.constant.CertStatus;
import kl.npki.base.core.constant.UserStatus;
import kl.npki.base.core.exception.BaseValidationError;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import kl.npki.management.core.biz.admin.model.entity.RoleEntity;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;


/**
 * ssl 认证实现
 *
 * <AUTHOR>
 * @date 2023/2/24
 */
@Service
@DependsOn({"springRepositoryCollector"})
public class SslIdentifierImpl extends AbstractIdentifierImpl {

    /**
     * 认证
     *
     * @param credential 凭证
     * @return Principal 身份
     */
    @Override
    public Principal identifier(Credential credential) {
        // 如果是ssl站点登录的话，需要从request里面获取对应的cookie，并从cookie中获取对应的证书序列号
        SslCredential sslCredential = (SslCredential) credential;
        AdminCertEntity adminCert = adminCertRepository.getByCertSn(sslCredential.getCertSn());
        CheckUtils.notNull(adminCert, ManagementValidationError.ADMIN_NOT_EXISTS_ERROR.toException());
        // 检查当前证书状态是否允许登录
        CheckUtils.isTrue(CertStatus.allowToLogin(adminCert.getStatus()), BaseValidationError.USER_CERT_STATUS_ERROR.toException());
        AdminEntity adminInfo = adminInfoRepository.getById(adminCert.getAdminInfoId());
        // 验证该管理员对应的角色以及完整性
        RoleEntity roleEntity = roleRepository.getRoleByCode(adminInfo.getRoleCode());
        CheckUtils.notNull(roleEntity, ManagementValidationError.ADMIN_ROLE_NOT_EXISTS.toException());
        // 检查当前用户状态是否允许登录
        CheckUtils.isTrue(UserStatus.allowToLogin(adminInfo.getStatus()), BaseValidationError.USER_STATUS_ERROR.toException());
        return buildPrincipal(adminInfo);
    }

    @Override
    public boolean isSupport(Credential credential) {
        return credential instanceof SslCredential;
    }

    @Override
    protected String getIdType() {
        return AuthnType.SSL.name();
    }

}
