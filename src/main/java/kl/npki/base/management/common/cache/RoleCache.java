package kl.npki.base.management.common.cache;

import kl.npki.base.core.common.ICommonCache;
import kl.npki.management.core.biz.admin.model.entity.RoleEntity;
import kl.npki.management.core.constants.RoleType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色缓存
 * 用于缓存角色信息，提升角色查询性能
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
public enum RoleCache implements ICommonCache {

    /**
     * 单例对象
     */
    INSTANCE;

    private static final String CACHE_ID = "base:role";
    private static final String ALL_ROLES_KEY = "allRoles";

    /**
     * 缓存失效时间默认30分钟（毫秒）
     */
    private static final long TIME_TO_LIVE_MILLISECONDS = 30 * 60 * 1000L;

    private static final Logger LOG = LoggerFactory.getLogger(RoleCache.class);

    /**
     * 根据角色ID从缓存的角色列表中查找角色
     *
     * @param roleId 角色ID
     * @return 角色实体，如果缓存不存在或角色不存在则返回null
     */
    public RoleEntity getRoleById(Long roleId) {
        if (roleId == null) {
            return null;
        }
        
        List<RoleEntity> allRoles = getAllRoles();
        if (allRoles == null) {
            return null;
        }
        
        return allRoles.stream()
                .filter(role -> roleId.equals(role.getId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据角色编码从缓存的角色列表中查找角色
     *
     * @param roleCode 角色编码
     * @return 角色实体，如果缓存不存在或角色不存在则返回null
     */
    public RoleEntity getRoleByCode(String roleCode) {
        if (roleCode == null || roleCode.trim().isEmpty()) {
            return null;
        }
        
        List<RoleEntity> allRoles = getAllRoles();
        if (allRoles == null) {
            return null;
        }
        
        return allRoles.stream()
                .filter(role -> roleCode.equals(role.getRoleCode()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 缓存所有角色列表
     *
     * @param roles 角色列表
     */
    public void putAllRoles(List<RoleEntity> roles) {
        try {
            getClient().set(wrapCacheKey(ALL_ROLES_KEY), (Serializable) roles, TIME_TO_LIVE_MILLISECONDS);
            LOG.debug("All roles cached successfully, total {} records", 
                    roles != null ? roles.size() : 0);
        } catch (Exception e) {
            LOG.error("Failed to cache all roles", e);
        }
    }

    /**
     * 获取缓存的所有角色列表
     *
     * @return 角色列表，如果缓存不存在或异常则返回null
     */
    public List<RoleEntity> getAllRoles() {
        try {
            return getClient().get(wrapCacheKey(ALL_ROLES_KEY));
        } catch (Exception e) {
            LOG.error("Failed to get all roles from cache", e);
            return null;
        }
    }

    /**
     * 清除所有角色缓存
     */
    public void clearAllRoles() {
        try {
            getClient().delete(wrapCacheKey(ALL_ROLES_KEY));
            LOG.debug("All roles cache cleared successfully");
        } catch (Exception e) {
            LOG.error("Failed to clear all roles cache", e);
        }
    }

    /**
     * 检查角色列表缓存是否存在
     *
     * @return true如果缓存存在，false如果不存在
     */
    public boolean isCacheExists() {
        try {
            return getClient().get(wrapCacheKey(ALL_ROLES_KEY)) != null;
        } catch (Exception e) {
            LOG.error("Exception occurred while checking if roles cache exists", e);
            return false;
        }
    }

    @Override
    public String getId() {
        return CACHE_ID;
    }

    public List<RoleEntity> getRoleByParentRoleCode(String parentRoleCode) {
        if (parentRoleCode == null || parentRoleCode.trim().isEmpty()) {
            return null;
        }

        List<RoleEntity> allRoles = getAllRoles();
        if (allRoles == null) {
            return null;
        }

        return allRoles.stream()
                .filter(role -> parentRoleCode.equals(role.getParentRoleCode()))
                .collect(Collectors.toList());
    }

    public List<RoleEntity> getAllSystemRoles() {
        List<RoleEntity> allRoles = getAllRoles();
        if (allRoles == null) {
            return null;
        }

        // 过滤出系统角色
        return allRoles.stream()
                .filter(role -> role.getIsCustom().equals(RoleType.SYS_ROLE.getCode())) // 0表示系统角色
                .collect(Collectors.toList());
    }
}
