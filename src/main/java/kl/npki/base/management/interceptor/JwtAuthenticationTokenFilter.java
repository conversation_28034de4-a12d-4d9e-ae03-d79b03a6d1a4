package kl.npki.base.management.interceptor;

import kl.nbase.auth.authority.AllowListFactory;
import kl.nbase.auth.principal.Principal;
import kl.nbase.auth.token.AuthenticationToken;
import kl.nbase.auth.utils.AntPathMatcher;
import kl.nbase.auth.utils.JwtHelper;
import kl.nbase.helper.utils.WebUtils;
import kl.npki.base.core.biz.security.model.UrlRoleInfo;
import kl.npki.base.core.configs.NpkiServerConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.management.common.cache.DenyJwtCache;
import kl.npki.base.management.common.cache.GraceJwtCache;
import kl.npki.base.management.filter.StaticResourceFilter;
import kl.npki.base.management.service.impl.UrlRoleCacheService;
import kl.npki.base.management.utils.AuthzUtil;
import kl.npki.base.management.utils.JwtSecretKeyUtil;
import kl.npki.base.service.util.EnvironmentUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * JWT认证过滤器
 * 复用StaticResourceFilter的静态资源过滤功能，避免对允许的静态资源进行认证处理
 *
 * <AUTHOR>
 */
@Component
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {
    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationTokenFilter.class);
    private static final AntPathMatcher ANT_PATH_MATCHER = new AntPathMatcher();

    private final StaticResourceFilter staticResourceFilter;

    private final UrlRoleCacheService urlRoleCacheService;

    private final String apiPrefixPattern;

    public JwtAuthenticationTokenFilter(NpkiServerConfig npkiServerConfig, UrlRoleCacheService urlRoleCacheService, StaticResourceFilter staticResourceFilter) {
        this.urlRoleCacheService = urlRoleCacheService;
        this.staticResourceFilter = staticResourceFilter;
        // 获取API前缀配置，如果没有配置则默认为"/**"
        apiPrefixPattern = StringUtils.isNotBlank(npkiServerConfig.getRealApiPrefix()) ?
                npkiServerConfig.getRealApiPrefix() + "/**" : "/**";
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        // 复用StaticResourceFilter的静态资源过滤逻辑，如果是允许的静态资源则直接放行，不进行JWT认证
        if (staticResourceFilter.shouldTreatAsStaticResource(request, request.getRequestURI())) {
            filterChain.doFilter(request, response);
            return;
        }

        // 检查是否白名单，如果是则直接放行，不进行JWT认证
        String requestUrl = WebUtils.getOriginatingRequestUri(request);

        if (!ANT_PATH_MATCHER.match(apiPrefixPattern, requestUrl)) {
            // 如果请求不在API前缀下，则直接放行
            filterChain.doFilter(request, response);
            return;
        }

        List<String> allowList = AllowListFactory.getAllowList();
        if (CollectionUtils.isNotEmpty(allowList)) {
            for (String r : allowList) {
                if (ANT_PATH_MATCHER.match(r, requestUrl)) {
                    filterChain.doFilter(request, response);
                    return;
                }
            }
        }

        // 对非静态资源进行JWT认证处理
        handleJwtAuthentication(request, response);

        filterChain.doFilter(request, response);
    }

    /**
     * 处理JWT认证
     */
    private void handleJwtAuthentication(HttpServletRequest request, HttpServletResponse response) {
        String token = AuthzUtil.getAuthorization(request);

        if (token != null) {
            // 黑名单校验
            if (DenyJwtCache.INSTANCE.isDenylisted(token)) {
                return;
            }
            String username = null;
            Principal principal = null;
            try {
                // 解密、验证
                AuthenticationToken authenticationToken = JwtHelper.verifyToken(JwtSecretKeyUtil.getSecretKey(), token);
                principal = authenticationToken.getPrincipal();
                if (principal != null) {
                    username = principal.getId();
                }
            } catch (Exception e) {
                // Token 解析失败，可能是伪造的或已过期
                logger.warn("JWT token parse failed: {}", e.getMessage(), e);
            }

            if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                // 从Principal对象的角色和UrlRole缓存构造 UserDetails
                UserDetails userDetails = buildUserDetailsFromPrincipal(principal);
                UsernamePasswordAuthenticationToken authentication =
                    new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                SecurityContextHolder.getContext().setAuthentication(authentication);

                // 处理token刷新
                handleTokenRefresh(token, response);
            }
        }
    }

    private void handleTokenRefresh(String token, HttpServletResponse response) {
        if (GraceJwtCache.INSTANCE.isInGracePeriod(token)) {
            // 如果token在宽限期内，则不需要刷新token
            return;
        }
        long refreshTokenThreshold = BaseConfigWrapper.getLoginConfig().getRefreshTokenThreshold();
        String newToken = JwtHelper.getOrRefreshToken(JwtSecretKeyUtil.getSecretKey(), EnvironmentUtil.getTokenTimeByEnv(), refreshTokenThreshold, token);
        // token有变化则通知浏览器刷新token
        if (!StringUtils.equals(token, newToken)) {
            // 将旧的 token 添加到黑名单和宽限期名单
            long gracePeriod = BaseConfigWrapper.getLoginConfig().getGracePeriod();
            GraceJwtCache.INSTANCE.addToGraceList(token, gracePeriod);
            DenyJwtCache.INSTANCE.addToDenylist(token);
            AuthzUtil.setAuthorization(newToken, response);
        }
    }    /**
     * 从Principal对象构建UserDetails，使用UrlRole缓存构造权限
     *
     * @param principal Principal对象
     * @return UserDetails对象
     */
    private UserDetails buildUserDetailsFromPrincipal(Principal principal) {
        String userId = principal.getId();

        // 从Principal获取用户角色
        Set<String> userRoles = principal.getRoles();
        if (userRoles == null || userRoles.isEmpty()) {
            logger.warn("No roles found for user ID: {}", userId);
            userRoles = Collections.emptySet();
        }

        // 从UrlRole缓存构造权限列表
        List<GrantedAuthority> authorities = buildAuthoritiesFromCache(userRoles);

        logger.debug("Built UserDetails for user ID: {}, roles: {}, authorities count: {}",
                userId, userRoles, authorities.size());

        // 创建简单的UserDetails实现，不依赖数据库
        return new SimpleUserDetails(userId, authorities);
    }

    /**
     * 从UrlRole缓存和用户角色构造权限列表
     *
     * @param userRoles 用户角色集合
     * @return 权限列表
     */
    private List<GrantedAuthority> buildAuthoritiesFromCache(Set<String> userRoles) {
        try {
            List<UrlRoleInfo> urlRoleList = urlRoleCacheService.getUrlRoles();
            if (urlRoleList == null || urlRoleList.isEmpty()) {
                logger.warn("UrlRole cache is empty or unavailable, returning empty authorities");
                return Collections.emptyList();
            }

            // 过滤出包含用户角色的URL权限
            List<GrantedAuthority> authorities = urlRoleList.stream()
                .filter(urlRole -> urlRole.getRoleCodeList() != null &&
                    urlRole.getRoleCodeList().stream().anyMatch(userRoles::contains))
                .map(urlRole -> new SimpleGrantedAuthority(urlRole.getRequestMethod() + ":" + urlRole.getUrl()))
                .collect(Collectors.toList());

            logger.debug("Built {} authorities from cache for roles: {}", authorities.size(), userRoles);
            return authorities;
        } catch (Exception e) {
            logger.error("Failed to build authorities from cache for roles: {}", userRoles, e);            return Collections.emptyList();
        }
    }

    /**
     * 简单的UserDetails实现，用于JWT认证
     */
    private static class SimpleUserDetails implements UserDetails {
        private final String username;
        private final List<GrantedAuthority> authorities;

        public SimpleUserDetails(String username, List<GrantedAuthority> authorities) {
            this.username = username;
            this.authorities = authorities;
        }

        @Override
        public String getUsername() {
            return username;
        }

        @Override
        public String getPassword() {
            return "";  // JWT认证不需要密码
        }

        @Override
        public List<GrantedAuthority> getAuthorities() {
            return authorities;
        }

        @Override
        public boolean isAccountNonExpired() {
            return true;
        }

        @Override
        public boolean isAccountNonLocked() {
            return true;
        }

        @Override
        public boolean isCredentialsNonExpired() {
            return true;
        }

        @Override
        public boolean isEnabled() {
            return true;
        }
    }
}