package kl.npki.base.management.configurations;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;
import java.security.Security;

/**
 * <AUTHOR>
 * @since 2025/7/12 上午11:54
 */
@WebListener
public class SecurityServletContextListener implements ServletContextListener {
    @Override
    public void contextInitialized(ServletContextEvent sce) {
        // 初始化阶段注册 BC
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        // 应用卸载时清理 BC
        Security.removeProvider(BouncyCastleProvider.PROVIDER_NAME);
    }

}