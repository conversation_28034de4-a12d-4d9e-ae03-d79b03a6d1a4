package kl.npki.base.management.configurations;

import com.tongweb.springboot.starter.TongWebServletWebServerFactory;
import kl.npki.base.management.common.gmcustom.jetty.GmSslJettyServerCustomizer;
import kl.npki.base.management.common.gmcustom.tongweb.GmSslTongWebServerCustomizer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.embedded.jetty.JettyServerCustomizer;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

/**
 * <AUTHOR>
 */
@Configuration
@DependsOn({"fileConfigService"})
public class MgmtServerAutoConfiguration extends AbstractServerProvider{

//
//    @ConditionalOnProperty(name = "server.ssl.enabled", havingValue = "true")
//    class MgmtHttpsConfiguration {
//
//        @Bean
//        @ConditionalOnProperty(prefix = "server", name = "embed-name", havingValue = "jetty")
//        public JettyServerCustomizer getManagerHttpsJettyServerCustomizer() {
//            int port = serverProperties.getPort();
//            return new GmSslJettyServerCustomizer(serverProperties, port);
//        }
//
//        @Bean
//        @ConditionalOnProperty(prefix = "server", name = "embed-name", havingValue = "tongweb")
//        public WebServerFactoryCustomizer<TongWebServletWebServerFactory> getManagerHttpsTongWebServerCustomizer() {
//            int port = serverProperties.getPort();
//            return new GmSslTongWebServerCustomizer(serverProperties, port);
//        }
//    }

}
