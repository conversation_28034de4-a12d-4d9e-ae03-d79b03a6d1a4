package kl.npki.base.management.configurations;

import kl.nbase.gmssl.jsse.KlSslProvider;
import kl.nbase.security.jce.provider.BouncyCastleProvider;
import kl.npki.base.core.configs.ManagementSslConfig;
import org.springframework.boot.autoconfigure.web.ServerProperties;

import javax.annotation.Resource;
import java.security.Security;

/**
 * <AUTHOR>
 */
public abstract class AbstractServerProvider {
    @Resource
    ServerProperties serverProperties;

    @Resource
    ManagementSslConfig managementSslConfig;

    static {
        if (null == Security.getProvider(BouncyCastleProvider.PROVIDER_NAME)) {
            Security.addProvider(new BouncyCastleProvider());
        }
        if (null == Security.getProvider(KlSslProvider.PROVIDER_NAME)) {
            Security.addProvider(new KlSslProvider());
        }
        Security.removeProvider("SunEC");
    }
}
