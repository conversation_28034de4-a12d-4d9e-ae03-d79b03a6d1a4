package kl.npki.base.management.configurations;

import com.tongweb.springboot.starter.TongWebServer;
import com.tongweb.springboot.starter.TongWebServletWebServerFactory;
import org.eclipse.jetty.server.Server;
import org.eclipse.jetty.util.Loader;
import org.eclipse.jetty.webapp.WebAppContext;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.boot.autoconfigure.condition.*;
import org.springframework.boot.autoconfigure.web.reactive.ReactiveWebServerFactoryAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration;
import org.springframework.boot.web.embedded.jetty.JettyServerCustomizer;
import org.springframework.boot.web.embedded.jetty.JettyServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Servlet;
import java.util.stream.Collectors;

/**
 * 自定义的Servlet Web服务器工厂配置。
 * 根据server.embed.name的值来决定使用Jetty还是TongWeb作为嵌入式Web服务器。
 *
 * <AUTHOR>
 * @date 2023/8/14
 */
@Configuration
@AutoConfigureOrder(-2147483648)
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@AutoConfigureBefore({ServletWebServerFactoryAutoConfiguration.class, ReactiveWebServerFactoryAutoConfiguration.class})
public class WebServerFactoryAutoConfiguration {

    /**
     * Nested configuration if Jetty is being used.
     */
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnProperty(prefix = "server", name = "embed-name", havingValue = "jetty")
    @ConditionalOnClass({Servlet.class, Server.class, Loader.class, WebAppContext.class})
    @ConditionalOnMissingBean(value = ServletWebServerFactory.class, search = SearchStrategy.CURRENT)
    static class EmbeddedJetty {

        @Bean
        JettyServletWebServerFactory jettyServletWebServerFactory(
            ObjectProvider<JettyServerCustomizer> serverCustomizers) {
            JettyServletWebServerFactory factory = new JettyServletWebServerFactory();
            factory.getServerCustomizers().addAll(serverCustomizers.orderedStream().collect(Collectors.toList()));
            return factory;
        }

    }

    /**
     * 嵌入式TongWeb的配置类。
     */
    @Configuration
    @ConditionalOnProperty(prefix = "server", name = "embed-name", havingValue = "tongweb")
    @ConditionalOnClass({Servlet.class, TongWebServer.class, com.tongweb.connector.UpgradeProtocol.class})
    public static class EmbeddedTongWeb {

        /**
         * 创建TongWebServletWebServerFactory的Bean。
         *
         * @return TongWebServletWebServerFactory的实例
         */
        @Bean
        public TongWebServletWebServerFactory tongWebServletWebServerFactory() {
            return new TongWebServletWebServerFactory();
        }
    }

}