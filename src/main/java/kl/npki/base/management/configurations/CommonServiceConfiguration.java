package kl.npki.base.management.configurations;

import kl.nbase.bean.convert.ConvertService;
import kl.nbase.timer.client.ITimerClient;
import kl.npki.base.core.biz.cert.service.ICertExportService;
import kl.npki.base.core.biz.cert.service.ITrustCertService;
import kl.npki.base.core.biz.cert.service.impl.CertExportServiceImpl;
import kl.npki.base.core.biz.cert.service.impl.TrustCertServiceImpl;
import kl.npki.base.core.biz.check.ISelfCheckItem;
import kl.npki.base.core.biz.check.handler.SelfCheckResultRepository;
import kl.npki.base.core.biz.check.handler.impl.InMemoryCheckResultRepositoryImpl;
import kl.npki.base.core.biz.config.service.IConfigStatusService;
import kl.npki.base.core.biz.db.service.IDbSwitchConfigService;
import kl.npki.base.core.biz.home.service.SelfCheckStatisticService;
import kl.npki.base.core.biz.home.service.impl.SelfCheckStatisticServiceImpl;
import kl.npki.base.core.biz.license.service.IExternalLicenseCheck;
import kl.npki.base.core.biz.license.service.ILicenseCountService;
import kl.npki.base.core.biz.license.service.ILicenseService;
import kl.npki.base.core.biz.license.service.impl.LicenseConfigListener;
import kl.npki.base.core.biz.license.service.impl.LicenseServiceImpl;
import kl.npki.base.core.biz.log.service.IApiLogService;
import kl.npki.base.core.biz.log.service.IOpLogService;
import kl.npki.base.core.biz.log.service.impl.ApiLogServiceImpl;
import kl.npki.base.core.biz.log.service.impl.OpLogServiceImpl;
import kl.npki.base.core.biz.mainkey.service.MainKeyManager;
import kl.npki.base.core.biz.metrics.service.CertMonitor;
import kl.npki.base.core.biz.org.service.IOrgService;
import kl.npki.base.core.biz.org.service.impl.OrgServiceImpl;
import kl.npki.base.core.configs.LicenseConfig;
import kl.npki.base.management.common.metrics.AdminCertMonitorImpl;
import kl.npki.management.core.biz.check.*;
import kl.npki.management.core.service.AdminMgrService;
import kl.npki.management.core.service.SecurityService;
import kl.npki.management.core.service.backup.CombinedBackupRestoreService;
import kl.npki.management.core.service.backup.IBackupRestoreService;
import kl.npki.management.core.service.backup.impl.CertBackupRestoreServiceImpl;
import kl.npki.management.core.service.backup.impl.ConfigBackupRestoreServiceImpl;
import kl.npki.management.core.service.backup.impl.EngineBackupRestoreServiceImpl;
import kl.npki.management.core.service.cert.IIdCertService;
import kl.npki.management.core.service.cert.IKeyIndexService;
import kl.npki.management.core.service.cert.ISslCertService;
import kl.npki.management.core.service.cert.impl.IdCertServiceImpl;
import kl.npki.management.core.service.cert.impl.KeyIndexServiceImpl;
import kl.npki.management.core.service.cert.impl.SslCertServiceImpl;
import kl.npki.management.core.service.config.*;
import kl.npki.management.core.service.help.SysHelpService;
import kl.npki.management.core.service.help.SysHelpTxtServiceImpl;
import kl.npki.management.core.service.permission.PermissionService;
import kl.npki.management.core.service.permission.impl.PermissionServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.lang.Nullable;

import javax.sql.DataSource;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * 加载配置类到容器，先后顺序为页面显示顺序
 */
@Configuration
@DependsOn({"springRepositoryCollector"})
public class CommonServiceConfiguration {

    /**
     * logger
     **/
    private static final Logger logger = LoggerFactory.getLogger(CommonServiceConfiguration.class);

    @Bean
    public LogConfigService getLogConfigService() {
        return new LogConfigService();
    }

    @Bean
    public LoginTypeConfigService getLoginTypeConfigService() {
        return new LoginTypeConfigService();
    }

    @Bean
    public RegionAndLanguageConfigService getRegionAndLanguageConfigService() {
        return new RegionAndLanguageConfigService();
    }

    @Bean
    public DbConfigService getDbConfigService(IDbSwitchConfigService dbSwitchConfigService) {
        return new DbConfigService(dbSwitchConfigService);
    }

    @Bean
    public EngineConfigService getEngineConfigService() {
        return new EngineConfigService();
    }

    @Bean
    public CacheConfigService getCacheConfigService() {
        return new CacheConfigService();
    }

    @Bean
    @ConditionalOnMissingBean
    public MainKeyManager getMainKeyManager() {
        return MainKeyManager.getInstance();
    }

    @Bean
    public ITrustCertService getTrustCertService() {
        return new TrustCertServiceImpl();
    }

    @Bean
    public ISelfCheckItem adminCertValidityCheckItem() {
        return new AdminCertValidityPeriodCheckItem();
    }

    @Bean
    public ISelfCheckItem identityCertValidityCheckItem() {
        return new IdentityCertValidityPeriodCheckItem();
    }

    @Bean
    public ISelfCheckItem sslCertValidityCheckItem() {
        return new SslCertValidityPeriodCheckItem();
    }

    @Bean
    public ISelfCheckItem mgtRootCertValidityCheckItem() {
        return new MgtRootCertValidityPeriodCheckItem();
    }

    @Bean
    public ISelfCheckItem adminIssueIntegrityCheckItem() {
        return new AdminIssueIntegrityCheckItem();
    }

    @Bean
    public ISelfCheckItem timerJobCheckItem(ITimerClient timerClient) {
        return new TimerJobCheckItem(timerClient);
    }

    @Bean
    public ISelfCheckItem emEngineConfigCheckItem() {
        return new EmEngineServiceCheckItem();
    }

    @Bean
    public ISelfCheckItem licenseValidityPeriodCheckItem() {
        return new LicenseValidityPeriodCheckItem();
    }

    @Bean
    public ISelfCheckItem databaseServiceCheckItem(DataSource dataSource) {
        return new DatabaseServiceCheckItem(dataSource);
    }

    @Bean
    public ISelfCheckItem diskSpaceCheckItem() {
        return new DiskSpaceCheckItem();
    }

    @Bean
    public ISelfCheckItem jvmMemoryCheckItem() {
        return new JvmMemoryCheckItem();
    }

    @Bean
    public SelfCheckResultRepository checkResultRepository() {
        return new InMemoryCheckResultRepositoryImpl();
    }

    @Bean
    public CertBackupRestoreServiceImpl getCertBackupRestoreServiceImpl() {
        return new CertBackupRestoreServiceImpl();
    }

    @Bean
    public ConfigBackupRestoreServiceImpl getConfigBackupRestoreServiceImpl() {
        return new ConfigBackupRestoreServiceImpl();
    }

    @Bean
    public EngineBackupRestoreServiceImpl getEngineBackupRestoreServiceImpl() {
        return new EngineBackupRestoreServiceImpl();
    }

    @Bean
    public CombinedBackupRestoreService getCombinedBackupRestoreService(List<IBackupRestoreService> backupRestoreServices) {
        return new CombinedBackupRestoreService(backupRestoreServices);
    }

    @Bean
    public AdminMgrService getAdminMgrService() {
        return new AdminMgrService();
    }

    @Bean
    public SecurityService securityService() {
        return new SecurityService();
    }

    @Bean
    public IIdCertService getIdCertService() {
        return new IdCertServiceImpl();
    }

    @Bean
    public ServerCertConfigStatusService getServerCertConfigStatusService() {
        return new ServerCertConfigStatusService();
    }

    @Bean
    public ISslCertService getSSLCertService() {
        return new SslCertServiceImpl();
    }

    @Bean
    @DependsOn("licenseConfig")
    public ILicenseService licenseService(@Nullable IExternalLicenseCheck externalLicenseCheck,
                                          @Nullable ILicenseCountService licenseCountService,
                                          LicenseConfig licenseConfig) {
        if (Objects.nonNull(externalLicenseCheck)) {
            LicenseConfigListener.setExternalLicenseCheck(externalLicenseCheck);
        }
        LicenseServiceImpl licenseService = new LicenseServiceImpl(externalLicenseCheck, licenseCountService, licenseConfig);
        if (StringUtils.isBlank(licenseConfig.getLicense())) {
            logger.warn("License is empty");
            return licenseService;
        }
        try {
            licenseService.importLicense(licenseConfig);
        } catch (Exception e) {
            logger.error("Import license failed! ", e);
        }
        return licenseService;
    }

    @Bean
    @DependsOn("licenseService")
    public SystemConfigService getSystemConfigService(Set<IConfigStatusService> configStatusServiceList, IDbSwitchConfigService dbSwitchConfigService, PermissionService permissionService) {
        return new SystemConfigService(configStatusServiceList, dbSwitchConfigService, permissionService);
    }


    @Bean
    public SysHelpService sysHelpService() {
        return new SysHelpTxtServiceImpl();
    }

    @Bean
    public IKeyIndexService keyIndexService() {
        return new KeyIndexServiceImpl();
    }

    @Bean
    public BizOperInitStatusService bizOperCertStatusService() {
        return new BizOperInitStatusService();
    }

    @Bean
    public AuditOperInitStatusService auditOperCertStatusService() {
        return new AuditOperInitStatusService();
    }

    @Bean
    public CryptoAlgoConfigService algoConfigService() {
        return new CryptoAlgoConfigService();
    }

    @Bean
    @DependsOn({"springRepositoryCollector"})
    public SelfCheckStatisticService getSelfCheckStatisticService() {
        return new SelfCheckStatisticServiceImpl();
    }

    @Bean
    public PermissionService permissionService() {
        return new PermissionServiceImpl();
    }

    @Bean
    public CertMonitor adminCertMonitor() {
        return new AdminCertMonitorImpl();
    }

    @Bean
    public IOpLogService getOpLogService() {
        return new OpLogServiceImpl();
    }

    @Bean
    public IApiLogService getApiLogService() {
        return new ApiLogServiceImpl();
    }

    @Bean
    @DependsOn({"springRepositoryCollector"})
    public IOrgService getOrgService(ConvertService convertService) {
        return new OrgServiceImpl(convertService);
    }

    @Bean
    public ICertExportService getCertExportService() {
        return new CertExportServiceImpl();
    }
}
