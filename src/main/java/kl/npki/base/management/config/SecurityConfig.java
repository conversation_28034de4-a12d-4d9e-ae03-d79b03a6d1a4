package kl.npki.base.management.config;

import kl.npki.base.core.configs.NpkiServerConfig;
import kl.npki.base.management.filter.StaticResourceFilter;
import kl.npki.base.management.interceptor.JwtAuthenticationTokenFilter;
import kl.npki.base.management.repository.impl.ResourceRepositoryImpl;
import kl.npki.base.management.security.CustomAuthorizationManager;
import kl.npki.base.management.security.RestAccessDeniedHandler;
import kl.npki.base.management.security.RestAuthenticationEntryPoint;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import javax.annotation.Resource;

/**
 * Spring Security 配置
 * 复用StaticResourceFilter的静态资源过滤功能，确保静态资源访问策略的一致性
 *
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig {
    @Resource
    private JwtAuthenticationTokenFilter jwtAuthenticationTokenFilter;
    @Resource
    private NpkiServerConfig npkiServerConfig;
    @Resource
    private StaticResourceFilter staticResourceFilter;
    @Resource
    private RestAuthenticationEntryPoint authenticationEntryPoint;
    @Resource
    private RestAccessDeniedHandler accessDeniedHandler;
    @Resource
    private CustomAuthorizationManager customAuthorizationManager;
    @Resource
    private ResourceRepositoryImpl unifiedResourceRepository;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            // 关闭 CSRF
            .csrf().disable()
            // 异常处理
            .exceptionHandling()
            .authenticationEntryPoint(authenticationEntryPoint).accessDeniedHandler(accessDeniedHandler)
            .and()
            // 不通过 Session 获取 SecurityContext
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            // 替换为 authorizeHttpRequests() 并使用新的 API
            .authorizeHttpRequests()
            // 复用StaticResourceFilter的静态资源过滤逻辑，不需要认证
            .antMatchers(staticResourceFilter.getStaticResourcePatterns()).permitAll()
            // 配置白名单
            .antMatchers(unifiedResourceRepository.getAllowList().toArray(new String[0])).permitAll()
            // 除上面外的所有前缀匹配npkiServerConfig.getApiPrefix()的请求全部需要鉴权认证
            .antMatchers(npkiServerConfig.getRealApiPrefix() + "/**").access(customAuthorizationManager)
            // 其他所有请求都放行
            .anyRequest().permitAll();

        // 禁用HTTP响应标头
        http.headers().cacheControl();

        // 添加JWT filter
        http.addFilterBefore(jwtAuthenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);

        // 禁用form表单登录
        http.formLogin().disable();
        // 禁用basic认证
        http.httpBasic().disable();

        return http.build();
    }

}
