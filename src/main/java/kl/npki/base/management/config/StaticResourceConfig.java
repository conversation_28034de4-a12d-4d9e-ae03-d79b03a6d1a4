package kl.npki.base.management.config;

import org.springframework.util.AntPathMatcher;

/**
 * 静态资源配置工具类
 * 统一管理前端静态资源路径模式，避免在多个配置类中重复定义
 *
 * <AUTHOR>
 */
public final class StaticResourceConfig {

    private StaticResourceConfig() {
        // 工具类，不允许实例化
    }

    /**
     * 前端静态资源路径模式
     * 这些路径不需要进行认证和权限验证
     */
    public static final String[] PATTERNS = {
        "/*",           // 根路径下的文件，如favicon.ico, index.html等
        "/npki/**",     // npki前端资源目录
        "/img/**",      // 图片资源目录
        "/css/**",      // CSS样式文件目录
        "/js/**",       // JavaScript文件目录
        "/static/**",   // 静态资源目录
        "/favicon.ico", // 网站图标
        "/favicon.png", // 网站图标PNG格式
        "/loading.gif", // 加载动画
        "/assets/**"    // 前端构建后的资源目录
    };

    private static final AntPathMatcher PATH_MATCHER = new AntPathMatcher();

    /**
     * 检查请求路径是否为静态资源
     *
     * @param requestPath 请求路径
     * @return true如果是静态资源，false否则
     */
    public static boolean isStaticResource(String requestPath) {
        for (String pattern : PATTERNS) {
            if (PATH_MATCHER.match(pattern, requestPath)) {
                return true;
            }
        }
        return false;
    }
}
