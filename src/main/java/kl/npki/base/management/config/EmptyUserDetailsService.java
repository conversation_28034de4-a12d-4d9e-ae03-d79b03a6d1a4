package kl.npki.base.management.config;

import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Service
public class EmptyUserDetailsService implements UserDetailsService {
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 消除启动警告
        // Spring Boot 检测到没有 UserDetailsService，会自动创建一个内置用户并输出警告。实现后可消除该警告。
        throw new UsernameNotFoundException("Unsupported operation: EmptyUserDetailsService does not support loading users by username.");
    }
}