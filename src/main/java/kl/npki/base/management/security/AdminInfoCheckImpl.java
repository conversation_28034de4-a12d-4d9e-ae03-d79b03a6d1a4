package kl.npki.base.management.security;

import kl.npki.base.core.common.manager.MgrHolder;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.management.constant.I18nConstant;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.service.common.security.detection.IUserInfoCheckServer;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import kl.npki.management.core.biz.admin.service.AdminMgr;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/7/2 下午1:07
 */
@Service
public class AdminInfoCheckImpl implements IUserInfoCheckServer {

    @Override
    public void checkUserInfo(Long userId, String userName) {
        if (!SystemUtil.isDeployed()) {
            // 如果系统还未部署完成，不需要做安全检测
            return;
        }

        // 若用户ID和用户名均为空，视为未登录或无需校验
        if (userId == null && StringUtils.isBlank(userName)) {
            return;
        }

        //单点登录无需校验
        if(SystemUtil.isSSO()){
            return;
        }

        // 获取当前租户下的管理员管理器实例
        AdminMgr adminMgr = MgrHolder.getCommonTenantObj(AdminMgr.class);

        // 根据ID和用户名分别获取对应的管理员实体，并进行合法性校验
        AdminEntity adminById  = getEntityById(adminMgr, userId);
        AdminEntity adminByName  = getEntityByName(adminMgr, userName);

        // 若两者都存在，但不匹配，说明ID与用户名不一致
        if (null != adminById
                && null != adminByName
                && !adminById.getId().equals(adminByName.getId())) {
            // 代码走到这里说明被攻击了，正常情况是不会走到这里的
            throw ManagementValidationError.ADMIN_INFO_ERROR.toException(
                    I18nConstant.getBaseMgmtI18nMessage(I18nConstant.ADMIN_NAME_ID_ERROR_I18N_KEY, userId, userName));
        }
    }

    /**
     * 根据用户ID获取管理员信息并校验合法性。
     *
     * @param adminMgr 管理员管理器
     * @param userId   用户ID
     * @return 对应的管理员实体，若ID为空则返回null
     * @throws IllegalArgumentException 若ID不为空但无对应管理员
     */
    private AdminEntity getEntityById(AdminMgr adminMgr, Long userId) {
        if (userId == null) {
            return null;
        }

        // 如果userId不为空，但是根据ID未找到对应的管理员实体，则抛出异常
        return adminMgr.getAdminInfoMap().values().stream()
                .filter(admin -> userId.equals(admin.getId()))
                .findFirst()
                .orElseThrow(() -> ManagementValidationError.ADMIN_INFO_ERROR.toException(
                        I18nConstant.getBaseMgmtI18nMessage(I18nConstant.ADMIN_ID_ERROR_I18N_KEY, String.valueOf(userId))));
    }

    /**
     * 根据用户名查找管理员信息并校验合法性。
     *
     * @param adminMgr 管理员管理器
     * @param userName 用户名
     * @return 对应的管理员实体，若用户名为空则返回null
     * @throws IllegalArgumentException 若用户名不为空但未找到对应管理员
     */
    private AdminEntity getEntityByName(AdminMgr adminMgr, String userName) {
        if (StringUtils.isBlank(userName)) {
            return null;
        }

        // 如果userName不为空，但是根据用户名未找到对应的管理员实体，则抛出异常
        return adminMgr.getAdminInfoMap().values().stream()
                .filter(admin -> userName.equals(admin.getUsername()))
                .findFirst()
                .orElseThrow(() -> ManagementValidationError.ADMIN_INFO_ERROR.toException(
                        I18nConstant.getBaseMgmtI18nMessage(I18nConstant.ADMIN_NAME_ERROR_I18N_KEY, userName)));
    }

}