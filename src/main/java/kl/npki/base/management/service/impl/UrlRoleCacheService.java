package kl.npki.base.management.service.impl;

import kl.npki.base.core.biz.security.model.UrlRoleInfo;
import kl.npki.base.management.common.cache.UrlRoleCache;
import kl.npki.management.core.repository.IRoleRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * URL角色缓存服务
 * 提供URL与角色权限关系的缓存管理功能
 *
 * <AUTHOR>
 * @since 2025/06/19
 */
@Service
public class UrlRoleCacheService {

    private static final Logger LOG = LoggerFactory.getLogger(UrlRoleCacheService.class);

    @Resource
    private IRoleRepository roleRepository;

    /**
     * 获取URL角色列表，优先从缓存获取，缓存不存在时从数据库加载并缓存
     *
     * @return URL角色信息列表
     */
    public List<UrlRoleInfo> getUrlRoles() {
        // 先尝试从缓存获取
        List<UrlRoleInfo> urlRoleList = UrlRoleCache.INSTANCE.getUrlRoleList();
        if (urlRoleList != null) {
            LOG.debug("Successfully retrieved URL role list from cache, total {} records", urlRoleList.size());
            return urlRoleList;
        }

        // 缓存不存在，从数据库加载
        LOG.debug("URL role list not found in cache, loading from database");
        urlRoleList = loadUrlRolesFromDatabase();

        // 加载成功后缓存结果
        if (urlRoleList != null) {
            UrlRoleCache.INSTANCE.putUrlRoleList(urlRoleList);
            LOG.info("URL role list loaded from database and cached, total {} records", urlRoleList.size());
        }

        return urlRoleList;
    }

    /**
     * 获取指定租户的URL角色列表
     *
     * @param tenantId 租户ID
     * @return URL角色信息列表
     */
    public List<UrlRoleInfo> getUrlRoles(String tenantId) {
        // 先尝试从缓存获取
        List<UrlRoleInfo> urlRoleList = UrlRoleCache.INSTANCE.getUrlRoleList(tenantId);
        if (urlRoleList != null) {
            LOG.debug("Successfully retrieved URL role list from cache for tenant [{}], total {} records", tenantId,
                urlRoleList.size());
            return urlRoleList;
        }

        // 缓存不存在，从数据库加载
        LOG.debug("URL role list not found in cache for tenant [{}], loading from database", tenantId);
        urlRoleList = loadUrlRolesFromDatabase();

        // 加载成功后缓存结果
        if (urlRoleList != null) {
            UrlRoleCache.INSTANCE.putUrlRoleList(tenantId, urlRoleList);
            LOG.info("URL role list loaded from database and cached for tenant [{}], total {} records", tenantId,
                urlRoleList.size());
        }

        return urlRoleList;
    }

    /**
     * 强制刷新URL角色缓存
     * 从数据库重新加载最新的URL角色关系并更新缓存
     *
     * @return 刷新后的URL角色信息列表
     */
    public List<UrlRoleInfo> refreshUrlRolesCache() {
        LOG.info("Starting to refresh URL role cache");

        // 清除现有缓存
        UrlRoleCache.INSTANCE.clearUrlRoleList();

        // 从数据库重新加载
        List<UrlRoleInfo> urlRoleList = loadUrlRolesFromDatabase();

        // 重新缓存
        if (urlRoleList != null) {
            UrlRoleCache.INSTANCE.putUrlRoleList(urlRoleList);
            LOG.info("URL role cache refresh completed, total {} records", urlRoleList.size());
        } else {
            LOG.warn("URL role cache refresh failed, unable to get data from database");
        }

        return urlRoleList;
    }

    /**
     * 强制刷新指定租户的URL角色缓存
     *
     * @param tenantId 租户ID
     * @return 刷新后的URL角色信息列表
     */
    public List<UrlRoleInfo> refreshUrlRolesCache(String tenantId) {
        LOG.info("Starting to refresh URL role cache for tenant [{}]", tenantId);

        // 清除现有缓存
        UrlRoleCache.INSTANCE.clearUrlRoleList(tenantId);

        // 从数据库重新加载
        List<UrlRoleInfo> urlRoleList = loadUrlRolesFromDatabase();

        // 重新缓存
        if (urlRoleList != null) {
            UrlRoleCache.INSTANCE.putUrlRoleList(tenantId, urlRoleList);
            LOG.info("URL role cache refresh completed for tenant [{}], total {} records", tenantId,
                urlRoleList.size());
        } else {
            LOG.warn("URL role cache refresh failed for tenant [{}], unable to get data from database", tenantId);
        }

        return urlRoleList;
    }

    /**
     * 清除URL角色缓存
     */
    public void clearUrlRolesCache() {
        UrlRoleCache.INSTANCE.clearUrlRoleList();
        LOG.info("URL role cache has been cleared");
    }

    /**
     * 清除指定租户的URL角色缓存
     *
     * @param tenantId 租户ID
     */
    public void clearUrlRolesCache(String tenantId) {
        UrlRoleCache.INSTANCE.clearUrlRoleList(tenantId);
        LOG.info("URL role cache has been cleared for tenant [{}]", tenantId);
    }

    /**
     * 检查缓存状态
     *
     * @return true如果缓存存在并且有效，false如果缓存不存在或失效
     */
    public boolean isCacheAvailable() {
        return UrlRoleCache.INSTANCE.isCacheExists();
    }

    /**
     * 检查指定租户的缓存状态
     *
     * @param tenantId 租户ID
     * @return true如果缓存存在并且有效，false如果缓存不存在或失效
     */
    public boolean isCacheAvailable(String tenantId) {
        return UrlRoleCache.INSTANCE.isCacheExists(tenantId);
    }

    /**
     * 从数据库加载URL角色数据
     *
     * @return URL角色信息列表
     */
    private List<UrlRoleInfo> loadUrlRolesFromDatabase() {
        try {
            return roleRepository.listUrlRoles();
        } catch (Exception e) {
            LOG.error("Failed to load URL role list from database", e);
            return null;
        }
    }
}
