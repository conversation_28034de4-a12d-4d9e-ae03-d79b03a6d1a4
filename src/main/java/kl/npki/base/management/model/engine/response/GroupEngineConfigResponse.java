package kl.npki.base.management.model.engine.response;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.configs.ClusterEmConfig;
import kl.npki.base.core.configs.EmConfig;
import kl.npki.base.core.configs.GroupEmConfig;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 加密机组配置请求类
 *
 * <AUTHOR>
 */
public class GroupEngineConfigResponse {

    @Schema(description = "组名")
    private String groupName;

    @Schema(description = "组内加密机配置")
    private List<EngineConfigResponse> engineConfigs;

    @Schema(description = "辅助加密机配置")
    private EngineConfigResponse backupEngineConfig;

    @Schema(description = "是否启用辅助密码机")
    private boolean enableBackupEngine;


    public GroupEngineConfigResponse(GroupEmConfig groupEmConfig) {
        this.groupName = groupEmConfig.getGroupName();
        this.engineConfigs = new ArrayList<>();
        EmConfig backupEngine = groupEmConfig.getBackupEngine();
        this.enableBackupEngine = groupEmConfig.isEnableBackupEngine();
        if (backupEngine != null) {
            this.backupEngineConfig = new EngineConfigResponse(backupEngine);
        }
        List<EmConfig> engines = groupEmConfig.getEngines();
        if (CollectionUtils.isNotEmpty(engines)) {
            for (EmConfig emConfig : engines) {
                this.engineConfigs.add(new EngineConfigResponse(emConfig));
            }
        }
    }

    public GroupEngineConfigResponse(ClusterEmConfig clusterEmConfig) {
        this((clusterEmConfig != null && clusterEmConfig.getGroupEngine() != null) ?
            // 默认取第一个组
            clusterEmConfig.getGroupEngine() :
            new GroupEmConfig());
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public List<EngineConfigResponse> getEngineConfigs() {
        return engineConfigs;
    }

    public void setEngineConfigs(List<EngineConfigResponse> engineConfigs) {
        this.engineConfigs = engineConfigs;
    }

    public EngineConfigResponse getBackupEngineConfig() {
        return backupEngineConfig;
    }

    public GroupEngineConfigResponse setBackupEngineConfig(EngineConfigResponse backupEngineConfig) {
        this.backupEngineConfig = backupEngineConfig;
        return this;
    }

    public boolean isEnableBackupEngine() {
        return enableBackupEngine;
    }

    public GroupEngineConfigResponse setEnableBackupEngine(boolean enableBackupEngine) {
        this.enableBackupEngine = enableBackupEngine;
        return this;
    }

}
