package kl.npki.base.management.model.batch;

import kl.nbase.bean.convert.ConvertService;
import kl.npki.base.core.biz.batch.model.BaseBatchInfo;

import javax.annotation.Resource;
import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.List;

/**
 * 抽象批量请求转化
 *
 * <AUTHOR> rs
 * @date 2024/7/5 17:47
 */
public abstract class BatchRequestConvert<T extends BaseBatchRequest, S extends BaseBatchInfo> {

    @Resource
    private ConvertService convertService;

    public List<S> convert(T request) {
        ArrayList<S> dataList = new ArrayList<>();
        request.getIdList().stream().distinct().forEach(id -> {
            S data = null;
            if (getBatchInfoType().isAssignableFrom(BaseBatchInfo.class)) {
                //直接创建BaseBatchInfo
                data = (S) new BaseBatchInfo();
            } else {
                //通过转化器转化
                data = convertService.convert(request, getBatchInfoType());
            }
            data.setId(id);
            dataList.add(data);
        });
        return dataList;
    }

    /**
     * 获取批量信息泛型对应的Class
     *
     * @return
     */
    protected Class<S> getBatchInfoType() {
        return (Class<S>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
    }
}
