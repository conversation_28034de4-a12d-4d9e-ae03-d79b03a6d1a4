package kl.npki.base.management.model.log.response;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 服务日志返回响应(列表展示使用)
 *
 * <AUTHOR>
 * @date 2022/12/13
 */
public class ApiLogListResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "服务日志id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long logId;

    @Schema(description = "客户端IP")
    private String clientIp;

    @Schema(description = "请求的业务")
    private String biz;

    @Schema(description = "请求时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime logWhen;

    @Schema(description = "请求结果")
    private Boolean result;

    @Schema(description = "终端标识")
    private String callerName;


    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getBiz() {
        return biz;
    }

    public void setBiz(String biz) {
        this.biz = biz;
    }

    public LocalDateTime getLogWhen() {
        return logWhen;
    }

    public void setLogWhen(LocalDateTime logWhen) {
        this.logWhen = logWhen;
    }

    public Boolean getResult() {
        return result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    public String getCallerName() {
        return callerName;
    }

    public void setCallerName(String callerName) {
        this.callerName = callerName;
    }
}
