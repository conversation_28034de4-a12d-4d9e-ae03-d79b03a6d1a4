package kl.npki.base.management.model.system.response;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.*;

/**
 * <AUTHOR>
 * @Date 2023/12/13
 */
public class PortConfigResponse {

    @Min(value = 0, message = THE_MANAGEMENT_SERVICE_PORT_NUMBER_CANNOT_BE_LESS_THAN_0_I18N_KEY)
    @Max(value = 65535, message = THE_MANAGEMENT_SERVICE_PORT_NUMBER_CANNOT_EXCEED_65535_I18N_KEY)
    @Schema(description = "管理服务端口")
    private Integer mgmtHttpPort;

    @NotNull(message = THE_MANAGEMENT_SERVICE_SSL_SWITCH_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "管理服务SSL开关")
    private Boolean mgmtHttpSSLEnable;

    @NotNull(message = THE_HTTP_BUSINESS_SERVICE_SWITCH_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "HTTP业务服务")
    private Boolean serviceHttpEnable;

    @Min(value = 0, message = THE_HTTP_BUSINESS_SERVICE_PORT_NUMBER_CANNOT_BE_LESS_THAN_0_I18N_KEY)
    @Max(value = 65535, message = THE_HTTP_BUSINESS_SERVICE_PORT_NUMBER_CANNOT_EXCEED_65535_I18N_KEY)
    @Schema(description = "HTTP业务服务端口")
    private Integer serviceHttpPort;

    @NotNull(message = THE_SSL_SWITCH_FOR_HTTP_BUSINESS_SERVICES_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "SSL开关")
    private Boolean serviceHttpSSLEnable;

    @NotNull(message = TCP_SERVICE_SWITCH_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "TCP业务服务")
    private Boolean serviceTcpEnable;

    @Min(value = 0, message = THE_TCP_SERVICE_PORT_NUMBER_CANNOT_BE_LESS_THAN_0_I18N_KEY)
    @Max(value = 65535, message = THE_TCP_SERVICE_PORT_NUMBER_CANNOT_EXCEED_65535_I18N_KEY)
    @Schema(description = "TCP业务服务端口")
    private Integer serviceTcpPort;

    @NotNull(message = TCP_BUSINESS_SERVICE_SSL_SWITCH_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "TCP服务SSL开关")
    private Boolean serviceTcpSSLEnable;

    public Integer getMgmtHttpPort() {
        return mgmtHttpPort;
    }

    public void setMgmtHttpPort(Integer mgmtHttpPort) {
        this.mgmtHttpPort = mgmtHttpPort;
    }

    public Boolean getMgmtHttpSSLEnable() {
        return mgmtHttpSSLEnable;
    }

    public void setMgmtHttpSSLEnable(Boolean mgmtHttpSSLEnable) {
        this.mgmtHttpSSLEnable = mgmtHttpSSLEnable;
    }

    public Boolean getServiceHttpEnable() {
        return serviceHttpEnable;
    }

    public void setServiceHttpEnable(Boolean serviceHttpEnable) {
        this.serviceHttpEnable = serviceHttpEnable;
    }

    public Integer getServiceHttpPort() {
        return serviceHttpPort;
    }

    public void setServiceHttpPort(Integer serviceHttpPort) {
        this.serviceHttpPort = serviceHttpPort;
    }

    public Boolean getServiceHttpSSLEnable() {
        return serviceHttpSSLEnable;
    }

    public void setServiceHttpSSLEnable(Boolean serviceHttpSSLEnable) {
        this.serviceHttpSSLEnable = serviceHttpSSLEnable;
    }

    public Boolean getServiceTcpEnable() {
        return serviceTcpEnable;
    }

    public void setServiceTcpEnable(Boolean serviceTcpEnable) {
        this.serviceTcpEnable = serviceTcpEnable;
    }

    public Integer getServiceTcpPort() {
        return serviceTcpPort;
    }

    public void setServiceTcpPort(Integer serviceTcpPort) {
        this.serviceTcpPort = serviceTcpPort;
    }

    public Boolean getServiceTcpSSLEnable() {
        return serviceTcpSSLEnable;
    }

    public void setServiceTcpSSLEnable(Boolean serviceTcpSSLEnable) {
        this.serviceTcpSSLEnable = serviceTcpSSLEnable;
    }
}
