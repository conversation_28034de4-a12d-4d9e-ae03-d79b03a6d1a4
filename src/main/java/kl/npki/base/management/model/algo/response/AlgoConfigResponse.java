package kl.npki.base.management.model.algo.response;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 密码算法配置响应
 *
 * <AUTHOR>
 * @date 2024/4/3
 */
public class AlgoConfigResponse implements Serializable {

    private static final long serialVersionUID = 2284323060914126994L;

    @Schema(description = "支持的对称密码算法")
    private List<String> symAlgo;

    @Schema(description = "支持的非对称密码算法")
    private List<String> asymAlgo;

    @Schema(description = "支持的Hash密码算法")
    private List<String> hashAlgo;

    @Schema(description = "支持的抗量子密码算法")
    private List<String> pqcAlgo;

    /**
     * 是否已配置对称密钥算法 : 默认为false, 系统部署生成主密钥后[对称密钥算法]不可更改
     */
    private Boolean symAlgoConfigured = Boolean.FALSE;

    public AlgoConfigResponse(List<String> symAlgo, List<String> asymAlgo, List<String> hashAlgo, List<String> pqcAlgo) {
        this.symAlgo = symAlgo.isEmpty() ? symAlgo : symAlgo.stream().sorted().collect(Collectors.toList());
        this.asymAlgo = asymAlgo.isEmpty() ? asymAlgo : asymAlgo.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
        this.hashAlgo = hashAlgo.isEmpty() ? hashAlgo : hashAlgo.stream().sorted().collect(Collectors.toList());
        this.pqcAlgo = pqcAlgo.isEmpty() ? pqcAlgo : pqcAlgo.stream().sorted().collect(Collectors.toList());
    }

    public AlgoConfigResponse(List<String> symAlgo, List<String> asymAlgo, List<String> hashAlgo, List<String> pqcAlgo, Boolean symAlgoConfigured) {
        this(symAlgo, asymAlgo, hashAlgo, pqcAlgo);
        this.symAlgoConfigured = symAlgoConfigured;
    }

    public List<String> getSymAlgo() {
        return symAlgo;
    }

    public void setSymAlgo(List<String> symAlgo) {
        this.symAlgo = symAlgo;
    }

    public List<String> getAsymAlgo() {
        return asymAlgo;
    }

    public void setAsymAlgo(List<String> asymAlgo) {
        this.asymAlgo = asymAlgo;
    }

    public List<String> getHashAlgo() {
        return hashAlgo;
    }

    public void setHashAlgo(List<String> hashAlgo) {
        this.hashAlgo = hashAlgo;
    }

    public List<String> getPqcAlgo() {
        return pqcAlgo;
    }

    public void setPqcAlgo(List<String> pqcAlgo) {
        this.pqcAlgo = pqcAlgo;
    }

    public Boolean getSymAlgoConfigured() {
        return symAlgoConfigured;
    }

    public void setSymAlgoConfigured(Boolean symAlgoConfigured) {
        this.symAlgoConfigured = symAlgoConfigured;
    }
}
