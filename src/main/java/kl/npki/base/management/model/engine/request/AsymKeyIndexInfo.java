package kl.npki.base.management.model.engine.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;

import java.io.Serializable;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.ASYMMETRIC_ALGORITHM_NAME_CANNOT_BE_EMPTY_I18N_KEY;

/**
 * <AUTHOR>
 * @since 2024/8/13
 */
public class AsymKeyIndexInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "非对称算法")
    @NotEmpty(message = ASYMMETRIC_ALGORITHM_NAME_CANNOT_BE_EMPTY_I18N_KEY)
    private String algoName;

    @Min(1)
    @Schema(description = "密钥索引起始索引")
    private Integer keyStartIndex;

    @Min(1)
    @Schema(description = "密钥索引结束索引")
    private Integer keyEndIndex;

    public @NotEmpty(message = ASYMMETRIC_ALGORITHM_NAME_CANNOT_BE_EMPTY_I18N_KEY) String getAlgoName() {
        return algoName;
    }

    public void setAlgoName(@NotEmpty(message = ASYMMETRIC_ALGORITHM_NAME_CANNOT_BE_EMPTY_I18N_KEY) String algoName) {
        this.algoName = algoName;
    }

    public @Min(1) Integer getKeyStartIndex() {
        return keyStartIndex;
    }

    public void setKeyStartIndex(@Min(1) Integer keyStartIndex) {
        this.keyStartIndex = keyStartIndex;
    }

    public @Min(1) Integer getKeyEndIndex() {
        return keyEndIndex;
    }

    public void setKeyEndIndex(@Min(1) Integer keyEndIndex) {
        this.keyEndIndex = keyEndIndex;
    }
}
