package kl.npki.base.management.model.system.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import kl.npki.base.core.annotation.ValueOfEnum;
import kl.npki.base.core.constant.RegionAndLanguageConstant;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.*;

/**
 * 区域与语言请求对象
 * <AUTHOR>
 * @since 2025/7/3 11:16
 */
public class RegionAndLanguageRequest {

    @Schema(description = "区域")
    @NotBlank(message = THE_REGION_CANNOT_BE_EMPTY_I18N_KEY)
    @ValueOfEnum(enumClass = RegionAndLanguageConstant.class,message = THE_REGION_PARAM_ERROR_I18N_KEY)
    private String region;

    @Schema(description = "语言")
    @NotBlank(message = THE_LANGUAGE_CANNOT_BE_EMPTY_I18N_KEY)
    @ValueOfEnum(values = {"zh", "en"},message = THE_LANGUAGE_PARAM_ERROR_I18N_KEY)
    private String language;

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }
}