package kl.npki.base.management.model.permission.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.io.Serializable;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.*;

/**
 * 新增/更新统一资源请求，目前仅支持新增API资源
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
public class AddResourceRequest implements Serializable {

    /**
     * 资源名称
     */
    @NotBlank(message = RESOURCE_NAME_SHOULD_NOT_BE_EMPTY_I18N_KEY)
    @Size(max = 255, message = RESOURCE_NAME_LENGTH_EXCEED_LIMIT_I18N_KEY)
    @Schema(description = "资源名称")
    private String resourceName;

    /**
     * 资源名称国际化key
     */
    @Schema(description = "资源名称国际化key")
    private String resourceNameI18nKey;

    /**
     * 备注
     */
    @Size(max = 500, message = REMARK_LENGTH_EXCEED_LIMIT_I18N_KEY)
    @Schema(description = "备注")
    private String remark;
    /**
     * 父级ID
     */
    @Schema(description = "父级Code")
    private String parentResourceCode;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序，暂不考虑实现")
    private Integer showOrder;

    @NotNull(message = RESOURCE_TYPE_SHOULD_NOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "资源类型 1-页面 2-菜单 3-按钮 4-API，暂时只需要支持API资源")
    private Integer resourceType;

    /**
     * 请求路径（API资源）
     */
    @Size(max = 500, message = URL_LENGTH_EXCEED_LIMIT_I18N_KEY)
    @Schema(description = "请求路径（API资源）")
    private String resourceUrl;

    /**
     * 请求方法（API资源）
     */
    @Size(max = 20, message = REQUEST_METHOD_LENGTH_EXCEED_LIMIT_I18N_KEY)
    @Schema(description = "请求方法（API资源）")
    private String requestMethod;

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getParentResourceCode() {
        return parentResourceCode;
    }

    public void setParentResourceCode(String parentResourceCode) {
        this.parentResourceCode = parentResourceCode;
    }

    public String getResourceNameI18nKey() {
        return resourceNameI18nKey;
    }

    public void setResourceNameI18nKey(String resourceNameI18nKey) {
        this.resourceNameI18nKey = resourceNameI18nKey;
    }

    public Integer getResourceType() {
        return resourceType;
    }

    public void setResourceType(Integer resourceType) {
        this.resourceType = resourceType;
    }

    public String getResourceUrl() {
        return resourceUrl;
    }

    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl;
    }

    public String getRequestMethod() {
        return requestMethod;
    }

    public void setRequestMethod(String requestMethod) {
        this.requestMethod = requestMethod;
    }

    public Integer getShowOrder() {
        return showOrder;
    }

    public void setShowOrder(Integer showOrder) {
        this.showOrder = showOrder;
    }
}
