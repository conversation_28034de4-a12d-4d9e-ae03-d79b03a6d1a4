package kl.npki.base.management.model.cert.response;

import kl.nbase.security.entity.algo.AsymAlgo;

/**
 * 管理根算法类型响应
 * <AUTHOR>
 */
public class CertKeyTypeResponse {
    /**
     * 证书算法类型名称
     */
    private String asymAlgoName;

    /**
     * 算法是否为抗量子算法
     */
    private boolean isPqc;

    public CertKeyTypeResponse(AsymAlgo asymAlgo) {
        this.asymAlgoName = asymAlgo.getAlgoName();
        this.isPqc = asymAlgo.isPQC();
    }

    public String getAsymAlgoName() {
        return asymAlgoName;
    }

    public CertKeyTypeResponse setAsymAlgoName(String asymAlgoName) {
        this.asymAlgoName = asymAlgoName;
        return this;
    }

    public boolean isPqc() {
        return isPqc;
    }

    public CertKeyTypeResponse setPqc(boolean pqc) {
        isPqc = pqc;
        return this;
    }
}
