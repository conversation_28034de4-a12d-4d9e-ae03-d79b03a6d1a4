package kl.npki.base.management.model.admin.request;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.USER_ID_SHOULD_NOT_BE_EMPTY_I18N_KEY;

/**
 * <AUTHOR>
 * @date 2022/9/2
 * @desc 签发管理员证书请求参数
 */
public class IssueAdminCertRequest {

    /**
     * 用户ID
     */
    @NotNull(message = USER_ID_SHOULD_NOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "管理员id")
    private Long adminInfoId;

    /**
     * p10证书请求
     */
    // @NotBlank(message = CERTIFICATE_ISSUANCE_REQUEST_SHOULD_NOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "证书签发请求（P10请求）")
    private String p10CertReq;

    /**
     * 证书有效期
     */
    @Schema(description = "证书有效期(天)")
    private Long validDays;

    /**
     * 是否自签发(默认走自签发流程)
     */
    @Schema(description = "是否自签发")
    private Boolean selfSign = true;

    /**
     * 是否签发双证
     */
    @Schema(description = "是否签发双证")
    private Boolean twinCert;

    @Schema(description = "抗量子加密证书密钥类型")
    private String pqEncKeyType;

    /**
     * pfx文件密码
     */
    private String pfxKey;

    public Long getAdminInfoId() {
        return adminInfoId;
    }

    public IssueAdminCertRequest setAdminInfoId(Long adminInfoId) {
        this.adminInfoId = adminInfoId;
        return this;
    }

    public String getP10CertReq() {
        return p10CertReq;
    }

    public IssueAdminCertRequest setP10CertReq(String p10CertReq) {
        this.p10CertReq = p10CertReq;
        return this;
    }

    public Long getValidDays() {
        return validDays;
    }

    public void setValidDays(Long validDays) {
        this.validDays = validDays;
    }

    public Boolean getTwinCert() {
        return twinCert;
    }

    public void setTwinCert(Boolean twinCert) {
        this.twinCert = twinCert;
    }

    public String getPfxKey() {
        return pfxKey;
    }

    public void setPfxKey(String pfxKey) {
        this.pfxKey = pfxKey;
    }

    public String getPqEncKeyType() {
        return pqEncKeyType;
    }

    public void setPqEncKeyType(String pqEncKeyType) {
        this.pqEncKeyType = pqEncKeyType;
    }

    public Boolean getSelfSign() {
        return selfSign;
    }

    public void setSelfSign(Boolean selfSign) {
        this.selfSign = selfSign;
    }

    @Override
    public String toString() {
        return "IssueAdminCertRequest{" +
            "adminInfoId=" + adminInfoId +
            ", p10CertReq='" + p10CertReq + '\'' +
            ", validDays=" + validDays +
            ", twinCert=" + twinCert +
            ", pqEncKeyType='" + pqEncKeyType + '\'' +
            ", pfxKey='" + pfxKey + '\'' +
            '}';
    }
}
