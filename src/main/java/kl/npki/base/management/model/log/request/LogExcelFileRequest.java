package kl.npki.base.management.model.log.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.*;

/**
 * <AUTHOR>
 * @create 2025/2/17 下午1:49
 */
public class LogExcelFileRequest {

    @NotEmpty(message = SIGNER_CERTIFICATE_CHAIN_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "签名者证书链不能为空")
    private List<String> signerCertChain;

    @NotNull(message = SIGNATURE_TIME_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "签名时间")
    private Long timestamp;

    @NotNull(message = FILE_ID_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "文件ID不能为空")
    private Long fileId;

    @NotEmpty(message = SIGNATURE_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "签名值不能为空")
    private String signature;

    public List<String> getSignerCertChain() {
        return signerCertChain;
    }

    public LogExcelFileRequest setSignerCertChain(List<String> signerCertChain) {
        this.signerCertChain = signerCertChain;
        return this;
    }

    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    public String getSignature() {
        return signature;
    }

    public LogExcelFileRequest setSignature(String signature) {
        this.signature = signature;
        return this;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public LogExcelFileRequest setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
        return this;
    }
}