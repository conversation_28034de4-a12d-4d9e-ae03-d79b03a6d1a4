package kl.npki.base.management.model.admin.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.THE_ADMINISTRATOR_ID_SHOULD_NOT_BE_EMPTY_I18N_KEY;

/**
 * <AUTHOR>
 * @date 2022/9/7
 * @desc 导入管理员证书
 */
public class ImportExtendAdminCertRequest extends ImportAdminCertRequest {

    /**
     * 管理员id
     */
    @NotNull(message = THE_ADMINISTRATOR_ID_SHOULD_NOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "管理员id")
    private Long adminInfoId;

    public @NotNull(message = THE_ADMINISTRATOR_ID_SHOULD_NOT_BE_EMPTY_I18N_KEY) Long getAdminInfoId() {
        return adminInfoId;
    }

    public void setAdminInfoId(@NotNull(message = THE_ADMINISTRATOR_ID_SHOULD_NOT_BE_EMPTY_I18N_KEY) Long adminInfoId) {
        this.adminInfoId = adminInfoId;
    }

    @Override
    public String toString() {
        return "ImportExtendAdminCertRequest{" +
            "adminInfoId=" + adminInfoId +
            ", roleCode='" + roleCode + '\'' +
            ", passwordHash='" + passwordHash + '\'' +
            ", certValue='" + certValue + '\'' +
            ", encCertValue='" + encCertValue + '\'' +
            '}';
    }
}
