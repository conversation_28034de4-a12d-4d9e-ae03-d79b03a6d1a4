package kl.npki.base.management.model.admin.request;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import kl.npki.base.core.common.json.deserializer.StringToLongDeserializer;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.*;

/**
 * <AUTHOR>
 * @date 2022/9/7
 * @desc 导入管理员证书
 */
public class ImportAdminCertRequest {

    @NotNull(message = ROLE_INFORMATION_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = THE_LENGTH_OF_THE_CHARACTER_CODE_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = CHARACTER_ENCODING_CANNOT_USE_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "角色编码")
    protected String roleCode;

    @Schema(description = "组织机构ID")
    @JsonDeserialize(using = StringToLongDeserializer.class)
    private Long orgId;

    @NotNull(message = PASSWORD_INFORMATION_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = PASSWORD_HASH_LENGTH_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = PASSWORD_HASH_CANNOT_USE_SPACES_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "密码hash")
    protected String passwordHash;

    @NotNull(message = THE_VALUE_OF_THE_SIGNATURE_CERTIFICATE_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 4096, message = THE_LENGTH_OF_THE_SIGNATURE_CERTIFICATE_VALUE_CANNOT_EXCEED_4096_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = THE_CERTIFICATE_VALUE_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "签名证书值")
    protected String certValue;

    @Size(max = 4096, message = THE_LENGTH_OF_THE_ENCRYPTION_CERTIFICATE_VALUE_CANNOT_EXCEED_4096_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = THE_ENCRYPTION_CERTIFICATE_VALUE_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "加密证书值")
    protected String encCertValue;

    public String getRoleCode() {
        return roleCode;
    }

    public String getCertValue() {
        return certValue;
    }

    public String getPasswordHash() {
        return passwordHash;
    }

    public String getEncCertValue() {
        return encCertValue;
    }

    public ImportAdminCertRequest setRoleCode(String roleCode) {
        this.roleCode = roleCode;
        return this;
    }

    public Long getOrgId() {
        return orgId;
    }

    public ImportAdminCertRequest setOrgId(Long orgId) {
        this.orgId = orgId;
        return this;
    }

    @Override
    public String toString() {
        return "ImportAdminCertRequest{" +
                "roleCode='" + roleCode + '\'' +
                ", orgId=" + orgId +
                ", passwordHash='" + passwordHash + '\'' +
                ", certValue='" + certValue + '\'' +
                ", encCertValue='" + encCertValue + '\'' +
                '}';
    }
}
