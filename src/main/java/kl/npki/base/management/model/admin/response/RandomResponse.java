package kl.npki.base.management.model.admin.response;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @since 2025/7/15 上午11:01
 */
public class RandomResponse {
    @Schema(description = "认证ID")
    private String authRefId ;

    @Schema(description = "随机数")
    private String random;

    public String getAuthRefId() {
        return authRefId;
    }

    public RandomResponse setAuthRefId(String authRefId) {
        this.authRefId = authRefId;
        return this;
    }

    public String getRandom() {
        return random;
    }

    public RandomResponse setRandom(String random) {
        this.random = random;
        return this;
    }
}