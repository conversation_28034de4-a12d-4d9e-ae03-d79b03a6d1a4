package kl.npki.base.management.model.inspection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.time.LocalDateTime;

/**
 * 系统巡检记录列表响应
 *
 * <AUTHOR>
 * @date 07/05/2025 19:54
 **/
public class InspectionRecordListResponse {

    /**
     * 巡检项目ID
     */
    @Schema(description = "巡检项目ID")
    private Long id;

    /**
     * 本次巡检名称
     */
    @Schema(description = "本次巡检名称")
    private String name;

    /**
     * 本次巡检耗时
     */
    @Schema(description = "本次巡检耗时")
    private Long costMills;

    /**
     * 本次巡检执行开始时间
     */
    @Schema(description = "本次巡检执行开始时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime startTime;

    /**
     * 本次巡检执行结束时间
     */
    @Schema(description = "本次巡检执行结束时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime endTime;

    /**
     * 本次巡检操作人
     */
    @Schema(description = "本次巡检操作人")
    private Long createBy;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCostMills() {
        return costMills;
    }

    public void setCostMills(Long costMills) {
        this.costMills = costMills;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }
}
