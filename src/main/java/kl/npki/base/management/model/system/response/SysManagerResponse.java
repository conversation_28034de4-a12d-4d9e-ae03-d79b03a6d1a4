package kl.npki.base.management.model.system.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.nbase.helper.utils.LocalDateTimeUtils;
import kl.nbase.i18n.i18n.I18nUtil;
import kl.npki.base.core.biz.config.model.PatchInfo;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.constant.DeployTypeEnum;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 各子系统公共属性响应
 *
 * <AUTHOR>
 * @date 2022/12/21
 */
public class SysManagerResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "系统名称")
    private String name;

    @Schema(description = "系统版本")
    private String version;

    @Schema(description = "内核版本")
    private String kernelVersion;

    @Schema(description = "定制版本")
    private String customizedVersion;

    @Schema(description = "电子邮件")
    private String email;

    @Schema(description = "系统LOGO")
    private String logo;

    @Schema(description = "部署时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime deploymentTime;

    @Schema(description = "启动时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime startTime;

    @Schema(description = "当前系统时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime systemTime;

    @Schema(description = "补丁列表")
    private List<PatchInfo> patchList;

    @Schema(description = "是否显示微软CSP设备")
    private boolean showCSP;

    @Schema(description = "安装包类型")
    private String installationPackageType;

    @Schema(description = "是否开启人机密码分离")
    private boolean passwdSeparateDb;

    public static SysManagerResponse buildSysManagerResponse(SysInfoConfig sysInfoConf) {
        SysManagerResponse sysManagerResponse = new SysManagerResponse();
        sysManagerResponse.name = I18nUtil.tr(sysInfoConf.getName());
        sysManagerResponse.version = sysInfoConf.getVersion();
        sysManagerResponse.email = sysInfoConf.getEmail();
        sysManagerResponse.customizedVersion = sysInfoConf.getCustomizedVersion();
        sysManagerResponse.kernelVersion = sysInfoConf.getKernelVersion();
        sysManagerResponse.logo = sysInfoConf.getLogo();
        sysManagerResponse.deploymentTime = sysInfoConf.getDeploymentTime() == null ? null : LocalDateTimeUtils.parseLocalDateTime(sysInfoConf.getDeploymentTime());
        sysManagerResponse.startTime = LocalDateTimeUtils.parseLocalDateTime(sysInfoConf.getStartTime());
        sysManagerResponse.systemTime = LocalDateTime.now();
        sysManagerResponse.patchList = sysInfoConf.getPatchList();
        sysManagerResponse.installationPackageType = DeployTypeEnum.getDeployType().name();
        sysManagerResponse.passwdSeparateDb = sysInfoConf.isPasswdSeparateDb();
        return sysManagerResponse;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getKernelVersion() {
        return kernelVersion;
    }

    public void setKernelVersion(String kernelVersion) {
        this.kernelVersion = kernelVersion;
    }

    public String getCustomizedVersion() {
        return customizedVersion;
    }

    public void setCustomizedVersion(String customizedVersion) {
        this.customizedVersion = customizedVersion;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public LocalDateTime getDeploymentTime() {
        return deploymentTime;
    }

    public void setDeploymentTime(LocalDateTime deploymentTime) {
        this.deploymentTime = deploymentTime;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getSystemTime() {
        return systemTime;
    }

    public void setSystemTime(LocalDateTime systemTime) {
        this.systemTime = systemTime;
    }

    public List<PatchInfo> getPatchList() {
        return patchList;
    }

    public void setPatchList(List<PatchInfo> patchList) {
        this.patchList = patchList;
    }

    public boolean isShowCSP() {
        return showCSP;
    }

    public void setShowCSP(boolean showCSP) {
        this.showCSP = showCSP;
    }

    public String getInstallationPackageType() {
        return installationPackageType;
    }

    public void setInstallationPackageType(String installationPackageType) {
        this.installationPackageType = installationPackageType;
    }
    public boolean isPasswdSeparateDb() {
        return passwdSeparateDb;
    }

    public void setPasswdSeparateDb(boolean passwdSeparateDb) {
        this.passwdSeparateDb = passwdSeparateDb;
    }
}
