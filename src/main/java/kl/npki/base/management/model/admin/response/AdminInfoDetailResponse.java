package kl.npki.base.management.model.admin.response;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 2023/5/25
 */
public class AdminInfoDetailResponse extends AdminInfoListResponse {

    /**
     * 证书cn
     */
    @Schema(description = "证书cn")
    private String subjectCn;

    @Schema(description = "签名证书值")
    private String signCertValue;

    @Schema(description = "加密证书值")
    private String encCertValue;

    @Schema(description = "证书算法")
    private String asymAlgo;

    public String getSubjectCn() {
        return subjectCn;
    }

    public void setSubjectCn(String subjectCn) {
        this.subjectCn = subjectCn;
    }

    public String getSignCertValue() {
        return signCertValue;
    }

    public void setSignCertValue(String signCertValue) {
        this.signCertValue = signCertValue;
    }

    public String getEncCertValue() {
        return encCertValue;
    }

    public void setEncCertValue(String encCertValue) {
        this.encCertValue = encCertValue;
    }

    public String getAsymAlgo() {
        return asymAlgo;
    }

    public void setAsymAlgo(String asymAlgo) {
        this.asymAlgo = asymAlgo;
    }

}
