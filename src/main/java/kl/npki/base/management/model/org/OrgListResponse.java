package kl.npki.base.management.model.org;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 机构列表响应
 *
 * <AUTHOR>
 * @create 2024/3/21 16:58
 */
public class OrgListResponse {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "机构名称")
    private String orgName;

    @Schema(description = "机构编码")
    private String orgCode;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "上级机构id")
    private Long parentId;

    @Schema(description = "完整机构id")
    private String fullId;

    @Schema(description = "上级机构名称")
    private String parentName;

    @Schema(description = "机构描述")
    private String orgDesc;

    @Schema(description = "机构状态")
    private Integer orgStatus;

    @Schema(description = "机构排序码")
    private Integer orderIndex;


    @Schema(description = "机构排序完整编码")
    private String orderFullIndex;

    @Schema(description = "机构级别")
    private Integer orgLevel;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgDesc() {
        return orgDesc;
    }

    public void setOrgDesc(String orgDesc) {
        this.orgDesc = orgDesc;
    }

    public Integer getOrgStatus() {
        return orgStatus;
    }

    public void setOrgStatus(Integer orgStatus) {
        this.orgStatus = orgStatus;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public String getOrderFullIndex() {
        return orderFullIndex;
    }

    public void setOrderFullIndex(String orderFullIndex) {
        this.orderFullIndex = orderFullIndex;
    }

    public Integer getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(Integer orgLevel) {
        this.orgLevel = orgLevel;
    }

    public Long getParentId() {
        return parentId;
    }

    public OrgListResponse setParentId(Long parentId) {
        this.parentId = parentId;
        return this;
    }

    public String getParentName() {
        return parentName;
    }

    public OrgListResponse setParentName(String parentName) {
        this.parentName = parentName;
        return this;
    }

    public String getFullId() {
        return fullId;
    }

    public OrgListResponse setFullId(String fullId) {
        this.fullId = fullId;
        return this;
    }
}
