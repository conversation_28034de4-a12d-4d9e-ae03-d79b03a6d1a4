package kl.npki.base.management.model.db.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import kl.npki.management.core.biz.config.model.DbPasswdConfigInfo;

import java.io.Serializable;
import java.util.List;

/**
 * 数据库密码配置请求
 *
 * <AUTHOR>
 * @since 2022/8/24
 */
public class DbPasswdRequest implements Serializable {

    @Valid
    @Schema(description = "密码信息列表")
    private List<DbPasswdConfigInfo> dbPasswdRequestList;

    public List<DbPasswdConfigInfo> getDbPasswdRequestList() {
        return dbPasswdRequestList;
    }

    public void setDbPasswdRequestList(List<DbPasswdConfigInfo> dbPasswdRequestList) {
        this.dbPasswdRequestList = dbPasswdRequestList;
    }
}
