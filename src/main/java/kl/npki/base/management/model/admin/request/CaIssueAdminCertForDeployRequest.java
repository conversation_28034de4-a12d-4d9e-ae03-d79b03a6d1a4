package kl.npki.base.management.model.admin.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.*;

public class CaIssueAdminCertForDeployRequest extends IssueAdminCertForDeployRequest {
    /**
     * 颁发者CN
     */
    @Size(max = 128, message = THE_LENGTH_OF_THE_ISSUE_CN_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Schema(description = "颁发者CN项")
    private String issuerCn;

    /**
     * 证书模板ID
     */
    @Size(max = 128, message = THE_LENGTH_OF_THE_TEMPLATE_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Schema(description = "证书模板ID")
    private String templateId;

    public String getIssuerCn() {
        return issuerCn;
    }

    public void setIssuerCn(String issuerCn) {
        this.issuerCn = issuerCn;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }
}
