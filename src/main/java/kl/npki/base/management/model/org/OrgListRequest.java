package kl.npki.base.management.model.org;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.nbase.db.support.mybatis.query.QueryField;
import kl.nbase.db.support.mybatis.query.QueryFieldOpType;
import kl.nbase.db.support.mybatis.query.QueryInfo;


/**
 * 机构列表查询请求
 *
 * <AUTHOR>
 * @create 2024/3/21 16:58
 */
public class OrgListRequest extends QueryInfo {

    @Schema(description = "机构名称")
    @QueryField(value = "org_name", op = QueryFieldOpType.LIKE)
    private String orgName;

    @Schema(description = "机构名称拼音")
    @QueryField(value = "name_pinyin", op = QueryFieldOpType.LIKE)
    private String namePinyin;

    @Schema(description = "机构状态，示例【 1002:正常, -1:注销】", allowableValues = "【 1002, -1 】")
    @QueryField(value = "org_status", op = QueryFieldOpType.EQ)
    private Integer orgStatus;

    @Schema(description = "上级机构id")
    @QueryField(value = "parent_id", op = QueryFieldOpType.EQ)
    private Long parentId;

    @Schema(description = "完整机构id")
    @QueryField(value = "full_id", op = QueryFieldOpType.RIGHT_LIKE)
    private String fullId;

    @Schema(description = "上级机构编码")
    @QueryField(value = "parent_code", op = QueryFieldOpType.EQ)
    private String parentCode;

    @Schema(description = "机构编码")
    @QueryField(value = "org_code", op = QueryFieldOpType.EQ)
    private String orgCode;

    @Schema(description = "机构级别，示例【 0, 1, 2, 4,...】")
    @QueryField(value = "org_level", op = QueryFieldOpType.EQ)
    private String orgLevel;

    @Schema(description = "机构连接人")
    @QueryField(value = "linkman", op = QueryFieldOpType.EQ)
    private String linkman;

    @Schema(description = "机构连接电话")
    @QueryField(value = "telephone", op = QueryFieldOpType.EQ)
    private String telephone;

    @Schema(description = "机构邮政编码")
    @QueryField(value = "telephone", op = QueryFieldOpType.EQ)
    private String postcode;

    @Schema(description = "机构联系地址")
    @QueryField(value = "address", op = QueryFieldOpType.EQ)
    private String address;

    @Schema(description = "机构创建时间")
    @QueryField(value = "created_at", op = QueryFieldOpType.GE)
    private String createdAt;

    @Schema(description = "机构更新时间")
    @QueryField(value = "updated_at", op = QueryFieldOpType.GE)
    private String updatedAt;

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getOrgStatus() {
        return orgStatus;
    }

    public void setOrgStatus(Integer orgStatus) {
        this.orgStatus = orgStatus;
    }

    public String getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(String orgLevel) {
        this.orgLevel = orgLevel;
    }

    public String getNamePinyin() {
        return namePinyin;
    }

    public void setNamePinyin(String namePinyin) {
        this.namePinyin = namePinyin;
    }

    public Long getParentId() {
        return parentId;
    }

    public OrgListRequest setParentId(Long parentId) {
        this.parentId = parentId;
        return this;
    }

    public String getFullId() {
        return fullId;
    }

    public OrgListRequest setFullId(String fullId) {
        this.fullId = fullId;
        return this;
    }

    public String getParentCode() {
        return parentCode;
    }

    public OrgListRequest setParentCode(String parentCode) {
        this.parentCode = parentCode;
        return this;
    }

    public String getLinkman() {
        return linkman;
    }

    public void setLinkman(String linkman) {
        this.linkman = linkman;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }
}
