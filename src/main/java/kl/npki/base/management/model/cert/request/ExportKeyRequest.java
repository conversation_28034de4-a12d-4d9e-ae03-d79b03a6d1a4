package kl.npki.base.management.model.cert.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import kl.npki.base.core.constant.ExportKeyTypeEnum;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.THE_KEY_EXPORT_FORMAT;

/**
 * NGPKI中所有子系统通用的私钥导出请求
 * <p>
 * <b>导出格式支持PFX、GMT0009_2012、GMT0009_2023、GMT0016_2023、P8，默认为DER格式</b>
 * </p>
 *
 * <p>
 * <li>通过证书ID导出</li>
 * </p>
 *
 * <AUTHOR>
 * @create 2025/4/2 下午5:02
 */
public class ExportKeyRequest {
    @Schema(description = "证书ID")
    private Long id;

    @Pattern(regexp = "^(?:PFX|GMT0009_2012|GMT0009_2023|GMT0016_2023|P8)$", message = THE_KEY_EXPORT_FORMAT)
    @Schema(description = "私钥导出格式")
    private String format = ExportKeyTypeEnum.PFX.name();

    public Long getId() {
        return id;
    }

    public ExportKeyRequest setId(Long id) {
        this.id = id;
        return this;
    }



    public String getFormat() {
        return format;
    }

    public ExportKeyRequest setFormat(String format) {
        this.format = format;
        return this;
    }

    public ExportKeyTypeEnum getExportKeyTypeEnum() {
        return ExportKeyTypeEnum.valueOfByType(format);
    }
}