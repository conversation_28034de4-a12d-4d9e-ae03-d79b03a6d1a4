package kl.npki.base.management.model.log.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 日志审计list响应
 *
 * <AUTHOR>
 * @date 2023/3/21
 */
public class AuditLogResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "操作日志id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long logId;

    /**
     * 操作名称
     */
    @Schema(description = "操作名称")
    private String logDo;

    /**
     * 操作详情
     */
    @Schema(description = "操作详情")
    private String logWhat;

    /**
     * 操作结果id
     */
    @Schema(description = "操作结果")
    private boolean result;

    /**
     * 客户端ip
     */
    @Schema(description = "客户端ip")
    private String clientIp;

    /**
     * 操作时间
     */
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @Schema(description = "操作时间")
    private LocalDateTime logWhen;

    @Schema(description = "请求体")
    private String request;

    /**
     * 审计结果状态
     */
    @Schema(description = "审计结果状态编码")
    private Integer auditStatus;

    /**
     * 审计结果状态
     */
    @Schema(description = "审计结果状态描述")
    private String auditStatusDesc;

    /**
     * 审计时间
     */
    @Schema(description = "审计时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime auditTime;

    @Schema(description = "用户名")
    private String username;

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public String getLogDo() {
        return logDo;
    }

    public void setLogDo(String logDo) {
        this.logDo = logDo;
    }

    public String getLogWhat() {
        return logWhat;
    }

    public void setLogWhat(String logWhat) {
        this.logWhat = logWhat;
    }

    public boolean isResult() {
        return result;
    }

    public void setResult(boolean result) {
        this.result = result;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public LocalDateTime getLogWhen() {
        return logWhen;
    }

    public void setLogWhen(LocalDateTime logWhen) {
        this.logWhen = logWhen;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getAuditStatusDesc() {
        return auditStatusDesc;
    }

    public void setAuditStatusDesc(String auditStatusDesc) {
        this.auditStatusDesc = auditStatusDesc;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public LocalDateTime getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(LocalDateTime auditTime) {
        this.auditTime = auditTime;
    }

    @Override
    public String toString() {
        return "AuditLogResponse{" +
            "logId=" + logId +
            ", logDo='" + logDo + '\'' +
            ", logWhat='" + logWhat + '\'' +
            ", result=" + result +
            ", clientIp='" + clientIp + '\'' +
            ", logWhen=" + logWhen +
            ", request='" + request + '\'' +
            ", auditStatus=" + auditStatus +
            ", auditStatusDesc='" + auditStatusDesc + '\'' +
            ", auditTime=" + auditTime +
            ", username='" + username + '\'' +
            '}';
    }
}
