package kl.npki.base.management.model.cert.response;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.util.encoders.Base64;
import kl.nbase.security.utils.PEMUtil;
import kl.npki.base.core.biz.cert.model.parser.x509.CertificateInfo;
import kl.npki.base.core.biz.cert.parser.CertificateParserFactory;
import kl.npki.base.core.common.date.CustomLocalDateTimeDeserializer;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.management.core.biz.cert.model.CertDetailInfo;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class CertDetailResponse extends CertDetailInfo {
    /**
     * 证书生效时间
     */
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime validStart;

    /**
     * 证书失效时间
     */
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime validEnd;

    public Map<String, CertificateInfo> getCertificateInfosMap() {
        return certificateInfosMap;
    }

    public void setCertificateInfosMap(Map<String, CertificateInfo> certificateInfosMap) {
        this.certificateInfosMap = certificateInfosMap;
    }

    private Map<String, CertificateInfo> certificateInfosMap =  new HashMap();

    @Override
    public void setB64Value(String b64Value) {
        super.setB64Value(b64Value);
        dealCertificateInfosMap("b64Value", b64Value);
    }


    @Override
    public void setB64EncCertValue(String b64EncCertValue) {
        super.setB64EncCertValue(b64EncCertValue);
        dealCertificateInfosMap("b64EncCertValue", b64EncCertValue);
    }

    public void dealCertificateInfosMap(String key, String certValue){
        if(StringUtils.isNotBlank(certValue)){
            try {
                Certificate certificateObj = Certificate.getInstance(Base64.decode(PEMUtil.unFormatCert(certValue)));
                CertificateInfo certificateInfo = CertificateParserFactory.getX509CertificateParser().parse(certificateObj);
                certificateInfosMap.put(key,certificateInfo );
            } catch (Exception e) {
                throw ManagementInternalError.CERT_PREVIEW_ERROR.toException(e);
            }
        }
    }


}
