package kl.npki.base.management.model.log.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 服务日志详情响应
 *
 * <AUTHOR>
 * @date 2022/12/14
 */
public class ApiLogDetailResponse implements Serializable {

    private static final long serialVersionUID = 1L;
    @Schema(description = "日志ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long logId;

    @Schema(description = "客户端IP")
    private String clientIp;

    @Schema(description = "请求的业务")
    private String biz;

    @Schema(description = "请求时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime logWhen;

    @Schema(description = "请求结果")
    private Boolean result;

    @Schema(description = "调用者标识")
    private String callerId;

    @Schema(description = "调用者名称")
    private String callerName;

    @Schema(description = "请求详细信息")
    private String detail;

    @Schema(description = "业务用户标识")
    private String entityId;

    @Schema(description = "请求体")
    private String request;

    @Schema(description = "响应体")
    private String response;


    @Schema(description = "请求耗时")
    private Long elapsedTime;

    @Schema(description = "certId")
    private String certId;

    @Schema(description = "扩展项1")
    private String ext1;

    @Schema(description = "扩展项2")
    private String ext2;

    @Schema(description = "扩展项3")
    private String ext3;



    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getBiz() {
        return biz;
    }

    public void setBiz(String biz) {
        this.biz = biz;
    }

    public LocalDateTime getLogWhen() {
        return logWhen;
    }

    public void setLogWhen(LocalDateTime logWhen) {
        this.logWhen = logWhen;
    }

    public Boolean getResult() {
        return result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getCallerId() {
        return callerId;
    }

    public void setCallerId(String callerId) {
        this.callerId = callerId;
    }

    public String getCallerName() {
        return callerName;
    }

    public void setCallerName(String callerName) {
        this.callerName = callerName;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public Long getElapsedTime() {
        return elapsedTime;
    }

    public void setElapsedTime(Long elapsedTime) {
        this.elapsedTime = elapsedTime;
    }

    public String getCertId() {
        return certId;
    }

    public void setCertId(String certId) {
        this.certId = certId;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }
}
