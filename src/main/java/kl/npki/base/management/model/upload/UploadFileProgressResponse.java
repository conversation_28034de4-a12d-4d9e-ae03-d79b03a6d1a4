package kl.npki.base.management.model.upload;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @create 2024/5/8 下午7:34
 */
public class UploadFileProgressResponse {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "文件标识")
    private Long id;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "处理状态")
    private Integer processStatus;

    @Schema(description = "总计条数")
    private Integer totalCount;

    @Schema(description = "已经处理条数")
    private Integer processCount;

    @Schema(description = "处理成功条数")
    private Integer processSuccessCount;

    @Schema(description = "处理失败条数")
    private Integer processErrorCount;

    public Long getId() {
        return id;
    }

    public String getFileName() {
        return fileName;
    }

    public Integer getProcessStatus() {
        return processStatus;
    }

    public UploadFileProgressResponse setProcessStatus(Integer processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public UploadFileProgressResponse setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
        return this;
    }

    public Integer getProcessCount() {
        return processCount;
    }

    public UploadFileProgressResponse setProcessCount(Integer processCount) {
        this.processCount = processCount;
        return this;
    }

    public Integer getProcessSuccessCount() {
        return processSuccessCount;
    }

    public UploadFileProgressResponse setProcessSuccessCount(Integer processSuccessCount) {
        this.processSuccessCount = processSuccessCount;
        return this;
    }

    public Integer getProcessErrorCount() {
        return processErrorCount;
    }

    public UploadFileProgressResponse setProcessErrorCount(Integer processErrorCount) {
        this.processErrorCount = processErrorCount;
        return this;
    }

    public UploadFileProgressResponse setId(Long id) {
        this.id = id;
        return this;
    }

    public UploadFileProgressResponse setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }
}