package kl.npki.base.management.model.admin.response;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/5/25
 */
public class AdminInfoResponse {
    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 角色ID
     */
    private Set<String> roleId;

    public AdminInfoResponse() {
    }

    public AdminInfoResponse(String userId, String username, Set<String> roleId) {
        this.userId = userId;
        this.username = username;
        this.roleId = roleId;
    }

    public String getUserId() {
        return userId;
    }

    public AdminInfoResponse setUserId(String userId) {
        this.userId = userId;
        return this;
    }

    public String getUsername() {
        return username;
    }

    public AdminInfoResponse setUsername(String username) {
        this.username = username;
        return this;
    }

    public Set<String> getRoleId() {
        return roleId;
    }

    public AdminInfoResponse setRoleId(Set<String> roleId) {
        this.roleId = roleId;
        return this;
    }

    @Override
    public String toString() {
        return "AdminInfoResponse{" +
            "userId='" + userId + '\'' +
            ", username='" + username + '\'' +
            ", roleId=" + roleId +
            '}';
    }
}
