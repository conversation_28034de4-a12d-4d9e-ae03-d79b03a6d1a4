package kl.npki.base.management.model.org;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 机构详细信息响应
 *
 * <AUTHOR>
 * @create 2024/3/21 16:58
 */
public class OrgDetailResponse {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "机构名称")
    private String orgName;

    @Schema(description = "机构全称")
    private String fullName;

    @Schema(description = "机构名称拼音")
    private String namePinyin;

    @Schema(description = "机构状态")
    private Integer orgStatus;

    @Schema(description = "机构编码")
    private String orgCode;

    @Schema(description = "机构类型")
    private Integer orgType;

    @Schema(description = "上级机构ID")
    private String parentId;

    @Schema(description = "上级机构编码")
    private String parentCode;

    @Schema(description = "机构完整编码")
    private String fullCode;

    @Schema(description = "机构完整ID")
    private String fullId;

    @Schema(description = "机构排序码")
    private Integer orderIndex;

    @Schema(description = "机构排序完整编码")
    private String orderFullIndex;

    @Schema(description = "机构级别")
    private Integer orgLevel;

    @Schema(description = "联系人")
    private String linkman;

    @Schema(description = "联系电话")
    private String telephone;

    @Schema(description = "邮政编码")
    private String postcode;

    @Schema(description = "联系地址")
    private String address;

    @Schema(description = "机构描述")
    private String orgDesc;

    @Schema(description = "逻辑删除标志")
    private Integer isDelete;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getNamePinyin() {
        return namePinyin;
    }

    public void setNamePinyin(String namePinyin) {
        this.namePinyin = namePinyin;
    }

    public Integer getOrgStatus() {
        return orgStatus;
    }

    public void setOrgStatus(Integer orgStatus) {
        this.orgStatus = orgStatus;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public String getFullCode() {
        return fullCode;
    }

    public void setFullCode(String fullCode) {
        this.fullCode = fullCode;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public String getOrderFullIndex() {
        return orderFullIndex;
    }

    public void setOrderFullIndex(String orderFullIndex) {
        this.orderFullIndex = orderFullIndex;
    }

    public Integer getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(Integer orgLevel) {
        this.orgLevel = orgLevel;
    }

    public String getLinkman() {
        return linkman;
    }

    public void setLinkman(String linkman) {
        this.linkman = linkman;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getOrgDesc() {
        return orgDesc;
    }

    public void setOrgDesc(String orgDesc) {
        this.orgDesc = orgDesc;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getFullId() {
        return fullId;
    }

    public OrgDetailResponse setFullId(String fullId) {
        this.fullId = fullId;
        return this;
    }
}
