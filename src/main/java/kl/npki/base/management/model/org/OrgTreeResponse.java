package kl.npki.base.management.model.org;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.biz.org.model.OrgEntity;
import kl.npki.base.core.common.structure.TreeNode;

import java.util.ArrayList;
import java.util.List;

/**
 * 机构树响应
 *
 * <AUTHOR>
 */
public class OrgTreeResponse {
    @Schema(description = "机构ID")
    private String id;

    @Schema(description = "机构名称")
    private String orgName;
    private List<OrgTreeResponse> children;

    public OrgTreeResponse() {
    }

    public OrgTreeResponse(TreeNode<Long, OrgEntity> orgTree) {
        this.id = String.valueOf(orgTree.getId());
        this.orgName = orgTree.getData().getOrgName();
        this.children = new ArrayList<>();
        if (orgTree.getChildren() == null) {
            return;
        }
        // 递归构造子节点
        for (TreeNode<Long, OrgEntity> child : orgTree.getChildren()) {
            this.children.add(new OrgTreeResponse(child));
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<OrgTreeResponse> getChildren() {
        return children;
    }

    public void setChildren(List<OrgTreeResponse> children) {
        this.children = children;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}
