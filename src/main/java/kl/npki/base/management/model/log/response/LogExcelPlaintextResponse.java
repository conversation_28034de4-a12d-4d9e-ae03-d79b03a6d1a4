package kl.npki.base.management.model.log.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 导出Excel电子签名之前的，Excel电子签名原文导出响应
 *
 * <AUTHOR>
 * @create 2025/2/11 上午11:52
 */
public class LogExcelPlaintextResponse {

    /**
     * 签名时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "签名时间")
    private long timestamp;

    /**
     * 导出文件标识
     */
    @Schema(description = "导出文件标识")
    private String fileId;

    /**
     * Base64之后的签名原文，签名的时候需要先Base64解码
     */
    @Schema(description = "Base64之后的签名原文，签名的时候需要先Base64解码")
    private String plaintext;

    public String getPlaintext() {
        return plaintext;
    }

    public LogExcelPlaintextResponse setPlaintext(String plaintext) {
        this.plaintext = plaintext;
        return this;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public LogExcelPlaintextResponse setTimestamp(long timestamp) {
        this.timestamp = timestamp;
        return this;
    }

    public String getFileId() {
        return fileId;
    }

    public LogExcelPlaintextResponse setFileId(String fileId) {
        this.fileId = fileId;
        return this;
    }
}