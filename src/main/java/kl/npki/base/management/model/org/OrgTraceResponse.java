package kl.npki.base.management.model.org;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.time.LocalDateTime;

/**
 * 历史轨迹响应
 *
 * <AUTHOR> rs
 * @date 2024/6/11 20:32
 */
public class OrgTraceResponse {

    /**
     * 操作者名称
     */
    @Schema(description = "操作者名称")
    private String operatorName;

    /**
     * 发起者IP
     */
    @Schema(description = "发起者IP")
    private String sourceIp;

    /**
     * 调用方式
     */
    @Schema(description = "调用方式")
    private String invocationType;

    /**
     * 创建时间
     */
    @Schema(description = "轨迹创建时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime createTime;

    /**
     * 变更事件/动作/业务类型
     */
    @Schema(description = "变更事件/动作/业务类型")
    private String bizName;

    /**
     * 业务流水号
     */
    @Schema(description = "业务流水号")
    private String bizId;

    /**
     * json格式的实体数据内容
     */
    @Schema(description = "json格式的实体数据内容")
    private String entityInfo;

    /**
     * 操作结果
     */
    @Schema(description = "操作结果")
    private String operatorResult;

    /**
     * 实体状态
     */
    @Schema(description = "实体状态")
    private int entityStatus;

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getSourceIp() {
        return sourceIp;
    }

    public void setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
    }

    public String getInvocationType() {
        return invocationType;
    }

    public void setInvocationType(String invocationType) {
        this.invocationType = invocationType;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getBizName() {
        return bizName;
    }

    public void setBizName(String bizName) {
        this.bizName = bizName;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getEntityInfo() {
        return entityInfo;
    }

    public void setEntityInfo(String entityInfo) {
        this.entityInfo = entityInfo;
    }

    public String getOperatorResult() {
        return operatorResult;
    }

    public void setOperatorResult(String operatorResult) {
        this.operatorResult = operatorResult;
    }

    public int getEntityStatus() {
        return entityStatus;
    }

    public void setEntityStatus(int entityStatus) {
        this.entityStatus = entityStatus;
    }
}
