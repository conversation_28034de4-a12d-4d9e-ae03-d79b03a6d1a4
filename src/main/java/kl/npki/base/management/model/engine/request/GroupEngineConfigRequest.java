package kl.npki.base.management.model.engine.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import kl.npki.base.core.configs.ClusterEmConfig;
import kl.npki.base.core.configs.EmConfig;
import kl.npki.base.core.configs.EmLoadBalancerConfig;
import kl.npki.base.core.configs.GroupEmConfig;
import kl.npki.base.core.constant.EngineLoadBalancerEnum;
import kl.npki.base.core.utils.EngineUtil;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.THE_CONFIGURATION_OF_ENCRYPTION_MACHINES_WITHIN_THE_GROUP_CANNOT_BE_EMPTY_I18N_KEY;

/**
 * 加密机组配置请求类
 *
 * <AUTHOR>
 */
public class GroupEngineConfigRequest implements Serializable {
    private static final String DEFAULT_ENGINE_GROUP_NAME = "main";
    private static final String BACK_UP_ENGINE_NAME = "backup";

    private static final long serialVersionUID = 744691492199345661L;
    @Schema(description = "组名")
    private String groupName;
    @Valid
    @NotEmpty(message = THE_CONFIGURATION_OF_ENCRYPTION_MACHINES_WITHIN_THE_GROUP_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "组内加密机配置")
    private List<EngineConfigRequest> engineConfigs;

    @Schema(description = "辅助加密机配置")
    private EngineConfigRequest backupEngineConfig;

    @Schema(description = "是否启用辅助密码机")
    private boolean enableBackupEngine;

    public GroupEmConfig toGroupEmConfig() {
        GroupEmConfig groupEmConfig = new GroupEmConfig();
        groupEmConfig.setGroupName(StringUtils.isBlank(this.groupName) ? DEFAULT_ENGINE_GROUP_NAME : this.groupName);

        // 负载策略，默认使用轮询策略
        EmLoadBalancerConfig emLoadBalancerConfig = new EmLoadBalancerConfig();
        emLoadBalancerConfig.setType(EngineLoadBalancerEnum.ROUND_ROBIN.name());
        // 预置权重值配置以便修改，权重策略下才会生效
        Map<String, Integer> engineWeightMap = new HashMap<>();
        emLoadBalancerConfig.setWeight(engineWeightMap);
        groupEmConfig.setLoadBalancer(emLoadBalancerConfig);

        boolean isFileEngine = true;
        List<EmConfig> emConfigs = new ArrayList<>();
        for (int i = 0; i < engineConfigs.size(); i++) {
            EngineConfigRequest engineConfigRequest = engineConfigs.get(i);
            EmConfig emConfig = engineConfigRequest.toEmConfig(String.format(EngineUtil.DEFAULT_ENGINE_NAME_FORMAT, i), false);
            emConfigs.add(emConfig);
            engineWeightMap.put(emConfig.getEngineName(), EngineUtil.ENGINE_DEFAULT_WEIGHT);
            // 获取主密码机类型，判断是否为文件密码机, 只要存在一个非文件密码机，则认为是不是文件密码机
            isFileEngine = isFileEngine ? EngineUtil.isFileEngine(emConfig.getEngineType()) : isFileEngine;
        }
        groupEmConfig.setEngines(emConfigs);
        if (backupEngineConfig != null) {
            EmConfig emConfig = backupEngineConfig.toEmConfig(String.format(EngineUtil.DEFAULT_ENGINE_NAME_FORMAT, BACK_UP_ENGINE_NAME), true);
            groupEmConfig.setBackupEngine(emConfig);
        }
        // 主密码机必须不为文件密码机才能启用辅助密码机
        if (isFileEngine) {
            enableBackupEngine = false;
        }
        groupEmConfig.setEnableBackupEngine(enableBackupEngine);
        return groupEmConfig;
    }

    public ClusterEmConfig toClusterEmConfig() {
        ClusterEmConfig clusterEmConfig = new ClusterEmConfig();
        // 默认配置一个组
        clusterEmConfig.setGroupEngine(toGroupEmConfig());
        return clusterEmConfig;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public List<EngineConfigRequest> getEngineConfigs() {
        return engineConfigs;
    }

    public void setEngineConfigs(@Valid List<EngineConfigRequest> engineConfigs) {
        this.engineConfigs = engineConfigs;
    }

    public EngineConfigRequest getBackupEngineConfig() {
        return backupEngineConfig;
    }

    public GroupEngineConfigRequest setBackupEngineConfig(EngineConfigRequest backupEngineConfig) {
        this.backupEngineConfig = backupEngineConfig;
        return this;
    }

    public boolean isEnableBackupEngine() {
        return enableBackupEngine;
    }

    public GroupEngineConfigRequest setEnableBackupEngine(boolean enableBackupEngine) {
        this.enableBackupEngine = enableBackupEngine;
        return this;
    }

}
