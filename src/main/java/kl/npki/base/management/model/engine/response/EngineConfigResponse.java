package kl.npki.base.management.model.engine.response;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.nbase.emengine.conf.IndexPair;
import kl.npki.base.core.configs.EmConfig;
import kl.npki.base.management.model.engine.request.AsymKeyIndexInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 加密机属性配置请求
 *
 * <AUTHOR>
 */
public class EngineConfigResponse implements Serializable {
    private static final long serialVersionUID = 3881821943783769923L;
    @Schema(description = "加密机名称")
    private String engineName;
    @Schema(description = "加密机类型")
    private String engineType;
    @Schema(description = "加密机ip")
    private String ip;
    @Schema(description = "加密机端口")
    private Integer port;
    @Schema(description = "加密机用户名")
    private String username;
    @Schema(description = "非对称密钥索引信息")
    private List<AsymKeyIndexInfo> asymKeyIndexInfoList;

    public EngineConfigResponse(EmConfig engineConfig) {
        this.engineName = engineConfig.getEngineName();
        this.engineType = engineConfig.getEngineType();
        this.ip = engineConfig.getIp();
        this.port = engineConfig.getPort();
        this.username = engineConfig.getUsername();

        parseIndexes(engineConfig);

    }

    public void parseIndexes(EmConfig engineConfig) {
        Map<String, String> signIndexes = engineConfig.getSignIndexes();
        Map<String, String> encIndexes = engineConfig.getEncIndexes();
        Map<String, String> allIndex = new LinkedHashMap<>();

        if (MapUtils.isNotEmpty(signIndexes)) {
            allIndex.putAll(signIndexes);
        }
        if (MapUtils.isNotEmpty(encIndexes)) {
            allIndex.putAll(encIndexes);
        }

        this.asymKeyIndexInfoList = new ArrayList<>();
        if (MapUtils.isNotEmpty(allIndex)) {
            allIndex.forEach((key, value) -> {
                List<IndexPair> indexPairs = engineConfig.parseToKeyIndexes(value);
                if (CollectionUtils.isNotEmpty(indexPairs)) {
                    IndexPair indexPair = indexPairs.get(0);
                    AsymKeyIndexInfo asymKeyIndexInfo = new AsymKeyIndexInfo();
                    asymKeyIndexInfo.setAlgoName(key);
                    asymKeyIndexInfo.setKeyStartIndex(indexPair.getIndex());
                    asymKeyIndexInfo.setKeyEndIndex(indexPairs.get(indexPairs.size() - 1).getIndex());
                    asymKeyIndexInfoList.add(asymKeyIndexInfo);
                }
            });
        }
    }

    public String getEngineName() {
        return engineName;
    }

    public void setEngineName(String engineName) {
        this.engineName = engineName;
    }

    public String getEngineType() {
        return engineType;
    }

    public void setEngineType(String engineType) {
        this.engineType = engineType;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public List<AsymKeyIndexInfo> getAsymKeyIndexInfoList() {
        return asymKeyIndexInfoList;
    }

    public void setAsymKeyIndexInfoList(List<AsymKeyIndexInfo> asymKeyIndexInfoList) {
        this.asymKeyIndexInfoList = asymKeyIndexInfoList;
    }
}
