package kl.npki.base.management.model.cert.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import kl.npki.base.core.annotation.FieldRule;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.*;

/**
 * 管理根与身份证书签发请求参数
 *
 * <AUTHOR>
 */
public class CertSignRequest {

    /**
     * 通用名
     */
    @NotBlank(message = THE_CERTIFICATE_UNIVERSAL_NAME_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 32, message = THE_LENGTH_OF_THE_CERTIFICATE_GENERIC_NAME_CANNOT_EXCEED_32_CHARACTERS_I18N_KEY)
    @FieldRule(description = CERT_CHAIN_THE_CERTIFICATE_COMMON_NAME_DESC_I18N_KEY)
    @Schema(description = "证书通用名")
    private String cn;

    /**
     * 省
     */
    @Size(max = 128, message = THE_DATA_LENGTH_OF_PROVINCE_INFORMATION_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Schema(description = "省")
    @FieldRule(description = PROVINCE_NAME_DESC_I18N_KEY)
    private String province;

    /**
     * 市
     */
    @Size(max = 128, message = THE_DATA_LENGTH_OF_CITY_INFORMATION_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Schema(description = "市")
    @FieldRule(description = CITY_DESC_I18N_KEY)
    private String city;

    /**
     * 机构
     */
    @Size(max = 128, message = THE_DATA_LENGTH_OF_INSTITUTIONAL_INFORMATION_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Schema(description = "机构")
    @FieldRule(description = ORGANIZATION_NAME_DESC_I18N_KEY)
    private String org;

    /**
     * 单位
     */
    @Size(max = 128, message = THE_DATA_LENGTH_OF_UNIT_INFORMATION_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @FieldRule(description = UNIT_NAME_DESC_I18N_KEY)
    @Schema(description = "组织")
    private String unit;

    /**
     * 证书有效时间（单位:天）
     */
    @NotNull(message = THE_VALIDITY_PERIOD_OF_THE_CERTIFICATE_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "证书有效时间（单位:天）")
    private Long validDays;

    /**
     * 证书密钥对索引
     */
    @Schema(description = "证书密钥对索引")
    private Integer keyIndex;

    /**
     * 密钥类型
     */
    @Size(max = 128, message = THE_LENGTH_OF_THE_KEY_TYPE_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = THE_KEY_TYPE_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "密钥类型")
    private String keyType;

    /**
     * 抗量子加密密钥类型
     */
    @Size(max = 128, message = THE_LENGTH_OF_THE_KEY_TYPE_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = THE_ENCRYPTION_KEY_TYPE_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "抗量子加密密钥类型")
    private String pqEncKeyType;

    @Schema(description = "是否是双证书")
    private Boolean twinCert;

    /**
     *  是否自签发(默认走自签发流程)
     */
    @Schema(description = "是否自签发")
    private Boolean selfSign = true;

    /**
     * keyStore密码
     */
    @Size(max = 128, message = THE_KEYSTORE_PASSWORD_CANNOT_BE_GREATER_THAN_128_CHARACTERS_I18N_KEY)
    @Schema(description = "keyStore密码")
    private String keyStorePwd;

    @Schema(description = "主体备用名")
    private String subjectAlternativeName;

    public long getValidDays() {
        return validDays;
    }

    public CertSignRequest setValidDays(long validDays) {
        this.validDays = validDays;
        return this;
    }

    public int getKeyIndex() {
        return keyIndex;
    }

    public CertSignRequest setKeyIndex(int keyIndex) {
        this.keyIndex = keyIndex;
        return this;
    }

    public String getKeyType() {
        return keyType;
    }

    public CertSignRequest setKeyType(String keyType) {
        this.keyType = keyType;
        return this;
    }

    public String getCn() {
        return cn;
    }

    public void setCn(String cn) {
        this.cn = cn;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getOrg() {
        return org;
    }

    public String getPqEncKeyType() {
        return pqEncKeyType;
    }

    public void setPqEncKeyType(String pqEncKeyType) {
        this.pqEncKeyType = pqEncKeyType;
    }

    public void setOrg(String org) {
        this.org = org;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public void setValidDays(Long validDays) {
        this.validDays = validDays;
    }

    public void setKeyIndex(Integer keyIndex) {
        this.keyIndex = keyIndex;
    }

    public String getKeyStorePwd() {
        return keyStorePwd;
    }

    public void setKeyStorePwd(String keyStorePwd) {
        this.keyStorePwd = keyStorePwd;
    }

    public Boolean getTwinCert() {
        return twinCert;
    }

    public void setTwinCert(Boolean twinCert) {
        this.twinCert = twinCert;
    }

    public Boolean getSelfSign() {
        return selfSign;
    }

    public void setSelfSign(Boolean selfSign) {
        this.selfSign = selfSign;
    }

    public String getSubjectAlternativeName() {
        return subjectAlternativeName;
    }

    public CertSignRequest setSubjectAlternativeName(String subjectAlternativeName) {
        this.subjectAlternativeName = subjectAlternativeName;
        return this;
    }
}
