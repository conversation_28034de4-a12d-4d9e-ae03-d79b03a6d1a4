package kl.npki.base.management.model.admin.request;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import kl.npki.base.core.common.json.deserializer.StringToLongDeserializer;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.*;

/**
 * <AUTHOR>
 * @date 2022/6/24
 * @desc 注册管理员请求信息
 */

public class RegisterAdminRequest {

    /**
     * 用户角色id
     */
    @NotNull(message = ROLE_INFORMATION_SHOULD_NOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = THE_LENGTH_OF_CHARACTER_INFORMATION_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = CHARACTER_ENCODING_CANNOT_USE_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "角色编码")
    private String roleCode;

    @Schema(description = "组织机构ID")
    @JsonDeserialize(using = StringToLongDeserializer.class)
    private Long orgId;

    /**
     * 人员姓名
     */
    @NotBlank(message = ADMINISTRATOR_NAME_SHOULD_NOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = THE_LENGTH_OF_THE_ADMINISTRATOR_NAME_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = THE_NAME_OF_THE_CORNER_ADMINISTRATOR_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "管理员姓名")
    private String subjectCn;

    @NotBlank(message = PASSWORD_CANNOT_BE_EMPTY_I18N_KEY)
    @Pattern(regexp = "^[0-9a-f]{64}$|^[0-9A-F]{64}$", message = PASSWORD_DIGEST_FORMAT_ERROR_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "密码hash")
    private String passwordHash;

    /**
     * 组织
     */
    @Size(max = 128, message = THE_DATA_LENGTH_OF_ORGANIZATIONAL_INFORMATION_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Schema(description = "组织")
    private String organization;

    /**
     * 机构
     */
    @Size(max = 128, message = THE_DATA_LENGTH_OF_INSTITUTIONAL_INFORMATION_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Schema(description = "机构")
    private String organizationUnit;

    /**
     * 省
     */
    @Size(max = 128, message = THE_DATA_LENGTH_OF_PROVINCE_INFORMATION_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Schema(description = "省")
    private String province;

    /**
     * 市
     */
    @Size(max = 128, message = THE_DATA_LENGTH_OF_CITY_INFORMATION_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Schema(description = "市")
    private String city;

    /**
     * 邮箱
     */
    @Email(message = EMAIL_ERROR_I18N_KEY)
    @Schema(description = "邮箱")
    private String email;

    public String getRoleCode() {
        return roleCode;
    }

    public RegisterAdminRequest setRoleCode(String roleCode) {
        this.roleCode = roleCode;
        return this;
    }

    public String getSubjectCn() {
        return subjectCn;
    }

    public RegisterAdminRequest setSubjectCn(String subjectCn) {
        this.subjectCn = subjectCn;
        return this;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrganization() {
        return organization;
    }

    public RegisterAdminRequest setOrganization(String organization) {
        this.organization = organization;
        return this;
    }

    public String getOrganizationUnit() {
        return organizationUnit;
    }

    public RegisterAdminRequest setOrganizationUnit(String organizationUnit) {
        this.organizationUnit = organizationUnit;
        return this;
    }

    public String getProvince() {
        return province;
    }

    public RegisterAdminRequest setProvince(String province) {
        this.province = province;
        return this;
    }

    public String getCity() {
        return city;
    }

    public RegisterAdminRequest setCity(String city) {
        this.city = city;
        return this;
    }

    public String getEmail() {
        return email;
    }

    public RegisterAdminRequest setEmail(String email) {
        this.email = email;
        return this;
    }

    public String getPasswordHash() {
        return passwordHash;
    }

    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }

    @Override
    public String toString() {
        return "RegisterAdminRequest{" +
                ", roleCode=" + roleCode +
                ", subjectCn='" + subjectCn + '\'' +
                ", organization='" + organization + '\'' +
                ", organizationUnit='" + organizationUnit + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", email='" + email + '\'' +
                '}';
    }
}
