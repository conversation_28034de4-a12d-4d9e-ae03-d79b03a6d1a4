package kl.npki.base.management.model.admin.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.LOGIN_MODE_CANNOT_BE_EMPTY_I18N_KEY;

/**
 * 登录模式配置请求类
 * <AUTHOR>
 */
public class UpdateLoginTypeRequest {

    @NotNull(message = LOGIN_MODE_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "登录模式", allowableValues = {"PWD","SIGN","SIGN_PWD","MULTI_SIGN_3_OF_5","MULTI_SIGN_2_OF_3"})
    private String loginType;

    public String getLoginType() {
        return loginType;
    }

    public void setLoginType(String loginType) {
        this.loginType = loginType;
    }
}
