package kl.npki.base.management.model.admin.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;

import java.util.List;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.THE_AUTHENTICATION_TYPE_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY;

/**
 * <AUTHOR>
 * @date 2023/5/25
 */
public class LoginRequest {

    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = THE_AUTHENTICATION_TYPE_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "认证类型", allowableValues = "示例【 pwd:口令登录, cert_sign:证书登录, ssl:https登录】")
    private String authnType;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "图片验证码")
    private String captcha;

    /**
     * 主管理员证书登录信息
     */
    private CertLoginRequest mainAdmin;

    /**
     * 5选3 3选2 模式中的其它管理员证书信息
     */
    private List<CertLoginRequest> otherAdmins;

    @Schema(description = "认证ID")
    private String authRefId;

    public CertLoginRequest getMainAdmin() {
        return mainAdmin;
    }

    public void setMainAdmin(CertLoginRequest mainAdmin) {
        this.mainAdmin = mainAdmin;
    }

    public List<CertLoginRequest> getOtherAdmins() {
        return otherAdmins;
    }

    public void setOtherAdmins(List<CertLoginRequest> otherAdmins) {
        this.otherAdmins = otherAdmins;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public LoginRequest setPassword(String password) {
        this.password = password;
        return this;
    }

    public String getAuthnType() {
        return authnType;
    }

    public LoginRequest setAuthnType(String authnType) {
        this.authnType = authnType;
        return this;
    }

    public String getCaptcha() {
        return captcha;
    }

    public void setCaptcha(String captcha) {
        this.captcha = captcha;
    }

    public String getAuthRefId() {
        return authRefId;
    }

    public LoginRequest setAuthRefId(String authRefId) {
        this.authRefId = authRefId;
        return this;
    }

    @Override
    public String toString() {
        return "LoginRequest{" +
            "authnType='" + authnType + '\'' +
            ", username='" + username + '\'' +
            ", password='" + password + '\'' +
            ", captcha='" + captcha + '\'' +
            ", mainAdmin=" + mainAdmin +
            ", otherAdmins=" + otherAdmins +
            ", authRefId=" + authRefId +
            '}';
    }
}
