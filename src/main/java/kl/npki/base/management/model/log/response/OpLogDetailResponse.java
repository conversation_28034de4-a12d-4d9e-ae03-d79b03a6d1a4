package kl.npki.base.management.model.log.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 操作日志请求响应
 *
 * <AUTHOR>
 * @date 2022/12/13
 */
public class OpLogDetailResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "操作日志id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long logId;

    @Schema(description = "操作名称")
    private String logDo;

    @Schema(description = "操作结果")
    private Boolean result;

    @Schema(description = "客户端ip")
    private String clientIp;

    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @Schema(description = "操作时间")
    private LocalDateTime logWhen;

    @Schema(description = "操作详情")
    private String logWhat;

    @Schema(description = "请求")
    private String request;

    @Schema(description = "用户名")
    private String username;

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public String getLogDo() {
        return logDo;
    }

    public void setLogDo(String logDo) {
        this.logDo = logDo;
    }

    public Boolean getResult() {
        return result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public LocalDateTime getLogWhen() {
        return logWhen;
    }

    public void setLogWhen(LocalDateTime logWhen) {
        this.logWhen = logWhen;
    }

    public String getLogWhat() {
        return logWhat;
    }

    public void setLogWhat(String logWhat) {
        this.logWhat = logWhat;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
}
