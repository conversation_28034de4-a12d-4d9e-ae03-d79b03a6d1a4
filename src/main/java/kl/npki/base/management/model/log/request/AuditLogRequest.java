package kl.npki.base.management.model.log.request;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.nbase.db.support.mybatis.query.QueryField;
import kl.nbase.db.support.mybatis.query.QueryFieldOpType;
import kl.nbase.db.support.mybatis.query.QueryInfo;

import java.time.LocalDateTime;

/**
 * 日志审计请求体
 *
 * <AUTHOR>
 * @date 2023/3/21
 */
public class AuditLogRequest extends QueryInfo {

    /**
     * 业务、操作名称
     */
    @QueryField(value = "log_do", op = QueryFieldOpType.LIKE)
    @Schema(description = "业务名称，操作名称")
    private String opName;

    /**
     * 请求结果，0 代表失败， 1 表示成功
     */
    @QueryField(value = "result", op = QueryFieldOpType.EQ)
    @Schema(description = "执行结果")
    private Boolean result;

    /**
     * 请求结果，-1 不审计 0-待审计，1-审计成功，2-审计失败
     */
    @QueryField(value = "audit_status", op = QueryFieldOpType.EQ)
    @Schema(description = "审计状态")
    private Integer auditStatus;

    /**
     * 开始时间
     */
    @QueryField(value = "log_when", op = QueryFieldOpType.GE)
    @Schema(description = "开始时间")
    private LocalDateTime beginDate;

    /**
     * 结束时间
     */
    @QueryField(value = "log_when", op = QueryFieldOpType.LE)
    @Schema(description = "结束时间")
    private LocalDateTime endDate;

    /**
     * 客户端IP
     */
    @QueryField(value = "client_ip", op = QueryFieldOpType.LIKE)
    @Schema(description = "客户端ip")
    private String clientIp;

    /**
     * 用户名
     */
    @QueryField(value = "username", op = QueryFieldOpType.LIKE)
    @Schema(description = "用户名")
    private String username;

    public String getOpName() {
        return opName;
    }

    public void setOpName(String opName) {
        this.opName = opName;
    }

    public Boolean getResult() {
        return result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public LocalDateTime getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(LocalDateTime beginDate) {
        this.beginDate = beginDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    @Override
    public String toString() {
        return "AuditLogRequest{" +
            "opName='" + opName + '\'' +
            ", result=" + result +
            ", auditStatus=" + auditStatus +
            ", beginDate=" + beginDate +
            ", endDate=" + endDate +
            ", clientIp='" + clientIp + '\'' +
            ", username='" + username + '\'' +
            '}';
    }
}
