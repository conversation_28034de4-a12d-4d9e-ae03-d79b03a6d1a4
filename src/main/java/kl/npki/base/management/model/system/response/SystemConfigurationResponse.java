package kl.npki.base.management.model.system.response;

/**
 * <AUTHOR>
 * @date 2022/9/1 10:52
 */
public class SystemConfigurationResponse {
    /**
     * 主键id
     */
    private Integer id;
    /**
     * 部署步骤名称
     */
    private String configName;
    /**
     * 部署步骤序号
     */
    private Integer configOrder;
    /**
     * 对应配置文件名称
     */
    private String configFileName;
    /**
     * 当前步骤是否已完成
     */
    private Integer isComplete;



    public Integer getId() {
        return id;
    }

    public SystemConfigurationResponse setId(Integer id) {
        this.id = id;
        return this;
    }

    public String getConfigName() {
        return configName;
    }

    public SystemConfigurationResponse setConfigName(String configName) {
        this.configName = configName;
        return this;
    }

    public Integer getConfigOrder() {
        return configOrder;
    }

    public SystemConfigurationResponse setConfigOrder(Integer configOrder) {
        this.configOrder = configOrder;
        return this;
    }

    public String getConfigFileName() {
        return configFileName;
    }

    public SystemConfigurationResponse setConfigFileName(String configFileName) {
        this.configFileName = configFileName;
        return this;
    }

    public Integer getIsComplete() {
        return isComplete;
    }

    public SystemConfigurationResponse setIsComplete(Integer isComplete) {
        this.isComplete = isComplete;
        return this;
    }
}
