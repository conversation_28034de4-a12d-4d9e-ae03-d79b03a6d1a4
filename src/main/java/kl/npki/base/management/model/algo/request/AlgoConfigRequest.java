package kl.npki.base.management.model.algo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import kl.npki.management.core.biz.config.model.AlgoConfigInfo;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.*;

/**
 * 密码算法配置请求
 *
 * <AUTHOR>
 * @date 2024/4/3
 */
public class AlgoConfigRequest implements Serializable {

    private static final long serialVersionUID = 786612416280833905L;

    /**
     * 对称密钥算法
     */
    @NotBlank(message = SYMMETRIC_KEY_ALGORITHM_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = SYMMETRIC_KEY_ALGORITHM_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = SYMMETRIC_KEY_ALGORITHM_CANNOT_USE_SPACES_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "对称密钥算法", defaultValue = "SM4 、SM1 ")
    private String symAlgo;

    /**
     * 非对称密钥算法
     */
    @Schema(description = "非对称密钥算法")
    private List<String> asymAlgo;

    /**
     * Hash算法
     */
    @Schema(description = "Hash算法")
    private List<String> hashAlgo;

    /**
     * 抗量子密码算法
     */
    @Schema(description = "抗量子密码算法")
    private List<String> pqcAlgo;

    public String getSymAlgo() {
        return symAlgo;
    }

    public void setSymAlgo(String symAlgo) {
        this.symAlgo = symAlgo;
    }

    public List<String> getAsymAlgo() {
        return asymAlgo;
    }

    public void setAsymAlgo(List<String> asymAlgo) {
        this.asymAlgo = asymAlgo;
    }

    public List<String> getHashAlgo() {
        return hashAlgo;
    }

    public void setHashAlgo(List<String> hashAlgo) {
        this.hashAlgo = hashAlgo;
    }

    public List<String> getPqcAlgo() {
        return pqcAlgo;
    }

    public void setPqcAlgo(List<String> pqcAlgo) {
        this.pqcAlgo = pqcAlgo;
    }

    public AlgoConfigInfo toAlgoConfigInfo() {
        AlgoConfigInfo algoConfigInfo = new AlgoConfigInfo();
        algoConfigInfo.setSymAlgo(Collections.singletonList(this.symAlgo));
        algoConfigInfo.setAsymAlgo(this.asymAlgo);
        algoConfigInfo.setHashAlgo(this.hashAlgo);
        algoConfigInfo.setPqcAlgo(this.pqcAlgo);
        return algoConfigInfo;
    }
}
