package kl.npki.base.management.model.constant.response;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 通用常量响应
 *
 * <AUTHOR>
 * @date 2025/5/7
 */
public class BaseConstantResponse implements Serializable {

    @Schema(description = "编码，若常量没有定义code则可通过name区分")
    private Integer code;

    @Schema(description = "名称，若常量没有定义code则可通过name区分")
    private String name;

    @Schema(description = "国际化后的描述，若常量无需翻译则可能返回空")
    private String desc;

    public BaseConstantResponse() {
    }

    public BaseConstantResponse(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
