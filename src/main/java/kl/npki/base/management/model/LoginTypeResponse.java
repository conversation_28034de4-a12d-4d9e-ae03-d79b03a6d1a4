/**
 * @projectName npki-ca-management
 * @package kl.npki.base.management.model
 * @className kl.npki.base.management.model.LoginTypeResponse
 */
package kl.npki.base.management.model;

import kl.npki.base.core.constant.LoginType;

/**
 * <AUTHOR>
 * @date 2025/6/26 15:39
 */
public class LoginTypeResponse {
    /**
     * 登录方式
     * {@link LoginType#getType()}
     */
    private String type;

    /**
     * 登录方式描述
     * {@link LoginType#getDesc()}
     */
    private String desc;

    /**
     * 登录方式模式
     * {@link LoginType#getMode()}
     */
    private String mode;

    public String getType() {
        return type;
    }

    public LoginTypeResponse setType(String type) {
        this.type = type;
        return this;
    }

    public String getDesc() {
        return desc;
    }

    public LoginTypeResponse setDesc(String desc) {
        this.desc = desc;
        return this;
    }

    public String getMode() {
        return mode;
    }

    public LoginTypeResponse setMode(String mode) {
        this.mode = mode;
        return this;
    }
}
