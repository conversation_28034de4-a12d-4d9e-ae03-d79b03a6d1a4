package kl.npki.base.management.model.org;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 新增加机构请求
 *
 * <AUTHOR>
 * @create 2024/3/22 10:50
 */
public class OrgAddRequest {

    @NotBlank(message = "kl.npki.base.management.i18n_annotation.institution_name")
    @Size(max = 256, message = "kl.npki.base.management.i18n_annotation.the_length_of_the_institution_name_cannot_exceed_256")
    @Schema(description = "机构名称")
    private String orgName;

    //message = 机构全称长度不能大于256字符
    @Size(max = 256, message = "kl.npki.base.management.i18n_annotation.the_length_of_the_full_name_of_the_institution_cannot_exceed_256")
    @Schema(description = "机构全称")
    private String fullName;

    //message = 机构名称拼音不能大于256字符
    @Size(max = 256, message = "kl.npki.base.management.i18n_annotation.the_pinyin_of_the_institution_name_cannot_exceed_256")
    @Schema(description = "机构名称拼音")
    private String namePinyin;

    //message = 机构编码
    @NotBlank(message = "kl.npki.base.management.i18n_annotation.institution_code")
    //message = 机构编码不能大于256字符
    @Size(max = 256, message = "kl.npki.base.management.i18n_annotation.institution_code_cannot_exceed_256")
    @Schema(description = "机构编码")
    private String orgCode;

    @Schema(description = "上级机构")
    private Long parentId;

    //message = 上级机构编码
    @NotBlank(message = "kl.npki.base.management.i18n_annotation.superior_institution_code")
    //message = 上级机构编码不能大于256字符
    @Size(max = 256, message = "kl.npki.base.management.i18n_annotation.the_superior_organization_code_cannot_exceed_256")
    @Schema(description = "上级机构编码")
    private String parentCode;

    @Schema(description = "机构类型")
    private Integer orgType;

    //message = 联系人不能大于256字符
    @Size(max = 256, message = "kl.npki.base.management.i18n_annotation.contact_person_cannot_exceed_256")
    @Schema(description = "联系人")
    private String linkman;

    //message = 联系电话不能大于256字符
    @Size(max = 256, message = "kl.npki.base.management.i18n_annotation.the_contact_phone_number_cannot_exceed_256")
    @Schema(description = "联系电话")
    private String telephone;

    //message = 邮政编码不能大于256字符
    @Size(max = 256, message = "kl.npki.base.management.i18n_annotation.the_postal_code_cannot_exceed_256")
    @Schema(description = "邮政编码")
    private String postcode;

    //message = 联系地址不能大于256字符
    @Size(max = 256, message = "kl.npki.base.management.i18n_annotation.the_contact_address_cannot_exceed_256")
    @Schema(description = "联系地址")
    private String address;

    //message = 机构描述不能大于256字符
    @Size(max = 256, message = "kl.npki.base.management.i18n_annotation.institution_description_cannot_exceed_256")
    @Schema(description = "机构描述")
    private String orgDesc;

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getNamePinyin() {
        return namePinyin;
    }

    public void setNamePinyin(String namePinyin) {
        this.namePinyin = namePinyin;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public String getLinkman() {
        return linkman;
    }

    public void setLinkman(String linkman) {
        this.linkman = linkman;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getOrgDesc() {
        return orgDesc;
    }

    public void setOrgDesc(String orgDesc) {
        this.orgDesc = orgDesc;
    }
}