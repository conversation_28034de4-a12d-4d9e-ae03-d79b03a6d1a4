package kl.npki.base.management.model.log.request;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.nbase.db.support.mybatis.query.QueryField;
import kl.nbase.db.support.mybatis.query.QueryFieldOpType;
import kl.nbase.db.support.mybatis.query.QueryInfo;

import java.time.LocalDateTime;

/**
 * 服务日志查询请求体
 *
 * <AUTHOR>
 * @date 2023/3/15
 */
public class ApiLogRequest extends QueryInfo {

    /**
     * 业务、操作名称
     */
    @QueryField(value = "biz", op = QueryFieldOpType.RIGHT_LIKE)
    @Schema(description = "业务名称，操作名称")
    private String bizName;

    /**
     * 请求结果，0 代表失败， 1 表示成功
     */
    @QueryField(value = "result", op = QueryFieldOpType.EQ)
    @Schema(description = "执行结果")
    private Boolean result;

    /**
     * 开始时间
     */
    @QueryField(value = "log_when", op = QueryFieldOpType.GE)
    @Schema(description = "开始时间")
    private LocalDateTime beginDate;

    /**
     * 结束时间
     */
    @QueryField(value = "log_when", op = QueryFieldOpType.LE)
    @Schema(description = "结束时间")
    private LocalDateTime endDate;

    /**
     * 客户端IP
     */
    @QueryField(value = "client_ip", op = QueryFieldOpType.RIGHT_LIKE)
    @Schema(description = "客户端ip")
    private String clientIp;


    @QueryField(value = "caller_name", op = QueryFieldOpType.EQ)
    @Schema(description = "调用者名称")
    private String callerName;


    public String getBizName() {
        return bizName;
    }

    public void setBizName(String bizName) {
        this.bizName = bizName;
    }

    public Boolean getResult() {
        return result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    public LocalDateTime getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(LocalDateTime beginDate) {
        this.beginDate = beginDate;
    }

    public LocalDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDateTime endDate) {
        this.endDate = endDate;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getCallerName() {
        return callerName;
    }

    public void setCallerName(String callerName) {
        this.callerName = callerName;
    }

}
