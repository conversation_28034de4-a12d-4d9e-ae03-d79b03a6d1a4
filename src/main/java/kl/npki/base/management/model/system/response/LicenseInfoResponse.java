package kl.npki.base.management.model.system.response;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.base.core.biz.license.model.LicenseCountInfo;
import kl.security.license.bean.CustomLicenseItem;

import java.io.Serializable;
import java.util.Map;

/**
 * License 信息响应
 *
 * <AUTHOR>
 * @since 2023/1/31
 */
@Schema(description = "License信息响应")
public class LicenseInfoResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 版本
     */
    @Schema(description = "授权版本号")
    private String version;
    /**
     * 用途
     */
    @Schema(description = "授权用途")
    private String purpose;
    /**
     * 机器码
     */
    @Schema(description = "机器码")
    private String serialNumber;

    /**
     * License状态
     */
    @Schema(description = "授权状态")
    private String licenseStatus;

    /**
     * 起始时间
     */
    @Schema(description = "授权起始时间")
    private String validStart;

    /**
     * 结束时间
     */
    @Schema(description = "授权结束时间")
    private String validEnd;

    /**
     * 自定义字段限制
     */
    @Schema(description = "定制授权项")
    private Map<String, CustomLicenseItem> cusMap;

    /**
     * 已使用自定义字段限制数量
     */
    @Schema(description = "定制授权项使用情况")
    private Map<String, LicenseCountInfo> cusUsedMap;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getValidStart() {
        return validStart;
    }

    public void setValidStart(String validStart) {
        this.validStart = validStart;
    }

    public String getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(String validEnd) {
        this.validEnd = validEnd;
    }

    public Map<String, CustomLicenseItem> getCusMap() {
        return cusMap;
    }

    public void setCusMap(Map<String, CustomLicenseItem> cusMap) {
        this.cusMap = cusMap;
    }

    public Map<String, LicenseCountInfo> getCusUsedMap() {
        return cusUsedMap;
    }

    public void setCusUsedMap(Map<String, LicenseCountInfo> cusUsedMap) {
        this.cusUsedMap = cusUsedMap;
    }

    public String getLicenseStatus() {
        return licenseStatus;
    }

    public void setLicenseStatus(String licenseStatus) {
        this.licenseStatus = licenseStatus;
    }

    @Override
    public String toString() {
        return "LicenseInfoResponse{" +
            "cusMap=" + cusMap +
            ", version='" + version + '\'' +
            ", purpose='" + purpose + '\'' +
            ", serialNumber='" + serialNumber + '\'' +
            ", licenseStatus='" + licenseStatus + '\'' +
            ", validStart='" + validStart + '\'' +
            ", validEnd='" + validEnd + '\'' +
            ", cusUsedMap=" + cusUsedMap +
            '}';
    }
}
