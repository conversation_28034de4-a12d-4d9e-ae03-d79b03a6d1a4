package kl.npki.base.management.model.batch;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * 批量基础操请求
 *
 * <AUTHOR> rs
 * @date 2024/7/5 17:32
 */
public class BaseBatchRequest {

    /**
     * 操作主键ID
     */
    @Schema(description = "操作主键ID集合")
    //message = 操作主键ID集合不能为空
    @NotNull(message = "kl.npki.ra.management.i18n_annotation.the_set_of_primary_key_ids_for_operation_cannot_be_empty")
    //message = idList最少需要包含一个ID
    @Size(min = 1, message = "kl.npki.ra.management.i18n_annotation.idlist_needs_to_contain_at_least_one_id")
    private List<Long> idList;
    public List<Long> getIdList() {
        return idList;
    }

    public void setIdList(List<Long> idList) {
        this.idList = idList;
    }
}
