package kl.npki.base.management.model.admin.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import javax.validation.constraints.NotNull;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.*;

/**
 * 管理员修改密码请求体
 *
 * <AUTHOR>
 * @date 2023/2/15
 */
public class AdminUpdatePwdRequest {

    /**
     * 管理员Id
     */
    @NotNull(message = ADMINISTRATOR_ID_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "管理员id")
    private Long adminInfoId;

    /**
     * 旧密码的摘要
     */
    @NotBlank(message = OLD_PASSWORD_CANNOT_BE_EMPTY_I18N_KEY)
    @Size(max = 128, message = THE_LENGTH_OF_THE_OLD_PASSWORD_DIGEST_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY)
    @Pattern(regexp = "^(?!\\s|%20|%0a|%00).*(?<!\\s|%20|%0a|%00)$", message = OLD_PASSWORD_DIGEST_CANNOT_CONTAIN_SPACES_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "旧密码摘要")
    private String oldPwdHash;

    /**
     * 新密码的摘要
     */
    @NotBlank(message = THE_NEW_PASSWORD_CANNOT_BE_EMPTY_I18N_KEY)
    @Pattern(regexp = "^[0-9a-f]{64}$|^[0-9A-F]{64}$", message = NEW_PASSWORD_DIGEST_FORMAT_ERROR_I18N_KEY, flags = Pattern.Flag.DOTALL)
    @Schema(description = "新密码摘要")
    private String newPwdHash;

    public Long getAdminInfoId() {
        return adminInfoId;
    }

    public void setAdminInfoId(Long adminInfoId) {
        this.adminInfoId = adminInfoId;
    }

    public String getOldPwdHash() {
        return oldPwdHash;
    }

    public void setOldPwdHash(String oldPwdHash) {
        this.oldPwdHash = oldPwdHash;
    }

    public String getNewPwdHash() {
        return newPwdHash;
    }

    public void setNewPwdHash(String newPwdHash) {
        this.newPwdHash = newPwdHash;
    }
}
