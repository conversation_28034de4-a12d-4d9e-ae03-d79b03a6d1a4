package kl.npki.base.management.model.permission.request;

import kl.nbase.db.support.mybatis.query.QueryField;
import kl.nbase.db.support.mybatis.query.QueryFieldOpType;
import kl.nbase.db.support.mybatis.query.QueryInfo;

/**
 * <AUTHOR>
 * @date 2022/10/1
 * @desc
 */
public class RoleListRequest extends QueryInfo {
    /**
     * 角色名
     */
    @QueryField(value = "role_name", op = QueryFieldOpType.LIKE)
    private String roleName;

    /**
     * 角色类型
     */
    @QueryField(value = "is_custom", op = QueryFieldOpType.EQ)
    private Integer isCustom;


    public String getRoleName() {
        return roleName;
    }

    public RoleListRequest setRoleName(String roleName) {
        this.roleName = roleName;
        return this;
    }

    public Integer getIsCustom() {
        return isCustom;
    }

    public RoleListRequest setIsCustom(Integer isCustom) {
        this.isCustom = isCustom;
        return this;
    }


    @Override
    public String toString() {
        return "RoleListRequest{" +
               "roleName='" + roleName + '\'' +
               ", isCustom=" + isCustom +
               '}';
    }
}
