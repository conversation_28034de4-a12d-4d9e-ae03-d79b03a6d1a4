package kl.npki.base.management.model.upload;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @create 2024/5/12 上午10:54
 */
public class InterfaceNameResponse {

    @Schema(description = "业务接口名称")
    private String interfaceName;

    @Schema(description = "业务接口描述")
    private String desc;

    public String getInterfaceName() {
        return interfaceName;
    }

    public InterfaceNameResponse setInterfaceName(String interfaceName) {
        this.interfaceName = interfaceName;
        return this;
    }

    public String getDesc() {
        return desc;
    }

    public InterfaceNameResponse setDesc(String desc) {
        this.desc = desc;
        return this;
    }
}