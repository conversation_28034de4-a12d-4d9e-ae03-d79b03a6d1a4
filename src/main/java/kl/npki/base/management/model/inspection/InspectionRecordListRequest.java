package kl.npki.base.management.model.inspection;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.nbase.db.support.mybatis.query.QueryField;
import kl.nbase.db.support.mybatis.query.QueryFieldOpType;
import kl.nbase.db.support.mybatis.query.QueryInfo;

/**
 * 系统巡检记录列表响应
 *
 * <AUTHOR>
 * @date 07/05/2025 19:54
 **/
public class InspectionRecordListRequest extends QueryInfo {

    /**
     * 本次巡检名称
     */
    @Schema(description = "本次巡检名称")
    @QueryField(value = "NAME", op = QueryFieldOpType.LIKE)
    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}