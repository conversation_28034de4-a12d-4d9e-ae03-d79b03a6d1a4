package kl.npki.base.management.model.log.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.FILE_ID_CANNOT_BE_EMPTY_I18N_KEY;
import static kl.npki.base.management.constant.I18nParameterVerifyConstant.SIGNER_CERTIFICATE_CHAIN_CANNOT_BE_EMPTY_I18N_KEY;

/**
 * 导出Excel电子签名之前的，Excel电子签名原文导出请求
 *
 * <AUTHOR>
 * @create 2025/2/11 上午11:52
 */
public class ApiLogExcelPlaintextRequest extends ApiLogExcelRequest {

    @NotEmpty(message = SIGNER_CERTIFICATE_CHAIN_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "签名者证书链不能为空")
    private List<String> signerCertChain;

    @NotNull(message = FILE_ID_CANNOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "文件ID不能为空")
    private Long fileId;

    public List<String> getSignerCertChain() {
        return signerCertChain;
    }

    public ApiLogExcelPlaintextRequest setSignerCertChain(List<String> signerCertChain) {
        this.signerCertChain = signerCertChain;
        return this;
    }

    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }
}