package kl.npki.base.management.model.admin.request;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 证书登录时的请求信息
 *
 * <AUTHOR>
 */
public class CertLoginRequest {
    @Schema(description = "签名数据")
    private String signData;

    @Schema(description = "证书序列号")
    private String certSn;

    public String getSignData() {
        return signData;
    }

    public void setSignData(String signData) {
        this.signData = signData;
    }

    public String getCertSn() {
        return certSn;
    }

    public void setCertSn(String certSn) {
        this.certSn = certSn;
    }

    @Override
    public String toString() {
        return "CertLoginRequest{" +
            "signData='" + signData + '\'' +
            ", certSn='" + certSn + '\'' +
            '}';
    }
}
