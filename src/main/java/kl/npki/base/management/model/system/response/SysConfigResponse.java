package kl.npki.base.management.model.system.response;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @Date 2023/10/16
 */
public class SysConfigResponse {
    @Schema(description = "是否显示微软CSP设备")
    private boolean showCSP;

    @Schema(description = "是否开启完整性检查")
    private boolean openDataFull;

    @Schema(description = "是否加密请求体")
    private boolean encryptRequestBody;

    /**
     * 性能测试配置
     */
    @Schema(description = "高性能模式", defaultValue = "false")
    private boolean performanceTest;

    /**
     * PKI中间件版本号
     */
    private String pkiClientVersion;

    public boolean isShowCSP() {
        return showCSP;
    }

    public boolean isOpenDataFull() {
        return openDataFull;
    }

    public SysConfigResponse setOpenDataFull(boolean openDataFull) {
        this.openDataFull = openDataFull;
        return this;
    }

    public void setShowCSP(boolean showCSP) {
        this.showCSP = showCSP;
    }

    public boolean isEncryptRequestBody() {
        return encryptRequestBody;
    }

    public void setEncryptRequestBody(boolean encryptRequestBody) {
        this.encryptRequestBody = encryptRequestBody;
    }

    public boolean isPerformanceTest() {
        return performanceTest;
    }

    public void setPerformanceTest(boolean performanceTest) {
        this.performanceTest = performanceTest;
    }

    public String getPkiClientVersion() {
        return pkiClientVersion;
    }

    public void setPkiClientVersion(String pkiClientVersion) {
        this.pkiClientVersion = pkiClientVersion;
    }

}
