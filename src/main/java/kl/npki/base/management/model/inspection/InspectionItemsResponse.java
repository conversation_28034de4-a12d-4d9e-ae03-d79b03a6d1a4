package kl.npki.base.management.model.inspection;

import io.swagger.v3.oas.annotations.media.Schema;
import kl.npki.management.core.biz.inspection.model.InspectionGroupedItems;

import java.util.List;

/**
 * 系统巡检项目响应
 *
 * <AUTHOR>
 * @date 07/05/2025 19:56
 **/
public class InspectionItemsResponse {

    /**
     * 巡检项目信息
     */
    @Schema(description = "巡检项目列表")
    private List<InspectionGroupedItems> items;

    public InspectionItemsResponse(List<InspectionGroupedItems> items) {
        this.items = items;
    }

    public List<InspectionGroupedItems> getItems() {
        return items;
    }

    public void setItems(List<InspectionGroupedItems> items) {
        this.items = items;
    }
}
