package kl.npki.base.management.model.admin.response;

/**
 * <AUTHOR>
 * @since 2025/7/15 下午1:25
 */
public class CaptchaPayloadResponse {

    /**
     * 验证码在缓存组件中存储的id
     */
    private String authRefId;

    /**
     * base64编码的验证码
     */
    private String captcha;

    public String getAuthRefId() {
        return authRefId;
    }

    public CaptchaPayloadResponse setAuthRefId(String authRefId) {
        this.authRefId = authRefId;
        return this;
    }

    public String getCaptcha() {
        return captcha;
    }

    public CaptchaPayloadResponse setCaptcha(String captcha) {
        this.captcha = captcha;
        return this;
    }
}