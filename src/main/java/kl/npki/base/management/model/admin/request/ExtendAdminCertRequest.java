package kl.npki.base.management.model.admin.request;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

import static kl.npki.base.management.constant.I18nParameterVerifyConstant.THE_ADMINISTRATOR_ID_SHOULD_NOT_BE_EMPTY_I18N_KEY;
import static kl.npki.base.management.constant.I18nParameterVerifyConstant.THE_CERTIFICATE_EXTENSION_PERIOD_SHOULD_NOT_BE_LESS_THAN_1_DAY_I18N_KEY;

/**
 * 延期初始化三员证书请求
 *
 * <AUTHOR>
 */
public class ExtendAdminCertRequest {

    /**
     * 管理员id
     */
    @NotNull(message = THE_ADMINISTRATOR_ID_SHOULD_NOT_BE_EMPTY_I18N_KEY)
    @Schema(description = "管理员id")
    private Long adminInfoId;

    /**
     * 证书有效期
     */
    @Schema(description = "证书延期天数(天)")
    @Min(value = 1, message = THE_CERTIFICATE_EXTENSION_PERIOD_SHOULD_NOT_BE_LESS_THAN_1_DAY_I18N_KEY)
    private Integer extendDays;

    public @NotNull(message = THE_ADMINISTRATOR_ID_SHOULD_NOT_BE_EMPTY_I18N_KEY) Long getAdminInfoId() {
        return adminInfoId;

    }

    public void setAdminInfoId(@NotNull(message = THE_ADMINISTRATOR_ID_SHOULD_NOT_BE_EMPTY_I18N_KEY) Long adminInfoId) {
        this.adminInfoId = adminInfoId;
    }

    public Integer getExtendDays() {
        return extendDays;
    }

    public void setExtendDays(Integer extendDays) {
        this.extendDays = extendDays;
    }

    @Override
    public String toString() {
        return "ExtendAdminCertRequest{" +
            "adminInfoId=" + adminInfoId +
            ", validDays=" + extendDays +
            '}';
    }
}
