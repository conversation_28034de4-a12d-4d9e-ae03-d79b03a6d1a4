package kl.npki.base.management.exception;

import kl.nbase.exception.interfaces.IRemoteError;

import static kl.npki.base.management.constant.I18nConstant.BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX;

/**
 * <AUTHOR>
 * Created on 2022/08/24 11:16
 */
public enum ManagementRemoteError implements IRemoteError, ErrorCode {
    REMOTE_SERVICE_ERROR("000", "远端服务错误");

    private final String code;
    private final String desc;

    ManagementRemoteError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}