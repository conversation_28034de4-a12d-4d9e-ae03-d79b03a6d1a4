package kl.npki.base.management.exception;

import kl.nbase.exception.interfaces.IInternalError;

import static kl.npki.base.management.constant.I18nConstant.BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX;

/**
 * <AUTHOR>
 * Created on 2022/08/24 11:17
 */
public enum ManagementInternalError implements IInternalError, ErrorCode {
    SYSTEM_ERROR("000", "系统异常"),

    // 数据库异常
    DB_INSERT_ERROR("104", "数据新增异常"),
    DB_DELETE_ERROR("105", "数据删除异常"),
    DB_UPDATE_ERROR("106", "数据修改异常"),
    DB_QUERY_ERROR("107", "数据查询异常"),

    VERIFY_SIGNED_ERROR("110", "验证签名失败"),
    SIGN_KEY_NOT_FOUND("111", "签名密钥不存在"),
    // License异常
    LICENSE_PARSE_ERROR("112", "License解析失败"),
    // 文件操作异常
    FILE_DOWNLOAD_FAIL("123", "文件下载失败"),
    FILE_CREATE_FAIL("124", "文件创建失败"),

    // 完整性值验证失败
    VERIFY_FULL_DATA_HASH_ERROR("125", "数据完整性验证失败"),
    // 证书解析预览失败
    CERT_PREVIEW_ERROR("126", "证书解析预览失败"),

    // Excel文件处理异常
    EXCEL_FILE_ERROR("127", "Excel文件构造失败"),
    EXCEL_FILE_DIGEST_ERROR("128", "Excel文件哈希计算失败"),
    EXCEL_FILE_CREATE_ERROR("129", "Excel文件创建失败"),

    CERT_ENCODE_ERROR("135", "证书ASN结构编码失败"),

    PORT_USED_ERROR("150", "端口已被使用"),


    /**
     * 组织机构相关
     */
    ORG_NOT_INIT_ERROR("200", "组织机构未初始化"),

    /**
     * 文件操作
     */
    FILE_OPERATE_ERROR("300", "文件操作错误"),

    ;

    private final String code;
    private final String desc;

    ManagementInternalError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }

    @Override
    public String getDesc() {
        return desc;
    }
    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
