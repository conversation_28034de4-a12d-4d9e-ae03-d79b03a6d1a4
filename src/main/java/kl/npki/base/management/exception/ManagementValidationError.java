package kl.npki.base.management.exception;

import kl.nbase.exception.interfaces.IValidationError;

import static kl.npki.base.management.constant.I18nConstant.BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX;

/**
 * <AUTHOR>
 * Created on 2022/08/24 11:16
 */
public enum ManagementValidationError implements IValidationError, ErrorCode {
    // 管理员管理 101-200
    PARAM_ERROR("101", "参数错误"),
    BIZ_STATUS_ERROR("102", "业务状态错误"),
    ADMIN_NOT_EXISTS_ERROR("103", "无效的管理员"),
    ROLE_NOT_EXISTS("104", "角色不存在"),
    SYS_ROLE_NOT_REMOVE("105", "系统角色不能删除"),
    AUTH_REF_ID_ERROR("106", "认证ID不能为空"),
    INVALID_AUTH_TYPE("107", "无效的认证类型"),
    SSL_LOGIN_FAIL("109", "SSL登录验证失败"),
    ADMIN_USERNAME_OR_PWD_ERROR("110", "管理员账号或密码错误"),
    INVALID_CERT_SN("111", "无效的证书序列号"),
    ADMIN_ROLE_NOT_EXISTS("112", "管理员角色不存在"),
    OTHER_ADMIN_ROLE_INCONSISTENT("113", "其它管理员角色与主登录管理员角色不一致"),
    LOGIN_STATUS_EXPIRED("114", "用户未登录或登录状态过期"),
    LOGIN_STATUS_HAS_BEEN_REPLACED("115", "该账号已在其他地方登录，您已被迫下线，如非本人操作请重新登录并及时修改登录信息"),
    LOGIN_ACCOUNT_HAS_BEEN_LOCKED("116", "账号已被锁定"),

    OTHER_ADMIN_GROUP_INCONSISTENT("117", "其它管理员与主登录管理员组不一致，备份管理和初始化管理员不属于同一组！"),
    LOGIN_STATUS_IP_ERROR("118", "登录信息IP地址不一致，请重新登录"),
    KCSP_SSO_FAIL("119", "KCSP单点登录失败"),
    CAPTCHA_VERIFY_FAIL("120", "验证码错误"),
    ADMINISTRATOR_CERTIFICATE_DOES_NOT_EXIST("121", "管理员证书不存在"),

    ADMIN_IS_CANCEL("121", "管理员已被注销"),
    MANAGEMENT_ROOT_CERT_NOT_EXIST("122", "管理根证书不存在"),
    // 前端需要退出登录，不可随意修改错误码↓↓↓
    LOGIN_METHOD_HAS_BEEN_MODIFIED("123", "登录方式已被修改"),

    ROLE_NOT_EXISTS_BY_CODE("124", "根据角色编码未找到角色"),
    ADMIN_INFO_ERROR("125", "管理员信息异常，请重新登录"),



    /**
     * 参数类异常
     */
    UNSUPPORTED_DB_TYPE("201", "不支持的数据库类型"),
    NO_PERMISSION("202", "没有权限"),
    ROLE_CODE_IS_NOT_NUMERIC("203", "角色编码必须为数字"),
    ASYM_ALGO_NAME_REPEAT_ERROR("204", "非对称算法名称重复"),
    RESOURCE_CODE_CANNOT_EQUALS_PARENT_CODE("205", "资源编码不能等于父级资源编码"),
    ROLE_CODE_CANNOT_EQUALS_PARENT_CODE("206", "角色编码不能等于父级角色编码"),
    RESOURCE_CODE_ALREADY_EXISTS("208", "资源编码已存在"),

    /**
     * 管理员
     */
    QUERY_BACKUP_ADMIN_ERROR("230", "该角色不支持查看备份管理员列表"),

    /**
     * 完整性校验异常
     */
    CONFIG_FILE_VERIFY_HMAC_FAILED("300", "配置文件被篡改, 完整性校验失败！"),

    /**
     * 演示环境初始化异常
     */
    DEMO_INIT_ERROR("400", "系统已部署"),

    /**
     * 历史轨迹
     */
    TRACE_NOT_FOUND_ERROR("500", "此ID的轨迹未找到！"),

    /**
     * 组织机构
     */
    ORG_NOT_FOUND("600", "组织机构不存在"),

    /**
     * 文件
     */
    UPLOAD_FILE_NAME_ERROR("700", "无法获取文件名"),
    UPLOAD_FILE_EMPTY_ERROR("701", "上传文件内容为空"),
    UPLOAD_FILE_NOT_FOUND_ERROR("702", "文件不存在"),
    UPLOAD_FILE_EXIST_UNFINISHED_ERROR("703", "当前业务存在未完成的文件上传操作"),
    UPLOAD_FILE_INTERFACE_NOT_EXIST_ERROR("704", "此业务不存在文件上传操作"),
    UPLOAD_FILE_FINISHED_ERROR("705", "上传文件已经完成处理"),
    UPLOAD_FILE_PROCESSING_ERROR("706", "上传文件正在处理"),

    /**
     * 资源管理
     */
    RESOURCE_HAS_CHILD_RESOURCES_CANNOT_DELETE("800", "该资源下存在子资源，无法删除"),
    BATCH_DELETE_HAS_CHILD_RESOURCES_NOT_SELECTED("801", "无法删除，所选资源下存在未被一同删除的子资源"),

    VERIFY_SIGNED_ERROR("110", "验证签名失败"),

    ;

    private final String code;
    private final String desc;

    ManagementValidationError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }


    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}