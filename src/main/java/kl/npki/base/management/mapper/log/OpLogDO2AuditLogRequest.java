package kl.npki.base.management.mapper.log;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.log.request.AuditLogRequest;
import kl.npki.base.service.repository.entity.TOpLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/3/21
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OpLogDO2AuditLogRequest extends BaseMapper<TOpLogDO, AuditLogRequest> {

}
