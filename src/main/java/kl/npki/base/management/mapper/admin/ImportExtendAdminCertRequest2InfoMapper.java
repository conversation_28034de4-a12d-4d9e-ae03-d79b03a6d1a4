package kl.npki.base.management.mapper.admin;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.admin.request.ImportExtendAdminCertRequest;
import kl.npki.management.core.biz.cert.model.ImportExtendAdminCertInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/29
 * @desc
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ImportExtendAdminCertRequest2InfoMapper extends BaseMapper<ImportExtendAdminCertRequest, ImportExtendAdminCertInfo> {}
