package kl.npki.base.management.mapper.log;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.log.response.ApiLogDetailResponse;
import kl.npki.base.service.repository.entity.TApiLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 *
 * <AUTHOR>
 * @date 2022/12/14
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ApiLogDo2ResponseDetailMapper extends BaseMapper<TApiLogDO, ApiLogDetailResponse> {

    @Override
    @Mapping(source = "id", target = "logId")
    ApiLogDetailResponse map(TApiLogDO source);


    @Override
    @Mapping(source = "logId", target = "id")
    TApiLogDO reverseMap(ApiLogDetailResponse target);
}
