package kl.npki.base.management.mapper.upload;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.upload.UploadFileListResponse;
import kl.npki.base.service.repository.entity.UploadFileDO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @create 2024/5/9 下午2:56
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UploadFileDO2UploadFileListResponseMapper extends BaseMapper<UploadFileDO, UploadFileListResponse> {

    @Override
    UploadFileListResponse map(UploadFileDO source);

    @Override
    UploadFileDO reverseMap(UploadFileListResponse target);
}
