package kl.npki.base.management.mapper.permission;

import kl.nbase.bean.convert.BaseMapper;
import kl.nbase.i18n.i18n.I18nUtil;
import kl.npki.base.management.repository.entity.TResourceDO;
import kl.npki.management.core.biz.permission.response.UnifiedResourceResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/29
 * @desc
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {I18nUtil.class})
public interface ResourceDo2ResponseMapper extends BaseMapper<TResourceDO, UnifiedResourceResponse> {
    @Override
    @Mapping(target = "resourceName", expression = "java(I18nUtil.trWithDefaultMessage(tResourceDO.getResourceNameI18nKey(), tResourceDO.getResourceName()))")
    UnifiedResourceResponse map(TResourceDO tResourceDO);

    @Override
    TResourceDO reverseMap(UnifiedResourceResponse resourceEntity);

}