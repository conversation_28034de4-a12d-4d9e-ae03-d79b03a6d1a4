package kl.npki.base.management.mapper.admin;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.repository.entity.TAdminInfoDO;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/29
 * @desc
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AdminInfoDo2EntityMapper extends BaseMapper<TAdminInfoDO, AdminEntity> {}