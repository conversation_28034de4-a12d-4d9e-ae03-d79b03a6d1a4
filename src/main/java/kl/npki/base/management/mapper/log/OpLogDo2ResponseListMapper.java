package kl.npki.base.management.mapper.log;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.log.response.OpLogListResponse;
import kl.npki.base.service.repository.entity.TOpLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 *
 * <AUTHOR>
 * @date 2023/3/14
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OpLogDo2ResponseListMapper extends BaseMapper<TOpLogDO, OpLogListResponse> {


    @Override
    @Mapping(source = "id", target = "logId")
    OpLogListResponse map(TOpLogDO source);


    @Override
    @Mapping(source = "logId", target = "id")
    TOpLogDO reverseMap(OpLogListResponse target);
}
