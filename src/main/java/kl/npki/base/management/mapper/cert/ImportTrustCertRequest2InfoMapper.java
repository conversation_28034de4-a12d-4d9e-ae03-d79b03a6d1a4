package kl.npki.base.management.mapper.cert;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.cert.model.ImportTrustCertInfo;
import kl.npki.base.management.model.cert.request.ImportTrustCertRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * ImportTrustCertRequest对象与ImportTrustCertInfo对象的映射
 *
 * <AUTHOR>
 * @date 2023/9/4
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ImportTrustCertRequest2InfoMapper extends BaseMapper<ImportTrustCertRequest, ImportTrustCertInfo> {

    @Override
    ImportTrustCertInfo map(ImportTrustCertRequest source);

    @Override
    ImportTrustCertRequest reverseMap(ImportTrustCertInfo target);
}
