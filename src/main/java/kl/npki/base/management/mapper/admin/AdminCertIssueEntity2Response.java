package kl.npki.base.management.mapper.admin;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.admin.response.AdminCertIssueResponse;
import kl.npki.management.core.biz.admin.model.info.AdminCertIssueInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/2/21
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AdminCertIssueEntity2Response extends BaseMapper<AdminCertIssueInfo, AdminCertIssueResponse> {
}
