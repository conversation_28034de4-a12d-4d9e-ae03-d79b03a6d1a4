package kl.npki.base.management.mapper.config;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.repository.entity.TSystemConfigDO;
import kl.npki.management.core.biz.config.model.SystemConfigEntity;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/1 11:04
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SystemConfigDo2EntityMapper extends BaseMapper<TSystemConfigDO, SystemConfigEntity> {}
