package kl.npki.base.management.mapper.permission;

import kl.nbase.bean.convert.BaseMapper;
import kl.nbase.i18n.i18n.I18nUtil;
import kl.npki.base.management.repository.entity.TRoleDO;
import kl.npki.management.core.biz.permission.response.RoleListResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/29
 * @desc
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {I18nUtil.class})
public interface RoleDo2ResponseMapper extends BaseMapper<TRoleDO, RoleListResponse> {

    @Override
    @Mapping(target = "roleName", expression = "java(I18nUtil.trWithDefaultMessage(tRoleDO.getRoleNameI18nKey(), tRoleDO.getRoleName()))")
    @Mapping(target = "remark", expression = "java(tRoleDO.getRemark() == null ? null : I18nUtil.trWithDefaultMessage(tRoleDO.getRemark(), tRoleDO.getRemark()))")
    RoleListResponse map(TRoleDO tRoleDO);

    @Override
    TRoleDO reverseMap(RoleListResponse roleListResponse);
}
