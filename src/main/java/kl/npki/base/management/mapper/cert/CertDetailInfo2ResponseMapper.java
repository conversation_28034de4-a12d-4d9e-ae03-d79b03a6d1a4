package kl.npki.base.management.mapper.cert;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.cert.response.CertDetailResponse;
import kl.npki.management.core.biz.cert.model.CertDetailInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CertDetailInfo2ResponseMapper extends BaseMapper<CertDetailInfo, CertDetailResponse> {
    @Override
    CertDetailResponse map(CertDetailInfo source);

    @Override
    CertDetailInfo reverseMap(CertDetailResponse target);
}
