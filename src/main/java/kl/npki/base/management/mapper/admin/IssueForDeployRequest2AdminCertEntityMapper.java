package kl.npki.base.management.mapper.admin;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.admin.request.IssueAdminCertForDeployRequest;
import kl.npki.management.core.biz.admin.model.entity.AdminCertEntity;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/1/17
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface IssueForDeployRequest2AdminCertEntityMapper extends BaseMapper<IssueAdminCertForDeployRequest, AdminCertEntity> {
}
