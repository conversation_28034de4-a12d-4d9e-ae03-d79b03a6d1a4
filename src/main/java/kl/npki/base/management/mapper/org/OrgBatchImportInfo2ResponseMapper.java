package kl.npki.base.management.mapper.org;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.org.model.OrgBatchImportInfo;
import kl.npki.base.management.model.org.OrgBatchImportResponse;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @create 2024/3/21 15:05
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrgBatchImportInfo2ResponseMapper extends BaseMapper<OrgBatchImportInfo, OrgBatchImportResponse> {

    @Override
    OrgBatchImportResponse map(OrgBatchImportInfo source);

    @Override
    OrgBatchImportInfo reverseMap(OrgBatchImportResponse target);
}
