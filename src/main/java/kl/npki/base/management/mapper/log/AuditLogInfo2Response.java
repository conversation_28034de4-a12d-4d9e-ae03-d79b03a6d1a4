package kl.npki.base.management.mapper.log;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.constant.AuditStatusEnum;
import kl.npki.base.management.model.log.response.AuditLogResponse;
import kl.npki.management.core.biz.log.model.AuditLogInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/5/25
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = AuditStatusEnum.class)
public interface AuditLogInfo2Response extends BaseMapper<AuditLogInfo, AuditLogResponse> {

    @Override
    @Mapping(expression = "java(source.getAuditStatus().tr())", target = "auditStatusDesc")
    @Mapping(expression = "java(source.getAuditStatus().getCode())", target = "auditStatus")
    AuditLogResponse map(AuditLogInfo source);

    @Override
    @Mapping(target = "auditStatus", ignore = true)
    AuditLogInfo reverseMap(AuditLogResponse target);
}
