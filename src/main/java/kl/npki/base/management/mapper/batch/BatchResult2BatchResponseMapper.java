package kl.npki.base.management.mapper.batch;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.batch.model.BatchResult;
import kl.npki.base.management.model.batch.BatchResponse;
import org.mapstruct.Mapper;

/**
 * 批量操作结果转化批量操作响应
 *
 * <AUTHOR> rs
 * @date 2024/7/8 10:01
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE)
public interface BatchResult2BatchResponseMapper extends BaseMapper<BatchResult, BatchResponse> {

    @Override
    BatchResponse map(BatchResult batchResult);

    @Override
    BatchResult reverseMap(BatchResponse batchResponse);
}
