package kl.npki.base.management.mapper.admin;

import kl.nbase.bean.convert.BaseMapper;
import kl.nbase.i18n.i18n.I18nUtil;
import kl.npki.base.management.model.admin.response.AdminInfoDetailResponse;
import kl.npki.management.core.biz.admin.model.info.AdminInfoDetailInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/5/25
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {I18nUtil.class})
public interface AdminDetailInfo2Response extends BaseMapper<AdminInfoDetailInfo, AdminInfoDetailResponse> {

    @Override
    @Mapping(target = "roleName", expression = "java(I18nUtil.trWithDefaultMessage(adminInfoDetailInfo.getRoleNameI18nKey(), adminInfoDetailInfo.getRoleName()))")
    AdminInfoDetailResponse map(AdminInfoDetailInfo adminInfoDetailInfo);

}
