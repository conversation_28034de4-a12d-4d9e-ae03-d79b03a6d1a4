package kl.npki.base.management.mapper.org;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.org.model.OrgTraceEntity;
import kl.npki.base.management.model.org.OrgTraceResponse;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrgTraceEntity2ResponseMapper extends BaseMapper<OrgTraceEntity, OrgTraceResponse> {
}
