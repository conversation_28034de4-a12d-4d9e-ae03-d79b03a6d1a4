package kl.npki.base.management.mapper.license;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.license.model.LicenseInfo;
import kl.npki.base.management.model.system.response.LicenseInfoResponse;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * LicenseInfo转换为LicenseInfoResponse
 *
 * <AUTHOR>
 * @date 2025/4/10 16:53
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LicenseInfo2ResponseMapper extends BaseMapper<LicenseInfo, LicenseInfoResponse> {
}
