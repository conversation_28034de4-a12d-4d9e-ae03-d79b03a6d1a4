package kl.npki.base.management.mapper.config;


import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.configs.RegionAndLanguageConfig;
import kl.npki.base.management.model.system.request.RegionAndLanguageRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 区域与语言配置转换
 * <AUTHOR>
 * @since 2025/7/3 11:50
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RegionAndLanguageConfig2RequestMapper extends BaseMapper<RegionAndLanguageConfig, RegionAndLanguageRequest> {

}