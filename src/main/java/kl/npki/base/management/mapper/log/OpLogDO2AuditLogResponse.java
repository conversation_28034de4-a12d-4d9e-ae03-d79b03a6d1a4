package kl.npki.base.management.mapper.log;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.constant.AuditStatusEnum;
import kl.npki.base.management.model.log.response.AuditLogResponse;
import kl.npki.base.service.repository.entity.TOpLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/3/21
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {AuditStatusEnum.class})
public interface OpLogDO2AuditLogResponse extends BaseMapper<TOpLogDO, AuditLogResponse> {


    @Override
    @Mapping(source = "id", target = "logId")
    @Mapping(expression = "java(AuditStatusEnum.getByCode(source.getAuditStatus()).tr())", target = "auditStatusDesc")
    AuditLogResponse map(TOpLogDO source);


    @Override
    @Mapping(source = "logId", target = "id")
    TOpLogDO reverseMap(AuditLogResponse target);
}
