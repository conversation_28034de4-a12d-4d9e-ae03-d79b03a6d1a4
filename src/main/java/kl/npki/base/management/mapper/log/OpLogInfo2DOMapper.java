package kl.npki.base.management.mapper.log;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.log.model.OpLogInfo;
import kl.npki.base.core.constant.AuditStatusEnum;
import kl.npki.base.service.repository.entity.TOpLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/3/2
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OpLogInfo2DOMapper extends BaseMapper<OpLogInfo, TOpLogDO> {

    @Override
    TOpLogDO map(OpLogInfo source);

    @Override
    @Mapping(target = "logId", source = "id")
    OpLogInfo reverseMap(TOpLogDO target);

    /**
     * Convert AuditStatusEnum to Integer
     *
     * @param value AuditStatusEnum value
     * @return Integer representation of the enum
     */
    default Integer map(AuditStatusEnum value) {
        return value != null ? value.getCode() : null;
    }

    /**
     * Convert Integer to AuditStatusEnum
     *
     * @param value Integer value
     * @return AuditStatusEnum representation of the integer
     */
    default AuditStatusEnum map(Integer value) {
        return value != null ? AuditStatusEnum.getByCode(value) : null;
    }
}
