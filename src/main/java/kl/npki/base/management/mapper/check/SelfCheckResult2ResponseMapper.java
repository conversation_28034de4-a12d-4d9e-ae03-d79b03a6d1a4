package kl.npki.base.management.mapper.check;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.check.model.SelfCheckResult;
import kl.npki.base.management.model.check.SelfCheckResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * 自检结果与响应的mapper映射
 *
 * <AUTHOR>
 * @date 2023/10/17
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SelfCheckResult2ResponseMapper extends BaseMapper<SelfCheckResult, SelfCheckResponse> {

    @Override
    @Mapping(target = "name", expression = "java(mapName(source))")
    @Mapping(target = "type", source = "category")
    SelfCheckResponse map(SelfCheckResult source);

    @Override
    @Mapping(target = "category", source = "type")
    SelfCheckResult reverseMap(SelfCheckResponse target);


    default String mapName(SelfCheckResult source) {
        return source.getName() + source.getStatus().getName();
    }
}
