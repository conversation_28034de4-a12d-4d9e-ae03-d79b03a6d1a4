package kl.npki.base.management.mapper.org;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.org.model.OrgAddInfo;
import kl.npki.base.core.biz.org.model.OrgEntity;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrgAddInfo2EntityMapper extends BaseMapper<OrgAddInfo, OrgEntity> {
}
