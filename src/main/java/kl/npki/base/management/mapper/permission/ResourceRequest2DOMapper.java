package kl.npki.base.management.mapper.permission;

import kl.nbase.bean.convert.BaseMapper;
import kl.nbase.i18n.i18n.I18nUtil;
import kl.npki.base.management.model.permission.request.AddResourceRequest;
import kl.npki.base.management.repository.entity.TResourceDO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/29
 * @desc
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {I18nUtil.class})
public interface ResourceRequest2DOMapper extends BaseMapper<AddResourceRequest, TResourceDO> {

    @Override
    TResourceDO map(AddResourceRequest source);

    @Override
    AddResourceRequest reverseMap(TResourceDO target);
}