package kl.npki.base.management.mapper.admin;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.admin.request.ImportAdminCertRequest;
import kl.npki.management.core.biz.cert.model.ImportAdminCertInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/29
 * @desc
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ImportAdminCertRequest2InfoMapper extends BaseMapper<ImportAdminCertRequest, ImportAdminCertInfo> {}
