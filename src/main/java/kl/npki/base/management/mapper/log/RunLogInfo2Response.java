package kl.npki.base.management.mapper.log;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.log.response.RunLogResponse;
import kl.npki.management.core.biz.log.model.RunLogInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/5/25
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RunLogInfo2Response extends BaseMapper<RunLogInfo, RunLogResponse> {
}
