package kl.npki.base.management.mapper.inspection;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.inspection.InspectionRecordListResponse;
import kl.npki.base.service.repository.entity.InspectionRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 巡检记录数据库DO转换
 *
 * <AUTHOR>
 * @date 08/05/2025 12:10
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface InspectionDO2ListResponseMapper extends BaseMapper<InspectionRecordDO, InspectionRecordListResponse> {
}
