package kl.npki.base.management.mapper.log;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.log.model.SystemLogFileEntity;
import kl.npki.base.core.biz.log.model.SystemLogFileInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2025/7/10
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SystemLogFileInfo2EntitylMapper extends BaseMapper<SystemLogFileInfo, SystemLogFileEntity> {

}