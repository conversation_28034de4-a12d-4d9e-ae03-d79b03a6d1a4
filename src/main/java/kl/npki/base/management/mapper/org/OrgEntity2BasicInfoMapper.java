package kl.npki.base.management.mapper.org;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.org.model.OrgBasicInfo;
import kl.npki.base.core.biz.org.model.OrgEntity;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @create 2024/3/20 13:43
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrgEntity2BasicInfoMapper extends BaseMapper<OrgEntity, OrgBasicInfo> {

    @Override
    OrgBasicInfo map(OrgEntity source);

    @Override
    OrgEntity reverseMap(OrgBasicInfo target);
}