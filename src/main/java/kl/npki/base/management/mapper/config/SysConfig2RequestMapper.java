package kl.npki.base.management.mapper.config;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.configs.SysConfig;
import kl.npki.base.management.model.system.request.SysConfigRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @Date 2023/10/16
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SysConfig2RequestMapper extends BaseMapper<SysConfig, SysConfigRequest> {
}
