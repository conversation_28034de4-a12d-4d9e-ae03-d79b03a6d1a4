package kl.npki.base.management.mapper.admin;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.admin.response.CaptchaPayloadResponse;
import kl.npki.management.core.biz.admin.model.info.CaptchaPayloadInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @since 2025/7/15 下午1:26
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AdminCaptchaPayloadInfo2Response extends BaseMapper<CaptchaPayloadInfo, CaptchaPayloadResponse> {

}