package kl.npki.base.management.mapper.cert;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.cert.model.ExportCertInfo;
import kl.npki.base.management.model.cert.request.ExportCertRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @Date 2025/5/22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ExportCertRequest2InfoMapper extends BaseMapper<ExportCertRequest, ExportCertInfo> {
}
