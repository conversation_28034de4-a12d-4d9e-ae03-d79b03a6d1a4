package kl.npki.base.management.mapper.cert;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.service.repository.entity.TTrustCertDO;
import kl.npki.base.core.biz.cert.model.TrustCertEntity;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/29
 * @desc
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TrustCertDo2EntityMapper extends BaseMapper<TTrustCertDO, TrustCertEntity> {

    @Override
    TrustCertEntity map(TTrustCertDO source);

    @Override
    TTrustCertDO reverseMap(TrustCertEntity target);

    default Integer map(Boolean value) {
        return (value == null) ? null : (value ? 1 : 0);
    }

    default Boolean map(Integer value) {
        return (value == null) ? null : (value != 0);
    }
}
