package kl.npki.base.management.mapper.permission;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.management.model.permission.request.AddRoleRequest;
import kl.npki.management.core.biz.admin.model.entity.RoleEntity;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/9/29
 * @desc
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RoleRequest2DtoMapper extends BaseMapper<AddRoleRequest, RoleEntity> {}
