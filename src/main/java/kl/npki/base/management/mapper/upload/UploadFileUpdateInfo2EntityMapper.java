package kl.npki.base.management.mapper.upload;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.upload.model.UploadFileEntity;
import kl.npki.base.core.biz.upload.model.UploadFileUpdateInfo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @create 2024/5/8 下午3:48
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UploadFileUpdateInfo2EntityMapper extends BaseMapper<UploadFileUpdateInfo, UploadFileEntity> {

    @Override
    UploadFileEntity map(UploadFileUpdateInfo source);

    @Override
    UploadFileUpdateInfo reverseMap(UploadFileEntity target);
}
