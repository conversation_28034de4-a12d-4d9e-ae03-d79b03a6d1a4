package kl.npki.base.management.mapper.upload;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.upload.model.UploadFileAddInfo;
import kl.npki.base.core.biz.upload.model.UploadFileEntity;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @create 2024/5/8 下午3:48
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UploadFileAddInfo2EntityMapper extends BaseMapper<UploadFileAddInfo, UploadFileEntity> {

    @Override
    UploadFileEntity map(UploadFileAddInfo source);

    @Override
    UploadFileAddInfo reverseMap(UploadFileEntity target);
}
