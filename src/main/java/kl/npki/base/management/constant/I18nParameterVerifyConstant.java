package kl.npki.base.management.constant;

import static kl.npki.base.management.constant.I18nConstant.BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX;

/**
 * i18n参数验证常量
 *
 * <AUTHOR>
 * @date 2025/01/22
 */
public final class I18nParameterVerifyConstant {

    // region kl.npki.base.management.model.log.request.LogExcelFileRequest
    /**
     * 签名者证书链不能为空
     */
    public static final String SIGNER_CERTIFICATE_CHAIN_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "signer_certificate_chain_cannot_be_empty";
    /**
     * 签名时间不能为空
     */
    public static final String SIGNATURE_TIME_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "signature_time_cannot_be_empty";
    /**
     * 文件ID不能为空
     */
    public static final String FILE_ID_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "file_id_cannot_be_empty";
    /**
     * 签名值不能为空
     */
    public static final String SIGNATURE_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "signature_cannot_be_empty";
    // endregion

    // region kl.npki.base.management.model.log.request.LogConfigRequest
    /**
     * 系统日志级别不能为空
     */
    public static final String THE_SYSTEM_LOG_LEVEL_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_system_log_level_cannot_be_empty";
    /**
     * 系统日志级别长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_SYSTEM_LOG_LEVEL_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_system_log_level_cannot_exceed_128_characters";
    /**
     * 系统日志级别不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String THE_SYSTEM_LOG_LEVEL_CANNOT_BE_FILLED_WITH_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_system_log_level_cannot_be_filled_with_spaces_or_20_starting_or_ending_with_0a_or_00";
    /**
     * 系统日志最大容量不能为空
     */
    public static final String THE_MAXIMUM_CAPACITY_OF_SYSTEM_LOGS_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_maximum_capacity_of_system_logs_cannot_be_empty";
    /**
     * 日志最大容量值长度不能大于32字符
     */
    public static final String THE_MAXIMUM_LENGTH_OF_THE_LOG_CAPACITY_VALUE_CANNOT_EXCEED_32_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_maximum_length_of_the_log_capacity_value_cannot_exceed_32_characters";
    /**
     * 日志保留天数不能小于1
     */
    public static final String THE_RETENTION_PERIOD_OF_LOGS_CANNOT_BE_LESS_THAN_1_DAY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_retention_period_of_logs_cannot_be_less_than_1_day";
    /**
     * 日志保留天数不能大于1000
     */
    public static final String THE_RETENTION_PERIOD_OF_LOGS_CANNOT_EXCEED_1000_DAYS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_retention_period_of_logs_cannot_exceed_1000_days";
    /**
     * 审计服务是否启用不可为空
     */
    public static final String WHETHER_THE_AUDIT_SERVICE_IS_ENABLED_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "whether_the_audit_service_is_enabled_cannot_be_empty";
    /**
     * 审计服务IP地址超出范围或格式有误
     */
    public static final String AUDIT_SERVICE_IP_ADDRESS_IS_OUT_OF_RANGE_OR_FORMATTED_INCORRECTLY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "audit_service_ip_address_is_out_of_range_or_formatted_incorrectly";
    /**
     * 审计服务端口号超出范围或格式有误
     */
    public static final String AUDIT_SERVICE_PORT_NUMBER_IS_OUT_OF_RANGE_OR_FORMATTED_INCORRECTLY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "audit_service_port_number_is_out_of_range_or_formatted_incorrectly";

    /**
     * 审计服务协议
     */
    public static final String AUDIT_SERVICE_PROTOCOL_IS_INCORRECT_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "audit_service_protocol_is_incorrect";
    // endregion

    // region kl.npki.base.management.model.admin.request.ExtendAdminCertRequest
    /**
     * 管理员id不应为空
     */
    public static final String THE_ADMINISTRATOR_ID_SHOULD_NOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_administrator_id_should_not_be_empty";
    /**
     * 证书延期天数不应小于1天
     */
    public static final String THE_CERTIFICATE_EXTENSION_PERIOD_SHOULD_NOT_BE_LESS_THAN_1_DAY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_certificate_extension_period_should_not_be_less_than_1_day";
    // endregion

    // region kl.npki.base.management.model.cert.request.ImportCertChainRequest
    /**
     * 证书不能为空
     */
    public static final String CERTIFICATE_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "certificate_cannot_be_empty";
    // endregion

    // region kl.npki.base.management.model.admin.request.ImportAdminCertRequest
    /**
     * 角色信息不能为空
     */
    public static final String ROLE_INFORMATION_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "role_information_cannot_be_empty";
    /**
     * 角色编码长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_CHARACTER_CODE_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_character_code_cannot_exceed_128_characters";
    /**
     * 角色编码不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String CHARACTER_ENCODING_CANNOT_USE_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "character_encoding_cannot_use_spaces_or_20_starting_or_ending_with_0a_or_00";
    /**
     * 密码信息不能为空
     */
    public static final String PASSWORD_INFORMATION_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "password_information_cannot_be_empty";
    /**
     * 密码hash长度不能大于128字符
     */
    public static final String PASSWORD_HASH_LENGTH_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "password_hash_length_cannot_exceed_128_characters";
    /**
     * 密码hash不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String PASSWORD_HASH_CANNOT_USE_SPACES_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "password_hash_cannot_use_spaces_20_starting_or_ending_with_0a_or_00";
    /**
     * 签名证书值不能为空
     */
    public static final String THE_VALUE_OF_THE_SIGNATURE_CERTIFICATE_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_value_of_the_signature_certificate_cannot_be_empty";
    /**
     * 签名证书值长度不能大于4096字符
     */
    public static final String THE_LENGTH_OF_THE_SIGNATURE_CERTIFICATE_VALUE_CANNOT_EXCEED_4096_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_signature_certificate_value_cannot_exceed_4096_characters";
    /**
     * 证书值不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String THE_CERTIFICATE_VALUE_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_certificate_value_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00";
    /**
     * 加密证书值长度不能大于4096字符
     */
    public static final String THE_LENGTH_OF_THE_ENCRYPTION_CERTIFICATE_VALUE_CANNOT_EXCEED_4096_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_encryption_certificate_value_cannot_exceed_4096_characters";
    /**
     * 加密证书值不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String THE_ENCRYPTION_CERTIFICATE_VALUE_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_encryption_certificate_value_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00";
    // endregion

    // region
    // kl.npki.base.management.model.permission.request.PermissionTreeSaveRequest
    /**
     * 角色名不应为空
     */
    public static final String THE_ROLE_NAME_SHOULD_NOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_role_name_should_not_be_empty";
    // endregion

    // region kl.npki.base.management.model.system.request.SysManagerRequest
    /**
     * 系统名称不能为空
     */
    public static final String THE_SYSTEM_NAME_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_system_name_cannot_be_empty";
    /**
     * 系统名称长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_SYSTEM_NAME_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_system_name_cannot_exceed_128_characters";
    /**
     * 系统名称不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String THE_SYSTEM_NAME_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_system_name_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00";
    /**
     * 系统LOGO格式有误
     */
    public static final String THE_SYSTEM_LOGO_FORMAT_IS_INCORRECT_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_system_logo_format_is_incorrect";
    // endregion

    // region kl.npki.base.management.model.admin.request.RegisterAdminRequest
    /**
     * 角色信息不应为空
     */
    public static final String ROLE_INFORMATION_SHOULD_NOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "role_information_should_not_be_empty";
    /**
     * 角色信息长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_CHARACTER_INFORMATION_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_character_information_cannot_exceed_128_characters";
    /**
     * 管理员姓名不应为空
     */
    public static final String ADMINISTRATOR_NAME_SHOULD_NOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "administrator_name_should_not_be_empty";
    /**
     * 管理员姓名长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_ADMINISTRATOR_NAME_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_administrator_name_cannot_exceed_128_characters";
    /**
     * 颁发者CN长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_ISSUE_CN_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_issuer_cn_cannot_exceed_128_characters";
    /**
     * 证书模板长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_TEMPLATE_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_template_cannot_exceed_128_characters";
    /**
     * 角管理员姓名不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String THE_NAME_OF_THE_CORNER_ADMINISTRATOR_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_name_of_the_corner_administrator_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00";
    /**
     * 密码不能为空
     */
    public static final String PASSWORD_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "password_cannot_be_empty";
    /**
     * 密码摘要格式错误
     */
    public static final String PASSWORD_DIGEST_FORMAT_ERROR_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "password_digest_format_error";
    /**
     * 组织信息的数据长度不能大于128字符
     */
    public static final String THE_DATA_LENGTH_OF_ORGANIZATIONAL_INFORMATION_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_data_length_of_organizational_information_cannot_exceed_128_characters";
    /**
     * 机构信息的数据长度不能大于128字符
     */
    public static final String THE_DATA_LENGTH_OF_INSTITUTIONAL_INFORMATION_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_data_length_of_institutional_information_cannot_exceed_128_characters";
    /**
     * 省份信息的数据长度不能大于128字符
     */
    public static final String THE_DATA_LENGTH_OF_PROVINCE_INFORMATION_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_data_length_of_province_information_cannot_exceed_128_characters";
    /**
     * 城市信息的数据长度不能大于128字符
     */
    public static final String THE_DATA_LENGTH_OF_CITY_INFORMATION_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_data_length_of_city_information_cannot_exceed_128_characters";
    /**
     * 邮箱错误
     */
    public static final String EMAIL_ERROR_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "email_error";
    /**
     * 解析类型接受的值
     */
    public static final String PARSING_TYPE_ACCEPT_VALUES_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "parsing_type_accept_values";

    // endregion

    // region kl.npki.base.management.model.admin.request.IssueAdminCertRequest
    /**
     * 用户id不应为空
     */
    public static final String USER_ID_SHOULD_NOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "user_id_should_not_be_empty";
    // endregion

    // region kl.npki.base.management.model.engine.request.GroupEngineConfigRequest
    /**
     * 组内加密机配置不能为空
     */
    public static final String THE_CONFIGURATION_OF_ENCRYPTION_MACHINES_WITHIN_THE_GROUP_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_configuration_of_encryption_machines_within_the_group_cannot_be_empty";
    // endregion

    // region kl.npki.base.management.model.engine.request.AsymKeyIndexInfo
    /**
     * 非对称算法名称不能为空
     */
    public static final String ASYMMETRIC_ALGORITHM_NAME_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "asymmetric_algorithm_name_cannot_be_empty";
    // endregion

    // region kl.npki.base.management.model.admin.request.LoginRequest
    /**
     * 认证类型不能为空
     */
    public static final String AUTHENTICATION_TYPE_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "authentication_type_cannot_be_empty";
    /**
     * 认证类型不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String THE_AUTHENTICATION_TYPE_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_authentication_type_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00";
    // endregion

    // region kl.npki.base.management.model.admin.request.AdminUpdatePwdRequest
    /**
     * 管理员id不能为空
     */
    public static final String ADMINISTRATOR_ID_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "administrator_id_cannot_be_empty";
    /**
     * 旧密码不能为空
     */
    public static final String OLD_PASSWORD_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "old_password_cannot_be_empty";
    /**
     * 旧密码摘要长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_OLD_PASSWORD_DIGEST_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_old_password_digest_cannot_exceed_128_characters";
    /**
     * 旧密码摘要不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String OLD_PASSWORD_DIGEST_CANNOT_CONTAIN_SPACES_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "old_password_digest_cannot_contain_spaces_20_starting_or_ending_with_0a_or_00";
    /**
     * 新密码不能为空
     */
    public static final String THE_NEW_PASSWORD_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_new_password_cannot_be_empty";
    /**
     * 新密码摘要格式错误
     */
    public static final String NEW_PASSWORD_DIGEST_FORMAT_ERROR_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "new_password_digest_format_error";
    // endregion

    // region kl.npki.base.management.model.permission.request.AddRoleRequest
    /**
     * 角色名称不能为空
     */
    public static final String THE_ROLE_NAME_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_role_name_cannot_be_empty";
    /**
     * 角色名称长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_CHARACTER_NAME_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_character_name_cannot_exceed_128_characters";
    /**
     * 角色名称不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String THE_ROLE_NAME_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_role_name_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00";
    /**
     * 角色编码不能为空
     */
    public static final String ROLE_CODE_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "role_code_cannot_be_empty";
    /**
     * 角色备注长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_ROLE_NOTE_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_role_note_cannot_exceed_128_characters";

    /**
     * 父角色编码不能为空
     */
    public static final String PARENT_ROLE_CODE_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "parent_role_code_cannot_be_empty";

    /**
     * 父角色编码长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_PARENT_ROLE_CODE_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_parent_role_code_cannot_exceed_128_characters";

    /**
     * 父角色编码不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String PARENT_ROLE_CODE_CANNOT_USE_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "parent_role_code_cannot_use_spaces_or_20_starting_or_ending_with_0a_or_00";

    // endregion

    // region kl.npki.base.management.model.system.request.PortConfigRequest
    /**
     * 管理服务端口号不可小于1
     */
    public static final String THE_MANAGEMENT_SERVICE_PORT_NUMBER_CANNOT_BE_LESS_THAN_1_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_management_service_port_number_cannot_be_less_than_1";
    /**
     * 管理服务端口号不可大于65535
     */
    public static final String THE_MANAGEMENT_SERVICE_PORT_NUMBER_CANNOT_EXCEED_65535_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_management_service_port_number_cannot_exceed_65535";
    /**
     * 管理服务SSL开关不可为空
     */
    public static final String THE_MANAGEMENT_SERVICE_SSL_SWITCH_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_management_service_ssl_switch_cannot_be_empty";
    /**
     * HTTPS管理服务端口号不可小于1
     */
    public static final String THE_HTTPS_MANAGEMENT_SERVICE_PORT_NUMBER_CANNOT_BE_LESS_THAN_1_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_https_management_service_port_number_cannot_be_less_than_1";
    /**
     * HTTPS管理服务端口号不可大于65535
     */
    public static final String THE_HTTPS_MANAGEMENT_SERVICE_PORT_NUMBER_CANNOT_EXCEED_65535_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_https_management_service_port_number_cannot_exceed_65535";
    /**
     * HTTP业务服务开关不可为空
     */
    public static final String THE_HTTP_BUSINESS_SERVICE_SWITCH_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_http_business_service_switch_cannot_be_empty";
    /**
     * HTTP业务服务端口号不可小于1
     */
    public static final String THE_HTTP_BUSINESS_SERVICE_PORT_NUMBER_CANNOT_BE_LESS_THAN_1_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_http_business_service_port_number_cannot_be_less_than_1";
    /**
     * HTTP业务服务端口号不可大于65535
     */
    public static final String THE_HTTP_BUSINESS_SERVICE_PORT_NUMBER_CANNOT_EXCEED_65535_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_http_business_service_port_number_cannot_exceed_65535";
    /**
     * HTTP业务服务SSL开关不可为空
     */
    public static final String THE_SSL_SWITCH_FOR_HTTP_BUSINESS_SERVICES_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_ssl_switch_for_http_business_services_cannot_be_empty";
    /**
     * TCP业务服务开关不可为空
     */
    public static final String TCP_SERVICE_SWITCH_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "tcp_service_switch_cannot_be_empty";
    /**
     * TCP业务服务端口号不可小于1
     */
    public static final String THE_TCP_SERVICE_PORT_NUMBER_CANNOT_BE_LESS_THAN_1_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_tcp_service_port_number_cannot_be_less_than_1";
    /**
     * TCP业务服务端口号不可大于65535
     */
    public static final String THE_TCP_SERVICE_PORT_NUMBER_CANNOT_EXCEED_65535_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_tcp_service_port_number_cannot_exceed_65535";
    /**
     * TCP业务服务SSL开关不可为空
     */
    public static final String TCP_BUSINESS_SERVICE_SSL_SWITCH_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "tcp_business_service_ssl_switch_cannot_be_empty";
    // endregion

    // region kl.npki.base.management.model.cert.request.ImportTrustCertRequest
    /**
     * 可信证书不能为空
     */
    public static final String TRUSTED_CERTIFICATE_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "trusted_certificate_cannot_be_empty";
    // endregion

    // region kl.npki.base.management.model.algo.request.AlgoConfigRequest
    /**
     * 对称密钥算法不能为空
     */
    public static final String SYMMETRIC_KEY_ALGORITHM_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "symmetric_key_algorithm_cannot_be_empty";
    /**
     * 对称密钥算法不能大于128字符
     */
    public static final String SYMMETRIC_KEY_ALGORITHM_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "symmetric_key_algorithm_cannot_exceed_128_characters";
    /**
     * 对称密钥算法不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String SYMMETRIC_KEY_ALGORITHM_CANNOT_USE_SPACES_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "symmetric_key_algorithm_cannot_use_spaces_20_starting_or_ending_with_0a_or_00";
    // endregion

    // region kl.npki.base.management.model.db.request.DbConfigRequest
    /**
     * 数据库类型不能为空
     */
    public static final String THE_DATABASE_TYPE_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_database_type_cannot_be_empty";
    /**
     * 数据库类型长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_DATABASE_TYPE_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_database_type_cannot_exceed_128_characters";
    /**
     * 数据库类型不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String THE_DATABASE_TYPE_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_database_type_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00";
    /**
     * 最大连接数不能为空
     */
    public static final String THE_MAXIMUM_NUMBER_OF_CONNECTIONS_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_maximum_number_of_connections_cannot_be_empty";
    /**
     * 是否启用读写分离配置不能为空
     */
    public static final String WHETHER_TO_ENABLE_READWRITE_SEPARATION_CONFIGURATION_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "whether_to_enable_readwrite_separation_configuration_cannot_be_empty";
    // endregion

    // region
    // kl.npki.base.management.model.admin.request.IssueAdminCertForDeployRequest
    /**
     * 证书签发请求不应为空
     */
    public static final String CERTIFICATE_ISSUANCE_REQUEST_SHOULD_NOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "certificate_issuance_request_should_not_be_empty";
    // endregion

    // region kl.npki.base.management.model.cache.CacheConfigRequest
    /**
     * 缓存类型不能为空
     */
    public static final String CACHE_TYPE_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "cache_type_cannot_be_empty";
    /**
     * 数据库类型长度不能大于20字符
     */
    public static final String THE_LENGTH_OF_THE_DATABASE_TYPE_CANNOT_EXCEED_20_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_database_type_cannot_exceed_20_characters";
    /**
     * 缓存运行模式不能为空
     */
    public static final String CACHE_OPERATION_MODE_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "cache_operation_mode_cannot_be_empty";
    /**
     * 缓存运行模式不能大于20字符
     */
    public static final String CACHE_OPERATION_MODE_CANNOT_EXCEED_20_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "cache_operation_mode_cannot_exceed_20_characters";
    /**
     * 缓存运行模式不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String CACHE_OPERATION_MODE_CANNOT_USE_SPACES_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "cache_operation_mode_cannot_use_spaces_20_starting_or_ending_with_0a_or_00";
    /**
     * SSL开关不能为空
     */
    public static final String SSL_SWITCH_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "ssl_switch_cannot_be_empty";
    /**
     * redis节点配置不能为空
     */
    public static final String REDIS_NODE_CONFIGURATION_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "redis_node_configuration_cannot_be_empty";
    // endregion

    // region kl.npki.base.management.model.system.response.PortConfigResponse
    /**
     * 管理服务端口号不可小于0
     */
    public static final String THE_MANAGEMENT_SERVICE_PORT_NUMBER_CANNOT_BE_LESS_THAN_0_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_management_service_port_number_cannot_be_less_than_0";
    /**
     * HTTPS管理服务端口号不可小于0
     */
    public static final String THE_HTTPS_MANAGEMENT_SERVICE_PORT_NUMBER_CANNOT_BE_LESS_THAN_0_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_https_management_service_port_number_cannot_be_less_than_0";
    /**
     * HTTP业务服务端口号不可小于0
     */
    public static final String THE_HTTP_BUSINESS_SERVICE_PORT_NUMBER_CANNOT_BE_LESS_THAN_0_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_http_business_service_port_number_cannot_be_less_than_0";
    /**
     * TCP业务服务端口号不可小于0
     */
    public static final String THE_TCP_SERVICE_PORT_NUMBER_CANNOT_BE_LESS_THAN_0_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_tcp_service_port_number_cannot_be_less_than_0";
    // endregion

    // region kl.npki.base.management.model.admin.request.UpdateLoginTypeRequest
    /**
     * 登录模式不能为空
     */
    public static final String LOGIN_MODE_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "login_mode_cannot_be_empty";
    // endregion

    // region kl.npki.base.management.model.engine.request.EngineConfigRequest
    /**
     * 加密机类型不能为空
     */
    public static final String THE_ENCRYPTION_MACHINE_TYPE_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_encryption_machine_type_cannot_be_empty";
    /**
     * 加密机类型长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_ENCRYPTION_MACHINE_TYPE_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_encryption_machine_type_cannot_exceed_128_characters";
    /**
     * 加密机类型不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String ENCRYPTION_MACHINE_TYPE_CANNOT_USE_SPACES_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "encryption_machine_type_cannot_use_spaces_20_starting_or_ending_with_0a_or_00";
    /**
     * 非对称密钥索引信息不能为空
     */
    public static final String ASYMMETRIC_KEY_INDEX_INFORMATION_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "asymmetric_key_index_information_cannot_be_empty";
    // endregion

    // region kl.npki.base.management.model.cert.request.CertSignRequest
    /**
     * 证书通用名不能为空！
     */
    public static final String THE_CERTIFICATE_UNIVERSAL_NAME_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_certificate_universal_name_cannot_be_empty";
    /**
     * 证书通用名长度不能大于32字符
     */
    public static final String THE_LENGTH_OF_THE_CERTIFICATE_GENERIC_NAME_CANNOT_EXCEED_32_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_certificate_generic_name_cannot_exceed_32_characters";
    /**
     * 证书通用名描述
     */
    public static final String CERT_CHAIN_THE_CERTIFICATE_COMMON_NAME_DESC_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_certificate_common_name";

    /**
     * 单位信息描述
     */
    public static final String UNIT_NAME_DESC_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_unit_name";

    /**
     * 省份名称描述
     */
    public static final String PROVINCE_NAME_DESC_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_province_name";

    /**
     * 市名称描述
     */
    public static final String CITY_DESC_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_city_name";

    /**
     * 机构名称描述
     */
    public static final String ORGANIZATION_NAME_DESC_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_organization_name";

    /**
     * 单位信息的数据长度不能大于128字符
     */
    public static final String THE_DATA_LENGTH_OF_UNIT_INFORMATION_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_data_length_of_unit_information_cannot_exceed_128_characters";
    /**
     * 证书有效天数不能为空
     */
    public static final String THE_VALIDITY_PERIOD_OF_THE_CERTIFICATE_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_validity_period_of_the_certificate_cannot_be_empty";
    /**
     * 密钥类型长度不能大于128字符
     */
    public static final String THE_LENGTH_OF_THE_KEY_TYPE_CANNOT_EXCEED_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_length_of_the_key_type_cannot_exceed_128_characters";
    /**
     * 密钥类型不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String THE_KEY_TYPE_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_key_type_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00";
    /**
     * 加密密钥类型不能以空格、%20、%0a、%00开头或结尾
     */
    public static final String THE_ENCRYPTION_KEY_TYPE_CANNOT_CONTAIN_SPACES_OR_20_STARTING_OR_ENDING_WITH_0A_OR_00_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_encryption_key_type_cannot_contain_spaces_or_20_starting_or_ending_with_0a_or_00";
    /**
     * keyStore密码不能大于128字符
     */
    public static final String THE_KEYSTORE_PASSWORD_CANNOT_BE_GREATER_THAN_128_CHARACTERS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_keystore_password_cannot_be_greater_than_128_characters";
    // endregion // region
    // kl.npki.base.management.model.cert.request.ExportCertRequest
    public static final String THE_CERTIFICATE_EXPORT_FORMAT = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_certificate_export_format";
    // endregion

    /**
     * 私钥导出格式只能是PFX、GMT0009_2012、GMT0009_2023、GMT0016_2023、P8
     */
    public static final String THE_KEY_EXPORT_FORMAT = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "the_key_export_format";

    // region kl.npki.base.management.model.permission.request.AddResourceRequest
    /**
     * 资源名称不能为空
     */
    public static final String RESOURCE_NAME_SHOULD_NOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "resource_name_should_not_be_empty";
    /**
     * 资源名称长度不能超过255字符
     */
    public static final String RESOURCE_NAME_LENGTH_EXCEED_LIMIT_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "resource_name_length_exceed_limit";
    /**
     * 资源编码不能为空
     */
    public static final String RESOURCE_CODE_SHOULD_NOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "resource_code_should_not_be_empty";
    /**
     * 资源编码长度不能超过255字符
     */
    public static final String RESOURCE_CODE_LENGTH_EXCEED_LIMIT_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "resource_code_length_exceed_limit";
    /**
     * 资源类型不能为空
     */
    public static final String RESOURCE_TYPE_SHOULD_NOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "resource_type_should_not_be_empty";
    /**
     * 菜单图标长度不能超过255字符
     */
    public static final String MENU_ICON_LENGTH_EXCEED_LIMIT_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "menu_icon_length_exceed_limit";
    /**
     * 页面路径长度不能超过500字符
     */
    public static final String PAGE_PATH_LENGTH_EXCEED_LIMIT_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "page_path_length_exceed_limit";
    /**
     * URL长度不能超过500字符
     */
    public static final String URL_LENGTH_EXCEED_LIMIT_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "url_length_exceed_limit";
    /**
     * 请求方法长度不能超过20字符
     */
    public static final String REQUEST_METHOD_LENGTH_EXCEED_LIMIT_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "request_method_length_exceed_limit";
    /**
     * 备注长度不能超过500字符
     */
    public static final String REMARK_LENGTH_EXCEED_LIMIT_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX
        + "remark_length_exceed_limit";
    // endregion

    /**
     * 区域不能为空
     */
    public static final String THE_REGION_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "the_region_cannot_be_empty";

    /**
     * 区域参数有误
     */
    public static final String THE_REGION_PARAM_ERROR_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "the_region_param_error";

    /**
     * 语言不能为空
     */
    public static final String THE_LANGUAGE_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "the_language_cannot_be_empty";

    /**
     * 语言参数有误
     */
    public static final String THE_LANGUAGE_PARAM_ERROR_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "the_language_param_error";

}
