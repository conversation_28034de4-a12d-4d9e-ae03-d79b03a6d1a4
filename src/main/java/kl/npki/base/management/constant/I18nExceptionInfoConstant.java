package kl.npki.base.management.constant;

import static kl.npki.base.management.constant.I18nConstant.BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX;


/**
 * i18n异常信息常量
 *
 * <AUTHOR>
 * @date 2025/01/22
 */
public final class I18nExceptionInfoConstant {

    // region kl.npki.base.management.controller.LicenseController
    /**
     * 文件大小超出限制:{0}
     */
    public static final String THE_FILE_SIZE_EXCEEDS_THE_LIMIT_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "the_file_size_exceeds_the_limit";
    /**
     * 文件不是dat文件:{0}
     */
    public static final String FILE_IS_NOT_A_DAT_FILE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "file_is_not_a_dat_file";
    /**
     * 读取授权文件异常
     */
    public static final String LICENSE_FILE_READ_EXCEPTION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "license_file_read_exception";
    // endregion

    // region kl.npki.base.management.common.auth.JwtAuthenticateService
    /**
     * 多因子认证身份认证信息{0}和{1}不一致
     */
    public static final String INCONSISTENT_IDENTITY_CREDENTIALS_FOR_MULTI_FACTOR_AUTHENTICATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "inconsistent_identity_credentials_for_multi_factor_authentication";
    // endregion

    // region kl.npki.base.management.controller.log.RunLogController
    /**
     * 请指定文件，不能为空或null
     */
    public static final String PLEASE_ASSIGN_FILE_MUST_NOT_BE_EMPTY_OR_NULL_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "please_assign_file_must_not_be_empty_or_null";
    /**
     * 无效的日志文件名:{0}
     */
    public static final String INVALID_LOG_NAME_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "invalid_log_name";
    /**
     * 文件[{0}]下载失败
     */
    public static final String FILE_DOWNLOAD_FAILED_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "file_download_failed";
    // endregion

    // region kl.npki.base.management.repository.impl.AdminInfoMgrRepositoryImpl
    /**
     * 数据为空
     */
    public static final String DATA_IS_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "data_is_empty";
    // endregion

    // region kl.npki.base.management.common.auth.KcspSsoIdentifierImpl
    /**
     * 远程调用KCSP用户信息接口认证失败,错误详情: {0}; HTTP 状态码: {1}; 响应体: {2}
     */
    public static final String REMOTE_CALL_TO_KCSP_USER_INFORMATION_INTERFACE_AUTHENTICATION_FAILED_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "remote_call_to_kcsp_user_information_interface_authentication_failed";
    /**
     * 处理KCSP用户认证时发生未知错误,错误详情: {0}
     */
    public static final String UNKNOWN_ERROR_OCCURRED_WHILE_PROCESSING_KCSP_USER_AUTHENTICATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "unknown_error_occurred_while_processing_kcsp_user_authentication";
    /**
     * 用户状态异常，请联系管理员
     */
    public static final String THE_USER_STATUS_IS_ABNORMAL_PLEASE_CONTACT_THE_ADMINISTRATOR_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "the_user_status_is_abnormal_please_contact_the_administrator";
    /**
     * 获取有效用户信息失败
     */
    public static final String FAILED_TO_OBTAIN_VALID_USER_INFORMATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "failed_to_obtain_valid_user_information";
    /**
     * 未提供AccessToken，用户未登录或会话已过期
     */
    public static final String NO_ACCESS_TOKEN_PROVIDED_USER_NOT_LOGGED_IN_OR_SESSION_EXPIRED_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "no_access_token_provided_user_not_logged_in_or_session_expired";
    // endregion

    // region kl.npki.base.management.utils.ValidateHelper
    /**
     * 角色不存在
     */
    public static final String THE_ROLE_NOT_EXIST_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "the_role_not_exist";


    /**
     * 资源不存在
     */
    public static final String THE_RESOURCE_NOT_EXIST_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "the_resource_not_exist";

    /**
     * 请重新登录系统
     */
    public static final String PLEASE_LOGIN_TO_SYSTEM_AGAIN_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "please_login_to_system_again";

    // endregion

    // region kl.npki.base.management.repository.impl.AdminCertRepositoryImpl
    /**
     * 管理员证书信息完整性校验失败,用户名：{0}
     */
    public static final String ADMINISTRATOR_CERTIFICATE_INFORMATION_INTEGRITY_VERIFICATION_FAILED_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "administrator_certificate_information_integrity_verification_failed";
    // endregion

    // region kl.npki.base.management.controller.administrator.AdminMgrController
    /**
     * 没有注册该类型管理员的权限
     */
    public static final String NO_PERMISSION_TO_REGISTER_AS_AN_ADMINISTRATOR_FOR_THIS_TYPE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "no_permission_to_register_as_an_administrator_for_this_type";
    /**
     * 没有导入该类型管理员的权限
     */
    public static final String NO_PERMISSION_TO_IMPORT_THIS_TYPE_OF_ADMINISTRATOR_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "no_permission_to_import_this_type_of_administrator";
    /**
     * 没有请求废除该类型管理员证书的权限
     */
    public static final String NO_PERMISSION_TO_REQUEST_REVOCATION_OF_THIS_TYPE_OF_ADMINISTRATOR_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "no_permission_to_request_revocation_of_this_type_of_administrator_certificate";
    /**
     * 不容许管理员废除自己
     */
    public static final String DO_NOT_ALLOW_ADMINISTRATORS_TO_ABOLISH_THEMSELVES_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "do_not_allow_administrators_to_abolish_themselves";
    /**
     * 没有撤销该类型管理员证书废除申请的权限
     */
    public static final String DO_NOT_HAVE_THE_AUTHORITY_TO_REVOKE_THE_ADMINISTRATOR_CERTIFICATE_REVOCATION_APPLICATION_FOR_THIS_TYPE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "do_not_have_the_authority_to_revoke_the_administrator_certificate_revocation_application_for_this_type";
    /**
     * 没有注销该类型管理员的权限
     */
    public static final String DO_NOT_HAVE_PERMISSION_TO_LOG_OUT_OF_THIS_TYPE_OF_ADMINISTRATOR_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "do_not_have_permission_to_log_out_of_this_type_of_administrator";
    /**
     * 没有延期该类型管理员的权限
     */
    public static final String NO_EXTENSION_OF_ADMINISTRATOR_PRIVILEGES_FOR_THIS_TYPE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "no_extension_of_administrator_privileges_for_this_type";
    // endregion

    // region kl.npki.base.management.repository.impl.RoleRepositoryImpl
    /**
     * 角色信息完整性校验失败,角色名：{0}
     */
    public static final String CHARACTER_INFORMATION_INTEGRITY_VERIFICATION_FAILED_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "character_information_integrity_verification_failed";
    // endregion

    // region kl.npki.base.management.controller.config.DbConfigController
    /**
     * 不允许配置H2数据库
     */
    public static final String H2_DATABASE_CONFIGURATION_IS_NOT_ALLOWED_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "h2_database_configuration_is_not_allowed";
    /**
     * 数据库配置信息不能为空
     */
    public static final String DATABASE_CONFIGURATION_INFORMATION_CANNOT_BE_EMPTY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "database_configuration_information_cannot_be_empty";
    // endregion

    // region kl.npki.base.management.common.auth.AbstractIdentifierImpl
    /**
     * 会话ID不存在
     */
    public static final String THE_SESSION_ID_DOES_NOT_EXIST_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "the_session_id_does_not_exist";
    // endregion

    public static final String THE_ROOT_ORG_DOES_NOT_EXIST_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "the_root_org_does_not_exist_i18n_key";

}
