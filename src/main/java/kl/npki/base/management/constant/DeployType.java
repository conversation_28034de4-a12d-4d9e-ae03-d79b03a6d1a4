package kl.npki.base.management.constant;

import kl.nbase.i18n.i18n.EnumI18n;

import static kl.npki.base.management.constant.I18nConstant.BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX;

/**
 * <AUTHOR>
 * @date 2022/8/29 14:50
 */
public enum DeployType implements EnumI18n {

    NOT_DEPLOYED(0, "未部署"),
    DEPLOYED(1, "已部署")
    ;

    private int code;
    private String desc;

    DeployType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return tr();
    }

    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }

}
