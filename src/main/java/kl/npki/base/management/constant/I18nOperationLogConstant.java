package kl.npki.base.management.constant;

import static kl.npki.base.management.constant.I18nConstant.BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX;


/**
 * i18n操作日志常量
 *
 * <AUTHOR>
 * @date 2025/01/22
 */
public final class I18nOperationLogConstant {

    private I18nOperationLogConstant() {
    }

    // region kl.npki.base.management.controller.config.PortConfigController
    /**
     * 获取端口配置
     */
    public static final String GET_PORT_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "get_port_configuration";
    /**
     * 修改端口配置
     */
    public static final String MODIFY_PORT_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "modify_port_configuration";
    // endregion

    // region kl.npki.base.management.controller.cert.SslServerCertController
    /**
     * 自签发站点证书
     */
    public static final String SELF_ISSUED_SITE_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "self_issued_site_certificate";
    /**
     * 获取签发站点证书的密钥列表
     */
    public static final String OBTAIN_THE_KEY_LIST_FOR_ISSUING_SITE_CERTIFICATES_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "obtain_the_key_list_for_issuing_site_certificates";
    /**
     * 查看服务器站点证书详细信息
     */
    public static final String VIEW_DETAILED_INFORMATION_OF_SERVER_SITE_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "view_detailed_information_of_server_site_certificate";

    /**
     * 查看身份证书请求信息
     */
    public static final String VIEW_DETAILED_INFORMATION_OF_SERVER_ID_CERTIFICATE_INFO_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "view_detailed_information_of_server_id_certificate_info";

    /**
     * 查看ssl证书请求信息
     */
    public static final String VIEW_DETAILED_INFORMATION_OF_SERVER_SSl_CERTIFICATE_INFO_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "view_detailed_information_of_server_ssl_certificate_info";

    /**
     * 查看根证书请求信息
     */
    public static final String VIEW_DETAILED_INFORMATION_OF_SERVER_ROOT_CERTIFICATE_INFO_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "view_detailed_information_of_server_root_certificate_info";

    // endregion

    // region kl.npki.base.management.controller.log.OpLogController
    /**
     * 查询操作日志列表
     */
    public static final String QUERY_OPERATION_LOG_LIST_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_operation_log_list";
    /**
     * 查询操作日志详细信息
     */
    public static final String QUERY_OPERATION_LOG_DETAILED_INFORMATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_operation_log_detailed_information";
    // endregion

    // region kl.npki.base.management.controller.administrator.SecurityMgrController
    /**
     * 待审核列表
     */
    public static final String PENDING_REVIEW_LIST_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "pending_review_list";
    /**
     * 审核通过
     */
    public static final String APPROVED_BY_REVIEW_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "approved_by_review";
    /**
     * 审核拒绝
     */
    public static final String REVIEW_REJECTED_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "review_rejected";
    /**
     * 查看审核详情
     */
    public static final String VIEW_AUDIT_DETAILS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "view_audit_details";
    // endregion

    // region kl.npki.base.management.controller.SysHelpController
    /**
     * 获取操作引导信息
     */
    public static final String OBTAIN_OPERATION_GUIDANCE_INFORMATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "obtain_operation_guidance_information";
    /**
     * 修改操作引导信息
     */
    public static final String MODIFY_OPERATION_GUIDANCE_INFORMATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "modify_operation_guidance_information";
    // endregion

    // region kl.npki.base.management.controller.administrator.AuditController
    /**
     * 审计日志列表查询
     */
    public static final String AUDIT_LOG_LIST_QUERY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "audit_log_list_query";
    /**
     * 日志审计
     */
    public static final String LOG_AUDIT_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "log_audit";
    /**
     * 审计日志详情查询
     */
    public static final String AUDIT_LOG_DETAILS_QUERY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "audit_log_details_query";
    // endregion

    // region kl.npki.base.management.controller.config.CacheConfigController
    /**
     * 查询缓存配置
     */
    public static final String QUERY_CACHE_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_cache_configuration";
    /**
     * 保存缓存配置
     */
    public static final String SAVE_CACHE_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "save_cache_configuration";
    /**
     * 测试缓存连接
     */
    public static final String TEST_CACHE_CONNECTION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "test_cache_connection";
    // endregion

    // region kl.npki.base.management.controller.RoleController
    /**
     * 新增角色
     */
    public static final String ADD_NEW_ROLE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "add_new_role";
    /**
     * 禁用角色
     */
    public static final String DISABLE_ROLE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "disable_role";
    /**
     * 角色列表查询
     */
    public static final String ROLE_LIST_QUERY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "role_list_query";
    /**
     * 初始化角色权限树
     */
    public static final String INITIALIZE_ROLE_PERMISSION_TREE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "initialize_role_permission_tree";
    /**
     * 保存角色权限树
     */
    public static final String SAVE_ROLE_PERMISSION_TREE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "save_role_permission_tree";
    /**
     * 获取角色的API资源权限
     */
    public static final String GET_ROLE_API_RESOURCES_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "get_role_api_resources";
    /**
     * 更新角色的API资源权限
     */
    public static final String UPDATE_ROLE_API_RESOURCES_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "update_role_api_resources";    /**
     * 查询API资源权限列表
     */
    public static final String LIST_API_RESOURCES_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "list_api_resources";
    /**
     * 查询API资源权限列表（树形结构）
     */
    public static final String QUERY_API_RESOURCE_PERMISSIONS_TREE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_api_resource_permissions_tree";
    /**
     * 查询API资源权限详情
     */
    public static final String QUERY_API_RESOURCE_PERMISSION_DETAILS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_api_resource_permission_details";
    /**
     * 新增API资源权限
     */
    public static final String CREATE_API_RESOURCE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "create_api_resource";
    /**
     * 新增API资源权限
     */
    public static final String CREATE_API_RESOURCE_PERMISSION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "create_api_resource_permission";
    /**
     * 更新API资源权限
     */
    public static final String UPDATE_API_RESOURCE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "update_api_resource";
    /**
     * 更新API资源权限
     */
    public static final String UPDATE_API_RESOURCE_PERMISSION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "update_api_resource_permission";
    /**
     * 删除API资源权限
     */
    public static final String DELETE_API_RESOURCE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "delete_api_resource";
    /**
     * 删除API资源权限
     */
    public static final String DELETE_API_RESOURCE_PERMISSION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "delete_api_resource_permission";
    /**
     * 获取角色的统一资源权限
     */
    public static final String GET_ROLE_UNIFIED_RESOURCES_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "get_role_unified_resources";
    /**
     * 更新角色的统一资源权限
     */
    public static final String UPDATE_ROLE_UNIFIED_RESOURCES_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "update_role_unified_resources";
    // endregion

    // region kl.npki.base.management.controller.config.LogConfigController
    /**
     * 查询日志配置
     */
    public static final String QUERY_LOG_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_log_configuration";
    /**
     * 保存日志配置
     */
    public static final String SAVE_LOG_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "save_log_configuration";
    /**
     * 获取日志输出类型
     */
    public static final String GET_LOG_OUTPUT_TYPE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "get_log_output_type";
    /**
     * 获取日志级别类型下拉选项
     */
    public static final String GET_LOG_LEVEL_TYPE_DROPDOWN_OPTIONS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "get_log_level_type_dropdown_options";
    // endregion

    // region kl.npki.base.management.controller.LicenseController
    /**
     * 导入License
     */
    public static final String IMPORT_LICENSE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "import_license";
    /**
     * 查询License
     */
    public static final String QUERY_LICENSE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_license";
    /**
     * 解析License
     */
    public static final String PARSE_LICENSE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "parse_license";
    // endregion

    // region kl.npki.base.management.controller.config.LoginTypeConfigController
    /**
     * 保存登录模式
     */
    public static final String SAVE_LOGIN_MODE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "save_login_mode";
    /**
     * 获取登录模式
     */
    public static final String GET_LOGIN_MODE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "get_login_mode";
    /**
     * 获取登录模式枚举列表
     */
    public static final String GET_LOGIN_MODE_ENUMERATION_LIST_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "get_login_mode_enumeration_list";
    // endregion

    // region kl.npki.base.management.controller.home.BaseHomePageController
    /**
     * 服务自检统计结果
     */
    public static final String SERVICE_SELF_INSPECTION_STATISTICS_RESULTS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "service_self_inspection_statistics_results";
    // endregion

    // region kl.npki.base.management.controller.config.EngineConfigController
    /**
     * 查询加密机配置
     */
    public static final String QUERY_ENCRYPTION_MACHINE_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_encryption_machine_configuration";
    /**
     * 保存加密机配置
     */
    public static final String SAVE_ENCRYPTION_MACHINE_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "save_encryption_machine_configuration";
    /**
     * 查询加密机类型列表
     */
    public static final String QUERY_THE_LIST_OF_ENCRYPTION_MACHINE_TYPES_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_the_list_of_encryption_machine_types";
    /**
     * 测试加密机连接
     */
    public static final String TEST_ENCRYPTION_MACHINE_CONNECTION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "test_encryption_machine_connection";
    /**
     * 查询当前配置加密机支持的非对称密钥类型列表
     */
    public static final String QUERY_THE_LIST_OF_ASYMMETRIC_KEY_TYPES_SUPPORTED_BY_THE_CURRENT_CONFIGURED_ENCRYPTION_MACHINE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_the_list_of_asymmetric_key_types_supported_by_the_current_configured_encryption_machine";
    /**
     * 查询当前配置加密机支持的抗量子加密密钥类型列表
     */
    public static final String QUERY_THE_LIST_OF_ANTI_QUANTUM_ENCRYPTION_KEY_TYPES_SUPPORTED_BY_THE_CURRENT_CONFIGURED_ENCRYPTION_MACHINE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_the_list_of_anti_quantum_encryption_key_types_supported_by_the_current_configured_encryption_machine";
    /**
     * 根据加密机类型查询支持的非对称密钥类型列表
     */
    public static final String QUERY_THE_LIST_OF_SUPPORTED_ASYMMETRIC_KEY_TYPES_BASED_ON_THE_ENCRYPTION_MACHINE_TYPE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_the_list_of_supported_asymmetric_key_types_based_on_the_encryption_machine_type";
    /**
     * 查询密钥索引列表
     */
    public static final String QUERY_KEY_INDEX_LIST_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_key_index_list";
    /**
     * 查询配置是否完成
     */
    public static final String CHECK_IF_THE_CONFIGURATION_IS_COMPLETE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "check_if_the_configuration_is_complete";
    // endregion

    // region kl.npki.base.management.controller.SysInfoController
    /**
     * 获取系统信息相关配置
     */
    public static final String OBTAIN_SYSTEM_INFORMATION_RELATED_CONFIGURATIONS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "obtain_system_information_related_configurations";
    /**
     * 更新系统信息配置
     */
    public static final String UPDATE_SYSTEM_INFORMATION_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "update_system_information_configuration";
    /**
     * 查询用户是否同意使用许可协议
     */
    public static final String CHECK_IF_THE_USER_AGREES_TO_USE_THE_LICENSE_AGREEMENT_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "check_if_the_user_agrees_to_use_the_license_agreement";
    /**
     * 用户同意使用许可协议
     */
    public static final String THE_USER_AGREES_TO_USE_THE_LICENSE_AGREEMENT_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "the_user_agrees_to_use_the_license_agreement";
    /**
     * 系统当前语言查询
     */
    public static final String CURRENT_LANGUAGE_QUERY_IN_THE_SYSTEM_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "current_language_query_in_the_system";
    /**
     * 系统支持语言列表查询
     */
    public static final String THE_SYSTEM_SUPPORTS_LANGUAGE_LIST_QUERY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "the_system_supports_language_list_query";
    /**
     * 系统语言设置
     */
    public static final String SYSTEM_LANGUAGE_SETTINGS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "system_language_settings";

    /**
     * 系统当前日期格式化配置
     */
    public static final String SYSTEM_DATETIME_SETTINGS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "system_date_time_settings";


    // endregion

    // region kl.npki.base.management.controller.cert.TrustCertController
    /**
     * 查询管理根证书
     */
    public static final String QUERY_MANAGEMENT_ROOT_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_management_root_certificate";
    /**
     * 查询管理根证书算法
     */
    public static final String QUERY_MANAGEMENT_ROOT_CERTIFICATE_ALGORITHM_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_management_root_certificate_algorithm";
    /**
     * 查询管理根证书算法
     */
    public static final String QUERY_ROOT_CERTIFICATE_ALGORITHM_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_root_certificate_algorithm";
    /**
     * 自签发根证书
     */
    public static final String SELF_SIGNED_HAIR_ROOT_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "self_signed_hair_root_certificate";
    /**
     * 重新签发根证书
     */
    public static final String RE_SIGN_THE_HAIR_ROOT_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "re_sign_the_hair_root_certificate";
    /**
     * 导入可信证书
     */
    public static final String IMPORT_TRUSTED_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "import_trusted_certificate";
    /**
     * 删除可信证书链
     */
    public static final String DELETE_TRUSTED_CERTIFICATE_CHAIN_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "delete_trusted_certificate_chain";
    /**
     * 导入管理根证书
     */
    public static final String IMPORT_MANAGEMENT_ROOT_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "import_management_root_certificate";
    /**
     * 更新导入管理根证书
     */
    public static final String UPDATE_IMPORT_MANAGEMENT_ROOT_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "update_import_management_root_certificate";
    /**
     * 生成证书请求
     */
    public static final String GENERATE_CERTIFICATE_REQUEST_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "generate_certificate_request";
    /**
     * 更新证书请求
     */
    public static final String UPDATE_CERTIFICATE_REQUEST_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "update_certificate_request";
    /**
     * 导出证书请求
     */
    public static final String EXPORT_CERTIFICATE_REQUEST_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "export_certificate_request";
    /**
     * 导出证书
     */
    public static final String EXPORT_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "export_certificate";
    /**
     * 查询可信证书列表
     */
    public static final String QUERY_THE_LIST_OF_TRUSTED_CERTIFICATES_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_the_list_of_trusted_certificates";
    // endregion

    // region kl.npki.base.management.controller.cert.IdCertController
    /**
     * 自签发身份证书
     */
    public static final String SELF_ISSUED_IDENTITY_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "self_issued_identity_certificate";
    /**
     * 更新身份证书
     */
    public static final String UPDATE_IDENTITY_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "update_identity_certificate";
    /**
     * 获取身份证书
     */
    public static final String OBTAIN_IDENTITY_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "obtain_identity_certificate";
    // endregion

    // region kl.npki.base.management.controller.cert.CertPreviewController
    /**
     * 证书预览解析
     */
    public static final String CERTIFICATE_PREVIEW_ANALYSIS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "certificate_preview_analysis";
    /**
     * 解析证书
     */
    public static final String PARSE_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "parse_certificate";
    // endregion

    // region kl.npki.base.management.controller.administrator.AdminMgrController
    /**
     * 注册管理员
     */
    public static final String REGISTRATION_ADMINISTRATOR_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "registration_administrator";
    /**
     * 查询可操作的角色列表
     */
    public static final String QUERY_THE_LIST_OF_OPERABLE_ROLES_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_the_list_of_operable_roles";
    /**
     * 导入管理员证书
     */
    public static final String IMPORT_ADMINISTRATOR_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "import_administrator_certificate";
    /**
     * 导入延期管理员证书
     */
    public static final String IMPORT_EXTENDED_ADMINISTRATOR_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "import_extended_administrator_certificate";
    /**
     * 请求废除证书
     */
    public static final String REQUEST_TO_REVOKE_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "request_to_revoke_certificate";
    /**
     * 废除证书
     */
    public static final String ABOLISH_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "abolish_certificate";
    /**
     * 废除超级管理员
     */
    public static final String ABOLISH_SUPER_ADMINISTRATORS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "abolish_super_administrators";
    /**
     * 撤销证书废除申请
     */
    public static final String REVOCATION_OF_CERTIFICATE_REVOCATION_APPLICATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "revocation_of_certificate_revocation_application";
    /**
     * 查询系统管理员列表
     */
    public static final String QUERY_THE_LIST_OF_SYSTEM_ADMINISTRATORS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_the_list_of_system_administrators";
    /**
     * 查询备份管理员列表
     */
    public static final String QUERY_THE_LIST_OF_BACKUP_ADMINISTRATORS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_the_list_of_backup_administrators";
    /**
     * 系统管理员信息详情
     */
    public static final String SYSTEM_ADMINISTRATOR_INFORMATION_DETAILS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "system_administrator_information_details";
    /**
     * 系统管理员证书SN
     */
    public static final String SYSTEM_ADMINISTRATOR_CERTIFICATE_SN_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "system_administrator_certificate_sn";
    /**
     * 申请注销管理员
     */
    public static final String APPLY_FOR_CANCELLATION_OF_ADMINISTRATOR_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "apply_for_cancellation_of_administrator";
    /**
     * 注销管理员
     */
    public static final String CANCEL_ADMINISTRATOR_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "cancel_administrator";
    /**
     * 申请延期管理员
     */
    public static final String APPLY_FOR_EXTENSION_ADMINISTRATOR_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "apply_for_extension_administrator";
    /**
     * 导入证书链
     */
    public static final String IMPORT_CERTIFICATE_CHAIN_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "import_certificate_chain";
    /**
     * 部署时，获取内置的管理员列表
     */
    public static final String DURING_DEPLOYMENT_RETRIEVE_THE_BUILT_IN_ADMINISTRATOR_LIST_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "during_deployment_retrieve_the_built_in_administrator_list";
    /**
     * 修改密码
     */
    public static final String CHANGE_PASSWORD_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "change_password";
    /**
     * 解除账号锁定
     */
    public static final String UNLOCK_THE_ACCOUNT_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "unlock_the_account";
    /**
     * 获取管理员用户状态
     */
    public static final String GET_ADMIN_USER_STATUS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "get_admin_user_status";
    /**
     * 获取管理员证书状态
     */
    public static final String GET_ADMIN_CER_STATUS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "get_admin_cert_status";
    // endregion

    // region kl.npki.base.management.controller.log.ApiLogController
    /**
     * 查询服务日志列表
     */
    public static final String QUERY_SERVICE_LOG_LIST_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_service_log_list";
    /**
     * 查询服务日志详细信息
     */
    public static final String QUERY_DETAILED_INFORMATION_OF_SERVICE_LOGS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_detailed_information_of_service_logs";
    public static final String QUERY_EXCEL_ELECTRONIC_SIGNATURE_INFORMATION_LOGS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_excel_electronic_signature_information";
    public static final String EXPORT_SIGNED_FILE_OF_SERVICE_LOGS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "export_signed_file_of_service_logs_i18n_key";
    public static final String EXPORT_FILE_OF_SERVICE_LOGS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "export_file_of_service_logs_i18n_key";
    public static final String EXPORT_FILE_OF_VIEW_LOGS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "export_file_of_view_logs_i18n_key";
    public static final String EXPORT_FILE_OF_DELETE_LOGS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "export_file_of_delete_logs_i18n_key";
    public static final String EXPORT_FILE_OF_DOWNLOAD_LOGS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "export_file_of_download_logs_i18n_key";
    // endregion

    // region kl.npki.base.management.controller.log.RunLogController
    /**
     * 运行日志列表查看
     */
    public static final String VIEW_THE_LIST_OF_RUNNING_LOGS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "view_the_list_of_running_logs";
    /**
     * 运行日志查看
     */
    public static final String VIEW_OPERATION_LOGS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "view_operation_logs";
    /**
     * 运行日志下载
     */
    public static final String DOWNLOAD_RUNNING_LOGS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "download_running_logs";
    // endregion

    // region kl.npki.base.management.controller.backup.SystemBackupRestoreController
    /**
     * 配置备份
     */
    public static final String CONFIGURE_BACKUP_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "configure_backup";
    /**
     * 配置恢复
     */
    public static final String CONFIGURE_RECOVERY_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "configure_recovery";
    // endregion

    // region kl.npki.base.management.controller.config.CryptoAlgoConfigController
    /**
     * 查询密码机支持的密码算法配置
     */
    public static final String QUERY_THE_PASSWORD_ALGORITHM_CONFIGURATION_SUPPORTED_BY_THE_PASSWORD_MACHINE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_the_password_algorithm_configuration_supported_by_the_password_machine";
    /**
     * 查询系统配置的密码算法配置
     */
    public static final String QUERY_THE_PASSWORD_ALGORITHM_CONFIGURATION_OF_THE_SYSTEM_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_the_password_algorithm_configuration_of_the_system_configuration";
    /**
     * 修改密码算法配置
     */
    public static final String MODIFY_PASSWORD_ALGORITHM_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "modify_password_algorithm_configuration";
    // endregion

    // region kl.npki.base.management.controller.AuthenticateController
    /**
     * 获取登录签名数据原文
     */
    public static final String RETRIEVE_THE_ORIGINAL_LOGIN_SIGNATURE_DATA_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "retrieve_the_original_login_signature_data";
    /**
     * 获取登录时所需管理员数量
     */
    public static final String OBTAIN_THE_REQUIRED_NUMBER_OF_ADMINISTRATORS_FOR_LOGIN_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "obtain_the_required_number_of_administrators_for_login";
    /**
     * 获取管理员签名证书序列号
     */
    public static final String OBTAIN_THE_SERIAL_NUMBER_OF_THE_ADMINISTRATOR_SIGNATURE_CERTIFICATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "obtain_the_serial_number_of_the_administrator_signature_certificate";
    /**
     * 验证SSL通道
     */
    public static final String VERIFY_SSL_CHANNEL_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "verify_ssl_channel";
    /**
     * 管理员登录
     */
    public static final String ADMINISTRATOR_LOGIN_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "administrator_login";
    /**
     * 注销登录
     */
    public static final String LOG_OUT_OF_LOGIN_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "log_out_of_login";

    public static final String SSO_AUTH_ADDR_LOGIN_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "sso_auth_addr";
    /**
     * 获取验证码图片Base64编码
     */
    public static final String GET_CAPTCHA_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "i18n_annotation.get_captcha";
    // endregion

    // region kl.npki.base.management.controller.config.SystemConfigController
    /**
     * 获取系统配置模板
     */
    public static final String OBTAIN_SYSTEM_CONFIGURATION_TEMPLATE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "obtain_system_configuration_template";
    /**
     * 查询系统当前环境
     */
    public static final String QUERY_THE_CURRENT_ENVIRONMENT_OF_THE_SYSTEM_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_the_current_environment_of_the_system";
    /**
     * 完成系统初始化
     */
    public static final String COMPLETE_SYSTEM_INITIALIZATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "complete_system_initialization";
    /**
     * 演示环境初始化
     */
    public static final String DEMO_ENVIRONMENT_INITIALIZATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "demo_environment_initialization";
    /**
     * 查询系统整体配置状态与进度
     */
    public static final String QUERY_THE_OVERALL_CONFIGURATION_STATUS_AND_PROGRESS_OF_THE_SYSTEM_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_the_overall_configuration_status_and_progress_of_the_system";
    /**
     * 查询系统通用配置
     */
    public static final String QUERY_SYSTEM_GENERAL_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_system_general_configuration";
    /**
     * 保存系统通用配置
     */
    public static final String SAVE_SYSTEM_GENERAL_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "save_system_general_configuration";
    // endregion

    // region kl.npki.base.management.controller.SelfCheckController
    /**
     * 执行所有服务自检项
     */
    public static final String EXECUTE_ALL_SERVICE_SELF_CHECK_ITEMS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "execute_all_service_self_check_items";
    /**
     * 执行指定的服务自检项
     */
    public static final String EXECUTE_SPECIFIED_SERVICE_SELF_TEST_ITEMS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "execute_specified_service_self_test_items";
    /**
     * 查询所有自检数据
     */
    public static final String QUERY_ALL_SELF_CHECKING_DATA_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_all_self_checking_data";
    /**
     * 查询告警自检数据
     */
    public static final String QUERY_ALARM_SELF_TEST_DATA_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_alarm_self_test_data";
    // endregion

    // region kl.npki.base.management.controller.config.DbConfigController
    /**
     * 查询数据库配置
     */
    public static final String QUERY_DATABASE_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_database_configuration";
    /**
     * 保存数据库配置
     */
    public static final String SAVE_DATABASE_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "save_database_configuration";
    /**
     * 保存数据库密码配置
     */
    public static final String SAVE_DATABASE_PASSWD_CONFIGURATION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "save_database_passwd_configuration";
    /**
     * 测试数据库连接
     */
    public static final String TEST_DATABASE_CONNECTION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "test_database_connection";
    /**
     * 查询配置状态
     */
    public static final String QUERY_CONFIGURATION_STATUS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_configuration_status";
    // endregion

    // region kl.npki.base.management.controller.inspection.InspectionMgmtController
    /**
     * 执行系统巡检
     */
    public static final String EXECUTE_INSPECTION_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "execute_inspection";
    /**
     * 查询巡检项列表
     */
    public static final String QUERY_INSPECTION_ITEM_LIST_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_inspection_item_list";
    /**
     * 查询巡检记录列表
     */
    public static final String QUERY_INSPECTION_RECORD_LIST_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_inspection_record_list";
    /**
     * 查询巡检记录详情
     */
    public static final String QUERY_INSPECTION_RECORD_DETAIL_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_inspection_record_details";
    /**
     * 删除巡检记录
     */
    public static final String DELETE_INSPECTION_RECORD_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "delete_inspection_record";
    /**
     * 导出巡检结果
     */
    public static final String EXPORT_INSPECTION_RECORD_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "export_inspection_record";
    // endregion

    // region kl.npki.base.management.controller.kcsp.KcspSvcSyncController
    /**
     * KCSP平台服务同步（新增、更新、删除平台公共服务）
     */
    public static final String KCSP_SYNC_SERVICE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "annotation.kcsp_sync_service";
    // endregion

    // region kl.npki.base.management.controller.util.UtilController
    /**
     * 获取配置信息
     */
    public static final String GET_ENV_VALUE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "annotation.get_env_value";
    // endregion

    // region kl.npki.base.management.controller.BaseConstantController
    /**
     * 查询非对称算法常量
     */
    public static final String CONSTANT_ASYM_ALGO_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "annotation.constant_asym_algo";
    /**
     * 查询证书状态常量
     */
    public static final String CONSTANT_CERT_STATUS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "annotation.constant_cert_status";
    /**
     * 查询用户状态常量
     */
    public static final String CONSTANT_USER_STATUS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "annotation.constant_user_status";
    /**
     * 查询CA等级常量
     */
    public static final String CONSTANT_CA_LEVEL_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "annotation.constant_ca_level";
    /**
     * 查询实体状态常量
     */
    public static final String CONSTANT_ENTITY_STATUS_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "annotation.constant_entity_status";
    /**
     * 查询HTTP请求类型列表
     */
    public static final String CONSTANT_HTTP_METHOD_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "annotation.constant_http_method";
    /**
     * 查询统一资源类型常量
     */
    public static final String CONSTANT_RESOURCE_TYPES_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "annotation.constant_resource_types";
    // endregion

}
