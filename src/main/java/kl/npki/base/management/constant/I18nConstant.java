package kl.npki.base.management.constant;

import kl.nbase.i18n.i18n.I18nUtil;

/**
 * <AUTHOR>
 * @date 2025/1/17 10:05
 */
public class I18nConstant {
    private I18nConstant() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 国际化资源路径
     */
    public static final String BASE_MANAGEMENT_I18N_RESOURCE_PATH = "kl.npki.base.management.i18n";

    /**
     * 国际化资源路径前缀
     */
    public static final String BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX = BASE_MANAGEMENT_I18N_RESOURCE_PATH + I18nUtil.SEPARATOR;

    // 统一资源管理相关 i18n key
    /**
     * 查询统一资源列表（树形结构）
     */
    public static final String QUERY_UNIFIED_RESOURCES_TREE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "query_unified_resources_tree";

    /**
     * 新增统一资源
     */
    public static final String CREATE_UNIFIED_RESOURCE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "create_unified_resource";
    /**
     * 更新统一资源
     */
    public static final String UPDATE_UNIFIED_RESOURCE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "update_unified_resource";
    /**
     * 删除统一资源
     */
    public static final String DELETE_UNIFIED_RESOURCE_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "delete_unified_resource";
    /**
     * 批量删除统一资源
     */
    public static final String BATCH_DELETE_UNIFIED_RESOURCES_I18N_KEY = BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + "batch_delete_unified_resources";

    /**
     * 管理员信息异常
     */
    public static final String ADMIN_ID_ERROR_I18N_KEY = "admin_id_error";
    public static final String ADMIN_NAME_ERROR_I18N_KEY = "admin_name_error";
    public static final String ADMIN_NAME_ID_ERROR_I18N_KEY = "admin_name_id_no_match_error";


    /**
     * 获取国际化后的信息
     *
     * @param key  国际化资源的键（不包含资源路径）
     * @param args 占位填充参数
     * @return 国际化结果
     */
    public static String getBaseMgmtI18nMessage(String key, Object... args) {
        return I18nUtil.tr(BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + key, args);
    }
}
