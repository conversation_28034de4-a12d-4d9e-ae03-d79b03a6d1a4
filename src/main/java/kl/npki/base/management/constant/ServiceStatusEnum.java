package kl.npki.base.management.constant;

import kl.nbase.i18n.i18n.EnumI18n;
import org.apache.commons.lang3.StringUtils;

import static kl.npki.base.management.constant.I18nConstant.BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX;

/**
 * 服务状态枚举类
 *
 * <AUTHOR>
 * @since 2025/7/4 15:50
 */

public enum ServiceStatusEnum implements EnumI18n {

    /**
     * 启动
     */
    START(1, "服务启动"),

    /**
     * 关闭
     */
    STOP(0, "服务关闭");

    private final int code;
    private final String desc;

    ServiceStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return tr();
    }

    @Override
    public String getMessageKey() {
        return BASE_MANAGEMENT_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }

    public String getMessageDesc() {
        String i18nValue = tr();
        if (StringUtils.isBlank(i18nValue) || getMessageKey().equalsIgnoreCase(i18nValue)) {
            return desc;
        }
        return i18nValue;
    }

}