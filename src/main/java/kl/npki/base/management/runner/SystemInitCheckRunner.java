package kl.npki.base.management.runner;

import kl.nbase.auth.authority.AllowListFactory;
import kl.npki.base.core.biz.check.SelfCheckManager;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.management.core.repository.IResourceRepository;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 初始化部署步骤信息
 *
 * <AUTHOR>
 * @date 2022/8/30 10:23
 */
@Component
public class SystemInitCheckRunner implements ApplicationRunner {

    @Resource
    private IResourceRepository resourceRepository;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        SysInfoConfig sysInfoConfig = BaseConfigWrapper.getSysInfoConfig();
        sysInfoConfig.setStartTime(new Date());
        ConfigHolder.get().save(sysInfoConfig);
        initAllowList();
        SelfCheckManager.getInstance().checkAll();
    }

    /**
     * 初始化白名单
     */
    private void initAllowList() {
        List<String> allowList = resourceRepository.getAllowList();
        AllowListFactory.init(allowList);
    }

}
