package kl.npki.base.management.runner;

import kl.nbase.bean.convert.ConvertService;
import kl.nbase.bean.validate.ValidateService;
import kl.npki.base.core.biz.org.OrgUploadFileProcessorImpl;
import kl.npki.base.core.biz.org.service.IOrgService;
import kl.npki.base.core.biz.upload.UploadProcessorRegistry;
import kl.npki.base.core.biz.upload.service.IUploadFileProcessor;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ServiceLoader;

/**
 * <AUTHOR>
 */
@Component
public class UploadProcessorRegistryInitRunner implements ApplicationRunner {

    @Resource
    private IOrgService orgService;

    @Resource
    private ConvertService convertService;

    @Resource
    private ValidateService validateService;
    @Override
    public void run(ApplicationArguments args) throws Exception {
        OrgUploadFileProcessorImpl orgUploadFileProcessor = new OrgUploadFileProcessorImpl(orgService, convertService, validateService);
        UploadProcessorRegistry.register(orgUploadFileProcessor);
        // 扫描并增加其他类型的文件上传处理器
        for (IUploadFileProcessor iUploadFileProcessor : ServiceLoader.load(IUploadFileProcessor.class)) {
            UploadProcessorRegistry.register(iUploadFileProcessor);
        }
    }
}
