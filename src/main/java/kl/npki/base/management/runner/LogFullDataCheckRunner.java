package kl.npki.base.management.runner;


import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.rolling.RollingFileAppender;
import kl.nbase.helper.utils.PropertiesUtils;
import kl.npki.base.core.biz.dataprotect.service.IDataProtectService;
import kl.npki.base.core.biz.dataprotect.service.impl.DataProtectServiceHashMacSm3Impl;
import kl.npki.base.core.biz.mainkey.model.NakedMainKeyEntity;
import kl.npki.base.core.biz.mainkey.service.MainKeyManager;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.EnvironmentEnum;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.base.service.util.LogbackHelper;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.Properties;

import static kl.npki.base.service.common.log.ProtectLogRollingFileAppender.*;


/**
 * 日志完整性校验服务
 *
 * <AUTHOR>
 */
@Component
public class LogFullDataCheckRunner implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(LogFullDataCheckRunner.class);

    private String logDirPath = "./logs";

    @PostConstruct
    private void init(){
        logDirPath = getLogFileDir();
    }

    @Override
    public void run(String... args) throws Exception {
        // 首先开启日志完整性保护
        STARTED = true;
        // 是否需要进行完整性校验
        if (!needVerifyFullLogFullData()) {
            return;
        }

        // 获取主密钥
        Long currentMainKeyId = MainKeyManager.getInstance().getCurrentMainKeyId();
        NakedMainKeyEntity mainKey = MainKeyManager.getInstance().getMainKey(currentMainKeyId);

        // 使用HMacSm3实现
        IDataProtectService dataProcess = new DataProtectServiceHashMacSm3Impl(mainKey.getSecretKey());

        // 读取日志完整性结果文件
        Properties properties;
        properties = PropertiesUtils.loadOrCreateCfgFile(LOG_PROTECT_RESULT_FILE);

        // 获取日志目录路径
        File logDir = new File(this.logDirPath);
        File[] files = logDir.listFiles();

        // 逐一进行完整性值验证
        for (File logFile : files) {
            // 跳过后缀名不为 log 的文件，以及跳过主日志文件
            String name = logFile.getName();
            if (!name.endsWith(LOG_SUFFIX) || !name.matches(EXCLUDE_PATTERN)) {
                continue;
            }

            String absolutePath = logFile.getAbsolutePath();
            String fileKey = Base64Util.base64Encode(absolutePath.getBytes());
            String hMacValue = properties.getProperty(fileKey);

            // 读取文件原文
            byte[] oriData = FileUtils.readFileToByteArray(logFile);

            if (StringUtils.isBlank(hMacValue)) {
                // 生成完整性值
                byte[] hMac = dataProcess.generateData(oriData);
                properties.setProperty(fileKey, Base64Util.base64Encode(hMac));
                continue;
            }

            boolean result = dataProcess.verifyData(oriData, Base64Util.base64Decode(hMacValue));

            if (result) {
                log.info("Log: {} Integrity verification successful", name);
            } else {
                log.error("Log: {} Integrity verification failed, service exited!", name);
                // 完整性校验不通过
                System.exit(0);
            }
        }

        // 保存结果
        PropertiesUtils.attemptWriteProperties(properties, LOG_PROTECT_RESULT_FILE);
    }

    private boolean needVerifyFullLogFullData() {
        String environmentSwitchId = BaseConfigWrapper.getSysInfoConfig().getEnvironmentSwitchId();
        return !EnvironmentEnum.DEMO.getId().equals(environmentSwitchId) &&
            BaseConfigWrapper.getSysConfig().isOpenDataFull();
    }

    private String getLogFileDir(){
        RollingFileAppender<ILoggingEvent> rollingFileAppender = LogbackHelper.getRollingFileAppender();
        File currentLogFile = new File(rollingFileAppender.getFile());
        return currentLogFile.getParent();
    }

}
