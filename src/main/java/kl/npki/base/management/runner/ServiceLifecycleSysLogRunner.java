package kl.npki.base.management.runner;

import kl.nbase.helper.utils.JsonUtils;
import kl.nbase.helper.utils.LocalDateTimeUtils;
import kl.nbase.log.store.ILogStore;
import kl.nbase.log.store.LogStoreFactory;
import kl.npki.base.core.biz.log.model.OpLogInfo;
import kl.npki.base.core.configs.LogConfig;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.AuditStatusEnum;
import kl.npki.base.core.constant.LogTypeEnum;
import kl.npki.base.management.constant.ServiceStatusEnum;
import kl.npki.base.service.common.log.resolver.LogResolveInfo;
import kl.npki.base.service.common.log.store.SysLogStoreService;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 服务生命周期发送审计日志： 启动、关闭
 *
 * <AUTHOR>
 * @since 2025/7/4 15:17
 */

@Component
public class ServiceLifecycleSysLogRunner implements CommandLineRunner, ApplicationListener<ContextClosedEvent> {


    private static final Logger log = LoggerFactory.getLogger(ServiceLifecycleSysLogRunner.class);


    /**
     * 服务关闭审计是否发送：默认未发送
     */
    private final AtomicBoolean stopAlreadySend = new AtomicBoolean(Boolean.FALSE);

    /**
     * 服务启动发送审计
     *
     * @param args incoming main method arguments
     * @throws Exception Exception
     */
    @Override
    public void run(String... args) throws Exception {
        if (sendSysLogFlag()) {
            return;
        }
        // 发送服务启动审计日志
        sendSyslogMessage(ServiceStatusEnum.START);
    }


    /**
     * 上下文关闭前发送审计
     *
     * @param event 事件
     */
    @Override
    public void onApplicationEvent(@NotNull ContextClosedEvent event) {
        if (sendSysLogFlag()) {
            return;
        }
        // 不重复发送
        if (stopAlreadySend.get()) {
            return;
        }
        // 发送服务关闭审计日志
        sendSyslogMessage(ServiceStatusEnum.STOP);

        stopAlreadySend.set(Boolean.TRUE);
    }

    /**
     * 判断审计是否开启
     *
     * @return boolean
     */
    private boolean sendSysLogFlag() {
        LogConfig logConfig = BaseConfigWrapper.getLogConfig();
        return !Boolean.TRUE.toString().equalsIgnoreCase(logConfig.getSyslogEnable());
    }

    /**
     * 获取服务名
     *
     * @return String
     */
    private String getServerName() {
        SysInfoConfig sysInfoConfig = BaseConfigWrapper.getSysInfoConfig();
        return sysInfoConfig.getShortName();
    }

    /**
     * 发送审计信息
     *
     * @param serviceStatusEnum 服务状态枚举类
     */
    private void sendSyslogMessage(ServiceStatusEnum serviceStatusEnum) {
        String tenantId = BaseConfigWrapper.getSysInfoConfig().getEnvironmentSwitchId();
        long startTime = System.currentTimeMillis();
        LocalDateTime logWhenLocalDateTime = LocalDateTimeUtils.milliSecondParseLocalDateTime(startTime);
        String serviceContent = String.format("[%s]%s", getServerName(), serviceStatusEnum.getMessageDesc());
        OpLogInfo opLogInfo = new OpLogInfo();
        opLogInfo.setTenantId(tenantId);
        opLogInfo.setTraceId(StringUtils.EMPTY);
        opLogInfo.setLogWho(getServerName());
        opLogInfo.setUsername(StringUtils.EMPTY);
        opLogInfo.setLogDo(serviceContent);
        opLogInfo.setResult(Boolean.TRUE);
        opLogInfo.setLogWhat(serviceContent);
        opLogInfo.setClientIp(StringUtils.EMPTY);
        opLogInfo.setServerIp(StringUtils.EMPTY);
        opLogInfo.setLogWhen(logWhenLocalDateTime);
        opLogInfo.setRequest(StringUtils.EMPTY);
        opLogInfo.setAuditStatus(AuditStatusEnum.PENDING);
        opLogInfo.setOriginData(StringUtils.EMPTY);
        opLogInfo.setSecurityLog(Boolean.TRUE);
        long endTime = System.currentTimeMillis();
        opLogInfo.setLogEnd(LocalDateTimeUtils.milliSecondParseLocalDateTime(endTime));
        opLogInfo.setElapsedTime((int) (endTime - startTime));
        String opLogInfoStr = JsonUtils.toJson(opLogInfo);
        LogResolveInfo logResolveInfo = new LogResolveInfo(LogTypeEnum.OP_LOG.getLogType(), opLogInfoStr, tenantId);
        String syslogStr = JsonUtils.toJson(logResolveInfo);
        ILogStore logStore = LogStoreFactory.getInstance().getStore(SysLogStoreService.class);
        if (Objects.nonNull(logStore)) {
            log.info("{} send audit logs : {}", serviceContent, syslogStr);
            logStore.save(syslogStr);
        }
    }
}