package kl.npki.base.management.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.constant.CertStatus;
import kl.npki.base.core.constant.UserStatus;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.repository.entity.TAdminInfoDO;
import kl.npki.base.management.repository.mapper.TAdminInfoMapper;
import kl.npki.management.core.biz.admin.model.entity.AdminEntity;
import kl.npki.management.core.repository.IAdminInfoRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static kl.npki.base.management.constant.I18nExceptionInfoConstant.DATA_IS_EMPTY_I18N_KEY;

/**
 * <AUTHOR>
 * @date 2022/10/10 11:24
 * @desc 系统管理员仓库接口实现
 */
@Service
public class AdminInfoRepositoryImpl extends KlServiceImpl<TAdminInfoMapper, TAdminInfoDO> implements IAdminInfoRepository {

    @Resource
    private ConvertService convertService;

    @Override
    public List<AdminEntity> findValidAll() {
        List<TAdminInfoDO> adminInfoDOS = baseMapper.selectList(Wrappers
            .lambdaQuery(TAdminInfoDO.class)
            .ge(TAdminInfoDO::getStatus, UserStatus.NORMAL.getCode())
            .eq(TAdminInfoDO::getIsDelete, 0));
        return convertService.convert(adminInfoDOS, AdminEntity.class);
    }

    @Override
    public Long save(AdminEntity adminEntity) {
        CheckUtils.notNull(adminEntity, ManagementValidationError.PARAM_ERROR.toException());
        TAdminInfoDO infoDO = convertService.convert(adminEntity, TAdminInfoDO.class);
        try {
            this.save(infoDO);
        } catch (Exception e) {
            throw ManagementInternalError.DB_INSERT_ERROR.toException(e);
        }
        return infoDO.getId();
    }

    @Override
    public void update(AdminEntity adminEntity) {
        CheckUtils.notNull(adminEntity, ManagementValidationError.PARAM_ERROR.toException());
        try {
            super.updateById(convertService.convert(adminEntity, TAdminInfoDO.class));
        } catch (Exception e) {
            throw ManagementInternalError.DB_UPDATE_ERROR.toException(e);
        }
    }

    @Override
    public void delete(Long id) {
        try {
            removeById(id);
        } catch (Exception e) {
            throw ManagementInternalError.DB_DELETE_ERROR.toException(e);
        }
    }

    @Override
    public AdminEntity getById(Long id) {
        TAdminInfoDO infoDO = super.getById(id);
        CheckUtils.notNull(infoDO, ManagementInternalError.DB_QUERY_ERROR.toException(DATA_IS_EMPTY_I18N_KEY));
        return convertService.convert(infoDO, AdminEntity.class);
    }

    @Override
    public Boolean checkRoleUse(String roleCode) {
        long count = count(Wrappers
                                                .lambdaQuery(TAdminInfoDO.class)
                                                .eq(TAdminInfoDO::getRoleCode, roleCode));
        return 0 != count;
    }

    /**
     * 检查是否存在同名的管理员
     *
     * @param username
     * @return
     */
    @Override
    public Boolean checkUsername(String username) {
        long count = count(Wrappers
            .lambdaQuery(TAdminInfoDO.class)
            .eq(TAdminInfoDO::getUsername, username));
        return 0 != count;
    }

    @Override
    public long countAdminByRoleCode(String roleCode) {
        return baseMapper.countIssuedAdminByRole(roleCode, CertStatus.ISSUED.getCode(), null);
    }

    @Override
    public long countAdminByRoleAndGroup(String roleCode, String adminGroup) {
        return baseMapper.countIssuedAdminByRole(roleCode, CertStatus.ISSUED.getCode(), adminGroup);
    }

}
