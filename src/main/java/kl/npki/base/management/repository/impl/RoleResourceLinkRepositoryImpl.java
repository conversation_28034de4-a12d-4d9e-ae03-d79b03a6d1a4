package kl.npki.base.management.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.repository.RepositoryFactory;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.repository.entity.TResourceDO;
import kl.npki.base.management.repository.entity.TRoleResourceLinkDO;
import kl.npki.base.management.repository.mapper.TRoleResourceLinkMapper;
import kl.npki.management.core.biz.admin.model.entity.ResourceEntity;
import kl.npki.management.core.biz.admin.model.entity.RoleEntity;
import kl.npki.management.core.biz.admin.model.entity.RoleResourceLinkEntity;
import kl.npki.management.core.repository.IRoleRepository;
import kl.npki.management.core.repository.IRoleResourceLinkRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/10 11:24
 * @desc 系统管理员证书仓库接口实现
 */
@Service
public class RoleResourceLinkRepositoryImpl extends KlServiceImpl<TRoleResourceLinkMapper, TRoleResourceLinkDO> implements IRoleResourceLinkRepository {
    
    @Resource
    private ConvertService convertService;

    @Resource
    private TRoleResourceLinkMapper roleResourceLinkMapper;

    @Resource
    private IRoleRepository roleRepository;
    
    @Override
    public void batchSave(List<RoleResourceLinkEntity> linkEntityList) {
        CheckUtils.isTrue(!linkEntityList.isEmpty(), ManagementValidationError.PARAM_ERROR.toException());
        List<TRoleResourceLinkDO> linkDOList = linkEntityList
                                                   .stream()
                                                   .map(v -> convertService.convert(v, TRoleResourceLinkDO.class))
                                                   .collect(Collectors.toList());
        try {
            super.saveBatch(linkDOList);
        } catch (Exception e) {
            throw ManagementInternalError.DB_INSERT_ERROR.toException(e);
        }
    }
    
    @Override
    public List<ResourceEntity> queryResourceByRole(String roleCode, List<String> resourceTypes) {
        CheckUtils.notNull(roleCode, ManagementValidationError.PARAM_ERROR.toException());
        List<TResourceDO> resourceDOList = baseMapper.selectResourceByRole(roleCode, resourceTypes);
        return resourceDOList
                   .stream()
                   .map(v -> convertService.convert(v, ResourceEntity.class))
                   .collect(Collectors.toList());
    }
    

    
    @Override
    public void removeByRoleCode(String roleCode) {
        CheckUtils.notNull(roleCode, ManagementValidationError.PARAM_ERROR.toException());
        LambdaQueryWrapper<TRoleResourceLinkDO> queryWrapper = Wrappers
                                                                   .lambdaQuery(TRoleResourceLinkDO.class)
                                                                   .eq(TRoleResourceLinkDO::getRoleCode, roleCode);
        try {
            remove(queryWrapper);
        } catch (Exception e) {
            throw ManagementInternalError.DB_DELETE_ERROR.toException(e);
        }
    }

    @Override
    public List<RoleResourceLinkEntity> queryForList() {
        List<TRoleResourceLinkDO> resourceLinkDOList = super.list();
        if (resourceLinkDOList.isEmpty()) {
            return Collections.emptyList();
        }
        return convertService.convert(resourceLinkDOList, RoleResourceLinkEntity.class);
    }

    @Override
    public boolean batchDelete(Collection<RoleResourceLinkEntity> roleResourceLinkEntities) {
        if (roleResourceLinkEntities == null || roleResourceLinkEntities.isEmpty()) {
            return Boolean.TRUE;
        }
        List<Long> idList = roleResourceLinkEntities.stream().map(RoleResourceLinkEntity::getId).collect(Collectors.toList());
        return super.removeBatchByIds(idList);
    }

    @Override
    public void updateRoleResourceStatus(List<String> resourceList, Integer status) {
        if (CollectionUtils.isEmpty(resourceList)) {
            return;
        }
        // 拼接获取当前及其子资源
        List<String> resourceRightLikeList = Lists.newArrayList();
        resourceList.forEach(resource -> {
            resource = resource + StringPool.PERCENT;
            resourceRightLikeList.add(resource);
        });
        // 存在多条历史记录，更新最后一条
        List<Long> roleResourceIdList = roleResourceLinkMapper.selectRoleResourceIdByRoleCode(resourceRightLikeList);
        if (CollectionUtils.isEmpty(roleResourceIdList)) {
            return;
        }
        // 获取需要更新Hash的角色Entity
        List<RoleEntity> needUpdateHashRole = new LinkedList<>();
        List<TRoleResourceLinkDO> resourceLinkList = listRealDataByIds(roleResourceIdList);
        if (!CollectionUtils.isEmpty(resourceLinkList)) {
            Set<String> roleCodeSet = resourceLinkList.stream().map(TRoleResourceLinkDO::getRoleCode).collect(Collectors.toSet());
            roleCodeSet.forEach(roleCode -> {
                if (StringUtils.isBlank(roleCode)) {
                    return;
                }
                RoleEntity roleEntity = roleRepository.getRoleByCode(roleCode);
                if (roleEntity != null) {
                    needUpdateHashRole.add(roleEntity);
                }
            });
        }
        // 更新角色资源关联表
        roleResourceLinkMapper.updateRoleResourceStatus(roleResourceIdList, status);
        // 更新角色的Hash值
        needUpdateHashRole.forEach(RoleEntity::updateDataFullHash);
    }

    @Override
    public List<RoleResourceLinkEntity> getRoleResourceByRoleCode(String roleCode) {
        CheckUtils.notNull(roleCode, ManagementValidationError.PARAM_ERROR.toException());
        LambdaQueryWrapper<TRoleResourceLinkDO> queryWrapper = Wrappers
                .lambdaQuery(TRoleResourceLinkDO.class)
                .eq(TRoleResourceLinkDO::getRoleCode, roleCode);
        List<TRoleResourceLinkDO> roleResourceLinkDOList = super.list(queryWrapper);
        if (CollectionUtils.isEmpty(roleResourceLinkDOList)) {
            return Collections.emptyList();
        }
        return convertService.convert(roleResourceLinkDOList, RoleResourceLinkEntity.class);
    }

    @Override
    public void batchInsert(List<RoleResourceLinkEntity> needInsertList) {
        CheckUtils.notNull(needInsertList, ManagementValidationError.PARAM_ERROR.toException());
        if (needInsertList.isEmpty()) {
            return;
        }
        List<TRoleResourceLinkDO> linkDOList = needInsertList
                .stream()
                .map(v -> convertService.convert(v, TRoleResourceLinkDO.class))
                .collect(Collectors.toList());
        try {
            super.saveBatch(linkDOList);
        } catch (Exception e) {
            throw ManagementInternalError.DB_INSERT_ERROR.toException(e);
        }
    }

}
