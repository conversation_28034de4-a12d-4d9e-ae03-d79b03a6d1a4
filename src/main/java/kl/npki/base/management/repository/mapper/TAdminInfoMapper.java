package kl.npki.base.management.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import kl.nbase.db.support.mybatis.mapper.KlBaseMapper;
import kl.npki.base.management.repository.entity.TAdminInfoDO;
import kl.npki.management.core.biz.admin.model.dto.AdminInfoListDTO;
import kl.npki.management.core.biz.admin.model.info.AdminInfoDetailInfo;
import kl.npki.management.core.biz.admin.model.info.AdminInfoListInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/8/26
 * @desc
 */
public interface TAdminInfoMapper extends KlBaseMapper<TAdminInfoDO> {

    /**
     * 分页查询管理员信息
     *
     * @param page                     分页对象
     * @param adminInfoListQuery       通用查询参数
     * @param roleCodes                当前用户的角色可查询的角色集合
     * @param toBeCheckedUserStatusSet 待审核用户状态集合
     * @param toBeCheckedCertStatusSet 待审核证书状态集合
     * @return
     */
    Page<AdminInfoListInfo> queryForList(IPage<AdminInfoListInfo> page,
                                         @Param("adminInfoListQuery") AdminInfoListDTO adminInfoListQuery,
                                         @Param("roleCodes") Set<String> roleCodes,
                                         @Param("toBeCheckedUserStatusSet") Set<Integer> toBeCheckedUserStatusSet,
                                         @Param("toBeCheckedCertStatusSet") Set<Integer> toBeCheckedCertStatusSet);

    /**
     * 查询详细信息
     *
     * @param adminInfoId 管理员id
     * @return
     */
    AdminInfoDetailInfo queryForDetail(@Param("adminInfoId") Long adminInfoId,
                                       @Param("roleCodes") Set<String> roleCodes);

    /**
     * 根据角色统计已签发证书的管理员数量
     *
     * @param roleCode         角色编码
     * @param issuedCertStatus 证书状态集合
     * @param adminGroup       管理员组
     * @return
     */
    long countIssuedAdminByRole(@Param("roleCode") String roleCode, @Param("issuedCertStatus") int issuedCertStatus, @Param("adminGroup") String adminGroup);

    /**
     * 检测管理员是否已经被注销
     *
     * @param username username
     * @return 根据username查到的被删除的管理员数据量
     */
    int checkAdminIsCancelled(@Param("username") String username);
}
