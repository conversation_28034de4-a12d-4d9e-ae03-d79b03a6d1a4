package kl.npki.base.management.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.tools.dbtool.constant.DataType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-08-26 14:26:11
 * 管理员用户表
 */

@TableName("T_ADMIN_INFO")
@KlDbTable(tbName = "T_ADMIN_INFO", indexes = {@Index(name = "IDX_TAI_ROLE_CODE", columnList = {"ROLE_CODE"}),
    @Index(name = "IDX_TAI_USERNAME", columnList = "USERNAME")})
public class TAdminInfoDO implements Serializable {

    private static final long serialVersionUID = -7751563846187582565L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID, value = "ID")
    @KlDbField(type = DataType.BIGINT, size = 20, isPrimary = true, remarks = "主键")
    private Long id;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, remarks = "租户id")
    private String tenantId;

    /**
     * 人员标识
     */
    @TableField(value = "USERNAME")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, notNull = true, remarks = "人员标识")
    private String username;

    /**
     * 机构ID
     */
    @TableField(value = "ORG_ID")
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "机构ID")
    private Long orgId;

    /**
     * 密码摘要
     */
    @TableField(value = "PASSWORD_HASH")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128, remarks = "密码摘要")
    private String passwordHash;

    /**
     * 盐值
     */
    @TableField(value = "PASSWORD_SALT")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128, remarks = "盐值")
    private String passwordSalt;

    /**
     * 角色ID
     */
    @TableField(value = "ROLE_CODE")
    @DataFullField
    @KlDbField(type = DataType.BIGINT, notNull = true, remarks = "角色ID")
    private String roleCode;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @DataFullField
    @KlDbField(type = DataType.DATE, notNull = true, defaultValue = "CURRENT_TIMESTAMP", remarks = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @KlDbField(type = DataType.DATE, remarks = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 用户状态
     */
    @TableField(value = "STATUS")
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "-1", remarks = "用户状态")
    private Integer status;

    /**
     * 删除状态0：正常，1：已删除
     */
    @TableField(value = "IS_DELETE")
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "0", remarks = " 删除状态0：正常，1：已删除")
    private Integer isDelete;

    /**
     * 省
     */
    @TableField(value = "PROVINCE")
    @KlDbField(type = DataType.VARCHAR, size = 32, remarks = "省")
    private String province;

    /**
     * 市
     */
    @TableField(value = "CITY")
    @KlDbField(type = DataType.VARCHAR, size = 32, remarks = "市")
    private String city;

    /**
     * 组织
     */
    @TableField(value = "ORGANIZATION")
    @KlDbField(type = DataType.VARCHAR, size = 32, remarks = "组织")
    private String organization;

    /**
     * 机构
     */
    @TableField(value = "ORGANIZATION_UNIT")
    @KlDbField(type = DataType.VARCHAR, size = 32, remarks = "机构")
    private String organizationUnit;

    /**
     * 电子邮箱
     */
    @TableField(value = "EMAIL")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128, remarks = "电子邮箱")
    private String email;

    /**
     * 登录重试次数
     */
    @TableField(value = "LOGIN_RETRIES")
    @KlDbField(type = DataType.INTEGER, size = 10, notNull = true, defaultValue = "0", remarks = "登录重试次数")
    private Integer loginRetries;

    /**
     * 账号锁定时间
     */
    @TableField(value = "LOCK_TIME")
    @KlDbField(type = DataType.DATE, remarks = "账号锁定时间")
    private LocalDateTime lockTime;

    /**
     * 完整性值
     */
    @TableField(value = "FULL_DATA_HASH", fill = FieldFill.INSERT_UPDATE)
    @KlDbField(type = DataType.VARCHAR, size = 1024, remarks = "完整性值")
    private String fullDataHash;

    /**
     * 管理员组，分为初始化管理员，备份管理员
     */
    @KlDbField(type = DataType.VARCHAR, size = 128, remarks = "管理员组，分为初始化管理员，备份管理员")
    @TableField(value = "ADMIN_GROUP")
    private String adminGroup;

    /**
     * 扩展项1
     */
    @KlDbField(type = DataType.VARCHAR, size = 255, remarks = "扩展项1")
    @TableField(value = "EXT1")
    private String ext1;

    /**
     * 扩展项2
     */
    @KlDbField(type = DataType.VARCHAR, size = 255, remarks = "扩展项2")
    @TableField(value = "EXT2")
    private String ext2;

    /**
     * 扩展项3
     */
    @KlDbField(type = DataType.VARCHAR, size = 255, remarks = "扩展项3")
    @TableField(value = "EXT3")
    private String ext3;

    public Long getId() {
        return id;
    }

    public TAdminInfoDO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getFullDataHash() {
        return fullDataHash;
    }

    public void setFullDataHash(String fullDataHash) {
        this.fullDataHash = fullDataHash;
    }

    public String getUsername() {
        return username;
    }

    public TAdminInfoDO setUsername(String username) {
        this.username = username;
        return this;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public TAdminInfoDO setRoleCode(String roleCode) {
        this.roleCode = roleCode;
        return this;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public TAdminInfoDO setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
        return this;
    }

    public Long getOrgId() {
        return orgId;
    }

    public TAdminInfoDO setOrgId(Long orgId) {
        this.orgId = orgId;
        return this;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public TAdminInfoDO setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public TAdminInfoDO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public TAdminInfoDO setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public String getOrganizationUnit() {
        return organizationUnit;
    }

    public void setOrganizationUnit(String organizationUnit) {
        this.organizationUnit = organizationUnit;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getLoginRetries() {
        return loginRetries;
    }

    public void setLoginRetries(Integer loginRetries) {
        this.loginRetries = loginRetries;
    }

    public LocalDateTime getLockTime() {
        return lockTime;
    }

    public void setLockTime(LocalDateTime lockTime) {
        this.lockTime = lockTime;
    }

    public String getPasswordHash() {
        return passwordHash;
    }

    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }

    public String getPasswordSalt() {
        return passwordSalt;
    }

    public void setPasswordSalt(String passwordSalt) {
        this.passwordSalt = passwordSalt;
    }

    public String getAdminGroup() {
        return adminGroup;
    }

    public void setAdminGroup(String adminGroup) {
        this.adminGroup = adminGroup;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    @Override
    public String toString() {
        return "TAdminInfoDO{" +
            "id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", username='" + username + '\'' +
            ", passwordHash='" + passwordHash + '\'' +
            ", passwordSalt='" + passwordSalt + '\'' +
            ", roleCode='" + roleCode + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", status=" + status +
            ", isDelete=" + isDelete +
            ", province='" + province + '\'' +
            ", city='" + city + '\'' +
            ", organization='" + organization + '\'' +
            ", organizationUnit='" + organizationUnit + '\'' +
            ", email='" + email + '\'' +
            ", loginRetries=" + loginRetries +
            ", lockTime=" + lockTime +
            ", fullDataHash='" + fullDataHash + '\'' +
            ", adminGroup='" + adminGroup + '\'' +
            ", ext1='" + ext1 + '\'' +
            ", ext2='" + ext2 + '\'' +
            ", ext3='" + ext3 + '\'' +
            '}';
    }
}
