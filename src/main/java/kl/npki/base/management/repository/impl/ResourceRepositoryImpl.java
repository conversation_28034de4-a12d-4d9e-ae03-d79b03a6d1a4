package kl.npki.base.management.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.npki.base.core.configs.NpkiServerConfig;
import kl.npki.base.management.repository.entity.TResourceDO;
import kl.npki.base.management.repository.mapper.TResourceMapper;
import kl.npki.management.core.biz.admin.model.entity.ResourceEntity;
import kl.npki.management.core.constants.DeleteStatusEnum;
import kl.npki.management.core.constants.ResourceType;
import kl.npki.management.core.repository.IResourceRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/10 11:24
 * @desc 系统管理员证书仓库接口实现
 */
@Service
public class ResourceRepositoryImpl extends KlServiceImpl<TResourceMapper, TResourceDO> implements IResourceRepository {

    @Resource
    private ConvertService convertService;

    @Resource
    private TResourceMapper resourceMapper;

    @Resource
    private NpkiServerConfig npkiServerConfig;

    @Override
    public List<ResourceEntity> queryForList() {
        // 查询有效的资源
        LambdaQueryWrapper<TResourceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TResourceDO::getIsDelete, DeleteStatusEnum.NORMAL.getType());
        List<TResourceDO> resourceDOList = super.list(queryWrapper);
        return resourceDOList
            .stream()
            .map(v -> convertService.convert(v, ResourceEntity.class))
            .collect(Collectors.toList());
    }

    @Override
    public boolean batchDelete(Collection<ResourceEntity> resourceEntityList) {
        if (resourceEntityList == null || resourceEntityList.isEmpty()) {
            return true;
        }
        List<Long> idList = resourceEntityList.stream().map(ResourceEntity::getId).collect(Collectors.toList());
        return super.removeBatchByIds(idList);
    }

    @Override
    public void batchUpdateI18N() {
        super.updateBatchById(super.list());
    }

    @Override
    public void updateResourceStatus(List<String> resourceList, Integer status) {
        if (CollectionUtils.isEmpty(resourceList)) {
            return;
        }
        // 拼接获取当前及其子资源
        List<String> resourceRightLikeList = Lists.newArrayList();
        resourceList.forEach(resource -> {
            resource = resource + StringPool.PERCENT;
            resourceRightLikeList.add(resource);
        });
        // 更新资源状态，当前资源及其子资源
        resourceMapper.updateResourceStatus(resourceRightLikeList, status);
    }

    @Override
    public List<String> getAllowList() {
        // 查询匿名访问的API资源
        List<TResourceDO> resources = this.getBaseMapper().selectList(
            new LambdaQueryWrapper<TResourceDO>()
                .eq(TResourceDO::getResourceType, ResourceType.API.getType())
                .eq(TResourceDO::getIsAnonymous, 1)
        );

        if (resources.isEmpty()) {
            return Collections.emptyList();
        }

        return resources.stream()
            .map(TResourceDO::getResourceUrl)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .map(v -> npkiServerConfig.getRealApiPrefix() + v)
            .collect(Collectors.toList());
    }
}
