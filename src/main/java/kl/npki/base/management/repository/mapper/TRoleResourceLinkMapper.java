package kl.npki.base.management.repository.mapper;

import kl.nbase.db.support.mybatis.mapper.KlBaseMapper;
import kl.npki.base.management.repository.entity.TResourceDO;
import kl.npki.base.management.repository.entity.TRoleResourceLinkDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/26
 * @desc
 */
public interface TRoleResourceLinkMapper extends KlBaseMapper<TRoleResourceLinkDO> {
    /**
     * 查询角色拥有的资源
     *
     * @param roleCode        角色编码
     * @param resourceTypes 资源类型集合
     *
     * @return
     */
    List<TResourceDO> selectResourceByRole(@Param("roleCode") String roleCode, @Param("resourceType") List<String> resourceTypes);


    /**
     * 获取资源id集合
     *
     * @param roleCodeList 资源编码
     * @return List<Long>
     */
    List<Long> selectRoleResourceIdByRoleCode(@Param("roleCodeList") List<String> roleCodeList);

    /**
     * 更新角色资源状态
     *
     * @param roleResourceIdList 角色资源id集合
     * @param status             状态
     */
    void updateRoleResourceStatus(@Param("roleResourceIdList") List<Long> roleResourceIdList, @Param("status") Integer status);

}
