package kl.npki.base.management.repository.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import jodd.util.Base64;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.bean.validate.ValidateService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.core.biz.org.handler.OrgBatchExportProcessHandler;
import kl.npki.base.core.biz.org.handler.OrgBatchImportProcessHandler;
import kl.npki.base.core.biz.org.model.OrgBatchExportInfo;
import kl.npki.base.core.biz.org.model.OrgBatchImportInfo;
import kl.npki.base.core.biz.org.model.OrgEntity;
import kl.npki.base.core.biz.org.service.IOrgService;
import kl.npki.base.core.biz.upload.UploadFileFormatCheckOrgAddHandler;
import kl.npki.base.core.biz.upload.model.UploadFileEntity;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.base.core.constant.OrgConstants;
import kl.npki.base.management.constant.I18nExceptionInfoConstant;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.model.org.OrgDetailResponse;
import kl.npki.base.management.model.org.OrgListRequest;
import kl.npki.base.management.model.org.OrgListResponse;
import kl.npki.base.management.repository.service.IOrgManagementService;
import kl.npki.base.service.repository.OrgRepositoryImpl;
import kl.npki.base.service.repository.entity.OrgDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 机构列表响应
 *
 * <AUTHOR>
 * @create 2024/3/22 11:30
 */
@Service
public class OrgManagementServiceImpl implements IOrgManagementService {

    @Resource
    private OrgRepositoryImpl orgRepository;

    @Resource
    private IOrgService orgService;

    @Resource
    private ConvertService convertService;

    @Resource
    private ValidateService validateService;

    @Override
    public IPage<OrgListResponse> queryOrgPageList(PageInfo pageInfo, OrgListRequest orgListRequest) {
        IPage<OrgDO> page = pageInfo.toPage();
        QueryWrapper<OrgDO> queryWrapper = orgListRequest.toQueryWrapper();
        IPage<OrgDO> orgDoPage = orgRepository.page(page, queryWrapper);
        IPage<OrgEntity> orgEntityIPage = orgDoPage.convert(orgDO -> convertService.convert(orgDO, OrgEntity.class));
        return orgEntityIPage.convert(this::convertOrgListResponse);
    }

    @Override
    public IPage<OrgListResponse> queryAllOrg(PageInfo pageInfo) {
        IPage<OrgDO> page = pageInfo.toPage();
        LambdaQueryWrapper<OrgDO> wrapper = new LambdaQueryWrapper<>();
        // 只查询id和orgName字段
        wrapper.select(Arrays.asList(OrgDO::getId, OrgDO::getOrgName));
        IPage<OrgDO> orgDoPage = orgRepository.page(page, wrapper);
        IPage<OrgEntity> orgEntityIPage = orgDoPage.convert(orgDO -> convertService.convert(orgDO, OrgEntity.class));
        return orgEntityIPage.convert(this::convertOrgListResponse);
    }

    @Override
    public OrgListResponse queryRootOrg() {
        List<OrgEntity> orgEntities = orgRepository.searchSubOrgEntityByOrgCode(OrgConstants.ORG_ROOT_PARENT_CODE_FLAG, new PageInfo());
        OrgEntity rootOrgEntity = orgEntities
                .stream()
                .filter(orgEntity -> orgEntity.getOrgStatus().intValue() == EntityStatus.NORMAL.getId())
                .findAny()
                .orElseThrow(() -> ManagementInternalError.ORG_NOT_INIT_ERROR.toException(I18nExceptionInfoConstant.THE_ROOT_ORG_DOES_NOT_EXIST_I18N_KEY));
        return convertOrgListResponse(rootOrgEntity);
    }

    @Override
    public List<OrgListResponse> querySubOrg(String parentCode) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(100000);
        List<OrgEntity> orgEntities = orgRepository.searchSubOrgEntityByOrgCode(parentCode, pageInfo);
        List<OrgEntity> orgEntitiesFiltered = orgEntities.stream()
                .filter(orgEntity -> orgEntity.getOrgStatus().intValue() == EntityStatus.NORMAL.getId())
                .collect(Collectors.toList());
        return convert(orgEntitiesFiltered);
    }

    @Override
    public OrgDetailResponse getOrgDetailById(Long id) {
        OrgEntity orgEntity = orgRepository.searchById(id);
        CheckUtils.notNull(orgEntity, ManagementValidationError.ORG_NOT_FOUND.toException("id = " + id));
        return convertService.convert(orgEntity, OrgDetailResponse.class);
    }

    @Override
    public OrgDetailResponse getOrgDetailByCode(String code) {
        OrgEntity orgEntity = orgRepository.searchByOrgCode(code);
        CheckUtils.notNull(orgEntity, ManagementValidationError.ORG_NOT_FOUND.toException("orgCode = " + code));
        return convertService.convert(orgEntity, OrgDetailResponse.class);
    }

    @Override
    public File batchImport(UploadFileEntity fileEntity) {
        OrgBatchImportProcessHandler orgBatchProcessHandler = new OrgBatchImportProcessHandler(
                orgService,
                convertService,
                validateService,
                fileEntity);
        ByteArrayInputStream inputStream = new ByteArrayInputStream(Base64.decode(fileEntity.getRequestFile()));
        EasyExcelFactory.read(inputStream, OrgBatchImportInfo.class, orgBatchProcessHandler)
                .sheet()
                .doRead();
        return orgBatchProcessHandler.getResultFile();
    }

    @Override
    public void checkFileFormat(InputStream fileInputStream) {
        UploadFileFormatCheckOrgAddHandler uploadFileFormatCheckOrgAddHandler = new UploadFileFormatCheckOrgAddHandler();
        EasyExcelFactory.read(fileInputStream, uploadFileFormatCheckOrgAddHandler)
                .headRowNumber(OrgConstants.BATCH_IMPORT_ORG_HEAD_ROW_NUMBER)
                .sheet()
                .doRead();
    }

    @Override
    public File batchExport(OrgListRequest orgListRequest) {
        OrgBatchExportProcessHandler excelHandler = new OrgBatchExportProcessHandler();
        long pageSize = 500;
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(pageSize);
        do {
            // 查询数据
            IPage<OrgDO> page = pageInfo.toPage();
            QueryWrapper<OrgDO> queryWrapper = orgListRequest.toQueryWrapper();
            List<OrgBatchExportInfo> records = orgRepository
                    .page(page, queryWrapper)
                    .convert(this::convertDO2BatchInfo)
                    .getRecords();

            // 将查询到的机构数据写入Excel文件中
            excelHandler.write(records);

            // 检查数据是否查询完成
            if (records.size() < pageSize) {
                break;
            }

            // 查询下一页数据
            pageInfo.setCurrentPage(pageInfo.getCurrentPage() + 1);
        } while (true);

        return excelHandler.getResultFile();
    }

    private List<OrgListResponse> convert(List<OrgEntity> orgEntities) {
        return orgEntities.stream().map(this::convertOrgListResponse).collect(Collectors.toList());
    }

    private OrgListResponse convertOrgListResponse(OrgEntity orgEntity) {
        OrgListResponse orgListResponse = convertService.convert(orgEntity, OrgListResponse.class);
        OrgEntity parentOrgEntity = orgRepository.searchById(orgListResponse.getParentId());
        if (null != parentOrgEntity) {
            orgListResponse.setParentName(parentOrgEntity.getOrgName());
        }
        return orgListResponse;
    }

    private OrgBatchExportInfo convertDO2BatchInfo(OrgDO orgDO) {
        return convertService.convert(orgDO, OrgBatchExportInfo.class);
    }
}
