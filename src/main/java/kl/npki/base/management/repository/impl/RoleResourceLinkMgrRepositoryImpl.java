package kl.npki.base.management.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.npki.base.management.repository.entity.TRoleResourceLinkDO;
import kl.npki.base.management.repository.mapper.TRoleResourceLinkMapper;
import kl.npki.management.core.constants.DeleteStatusEnum;
import kl.npki.management.core.repository.IRoleResourceLinkMgrRepository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/31 15:50
 * @desc
 */
@Service
public class RoleResourceLinkMgrRepositoryImpl extends KlServiceImpl<TRoleResourceLinkMapper, TRoleResourceLinkDO> implements IRoleResourceLinkMgrRepository {
    @Override
    public List<String> queryResourceByRoles(String roleCodes) {
        List<String> result = new ArrayList<>();
        QueryWrapper<TRoleResourceLinkDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_code", roleCodes);
        queryWrapper.select("resource_code");
        List<TRoleResourceLinkDO> roleResourceLinkDoList = baseMapper.selectList(queryWrapper);
        roleResourceLinkDoList.forEach(roleResourceLinkDO->
            result.add(roleResourceLinkDO.getResourceCode())
        );
        return result;
    }

    @Override
    public Boolean isExistQueryByRoleAndResourceCode(String roleCode, String resourceCode) {
        LambdaQueryWrapper<TRoleResourceLinkDO> queryWrapper = Wrappers.lambdaQuery(TRoleResourceLinkDO.class);
        queryWrapper.select(TRoleResourceLinkDO::getId);
        queryWrapper.eq(TRoleResourceLinkDO::getRoleCode, roleCode);
        queryWrapper.eq(TRoleResourceLinkDO::getResourceCode, resourceCode);
        queryWrapper.eq(TRoleResourceLinkDO::getIsDelete, DeleteStatusEnum.NORMAL.getType());
        return count(queryWrapper) != 0;
    }

}
