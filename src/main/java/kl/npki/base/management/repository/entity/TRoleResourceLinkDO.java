package kl.npki.base.management.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.tools.dbtool.constant.DataType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-08-26 14:27:03
 * 角色资源关系表
 */

@TableName("T_ROLE_RESOURCE_LINK")
@KlDbTable(tbName = "T_ROLE_RESOURCE_LINK")
public class TRoleResourceLinkDO implements Serializable {

    private static final long serialVersionUID = 8387844144611975137L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID, value = "ID")
    @KlDbField(type = DataType.BIGINT, size = 20, isPrimary = true, remarks = "主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "TENANT_ID")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, remarks = "租户ID")
    private String tenantId;

    /**
     * 角色编码
     */
    @TableField(value = "ROLE_CODE")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, notNull = true, remarks = "角色编码")
    private String roleCode;

    /**
     * 资源ID
     */
    @TableField(value = "RESOURCE_CODE")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, notNull = true, remarks = "资源ID")
    private String resourceCode;

    /**
     * 是否包含所以子元素，0：否 ，1：是
     */
    @TableField(value = "INCLUDE_SUB_RESOURCE")
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, remarks = "是否包含所以子元素，0：否 ，1：是")
    private Integer includeSubResource;

    /**
     * 创建者id
     */
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "创建者id")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @KlDbField(type = DataType.DATE, notNull = true, defaultValue = "CURRENT_TIMESTAMP", remarks = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者id
     */
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "更新者id")
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @KlDbField(type = DataType.DATE, remarks = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除状态0：正常，1：已删除
     */
    @TableField(value = "IS_DELETE")
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "0", remarks = "删除状态0：正常，1：已删除")
    private Integer isDelete;

    /**
     * 完整性值
     */
    @TableField(value = "FULL_DATA_HASH", fill = FieldFill.INSERT)
    @KlDbField(type = DataType.VARCHAR, size = 1024, remarks = "完整性值")
    private String fullDataHash;

    public Long getId() {
        return id;
    }

    public TRoleResourceLinkDO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getFullDataHash() {
        return fullDataHash;
    }

    public void setFullDataHash(String fullDataHash) {
        this.fullDataHash = fullDataHash;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getResourceCode() {
        return resourceCode;
    }

    public void setResourceCode(String resourceCode) {
        this.resourceCode = resourceCode;
    }

    public Integer getIncludeSubResource() {
        return includeSubResource;
    }

    public TRoleResourceLinkDO setIncludeSubResource(Integer includeSubResource) {
        this.includeSubResource = includeSubResource;
        return this;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public TRoleResourceLinkDO setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
        return this;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public TRoleResourceLinkDO setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public TRoleResourceLinkDO setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    @Override
    public String toString() {
        return "TRoleResourceLinkDO{" +
            "id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", roleCode='" + roleCode + '\'' +
            ", resourceCode='" + resourceCode + '\'' +
            ", includeSubResource=" + includeSubResource +
            ", createBy=" + createBy +
            ", createTime=" + createTime +
            ", updateBy=" + updateBy +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            ", fullDataHash='" + fullDataHash + '\'' +
            '}';
    }
}
