package kl.npki.base.management.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.npki.base.service.repository.entity.IntegrityProtectedDO;
import kl.tools.dbtool.constant.DataType;


/**
 * <AUTHOR>
 * @date 2022-08-26 14:27:03
 * 角色资源关系表
 */

@TableName("T_ROLE_RESOURCE_LINK")
@KlDbTable(tbName = "T_ROLE_RESOURCE_LINK")
public class TRoleResourceLinkDO extends IntegrityProtectedDO {

    private static final long serialVersionUID = 8387844144611975137L;

    /**
     * 角色编码
     */
    @TableField(value = "ROLE_CODE")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, notNull = true, remarks = "角色编码")
    private String roleCode;

    /**
     * 资源ID
     */
    @TableField(value = "RESOURCE_CODE")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, notNull = true, remarks = "资源ID")
    private String resourceCode;

    /**
     * 是否包含所以子元素，0：否 ，1：是
     */
    @TableField(value = "INCLUDE_SUB_RESOURCE")
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, remarks = "是否包含所以子元素，0：否 ，1：是")
    private Integer includeSubResource;

    /**
     * 创建者id
     */
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "创建者id")
    private Long createBy;

    /**
     * 更新者id
     */
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "更新者id")
    private Long updateBy;

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getResourceCode() {
        return resourceCode;
    }

    public void setResourceCode(String resourceCode) {
        this.resourceCode = resourceCode;
    }

    public Integer getIncludeSubResource() {
        return includeSubResource;
    }

    public TRoleResourceLinkDO setIncludeSubResource(Integer includeSubResource) {
        this.includeSubResource = includeSubResource;
        return this;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    @Override
    public String toString() {
        return "TRoleResourceLinkDO{" +
            "roleCode='" + roleCode + '\'' +
            ", resourceCode='" + resourceCode + '\'' +
            ", includeSubResource=" + includeSubResource +
            ", createBy=" + createBy +
            ", updateBy=" + updateBy +
            ", fullDataHash='" + fullDataHash + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
