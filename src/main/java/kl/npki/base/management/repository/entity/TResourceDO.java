package kl.npki.base.management.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.nbase.i18n.i18n.I18nUtil;
import kl.npki.base.core.annotation.DataFullField;
import kl.tools.dbtool.constant.DataType;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-08-26 14:27:02
 * 应用功能资源表
 */

@TableName("T_RESOURCE")
@KlDbTable(tbName = "T_RESOURCE")
public class TResourceDO implements Serializable {

    private static final long serialVersionUID = -4898513775154947808L;
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID, value = "ID")
    @KlDbField(type = DataType.BIGINT, size = 20, isPrimary = true, remarks = "主键ID")
    private Long id;
    /**
     * 资源名称
     */
    @TableField(value = "RESOURCE_NAME")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 128, notNull = true, remarks = "资源名称")
    private String resourceName;

    /**
     * 资源名称国际化键
     */
    @TableField(value = "RESOURCE_NAME_I18N_KEY")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256, remarks = "资源名称国际化键")
    private String resourceNameI18nKey;

    /**
     * 资源编码
     */
    @TableField(value = "RESOURCE_CODE")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, remarks = "资源编码")
    private String resourceCode;

    /**
     * 备注
     */
    @TableField(value = "REMARK")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 255, remarks = "备注")
    private String remark;

    /**
     * 资源父节点Code
     */
    @TableField(value = "PARENT_RESOURCE_CODE")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, remarks = "资源父节点Code")
    private String parentResourceCode;

    /**
     * 资源类型：页面、菜单、按钮、API
     */
    @TableField(value = "RESOURCE_TYPE")
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, remarks = "资源类型：页面、菜单、按钮、API")
    private Integer resourceType;

    /**
     * 资源URL
     */
    @TableField(value = "RESOURCE_URL")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 500, remarks = "资源URL")
    private String resourceUrl;

    /**
     * 请求方法（API资源时使用）
     */
    @TableField(value = "REQUEST_METHOD")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 16, remarks = "请求方法")
    private String requestMethod;

    /**
     * 是否匿名访问（API资源时使用）
     */
    @TableField(value = "IS_ANONYMOUS")
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "0", remarks = "是否匿名访问")
    private Integer isAnonymous;

    /**
     * 排序号
     */
    @TableField(value = "SHOW_ORDER")
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 10, remarks = "排序号")
    private Integer showOrder;

    /**
     * 租户ID
     */
    @TableField(value = "TENANT_ID")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, remarks = "租户ID")
    private Long tenantId;

    /**
     * 路径
     */
    @TableField(value = "RESOURCE_PATH")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 512, remarks = "资源路径")
    private String resourcePath;

    /**
     * 父路径
     */
    @TableField(value = "PARENT_PATH")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 512, remarks = "父路径")
    private String parentPath;

    /**
     * 创建者id
     */
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "创建者id")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @DataFullField
    @KlDbField(type = DataType.DATE, notNull = true, defaultValue = "CURRENT_TIMESTAMP", remarks = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者id
     */
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "更新者id")
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @KlDbField(type = DataType.DATE, remarks = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除状态0：正常，1：已删除
     */
    @TableField(value = "IS_DELETE")
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "0", remarks = "删除状态0：正常，1：已删除")
    private Integer isDelete;

    /**
     * 完整性值
     */
    @TableField(value = "FULL_DATA_HASH", fill = FieldFill.INSERT)
    @KlDbField(type = DataType.VARCHAR, size = 1024, remarks = "完整性值")
    private String fullDataHash;

    public Long getId() {
        return id;
    }

    public TResourceDO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getFullDataHash() {
        return fullDataHash;
    }

    public void setFullDataHash(String fullDataHash) {
        this.fullDataHash = fullDataHash;
    }

    public String getResourceName() {
        if (StringUtils.isBlank(resourceNameI18nKey)) {
            return resourceName;
        }
        String i18NValue = I18nUtil.tr(resourceNameI18nKey);
        if (StringUtils.isNotBlank(i18NValue) && !i18NValue.equalsIgnoreCase(resourceNameI18nKey) && i18NValue.length() < 128) {
            return i18NValue;
        } else {
            return resourceName;
        }
    }

    public TResourceDO setResourceName(String resourceName) {
        this.resourceName = resourceName;
        return this;
    }

    public String getResourceNameI18nKey() {
        return resourceNameI18nKey;
    }

    public void setResourceNameI18nKey(String resourceNameI18nKey) {
        this.resourceNameI18nKey = resourceNameI18nKey;
    }

    public String getResourceCode() {
        return resourceCode;
    }

    public TResourceDO setResourceCode(String resourceCode) {
        this.resourceCode = resourceCode;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public TResourceDO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public String getParentResourceCode() {
        return parentResourceCode;
    }

    public TResourceDO setParentResourceCode(String parentResourceCode) {
        this.parentResourceCode = parentResourceCode;
        return this;
    }

    public Integer getResourceType() {
        return resourceType;
    }

    public TResourceDO setResourceType(Integer resourceType) {
        this.resourceType = resourceType;
        return this;
    }

    public String getResourceUrl() {
        return resourceUrl;
    }

    public TResourceDO setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl;
        return this;
    }

    public String getRequestMethod() {
        return requestMethod;
    }

    public TResourceDO setRequestMethod(String requestMethod) {
        this.requestMethod = requestMethod;
        return this;
    }

    public Integer getIsAnonymous() {
        return isAnonymous;
    }

    public TResourceDO setIsAnonymous(Integer isAnonymous) {
        this.isAnonymous = isAnonymous;
        return this;
    }

    public Integer getShowOrder() {
        return showOrder;
    }

    public TResourceDO setShowOrder(Integer showOrder) {
        this.showOrder = showOrder;
        return this;
    }


    public Long getTenantId() {
        return tenantId;
    }

    public TResourceDO setTenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public String getResourcePath() {
        return resourcePath;
    }

    public TResourceDO setResourcePath(String resourcePath) {
        this.resourcePath = resourcePath;
        return this;
    }

    public String getParentPath() {
        return parentPath;
    }

    public TResourceDO setParentPath(String parentPath) {
        this.parentPath = parentPath;
        return this;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public TResourceDO setCreateBy(Long createBy) {
        this.createBy = createBy;
        return this;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public TResourceDO setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
        return this;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public TResourceDO setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
        return this;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public TResourceDO setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public TResourceDO setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    @Override
    public String toString() {
        return "TResourceDO{" +
            "id=" + id +
            ", resourceName='" + resourceName + '\'' +
            ", resourceNameI18nKey='" + resourceNameI18nKey + '\'' +
            ", resourceCode='" + resourceCode + '\'' +
            ", remark='" + remark + '\'' +
            ", parentResourceCode='" + parentResourceCode + '\'' +
            ", resourceType=" + resourceType +
            ", resourceUrl='" + resourceUrl + '\'' +
            ", requestMethod='" + requestMethod + '\'' +
            ", isAnonymous=" + isAnonymous +
            ", showOrder=" + showOrder +
            ", tenantId=" + tenantId +
            ", resourcePath='" + resourcePath + '\'' +
            ", parentPath='" + parentPath + '\'' +
            ", createBy=" + createBy +
            ", createTime=" + createTime +
            ", updateBy=" + updateBy +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            ", fullDataHash='" + fullDataHash + '\'' +
            '}';
    }
}
