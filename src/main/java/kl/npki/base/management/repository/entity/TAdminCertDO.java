package kl.npki.base.management.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.tools.dbtool.constant.DataType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-08-26 14:23:56
 * 管理员用户表
 */

@TableName("T_ADMIN_CERT")
@KlDbTable(tbName = "T_ADMIN_CERT", indexes = {@Index(name = "IDX_TAC_CN", columnList = {"SUBJECT_CN"}),
    @Index(name = "IDX_TAC_SN", columnList = "SIGN_CERT_SN")})
public class TAdminCertDO implements Serializable {

    private static final long serialVersionUID = 1135154636171397378L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID, value = "ID")
    @KlDbField(type = DataType.BIGINT, size = 20, isPrimary = true, remarks = "主键")
    private Long id;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, remarks = "租户id")
    private String tenantId;

    /**
     * 管理员信息id
     */
    @TableField(value = "ADMIN_INFO_ID")
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "管理员信息id")
    private Long adminInfoId;

    /**
     * 签名证书序列号
     */
    @TableField(value = "SIGN_CERT_SN")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 50, remarks = "签名证书序列号")
    private String signCertSn;

    /**
     * 加密证书序列号
     */
    @TableField(value = "ENC_CERT_SN")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 50, remarks = "加密证书序列号")
    private String encCertSn;

    /**
     * 签名证书内容
     */
    @TableField(value = "SIGN_CERT_VALUE")
    @DataFullField
    @KlDbField(type = DataType.CLOB, remarks = "签名证书内容")
    private String signCertValue;

    /**
     * 加密证书内容
     */
    @TableField(value = "ENC_CERT_VALUE")
    @DataFullField
    @KlDbField(type = DataType.CLOB, remarks = "加密证书内容")
    private String encCertValue;

    /**
     * p10证书请求
     */
    @DataFullField
    @TableField(value = "P10_CERT_REQ")
    @KlDbField(type = DataType.CLOB, remarks = "p10证书请求")
    private String p10CertReq;

    /**
     * 是否导入标识
     */
    @TableField(value = "IS_IMPORTED")
    @DataFullField
    @KlDbField(name = "IS_IMPORTED", type = DataType.INTEGER, size = 1, defaultValue = "0", remarks = "是否导入标识")
    private Boolean imported;

    /**
     * 证书CN
     */
    @TableField(value = "SUBJECT_CN")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, remarks = "证书CN")
    private String subjectCn;

    /**
     * 签发者CN
     */
    @TableField(value = "ISSUER_CN")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, remarks = "签发者CN")
    private String issuerCn;

    /**
     * 证书DN
     */
    @TableField(value = "SUBJECT_DN")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 512, remarks = "证书DN")
    private String subjectDn;

    /**
     * 证书有效期-起始
     */
    @TableField(value = "VALID_START")
    @DataFullField
    @KlDbField(type = DataType.DATE, remarks = " 证书有效期-起始")
    private LocalDateTime validStart;

    /**
     * 证书有效期-结束
     */
    @TableField(value = "VALID_END")
    @DataFullField
    @KlDbField(type = DataType.DATE, remarks = " 证书有效期-结束")
    private LocalDateTime validEnd;

    /**
     * 创建者id
     */
    @TableField(value = "CREATE_BY", fill = FieldFill.INSERT)
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "创建者id")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @KlDbField(type = DataType.DATE, notNull = true, defaultValue = "CURRENT_TIMESTAMP", remarks = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者id
     */
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "更新者id")
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @KlDbField(type = DataType.DATE, remarks = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除状态0：正常，1：已删除
     */
    @TableField(value = "IS_DELETE")
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, defaultValue = "0", remarks = "删除状态0：正常，1：已删除")
    private Integer isDelete;

    /**
     * 证书状态
     */
    @TableField(value = "STATUS")
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, defaultValue = "0", remarks = "证书状态")
    private Integer status;

    /**
     * 完整性值
     */
    @TableField(value = "FULL_DATA_HASH", fill = FieldFill.INSERT_UPDATE)
    @KlDbField(type = DataType.VARCHAR, size = 1024, remarks = "完整性值")
    private String fullDataHash;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Long getAdminInfoId() {
        return adminInfoId;
    }

    public TAdminCertDO setAdminInfoId(Long adminInfoId) {
        this.adminInfoId = adminInfoId;
        return this;
    }

    public String getSignCertSn() {
        return signCertSn;
    }

    public TAdminCertDO setSignCertSn(String signCertSn) {
        this.signCertSn = signCertSn;
        return this;
    }

    public String getEncCertSn() {
        return encCertSn;
    }

    public TAdminCertDO setEncCertSn(String encCertSn) {
        this.encCertSn = encCertSn;
        return this;
    }

    public Long getId() {
        return id;
    }

    public TAdminCertDO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getSubjectCn() {
        return subjectCn;
    }

    public TAdminCertDO setSubjectCn(String subjectCn) {
        this.subjectCn = subjectCn;
        return this;
    }

    public String getIssuerCn() {
        return issuerCn;
    }

    public TAdminCertDO setIssuerCn(String issuerCn) {
        this.issuerCn = issuerCn;
        return this;
    }

    public String getSubjectDn() {
        return subjectDn;
    }

    public TAdminCertDO setSubjectDn(String subjectDn) {
        this.subjectDn = subjectDn;
        return this;
    }

    public LocalDateTime getValidStart() {
        return validStart;
    }

    public TAdminCertDO setValidStart(LocalDateTime validStart) {
        this.validStart = validStart;
        return this;
    }

    public LocalDateTime getValidEnd() {
        return validEnd;
    }

    public TAdminCertDO setValidEnd(LocalDateTime validEnd) {
        this.validEnd = validEnd;
        return this;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public TAdminCertDO setCreateBy(Long createBy) {
        this.createBy = createBy;
        return this;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public TAdminCertDO setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
        return this;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public TAdminCertDO setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
        return this;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public TAdminCertDO setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public TAdminCertDO setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public TAdminCertDO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public String getSignCertValue() {
        return signCertValue;
    }

    public void setSignCertValue(String signCertValue) {
        this.signCertValue = signCertValue;
    }

    public String getEncCertValue() {
        return encCertValue;
    }

    public void setEncCertValue(String encCertValue) {
        this.encCertValue = encCertValue;
    }

    public String getFullDataHash() {
        return fullDataHash;
    }

    public void setFullDataHash(String fullDataHash) {
        this.fullDataHash = fullDataHash;
    }

    public String getP10CertReq() {
        return p10CertReq;
    }

    public void setP10CertReq(String p10CertReq) {
        this.p10CertReq = p10CertReq;
    }

    public Boolean getImported() {
        return imported;
    }

    public void setImported(Boolean imported) {
        this.imported = imported;
    }

    @Override
    public String toString() {
        return "TAdminCertDO{" +
            "id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", adminInfoId=" + adminInfoId +
            ", signCertSn='" + signCertSn + '\'' +
            ", encCertSn='" + encCertSn + '\'' +
            ", signCertValue='" + signCertValue + '\'' +
            ", encCertValue='" + encCertValue + '\'' +
            ", p10CertReq='" + p10CertReq + '\'' +
            ", imported=" + imported +
            ", subjectCn='" + subjectCn + '\'' +
            ", issuerCn='" + issuerCn + '\'' +
            ", subjectDn='" + subjectDn + '\'' +
            ", validStart=" + validStart +
            ", validEnd=" + validEnd +
            ", createBy=" + createBy +
            ", createTime=" + createTime +
            ", updateBy=" + updateBy +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            ", status=" + status +
            ", fullDataHash='" + fullDataHash + '\'' +
            '}';
    }
}
