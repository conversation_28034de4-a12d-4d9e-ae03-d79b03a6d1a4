package kl.npki.base.management.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.npki.base.service.repository.entity.IntegrityProtectedDO;
import kl.tools.dbtool.constant.DataType;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-08-26 14:23:56
 * 管理员用户表
 */

@TableName("t_admin_cert")
@KlDbTable(tbName = "t_admin_cert", indexes = {@Index(name = "idx_tac_cn", columnList = {"subject_cn"}),
    @Index(name = "idx_tac_sn", columnList = "sign_cert_sn")})
public class TAdminCertDO extends IntegrityProtectedDO {

    private static final long serialVersionUID = 1135154636171397378L;

    /**
     * 管理员信息id
     */
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "管理员信息id")
    private Long adminInfoId;

    /**
     * 签名证书序列号
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 50, remarks = "签名证书序列号")
    private String signCertSn;

    /**
     * 加密证书序列号
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 50, remarks = "加密证书序列号")
    private String encCertSn;

    /**
     * 签名证书内容
     */
    @DataFullField
    @KlDbField(type = DataType.CLOB, remarks = "签名证书内容")
    private String signCertValue;

    /**
     * 加密证书内容
     */
    @DataFullField
    @KlDbField(type = DataType.CLOB, remarks = "加密证书内容")
    private String encCertValue;

    /**
     * p10证书请求
     */
    @DataFullField
    @KlDbField(type = DataType.CLOB, remarks = "p10证书请求")
    private String p10CertReq;

    /**
     * 是否导入标识
     */
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, defaultValue = "0", remarks = "是否导入标识")
    private Boolean imported;

    /**
     * 证书CN
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, remarks = "证书CN")
    private String subjectCn;

    /**
     * 签发者CN
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64, remarks = "签发者CN")
    private String issuerCn;

    /**
     * 证书DN
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 512, remarks = "证书DN")
    private String subjectDn;

    /**
     * 证书有效期-起始
     */
    @DataFullField
    @KlDbField(type = DataType.DATE, remarks = " 证书有效期-起始", size = 6)
    private LocalDateTime validStart;

    /**
     * 证书有效期-结束
     */
    @DataFullField
    @KlDbField(type = DataType.DATE, remarks = " 证书有效期-结束", size = 6)
    private LocalDateTime validEnd;

    /**
     * 创建者id
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "创建者id")
    private Long createBy;

    /**
     * 更新者id
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "更新者id")
    private Long updateBy;

    /**
     * 证书状态
     */
    @TableField(value = "status")
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, defaultValue = "0", remarks = "证书状态")
    private Integer status;

    public Long getAdminInfoId() {
        return adminInfoId;
    }

    public TAdminCertDO setAdminInfoId(Long adminInfoId) {
        this.adminInfoId = adminInfoId;
        return this;
    }

    public String getSignCertSn() {
        return signCertSn;
    }

    public TAdminCertDO setSignCertSn(String signCertSn) {
        this.signCertSn = signCertSn;
        return this;
    }

    public String getEncCertSn() {
        return encCertSn;
    }

    public TAdminCertDO setEncCertSn(String encCertSn) {
        this.encCertSn = encCertSn;
        return this;
    }

    public String getSubjectCn() {
        return subjectCn;
    }

    public TAdminCertDO setSubjectCn(String subjectCn) {
        this.subjectCn = subjectCn;
        return this;
    }

    public String getIssuerCn() {
        return issuerCn;
    }

    public TAdminCertDO setIssuerCn(String issuerCn) {
        this.issuerCn = issuerCn;
        return this;
    }

    public String getSubjectDn() {
        return subjectDn;
    }

    public TAdminCertDO setSubjectDn(String subjectDn) {
        this.subjectDn = subjectDn;
        return this;
    }

    public LocalDateTime getValidStart() {
        return validStart;
    }

    public TAdminCertDO setValidStart(LocalDateTime validStart) {
        this.validStart = validStart;
        return this;
    }

    public LocalDateTime getValidEnd() {
        return validEnd;
    }

    public TAdminCertDO setValidEnd(LocalDateTime validEnd) {
        this.validEnd = validEnd;
        return this;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public TAdminCertDO setCreateBy(Long createBy) {
        this.createBy = createBy;
        return this;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public TAdminCertDO setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public TAdminCertDO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public String getSignCertValue() {
        return signCertValue;
    }

    public void setSignCertValue(String signCertValue) {
        this.signCertValue = signCertValue;
    }

    public String getEncCertValue() {
        return encCertValue;
    }

    public void setEncCertValue(String encCertValue) {
        this.encCertValue = encCertValue;
    }

    public String getP10CertReq() {
        return p10CertReq;
    }

    public void setP10CertReq(String p10CertReq) {
        this.p10CertReq = p10CertReq;
    }

    public Boolean getImported() {
        return imported;
    }

    public void setImported(Boolean imported) {
        this.imported = imported;
    }

    @Override
    public String toString() {
        return "TAdminCertDO{" +
            "adminInfoId=" + adminInfoId +
            ", signCertSn='" + signCertSn + '\'' +
            ", encCertSn='" + encCertSn + '\'' +
            ", signCertValue='" + signCertValue + '\'' +
            ", encCertValue='" + encCertValue + '\'' +
            ", p10CertReq='" + p10CertReq + '\'' +
            ", imported=" + imported +
            ", subjectCn='" + subjectCn + '\'' +
            ", issuerCn='" + issuerCn + '\'' +
            ", subjectDn='" + subjectDn + '\'' +
            ", validStart=" + validStart +
            ", validEnd=" + validEnd +
            ", createBy=" + createBy +
            ", updateBy=" + updateBy +
            ", status=" + status +
            ", fullDataHash='" + fullDataHash + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
