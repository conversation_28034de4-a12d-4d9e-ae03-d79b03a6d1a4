package kl.npki.base.management.repository.mapper;

import kl.nbase.db.support.mybatis.mapper.KlBaseMapper;
import kl.npki.base.core.biz.security.model.UrlRoleInfo;
import kl.npki.base.management.repository.entity.TRoleDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/26
 * @desc
 */
public interface TRoleMapper extends KlBaseMapper<TRoleDO> {

    /**
     * 根据资源编码查询拥有该资源的角色
     *
     * @param resourceCode
     * @return
     */
    List<TRoleDO> selectRoleByResourceCode(@Param("resourceCode") String resourceCode);


    /**
     * 查询路由角色列表
     *
     * @return 角色标签
     */
    List<UrlRoleInfo> listUrlRoles();
}
