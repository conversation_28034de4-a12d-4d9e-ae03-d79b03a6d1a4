package kl.npki.base.management.repository.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.npki.base.core.biz.upload.model.UploadFileEntity;
import kl.npki.base.management.model.org.OrgDetailResponse;
import kl.npki.base.management.model.org.OrgListRequest;
import kl.npki.base.management.model.org.OrgListResponse;

import java.io.File;
import java.io.InputStream;
import java.util.List;

/**
 * 机构管理服务
 *
 * <AUTHOR>
 * @create 2024/3/22 10:58
 */
public interface IOrgManagementService {

    /**
     * 查询机构列表
     *
     * @param pageInfo       分页查询条件
     * @param orgListRequest 查询条件
     * @return
     */
    IPage<OrgListResponse> queryOrgPageList(PageInfo pageInfo, OrgListRequest orgListRequest);

    /**
     * 获取该租户的所有的机构信息
     *
     * @param pageInfo
     * @return
     */
    IPage<OrgListResponse> queryAllOrg(PageInfo pageInfo);

    /**
     * 查询根机构
     *
     * @return 根机构
     */
    OrgListResponse queryRootOrg();

    /**
     * 查询根机构
     *
     * @return 根机构
     */
    List<OrgListResponse> querySubOrg(String parentCode);

    /**
     * 查询机构详情
     *
     * @param id 机构id
     * @return
     */
    OrgDetailResponse getOrgDetailById(Long id);

    /**
     * 查询机构详情
     *
     * @param code 机构code
     * @return
     */
    OrgDetailResponse getOrgDetailByCode(String code);

    /**
     * 批量导入机构
     *
     * @param fileEntity 批量导入的Excel文件实体
     * @return 批量导入处理结果Excel文件
     */
    File batchImport(UploadFileEntity fileEntity);

    /**
     * 检查文件头格式是否满足当前业务要求
     *
     * @param fileInputStream 批量导入的Excel文件流
     */
    void checkFileFormat(InputStream fileInputStream);

    /**
     * 批量导出机构
     *
     * @param orgListRequest 批量导出查询条件
     * @return 批量导出Excel文件
     */
    File batchExport(OrgListRequest orgListRequest);

}
