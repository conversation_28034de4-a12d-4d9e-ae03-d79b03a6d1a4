package kl.npki.base.management.repository.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.npki.base.core.biz.upload.model.UploadFileAddInfo;
import kl.npki.base.core.biz.upload.model.UploadFileEntity;
import kl.npki.base.management.model.upload.UploadFileListResponse;


/**
 * 机构管理服务
 *
 * <AUTHOR>
 * @create 2024/3/22 10:58
 */
public interface IUploadFileManagementService {

    /**
     * 查询机构列表
     *
     * @param pageInfo       分页查询条件
     * @param orgListRequest 查询条件
     * @return
     */
    IPage<UploadFileListResponse> queryUploadFileList(PageInfo pageInfo, String orgListRequest);

    /**
     * 根据ID查询实体
     * @param id
     * @return
     */
    UploadFileEntity searchById(Long id);

    /**
     * 删除上传文件
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 上传文件
     * @param uploadFileAddInfo
     * @return
     */
    Long uploadFile(UploadFileAddInfo uploadFileAddInfo);

    /**
     * 查询当前接口是否存在未完成的文件上传操作
     * @param interfaceName
     * @return
     */
    UploadFileEntity searchExistenceUnfinishedFile(String interfaceName);
}
