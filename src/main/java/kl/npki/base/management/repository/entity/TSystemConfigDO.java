package kl.npki.base.management.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.service.repository.entity.BaseDO;
import kl.tools.dbtool.constant.DataType;


/**
 * 系统配置表实体类
 *
 * <AUTHOR>
 * @date 2022/8/26 17:09
 * 暂时未使用
 */
@TableName("T_SYSTEM_CONFIG")
@KlDbTable(tbName = "T_SYSTEM_CONFIG")
public class TSystemConfigDO extends BaseDO {
    private static final long serialVersionUID = -6775032733083140277L;

    /**
     * 配置名称
     */
    @TableField("CONFIG_NAME")
    @KlDbField(type = DataType.VARCHAR, size = 64, notNull = true, remarks = " 配置名称")
    private String configName;

    /**
     * 配置序号
     */
    @TableField("CONFIG_ORDER")
    @KlDbField(type = DataType.INTEGER, size = 10, notNull = true, remarks = " 配置序号")
    private Integer configOrder;

    /**
     * 配置文件名称
     */
    @TableField("CONFIG_FILE_NAME")
    @KlDbField(type = DataType.VARCHAR, size = 64, notNull = true, remarks = " 配置文件名称")
    private String configFileName;

    /**
     * 配置文件内容
     */
    @TableField("CONFIG_FILE_VALUE")
    @KlDbField(type = DataType.CLOB, remarks = " 配置文件内容")
    private String configFileValue;

    /**
     * 配置类名
     */
    @TableField("CONFIG_CLASS")
    @KlDbField(type = DataType.VARCHAR, size = 255, notNull = true, remarks = " 配置类名")
    private String configClass;

    /**
     * 部署状态0:未完成,1:已完成
     */
    @TableField("IS_COMPLETE")
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "0", remarks = " 部署状态0:未完成,1:已完成")
    private Integer isComplete;

    /**
     * 更新者id
     */
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = " 更新者id")
    private Long updateBy;

    public String getConfigName() {
        return configName;
    }

    public TSystemConfigDO setConfigName(String configName) {
        this.configName = configName;
        return this;
    }

    public Integer getConfigOrder() {
        return configOrder;
    }

    public TSystemConfigDO setConfigOrder(Integer configOrder) {
        this.configOrder = configOrder;
        return this;
    }

    public String getConfigFileName() {
        return configFileName;
    }

    public TSystemConfigDO setConfigFileName(String configFileName) {
        this.configFileName = configFileName;
        return this;
    }

    public String getConfigFileValue() {
        return configFileValue;
    }

    public TSystemConfigDO setConfigFileValue(String configFileValue) {
        this.configFileValue = configFileValue;
        return this;
    }

    public String getConfigClass() {
        return configClass;
    }

    public TSystemConfigDO setConfigClass(String configClass) {
        this.configClass = configClass;
        return this;
    }

    public Integer getIsComplete() {
        return isComplete;
    }

    public TSystemConfigDO setIsComplete(Integer isComplete) {
        this.isComplete = isComplete;
        return this;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public TSystemConfigDO setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
        return this;
    }
}
