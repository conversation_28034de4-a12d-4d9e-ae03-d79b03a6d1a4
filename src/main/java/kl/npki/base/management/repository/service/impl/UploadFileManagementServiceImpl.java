package kl.npki.base.management.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.npki.base.core.biz.upload.model.UploadFileAddInfo;
import kl.npki.base.core.biz.upload.model.UploadFileEntity;
import kl.npki.base.core.constant.UploadFileProcessStatusEnum;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.model.upload.UploadFileListResponse;
import kl.npki.base.management.repository.service.IUploadFileManagementService;
import kl.npki.base.service.repository.UploadFileRepositoryImpl;
import kl.npki.base.service.repository.entity.UploadFileDO;
import kl.npki.management.core.utils.ConvertServiceHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 机构列表响应
 *
 * <AUTHOR>
 * @create 2024/3/22 11:30
 */
@Service
public class UploadFileManagementServiceImpl implements IUploadFileManagementService {

    @Resource
    private UploadFileRepositoryImpl uploadFileRepository;

    @Resource
    private ConvertService convertService;

    @Override
    public IPage<UploadFileListResponse> queryUploadFileList(PageInfo pageInfo, String orgListRequest) {
        IPage<UploadFileDO> page = pageInfo.toPage();
        LambdaQueryWrapper<UploadFileDO> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(Boolean.TRUE, UploadFileDO::getInterfaceName, orgListRequest);
        // 以文件添加时间倒序排列
        lambdaQuery.orderByDesc(UploadFileDO::getCreateTime);
        IPage<UploadFileDO> uploadFileDoPage = uploadFileRepository.page(page, lambdaQuery);
        return uploadFileDoPage.convert(this::convertOrgListResponse);
    }

    /**
     * 根据ID查询实体
     *
     * @param id
     * @return
     */
    @Override
    public UploadFileEntity searchById(Long id) {
        UploadFileDO uploadFileDO = uploadFileRepository.getById(id);
        if (uploadFileDO == null) {
            throw ManagementValidationError.UPLOAD_FILE_NOT_FOUND_ERROR.toException("id=" + id);
        }
        return convertService.convert(uploadFileDO, UploadFileEntity.class);
    }

    /**
     * 删除上传文件
     *
     * @param id
     * @return
     */
    @Override
    public Boolean delete(Long id) {
        return uploadFileRepository.removeById(id);
    }

    /**
     * 上传文件
     *
     * @param uploadFileAddInfo
     * @return
     */
    @Override
    public Long uploadFile(UploadFileAddInfo uploadFileAddInfo) {
        UploadFileEntity uploadFileEntity = ConvertServiceHelper
                .getConvertService()
                .convert(uploadFileAddInfo, UploadFileEntity.class);
        return uploadFileEntity.save();

    }

    /**
     * 查询当前接口是否存在未完成的文件上传操作
     *
     * @param interfaceName
     * @return
     */
    @Override
    public UploadFileEntity searchExistenceUnfinishedFile(String interfaceName) {
        return uploadFileRepository.searchByInterfaceNameProcessComplete(
                interfaceName,
                UploadFileProcessStatusEnum.UPLOAD,
                UploadFileProcessStatusEnum.PROCESSING);
    }

    private UploadFileListResponse convertOrgListResponse(UploadFileDO uploadFileDO) {
        return convertService.convert(uploadFileDO, UploadFileListResponse.class);
    }
}
