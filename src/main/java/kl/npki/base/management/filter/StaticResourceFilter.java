package kl.npki.base.management.filter;

import kl.nbase.auth.utils.AntPathMatcher;
import org.springframework.boot.autoconfigure.h2.H2ConsoleProperties;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;
import org.springframework.web.servlet.HandlerExecutionChain;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;
import org.springframework.web.util.ServletRequestPathUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 静态资源访问控制过滤器，为了安全考虑，只允许访问指定静态资源
 *
 * <AUTHOR>
 * @create 2025/6/3 下午6:46
 */
@Component
@DependsOn({ "springBeanRegister" })
public class StaticResourceFilter implements Filter {

    /**
     * 默认静态资源白名单，所有以这些路径开头的静态资源请求都将被放行，不会被拦截。
     * 由于目录结构不允许修改，所以此处对允许访问的静态资源进行硬编码
     * 例如：
     * <li>/pkiclient：PKI 安全中间件资源目录。</li>
     * <li>/img：图片资源目录（如 .png 格式）。</li>
     * <li>/npki：CSS、JS、字体等前端资源目录。</li>
     * <li>/favicon.ico：浏览器图标（.ico 格式）。</li>
     * <li>/favicon.png：浏览器图标的 PNG 格式。</li>
     * <li>/favicon_small.png：小尺寸的浏览器图标。</li>
     * <li>/index.html：系统首页。</li>
     * <li>/loading.gif：加载动画图标。</li>
     * <li>/swagger-ui.html、/doc.html： API 文档页面。</li>
     * <li>/webjars：通过 WebJars 引入的静态资源（JS/CSS等）。</li>
     */
    private static final List<String> DEFAULT_STATIC_RESOURCES = Arrays.asList(
            "/pkiclient/**", "/img/**/*.png", "/npki/**/*.css", "/npki/**/*.woff",
            "/npki/**/*.ttf", "/npki/**/*.eot", "/npki/**/*.svg", "/npki/**/*.png",
            "/npki/**/*.js", "/favicon.ico", "/favicon.png", "/favicon_small.png",
            "/index.html", "/loading.gif", "/swagger-ui/**", "/swagger-ui.html",
            "/","/doc.html", "/v3/api-docs/**","/webjars/**",  "/fail", "/sso_fail");

    /**
     * 路径匹配器
     */
    private static final AntPathMatcher ANT_PATH_MATCHER = new AntPathMatcher();
    /**
     * 静态资源白名单
     */
    private final List<String> staticResourceWhitelist = new ArrayList<>(DEFAULT_STATIC_RESOURCES);
    /**
     * 请求处理器，可以根据处理器类型判断当前请求资源类型
     */
    private List<HandlerMapping> handlerMappings;

    @Override
    public void init(FilterConfig filterConfig) {
        ServletContext servletContext = filterConfig.getServletContext();
        WebApplicationContext applicationContext = WebApplicationContextUtils
                .getRequiredWebApplicationContext(servletContext);

        // 获取所有 HandlerMapping 实例
        this.handlerMappings = new ArrayList<>(applicationContext.getBeansOfType(HandlerMapping.class).values());

        // 添加 H2 控制台路径
        applicationContext.getBeansOfType(H2ConsoleProperties.class).values().stream()
                .findFirst()
                .map(H2ConsoleProperties::getPath)
                .ifPresent(path -> staticResourceWhitelist.add(path + "/**"));
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse resp = (HttpServletResponse) response;

        String uri = req.getRequestURI();
        String contextPath = req.getContextPath();
        String path = uri.substring(contextPath.length());

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        if (accessNotAllowed(httpRequest, path)) {
            resp.sendError(HttpServletResponse.SC_NOT_FOUND, "Page Not Found");
            return;
        }
        chain.doFilter(request, response);
    }

    /**
     * 判断当前请求是否禁止访问
     *
     * @param httpRequest 请求对象
     * @param path        请求路径
     * @return 如果不允许访问，返回 true；否则返回 false
     */
    private boolean accessNotAllowed(HttpServletRequest httpRequest, String path) {
        if (isStaticResource(httpRequest)) {
            boolean inWhitelist = staticResourceWhitelist.stream()
                    .anyMatch(pattern -> ANT_PATH_MATCHER.match(pattern, path));
            // 静态资源，且资源路径不在白名单中，禁止访问
            return !inWhitelist;
        }

        // 非静态资源，允许访问
        return false;
    }

    /**
     * 检查给定的请求URI是否应该被当作静态资源处理
     * 供JwtAuthenticationTokenFilter等其他组件复用静态资源过滤逻辑
     *
     * @param httpRequest 请求对象
     * @param requestUri  请求URI
     * @return 如果应该被当作静态资源处理，返回 true；否则返回 false
     */
    public boolean shouldTreatAsStaticResource(HttpServletRequest httpRequest, String requestUri) {
        if (isStaticResource(httpRequest)) {
            String contextPath = httpRequest.getContextPath();
            String path = requestUri.substring(contextPath.length());

            // 检查是否在静态资源白名单中
            boolean inWhitelist = staticResourceWhitelist.stream()
                    .anyMatch(pattern -> ANT_PATH_MATCHER.match(pattern, path));
            return inWhitelist;
        }
        return false;
    }

    /**
     * 判断当前请求是否为静态资源访问
     *
     * @param httpRequest 请求对象
     * @return 如果是静态资源请求，返回 true；否则返回 false
     */
    public boolean isStaticResource(HttpServletRequest httpRequest) {
        ServletRequestPathUtils.parseAndCache(httpRequest);
        for (HandlerMapping handlerMapping : handlerMappings) {
            try {
                HandlerExecutionChain executionChain = handlerMapping.getHandler(httpRequest);
                if (null == executionChain) {
                    continue;
                }
                return executionChain.getHandler() instanceof ResourceHttpRequestHandler;
            } catch (Exception ignored) {
                // 忽略异常，尝试下一个 handlerMapping
            }
        }

        return false;
    }
    /**
     * 获取静态资源白名单模式数组
     * 供SecurityConfig等其他组件复用静态资源模式配置
     *
     * @return 静态资源模式数组
     */
    public String[] getStaticResourcePatterns() {
        return staticResourceWhitelist.toArray(new String[0]);
    }
}