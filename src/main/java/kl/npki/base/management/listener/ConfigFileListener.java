package kl.npki.base.management.listener;

import kl.nbase.config.event.ConfigFileEventManager;
import kl.nbase.config.event.ConfigFileRefreshEvent;
import kl.nbase.config.listener.ConfigFileRefreshEventListener;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.management.core.service.config.ProtectConfigFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Component
public class ConfigFileListener implements ConfigFileRefreshEventListener {

    private static final Logger log = LoggerFactory.getLogger(ConfigFileListener.class);


    private final ProtectConfigFileService protectConfigFileService = new ProtectConfigFileService();

    @Override
    public void onChange(ConfigFileRefreshEvent event) {
        String source = (String) event.getSource();

        ConfigFileRefreshEvent.RefreshState state = event.getState();
        switch (state) {
            case BEFORE:
                doCheck(source);
                break;
            case AFTER:
                doUpdate(source);
                break;
            default:
                break;
        }
    }

    private void doCheck(String source) {

        // 部署阶段或未开启完整性保护功能 不生成日志的完整性值
        if (ignoreCheckOrUpdate()) {
            return;
        }

        boolean result = protectConfigFileService.checkDataFull(source);
        if (!result) {
            throw ManagementValidationError.CONFIG_FILE_VERIFY_HMAC_FAILED.toException();
        }
    }

    private void doUpdate(String source) {
        if (ignoreCheckOrUpdate()) {
            return;
        }
        // 更新完整性值
        try {
            log.info("Start updating the integrity value of configuration file [{}]", source);
            protectConfigFileService.updateDataFull(source);
        } catch (Exception e) {
            log.error("Configuration file {}, integrity value update failed", source, e);
        }
    }

    /**
     * 是否忽略验证或更新完整性值
     * @return
     */
    private boolean ignoreCheckOrUpdate(){
        return !BaseConfigWrapper.getSysConfig().isOpenDataFull() || !SystemUtil.isDeployed();
    }

    @PostConstruct
    public void init() {
        // 将监听事件注册到配置文件更新事件管理器中
        ConfigFileEventManager.getInstance().addListener(this);
    }
}
