package kl.npki.base.management.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.biz.check.SelfCheckManager;
import kl.npki.base.core.biz.license.model.LicenseInfo;
import kl.npki.base.core.biz.license.service.ILicenseService;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.base.management.model.system.response.LicenseInfoResponse;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.management.core.biz.check.LicenseValidityPeriodCheckItem;
import kl.npki.management.core.utils.FileSizeHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

import static kl.npki.base.management.constant.I18nExceptionInfoConstant.*;
import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * <AUTHOR>
 * @since 2023/1/30
 */
@RestController
@RequestMapping("/license")
@Tag(name = "License管理")
@LogCollector(resolver = OperationLogResolver.class)
public class LicenseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LicenseController.class);

    @Resource
    private ILicenseService iLicenseService;

    @Resource
    private LicenseValidityPeriodCheckItem licenseValidityPeriodCheckItem;

    @Resource
    private ConvertService convertService;

    /**
     * License最大文件上限1M（单位字节)
     */
    private static final long MAX_FILE_SIZE_BYTE = 1024 * 1024L;

    private static final String LICENSE_EXTENSION = ".dat";

    /**
     * 导入License
     *
     * @return 是否导入成功
     */
    @PostMapping("/import")
    @Operation(description = IMPORT_LICENSE_I18N_KEY, summary = "导入License")
    public RestResponse<Boolean> importLicense(@RequestParam("file") MultipartFile file) {
        // 授权文件大小和格式校验
        validateImportedLicenseFile(file);
        // 从文件读取授权信息
        String licenseStr = readImportedLicenseFile(file);
        // 导入License
        boolean result = iLicenseService.importLicense(licenseStr);
        // 刷新License有效期自检结果
        try {
            SelfCheckManager.getInstance().checkItem(licenseValidityPeriodCheckItem.getFullCode());
        } catch (Exception e) {
            LOGGER.warn("Failed to recheck license status while importing new license file", e);
        }
        return RestResponse.success(result);
    }

    /**
     * 查询License
     *
     * @return
     */
    @GetMapping
    @Operation(description = QUERY_LICENSE_I18N_KEY, summary = "查询License")
    public RestResponse<LicenseInfoResponse> getLicense() {
        LicenseInfo licenseInfo = iLicenseService.getLicense();
        LicenseInfoResponse licenseInfoResponse = convertService.convert(licenseInfo, LicenseInfoResponse.class);
        return RestResponse.success(licenseInfoResponse);
    }

    /**
     * 导入License
     *
     * @return 是否导入成功
     */
    @PostMapping("/parse")
    @Operation(description = PARSE_LICENSE_I18N_KEY, summary = "解析License")
    public RestResponse<LicenseInfoResponse> parseLicense(@RequestParam("file") MultipartFile file) {
        // 授权文件大小和格式校验
        validateImportedLicenseFile(file);
        // 从文件读取授权信息
        String licenseStr = readImportedLicenseFile(file);
        // 解析License信息
        LicenseInfo licenseInfo = iLicenseService.parseLicense(licenseStr);
        LicenseInfoResponse licenseInfoResponse = convertService.convert(licenseInfo, LicenseInfoResponse.class);

        return RestResponse.success(licenseInfoResponse);
    }

    private static void validateImportedLicenseFile(MultipartFile file) {
        // 文件大小限制
        if (file.getSize() > MAX_FILE_SIZE_BYTE) {
            throw ManagementInternalError.LICENSE_PARSE_ERROR.toException(THE_FILE_SIZE_EXCEEDS_THE_LIMIT_I18N_KEY, FileSizeHelper.humanReadableByteCount(MAX_FILE_SIZE_BYTE));
        }
        // 验证文件扩展名
        if (!Objects.requireNonNull(file.getOriginalFilename()).endsWith(LICENSE_EXTENSION)) {
            throw ManagementInternalError.LICENSE_PARSE_ERROR.toException(FILE_IS_NOT_A_DAT_FILE_I18N_KEY, file.getOriginalFilename());
        }
    }

    private static String readImportedLicenseFile(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            byte[] bytes = new byte[1024];
            int len;
            while ((len = inputStream.read(bytes)) != -1) {
                //将读到的字节写入输出流
                outputStream.write(bytes, 0, len);
            }
            return outputStream.toString("GB2312");
        } catch (IOException e) {
            throw ManagementInternalError.LICENSE_PARSE_ERROR.toException(LICENSE_FILE_READ_EXCEPTION_I18N_KEY, e);
        }
    }
}