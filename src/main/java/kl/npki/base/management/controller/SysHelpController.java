package kl.npki.base.management.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.management.core.service.help.SysHelpService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static kl.npki.base.management.constant.I18nOperationLogConstant.MODIFY_OPERATION_GUIDANCE_INFORMATION_I18N_KEY;
import static kl.npki.base.management.constant.I18nOperationLogConstant.OBTAIN_OPERATION_GUIDANCE_INFORMATION_I18N_KEY;

/**
 * 系统帮助信息后端控制器
 *
 * <AUTHOR>
 * @date 2023/8/25
 */
@RestController
@RequestMapping("/sysHelp")
@Tag(name = "操作引导")
@LogCollector(resolver = OperationLogResolver.class)
public class SysHelpController implements BaseController {

    @Resource
    private SysHelpService helpService;

    @GetMapping("/{module}")
    @Operation(description = OBTAIN_OPERATION_GUIDANCE_INFORMATION_I18N_KEY, summary = "获取操作引导信息")
    public RestResponse<String> getHelpContent(@PathVariable(name = "module") String module) {
        return RestResponse.success(helpService.getHelpInfo(module));
    }

    @PostMapping("/{module}")
    @Operation(description = MODIFY_OPERATION_GUIDANCE_INFORMATION_I18N_KEY, summary = "修改操作引导信息")
    public RestResponse<Void> updateHelpContent(@PathVariable(name = "module") String module, @RequestBody String content) {
        helpService.updateHelpInfo(module, content);
        return RestResponse.success();
    }
}