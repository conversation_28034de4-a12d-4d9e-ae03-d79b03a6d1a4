package kl.npki.base.management.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.helper.utils.CheckUtils;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.model.batch.BaseBatchRequest;
import kl.npki.base.management.model.permission.request.AddResourceRequest;
import kl.npki.base.management.repository.entity.TResourceDO;
import kl.npki.base.management.service.UnifiedResourceService;
import kl.npki.base.management.service.impl.UrlRoleCacheService;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.management.core.biz.permission.response.UnifiedResourceResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static kl.npki.base.management.constant.I18nConstant.*;

/**
 * 统一资源管理
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
@RestController
@RequestMapping("/unifiedResource")
@Tag(name = "统一资源管理")
@LogCollector(resolver = OperationLogResolver.class)
public class UnifiedResourceController {

    @Resource
    private UnifiedResourceService unifiedResourceService;

    @Resource
    private UrlRoleCacheService urlRoleCacheService;

    @Resource
    private ConvertService convertService;

    /**
     * 查询所有资源列表（树形结构）
     */
    @GetMapping("/tree")
    @Operation(description = QUERY_UNIFIED_RESOURCES_TREE_I18N_KEY, summary = "查询所有资源列表")
    public RestResponse<List<UnifiedResourceResponse>> listResourcesTree(@RequestParam(name = "resourceNameOrUrl", required = false) String resourceNameOrUrl) {
        List<UnifiedResourceResponse> result = unifiedResourceService.listResourcesTree(resourceNameOrUrl);
        return RestResponse.success(result);
    }

    /**
     * 新增资源
     */
    @PostMapping
    @Operation(description = CREATE_UNIFIED_RESOURCE_I18N_KEY, summary = "新增资源")
    public RestResponse<Void> createResource(@RequestBody AddResourceRequest request) {
        TResourceDO resource = convertService.convert(request, TResourceDO.class);
        unifiedResourceService.createResource(resource);
        return RestResponse.success();
    }

    /**
     * 更新资源
     */
    @PutMapping("/{id}")
    @Operation(description = UPDATE_UNIFIED_RESOURCE_I18N_KEY, summary = "更新资源")
    public RestResponse<Void> updateResource(@PathVariable Long id, @RequestBody AddResourceRequest request) {
        TResourceDO resource = convertService.convert(request, TResourceDO.class);
        resource.setId(id);
        unifiedResourceService.updateResource(resource);
        return RestResponse.success();
    }

    /**
     * 删除资源
     */
    @DeleteMapping("/{id}")
    @Operation(description = DELETE_UNIFIED_RESOURCE_I18N_KEY, summary = "删除资源")
    public RestResponse<Void> deleteResource(@PathVariable Long id) {
        unifiedResourceService.deleteResource(id);
        urlRoleCacheService.refreshUrlRolesCache();
        return RestResponse.success();
    }

    /**
     * 批量删除资源
     */
    @DeleteMapping("/batch")
    @Operation(description = BATCH_DELETE_UNIFIED_RESOURCES_I18N_KEY, summary = "批量删除资源")
    public RestResponse<Void> batchDeleteResources(@RequestBody BaseBatchRequest batchRequest) {
        unifiedResourceService.batchDelete(batchRequest.getIdList());
        urlRoleCacheService.refreshUrlRolesCache();
        return RestResponse.success();
    }

}
