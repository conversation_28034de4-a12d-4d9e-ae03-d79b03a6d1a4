package kl.npki.base.management.controller.upload;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.biz.upload.UploadProcessorRegistry;
import kl.npki.base.core.biz.upload.model.UploadFileAddInfo;
import kl.npki.base.core.biz.upload.model.UploadFileEntity;
import kl.npki.base.core.biz.upload.service.IUploadFileProcessor;
import kl.npki.base.core.constant.UploadFileProcessStatusEnum;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.model.upload.*;
import kl.npki.base.management.repository.service.IUploadFileManagementService;
import kl.npki.base.management.utils.FileDownloadHelper;
import kl.npki.base.management.utils.MultipartFileUtil;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import org.apache.commons.codec.binary.Base64;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/5/8 下午2:49
 */
@RestController
@RequestMapping("/uploadFile")
@Tag(name = "文件上传管理")
public class UploadFileController {

    @Resource
    private ConvertService convertService;

    @Resource
    private IUploadFileManagementService uploadFileManagementService;

    @LogCollector(resolver = OperationLogResolver.class)
    @PostMapping("/upload")
    //message = 上传文件
    @Operation(description = "kl.npki.base.management.i18n_annotation.upload_files", summary = "上传文件")
    public RestResponse<UploadFileAddResponse> upload(@RequestParam("file") MultipartFile file,
                                                      @RequestParam("interfaceName") String interfaceName) {
        IUploadFileProcessor processor = UploadProcessorRegistry.getProcessor(interfaceName);
        int headRowNumber = processor.getHeadRowNumber();
        int excelContentCount = MultipartFileUtil.getExcelContentCount(file, headRowNumber);
        if (excelContentCount == -headRowNumber) {
            // 文件内容为空
            throw ManagementValidationError.UPLOAD_FILE_EMPTY_ERROR.toException();
        }
        UploadFileEntity fileEntity = uploadFileManagementService.searchExistenceUnfinishedFile(interfaceName);
        if (null != fileEntity) {
            // 检查当前接口是否存在未完成的文件上传操作
            throw ManagementValidationError.UPLOAD_FILE_EXIST_UNFINISHED_ERROR
                    .toException(String.valueOf(fileEntity.getId()));
        }

        UploadFileAddInfo uploadFileAddInfo = new UploadFileAddInfo();
        uploadFileAddInfo.setFileName(file.getOriginalFilename());
        uploadFileAddInfo.setInterfaceName(interfaceName);
        // 待处理总数需要减去文件头
        uploadFileAddInfo.setTotalCount(excelContentCount);
        try {
            uploadFileAddInfo.setRequestFile(Base64Util.base64Encode(file.getBytes()));
        } catch (IOException e) {
            throw ManagementInternalError.FILE_OPERATE_ERROR.toException(e.getMessage(), e);
        }
        // 校验上传的文件格式是否与interfaceName匹配
        String requestFile = uploadFileAddInfo.getRequestFile();
        try (InputStream inputStream = new ByteArrayInputStream(Base64Util.base64Decode(requestFile))) {
            processor.checkUploadFile(inputStream);
        } catch (IOException e) {
            throw ManagementInternalError.FILE_OPERATE_ERROR.toException(e.getMessage(), e);
        }
        // 开始上传文件
        Long id = uploadFileManagementService.uploadFile(uploadFileAddInfo);
        return RestResponse.success(new UploadFileAddResponse(id));
    }

    @LogCollector(resolver = OperationLogResolver.class)
    @PostMapping("/process")
    //message = 处理上传的文件
    @Operation(description = "kl.npki.base.management.i18n_annotation.process_uploaded_files", summary = "处理上传的文件")
    public RestResponse<Boolean> process(@RequestParam("id") Long id) {
        UploadFileEntity fileEntity = uploadFileManagementService.searchById(id);
        String interfaceName = fileEntity.getInterfaceName();
        // 检查是否已经完成处理
        if (UploadFileProcessStatusEnum.COMPLETE.getStatus() == fileEntity.getProcessStatus().intValue()) {
            throw ManagementValidationError.UPLOAD_FILE_FINISHED_ERROR.toException();
        }
        // 检查是否正在处理
        if (UploadFileProcessStatusEnum.PROCESSING.getStatus() == fileEntity.getProcessStatus().intValue()) {
            throw ManagementValidationError.UPLOAD_FILE_PROCESSING_ERROR.toException();
        }
        // 填充操作者信息
        String loginUserId = RequestContextHolder.getLoginUserId();
        fileEntity.setLastOperatorId(loginUserId);
        // 开始处理上传的文件
        IUploadFileProcessor processor = UploadProcessorRegistry.getProcessor(interfaceName);
        processor.processUploadFileEntity(fileEntity);
        return RestResponse.success(true);
    }

    @LogCollector(resolver = OperationLogResolver.class)
    @DeleteMapping("/delete")
    //message = 删除上传文件
    @Operation(description = "kl.npki.base.management.i18n_annotation.delete_uploaded_files", summary = "删除上传文件")
    public RestResponse<Boolean> delete(@RequestParam("id") Long id) {
        // 检查是否存在
        uploadFileManagementService.searchById(id);
        return RestResponse.success(uploadFileManagementService.delete(id));
    }

    @PostMapping("/progress")
    //message = 文件处理进度
    @Operation(description = "kl.npki.base.management.i18n_annotation.progress_of_file_processing", summary = "文件处理进度")
    public RestResponse<UploadFileProgressResponse> progress(@RequestParam("id") Long id) {
        // 批量导入机构信息
        UploadFileEntity fileEntity = uploadFileManagementService.searchById(id);
        // 开始处理上传的文件
        UploadFileProgressResponse response = convertService.convert(fileEntity, UploadFileProgressResponse.class);
        return RestResponse.success(response);
    }


    @GetMapping("/queryUnfinished")
    //message = 查询当前接口是否存在未完成的文件
    @Operation(description = "kl.npki.base.management.i18n_annotation.check_if_there_are_any_unfinished_files_in_the_current_interface", summary = "查询当前接口是否存在未完成的文件")
    public RestResponse<UploadFileUnfinishedResponse> queryUnfinished(
            @RequestParam(value = "interfaceName") String interfaceName) {
        UploadFileEntity fileEntity = uploadFileManagementService.searchExistenceUnfinishedFile(interfaceName);
        if (null == fileEntity) {
            return RestResponse.success(new UploadFileUnfinishedResponse());
        }
        UploadFileUnfinishedResponse response = convertService.convert(fileEntity, UploadFileUnfinishedResponse.class);
        return RestResponse.success(response);
    }

    @GetMapping("/list")
    //message = 文件上传列表
    @Operation(description = "kl.npki.base.management.i18n_annotation.file_upload_list", summary = "文件上传列表")
    public RestResponse<IPage<UploadFileListResponse>> list(PageInfo pageInfo, String interfaceName) {
        IPage<UploadFileListResponse> page = uploadFileManagementService.queryUploadFileList(pageInfo, interfaceName);
        return RestResponse.success(page);
    }

    @GetMapping("/download/source")
    //message = 下载源文件
    @Operation(description = "kl.npki.base.management.i18n_annotation.download_source_files", summary = "下载源文件")
    public ResponseEntity<byte[]> downloadSource(@RequestParam("id") Long id) {
        UploadFileEntity fileEntity = uploadFileManagementService.searchById(id);
        byte[] requestFile = Base64.decodeBase64(fileEntity.getRequestFile());
        File tempFile = MultipartFileUtil.getTempFile(requestFile,
                MultipartFileUtil.fileNameAddDate(fileEntity.getFileName()));
        return FileDownloadHelper.getResponseEntityByFile(tempFile, true);
    }

    @GetMapping("/download/result")
    //message = 下载结果文件
    @Operation(description = "kl.npki.base.management.i18n_annotation.download_result_file", summary = "下载结果文件")
    public ResponseEntity<byte[]> downloadResult(@RequestParam("id") Long id) {
        UploadFileEntity fileEntity = uploadFileManagementService.searchById(id);
        byte[] resultFile = Base64.decodeBase64(fileEntity.getResultFile());
        File tempFile = MultipartFileUtil.getTempFile(resultFile,
                MultipartFileUtil.fileNameAddDate(fileEntity.getFileName()));
        return FileDownloadHelper.getResponseEntityByFile(tempFile, true);
    }


    @GetMapping("/download/template")
    //message = 下载模板
    @Operation(description = "kl.npki.base.management.i18n_annotation.download_template", summary = "下载模板")
    public ResponseEntity<byte[]> downloadResult(@RequestParam("interfaceName") String interfaceName) {
        IUploadFileProcessor processor = UploadProcessorRegistry.getProcessor(interfaceName);
        if (null == processor) {
            throw ManagementValidationError.UPLOAD_FILE_INTERFACE_NOT_EXIST_ERROR.toException(interfaceName);
        }
        return FileDownloadHelper.getResponseEntityByFile(processor.getTemplateFile(),
                processor.getTemplateFileName(), true);
    }

    @GetMapping("/allInterfaceName")
    //message = 所有支持批量操作接口名称
    @Operation(description = "kl.npki.base.management.i18n_annotation.all_support_batch_operation_interface_names", summary = "所有支持批量操作接口名称")
    public RestResponse<List<InterfaceNameResponse>> downloadResult() {
        Map<String, IUploadFileProcessor> allProcessor = UploadProcessorRegistry.getAllProcessor();
        List<InterfaceNameResponse> result = new ArrayList<>();
        for (String key : allProcessor.keySet()) {
            InterfaceNameResponse response = new InterfaceNameResponse();
            response.setInterfaceName(key)
                    .setDesc(allProcessor.get(key).getDesc());
            result.add(response);
        }
        return RestResponse.success(result);
    }


}