package kl.npki.base.management.controller.log;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.base.management.model.log.response.RunLogResponse;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.management.core.biz.log.model.RunLogInfo;
import kl.npki.management.core.service.log.IRunLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.util.List;

import static kl.npki.base.management.constant.I18nExceptionInfoConstant.*;
import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * 运行日志Controller
 *
 * <AUTHOR>
 * @date 2022/12/14
 */
@RestController
@RequestMapping("/runLog")
@Tag(name = "运行日志")
@LogCollector(resolver = OperationLogResolver.class)
public class RunLogController implements BaseController {

    @Resource
    private IRunLogService runLogService;

    @Resource
    private ConvertService convertService;

    /**
     * 获取所有的运行日志文件列表
     *
     * @return
     */
    @GetMapping("/list")
    @Operation(description = VIEW_THE_LIST_OF_RUNNING_LOGS_I18N_KEY, summary = "运行日志列表查看")
    public RestResponse<List<RunLogResponse>> getRunLogList() {
        List<RunLogInfo> runLogList = runLogService.getRunLogList();
        List<RunLogResponse> runLogResponses = convertService.convert(runLogList, RunLogResponse.class);
        return RestResponse.success(runLogResponses);
    }

    /**
     * 查看日志详情
     *
     * @param logName
     * @return
     */
    @GetMapping("/viewLogs")
    @Operation(description = VIEW_OPERATION_LOGS_I18N_KEY, summary = "运行日志查看")
    public RestResponse<List<String>> getRunLogDetail(@RequestParam String logName, @RequestParam int logSize) {
        List<String> viewRunLog = runLogService.viewRunLog(logName, logSize);
        return RestResponse.success(viewRunLog);
    }

    /**
     * 运行日志下载
     *
     * @param response
     * @param logName
     * @return
     */
    @GetMapping("/downloadRunLog")
    @Operation(description = DOWNLOAD_RUNNING_LOGS_I18N_KEY, summary = "运行日志下载")
    public void downloadRunLogFile(HttpServletResponse response, @RequestParam String logName) {
        if (StringUtils.isEmpty(logName)) {
            throw ManagementInternalError.FILE_DOWNLOAD_FAIL.toException(PLEASE_ASSIGN_FILE_MUST_NOT_BE_EMPTY_OR_NULL_I18N_KEY);
        }

        File tobeDownloadLogFile = runLogService.getLogFileByName(logName);
        if(tobeDownloadLogFile == null){
            throw ManagementInternalError.FILE_DOWNLOAD_FAIL.toException(INVALID_LOG_NAME_I18N_KEY, logName);
        }

        // 设置强制下载不打开
        response.setContentType("application/force-download");
        // 设置文件名
        response.addHeader("Content-Disposition", "attachment;fileName=" + tobeDownloadLogFile.getName());
        byte[] buffer = new byte[1024];
        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(tobeDownloadLogFile));
             OutputStream out = response.getOutputStream()) {
            int len;
            while ((len = bis.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
        } catch (Exception e) {
            throw ManagementInternalError.FILE_DOWNLOAD_FAIL.toException(FILE_DOWNLOAD_FAILED_I18N_KEY, tobeDownloadLogFile.getName());
        }
    }
}