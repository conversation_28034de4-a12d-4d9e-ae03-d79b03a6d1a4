package kl.npki.base.management.controller.config;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.bean.validate.ValidateService;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.configs.RegionAndLanguageConfig;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.model.system.request.RegionAndLanguageRequest;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.management.core.service.config.RegionAndLanguageConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@RequestMapping("/regionAndLanguage")
@Tag(name = "区域与语言配置")
@LogCollector(resolver = OperationLogResolver.class)
public class RegionAndLanguageConfigController implements BaseController {

    @Resource
    private RegionAndLanguageConfigService regionAndLanguageConfigService;

    @Resource
    private ConvertService convertService;

    @Resource
    private ValidateService validateService;


    @PostMapping("/save")
    @Operation(description = "kl.npki.base.management.i18n_RegionAndLanguage_SAVE_REGION_LANGUAGE", summary = "保存区域与语言")
    public RestResponse<Void> save(@RequestBody RegionAndLanguageRequest regionAndLanguageRequest) {
        validateService.validate(regionAndLanguageRequest);
        RegionAndLanguageConfig regionAndLanguageConfig = convertService.convert(regionAndLanguageRequest, RegionAndLanguageConfig.class);
        regionAndLanguageConfigService.saveRegionAndLanguage(regionAndLanguageConfig);
        return RestResponse.success();
    }

    @GetMapping("/get")
    @Operation(description = "kl.npki.base.management.i18n_RegionAndLanguage_GET_REGION_LANGUAGE", summary = "获取区域与语言")
    public RestResponse<RegionAndLanguageConfig> get() {
        RegionAndLanguageConfig regionAndLanguageConfig = regionAndLanguageConfigService.getRegionAndLanguage();
        return RestResponse.success(regionAndLanguageConfig);
    }

    @GetMapping("/list")
    @Operation(description = "kl.npki.base.management.i18n_RegionAndLanguage_LIST_REGION_LANGUAGE", summary = "获取区域与语言枚举列表")
    public RestResponse<Map<String, Map<String, String>> > list() {
        Map<String, Map<String, String>>  regionAndLanguageList = regionAndLanguageConfigService.getRegionAndLanguageList();
        return RestResponse.success(regionAndLanguageList);
    }
}
