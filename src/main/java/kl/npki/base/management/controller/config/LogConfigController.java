package kl.npki.base.management.controller.config;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.configs.LogConfig;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.model.log.request.LogConfigRequest;
import kl.npki.base.management.model.log.response.LogConfigResponse;
import kl.npki.base.management.utils.ValidateHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.base.service.constants.LogLevel;
import kl.npki.base.service.constants.LogOutType;
import kl.npki.management.core.service.config.LogConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/logConfig")
@Tag(name = "日志配置")
@LogCollector(resolver = OperationLogResolver.class)
public class LogConfigController implements BaseController {

    @Resource
    private ConvertService convertService;

    @Resource
    private ValidateHelper validateUtil;

    @Resource
    LogConfigService logConfigService;

    @GetMapping("/info")
    @Operation(description = QUERY_LOG_CONFIGURATION_I18N_KEY, summary = "查询日志配置")
    public RestResponse<LogConfigResponse> queryLogConfig() {
        LogConfig logConfig = logConfigService.queryLogConfig();
        LogConfigResponse logConfigResponse = convertService.convert(logConfig, LogConfigResponse.class);
        return RestResponse.success(logConfigResponse);
    }

    @PostMapping("/save")
    @Operation(description = SAVE_LOG_CONFIGURATION_I18N_KEY, summary = "保存日志配置")
    public RestResponse<Void> saveLogConfig(@RequestBody LogConfigRequest logConfigRequest) {
        validateUtil.validate(logConfigRequest);
        LogConfig logConfig = convertService.convert(logConfigRequest, LogConfig.class);
        logConfigService.saveLogConfig(logConfig);
        return RestResponse.success();
    }

    @GetMapping("/logOutType/list")
    @Operation(description = GET_LOG_OUTPUT_TYPE_I18N_KEY, summary = "获取日志输出类型")
    public RestResponse<Map<String, String>> getLogOutTypeList() {
        return RestResponse.success(LogOutType.getValuesMap());
    }

    @GetMapping("/level/list")
    @Operation(description = GET_LOG_LEVEL_TYPE_DROPDOWN_OPTIONS_I18N_KEY, summary = "获取日志级别类型下拉选项")
    public RestResponse<Map<String, String>> getLogLevelList() {
        return RestResponse.success(LogLevel.getValuesMap());
    }
}