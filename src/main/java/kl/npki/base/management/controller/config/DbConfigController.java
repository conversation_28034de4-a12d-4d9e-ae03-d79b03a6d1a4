package kl.npki.base.management.controller.config;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.db.support.slot.SlotDataSourceConfig;
import kl.nbase.log.annotation.JsonFieldFilter;
import kl.nbase.log.annotation.JsonFieldFilters;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.biz.config.model.ConfigStatus;
import kl.npki.base.core.constant.DbConfigType;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.model.db.request.DbConfigRequest;
import kl.npki.base.management.model.db.request.DbPasswdRequest;
import kl.npki.base.management.model.db.response.DbConfigResponse;
import kl.npki.base.management.utils.DbPropertiesHelper;
import kl.npki.base.management.utils.ValidateHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.db.impl.MonthShardingServiceImpl;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.management.core.biz.config.model.DbConfigEntity;
import kl.npki.management.core.biz.config.model.DbConfigInfo;
import kl.npki.management.core.biz.config.model.DbPasswdConfigInfo;
import kl.npki.management.core.service.config.DbConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static kl.npki.base.management.constant.I18nExceptionInfoConstant.DATABASE_CONFIGURATION_INFORMATION_CANNOT_BE_EMPTY_I18N_KEY;
import static kl.npki.base.management.constant.I18nExceptionInfoConstant.H2_DATABASE_CONFIGURATION_IS_NOT_ALLOWED_I18N_KEY;
import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dbConfig")
@Tag(name = "数据库配置")
@LogCollector(resolver = OperationLogResolver.class, maskFields = {"username", "password"})
public class DbConfigController implements BaseController {

    @Resource
    DbConfigService dbConfigService;

    @Resource
    MonthShardingServiceImpl monthShardingServiceImpl;

    @Resource
    private ValidateHelper validateUtil;

    @GetMapping("/info")
    @Operation(description = QUERY_DATABASE_CONFIGURATION_I18N_KEY, summary = "查询数据库配置")
    @JsonFieldFilters(value = {@JsonFieldFilter(exclude = {"password"}, value = DbConfigInfo.class, type = JsonFieldFilter.JacksonFilterType.RESPONSE)})
    public RestResponse<DbConfigResponse> queryDbConfig() {
        SlotDataSourceConfig slotDataSourceConfig = dbConfigService.queryDbConfig();
        if (Objects.isNull(slotDataSourceConfig)) {
            return RestResponse.success();
        }
        return RestResponse.success(DbPropertiesHelper.translate2DbConfigResponse(slotDataSourceConfig));
    }

    /**
     * 数据库配置信息新增
     *
     * @param dbConfigRequest
     * @return
     */
    @PostMapping("/save")
    @Operation(description = SAVE_DATABASE_CONFIGURATION_I18N_KEY, summary = "保存数据库配置")
    public RestResponse<Void> addDbConfig(@RequestBody DbConfigRequest dbConfigRequest) {
        validateUtil.validate(dbConfigRequest);
        if (dbConfigRequest.getDbType().equalsIgnoreCase(DbConfigType.H2.getDbTypeName())) {
            throw ManagementValidationError.UNSUPPORTED_DB_TYPE.toException(H2_DATABASE_CONFIGURATION_IS_NOT_ALLOWED_I18N_KEY);
        }
        if (CollectionUtils.isEmpty(dbConfigRequest.getDbConfigInfoList())) {
            throw ManagementValidationError.PARAM_ERROR.toException(DATABASE_CONFIGURATION_INFORMATION_CANNOT_BE_EMPTY_I18N_KEY);
        }
        DbConfigEntity dbConfigEntity = dbConfigRequest.toDbConfigEntity();
        dbConfigService.reloadDbPasswd(dbConfigEntity);
        dbConfigService.saveDbConfig(dbConfigEntity);
        // 创建分表
        monthShardingServiceImpl.createTables();
        return RestResponse.success();
    }

    /**
     * 数据库配置信息新增
     *
     * @param dbConfigRequest
     * @return
     */
    @PostMapping("/test")
    @Operation(description = TEST_DATABASE_CONNECTION_I18N_KEY, summary = "测试数据库连接")
    public RestResponse<Void> testDbConnection(@RequestBody DbConfigRequest dbConfigRequest) {
        validateUtil.validate(dbConfigRequest);
        if (CollectionUtils.isEmpty(dbConfigRequest.getDbConfigInfoList())) {
            throw ManagementValidationError.PARAM_ERROR.toException(DATABASE_CONFIGURATION_INFORMATION_CANNOT_BE_EMPTY_I18N_KEY);
        }
        DbConfigEntity dbConfigEntity = dbConfigRequest.toDbConfigEntity();
        dbConfigService.reloadDbPasswd(dbConfigEntity);
        dbConfigService.testDbConnection(dbConfigEntity);
        return RestResponse.success();
    }

    @GetMapping("/status")
    @Operation(description = QUERY_CONFIGURATION_STATUS_I18N_KEY, summary = "查询配置状态")
    public RestResponse<ConfigStatus> queryConfigStatus() {
        return RestResponse.success(dbConfigService.queryConfigStatus());
    }

    /**
     * 保存数据库密码
     * @param dbPasswdRequest 保存数据库密码请求
     * @return
     */
    @PostMapping("/save/dbPasswd")
    @Operation(description = SAVE_DATABASE_CONFIGURATION_I18N_KEY, summary = "保存数据库密码")
    public RestResponse<Void> saveDbPasswd(@RequestBody DbPasswdRequest dbPasswdRequest) {
        validateUtil.validate(dbPasswdRequest);

        List<DbPasswdConfigInfo> dbPasswdRequestList = dbPasswdRequest.getDbPasswdRequestList();
        dbConfigService.saveDbPasswd(dbPasswdRequestList);

        return RestResponse.success();
    }
}