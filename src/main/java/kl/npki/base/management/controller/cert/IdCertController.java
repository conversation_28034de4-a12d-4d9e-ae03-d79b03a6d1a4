package kl.npki.base.management.controller.cert;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.biz.cert.model.CertRequestInfo;
import kl.npki.base.core.biz.cert.model.ServerCertRequestInfo;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.model.cert.request.CertSignRequest;
import kl.npki.base.management.model.cert.response.CertDetailResponse;
import kl.npki.base.management.model.cert.response.CertInfoResponse;
import kl.npki.base.management.utils.ValidateHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.management.core.biz.cert.model.CertDetailInfo;
import kl.npki.management.core.service.cert.IIdCertService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("idCert")
@Tag(name = "身份证书管理")
@LogCollector(resolver = OperationLogResolver.class)
public class IdCertController implements BaseController {

    @Resource
    private ValidateHelper validateUtil;

    @Resource
    private ConvertService convertService;

    @Resource
    private IIdCertService idCertService;

    @PostMapping("/issue")
    @Operation(description = SELF_ISSUED_IDENTITY_CERTIFICATE_I18N_KEY, summary = "自签发身份证书")
    public RestResponse<Boolean> selfIssueRootIdCert(@RequestBody CertSignRequest certSignRequest) {
        validateUtil.validate(certSignRequest);
        CertRequestInfo rootAndIdCertSigInfo = convertService.convert(certSignRequest, CertRequestInfo.class);
        if (Boolean.TRUE.equals(certSignRequest.getSelfSign())) {
            idCertService.issueIdCert(rootAndIdCertSigInfo);
        } else {
            idCertService.issueIdCertByCa(rootAndIdCertSigInfo);
        }
        return RestResponse.success(Boolean.TRUE);
    }

    @PostMapping("/update")
    @Operation(description = UPDATE_IDENTITY_CERTIFICATE_I18N_KEY, summary = "更新身份证书")
    public RestResponse<Boolean> updateRootIdCert(@RequestBody CertSignRequest certSignRequest) {
        validateUtil.validate(certSignRequest);
        CertRequestInfo rootAndIdCertSigInfo = convertService.convert(certSignRequest, CertRequestInfo.class);
        if (Boolean.TRUE.equals(certSignRequest.getSelfSign())) {
            idCertService.updateIdCert(rootAndIdCertSigInfo);
        } else {
            idCertService.updateIdCertByCa(rootAndIdCertSigInfo);
        }
        return RestResponse.success(Boolean.TRUE);
    }

    @GetMapping("/cert")
    @Operation(description = OBTAIN_IDENTITY_CERTIFICATE_I18N_KEY, summary = "获取身份证书")
    public RestResponse<CertDetailResponse> queryRootIdCert() {
        CertDetailInfo certDetailInfo = idCertService.queryIdCert();
        if (ObjectUtils.isEmpty(certDetailInfo)) {
            // 如果为空的话，则给前端返回一个空的对象,防止前端进入系统配置直接报空指针
            return RestResponse.success(new CertDetailResponse());
        }
        CertDetailResponse response = convertService.convert(certDetailInfo, CertDetailResponse.class);
        return RestResponse.success(response);
    }

    @GetMapping("/getIdCertRequestInfo")
    @Operation(description = VIEW_DETAILED_INFORMATION_OF_SERVER_ID_CERTIFICATE_INFO_I18N_KEY, summary = "查看身份证书请求信息")
    public RestResponse<CertInfoResponse> getIdCertRequestInfo() {
        ServerCertRequestInfo idCertRequestInfo = idCertService.getIdCertRequestInfo();
        CertInfoResponse response = convertService.convert(idCertRequestInfo, CertInfoResponse.class);
        return RestResponse.success(response);
    }

}