package kl.npki.base.management.controller.backup;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.model.backup.SystemBackupRequest;
import kl.npki.base.management.utils.FileDownloadHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.management.core.service.backup.CombinedBackupRestoreService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;

import static kl.npki.base.management.constant.I18nOperationLogConstant.CONFIGURE_BACKUP_I18N_KEY;
import static kl.npki.base.management.constant.I18nOperationLogConstant.CONFIGURE_RECOVERY_I18N_KEY;

/**
 * 系统备份功能控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system")
@Tag(name = "系统备份恢复功能")
@LogCollector(resolver = OperationLogResolver.class, maskFields = {"secureBackupKey"})
public class SystemBackupRestoreController implements BaseController {

    @Resource
    private CombinedBackupRestoreService combinedBackupRestoreService;

    @PostMapping("/backup")
    @LogCollector(resolver = OperationLogResolver.class, maskFields = {"secureBackupKey"})
    @Operation(description = CONFIGURE_BACKUP_I18N_KEY, summary = "配置备份")
    public ResponseEntity<byte[]> doSystemBackup(@RequestBody SystemBackupRequest systemBackupRequest) {
        File file = combinedBackupRestoreService.doSystemBackup(systemBackupRequest.getSecureBackupKey());
        return FileDownloadHelper.getResponseEntityByFile(file, true);
    }

    @PostMapping("/restore")
    @Operation(description = CONFIGURE_RECOVERY_I18N_KEY, summary = "配置恢复")
    public RestResponse<Void> doSystemRestore(@RequestParam(value = "secureBackupKey") String secureBackupKey, @RequestParam(value = "backupFile") MultipartFile backupFile) {
        combinedBackupRestoreService.doSystemRestore(secureBackupKey, backupFile);
        return RestResponse.success();
    }
}