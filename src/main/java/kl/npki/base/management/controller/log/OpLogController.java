package kl.npki.base.management.controller.log;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.bean.validate.ValidateService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.log.collect.LogCollector;
import kl.nbase.security.util.encoders.Base64;
import kl.nbase.security.utils.CertUtil;
import kl.npki.base.core.biz.log.model.SystemLogFileEntity;
import kl.npki.base.core.biz.log.model.SystemLogFileInfo;
import kl.npki.base.core.constant.SystemLogEnum;
import kl.npki.base.core.utils.CompressionUtils;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.model.log.request.LogExcelFileRequest;
import kl.npki.base.management.model.log.request.OpLogExcelPlaintextRequest;
import kl.npki.base.management.model.log.request.OpLogExcelRequest;
import kl.npki.base.management.model.log.request.OpLogRequest;
import kl.npki.base.management.model.log.response.LogExcelPlaintextResponse;
import kl.npki.base.management.model.log.response.OpLogDetailResponse;
import kl.npki.base.management.model.log.response.OpLogListResponse;
import kl.npki.base.management.utils.FileDownloadHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.base.service.repository.entity.TOpLogDO;
import kl.npki.base.service.repository.service.IOpLogRepositoryService;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.security.cert.X509Certificate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * 操作日志controller
 *
 * <AUTHOR>
 * @date 2022/12/13
 */
@RestController
@RequestMapping("/opLog")
@Tag(name = "操作日志")
@LogCollector(resolver = OperationLogResolver.class)
public class OpLogController implements BaseController {

    /**
     * 日志时间字段
     */
    private static final String LOG_WHEN_FIELD = "log_when";

    @Resource
    private IOpLogRepositoryService opLogRepository;

    @Resource
    private ConvertService convertService;

    @Resource
    private kl.npki.base.core.biz.log.service.IOpLogService logService;

    @Resource
    private ValidateService validateService;

    /**
     * 分页查询操作日志
     *
     * @param pageInfo
     * @return
     */
    @GetMapping("/list")
    @Operation(description = QUERY_OPERATION_LOG_LIST_I18N_KEY, summary = "查询操作日志列表")
    public RestResponse<IPage<OpLogListResponse>> getApiLogList(PageInfo pageInfo, OpLogRequest opLogRequest) {
        QueryWrapper<TOpLogDO> queryWrapper = opLogRequest.toQueryWrapper();
        // 如果未传 sortField 或 sortField 不为 log_when，默认按 log_when 倒序
        if (ObjectUtils.isEmpty(opLogRequest.getSortField()) || !LOG_WHEN_FIELD.equalsIgnoreCase(opLogRequest.getSortField())) {
            queryWrapper.orderByDesc(LOG_WHEN_FIELD);
        }
        //优化：只查前端需要的数据，前端不需要的大字段不查，减少数据库查询压力
        String sqlSelect = Wrappers.<TOpLogDO>lambdaQuery().select(TOpLogDO::getId, TOpLogDO::getLogDo, TOpLogDO::getLogWhen,
            TOpLogDO::getUsername, TOpLogDO::getClientIp, TOpLogDO::getResult).getSqlSelect();
        queryWrapper.select(sqlSelect);
        IPage<TOpLogDO> page = pageInfo.toPage();
        IPage<TOpLogDO> opLogDOList = opLogRepository.page(page, queryWrapper);
        IPage<OpLogListResponse> opLogResponsePage = opLogDOList.convert(opLogDO -> convertService.convert(opLogDO, OpLogListResponse.class));
        return RestResponse.success(opLogResponsePage);
    }

    /**
     * 根据日志id获取单条日志的具体信息
     *
     * @param logId
     * @return
     */
    @GetMapping("/detail/{logId}")
    @Operation(description = QUERY_OPERATION_LOG_DETAILED_INFORMATION_I18N_KEY, summary = "查询操作日志详细信息")
    public RestResponse<OpLogDetailResponse> getApiLogInfo(@PathVariable("logId") Long logId) {
        TOpLogDO opLogDO = opLogRepository.getById(logId);
        OpLogDetailResponse opLogDetailResponse = convertService.convert(opLogDO, OpLogDetailResponse.class);
        return RestResponse.success(opLogDetailResponse);
    }

    @PostMapping("/export/info")
    @Operation(description = QUERY_EXCEL_ELECTRONIC_SIGNATURE_INFORMATION_LOGS_I18N_KEY, summary = "查询Excel电子签名信息")
    public RestResponse<LogExcelPlaintextResponse> getExportFileInfo(@RequestBody OpLogExcelPlaintextRequest request) {
        validateService.validate(request);
        // 做签名的证书链
        List<X509Certificate> certChain = request
            .getSignerCertChain()
            .stream()
            .map(Base64::decode)
            .map(CertUtil::derToX509)
            .collect(Collectors.toList());
        // 做签名的时间
        Date date = new Date();
        // 构造Excel文件，然后将查询的数据添加到文件中
        Long fileId = request.getFileId();
        File exportFile = logService.writeFileContentToFile(fileId);
        // 根据Excel文件获取XAdES签名的签名原文
        byte[] plaintext = logService.getExcelFilePlaintext(exportFile, certChain, date);
        return RestResponse.success(
            new LogExcelPlaintextResponse()
                .setFileId(String.valueOf(fileId))
                .setPlaintext(Base64.toBase64String(plaintext))
                .setTimestamp(date.getTime()));
    }

    @PostMapping("/export/signedFile")
    @Operation(description = EXPORT_SIGNED_FILE_OF_SERVICE_LOGS_I18N_KEY, summary = "导出带签名日志")
    public ResponseEntity<byte[]> getExportFile(@RequestBody LogExcelFileRequest request) {
        validateService.validate(request);
        // 做签名的证书链
        List<X509Certificate> certChain = request
            .getSignerCertChain()
            .stream()
            .map(Base64::decode)
            .map(CertUtil::derToX509)
            .collect(Collectors.toList());
        Date date = new Date(request.getTimestamp());
        Long fileId = request.getFileId();
        byte[] signature = Base64.decode(request.getSignature());
        File exportFile = logService.writeFileContentToFile(fileId);
        // 更新Excel文件的签名
        logService.updateExcelFileSignature(exportFile, certChain, date, signature);
        SystemLogFileEntity systemLogFileEntity = logService.searchSystemLogFileStatus(fileId);
        return FileDownloadHelper.getResponseEntityByFile(exportFile,
                String.format("sign_%s", systemLogFileEntity.getFileName()), Boolean.TRUE);
    }

    @GetMapping("/export/file")
    @Operation(description = EXPORT_FILE_OF_SERVICE_LOGS_I18N_KEY, summary = "导出日志")
    public RestResponse<Long> getExportFile(OpLogExcelRequest request) {
        SystemLogEnum systemLogEnum = Boolean.TRUE.equals(request.isSecurityLog()) ? SystemLogEnum.SECURITY_LOG : SystemLogEnum.OPERATION_LOG;
        // 导出用户查询条件
        QueryWrapper<TOpLogDO> queryWrapper = request.toQueryWrapper();
        // 导出指定的字段和设置Excel标题名称
        Map<String, List<String>> enableFieldMapping = request.getEnableFieldMapping();
        // 构造Excel文件，然后将查询的数据存到数据库中
        Long fileId = logService.getExportFileAndSaveDb(queryWrapper, enableFieldMapping, systemLogEnum);
        return RestResponse.success(fileId);
    }

    @GetMapping("/export/file/temp")
    @Operation(description = EXPORT_FILE_OF_VIEW_LOGS_I18N_KEY, summary = "查看日志文件")
    public RestResponse<List<SystemLogFileInfo>>  getExportFileTemp(@RequestParam SystemLogEnum systemLogEnum) {
        List<SystemLogFileEntity> systemLogFileEntityList = logService.searchSystemLogFileList(systemLogEnum.getType());
        return RestResponse.success(convertService.convert(systemLogFileEntityList, SystemLogFileInfo.class));
    }

    @DeleteMapping("/export/file/temp/{fileId}")
    @Operation(description = EXPORT_FILE_OF_DELETE_LOGS_I18N_KEY, summary = "删除日志文件")
    public RestResponse<Object>  deleteExportFileTemp(@PathVariable(value = "fileId") @NotNull Long fileId) {
        logService.deleteTempFile(fileId);
        return RestResponse.success();
    }

    @GetMapping("/export/file/temp/download")
    @Operation(description = EXPORT_FILE_OF_DOWNLOAD_LOGS_I18N_KEY, summary = "下载日志文件")
    public ResponseEntity<byte[]> getExportFileTempDownload(@RequestParam Long fileId) {
        SystemLogFileEntity systemLogFileEntity = logService.searchSystemLogFile(fileId);
        byte[] fileBytes = CompressionUtils.decompressFromBase64String(systemLogFileEntity.getFileContent());
        return FileDownloadHelper.getResponseEntityByFile(fileBytes, systemLogFileEntity.getFileName());
    }

}
