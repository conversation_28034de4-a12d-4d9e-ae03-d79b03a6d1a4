package kl.npki.base.management.controller.log;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.log.collect.LogCollector;
import kl.nbase.security.util.encoders.Base64;
import kl.nbase.security.utils.CertUtil;
import kl.npki.base.core.common.date.CustomLocalDateTimePropertyEditor;
import kl.npki.base.core.utils.DateUtil;
import kl.npki.base.management.common.cache.LogExportDataCache;
import kl.npki.base.management.constant.LocalDateFieldConstant;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.model.log.request.LogExcelFileRequest;
import kl.npki.base.management.model.log.request.OpLogExcelPlaintextRequest;
import kl.npki.base.management.model.log.request.OpLogExcelRequest;
import kl.npki.base.management.model.log.request.OpLogRequest;
import kl.npki.base.management.model.log.response.LogExcelPlaintextResponse;
import kl.npki.base.management.model.log.response.OpLogDetailResponse;
import kl.npki.base.management.model.log.response.OpLogListResponse;
import kl.npki.base.management.utils.FileDownloadHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.base.service.repository.entity.TOpLogDO;
import kl.npki.base.service.repository.service.IOpLogRepositoryService;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * 操作日志controller
 *
 * <AUTHOR>
 * @date 2022/12/13
 */
@RestController
@RequestMapping("/opLog")
@Tag(name = "操作日志")
@LogCollector(resolver = OperationLogResolver.class)
public class OpLogController implements BaseController {

    /**
     * 日志时间字段
     */
    private static final String LOG_WHEN_FIELD = "log_when";

    @Resource
    private IOpLogRepositoryService opLogRepository;

    @Resource
    private ConvertService convertService;

    @Resource
    private kl.npki.base.core.biz.log.service.IOpLogService logService;

    /**
     * 分页查询操作日志
     *
     * @param pageInfo
     * @return
     */
    @GetMapping("/list")
    @Operation(description = QUERY_OPERATION_LOG_LIST_I18N_KEY, summary = "查询操作日志列表")
    public RestResponse<IPage<OpLogListResponse>> getApiLogList(PageInfo pageInfo, OpLogRequest opLogRequest) {
        QueryWrapper<TOpLogDO> queryWrapper = opLogRequest.toQueryWrapper();
        // 如果未传 sortField 或 sortField 不为 log_when，默认按 log_when 倒序
        if (ObjectUtils.isEmpty(opLogRequest.getSortField()) || !LOG_WHEN_FIELD.equalsIgnoreCase(opLogRequest.getSortField())) {
            queryWrapper.orderByDesc(LOG_WHEN_FIELD);
        }
        //优化：只查前端需要的数据，前端不需要的大字段不查，减少数据库查询压力
        String sqlSelect = Wrappers.<TOpLogDO>lambdaQuery().select(TOpLogDO::getId, TOpLogDO::getLogDo, TOpLogDO::getLogWhen,
            TOpLogDO::getUsername, TOpLogDO::getClientIp, TOpLogDO::getResult).getSqlSelect();
        queryWrapper.select(sqlSelect);
        IPage<TOpLogDO> page = pageInfo.toPage();
        IPage<TOpLogDO> opLogDOList = opLogRepository.page(page, queryWrapper);
        IPage<OpLogListResponse> opLogResponsePage = opLogDOList.convert(opLogDO -> convertService.convert(opLogDO, OpLogListResponse.class));
        return RestResponse.success(opLogResponsePage);
    }

    /**
     * 根据日志id获取单条日志的具体信息
     *
     * @param logId
     * @return
     */
    @GetMapping("/detail/{logId}")
    @Operation(description = QUERY_OPERATION_LOG_DETAILED_INFORMATION_I18N_KEY, summary = "查询操作日志详细信息")
    public RestResponse<OpLogDetailResponse> getApiLogInfo(@PathVariable("logId") Long logId) {
        TOpLogDO opLogDO = opLogRepository.getById(logId);
        OpLogDetailResponse opLogDetailResponse = convertService.convert(opLogDO, OpLogDetailResponse.class);
        return RestResponse.success(opLogDetailResponse);
    }

    @PostMapping("/export/info")
    @Operation(description = QUERY_EXCEL_ELECTRONIC_SIGNATURE_INFORMATION_LOGS_I18N_KEY, summary = "查询Excel电子签名信息")
    public RestResponse<LogExcelPlaintextResponse> getExportFileInfo(@RequestBody OpLogExcelPlaintextRequest request) {

        // 导出用户查询条件
        QueryWrapper<TOpLogDO> queryWrapper = request.toQueryWrapper();
        // 导出指定的字段和设置Excel标题名称
        Map<String, List<String>> enableFieldMapping = request.getEnableFieldMapping();
        // 做签名的证书链
        List<X509Certificate> certChain = request
            .getSignerCertChain()
            .stream()
            .map(Base64::decode)
            .map(CertUtil::derToX509)
            .collect(Collectors.toList());
        // 做签名的时间
        Date date = new Date();

        // 构造Excel文件，然后将查询的数据添加到文件中
        File exportFile = logService.getExportFile(queryWrapper, enableFieldMapping);
        // 根据Excel文件获取XAdES签名的签名原文
        byte[] plaintext = logService.getExcelFilePlaintext(exportFile, certChain, date);
        // 将导出的Excel文件缓存
        String fileId = LogExportDataCache.INSTANCE.set(exportFile);
        return RestResponse.success(
            new LogExcelPlaintextResponse()
                .setFileId(fileId)
                .setPlaintext(Base64.toBase64String(plaintext))
                .setTimestamp(date.getTime()));
    }

    @PostMapping("/export/signedFile")
    @Operation(description = EXPORT_SIGNED_FILE_OF_SERVICE_LOGS_I18N_KEY, summary = "导出带签名日志")
    public ResponseEntity<byte[]> getExportFile(@RequestBody LogExcelFileRequest request) {
        // 做签名的证书链
        List<X509Certificate> certChain = request
            .getSignerCertChain()
            .stream()
            .map(Base64::decode)
            .map(CertUtil::derToX509)
            .collect(Collectors.toList());
        File exportFile = LogExportDataCache.INSTANCE.get(request.getFileId());
        Date date = new Date(request.getTimestamp());
        byte[] signature = Base64.decode(request.getSignature());
        // 更新Excel文件的签名
        logService.updateExcelFileSignature(exportFile, certChain, date, signature);
        return FileDownloadHelper.getResponseEntityByFile(exportFile,
            String.format("Operation_Signed_Log_%s.xlsx", System.currentTimeMillis()), true);
    }

    @GetMapping("/export/file")
    @Operation(description = EXPORT_FILE_OF_SERVICE_LOGS_I18N_KEY, summary = "导出日志")
    public ResponseEntity<byte[]> getExportFile(OpLogExcelRequest request) {
        // 导出用户查询条件
        QueryWrapper<TOpLogDO> queryWrapper = request.toQueryWrapper();
        // 导出指定的字段和设置Excel标题名称
        Map<String, List<String>> enableFieldMapping = request.getEnableFieldMapping();
        // 构造Excel文件，然后将查询的数据添加到文件中
        File exportFile = logService.getExportFile(queryWrapper, enableFieldMapping);
        String fileNameFormat = request.isSecurityLog() ? "Security_%s.xlsx" : "Operation_%s.xlsx";
        return FileDownloadHelper.getResponseEntityByFile(exportFile,
            String.format(fileNameFormat, System.currentTimeMillis()), true);
    }
}
