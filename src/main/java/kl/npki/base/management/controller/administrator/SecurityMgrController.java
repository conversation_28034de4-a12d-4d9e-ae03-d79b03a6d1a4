package kl.npki.base.management.controller.administrator;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.constant.CertStatus;
import kl.npki.base.core.constant.UserStatus;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.model.admin.request.CheckListRequest;
import kl.npki.base.management.model.admin.response.AdminInfoDetailResponse;
import kl.npki.base.management.model.admin.response.AdminInfoListResponse;
import kl.npki.base.management.utils.ValidateHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.common.log.resolver.SecurityOperationLogResolver;
import kl.npki.management.core.biz.admin.model.dto.AdminInfoListDTO;
import kl.npki.management.core.biz.admin.model.info.AdminInfoDetailInfo;
import kl.npki.management.core.biz.admin.model.info.AdminInfoListInfo;
import kl.npki.management.core.repository.IAdminInfoMgrRepository;
import kl.npki.management.core.repository.IRoleRepository;
import kl.npki.management.core.service.SecurityService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Set;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * <AUTHOR>
 * @date 2022/8/24
 * @desc 安全管理员管理
 */
@RestController
@RequestMapping("/security")
@Tag(name = "安全管理员管理")
@LogCollector(resolver = SecurityOperationLogResolver.class)
public class SecurityMgrController implements BaseController {

    @Resource
    private SecurityService securityService;

    @Resource
    private IAdminInfoMgrRepository adminMgrRepostiry;

    @Resource
    private IRoleRepository roleRepository;

    @Resource
    private ConvertService convertService;

    @Resource
    private ValidateHelper validateUtil;

    /**
     * 待审核列表
     *
     * @api {GET} /security/checkList 待审核列表
     * @apiDescription 待审核列表
     * @apiPermission 安全管理员
     * @apiGroup security
     * @apiName checkList
     * @apiUse page
     * @apiParam {String} [username] 管理员姓名
     * @apiParam {String} [roleName] 角色名称
     * @apiParamExample {json} Request-Example:
     * {
     * "currentPage": 1,
     * "pageSize": 10,
     * "username": "张三",
     * "roleName": "业务管理员",
     * "leCreateAt": "2022-10-09 10:00:50",
     * "geCreateAt": "2022-10-10 10:00:50",
     * "quick":true
     * <p>
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @GetMapping("/checkList")
    @Operation(description = PENDING_REVIEW_LIST_I18N_KEY, summary = "待审核列表")
    public RestResponse<IPage<AdminInfoListResponse>> checkList(PageInfo pageInfo, CheckListRequest checkListRequest) {
        // 参数检查
        validateUtil.validate(checkListRequest);
        AdminInfoListDTO adminInfoListQuery = convertService.convert(checkListRequest, AdminInfoListDTO.class);
        IPage<AdminInfoListInfo> page = pageInfo.toPage();
        Set<String> roleCodeSet = roleRepository.getQueryRoleCodeSet(RequestContextHolder.getLoginUserRoles());
        if (CollectionUtils.isEmpty(roleCodeSet)) {
            return RestResponse.success();
        }
        // 待审核状态集合
        Set<Integer> userStatusSet = UserStatus.getToBeCheckedStatusSet();
        Set<Integer> certStatusSet = CertStatus.getToBeCheckedStatusSet();
        IPage<AdminInfoListInfo> adminInfoPages = adminMgrRepostiry.queryForToBeCheckedList(page, adminInfoListQuery, roleCodeSet, userStatusSet, certStatusSet);
        IPage<AdminInfoListResponse> adminInfoListResponse = adminInfoPages.convert(adminInfoPage -> convertService.convert(adminInfoPage, AdminInfoListResponse.class));
        return RestResponse.success(adminInfoListResponse);
    }

    /**
     * 审核通过
     *
     * @api {PUT} /security/checkPass/:adminInfoId 审核通过
     * @apiDescription 审核通过
     * @apiPermission 安全管理员
     * @apiGroup security
     * @apiName checkPass
     * @apiParam {Number} adminInfoId 管理员信息id
     * @apiParamExample {json} Request-Example:
     * {
     * "adminInfoId": 4711
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @Operation(description = APPROVED_BY_REVIEW_I18N_KEY, summary = "审核通过")
    @PutMapping("/checkPass/{adminInfoId}")
    public RestResponse<String> checkPass(@PathVariable("adminInfoId") Long adminInfoId) {
        securityService.checkPass(adminInfoId);
        return RestResponse.success();
    }

    /**
     * 审核拒绝
     *
     * @api {PUT} /security/checkFail/:adminInfoId 审核拒绝
     * @apiDescription 审核拒绝
     * @apiPermission 安全管理员
     * @apiGroup security
     * @apiName checkFail
     * @apiParam {Number} adminInfoId 管理员信息id
     * @apiParamExample {json} Request-Example:
     * {
     * "adminInfoId": 4711
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @PutMapping("/checkFail/{adminInfoId}")
    @Operation(description = REVIEW_REJECTED_I18N_KEY, summary = "审核拒绝")
    public RestResponse<String> checkFail(@PathVariable("adminInfoId") Long adminInfoId) {
        securityService.checkFail(adminInfoId);
        return RestResponse.success();
    }

    /**
     * 查看审核详情
     *
     * @api {GET} /security/checkDetail 查看审核详情
     * @apiDescription 查看审核详情
     * @apiPermission 安全管理员
     * @apiGroup security
     * @apiName checkDetail
     * @apiParam {Number} adminInfoId 管理员信息id
     * @apiParamExample {json} Request-Example:
     * {
     * "adminInfoId": 4711
     * }
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @GetMapping("/checkDetail")
    @Operation(description = VIEW_AUDIT_DETAILS_I18N_KEY, summary = "查看审核详情")
    public RestResponse<AdminInfoDetailResponse> checkDetail(@RequestParam("adminInfoId") Long adminInfoId) {
        Set<String> roleCodeSet = roleRepository.getQueryRoleCodeSet(RequestContextHolder.getLoginUserRoles());
        if (CollectionUtils.isEmpty(roleCodeSet)) {
            return RestResponse.success();
        }
        AdminInfoDetailInfo adminDetail = adminMgrRepostiry.queryAdminDetail(adminInfoId, roleCodeSet);
        AdminInfoDetailResponse detailResponse = convertService.convert(adminDetail, AdminInfoDetailResponse.class);
        return RestResponse.success(detailResponse);
    }
}