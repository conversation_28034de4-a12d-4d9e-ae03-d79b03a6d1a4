package kl.npki.base.management.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.biz.check.SelfCheckManager;
import kl.npki.base.core.biz.check.model.SelfCheckResult;
import kl.npki.base.core.biz.check.model.Status;
import kl.npki.base.management.model.check.DetailedSelfCheckResponse;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * 通用自检后端控制器入口
 *
 * <AUTHOR>
 * @date 2023/10/17
 */
@RestController
@RequestMapping("selfCheck")
@Tag(name = "系统自检后端控制器")
@LogCollector(resolver = OperationLogResolver.class)
public class SelfCheckController implements BaseController {

    @Resource
    private ConvertService convertService;

    @PostMapping
    @Operation(description = EXECUTE_ALL_SERVICE_SELF_CHECK_ITEMS_I18N_KEY, summary = "执行所有服务自检项")
    public RestResponse<Void> check() {
        SelfCheckManager.getInstance().checkAll();
        return RestResponse.success();
    }

    @PostMapping("{code}")
    @Operation(description = EXECUTE_SPECIFIED_SERVICE_SELF_TEST_ITEMS_I18N_KEY, summary = "执行指定的服务自检项")
    public RestResponse<Void> checkById(@PathVariable("code") String code) {
        SelfCheckManager.getInstance().checkItem(code);
        return RestResponse.success();
    }

    @GetMapping("all")
    @Operation(description = QUERY_ALL_SELF_CHECKING_DATA_I18N_KEY, summary = "查询所有自检数据")
    public RestResponse<List<DetailedSelfCheckResponse>> getAll() {
        List<SelfCheckResult> allCheckResults = SelfCheckManager.getInstance().getAllCheckResultByRole(RequestContextHolder.getLoginUserRoles());
        return RestResponse.success(convertService.convert(allCheckResults, DetailedSelfCheckResponse.class));
    }

    @GetMapping(value = "/warnings")
    @Operation(description = QUERY_ALARM_SELF_TEST_DATA_I18N_KEY, summary = "查询告警自检数据")
    public RestResponse<List<DetailedSelfCheckResponse>> getSelfCheckWarnings() {
        List<SelfCheckResult> allCheckResults = SelfCheckManager.getInstance().getAllCheckResultByRole(RequestContextHolder.getLoginUserRoles());
        List<SelfCheckResult> alertCheckResult = new ArrayList<>();
        for (SelfCheckResult result : allCheckResults) {
            if (result.getStatus().equals(Status.FAILURE) || result.getStatus().equals(Status.WARNING)) {
                alertCheckResult.add(result);
            }
        }
        return RestResponse.success(convertService.convert(alertCheckResult, DetailedSelfCheckResponse.class));
    }
}