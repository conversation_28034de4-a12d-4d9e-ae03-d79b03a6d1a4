package kl.npki.base.management.controller.config;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.biz.login.model.LoginTypeInfo;
import kl.npki.base.core.constant.LoginType;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.model.LoginTypeResponse;
import kl.npki.base.management.model.admin.request.UpdateLoginTypeRequest;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.management.core.service.config.LoginTypeConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * 登录模式配置控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/loginType")
@Tag(name = "登录模式配置")
@LogCollector(resolver = OperationLogResolver.class)
public class LoginTypeConfigController implements BaseController {

    @Resource
    private LoginTypeConfigService loginTypeConfigService;

    @Resource
    private ConvertService convertService;

    @PostMapping("/save")
    @Operation(description = SAVE_LOGIN_MODE_I18N_KEY, summary = "保存登录模式")
    public RestResponse<Void> saveLoginType(@RequestBody UpdateLoginTypeRequest updateLoginTypeRequest) {
        String loginTypeStr = updateLoginTypeRequest.getLoginType();
        String loginType = LoginType.valueOfLoginType(loginTypeStr);
        loginTypeConfigService.saveLoginType(loginType);
        return RestResponse.success();
    }

    @GetMapping("/get")
    @Operation(description = GET_LOGIN_MODE_I18N_KEY, summary = "获取登录模式")
    public RestResponse<LoginTypeResponse> getLoginType() {
        LoginTypeInfo loginTypeInfo = loginTypeConfigService.getLoginType();
        LoginTypeResponse loginTypeResponse = convertService.convert(loginTypeInfo, LoginTypeResponse.class);
        return RestResponse.success(loginTypeResponse);
    }

    @GetMapping("/list")
    @Operation(description = GET_LOGIN_MODE_ENUMERATION_LIST_I18N_KEY, summary = "获取登录模式枚举列表")
    public RestResponse<List<LoginTypeResponse>> getLoginTypeList() {
        List<LoginTypeInfo> loginTypeList = loginTypeConfigService.getLoginTypeList();
        return RestResponse.success(convertService.convert(loginTypeList, LoginTypeResponse.class));
    }
}