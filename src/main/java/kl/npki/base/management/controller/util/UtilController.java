package kl.npki.base.management.controller.util;

import com.github.promeg.pinyinhelper.Pinyin;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.i18n.locale.LocaleContext;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.management.constant.I18nOperationLogConstant;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Locale;

/**
 * 通用工具
 *
 * <AUTHOR> rs
 * @date 2024/7/26 18:19
 */

@RestController
@RequestMapping("/util")
@Tag(name = "通用工具")
@LogCollector(resolver = OperationLogResolver.class)
public class UtilController {
    @Resource
    private Environment environment;

    @GetMapping("/env/{envKey}")
    @Operation(
        summary = "获取配置信息",
        description = I18nOperationLogConstant.GET_ENV_VALUE_I18N_KEY
    )
    //message = 获取配置信息
    public RestResponse<String> envValue(@PathVariable(value = "envKey") String envKey) {
        String envValue = environment.getProperty(envKey);
        return RestResponse.success(StringUtils.isNotBlank(envValue) ? envValue : "");
    }

    @GetMapping("/pinyin/convert")
    //message = 拼音转化
    @Operation(description = "kl.npki.base.management.i18n_annotation.pinyin_conversion", summary = "拼音转化")
    public RestResponse<String> pinyinConvert(@RequestParam String name) {
        String pinyin = Pinyin.toPinyin(name, "");
        return RestResponse.success(StringUtils.isNotBlank(pinyin) ? pinyin.toLowerCase() : "");
    }

    @GetMapping("/locale/{locale}")
    //message = 设置语言
    @Operation(description = "kl.npki.base.management.i18n_annotation.set_language", summary = "设置语言")
    public RestResponse<String> setLocale(@PathVariable(value = "locale") String locale) {
        if ("us".equalsIgnoreCase(locale)) {
            LocaleContext.setDefaultLocal(Locale.US);
        } else {
            LocaleContext.setDefaultLocal(Locale.SIMPLIFIED_CHINESE);
        }
        return RestResponse.success();
    }
}