package kl.npki.base.management.controller;

import kl.npki.base.management.common.cache.IndexPageCache;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.base.service.common.context.RequestContextHolder;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

import static kl.npki.base.management.constant.BaseManagementConstant.NONCE_PLACEHOLDER;

/**
 * 处理根路径 ("/") 请求的控制器，用于返回前端首页 HTML 页面，作为门户或主界面入口。
 *
 * <AUTHOR>
 * @since 2025/7/3 下午4:51
 */
@RestController
public class IndexPageController {

    /**
     * 处理根路径 ("/") 的 GET 请求，返回前端首页 HTML 页面。
     * <p>
     * 在返回的 HTML 中动态注入 nonce 值，以支持 CSP 中的
     * {@code script-src 'nonce-...' 'strict-dynamic'} 策略，
     * 从而允许内联 <script> 标签安全执行。
     * </p>
     *
     * @param response HTTP 响应对象，用于输出 index.html 内容
     */
    @GetMapping({"/"})
    public void index(HttpServletResponse response) {
        // 获取 nonce
        String nonce = RequestContextHolder.getContext().getNonce();

        // 替换 nonce
        String cachedIndexHtml = IndexPageCache.INSTANCE.get();
        String html = cachedIndexHtml.replace(NONCE_PLACEHOLDER, nonce);

        // 输出首页
        try {
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setContentType(MediaType.TEXT_HTML_VALUE);
            response.getWriter().write(html);
        } catch (IOException e) {
            throw ManagementInternalError.SYSTEM_ERROR.toException(e);
        }
    }
}