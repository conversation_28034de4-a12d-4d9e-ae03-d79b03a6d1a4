package kl.npki.base.management.controller.config;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.validate.ValidateService;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.model.algo.request.AlgoConfigRequest;
import kl.npki.base.management.model.algo.response.AlgoConfigResponse;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.management.core.biz.config.model.AlgoConfigInfo;
import kl.npki.management.core.service.config.CryptoAlgoConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * 系统密码算法配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/algoConfig")
@Tag(name = "密码算法配置")
@LogCollector(resolver = OperationLogResolver.class)
public class CryptoAlgoConfigController implements BaseController {

    @Resource
    private ValidateService validateService;

    @Resource
    private CryptoAlgoConfigService algoConfigService;

    @GetMapping("/engineAlgorithms")
    @Operation(description = QUERY_THE_PASSWORD_ALGORITHM_CONFIGURATION_SUPPORTED_BY_THE_PASSWORD_MACHINE_I18N_KEY, summary = "查询密码机支持的密码算法配置")
    public RestResponse<AlgoConfigResponse> getEngineSupportAlgo() {
        AlgoConfigInfo algoConfigInfo = algoConfigService.queryEngineSupportAlgo();
        return RestResponse.success(new AlgoConfigResponse(algoConfigInfo.getSimplifySymAlgo(), algoConfigInfo.getAsymAlgo(), algoConfigInfo.getHashAlgo(), algoConfigInfo.getPqcAlgo()));
    }

    @GetMapping("/systemAlgorithms")
    @Operation(description = QUERY_THE_PASSWORD_ALGORITHM_CONFIGURATION_OF_THE_SYSTEM_CONFIGURATION_I18N_KEY, summary = "查询系统配置的密码算法配置")
    public RestResponse<AlgoConfigResponse> getSystemSupportAlgo() {
        AlgoConfigInfo algoConfigInfo = algoConfigService.querySystemSupportAlgo();
        return RestResponse.success(new AlgoConfigResponse(algoConfigInfo.getSimplifySymAlgo(), algoConfigInfo.getAsymAlgo(), algoConfigInfo.getHashAlgo(), algoConfigInfo.getPqcAlgo()));
    }

    @PutMapping("/systemAlgorithms")
    @Operation(description = MODIFY_PASSWORD_ALGORITHM_CONFIGURATION_I18N_KEY, summary = "修改密码算法配置")
    public RestResponse<Void> updateSupportAlgo(@RequestBody AlgoConfigRequest request) {
        //参数校验
        validateService.validate(request);
        algoConfigService.save(request.toAlgoConfigInfo());
        return RestResponse.success();
    }
}