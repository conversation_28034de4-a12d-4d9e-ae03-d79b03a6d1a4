package kl.npki.base.management.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.bean.validate.ValidateService;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.configs.DateFormatConfig;
import kl.npki.base.core.configs.SysConfig;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.management.model.system.request.SysManagerRequest;
import kl.npki.base.management.model.system.response.SysManagerResponse;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * <AUTHOR>
 * @date 2022/12/21
 */
@RestController
@RequestMapping("/sysInfo")
@Tag(name = "系统信息配置管理")
@LogCollector(resolver = OperationLogResolver.class)
public class SysInfoController implements BaseController {

    @Resource
    private ValidateService validateService;

    @Resource
    private ConvertService convertService;

    /**
     * 获取KM系统信息以及KM相关配置
     *
     * @return
     */
    @GetMapping("/sysManager")
    @Operation(description = OBTAIN_SYSTEM_INFORMATION_RELATED_CONFIGURATIONS_I18N_KEY, summary = "获取系统信息相关配置")
    public RestResponse<SysManagerResponse> getSysManager() {
        // 获取系统信息相关配置
        SysInfoConfig sysInfoConf = BaseConfigWrapper.getSysInfoConfig();
        SysConfig sysConfig = BaseConfigWrapper.getSysConfig();
        SysManagerResponse sysManagerResponse = SysManagerResponse.buildSysManagerResponse(sysInfoConf);
        // 是否显示微软设备
        sysManagerResponse.setShowCSP(sysConfig.isShowCSP());
        return RestResponse.success(sysManagerResponse);
    }

    @PostMapping("/update")
    @Operation(description = UPDATE_SYSTEM_INFORMATION_CONFIGURATION_I18N_KEY, summary = "更新系统信息配置")
    public RestResponse<String> updateSysManager(@RequestBody SysManagerRequest sysManagerRequest) {
        validateService.validate(sysManagerRequest);
        // 更新系统信息相关配置
        SysInfoConfig sysInfoConfig = ConfigHolder.get().get(SysInfoConfig.class);
        if (Objects.isNull(sysInfoConfig)) {
            sysInfoConfig = convertService.convert(sysManagerRequest, SysInfoConfig.class);
        }
        ConfigHolder.get().save(sysManagerRequest.updateSysInfoConfig(sysInfoConfig));
        return RestResponse.success();
    }

    @GetMapping("/license/agreement")
    @Operation(description = CHECK_IF_THE_USER_AGREES_TO_USE_THE_LICENSE_AGREEMENT_I18N_KEY, summary = "查询用户是否同意使用许可协议")
    public RestResponse<Boolean> getUserLicenseAgreement() {
        // 获取系统信息相关配置
        SysInfoConfig sysInfoConf = BaseConfigWrapper.getSysInfoConfig();
        // 获取证书相关的配置
        return RestResponse.success(sysInfoConf.isUserLicenseAgreement());
    }

    @PostMapping("/license/agreement")
    @Operation(description = THE_USER_AGREES_TO_USE_THE_LICENSE_AGREEMENT_I18N_KEY, summary = "用户同意使用许可协议")
    public RestResponse<Void> updateUserLicenseAgreement() {
        // 获取系统信息相关配置
        SysInfoConfig sysInfoConf = BaseConfigWrapper.getSysInfoConfig();
        sysInfoConf.setUserLicenseAgreement(true);
        ConfigHolder.get().save(sysInfoConf);
        return RestResponse.success();
    }

    @GetMapping("/dataTime")
    @Operation(description = SYSTEM_DATETIME_SETTINGS_I18N_KEY, summary = "系统日期格式化设置")
    public RestResponse<String> getDateTime() {
        // 获取时间日期格式化配置类
        DateFormatConfig dateFormatConfig = BaseConfigWrapper.getDateFormatConfig();
        // 获取日期格式化相关的配置
        return RestResponse.success(dateFormatConfig.getDateTimePattern());
    }

}