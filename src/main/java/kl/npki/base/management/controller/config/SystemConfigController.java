package kl.npki.base.management.controller.config;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.log.collect.LogCollector;
import kl.npki.base.core.biz.config.model.ConfigProgressInfo;
import kl.npki.base.core.configs.SysConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.base.management.model.system.request.SysConfigRequest;
import kl.npki.base.management.model.system.response.SysConfigResponse;
import kl.npki.base.management.model.system.response.SystemConfigurationResponse;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.management.core.biz.config.model.SystemConfigEntity;
import kl.npki.management.core.service.config.SystemConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * <AUTHOR>
 * @Date 2022/8/24
 */
@RestController
@RequestMapping("/sysConfig")
@Tag(name = "系统配置")
@LogCollector(resolver = OperationLogResolver.class)
public class SystemConfigController implements BaseController {

    @Resource
    private ConvertService convertService;

    @Resource
    private SystemConfigService configService;

    /**
     * 获取系统配置模板
     *
     * @api {get} /config/queryConfTemplate 获取系统配置模板
     * @apiDescription 获取当前系统部署流程信息
     * @apiGroup config
     * @apiName queryConfTemplate
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @Operation(description = OBTAIN_SYSTEM_CONFIGURATION_TEMPLATE_I18N_KEY, summary = "获取系统配置模板")
    @GetMapping("/queryConfTemplate")
    public RestResponse<List<SystemConfigurationResponse>> queryConfTemplate() {
        List<SystemConfigEntity> systemConfTemplate = configService.getSystemConfigurationTemplate();
        List<SystemConfigurationResponse> systemConfRspList = systemConfTemplate.stream().map(v -> convertService.convert(v, SystemConfigurationResponse.class)).collect(Collectors.toList());
        return RestResponse.success(systemConfRspList);
    }

    /**
     * 查询系统是否初始化完成
     *
     * @api {get} /config/initializationIsComplete 查询系统是否初始化完成
     * @apiDescription 查询系统是否初始化完成
     * @apiGroup config
     * @apiName initializationIsComplete
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @Operation(description = QUERY_THE_CURRENT_ENVIRONMENT_OF_THE_SYSTEM_I18N_KEY, summary = "查询系统当前环境")
    @GetMapping("/isCompletedSystemInit")
    public RestResponse<Boolean> isCompletedSystemInit() {
        return RestResponse.success(configService.isCompletedSystemInit());
    }

    /**
     * 完成系统初始化
     *
     * @api {post} /config/doCompleteInitialization 完成系统初始化
     * @apiDescription 完成系统初始化
     * @apiGroup config
     * @apiName doCompleteInitialization
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @Operation(description = COMPLETE_SYSTEM_INITIALIZATION_I18N_KEY, summary = "完成系统初始化")
    @PostMapping("/doCompleteSystemInit")
    public RestResponse<Boolean> doCompleteInitialization() {
        configService.doCompleteSystemInit();
        return RestResponse.success();
    }

    /**
     * 演示环境初始化
     *
     * @api {post} /sysConfig/doDemoInitialization 演示环境初始化
     * @apiDescription 完成演示环境初始化
     * @apiGroup config
     * @apiName doDemoSystemInit
     * @apiUse SuccessResponse
     * @apiUse FailResponse
     */
    @Operation(description = DEMO_ENVIRONMENT_INITIALIZATION_I18N_KEY, summary = "演示环境初始化")
    @PostMapping("/doDemoSystemInit")
    public RestResponse<Boolean> doDemoInitialization() {
        // 系统已经部署，不允许初始化演示环境
        if (SystemUtil.isDeployed()) {
            throw ManagementValidationError.DEMO_INIT_ERROR.toException();
        }
        // 开始初始化演示环境的数据和配置
        configService.doDemoSystemInit();
        return RestResponse.success();
    }

    @Operation(description = QUERY_THE_OVERALL_CONFIGURATION_STATUS_AND_PROGRESS_OF_THE_SYSTEM_I18N_KEY, summary = "查询系统整体配置状态与进度")
    @GetMapping("/status")
    public RestResponse<ConfigProgressInfo> status() {
        return RestResponse.success(configService.obtainConfigProgress());
    }

    @Operation(description = QUERY_SYSTEM_GENERAL_CONFIGURATION_I18N_KEY, summary = "查询系统通用配置")
    @GetMapping("/info")
    public RestResponse<SysConfigResponse> querySysConfig() {
        return RestResponse.success(convertService.convert(BaseConfigWrapper.getSysConfig(), SysConfigResponse.class));
    }

    @Operation(description = SAVE_SYSTEM_GENERAL_CONFIGURATION_I18N_KEY, summary = "保存系统通用配置")
    @PostMapping("/save")
    public RestResponse<Void> saveSysConfig(@RequestBody SysConfigRequest sysConfigRequest) {
        SysConfig tobeSaveConfig = convertService.convert(sysConfigRequest, SysConfig.class);
        configService.saveSysConfig(tobeSaveConfig);
        return RestResponse.success();
    }
}