package kl.npki.base.management.controller.log;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.bean.validate.ValidateService;
import kl.nbase.log.collect.LogCollector;
import kl.nbase.security.util.encoders.Base64;
import kl.nbase.security.utils.CertUtil;
import kl.npki.base.core.biz.log.model.SystemLogFileEntity;
import kl.npki.base.core.biz.log.service.IApiLogService;
import kl.npki.base.core.common.date.CustomLocalDateTimeDeserializer;
import kl.npki.base.core.constant.SystemLogEnum;
import kl.npki.base.management.controller.BaseController;
import kl.npki.base.management.model.log.request.ApiLogExcelPlaintextRequest;
import kl.npki.base.management.model.log.request.ApiLogExcelRequest;
import kl.npki.base.management.model.log.request.ApiLogRequest;
import kl.npki.base.management.model.log.request.LogExcelFileRequest;
import kl.npki.base.management.model.log.response.ApiLogDetailResponse;
import kl.npki.base.management.model.log.response.ApiLogListResponse;
import kl.npki.base.management.model.log.response.LogExcelPlaintextResponse;
import kl.npki.base.management.utils.FileDownloadHelper;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.common.log.resolver.OperationLogResolver;
import kl.npki.base.service.common.query.CursorPageInfo;
import kl.npki.base.service.repository.entity.TApiLogDO;
import kl.npki.base.service.repository.service.IApiLogRepositoryService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static kl.npki.base.management.constant.I18nOperationLogConstant.*;

/**
 * 服务日志controller
 *
 * <AUTHOR>
 * @date 2022/12/13
 */
@RestController
@RequestMapping("/apiLog")
@Tag(name = "服务日志")
@LogCollector(resolver = OperationLogResolver.class)
public class ApiLogController implements BaseController {

    @Resource
    private IApiLogRepositoryService repositoryService;

    @Resource
    private ConvertService convertService;

    @Resource
    private IApiLogService logService;

    @Resource
    private ValidateService validateService;

    /**
     * 分页查询所有的服务日志列表
     *
     * @param pageInfo
     * @return
     */
    @GetMapping("/list")
    @Operation(description = QUERY_SERVICE_LOG_LIST_I18N_KEY, summary = "查询服务日志列表")
    public RestResponse<IPage<ApiLogListResponse>> getApiLogList(CursorPageInfo pageInfo, ApiLogRequest apiLogRequest) {
        apiLogRequest.setEndDate(Objects.isNull(apiLogRequest.getEndDate()) ? LocalDateTime.now() : apiLogRequest.getEndDate());
        QueryWrapper<TApiLogDO> queryWrapper = apiLogRequest.toQueryWrapper();
        IPage<TApiLogDO> apiLogDOList = repositoryService.searchApiLogByCursorPage(pageInfo, queryWrapper);
        IPage<ApiLogListResponse> apiLogResponsePage = apiLogDOList.convert(apiLogDO -> convertService.convert(apiLogDO, ApiLogListResponse.class));
        return RestResponse.success(apiLogResponsePage);
    }

    /**
     * 根据日志id获取单条日志的具体信息
     *
     * @param logId
     * @param logWhen
     * @return
     */
    @GetMapping("/detail/{logId}")
    @Operation(description = QUERY_DETAILED_INFORMATION_OF_SERVICE_LOGS_I18N_KEY, summary = "查询服务日志详细信息")
    public RestResponse<ApiLogDetailResponse> getApiLogInfo(@PathVariable("logId") Long logId, @RequestParam(value = "logWhen", required = false) @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class) LocalDateTime logWhen) {
        TApiLogDO apiLogDO = repositoryService.searchApiLogById(logId, logWhen);
        ApiLogDetailResponse apiLogDetailResponse = convertService.convert(apiLogDO, ApiLogDetailResponse.class);
        return RestResponse.success(apiLogDetailResponse);
    }

    @PostMapping("/export/info")
    @Operation(description = QUERY_EXCEL_ELECTRONIC_SIGNATURE_INFORMATION_LOGS_I18N_KEY, summary = "查询Excel电子签名信息")
    public RestResponse<LogExcelPlaintextResponse> getExportFileInfo(@RequestBody ApiLogExcelPlaintextRequest request) {
        validateService.validate(request);
        request.setEndDate(Objects.isNull(request.getEndDate()) ? LocalDateTime.now() : request.getEndDate());
        // 做签名的证书链
        List<X509Certificate> certChain = request
                .getSignerCertChain()
                .stream()
                .map(Base64::decode)
                .map(CertUtil::derToX509)
                .collect(Collectors.toList());
        // 做签名的时间
        Date date = new Date();
        Long fileId = request.getFileId();
        File exportFile = logService.writeFileContentToFile(fileId);
        // 根据Excel文件获取XAdES签名的签名原文
        byte[] plaintext = logService.getExcelFilePlaintext(exportFile, certChain, date);
        return RestResponse.success(
                new LogExcelPlaintextResponse()
                        .setFileId(String.valueOf(fileId))
                        .setPlaintext(Base64.toBase64String(plaintext))
                        .setTimestamp(date.getTime()));
    }

    @PostMapping("/export/signedFile")
    @Operation(description = EXPORT_SIGNED_FILE_OF_SERVICE_LOGS_I18N_KEY, summary = "导出带签名日志")
    public ResponseEntity<byte[]> getExportSignedFile(@RequestBody LogExcelFileRequest request) {
        validateService.validate(request);
        // 做签名的证书链
        List<X509Certificate> certChain = request
                .getSignerCertChain()
                .stream()
                .map(Base64::decode)
                .map(CertUtil::derToX509)
                .collect(Collectors.toList());
        Long fileId = request.getFileId();
        File exportFile = logService.writeFileContentToFile(fileId);
        Date date = new Date(request.getTimestamp());
        byte[] signature = Base64.decode(request.getSignature());
        // 更新Excel文件的签名
        logService.updateExcelFileSignature(exportFile, certChain, date, signature);
        SystemLogFileEntity systemLogFileEntity = logService.searchSystemLogFileStatus(fileId);
        return FileDownloadHelper.getResponseEntityByFile(exportFile,
                String.format("sign_%s", systemLogFileEntity.getFileName()), Boolean.TRUE);
    }

    @GetMapping("/export/file")
    @Operation(description = EXPORT_FILE_OF_SERVICE_LOGS_I18N_KEY, summary = "导出日志")
    public RestResponse<Long> getExportFile(ApiLogExcelRequest request) {
        QueryWrapper<TApiLogDO> queryWrapper = convertToQueryWrapper(request);
        // 导出指定的字段和设置Excel标题名称
        Map<String, List<String>> enableFieldMapping = request.getEnableFieldMapping();
        Long fileId = logService.getExportFileAndSaveDb(queryWrapper, enableFieldMapping, SystemLogEnum.API_LOG);
        return RestResponse.success(fileId);
    }

    private QueryWrapper<TApiLogDO> convertToQueryWrapper(ApiLogExcelRequest request) {
        request.setEndDate(Objects.isNull(request.getEndDate()) ? LocalDateTime.now() : request.getEndDate());
        // 导出用户查询条件
        QueryWrapper<TApiLogDO> queryWrapper = request.toQueryWrapper();
        LambdaQueryWrapper<TApiLogDO> lambdaQueryWrapper = queryWrapper.lambda();
        // 查询指定字段
        lambdaQueryWrapper.select(TApiLogDO::getId, TApiLogDO::getTraceId,
                TApiLogDO::getSpanId, TApiLogDO::getClientIp,
                TApiLogDO::getLogWhen, TApiLogDO::getBizId,
                TApiLogDO::getBiz, TApiLogDO::getDetail,
                TApiLogDO::getRequest,TApiLogDO::getCallerId,
                TApiLogDO::getCallerName, TApiLogDO::getEntityId,
                TApiLogDO::getCertId);
        return queryWrapper;
    }
}