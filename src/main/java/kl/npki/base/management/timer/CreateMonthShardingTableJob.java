package kl.npki.base.management.timer;

import kl.nbase.db.support.slot.SlotDataSourceConfig;
import kl.nbase.timer.job.AbstractTimerJob;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.BaseJobEnum;
import kl.npki.base.core.constant.EnvironmentEnum;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.service.common.db.impl.MonthShardingServiceImpl;
import kl.npki.base.service.util.MonthShardingUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 创建月份分表定时任务
 *
 * <AUTHOR> G<PERSON>
 * @Date 2023/12/22
 */
@Component
public class CreateMonthShardingTableJob extends AbstractTimerJob {

    @Resource
    private MonthShardingServiceImpl monthShardingServiceImpl;

    @Override
    protected void taskExecute() {
        monthShardingServiceImpl.createTables();
    }

    @Override
    public boolean getEnable() {
        return EnvironmentEnum.isDeployed(BaseConfigWrapper.getSysInfoConfig().getEnvironmentSwitchId());
    }

    @Override
    public String getCron() {
        SlotDataSourceConfig slotDataSourceConfig = ConfigHolder.get().get(SlotDataSourceConfig.class);
        return slotDataSourceConfig.getSharding().getProps().get(MonthShardingUtil.CRON_CONFIG_KEY).toString();
    }

    @Override
    public String getId() {
        return BaseJobEnum.TABLE_SHARDING.getId();
    }

    @Override
    public String getName() {
        return BaseJobEnum.TABLE_SHARDING.getName();
    }

}
