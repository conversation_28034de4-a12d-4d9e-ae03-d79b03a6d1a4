package kl.npki.base.management.utils;

import kl.cloud.sql.util.EntityTransferUtil;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.core.constant.DbConfigType;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.tools.dbtool.ServiceContext;
import kl.tools.dbtool.conf.AppConfig;
import kl.tools.dbtool.task.DbSqlTaskBase;
import kl.tools.dbtool.task.ITask;
import kl.tools.dbtool.task.TaskMgr;
import kl.tools.dbtool.task.bean.TableEntry;
import kl.tools.dbtool.task.source.StructureSourceFactory;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ArrayUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;


/**
 * 初始化H2数据库，且生成对应db文件
 *
 * <AUTHOR>
 */
public class PkiSqlBuildUtil {

    public static final String H2 = "h2";
    /**
     * 建表语句的 base h2sql文件
     */
    public static final String BASE_H2SQL_FILE = "base-h2.sql";

    /**
     * 建表语句的 base mysql 文件
     */
    public static final String BASE_MYSQL_FILE = "base-mysql.sql";

    /**
     * 初始数据语句的 base sql 文件
     */
    public static final String BASE_INSERT_MYSQL_FILE = "base.sql";

    /**
     * 基础数据的sql文件目录
     */
    public static final String INSERT_DATA_DIR = "/data";
    /**
     * 测试数据的sql文件目录
     */
    public static final String DEMO_DATA_DIR = "/demo";

    /**
     * 合并后sql存放的目录
     */
    public static final String MERGE_SQL_DIR = "/sql";

    protected static final String[] SQL_EXTENSIONS = new String[]{"sql"};
    /**
     * 支持的数据库类型
     */
    private static final List<DbConfigType> supportedDatabaseTypes = Arrays.asList(DbConfigType.MYSQL,
            DbConfigType.H2, DbConfigType.ORACLE,
            DbConfigType.DM, DbConfigType.KINGBASE,
            DbConfigType.GREATDB, DbConfigType.GBASE_B,
            DbConfigType.GAUSSDB);

    /**
     * 存放Mysql建表语句的集合，有序，base在前，km在后
     */
    private static List<File> ddlMySqlFile = new ArrayList<>();
    /**
     * 存放H2建表语句的集合，有序，base在前，km在后
     */
    private static List<File> ddlH2SqlFile = new ArrayList<>();
    /**
     * 存放初始数据插入的sql文件集合，有序，base在前，km在后
     */
    private static List<File> dmlSqlFile = new ArrayList<>();
    /**
     * 存放初始测试数据插入的sql文件集合，有序，base在前，km在后
     */
    private static List<File> demoSqlFile = new ArrayList<>();

    public static void main(String[] args) throws Exception {
        if (ArrayUtils.isEmpty(args)) {
            throw ManagementValidationError.PARAM_ERROR.toException();
        }
        String tmpSqlPath = FilenameUtils.normalize(args[0]);
        String sqlPath = FilenameUtils.normalize(args[1]);
        // 生成不同数据库sql文件
        genSqlFile(sqlPath);
        // 扫描目录,初始化sql文件集合
        initSqlFileList(tmpSqlPath);
        // 合并文件
        mergeSqlFile(sqlPath);
    }

    public static void genSqlFile(String resourcePath) throws Exception {
        // 创建sql目录
        String dir = resourcePath + MERGE_SQL_DIR;
        //SQL语句输出路径二次封装
        AppConfig appConfig = ServiceContext.getConfig();
        appConfig.OUTPUT_DIR = dir;

        //设置包路径 获取java bean 定义
        List<TableEntry> tableEntryList = EntityTransferUtil.getTableEntries(BaseConstant.BASE_PACKAGE);
        StructureSourceFactory.setTableEntries(tableEntryList);
        for (DbConfigType dbConfigType : supportedDatabaseTypes) {
            //SQL语句生成
            ITask task =
                TaskMgr.getInstance().getTaskWithCheck(DbSqlTaskBase.SqlTaskShortOptionNameType.valueOf(dbConfigType.toString()).name());
            task.doTask();
        }

    }

    /**
     * 初始化sql文件集合
     *
     * @param tmpSqlPath
     */
    private static void initSqlFileList(String tmpSqlPath) {

        // 1. 合并DML sql文件
        dmlSqlFile.add(new File(tmpSqlPath + INSERT_DATA_DIR, BASE_INSERT_MYSQL_FILE));
        Collection<File> dataFiles = FileUtils.listFiles(new File(tmpSqlPath + INSERT_DATA_DIR), SQL_EXTENSIONS, false);
        for (File sqlFile : dataFiles) {
            String name = sqlFile.getName();
            if (BASE_INSERT_MYSQL_FILE.equals(name)) {
                continue;
            }
            dmlSqlFile.add(sqlFile);
        }

        // 2. 合并 demo sql文件
        demoSqlFile.add(new File(tmpSqlPath + DEMO_DATA_DIR, BASE_INSERT_MYSQL_FILE));
        Collection<File> demoFiles = FileUtils.listFiles(new File(tmpSqlPath + DEMO_DATA_DIR), SQL_EXTENSIONS, false);
        for (File sqlFile : demoFiles) {
            String name = sqlFile.getName();
            if (BASE_INSERT_MYSQL_FILE.equals(name)) {
                continue;
            }
            demoSqlFile.add(sqlFile);
        }

    }

    private static void mergeSqlFile(String resourcePath) throws IOException {
        // 创建sql目录
        String dir = resourcePath + MERGE_SQL_DIR;

        // 1. 合并初始数据sql文件
        File dataFile = new File(Paths.get(dir, INSERT_DATA_DIR, "npki.sql").toString());
        mergeFile(dataFile, dmlSqlFile);
        // 2. 合并测试初始数据sql文件
        File demoFile = new File(Paths.get(dir, INSERT_DATA_DIR, "npki-demo.sql").toString());
        mergeFile(demoFile, demoSqlFile);

    }

    private static void mergeFile(File destFile, List<File> files) throws IOException {
        File parentFile = destFile.getParentFile();
        if (!parentFile.exists()) {
            boolean success = parentFile.mkdir();
            if (!success) {
                throw ManagementInternalError.FILE_CREATE_FAIL.toException("Could not create directory: " + parentFile.getAbsolutePath());
            }
        }
        if (!destFile.exists()) {
            boolean success = destFile.createNewFile();
            if (!success) {
                throw ManagementInternalError.FILE_CREATE_FAIL.toException("Could not create file: " + destFile.getAbsolutePath());
            }
        }

        StringBuilder content = new StringBuilder();
        for (File file : files) {
            content.append(FileUtils.readFileToString(file, StandardCharsets.UTF_8));
            content.append("\r\n");
        }
        FileUtils.writeStringToFile(destFile, content.toString(), StandardCharsets.UTF_8);
    }
}
