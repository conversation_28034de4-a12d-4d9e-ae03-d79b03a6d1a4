package kl.npki.base.management.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.Collection;
import java.util.List;
import java.util.regex.Matcher;

/**
 * <AUTHOR>
 * @date 2021/12/15
 */
public enum AppPathUtil {
	/**
	 * 单例实列
	 */
	INSTANCE,
	;
	/**
	 * 打包后相对于项目根路径的资源路径
	 */
	private static final String RELATIVE_SOURCE_PATH = "config";

	private String sourcePath;
	private String rootPath;

	AppPathUtil() {
		init();
	}

	private void init() {
		//获取resource资源路径
		File file = new File("");
		rootPath = file.getAbsolutePath();
		try {
			// idea环境获取资源路径
			sourcePath = ResourceUtils
				             .getURL("classpath:")
				             .getPath();
		} catch (FileNotFoundException e) {
			//获取不到资源路径，判断系统在服务器上运行
			sourcePath = rootPath + File.separator + RELATIVE_SOURCE_PATH;
		}
	}

	/**
	 * 获取系统根路径
	 *
	 * @return
	 */
	public String getRootPath() {
		return rootPath;
	}

	/**
	 * 获取resource路径
	 *
	 * @return
	 */
	public String getSourcePath() {
		return sourcePath;
	}

	/**
	 * 获取应用根路径绝对路径
	 *
	 * @param relativePath 相对路径
	 *
	 * @return
	 */
	public String getRootAbsolutPath(String relativePath) {
		return replaceSeparator(rootPath + File.separator + relativePath);
	}

	/**
	 * 获取resource绝对路径
	 *
	 * @param relativePath 相对路径
	 *
	 * @return 绝对路径
	 */
	public String getSourceAbsolutPath(String relativePath) {
		return replaceSeparator(sourcePath + File.separator + relativePath);
	}

	/**
	/**
	 * 获取相对路径
	 *
	 * @param pathVariable 路径参数列表
	 *                     <p>
	 *                     将集合中的路径参数以当前系统的路径分隔符串联起来
	 *                     </p>
	 *
	 * @return
	 */
	public String getRelativePath(List<String> pathVariable) {
		StringBuilder builder = new StringBuilder();
		for (String path : pathVariable) {
			if (StringUtils.isNotEmpty(path)) {

				//判断相对路径第一个字符是否有路径分隔符,没有则拼接一个
				if ((File.separatorChar != path
					                           .trim()
					                           .charAt(0))) {
					builder.append(File.separator);
				}
				builder.append(path);
			}
		}
		//将路径分隔符替换为当前系统的路径分隔符
		return replaceSeparator(builder.toString());
	}

	/**
	 * 将指定目录包括子目录下的所有文件保存到集合中
	 *
	 * @param file 文件夹
	 * @param fileList 存放文件夹中的文件的集合
	 * @param isAll 是否所有子路径下的文件，false 则只查询当前路径下的不包括子路径
	 * @throws FileNotFoundException
	 */

	public void getFileList(File file, Collection<File> fileList, boolean isAll) throws FileNotFoundException {
		File[] fs = file.listFiles();
		if (fs == null) {
			throw new FileNotFoundException("the file not exist");
		}
		for (File f : fs) {
			//若是目录，则递归打印该目录下的文件
			if (f.isDirectory() && isAll) {
				getFileList(f, fileList, true);
			}
			//若是文件，直接放入list
			if (f.isFile()) {
				fileList.add(f);
			}
		}
	}

	private String replaceSeparator(String path) {
		//将路径分隔符替换为当前系统的路径分隔符
		return path.replaceAll("[/|\\\\]+", Matcher.quoteReplacement(File.separator));
	}

}