package kl.npki.base.management.utils;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.google.common.io.Files;
import kl.npki.base.management.constant.I18nConstant;
import kl.npki.base.management.exception.ManagementInternalError;
import kl.npki.base.management.exception.ManagementValidationError;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> rs
 * @date 2024/4/8 10:22
 */
public class MultipartFileUtil {


    public static final DateTimeFormatter FILE_NAME_FORMATTER =  DateTimeFormatter.ofPattern("-yyyyMMddHHmmssSSS");


    private MultipartFileUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * MultipartFile
     *
     * @return
     */
    public static String multipartFileToString(MultipartFile file) throws IOException {
        return inputStreamToString(file.getInputStream());
    }


    /**
     * InputStream
     *
     * @return
     */
    public static String inputStreamToString(InputStream inputStream) throws IOException {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            return reader.lines().collect(Collectors.joining(System.lineSeparator()));
        }
    }

    /**
     * 文件名称添加日期
     *
     * @param fileName 文件名称
     * @return
     */
    public static String fileNameAddDate(String fileName) {
        String nameWithoutExtension = Files.getNameWithoutExtension(fileName);
        String fileExtension = Files.getFileExtension(fileName);
        return nameWithoutExtension + LocalDateTime.now().format(FILE_NAME_FORMATTER) + "." + fileExtension;
    }

    /**
     * 获取临时文件
     *
     * @param fileContent 文件内容
     * @param fileName    文件名称
     * @return
     */
    public static File getTempFile(byte[] fileContent, String fileName) {
        String tempDirectoryPath = FileUtils.getTempDirectoryPath();
        File tempFile = new File(tempDirectoryPath + fileName);
        try {
            FileCopyUtils.copy(fileContent, tempFile);
        } catch (IOException e) {
            throw ManagementInternalError.FILE_OPERATE_ERROR.toException(e.getMessage(), e);
        }
        return tempFile;
    }

    /**
     * 生成临时文件
     *
     * @param fileContent 文件内容
     * @param tempDir     临时文件夹
     * @param fileName    文件名称
     * @return  临时文件
     */
    public static File getTempFile(File tempDir, String fileName, byte[] fileContent) {
        File tempFile = null;
        try {
            tempFile = new File(tempDir.getAbsoluteFile(), fileName);
            FileUtils.writeByteArrayToFile(tempFile, fileContent);
        } catch (IOException e) {
            throw ManagementInternalError.FILE_OPERATE_ERROR.toException(e.getMessage(), e);
        }
        return tempFile;
    }

    /**
     * 生成临时文件并返回文件路径
     *
     * @param fileContent 文件内容
     * @param tempDir     临时文件夹
     * @param fileName    文件名称
     * @return  临时文件绝对路径
     */
    public static String genTempFileAbsolutePath(File tempDir, String fileName, byte[] fileContent) {
        File file = getTempFile(tempDir, fileName, fileContent);
        return ObjectUtils.isNotEmpty(file) ? file.getAbsolutePath() : null;
    }

    public static int getExcelContentCount(MultipartFile file, int headLength) {
        File tempFile;
        try {
            String fileName = file.getOriginalFilename();
            if (null == fileName) {
                throw ManagementValidationError.UPLOAD_FILE_NAME_ERROR.toException();
            }
            fileName = fileNameAddDate(fileName);
            tempFile = getTempFile(file.getBytes(), fileName);
        } catch (IOException e) {
            throw ManagementInternalError.FILE_OPERATE_ERROR.toException(e.getMessage(), e);
        }
        AtomicInteger count = new AtomicInteger();
        EasyExcelFactory.read(tempFile).registerReadListener(new ReadNumberRowsListener(count)).sheet().doRead();
        FileUtils.deleteQuietly(tempFile);
        // 减掉头行
        return count.intValue() - headLength;
    }

    private static class ReadNumberRowsListener implements ReadListener<Object> {
        private final AtomicInteger count;

        public ReadNumberRowsListener(AtomicInteger count) {
            this.count = count;
        }

        @Override
        public void invoke(Object data, AnalysisContext context) {
            // do nothing
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            count.set(context.readSheetHolder().getApproximateTotalRowNumber());
        }
    }

    /**
     * 获取Excel文件头
     *
     * @param cls
     * @param enableFieldMapping
     * @return
     */
    public static List<List<String>> generateHeader(Class<?> cls, Map<String, String> enableFieldMapping) {
        List<List<String>> head = new ArrayList<>();
        Field[] declaredFields = FieldUtils.getFieldsWithAnnotation(cls, ExcelProperty.class);
        boolean existFieldMapping = MapUtils.isNotEmpty(enableFieldMapping);
        Arrays.stream(declaredFields).forEach(declaredField -> {
            String fieldName = declaredField.getName();
            if (existFieldMapping  && !enableFieldMapping.containsKey(fieldName)) {
                //不允许操作
                return;
            }
            ExcelProperty annotation = declaredField.getAnnotation(ExcelProperty.class);
            String[] value = annotation.value();
            if (ObjectUtils.isEmpty(value)) {
                return;
            }
            if (existFieldMapping) {
                String showFieldName = enableFieldMapping.get(fieldName);
                if (StringUtils.isNotBlank(showFieldName)) {
                    //更新为 显示名称
                    value[value.length - 1] = showFieldName;
                }
            }
            if (value.length > 1) {
                // 导入表头添加
                head.add(Arrays.asList(value));
            } else {
                // 导出从实体取翻译
                String i18nValue = I18nConstant.getBaseMgmtI18nMessage(value[0], cls.getSimpleName() + "." + declaredField.getName());
                head.add(Collections.singletonList(i18nValue));
            }
        });
        return head;
    }
}
