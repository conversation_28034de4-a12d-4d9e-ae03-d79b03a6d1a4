package kl.npki.base.management.utils;

import kl.nbase.db.core.DataSourceContext;
import kl.npki.base.core.biz.mainkey.model.NakedMainKeyEntity;
import kl.npki.base.core.biz.mainkey.service.MainKeyManager;
import kl.npki.base.core.constant.EnvironmentEnum;
import kl.npki.base.core.tenantholders.TenantContextHolder;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.util.EnvironmentUtil;

/**
 * JWT SecretKey工具类
 *
 * <AUTHOR>
 */
public class JwtSecretKeyUtil {

    public static byte[] getSecretKey() {
        boolean isDeploying = EnvironmentUtil.isDeploying();
        if (isDeploying) {
            String tenantIdForQuery = EnvironmentEnum.DEMO.getId();
            // 部署阶段，使用demo租户的主密钥
            TenantContextHolder.setTenantId(tenantIdForQuery);
            DataSourceContext.getTenantSession().setTenantId(tenantIdForQuery);
        }

        NakedMainKeyEntity currentMainKey = MainKeyManager.getInstance().getCurrentMainKey();

        if (isDeploying) {
            // 部署阶段，查询完成后恢复到实际请求的原租户
            String originTenantId = RequestContextHolder.getContext().getTenantId();
            TenantContextHolder.setTenantId(originTenantId);
            DataSourceContext.getTenantSession().setTenantId(originTenantId);
        }

        return currentMainKey.getSecretKey();
    }
}
