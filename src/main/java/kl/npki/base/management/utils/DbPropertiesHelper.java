package kl.npki.base.management.utils;

import kl.nbase.db.constant.DbRoleTypeEnum;
import kl.nbase.db.support.druid.DruidDataSourceProperties;
import kl.nbase.db.support.druid.SlotDruidDataSourceProperties;
import kl.nbase.db.support.sharding.config.ShardingConfig;
import kl.nbase.db.support.slot.SlotDataSourceConfig;
import kl.npki.management.core.biz.config.model.DbConfigInfo;
import kl.npki.base.management.model.db.response.DbConfigResponse;
import kl.npki.base.core.constant.DbConfigType;
import kl.npki.management.core.utils.DbUrlUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.database.core.connector.url.JdbcUrl;
import org.apache.shardingsphere.infra.database.core.connector.url.StandardJdbcUrlParser;

import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 数据库配置转换类
 * <AUTHOR>
 */
public class DbPropertiesHelper {

    private DbPropertiesHelper() {
        throw new IllegalStateException("Utility class");
    }


    private static final Pattern ORACLE_URL_PATTERN = Pattern.compile(
        "**************************):@(//)?(?<host>.+):(?<port>\\d+)[:/](?<dbName>\\w+)", Pattern.CASE_INSENSITIVE);

    private static final Pattern H2_URL_PATTERN = Pattern.compile("jdbc:h2:((?<modelMem>mem|~)[:/]" +
        "(?<catalog>[\\w\\-]+)|"
        + "(?<modelSslOrTcp>ssl:|tcp:)(//)?(?<host>[\\w\\-.]+)(:(?<port>\\d{1,4})/)?[/~\\w\\-.]+/(?<name>[\\-\\w]*)|"
        + "(?<modelFile>file:)[/~\\w\\-]+/(?<fileName>[\\-\\w]*));?\\S*", Pattern.CASE_INSENSITIVE);

    public static DbConfigResponse translate2DbConfigResponse(SlotDataSourceConfig slotDataSourceConfig) {
        SlotDruidDataSourceProperties druid = slotDataSourceConfig.getDruid();
        List<DruidDataSourceProperties> datasource = slotDataSourceConfig.getDatasource();
        ShardingConfig sharding = slotDataSourceConfig.getSharding();
        // 根据驱动解析数据库类型
        String driverClassName = druid.getDriverClassName();
        DbConfigType dbConfigType = DbConfigType.getDbConfigTypeByDriver(driverClassName);
        List<DbConfigInfo> dbConfigInfoList = datasource.stream()
            .map(dbConfig -> {
                DbConfigInfo dbConfigInfo = new DbConfigInfo();
                // 解析数据库url,获取 ip 端口
                String dbUrl = dbConfig.getUrl();
                String charSet = StringUtils.isNotBlank(dbConfig.getCharSet()) ? dbConfig.getCharSet() : DbUrlUtil.getCharSetFromUrl(dbUrl, dbConfigType);
                dbConfigInfo.setCharSet(charSet);
                // oracle url比较特殊，需要特殊处理
                if (dbConfigType != null && DbConfigType.ORACLE.getDbTypeName().equals(dbConfigType.getDbTypeName())) {
                    Matcher m = ORACLE_URL_PATTERN.matcher(dbUrl);
                    if (m.find()) {
                        dbConfigInfo.setDbAddress(m.group("host"));
                        dbConfigInfo.setDbPort(Integer.parseInt(m.group("port")));
                        dbConfigInfo.setDbName(m.group("dbName"));
                    }
                } else if (dbConfigType != null && DbConfigType.H2.getDbTypeName().equals(dbConfigType.getDbTypeName())) {
                    Matcher m = H2_URL_PATTERN.matcher(dbUrl);
                    if (m.find()) {
                        dbConfigInfo.setDbAddress(m.group("host"));
                        String port = m.group("port");
                        if (StringUtils.isNotBlank(port)) {
                            dbConfigInfo.setDbPort(Integer.parseInt(m.group("port")));
                        }
                        dbConfigInfo.setDbName(m.group("fileName"));
                    }
                } else {
                    // 其他数据库使用sharding 解析
                    if (StringUtils.isNotBlank(dbUrl)) {
                        JdbcUrl jdbcUrl = new StandardJdbcUrlParser().parse(dbUrl);
                        dbConfigInfo.setDbAddress(jdbcUrl.getHostname());
                        if (jdbcUrl.getPort() == -1 && dbConfigType != null) {
                            dbConfigInfo.setDbPort(dbConfigType.getDefaultPort());
                        } else {
                            dbConfigInfo.setDbPort(jdbcUrl.getPort());
                        }
                        dbConfigInfo.setDbName(StringUtils.isBlank(jdbcUrl.getDatabase()) ?
                            jdbcUrl.getQueryProperties().getProperty("schema") : jdbcUrl.getDatabase());
                    }
                }

                dbConfigInfo.setDbRoleType(Optional.ofNullable(dbConfig.getDbRoleType()).orElse(DbRoleTypeEnum.MASTER).getValue());
                dbConfigInfo.setUsername(dbConfig.getUsername());
                return dbConfigInfo;
            })
            .collect(Collectors.toList());
        DbConfigResponse dbConfigResponse = new DbConfigResponse();
        dbConfigResponse.setDbConfigInfoList(dbConfigInfoList);
        if (dbConfigType != null) {
            dbConfigResponse.setDbType(dbConfigType.name());
        }
        dbConfigResponse.setMaxConnect(druid.getMaxActive());
        dbConfigResponse.setEnableReadWrite(sharding.isReadWriteEnabled());
        return dbConfigResponse;
    }


}
