package kl.npki.base.management.utils;

import kl.nbase.bean.validate.ValidateService;
import kl.nbase.helper.utils.CheckUtils;
import kl.npki.base.management.exception.ManagementValidationError;
import kl.npki.management.core.constants.RoleCodeEnum;
import kl.npki.management.core.repository.IRoleRepository;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static kl.npki.base.management.constant.I18nExceptionInfoConstant.THE_ROLE_NOT_EXIST_I18N_KEY;

/**
 * <AUTHOR>
 * @date 2022/10/1
 * @desc
 */
@Component
public class ValidateHelper {
    @Resource
    private ValidateService validateService;
    
    @Resource
    private IRoleRepository roleService;
    
    public void validate(Object o) {
        try {
            validateService.validate(o);
        } catch (Exception e) {
            throw ManagementValidationError.PARAM_ERROR.toException(e);
        }
    }
    
    /**
     * 参数校验
     *
     * @param o      校验对象
     * @param roleCode 角色id
     */
    public void validate(Object o, String roleCode) {
        try {
            validateService.validate(o);
        } catch (Exception e) {
            throw ManagementValidationError.PARAM_ERROR.toException(e);
        }
        roleExistValidate(roleCode);
    }
    
    /**
     * 角色存在校验
     *
     * @param roleCode 角色编码
     */
    public void roleExistValidate(String roleCode) {
        RoleCodeEnum adminRole = RoleCodeEnum.getRoleCodeEnumByRoleCode(roleCode);
        // 从系统角色中找到则不在进行数据库查询
        if (adminRole != null) {
            return;
        }
        // 不是系统角色,为自定义角色,需要从数据库查询
        Boolean exists = roleService.checkRoleExists(roleCode);
        CheckUtils.isTrue(exists, ManagementValidationError.PARAM_ERROR.toException(THE_ROLE_NOT_EXIST_I18N_KEY));
    }
}
