package kl.npki.base.management.utils;

import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseCookie;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since  2022/9/14
 */
public class AuthzUtil {
    /**
     * 临时会话id
     */
    public static final String TEMP_SESSION_ID = "TempSessionId";

    private AuthzUtil() {
    }

    public static void setAuthorization(String value, HttpServletResponse response) {
        // 设置Authorization Header
        response.setHeader(HttpHeaders.AUTHORIZATION, value);
    }

    /**
     * 清除Authorization
     *
     * @param response 响应体
     */
    public static void removeAuthorization(HttpServletResponse response) {
        setAuthorization("", response);
    }

    /**
     * 获取cookie
     *
     * @param request 请求
     * @return value
     */
    public static String getAuthorization(HttpServletRequest request) {
        return request.getHeader(HttpHeaders.AUTHORIZATION);
    }
}
