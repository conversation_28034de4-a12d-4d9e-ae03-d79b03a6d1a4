package kl.npki.base.management.utils;

import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseCookie;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since  2022/9/14
 */
public class AuthzUtil {
    /**
     * 临时会话id
     */
    public static final String TEMP_SESSION_ID = "TempSessionId";

    private AuthzUtil() {
    }

    public static void setAuthorization(String value, HttpServletResponse response) {
        // 设置Authorization Header
        response.setHeader(HttpHeaders.AUTHORIZATION, value);
    }

    /**
     * 清除Authorization
     *
     * @param response 响应体
     */
    public static void removeAuthorization(HttpServletResponse response) {
        setAuthorization("", response);
    }

    /**
     * 获取cookie
     *
     * @param request 请求
     * @return value
     */
    public static String getAuthorization(HttpServletRequest request) {
        return request.getHeader(HttpHeaders.AUTHORIZATION);
    }

    public static void setTempSessionId(String value, HttpServletResponse response) {
        // 设置临时会话id
        setCookie(TEMP_SESSION_ID, value, response);
    }

    /**
     * 清除临时会话id
     *
     * @param response 响应体
     */
    public static void removeTempSessionId(HttpServletResponse response) {
        removeCookie(TEMP_SESSION_ID, response);
    }

    /**
     * 获取临时会话id
     *
     * @param request 请求
     * @return 临时会话id
     */
    public static String getTempSessionId(HttpServletRequest request) {
        return getCookie(TEMP_SESSION_ID, request);
    }

    public static void setCookie(String key, String value, HttpServletResponse response) {
        // 默认30分钟
        setCookie(key, value, 5 * 60 * 1000L, response);
    }

    /**
     * 设置cookie
     *
     * @param key      key
     * @param value    value
     * @param time     过期时间毫秒
     * @param response 响应体
     */
    public static void setCookie(String key, String value, Long time, HttpServletResponse response) {
        ResponseCookie.ResponseCookieBuilder builder = ResponseCookie
            // key & value
            .from(key, value)
            // 禁止js读取
            .httpOnly(true)
            // path
            .path("/")
            // 失效时间
            .maxAge(time / 1000)
            // 在http下也传输
            .secure(false);
        // 设置Cookie Header
        response.addHeader(HttpHeaders.SET_COOKIE, builder
            .build()
            .toString());
    }

    /**
     * 清除cookie
     *
     * @param key      cookie key
     * @param response 响应体
     */
    public static void removeCookie(String key, HttpServletResponse response) {
        setCookie(key, "", 0L, response);
    }

    /**
     * 获取cookie
     *
     * @param key    cookie key
     * @param request 请求
     * @return value
     */
    public static String getCookie(String key, HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        Optional<String> cookie = Optional.empty();
        if (cookies != null && cookies.length > 0) {
            cookie = Arrays
                .stream(cookies)
                .filter(v -> key.equals(v.getName()))
                .findFirst()
                .map(Cookie::getValue);
        }
        return cookie.orElse(null);
    }
}
