package kl.npki.base.management.utils;

import com.ctrip.framework.apollo.core.utils.ClassLoaderUtil;
import kl.npki.base.management.exception.ManagementValidationError;
import org.apache.commons.lang3.ClassUtils;

/**
 * <AUTHOR>
 * @create 2025/5/27 下午5:18
 */
public class SubsystemUtil {

    public static final String CA_MAIN_CLASS_NAME = "kl.npki.ca.management.CaManagementApplication";
    public static final String RA_MAIN_CLASS_NAME = "kl.npki.ra.management.RaManagementApplication";
    public static final String KM_MAIN_CLASS_NAME = "kl.npki.km.management.KmManagementApplication";
    public static final String CLM_MAIN_CLASS_NAME = "kl.npki.clm.management.AcmsManagementApplication";
    public static final String OCSP_MAIN_CLASS_NAME = "kl.npki.ocsp.management.OcspManagementApplication";

    private SubsystemUtil() {
    }

    /**
     * 获取当前运行中的系统主类class
     *
     * @return 当前系统的主类
     */
    public static Class<?> getSubsystemMainClass() {
        try {
            if (ClassLoaderUtil.isClassPresent(CA_MAIN_CLASS_NAME)) {
                return ClassUtils.getClass(CA_MAIN_CLASS_NAME, false);
            } else if (ClassLoaderUtil.isClassPresent(KM_MAIN_CLASS_NAME)) {
                return ClassUtils.getClass(KM_MAIN_CLASS_NAME, false);
            } else if (ClassLoaderUtil.isClassPresent(RA_MAIN_CLASS_NAME)) {
                return ClassUtils.getClass(RA_MAIN_CLASS_NAME, false);
            } else if (ClassLoaderUtil.isClassPresent(CLM_MAIN_CLASS_NAME)) {
                return ClassUtils.getClass(CLM_MAIN_CLASS_NAME, false);
            }  else if (ClassLoaderUtil.isClassPresent(OCSP_MAIN_CLASS_NAME)) {
                return ClassUtils.getClass(OCSP_MAIN_CLASS_NAME, false);
            } else {
                throw ManagementValidationError.PARAM_ERROR.toException("Unsupported system!");
            }
        } catch (ClassNotFoundException e) {
            throw ManagementValidationError.PARAM_ERROR.toException(e.getMessage(), e);
        }
    }

    /**
     * 获取系统代号，NGPKI固定为300
     *
     * @return
     */
    public static String getSystemCode() {
        return "300";
    }

    /**
     * 获取服务代号，参考 https://dev.koal.com/issues/109215
     * <pre>
     * 01：CA（ca-service）
     * 02：KM（km-service）
     * 03：RA（ra-service）
     * 04：CDS（同ra-service）
     * </pre>
     *
     * @return
     */
    public static String getServiceCode() {
        if (ClassLoaderUtil.isClassPresent(CA_MAIN_CLASS_NAME)) {
            return "01";
        } else if (ClassLoaderUtil.isClassPresent(KM_MAIN_CLASS_NAME)) {
            return "02";
        } else if (ClassLoaderUtil.isClassPresent(RA_MAIN_CLASS_NAME)) {
            return "03";
        } else if (ClassLoaderUtil.isClassPresent(CLM_MAIN_CLASS_NAME)) {
            return "06";
        }  else if (ClassLoaderUtil.isClassPresent(OCSP_MAIN_CLASS_NAME)) {
            return "07";
        } else {
            throw ManagementValidationError.PARAM_ERROR.toException("Unsupported system!");
        }
    }
}