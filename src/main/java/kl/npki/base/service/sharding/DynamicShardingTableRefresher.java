package kl.npki.base.service.sharding;

import kl.nbase.db.support.sharding.ShardingTableUtils;
import kl.npki.base.core.utils.SystemUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 动态分表刷新器，为动态创建的分表进行分表规则刷新
 *
 * <AUTHOR>
 * @date 2025/6/6 8:51
 **/
public interface DynamicShardingTableRefresher {

    Logger LOGGER = LoggerFactory.getLogger(DynamicShardingTableRefresher.class);

    /**
     * 获取逻辑表名
     *
     * @return 逻辑表名
     */
    String getLogicalTableName();

    /**
     * 获取分表名称匹配正则，用于精准匹配分表名称
     *
     * @return 分表名称匹配正则
     */
    String getShardingTableNameRegex();

    /**
     * 是否启用
     *
     * @return true->启动 false->不启用
     */
    default boolean isEnabled() {
        return SystemUtil.isDeployed();
    }

    /**
     * 刷新指定表的分表节点，通过逻辑表以及逻辑表分表正则进行过滤分表并刷新
     */
    default void refreshShardingTable() {

        String logicalTableName = getLogicalTableName();
        String shardingTableNameRegex = getShardingTableNameRegex();

        if (StringUtils.isBlank(logicalTableName) || StringUtils.isBlank(shardingTableNameRegex)) {
            return;
        }

        LOGGER.info("Start to refresh sharding table. logic table: {}, sharding table regex: {}", logicalTableName, shardingTableNameRegex);
        ShardingTableUtils.refreshShardingTable(logicalTableName, shardingTableNameRegex);
    }
}
