package kl.npki.base.service.sharding;

import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static kl.npki.base.service.sharding.DynamicShardingTableManager.INSTANCE_ID;

/**
 * 动态分表管理器
 *
 * <AUTHOR>
 * @date 2025/6/6 9:16
 **/
@Component(INSTANCE_ID)
public class DynamicShardingTableManager implements ApplicationContextAware, InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(DynamicShardingTableManager.class);

    /**
     * 注入到Spring容器中的实例名称
     */
    public static final String INSTANCE_ID = "dynamicShardingTableManager";

    private ApplicationContext appContext;

    private static final List<DynamicShardingTableRefresher> REFRESHERS = new ArrayList<>(4);

    @Override
    public void afterPropertiesSet() {
        Map<String, DynamicShardingTableRefresher> refresherMap = appContext.getBeansOfType(DynamicShardingTableRefresher.class);
        if (MapUtils.isEmpty(refresherMap)) {
            return;
        }

        refresherMap.values().forEach(refresher -> {
            if (!refresher.isEnabled()) {
                return;
            }
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("DynamicShardingTableManager initialization {} successful", refresher.getClass().getSimpleName());
            }
            REFRESHERS.add(refresher);
        });
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        appContext = applicationContext;
    }

    public void refresh() {
        for (DynamicShardingTableRefresher refresher : REFRESHERS) {
            LOGGER.info("DynamicShardingTableManager refresh {}", refresher.getClass().getSimpleName());
            refresher.refreshShardingTable();
        }
    }
}
