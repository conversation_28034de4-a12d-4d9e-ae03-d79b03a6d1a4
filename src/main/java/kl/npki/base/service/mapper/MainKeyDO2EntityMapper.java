package kl.npki.base.service.mapper;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.mainkey.model.MainKeyEntity;
import kl.npki.base.service.repository.entity.MainKeyDO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/11/1 16:53
 * @Description:
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MainKeyDO2EntityMapper extends BaseMapper<MainKeyDO, MainKeyEntity> {

    @Override
    MainKeyEntity map(MainKeyDO source);


    @Override
    MainKeyDO reverseMap(MainKeyEntity target);
}