package kl.npki.base.service.mapper;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.config.model.FileConfigEntity;
import kl.npki.base.service.repository.entity.TFileConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @since 2023/12/22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FileConfigEntity2DOMapper extends BaseMapper<FileConfigEntity, TFileConfigDO> {
}
