package kl.npki.base.service.mapper;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.log.model.ApiLogInfo;
import kl.npki.base.service.repository.entity.TApiLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2022/12/9
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ApiLogInfo2DOMapper extends BaseMapper<ApiLogInfo, TApiLogDO> {
    
}