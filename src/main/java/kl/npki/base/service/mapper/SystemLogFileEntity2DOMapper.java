package kl.npki.base.service.mapper;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.log.model.SystemLogFileEntity;
import kl.npki.base.service.repository.entity.TSystemLogFileDO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
/**
 * <AUTHOR>
 * @since 2025/7/10 14:03
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SystemLogFileEntity2DOMapper extends BaseMapper<TSystemLogFileDO, SystemLogFileEntity> {

}