package kl.npki.base.service.mapper;

import kl.nbase.bean.convert.BaseMapper;
import kl.npki.base.core.biz.upload.model.UploadFileEntity;
import kl.npki.base.service.repository.entity.UploadFileDO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UploadFileEntity2DOMapper extends BaseMapper<UploadFileEntity, UploadFileDO> {
}
