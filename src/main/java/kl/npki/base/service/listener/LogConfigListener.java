package kl.npki.base.service.listener;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.rolling.RollingFileAppender;
import ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy;
import ch.qos.logback.core.util.FileSize;
import kl.nbase.config.RefreshableConfig;
import kl.nbase.config.RefreshableConfigWrapper;
import kl.nbase.config.listener.ConfigRefreshListener;
import kl.npki.base.core.configs.LogConfig;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.service.util.LogbackHelper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;

/**
 * @Author: guoq
 * @Date: 2024/3/20
 * @description:
 */
public class LogConfigListener implements ConfigRefreshListener {

    private static final Logger log = LoggerFactory.getLogger(LogConfigListener.class);

    @Override
    public void onChange(RefreshableConfig before, RefreshableConfig after, Set<String> changedFields) {
        String configId = after.id();
        // 如果配置文件中没有租户id，说明更新的非租户配置
        if(StringUtils.isBlank(configId) && !BaseConstant.CONFIG_FILE_NAME.equals(configId)){
            return;
        }
        LogConfig logConfig = (((RefreshableConfigWrapper) after).unwrap());
        updateLogback(logConfig);
    }

    /**
     * 更新日志配置
     *
     * @param logConfig 日志配置对象
     */
    private void updateLogback(LogConfig logConfig) {
        // 修改日志级别
        LogbackHelper.updateLogLevel(logConfig.getLevel());
        // 文件Appender
        RollingFileAppender<ILoggingEvent> rollingFileAppender = LogbackHelper.getRollingFileAppender();
        if (ObjectUtils.isEmpty(rollingFileAppender)) {
            log.warn("Unconfigured file Appender output！");
            return;
        }
        SizeAndTimeBasedRollingPolicy policy =
            (SizeAndTimeBasedRollingPolicy) rollingFileAppender.getRollingPolicy();
        // 文件最大容量
        String totalSizeCap = logConfig.getTotalSizeCap();
        // 设置触发滚动策略文件最大容量
        policy.setTotalSizeCap(FileSize.valueOf(totalSizeCap));
        // 设置文件保存天数
        policy.setMaxHistory(logConfig.getMaxHistory());
        policy.stop();
        policy.start();


        log.info("Update log server output configuration result: {}", rollingFileAppender.isStarted());
    }
}
