package kl.npki.base.service.listener;

import kl.npki.base.core.biz.org.model.OrgEntity;
import kl.npki.base.core.biz.org.model.OrgTraceEntity;
import kl.npki.base.core.biz.trace.model.EntityChangeParam;
import kl.npki.base.core.common.trace.InvocationType;
import kl.npki.base.core.event.EntityChangeEvent;
import kl.npki.base.service.common.context.MsgContext;
import kl.npki.base.service.common.context.MsgContextHolder;
import kl.npki.base.service.common.context.RequestContext;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.constants.Constants;
import kl.npki.base.service.constants.I18nConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 机构实体变更事件监听器
 *
 * <AUTHOR>
 * @date 2024/07/04
 */
@Component
public class OrgEntityChangeListener extends AbstractEntityChangeListener{
    @Override
    public void onChange(EntityChangeEvent event) {
        // 获取事件参数
        EntityChangeParam param = (EntityChangeParam) event.getSource();
        OrgEntity orgEntity = (OrgEntity) param.getEntity();

        // 转化为轨迹实体
        OrgTraceEntity orgTraceEntity = new OrgTraceEntity();
        orgTraceEntity.parseEntity(orgEntity);
        // 填充其它参数
        fillData2TraceEntity(orgTraceEntity, param);
        // 保存轨迹
        orgTraceEntity.save();
    }

    @Override
    public String getEntityClass() {
        return OrgEntity.class.getName();
    }

    /**
     * 解析公共参数
     *
     * @param param
     */
    public void fillData2TraceEntity(OrgTraceEntity entity, EntityChangeParam param) {
        // 设置调用方式
        InvocationType invocationType = param.getInvocationType();
        entity.setInvocationType(invocationType.getDesc());
        // 设置业务变更名称
        entity.setBizName(param.getBizName());

        // 根据不同的调用方式，填充轨迹实体数据
        switch (invocationType) {
            case ADMINISTRATOR_OPERATION:
                RequestContext context = RequestContextHolder.getContext();
                // 流水号只跟客户端相关、前端界面操作流水号置空
                entity.setBizId(StringUtils.equals(Constants.UN_KNOW_BIZ_ID, context.getBizId()) ? StringUtils.EMPTY : context.getBizId());
                entity.setSourceIp(context.getIp());
                entity.setUserId(context.getUserId());
                entity.setOperatorName(context.getUsername());
                break;
            case SYSTEM_SCHEDULED_TASK:
                entity.setOperatorName(I18nConstant.SYSTEM_OPERATOR);
                entity.setSourceIp(getLocalIp());
                break;
            case TCP_SERVICE_INTERFACE:
                MsgContext msgContext = MsgContextHolder.getContext();
                entity.setSourceIp(msgContext.getIp());
                entity.setBizId(msgContext.getBizId());
                break;
            case HTTP_SERVICE_INTERFACE:
                RequestContext context2 = RequestContextHolder.getContext();
                entity.setBizId(context2.getBizId());
                entity.setSourceIp(context2.getIp());
                break;
            default:
                break;
        }
    }
}
