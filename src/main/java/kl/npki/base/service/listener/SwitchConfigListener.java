package kl.npki.base.service.listener;

import kl.nbase.config.RefreshableConfig;
import kl.nbase.config.RefreshableConfigWrapper;
import kl.nbase.config.listener.ConfigRefreshListener;
import kl.nbase.db.support.slot.SlotDataSourceConfig;
import kl.npki.base.core.constant.EnvironmentEnum;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.service.common.db.DbSwitchConfigServiceImpl;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/1/4 17:12
 * @Description: db配置刷新监听器
 */
@Component
public class SwitchConfigListener implements ConfigRefreshListener {

    private final DbSwitchConfigServiceImpl dbSwitchConfigService;

    public SwitchConfigListener(DbSwitchConfigServiceImpl dbSwitchConfigService) {
        this.dbSwitchConfigService = dbSwitchConfigService;
    }

    @Override
    public void onChange(RefreshableConfig before, RefreshableConfig after, Set<String> changedFields) {
        SlotDataSourceConfig slotDataSourceConfig = (((RefreshableConfigWrapper) after).unwrap());
        dbSwitchConfigService.refreshDatasource(slotDataSourceConfig);
        // 已完成部署，且使用了DB配置中心，且默认租户数据源发生变更时，更新bootstrap.yml文件中的dbConfigStore配置
        if (SystemUtil.isDeployed() && slotDataSourceConfig.getSlotName().equals(EnvironmentEnum.DEFAULT.getId())) {
            dbSwitchConfigService.updateDbConfigStoreIfNecessary(slotDataSourceConfig);
        }
    }
}
