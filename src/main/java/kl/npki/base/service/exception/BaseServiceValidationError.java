package kl.npki.base.service.exception;

import kl.nbase.exception.interfaces.IValidationError;

import static kl.npki.base.service.constants.I18nConstant.BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX;

/**
 * <AUTHOR>
 * Created on 2022/09/13 15:32
 */
public enum BaseServiceValidationError implements IValidationError, ErrorCode {

    /**
     * 系统异常
     */
    SLAVE_DB_COUNT_ERROR("001", "只读数据库数量错误！"),

    USER_NOT_LOGIN("002", "用户未登录"),

    DB_CONFIG_NOT_SET_ERROR("005", "当前环境数据库未配置，需要先配置数据库！"),

    USERNAME_FIELD_IN_HEADER_IS_EMPTY("006", "请求头中username字段为空"),

    IV_FIELD_IN_HEADER_IS_EMPTY("007", "请求头中IV字段为空"),

    IRREGULAR_REQUEST("008", "不规范的请求"),

    UNSUPPORT_REQUEST_METHOD("009", "不支持的请求方法"),

    UNSUPPORT_MEDIA_TYPE("010", "不支持的媒体类型"),

    /**
     * 请求参数异常相关
     */
    MISSING_REQUEST_PARAM("011", "缺失请求参数"),
    PARAM_CONVERT_ERROR("012", "请求参数类型转换异常"),

    /**
     * 客户端签名异常相关
     */
    MISSING_OPERATION_SIGNATURE_ERROR("020", "缺少操作签名"),
    OPERATION_SIGNATURE_VERIFICATION_FAILED("021", "操作签名验证失败"),

    /**
     * 客户端与服务端时间异常相关
     */
    SIGNATURE_TIMESTAMP_CHECK_ERROR("031", "本地时间与服务器不一致，请检查系统时间设置是否正确"),
    SIGNATURE_TIMESTAMP_FORMAT_ERROR("032", "本地时间戳格式错误"),

    NO_SUCH_LOG_LEVEL("050", "不存在对应日志级别"),

    /**
     * JWT
     */
    TOKEN_EXPIRED_ERROR("600", "TOKEN已超时"),
    DECODED_JWT_ERROR("601", "JWT 解码异常"),
    INVALID_COOKIE_INFO("602", "无效的cookie信息"),


    /**
     * license
     */
    LICENSE_ERROR("700", "License异常");;

    private final String code;
    private final String desc;

    BaseServiceValidationError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
