package kl.npki.base.service.exception;

import kl.nbase.exception.interfaces.IRemoteError;

import static kl.npki.base.service.constants.I18nConstant.BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 业务远程调用异常
 *
 * <AUTHOR>
 * @date 2024/4/30
 */
public enum BaseServiceRemoteError implements IRemoteError, ErrorCode {
    ;


    private final String code;
    private final String desc;

    BaseServiceRemoteError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
