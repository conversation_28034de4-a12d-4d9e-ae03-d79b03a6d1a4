package kl.npki.base.service.exception;

import kl.nbase.exception.BaseException;
import kl.nbase.helper.exception.HelperInternalError;
import kl.npki.base.service.common.RestResponse;
import org.mybatis.spring.MyBatisSystemException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import static kl.npki.base.service.constants.I18nExceptionInfoConstant.MISSING_REQUEST_PARAM_FIELD_I18N_KEY;

/**
 * <AUTHOR>
 * Created on 2022/08/24 13:38
 */
@RestControllerAdvice
public class ExceptionAdvice<T> {

    private final Logger log = LoggerFactory.getLogger(ExceptionAdvice.class);

    /**
     * 针对BaseException的异常处理
     *
     * @param e
     * @return
     */
    @ExceptionHandler(BaseException.class)
    public ResponseEntity<RestResponse<T>> handleBaseException(BaseException e) {
        log.error(e.getMessage(), e);
        return ResponseEntity.ok(RestResponse.fail(e));
    }

    /**
     * 针对数据库配置错误的处理
     *
     * @param e
     * @return
     */
    @ExceptionHandler(MyBatisSystemException.class)
    public ResponseEntity<RestResponse<T>> handleDatabaseConfigError(MyBatisSystemException e) {
        log.error("Database config error: {}", e.getMessage(), e);
        if (e.getMessage() != null && e.getMessage().contains(HelperInternalError.DB_SLOT_NOT_EXIST.getDesc())) {
            return ResponseEntity.ok(RestResponse.fail(BaseServiceValidationError.DB_CONFIG_NOT_SET_ERROR.toException()));
        }
        return handleGeneralException(e);
    }

    /**
     * 针对请求消息不可读的处理
     *
     * @param e
     * @return
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<RestResponse<T>> handleHttpMessageNotReadable(HttpMessageNotReadableException e) {
        log.error("Message not readable: {}", e.getMessage(), e);
        return ResponseEntity.badRequest().body(RestResponse.fail(BaseServiceValidationError.IRREGULAR_REQUEST.toException()));
    }

    /**
     * 针对不支持的请求方法的处理
     *
     * @param e
     * @return
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<RestResponse<T>> handleMethodNotSupported(HttpRequestMethodNotSupportedException e) {
        log.error("Method not supported: {}", e.getMessage(), e);
        return ResponseEntity.badRequest().body(RestResponse.fail(BaseServiceValidationError.UNSUPPORT_REQUEST_METHOD.toException()));
    }

    /**
     * 针对不支持的媒体类型的处理
     *
     * @param e
     * @return
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity<RestResponse<T>> handleMediaTypeNotSupported(HttpMediaTypeNotSupportedException e) {
        log.error("Media type not supported: {}", e.getMessage(), e);
        return ResponseEntity.badRequest().body(RestResponse.fail(BaseServiceValidationError.UNSUPPORT_MEDIA_TYPE.toException()));
    }

    /**
     * 通用异常处理
     *
     * @param e
     * @return
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<RestResponse<T>> handleGeneralException(Exception e) {
        log.error("System error: {}", e.getMessage(), e);
        return ResponseEntity.ok(RestResponse.fail(BaseServiceInternalError.SYSTEM_ERROR.toException()));
    }

    /**
     * 针对缺少请求参数的处理
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<RestResponse<T>> missingServletRequestParameterException(MissingServletRequestParameterException e) {
        log.error("Missing servlet request parameter exception: {}", e.getMessage(), e);
        String parameterName = e.getParameterName();
        return ResponseEntity.ok(RestResponse.fail(BaseServiceValidationError.MISSING_REQUEST_PARAM.toException(MISSING_REQUEST_PARAM_FIELD_I18N_KEY, parameterName)));

    }
}
