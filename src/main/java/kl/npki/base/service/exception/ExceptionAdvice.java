package kl.npki.base.service.exception;

import kl.nbase.exception.BaseException;
import kl.nbase.exception.InternalException;
import kl.nbase.exception.RemoteException;
import kl.nbase.exception.ValidationException;
import kl.nbase.helper.exception.HelperInternalError;
import kl.npki.base.service.common.RestResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.mybatis.spring.MyBatisSystemException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.List;

import static kl.npki.base.service.constants.I18nExceptionInfoConstant.MISSING_REQUEST_PARAM_FIELD_I18N_KEY;
import static kl.npki.base.service.constants.I18nExceptionInfoConstant.PARAM_CONVERT_ERROR_DESC_DESC_I18N_KEY;

/**
 * <AUTHOR>
 * Created on 2022/08/24 13:38
 */
@RestControllerAdvice
public class ExceptionAdvice<T> {

    private final Logger log = LoggerFactory.getLogger(ExceptionAdvice.class);

    /**
     * 请求体格式不正确或 JSON 解析失败（如字段类型不匹配、空体等）
     *
     * @param e Spring 在反序列化请求体时抛出的异常
     * @return 统一的客户端请求错误响应
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<RestResponse<T>> handleClientErrorMessageNotReadable(HttpMessageNotReadableException e) {
        return handleError(e,
                BaseServiceValidationError.IRREGULAR_REQUEST.toException(),
                HttpStatus.BAD_REQUEST
        );
    }

    /**
     * 请求方式不被支持（如 POST 请求到 GET 接口）
     *
     * @param e Spring 抛出的请求方法错误异常
     * @return 请求方式错误的标准响应体
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<RestResponse<T>> handleClientErrorMethodNotSupported(HttpRequestMethodNotSupportedException e) {
        return handleError(e,
                BaseServiceValidationError.UNSUPPORT_REQUEST_METHOD.toException(),
                HttpStatus.METHOD_NOT_ALLOWED
        );
    }

    /**
     * 接口不支持当前请求的媒体类型（如 Content-Type 设置错误）
     *
     * @param e Spring 抛出的媒体类型异常
     * @return 不支持的媒体类型响应
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity<RestResponse<T>> handleClientErrorMediaTypeNotSupported(HttpMediaTypeNotSupportedException e) {
        return handleError(e,
                BaseServiceValidationError.UNSUPPORT_MEDIA_TYPE.toException(),
                HttpStatus.UNSUPPORTED_MEDIA_TYPE
        );
    }

    /**
     * 缺少必要的请求参数时抛出
     *
     * @param e Spring 校验参数时抛出的缺参异常，包含缺失参数名等信息
     * @return 缺参提示的统一错误响应
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<RestResponse<T>> handleClientErrorMissingRequestParam(MissingServletRequestParameterException e) {
        String parameterName = e.getParameterName();
        return handleError(e,
                BaseServiceValidationError.MISSING_REQUEST_PARAM.toException(MISSING_REQUEST_PARAM_FIELD_I18N_KEY, parameterName),
                HttpStatus.BAD_REQUEST
        );
    }

    /**
     * 参数效验异常处理器
     *
     * @param e 参数验证异常
     * @return ResponseInfo
     */
    @ExceptionHandler({BindException.class, MethodArgumentNotValidException.class})
    public ResponseEntity<RestResponse<T>> parameterExceptionHandler(Exception e) {
        BindingResult bindingResult = null;
        if (e instanceof MethodArgumentNotValidException) {
            bindingResult = ((MethodArgumentNotValidException) e).getBindingResult();
        } else if (e instanceof BindException) {
            bindingResult = ((BindException) e).getBindingResult();
        }

        // 判断异常中是否有错误信息，如果存在就抛出异常中的消息
        String errorMessage = null;
        if (null != bindingResult && bindingResult.hasErrors()) {
            List<ObjectError> errors = bindingResult.getAllErrors();
            if (CollectionUtils.isNotEmpty(errors)) {
                FieldError fieldError = (FieldError) errors.get(0);
                errorMessage = fieldError.getDefaultMessage();
            }
        }

        return handleError(e,
                BaseServiceValidationError.IRREGULAR_REQUEST.toException(errorMessage),
                HttpStatus.BAD_REQUEST
        );
    }

    /**
     * 处理参数类型转换错误（如请求参数无法转换为控制器方法所需类型）。
     * <p>
     * 例如：当请求参数类型为 String，但控制器期望 Integer 类型时，会抛出 {@link MethodArgumentTypeMismatchException}。
     * 此方法将其封装为统一格式的业务异常 {@link BaseException}，并返回 400 Bad Request 响应。
     * </p>
     *
     * @param exception 抛出的参数类型不匹配异常，包含参数名、期望类型、实际值等信息
     * @return ResponseEntity 包装的统一错误响应对象 {@code RestResponse<T>}，状态码为 400
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<RestResponse<T>>  handleTypeMismatch(MethodArgumentTypeMismatchException exception) {
        // 参数名
        String name = exception.getName();
        // 期望的类型
        String requiredType = exception.getRequiredType().getSimpleName();
        // 传入的错误值
        Object value = exception.getValue();
        BaseException baseException = BaseServiceValidationError.MISSING_REQUEST_PARAM
                .toException(PARAM_CONVERT_ERROR_DESC_DESC_I18N_KEY, name, value, requiredType);
        return handleError(exception, baseException, HttpStatus.BAD_REQUEST);
    }

    /**
     * 处理 MyBatis 系统异常，主要用于数据库配置、连接等底层错误
     *
     * @param e MyBatis 抛出的系统异常，可能包含数据源、配置错误等信息
     * @return 标准响应体，根据具体错误返回不同的业务错误码
     */
    @ExceptionHandler(MyBatisSystemException.class)
    public ResponseEntity<RestResponse<T>> handleServerErrorDatabaseConfig(MyBatisSystemException e) {
        if (e.getMessage() != null && e.getMessage().contains(HelperInternalError.DB_SLOT_NOT_EXIST.getDesc())) {
            return handleError(e,
                    BaseServiceValidationError.DB_CONFIG_NOT_SET_ERROR.toException("DB slot not exist"),
                    HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
        return handleError(e,
                BaseServiceInternalError.SYSTEM_ERROR.toException("Database config error"),
                HttpStatus.INTERNAL_SERVER_ERROR
        );
    }

    /**
     * 处理 NGPKI 客户端参数校验异常。
     *
     * 当请求参数不符合业务校验要求时抛出该异常，返回 400 Bad Request。
     *
     * @param e 请求校验异常
     * @return HTTP 400 错误响应，包含统一格式的错误信息
     */
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<RestResponse<T>> handleClientValidation(ValidationException e) {
        return handleError(e, HttpStatus.BAD_REQUEST);
    }

    /**
     * 处理 NGPKI 服务端内部业务异常。
     *
     * 当服务端业务逻辑处理过程中发生内部错误时抛出该异常，返回 500 Internal Server Error。
     *
     * @param e 内部业务异常
     * @return HTTP 500 错误响应，包含统一格式的错误信息
     */
    @ExceptionHandler(InternalException.class)
    public ResponseEntity<RestResponse<T>> handleServerInternal(InternalException e) {
        return handleError(e, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * 处理 NGPKI 远程服务调用异常。
     *
     * 当调用外部系统或第三方服务发生错误时抛出该异常，返回 500 Internal Server Error。
     *
     * @param e 远程服务异常
     * @return HTTP 500 错误响应，包含统一格式的错误信息
     */
    @ExceptionHandler(RemoteException.class)
    public ResponseEntity<RestResponse<T>> handleServerRemote(RemoteException e) {
        return handleError(e, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * 未被特定捕获的所有系统异常（兜底处理）
     *
     * @param e 未知系统级异常，通常为程序 Bug 或非预期错误
     * @return 通用系统错误响应，隐藏内部错误细节
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<RestResponse<T>> handleGeneralException(Exception e) {
        return handleError(e,
                BaseServiceInternalError.SYSTEM_ERROR.toException(),
                HttpStatus.INTERNAL_SERVER_ERROR
        );
    }

    /**
     * 错误的统一处理器
     *
     * @param e 异常对象
     * @param baseException 对应的自定义异常信息（含错误码和信息）
     * @param status 返回的 HTTP 状态码（如 400、405、500 等）
     * @return HTTP 错误响应，封装为统一格式
     */
    private ResponseEntity<RestResponse<T>> handleError(Exception e,
                                                        BaseException baseException,
                                                        HttpStatus status) {
        // 打印异常
        log.error("[Exception][{}] [{}] Message: {}, stack trace omitted",
                e.getClass().getSimpleName(),
                baseException.getDesc(),
                e.getMessage(),
                e);

        // BaseException 是业务封装异常，此处仅打印摘要信息（无堆栈）
        log.warn("[BaseException][{}] [{}] Message: {}",
                baseException.getClass().getSimpleName(),
                baseException.getDesc(),
                baseException.getMessage());

        return ResponseEntity
                .status(status)
                .body(RestResponse.fail(baseException));
    }

    /**
     * 错误的统一处理器
     *
     * @param baseException 对应的自定义异常信息（含错误码和信息）
     * @param status 返回的 HTTP 状态码（如 400、405、500 等）
     * @return HTTP 错误响应，封装为统一格式
     */
    private ResponseEntity<RestResponse<T>> handleError(BaseException baseException, HttpStatus status) {
        log.warn("[BaseException][{}] [{}] Message: {}, stack trace omitted",
                baseException.getClass().getSimpleName(),
                baseException.getDesc(),
                baseException.getMessage(),
                baseException);

        return ResponseEntity
                .status(status)
                .body(RestResponse.fail(baseException));
    }
}