package kl.npki.base.service.exception;

import kl.nbase.exception.interfaces.IInternalError;

import static kl.npki.base.service.constants.I18nConstant.BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX;

/**
 * <AUTHOR>
 * Created on 2022/09/13 15:32
 */
public enum BaseServiceInternalError implements IInternalError, ErrorCode {

    /**
     * 系统异常
     */
    SYSTEM_ERROR("000", "系统错误"),

    DB_REFRESH_ERROR("001", "监听器db配置刷新失败！"),

    DB_CONFIG_DATABASE_NAME_FORMAT_ERROR("002", "db配置【数据库名称】解析失败！"),

    DB_TENANT_SLOT_CONFIG_NOT_FOUND("003", "租户id对应的slot配置未找到！"),

    INVALID_LOG_TYPE("004", "无效的日志类型"),

    TENANT_HOLDER_INIT_FAILED("005", "租户托管容器初始化失败"),

    REQUEST_WRAPPER_ERROR("006", "包装请求失败"),

    CREATE_SSL_CONNECTION_FACTORY_FAILED("007", "创建SslConnectionFactory失败"),
    FILE_WRITE_ERROR("008", "文件写入失败"),
    FILE_READ_ERROR("009", "文件读取失败"),
    FILE_CONFIG_ENTITY_SAVE_FAILED("010", "文件配置保存失败"),
    ADMINISTRATOR_CERTIFICATE_DOES_NOT_EXIST("012", "管理员证书未找到"),
    ;

    private final String code;
    private final String desc;

    BaseServiceInternalError(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getModuleCode() {
        return MODULE_CODE;
    }

    @Override
    public String getSecondCode() {
        return code;
    }

    @Override
    public String getExtFlag() {
        return EXT_FLAG;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
