package kl.npki.base.service.interceptor;

import kl.nbase.db.core.DataSourceContext;
import kl.nbase.db.core.TenantSession;
import kl.npki.base.core.tenantholders.TenantContextHolder;
import kl.npki.base.service.common.context.RequestContext;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.constants.Constants;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * Created on 2022/08/23 15:57
 */
public class RequestInterceptor implements HandlerInterceptor {

    private static final Logger log = LoggerFactory.getLogger(RequestInterceptor.class);

    /**
     * 封装ApiContext并打印请求地址和方法
     */
    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @Nullable HttpServletResponse response,
                             @Nullable Object handler) {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        try {
            RequestContext context = RequestContextHolder.getContext();
            context.assembleContext(request);
            MDC.put(Constants.TRACE_ID, context.getTraceId());
            MDC.put(Constants.SPAN_ID, context.getSpanId());
            MDC.put(Constants.BIZ_ID, context.getBizId());
            MDC.put(Constants.CLIENT_IP_PORT, context.getClientIpPort());
            if (log.isDebugEnabled()) {
                log.debug("Request {} {} start, clientIp {}", context.getRequestMethod(), context.getRequestUrl(),
                    context.getIp());
            }
            TenantSession tenantSession = DataSourceContext.getTenantSession();
            tenantSession.setTenantId(context.getTenantId());
            TenantContextHolder.setTenantId(context.getTenantId());
            if (Boolean.TRUE.equals(DataSourceContext.getWriteRouteOnly())) {
                HintManager.getInstance().setWriteRouteOnly();
            }
            return true;
        } catch (Exception e) {
            // 确保资源被释放，因为RequestInterceptor自身的preHandle抛异常是走不到自身的afterCompletion（checkUserIdAndUsername），可能会导致RequestContext资源无法释放，具体可以看org.springframework.web.servlet.HandlerExecutionChain拦截器实现的逻辑，OperationSignVerifyInterceptor在RequestInterceptor之后执行，OperationSignVerifyInterceptor preHandle抛异常还是会走到RequestInterceptor的afterCompletion释放的
            RequestContextHolder.remove();
            HintManager.clear();
            TenantContextHolder.clear();
            DataSourceContext.clearSession();
            MDC.remove(Constants.TRACE_ID);
            MDC.remove(Constants.SPAN_ID);
            MDC.remove(Constants.BIZ_ID);
            MDC.remove(Constants.CLIENT_IP_PORT);
            throw e;
        }
    }

    @Override
    public void postHandle(@NonNull HttpServletRequest request, @Nullable HttpServletResponse response,
                           @Nullable Object handler, @Nullable ModelAndView modelAndView) {
        //
    }

    /**
     * 计算接口耗时，封装响应码并打印请求地址和方法
     */
    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @Nullable HttpServletResponse response,
                                @Nullable Object handler, @Nullable Exception ex) {
        long now = System.currentTimeMillis();
        RequestContext context = RequestContextHolder.getContext();
        context.setEnd(now);
        if (log.isDebugEnabled()) {
            log.debug("Request {} {} end, cost {}ms", context.getRequestMethod(), context.getRequestUrl(),
                context.getEnd() - context.getStart());
        }
        RequestContextHolder.remove();
        DataSourceContext.clearSession();
        TenantContextHolder.clear();
        MDC.remove(Constants.TRACE_ID);
        MDC.remove(Constants.SPAN_ID);
        MDC.remove(Constants.BIZ_ID);
        MDC.remove(Constants.CLIENT_IP_PORT);
        HintManager.clear();
    }
}
