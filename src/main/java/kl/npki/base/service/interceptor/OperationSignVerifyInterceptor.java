package kl.npki.base.service.interceptor;

import kl.nbase.auth.authority.AllowListFactory;
import kl.nbase.auth.utils.AntPathMatcher;
import kl.nbase.emengine.service.ClusterEngine;
import kl.nbase.security.asn1.x509.Certificate;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.nbase.security.util.encoders.Base64;
import kl.nbase.security.utils.AsymKeyUtil;
import kl.nbase.security.utils.SignatureAlgoUtil;
import kl.npki.base.core.configs.LoginTypeConfig;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.LoginType;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.service.common.context.RequestContext;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.constants.Constants;
import kl.npki.base.service.exception.BaseServiceInternalError;
import kl.npki.base.service.exception.BaseServiceValidationError;
import kl.npki.base.service.util.OperationLogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.PublicKey;
import java.util.List;

import static kl.npki.base.service.constants.I18nExceptionInfoConstant.MISSING_REQUEST_HEADER_PARAM_FIELD_I18N_KEY;
import static kl.npki.base.service.constants.I18nExceptionInfoConstant.PLEASE_LOG_IN_AGAIN_I18N_KEY;

/**
 * 操作签名验证拦截器
 * 核心功能：
 * 1. 对需要认证的操作请求进行签名验证
 * 2. 支持白名单路径跳过验证
 * 3. 根据系统配置动态控制验证策略
 */
public class OperationSignVerifyInterceptor implements HandlerInterceptor {
    private static final Logger log = LoggerFactory.getLogger(OperationSignVerifyInterceptor.class);

    /**
     * 路径匹配器用于白名单校验
     */
    private static final AntPathMatcher ANT_PATH_MATCHER = new AntPathMatcher();


    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @Nullable HttpServletResponse response, @Nullable Object handler) {
        // 仅处理HandlerMethod类型的处理器
        boolean isHandlerMethod = handler instanceof HandlerMethod;
        // 获取系统配置判断是否启用操作签名
        SysInfoConfig sysInfoConfig = BaseConfigWrapper.getSysInfoConfig();
        if (!(isHandlerMethod) || !sysInfoConfig.isClientSign()) {
            return true;
        }
        // 部署未完成不需要进行验证
        if (!SystemUtil.isDeployed()) {
            return true;
        }
        // 执行签名验证流程
        operationSignatureVerification(request);
        return true;
    }

    /**
     * 操作签名验证主流程
     * 处理逻辑：
     * 1. 获取请求上下文
     * 2. 判断是否需要验证签名
     * 3. 执行实际验证操作
     */
    private static void operationSignatureVerification(HttpServletRequest request) {
        RequestContext context = RequestContextHolder.getContext();
        String signData = request.getHeader(Constants.SIGN_DATA);

        // 按顺序执行验证条件判断
        if (!needVerifySignData(signData, context.getUserCert(), request.getMethod(), request.getRequestURI())) {
            return;
        }
        // 需要验证签名的请求必须有签名值
        if (StringUtils.isBlank(signData)) {
            throw BaseServiceValidationError.MISSING_OPERATION_SIGNATURE_ERROR.toException();
        }
        //校验请求头start是否存在
        String start = request.getHeader(Constants.START);
        if (StringUtils.isBlank(start)) {
            throw BaseServiceValidationError.MISSING_REQUEST_PARAM.toException(MISSING_REQUEST_HEADER_PARAM_FIELD_I18N_KEY,Constants.START);
        }
        // 执行签名验证操作
        verifyRequestSignature(context, signData);
    }

    /**
     * 签名验证条件判断
     * 判断逻辑顺序：
     * 1. 白名单路径 -> 跳过
     * 2. 登录方式不需要证书 -> 跳过
     * 3. 非GET请求 -> 强制验证
     * 4. GET请求无签名 -> 跳过
     * 5. 证书不存在 -> 抛出异常
     *
     * @param signData   签名值
     * @param userCert   用户证书
     * @param method     请求方法类型
     * @param requestUrl 请求url
     * @return boolean 是否需要验证签名
     */
    private static boolean needVerifySignData(String signData, Certificate userCert, String method, String requestUrl) {
        // 条件1：白名单路径检查
        if (isAllowListRequest(requestUrl)) {
            return false;
        }
        // 条件2：登录类型不需要证书
        if (!isCertificateRequired()) {
            return false;
        }
        // 条件3：非GET方法强制验证
        if (isForceVerifyMethod(method)) {
            return true;
        }
        // 条件4：GET请求缺少签名数据
        if (!StringUtils.isNotBlank(signData)) {
            return false;
        }
        // 条件5：证书存在性验证
        validateCertificatePresence(userCert);
        return true;
    }

    /**
     * 执行签名验证操作
     * 处理步骤：
     * 1. 构建签名原文
     * 2. 提取公钥信息
     * 3. 获取哈希算法
     * 4. 调用验签引擎
     */
    private static void verifyRequestSignature(RequestContext context, String signData) {
        boolean isValid;
        // 构建签名原文数据
        String originData = OperationLogUtil.buildSignOriginData(context);
        try {
            // 执行验签操作
            isValid = performSignatureVerification(context.getUserCert(), signData, originData);
        } catch (Exception e) {
            throw BaseServiceValidationError.OPERATION_SIGNATURE_VERIFICATION_FAILED.toException(e);
        }
        if (!isValid) {
            // 在出现操作签名验证失败的时候方便排查问题
            log.debug("Signature verification failed for request {}", originData);
            throw BaseServiceValidationError.OPERATION_SIGNATURE_VERIFICATION_FAILED.toException();
        }
    }

    /**
     * 白名单路径验证
     * 匹配规则：
     * 使用Ant风格路径匹配，支持*和**通配符
     */
    private static boolean isAllowListRequest(String requestUrl) {
        List<String> allowList = AllowListFactory.getAllowList();
        return CollectionUtils.isNotEmpty(allowList) &&
            allowList.stream().anyMatch(url -> ANT_PATH_MATCHER.match(url, requestUrl));
    }

    /**
     * 登录类型配置检查
     * 获取系统登录方式配置，判断是否需要证书
     */
    private static boolean isCertificateRequired() {
        LoginTypeConfig config = BaseConfigWrapper.getLoginTypeConfig();
        LoginType loginType = LoginType.getLoginTypeByType(config.getLoginModel());
        return loginType.needCertificate();
    }

    /**
     * 强制验证方法检查
     * 所有非GET请求都需要强制验证
     */
    private static boolean isForceVerifyMethod(String method) {
        return !RequestMethod.GET.name().equalsIgnoreCase(method);
    }

    /**
     * 证书存在性强制校验
     * 需要证书但未找到时抛出认证异常
     */
    private static void validateCertificatePresence(Certificate userCert) {
        if (userCert == null) {
            throw BaseServiceInternalError.ADMINISTRATOR_CERTIFICATE_DOES_NOT_EXIST.toException(PLEASE_LOG_IN_AGAIN_I18N_KEY);
        }
    }

    /**
     * 核心验签逻辑
     * 包含：
     * 1. 公钥提取
     * 2. 哈希算法获取
     * 3. 密码机验签操作
     *
     * @param userCert   验签证书
     * @param signData   签名值
     * @param originData 签名原文
     * @return 验签结果
     */
    private static boolean performSignatureVerification(Certificate userCert, String signData, String originData) {
        ClusterEngine clusterEngine = EngineHolder.get();
        // 从证书中提取公钥
        PublicKey publicKey = AsymKeyUtil.x509Bytes2PublicKey(userCert.getSubjectPublicKeyInfo());
        // 获取签名使用的哈希算法
        HashAlgo hashAlgo = SignatureAlgoUtil.getHashAlgoByOid(userCert.getSignatureAlgorithm().getAlgorithm().getId());
        // 执行验签操作
        return clusterEngine.verify(publicKey, originData.getBytes(), Base64.decode(signData), hashAlgo, null);
    }

}