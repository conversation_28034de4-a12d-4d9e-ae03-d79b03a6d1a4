package kl.npki.base.service.configurations;

import kl.npki.base.service.filter.SecurityResponseFilter;
import kl.npki.base.service.filter.HttpServletRequestFilter;
import kl.npki.base.core.configs.NpkiServerConfig;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import javax.servlet.Filter;

/**
 * <AUTHOR>
 * @date 2023/3/24
 */
@Configuration
public class FilterAutoConfiguration {

    private static final String ANY_URL_PATTERN = "/*";

    @Resource
    private HttpServletRequestFilter httpServletRequestFilter;

    @Resource
    private SecurityResponseFilter securityResponseFilter;

    @Resource
    private NpkiServerConfig npkiServerConfig;

    @Bean
    public FilterRegistrationBean<Filter> securityResponseFilterRegistration() {
        FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<>();
        registration.setFilter(securityResponseFilter);
        registration.setOrder(1);
        registration.addUrlPatterns(ANY_URL_PATTERN);
        registration.setName("securityResponseFilter");
        return registration;
    }

    /**
     * 将过滤器注册到FilterRegistrationBean中
     * 进行简单配置
     */
    @Bean
    public FilterRegistrationBean<Filter> httpServletRequestFilterRegistration() {
        FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<>();
        registration.setFilter(httpServletRequestFilter);
        registration.setOrder(2);
        registration.addUrlPatterns(npkiServerConfig.getApiPrefix() + ANY_URL_PATTERN);
        registration.setName("httpServletRequestFilter");
        return registration;
    }

}
