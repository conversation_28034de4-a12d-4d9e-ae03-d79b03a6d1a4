package kl.npki.base.service.configurations;

import kl.nbase.auth.config.AuthConfig;
import kl.nbase.auth.core.AuthnType;
import kl.nbase.auth.identifier.BasicAuthIdentifierImpl;
import kl.nbase.auth.identifier.CredentialIdentifier;
import kl.nbase.auth.spring.filter.WWWAuthenticateFilter;
import kl.npki.base.core.tenantholders.ConfigHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 指标监控认证配置
 *
 * <AUTHOR>
 * @date 2024/6/14
 */
@Configuration
public class MetricsAuthConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(MetricsAuthConfiguration.class);

    @Bean
    @ConditionalOnProperty(name = "kl.base.metrics.auth.enabled", havingValue = "true")
    public FilterRegistrationBean<WWWAuthenticateFilter> registerWwwAuthenticateFilter() {
        FilterRegistrationBean<WWWAuthenticateFilter> bean = new FilterRegistrationBean<>();
        bean.setOrder(1);
        bean.setFilter(new WWWAuthenticateFilter(AuthnType.BASIC_AUTH));
        // 匹配"/metrics/"下面的所有url
        bean.addUrlPatterns("/metrics/*");
        return bean;
    }

    @Bean
    @ConditionalOnProperty(name = "kl.base.metrics.auth.enabled", havingValue = "true")
    public CredentialIdentifier basicAuthIdentifier() {
        AuthConfig authConfig = ConfigHolder.get().get(AuthConfig.class);
        AuthConfig.User user = authConfig.getUser();
        BasicAuthIdentifierImpl basicAuthIdentifierImpl = new BasicAuthIdentifierImpl();
        basicAuthIdentifierImpl.setRealmName("npki-metrics");
        basicAuthIdentifierImpl.setUserName(user.getName());
        basicAuthIdentifierImpl.setPassword(user.getPassword());
        if (user.isPasswordGenerated()) {
            logger.warn(String.format(
                "%n%n基本认证使用生成的安全密码：%s%n%n此生成的密码仅供开发使用。"
                    + "在将应用程序部署到生产环境之前，必须更新您的安全配置。%n",
                user.getPassword()));
        }
        return basicAuthIdentifierImpl;
    }
}
