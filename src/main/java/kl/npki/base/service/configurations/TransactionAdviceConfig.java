package kl.npki.base.service.configurations;

import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import org.springframework.aop.Advisor;
import org.springframework.aop.aspectj.AspectJExpressionPointcut;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionManager;
import org.springframework.transaction.interceptor.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: guoq
 * @Date: 2023/4/7
 * @description: 事务配置
 */
@Configuration
public class TransactionAdviceConfig {

    /**
     * 全局事务位置配置
     * 配置切入点表达式
     * 支持切 service包和子包  例如： "execution(* kl.npki.km.core.service..*.*(..))";
     */
    private static final String AOP_POINTCUT_EXPRESSION = " @annotation(kl.npki.base.core.annotation.KlTransactional) ";


    @Resource
    private TransactionManager transactionManager;


    @Bean
    @DependsOn({"tenantContextHolder"})
    public TransactionInterceptor txAdvice() {

        /*只读事物、不做更新删除等*/
        /*事务管理规则*/
        RuleBasedTransactionAttribute readOnlyRule = new RuleBasedTransactionAttribute();
        /*设置当前事务是否为只读事务，true为只读*/
        readOnlyRule.setReadOnly(true);
        // transactiondefinition 定义事务的隔离级别；如果当前存在事务，则加入该事务；如果当前没有事务，则以非事务的方式继续运行。
        readOnlyRule.setPropagationBehavior(TransactionDefinition.PROPAGATION_SUPPORTS);

        //增删改事务规则
        RuleBasedTransactionAttribute requireRule = new RuleBasedTransactionAttribute();
        //抛出异常后执行切点回滚 建议自定义异常
        requireRule.setRollbackRules(Collections.singletonList(new RollbackRuleAttribute(Exception.class)));
        // PROPAGATION_REQUIRED:事务隔离性为1，若当前存在事务，则加入该事务；如果当前没有事务，则创建一个新的事务。这是默认值。
        requireRule.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        /*设置事务失效时间*/
        int transactionTimeout = BaseConfigWrapper.getTransactionConfig().getTimeoutSeconds();
        requireRule.setTimeout(transactionTimeout);

        //配置事务管理规则
        //nameMap声明具备需要管理事务的方法名.
        //这里使用addTransactionalMethod  使用setNameMap

        Map<String, TransactionAttribute> nameMap = new HashMap<>();
        // 默认加注解就开启事务
        nameMap.put("*", requireRule);

        NameMatchTransactionAttributeSource source = new NameMatchTransactionAttributeSource();
        source.setNameMap(nameMap);

        return new TransactionInterceptor(transactionManager, source);
    }


    @Bean
    @DependsOn({"tenantContextHolder"})
    public Advisor txAdviceAdvisor() {
        AspectJExpressionPointcut pointcut = new AspectJExpressionPointcut();
        pointcut.setExpression(AOP_POINTCUT_EXPRESSION);
        return new DefaultPointcutAdvisor(pointcut, txAdvice());
    }
}
