package kl.npki.base.service.configurations;

import kl.nbase.config.RefreshableConfigWrapper;
import kl.nbase.db.support.druid.DruidDataSourceProperties;
import kl.nbase.db.support.id.config.UIdConfig;
import kl.nbase.db.support.id.worker.entity.WorkerNodeEntity;
import kl.nbase.db.support.sharding.config.ShardingConfig;
import kl.nbase.db.support.slot.SlotDataSourceConfig;
import kl.nbase.db.support.slot.SwitchableDataSourceConfig;
import kl.nbase.helper.utils.ResourceUtils;
import kl.nbase.log.config.LogExtConfig;
import kl.nbase.log.store.LogStoreFactory;
import kl.nbase.timer.config.TimerConfig;
import kl.nbase.timer.lock.TimerContext;
import kl.nbase.traffic.config.TrafficConfig;
import kl.npki.base.core.biz.config.model.FileConfigEntity;
import kl.npki.base.core.biz.config.service.IFileConfigService;
import kl.npki.base.core.biz.config.service.impl.FileConfigServiceImpl;
import kl.npki.base.core.biz.kcsp.service.KcspLogUploadService;
import kl.npki.base.core.biz.kcsp.service.impl.KcspLogUploadServiceImpl;
import kl.npki.base.core.configs.ClusterEmConfig;
import kl.npki.base.core.configs.LicenseConfig;
import kl.npki.base.core.configs.NpkiServerConfig;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.base.core.utils.NetUtils;
import kl.npki.base.core.utils.ServerIdUtils;
import kl.npki.base.service.listener.SwitchConfigListener;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static kl.npki.base.core.constant.BaseConstant.WEB_ROOT;

/**
 * Using DependsOn at the class level has no effect unless component-scanning is being used.
 * 建议在每个bean上加上@DependsOn({"tenantContextHolder"})
 *
 * <AUTHOR>
 * Created on 2022/11/08 16:31
 */
@Configuration
public class ConfigAutoConfiguration {

    /**
     * logger
     **/
    private static final Logger logger = LoggerFactory.getLogger(ConfigAutoConfiguration.class);

    /**
     * 使用配置组件映射加密机配置
     *
     * @return
     */
    @Bean
    @DependsOn({"tenantContextHolder"})
    public ClusterEmConfig emConfig() {
        return ConfigHolder.get().get(ClusterEmConfig.class);
    }

    /**
     * 使用配置组件映射DB组件的ShardingConfig
     *
     * @return
     */
    @Bean
    @DependsOn({"tenantContextHolder"})
    public ShardingConfig shardingConfig() {
        return ConfigHolder.get().get(ShardingConfig.class);
    }

    @Bean
    @DependsOn({"tenantContextHolder"})
    TrafficConfig getTrafficConfig(){
        return ConfigHolder.get().get(TrafficConfig.class);
    }

    /**
     * 使用配置组件映射DB组件的SwitchableDataSourceConfig
     *
     * @param switchConfigListener db配置监听器
     * @return
     */
    @Bean
    @DependsOn({"tenantContextHolder"})
    public SwitchableDataSourceConfig switchableConfig(SwitchConfigListener switchConfigListener) {
        List<SlotDataSourceConfig> slotDataSourceConfigArrayList = new ArrayList<>();
        ConfigHolder.getAll()
            .forEach(configHolder -> {
                RefreshableConfigWrapper original =
                    (RefreshableConfigWrapper) configHolder.getOriginal(SlotDataSourceConfig.class);
                if (original == null) {
                    return;
                }
                original.listenerPipeline().add(switchConfigListener);
                SlotDataSourceConfig slotDataSourceConfig = configHolder.get(SlotDataSourceConfig.class);
                List<DruidDataSourceProperties> datasource = slotDataSourceConfig.getDatasource();
                if (CollectionUtils.isNotEmpty(datasource) && StringUtils.isNotBlank(datasource.get(0).getUrl())) {
                    slotDataSourceConfigArrayList.add(slotDataSourceConfig);
                }
            });
        SwitchableDataSourceConfig switchableDataSourceConfig = new SwitchableDataSourceConfig();
        switchableDataSourceConfig.setSwitchable(slotDataSourceConfigArrayList);
        return switchableDataSourceConfig;
    }

    @Bean
    @DependsOn({"tenantContextHolder"})
    public TimerConfig timerConfig() {
        TimerContext.setServiceId(ServerIdUtils.getIpSm3Digest());
        return ConfigHolder.get().get(TimerConfig.class);
    }

    /**
     * 使用配置组件映射日志组件的LogConfig
     *
     * @return
     */
    @Bean
    @DependsOn({"tenantContextHolder"})
    public LogExtConfig logConfigSourceConfig() {
        LogExtConfig logConfig = ConfigHolder.get().get(LogExtConfig.class);
        // 程序启动的时候需要初始化logStore
        LogStoreFactory.getInstance().init(logConfig);
        return logConfig;
    }

    /**
     * 使用配置组件映射License组件的LicenseConfig
     *
     * @return
     */
    @Bean
    @DependsOn({"tenantContextHolder"})
    public LicenseConfig licenseConfig() {
        return BaseConfigWrapper.getLicenseConfig();
    }

    @Bean
    public KcspLogUploadService kcspLogUploadService() {
        return new KcspLogUploadServiceImpl();
    }

    @Bean
    @DependsOn({"tenantContextHolder"})
    public SysInfoConfig sysInfoConfig() {
        SysInfoConfig sysInfoConfig = BaseConfigWrapper.getSysInfoConfig();
        sysInfoConfig.setStartTime(new Date());
        ConfigHolder.get().save(sysInfoConfig);
        return sysInfoConfig;
    }

    @Bean
    @DependsOn({"tenantContextHolder"})
    public NpkiServerConfig npkiServerConfig() {
        return ConfigHolder.get().get(NpkiServerConfig.class);
    }

    @Bean
    @DependsOn({"tenantContextHolder"})
    public UIdConfig uIdConfig() {
        return ConfigHolder.get().get(UIdConfig.class);
    }

    @Bean
    public WorkerNodeEntity workerNodeEntity(NpkiServerConfig serverConfig) {
        return new WorkerNodeEntity(NetUtils.getLocalAddress().getHostAddress(),
            String.valueOf(serverConfig.getPort()), ServerIdUtils.getIpSm3Digest());
    }

    @Bean
    @DependsOn({"springRepositoryCollector"})
    public IFileConfigService fileConfigService() {
        FileConfigServiceImpl fileConfigService = new FileConfigServiceImpl();
        List<FileConfigEntity> fileConfigList = fileConfigService.getFileConfigList();
        if (CollectionUtils.isNotEmpty(fileConfigList)) {
            for (FileConfigEntity fileConfig : fileConfigList) {
                try {
                    String filePath = fileConfig.getFilePath();
                    if (!new File(filePath).isAbsolute()) {
                        // 系统中的所有相对路径必须以WEB-INF目录为基准进行解析
                        filePath = WEB_ROOT+ File.separator + filePath;
                    }
                    ResourceUtils.writeToFile(Base64Util.base64Decode(fileConfig.getFileContentBase64()), filePath);
                } catch (Exception e) {
                    logger.error("write file to {} failed!", fileConfig.getFilePath(), e);
                }
            }
        }
        return fileConfigService;
    }
}
