package kl.npki.base.service.configurations;

import kl.npki.base.core.configs.NpkiServerConfig;
import kl.npki.base.core.tenantholders.ConfigHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Set;

/**
 * HTTP方法过滤配置
 *
 * <AUTHOR>
 * @date 2024/09/02
 */
@Configuration(proxyBeanMethods = false)
public class HttpMethodFilterConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(HttpMethodFilterConfiguration.class);

    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE)
    @ConditionalOnProperty(prefix = "server", name = "http-disallowed")
    public OncePerRequestFilter httpMethodFilter() {
        NpkiServerConfig npkiServerConfig = ConfigHolder.get().get(NpkiServerConfig.class);
        logger.info(String.format(
            "%n%n System disabled HTTP methods:%s%n%n If adjustments are needed," +
                " please modify the 'server. http - decreased' property in the system configuration.", npkiServerConfig.getDisallowedMethods()));
        Set<String> disallowedMethodSet = npkiServerConfig.getDisallowedMethodSet();
        return new OncePerRequestFilter() {
            @Override
            protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
                String method = request.getMethod().toUpperCase();
                if (disallowedMethodSet.contains(method)) {
                    response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
                    return;
                }
                filterChain.doFilter(request, response);
            }
        };
    }
}