package kl.npki.base.service.runner;

import kl.nbase.db.support.slot.SlotDataSourceConfig;
import kl.npki.base.core.constant.DbConfigType;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.service.sharding.DynamicShardingTableManager;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 动态分表刷新任务，系统启动后执行一次
 *
 * <AUTHOR>
 * @date 2025/6/6 9:15
 **/
@Component
@Order(Integer.MIN_VALUE)
public class DynamicShardingTableRefreshRunner implements CommandLineRunner {

    @Resource
    private DynamicShardingTableManager dynamicShardingTableMgr;


    private boolean isSupportedDynamicRefreshDatabase(String driverClassName) {
        // 经过测试，mysql、oracle、kingbase、dm不需要动态刷新分表，其他GBase、GAUSS 数据库都需要执行动态分表刷新，先屏蔽已知的
        Set<String> supportedDrivers = new HashSet<>(Arrays.asList(
                DbConfigType.MYSQL.getDriver(),
                DbConfigType.MYSQL8.getDriver(),
                DbConfigType.ORACLE.getDriver(),
                DbConfigType.KINGBASE.getDriver(),
                DbConfigType.DM.getDriver()
        ));
        return supportedDrivers.contains(driverClassName);
    }


    @Override
    public void run(String... args) throws Exception {
        String driverClassName = ConfigHolder.get().get(SlotDataSourceConfig.class).getDruid().getDriverClassName();
        if (isSupportedDynamicRefreshDatabase(driverClassName)) {
            return;
        }
        // 执行刷新
//        dynamicShardingTableMgr.refresh();
    }
}
