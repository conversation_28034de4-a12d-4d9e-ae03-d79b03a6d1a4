package kl.npki.base.service.runner;

import kl.npki.base.service.sharding.DynamicShardingTableManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 动态分表刷新任务，系统启动后执行一次
 *
 * <AUTHOR>
 * @date 2025/6/6 9:15
 **/
@Component
@Order(Integer.MIN_VALUE)
public class DynamicShardingTableRefreshRunner implements CommandLineRunner {

    @Autowired
    private DynamicShardingTableManager dynamicShardingTableMgr;

    @Override
    public void run(String... args) throws Exception {
        dynamicShardingTableMgr.refresh();
    }
}
