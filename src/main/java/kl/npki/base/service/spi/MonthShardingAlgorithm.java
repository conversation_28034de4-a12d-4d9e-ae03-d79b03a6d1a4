package kl.npki.base.service.spi;

import com.google.common.collect.Range;
import kl.npki.base.service.util.MonthShardingUtil;
import org.apache.shardingsphere.sharding.api.sharding.standard.PreciseShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.RangeShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.StandardShardingAlgorithm;

import java.time.LocalDateTime;
import java.util.Collection;

/**
 * 根据月份进行分表
 *
 * <AUTHOR> Guiyu
 * @Date 2023/12/19
 */
public class MonthShardingAlgorithm implements StandardShardingAlgorithm<LocalDateTime> {

    public static final String TYPE_NAME = "KL_MONTH";

    @Override
    public String getType() {
        return TYPE_NAME;
    }

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<LocalDateTime> shardingValue) {
        return MonthShardingUtil.getTableName(shardingValue.getLogicTableName(), shardingValue.getValue());
    }

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, RangeShardingValue<LocalDateTime> shardingValue) {
        Range<LocalDateTime> valueRange = shardingValue.getValueRange();
        // 部署时间
        final LocalDateTime deployDate = MonthShardingUtil.getEarliestTableShardingDate();
        // 当前时间
        LocalDateTime currentDate = LocalDateTime.now();
        // 查询条件中时间范围的下界
        LocalDateTime lowerDate = valueRange.hasLowerBound() ? valueRange.lowerEndpoint() : deployDate;
        // 查询条件中时间范围的上界
        LocalDateTime upperDate = valueRange.hasUpperBound() ? valueRange.upperEndpoint() : currentDate;
        // 限制上、下界不会比部署时间早
        lowerDate = lowerDate.isBefore(deployDate) ? deployDate : lowerDate;
        upperDate = upperDate.isBefore(deployDate) ? deployDate : upperDate;
        // 限制上、下界不会比当前时间晚
        lowerDate = lowerDate.isAfter(currentDate) ? currentDate : lowerDate;
        upperDate = upperDate.isAfter(currentDate) ? currentDate : upperDate;
        // 根据时间范围获取对应的所有分表名称
        return MonthShardingUtil.getAllTableName(shardingValue.getLogicTableName(), lowerDate, upperDate);
    }

}
