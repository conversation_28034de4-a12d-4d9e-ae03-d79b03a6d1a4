package kl.npki.base.service.repository;

import kl.npki.base.core.repository.BaseRepository;
import kl.npki.base.core.repository.RepositoryCollector;
import kl.npki.base.core.repository.RepositoryFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Component
public class SpringRepositoryCollector implements RepositoryCollector, ApplicationContextAware, InitializingBean {

    private ApplicationContext appContext;

    @Override
    public Map<Class<?>, BaseRepository> collect() {
        Map<String, BaseRepository> repositoryMap = appContext.getBeansOfType(BaseRepository.class);
        if (!CollectionUtils.isEmpty(repositoryMap)) {
            Map<Class<?>, BaseRepository> result = new HashMap<>();
            repositoryMap.values().forEach(r -> result.put(AopUtils.getTargetClass(r).getInterfaces()[0], r));
            return result;
        }
        return Collections.emptyMap();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        appContext = applicationContext;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        RepositoryFactory.init(this);
    }
}
