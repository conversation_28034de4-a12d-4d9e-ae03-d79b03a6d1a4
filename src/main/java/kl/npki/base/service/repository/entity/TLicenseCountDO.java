package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.tools.dbtool.constant.DataType;


/**
 * 存储业务则License使用数据
 *
 * <AUTHOR> by ni<PERSON><PERSON> on 2025-05-29 16:35
 */
@TableName("t_license_count")
@KlDbTable(tbName = "t_license_count", caption = "License授权数据存储表", indexes = {@Index(name = "uk_cus_key", unique = true, columnList = {"cus_key"})
})
public class TLicenseCountDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 业务方自定义的统计标识
     */
    @KlDbField(type = DataType.VARCHAR, size = 255, remarks = "业务方自定义的统计标识")
    private String cusKey;

    /**
     * 业务方自定义的统计标识对应的值
     */
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "业务方自定义的统计标识对应的值")
    private Long cusValue;

    /**
     * 乐观锁版本号
     */
    @KlDbField(type = DataType.BIGINT, size = 20, remarks = "乐观锁版本号")
    private Long version;

    public String getCusKey() {
        return cusKey;
    }

    public void setCusKey(String cusKey) {
        this.cusKey = cusKey;
    }

    public Long getCusValue() {
        return cusValue;
    }

    public void setCusValue(Long cusValue) {
        this.cusValue = cusValue;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        return "TLicenseCountDO{" +
            "cusKey='" + cusKey + '\'' +
            ", cusValue=" + cusValue +
            ", version=" + version +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
