package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;
import kl.tools.dbtool.constant.DataType;

import java.time.LocalDateTime;

/**
 * 服务日志数据库实体
 *
 * <AUTHOR>
 * @since 2023/2/28
 */
@TableName("t_api_log")
@KlDbTable(tbName = "t_api_log", indexes = {@Index(name = "idx_log_when", columnList = {"log_when", "caller_name"}),
     @Index(name = "idx_tkc_client_ip", columnList = {"client_ip"}),
     @Index(name = "idx_tkc_biz", columnList = {"biz"}),
})
public class TApiLogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 链路id
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String traceId;

    /**
     * 跨度id
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String spanId;

    /**
     * 客户端ip
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String clientIp;

    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @KlDbField(type = DataType.DATE, size = 6)
    private LocalDateTime logWhen;

    /**
     * 业务id
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String bizId;

    /**
     * 请求业务
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String biz;

    /**
     * 请求详细内容, 报错错误信息
     */
    @KlDbField(type = DataType.CLOB)
    private String detail;

    /**
     * 请求结果
     */
    @KlDbField(type = DataType.TINYINT)
    private Boolean result;

    /**
     * 调用者标识
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String callerId;

    /**
     * 调用者名称
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String callerName;

    /**
     * 业务主体标识
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String entityId;

    /**
     * 请求
     */
    @KlDbField(type = DataType.CLOB)
    private String request;

    /**
     * 响应
     */
    @KlDbField(type = DataType.CLOB)
    private String response;

    /**
     * 服务耗时 单位毫秒
     */
    @KlDbField(type = DataType.INTEGER, size = 8)
    private Long elapsedTime;

    /**
     * cid
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String certId;

    /**
     * 扩展项1
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String ext1;

    /**
     * 扩展项2
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String ext2;

    /**
     * 扩展项3
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String ext3;

    /**
     * 签名值
     */
    @KlDbField(type = DataType.VARCHAR, size = 2048)
    private String signData;

    /**
     * 验签结果
     */
    @KlDbField(type = DataType.INTEGER, size = 1)
    private Integer signVerify;

    /**
     * 签名原文
     */
    @KlDbField(type = DataType.CLOB)
    private String originData;

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getSpanId() {
        return spanId;
    }

    public void setSpanId(String spanId) {
        this.spanId = spanId;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public LocalDateTime getLogWhen() {
        return logWhen;
    }

    public void setLogWhen(LocalDateTime logWhen) {
        this.logWhen = logWhen;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getBiz() {
        return biz;
    }

    public void setBiz(String biz) {
        this.biz = biz;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public Boolean getResult() {
        return result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public Long getElapsedTime() {
        return elapsedTime;
    }

    public void setElapsedTime(Long elapsedTime) {
        this.elapsedTime = elapsedTime;
    }

    public String getEntityId() {
        return entityId;
    }

    public void setEntityId(String entityId) {
        this.entityId = entityId;
    }

    public String getCallerId() {
        return callerId;
    }

    public void setCallerId(String callerId) {
        this.callerId = callerId;
    }

    public String getCallerName() {
        return callerName;
    }

    public void setCallerName(String callerName) {
        this.callerName = callerName;
    }

    public String getCertId() {
        return certId;
    }

    public void setCertId(String certId) {
        this.certId = certId;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    public String getSignData() {
        return signData;
    }

    public void setSignData(String signData) {
        this.signData = signData;
    }

    public Integer getSignVerify() {
        return signVerify;
    }

    public void setSignVerify(Integer signVerify) {
        this.signVerify = signVerify;
    }

    public String getOriginData() {
        return originData;
    }

    public void setOriginData(String originData) {
        this.originData = originData;
    }
}
