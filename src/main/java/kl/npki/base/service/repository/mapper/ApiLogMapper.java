package kl.npki.base.service.repository.mapper;

import kl.nbase.db.support.mybatis.mapper.KlBaseMapper;
import kl.npki.base.core.biz.log.model.ApiLogCountServiceResult;
import kl.npki.base.service.repository.entity.TApiLogDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/9
 */
public interface ApiLogMapper extends KlBaseMapper<TApiLogDO> {

    /**
     * 以请求者名称，请求结果，业务名称进行分组统计
     * @param callerNames 请求者名称列表
     * @param startTime 起始时间
     * @param endTime 截至时间
     * @return
     */
    List<ApiLogCountServiceResult> countGroupByCallerAndResultAndBiz(@Param("callerNames") List<String> callerNames, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 以 请求结果，业务名称进行分组统计
     * @param bizNames 业务名称
     * @param startTime 起始时间
     * @param endTime 截至时间
     * @return
     */
    List<ApiLogCountServiceResult> countGroupByResultAndBiz(@Param("bizNames") List<String> bizNames, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
