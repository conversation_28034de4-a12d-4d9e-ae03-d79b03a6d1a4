package kl.npki.base.service.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.nbase.exception.ValidationException;
import kl.npki.base.core.annotation.KlTransactional;
import kl.npki.base.core.biz.license.model.LicenseCountEntity;
import kl.npki.base.core.repository.ILicenseCountRepository;
import kl.npki.base.service.exception.BaseServiceValidationError;
import kl.npki.base.service.repository.entity.TLicenseCountDO;
import kl.npki.base.service.repository.mapper.LicenseCountMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static kl.npki.base.service.constants.I18nExceptionInfoConstant.EXCEEDS_LICENSE_LIMIT;
import static kl.npki.base.service.constants.I18nExceptionInfoConstant.LICENSE_UPDATE_ERROR;

/**
 * license授权数据库操作实现
 *
 * <AUTHOR> by niugang on 2025-05-29 17:27
 */
@Repository
public class LicenseCountRepositoryImpl extends KlServiceImpl<LicenseCountMapper, TLicenseCountDO> implements ILicenseCountRepository {
    private static final Logger LOGGER = LoggerFactory.getLogger(LicenseCountRepositoryImpl.class);
    @Resource
    private ConvertService convertService;

    @Override
    public LicenseCountEntity searchByCusKey(String cusKey) {
        LambdaQueryWrapper<TLicenseCountDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TLicenseCountDO::getCusKey, cusKey);
        TLicenseCountDO licenseCountDO = getOne(queryWrapper);
        if (licenseCountDO == null) {
            return new LicenseCountEntity(cusKey, 0L);
        }
        return convertService.convert(licenseCountDO, LicenseCountEntity.class);
    }

    @Override
    public List<LicenseCountEntity> batchSearchByCusKey(List<String> cusKeys) {
        if (CollectionUtils.isEmpty(cusKeys)) {
            return Collections.emptyList();
        }
        List<TLicenseCountDO> licenseCountDOList = getLicenseCountDOList(cusKeys);
        return convertService.convert(licenseCountDOList, LicenseCountEntity.class);
    }

    private List<TLicenseCountDO> getLicenseCountDOList(List<String> cusKeys) {
        LambdaQueryWrapper<TLicenseCountDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(TLicenseCountDO::getCusKey, new HashSet<>(cusKeys));
        return list(queryWrapper);
    }

    @Override
    @KlTransactional
    public void batchSaveOrUpdate(List<LicenseCountEntity> licenseCountEntities, boolean isCheckMaxLimit) {
        if (CollectionUtils.isEmpty(licenseCountEntities)) {
            return;
        }
        // 移除 cusKey 为空的数据
        List<LicenseCountEntity> filteredList = licenseCountEntities.stream()
            .filter(entity -> StringUtils.isNotBlank(entity.getCusKey())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredList)) {
            return;
        }

        // 提取 cusKeys 并去重
        List<String> cusKeys = filteredList.stream()
            .map(LicenseCountEntity::getCusKey)
            .distinct().collect(Collectors.toList());

        // 构建 cusKey 到实体的映射（保留第一个出现的）
        Map<String, LicenseCountEntity> paramMap = new HashMap<>();
        for (LicenseCountEntity entity : filteredList) {
            paramMap.putIfAbsent(entity.getCusKey(), entity);
        }

        List<TLicenseCountDO> licenseCountDOList = getLicenseCountDOList(cusKeys);
        Map<String, TLicenseCountDO> dbMap = new HashMap<>(4);
        for (TLicenseCountDO item : licenseCountDOList) {
            dbMap.put(item.getCusKey(), item);
        }
        //判更新还是新增
        DetermineResultWrapper determineResult = determineData(paramMap, dbMap, isCheckMaxLimit);
        //添加
        if (!determineResult.getSaveList().isEmpty()) {
            saveBatch(determineResult.getSaveList());
        }
        //更新
        if (!determineResult.getUpdateList().isEmpty()) {
            //更新操作，需要二次校验maxLimit防止超限，基于乐观锁实现
            if (isCheckMaxLimit) {
                //增量更新
                deltaUpdate(determineResult.getUpdateList(), paramMap);
            } else {
                getBaseMapper().batchUpdate(determineResult.getUpdateList());
            }
        }


    }

    private void deltaUpdate(List<TLicenseCountDO> updateList, Map<String, LicenseCountEntity> paramMap) {
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }

        int maxAttempts = 3;
        for (TLicenseCountDO item : updateList) {
            boolean success = false;
            int attempt = 0;
            while (!success && attempt++ < maxAttempts) {
                try {
                    // 每次重试都重新执行一次完整的 delta 更新逻辑
                    doSingleDeltaUpdate(item, paramMap);
                    success = true;
                } catch (Exception e) {
                    if (e instanceof ValidationException) {
                        ValidationException validationException = (ValidationException) e;
                        String secondCode = validationException.getSecondCode();
                        if (secondCode.equals(BaseServiceValidationError.LICENSE_ERROR.getSecondCode())) {
                            continue;
                        }
                    }
                    throw e;
                }
            }

            if (!success) {
                throw BaseServiceValidationError.LICENSE_ERROR.toException(LICENSE_UPDATE_ERROR, item.getCusKey(), item.getVersion());
            }
        }
    }

    public void doSingleDeltaUpdate(TLicenseCountDO item, Map<String, LicenseCountEntity> paramMap) {
        LambdaQueryWrapper<TLicenseCountDO> forUpdate = Wrappers.<TLicenseCountDO>lambdaQuery().eq(TLicenseCountDO::getId, item.getId()).last("for update");
        //查询数据库最新数据
        TLicenseCountDO dbCurrentCount = getBaseMapper().selectOne(forUpdate);
        Long dbValue = dbCurrentCount.getCusValue();
        Long maxLimit = paramMap.get(item.getCusKey()).getMaxLimit();
        if (dbValue + item.getCusValue() > maxLimit) {
            throw BaseServiceValidationError.LICENSE_ERROR.toException(EXCEEDS_LICENSE_LIMIT, item.getCusKey(), maxLimit);
        }
        LocalDateTime updateTime = LocalDateTime.now();
        int rowsUpdated = getBaseMapper().updateWithOptimisticLock(
            item.getId(),
            item.getCusValue(),
            dbCurrentCount.getVersion(), updateTime
        );
        if (rowsUpdated != 1) {
            throw BaseServiceValidationError.LICENSE_ERROR.toException(LICENSE_UPDATE_ERROR, item.getCusKey(), dbCurrentCount.getVersion());
        }
    }

    /**
     * 判断数据是新增还是更新
     *
     * @param paramMap 入参
     * @param dbMap    数据库查询结果
     * @return 判断结果
     */
    private DetermineResultWrapper determineData(Map<String, LicenseCountEntity> paramMap, Map<String, TLicenseCountDO> dbMap, boolean isCheckMaxLimit) {

        List<TLicenseCountDO> updateList = new ArrayList<>();
        List<TLicenseCountDO> saveList = new ArrayList<>();

        for (LicenseCountEntity entity : paramMap.values()) {
            String cusKey = entity.getCusKey();
            Long paramCusValue = Optional.ofNullable(entity.getCusValue()).orElse(0L);
            Long maxLimit = entity.getMaxLimit();
            //更新
            if (dbMap.containsKey(cusKey)) {
                TLicenseCountDO dbLicenseCount = dbMap.get(cusKey);
                // 检查是否超限
                if (isCheckMaxLimit && paramCusValue + dbLicenseCount.getCusValue() > maxLimit) {
                    LOGGER.warn("License usage exceeds total quota. cusKey: {}, required: {}, total: {}", cusKey, paramCusValue, maxLimit);
                    throw BaseServiceValidationError.LICENSE_ERROR.toException(EXCEEDS_LICENSE_LIMIT, cusKey, maxLimit);
                }
                dbLicenseCount.setUpdateTime(LocalDateTime.now());
                dbLicenseCount.setCusValue(paramCusValue);
                updateList.add(dbLicenseCount);
            } else {
                if (isCheckMaxLimit && entity.getCusValue() > entity.getMaxLimit()) {
                    LOGGER.warn("Initial license value exceeds total quota. cusKey: {}, value: {}, total: {}", cusKey, entity.getCusValue(), entity.getMaxLimit());
                    throw BaseServiceValidationError.LICENSE_ERROR.toException(EXCEEDS_LICENSE_LIMIT, cusKey, maxLimit);
                }
                TLicenseCountDO licenseCountDO = convertService.convert(entity, TLicenseCountDO.class);
                if (licenseCountDO.getCusValue() == null) {
                    licenseCountDO.setCusValue(0L);
                }
                licenseCountDO.setVersion(0L);

                saveList.add(licenseCountDO);
            }
        }

        return new DetermineResultWrapper(updateList, saveList);
    }

    /**
     * 数据判断结果包装器
     */
    private static class DetermineResultWrapper {

        private final List<TLicenseCountDO> updateList;
        private final List<TLicenseCountDO> saveList;


        public DetermineResultWrapper(List<TLicenseCountDO> updateList, List<TLicenseCountDO> saveList) {
            this.updateList = updateList;
            this.saveList = saveList;
        }

        public List<TLicenseCountDO> getUpdateList() {
            return updateList;
        }

        public List<TLicenseCountDO> getSaveList() {
            return saveList;
        }

    }


}
