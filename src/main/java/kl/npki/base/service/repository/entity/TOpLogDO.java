package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.npki.base.core.common.date.CustomLocalDateTimeSerializer;
import kl.tools.dbtool.constant.DataType;

import java.time.LocalDateTime;

/**
 * 操作日志数据库操作类
 *
 * <AUTHOR>
 * @date 2023/2/28
 */
@TableName("T_OP_LOG")
@KlDbTable(tbName = "T_OP_LOG")
public class TOpLogDO extends IntegrityProtectedDO {

    private static final long serialVersionUID = -4287939342834355223L;

    /**
     * 链路id
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 255, remarks = "链路id")
    private String traceId;

    /**
     * 跨度id
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 255, remarks = "跨度id")
    private String spanId;

    /**
     * 操作人员
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 255, remarks = "操作人员")
    private String logWho;

    /**
     * 操作人员
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 255, remarks = "操作人员")
    private String username;
    /**
     * 操作名称
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 255, remarks = "操作名称")
    private String logDo;

    /**
     * <p>
     * 操作结果id
     */
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, remarks = "操作结果id")
    private Boolean result;

    /**
     * 操作详情
     */
    @DataFullField
    @KlDbField(type = DataType.CLOB, remarks = "操作详情")
    private String logWhat;

    /**
     * 客户端ip
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 255, remarks = "客户端ip")
    private String clientIp;

    /**
     * 操作时间
     */
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @KlDbField(type = DataType.DATE, remarks = "操作时间", size = 6)
    private LocalDateTime logWhen;

    /**
     * 签名值
     */
    @DataFullField
    @KlDbField(type = DataType.CLOB, remarks = "签名值")
    private String signData;

    /**
     * 请求
     */
    @DataFullField
    @KlDbField(type = DataType.CLOB, remarks = "请求")
    private String request;

    /**
     * 是否属于安全日志
     */
    @DataFullField
    @KlDbField(type = DataType.TINYINT, size = 1, defaultValue = "0", remarks = "是否属于安全日志")
    private Boolean isSecurityLog;

    /**
     * 签名原文
     */
    @DataFullField
    @KlDbField(type = DataType.CLOB, remarks = "签名原文")
    private String originData;

    /**
     * 审计状态：0-待审计，1-审计成功，2-审计失败
     */
    @DataFullField
    @KlDbField(type = DataType.TINYINT, size = 1, remarks = "审计状态：-1 不审计 0-待审计，1-审计成功，2-审计失败")
    private Integer auditStatus;

    /**
     * 审计时间
     */
    @DataFullField
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @KlDbField(type = DataType.DATE, remarks = "审计时间", size = 6)
    private LocalDateTime auditTime;

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getSpanId() {
        return spanId;
    }

    public void setSpanId(String spanId) {
        this.spanId = spanId;
    }

    public String getLogWho() {
        return logWho;
    }

    public void setLogWho(String logWho) {
        this.logWho = logWho;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getLogDo() {
        return logDo;
    }

    public void setLogDo(String logDo) {
        this.logDo = logDo;
    }

    public Boolean getResult() {
        return result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    public String getLogWhat() {
        return logWhat;
    }

    public void setLogWhat(String logWhat) {
        this.logWhat = logWhat;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public LocalDateTime getLogWhen() {
        return logWhen;
    }

    public void setLogWhen(LocalDateTime logWhen) {
        this.logWhen = logWhen;
    }

    public Boolean isSecurityLog() {
        return isSecurityLog;
    }

    public void setSecurityLog(Boolean isSecurityLog) {
        this.isSecurityLog = isSecurityLog;
    }

    public String getSignData() {
        return signData;
    }

    public void setSignData(String signData) {
        this.signData = signData;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public LocalDateTime getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(LocalDateTime auditTime) {
        this.auditTime = auditTime;
    }

    public String getOriginData() {
        return originData;
    }

    public void setOriginData(String originData) {
        this.originData = originData;
    }
}

