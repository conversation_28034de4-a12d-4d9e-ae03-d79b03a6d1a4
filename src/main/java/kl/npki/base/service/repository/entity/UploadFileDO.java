package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.tools.dbtool.constant.DataType;

import java.time.LocalDateTime;

/**
 * 用于表示上传文件信息的实体类
 *
 * <AUTHOR>
 * @create 2024/5/8 上午9:38
 */
@TableName("T_UPLOAD_FILE")
@KlDbTable(tbName = "T_UPLOAD_FILE", indexes = {@Index(name = "IDX_INTERFACE_NAME", columnList = "INTERFACE_NAME")})
public class UploadFileDO {

    /**
     * 数据标识ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @KlDbField(type = DataType.BIGINT, size = 20, isPrimary = true, notNull = true)
    private Long id;

    /**
     * 上传文件业务接口名称
     */
    @KlDbField(type = DataType.VARCHAR, size = 128, notNull = true)
    private String interfaceName;

    /**
     * 文件名称
     */
    @KlDbField(type = DataType.VARCHAR, size = 128)
    private String fileName;

    /**
     * Excel文件中的数据处理状态
     * <li>-1 尚未开始处理</li>
     * <li>0 正在处理</li>
     * <li>1 处理完成</li>
     */
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "-1")
    private Integer processStatus;

    /**
     * 总计需要处理数量
     */
    @KlDbField(type = DataType.INTEGER, size = 8, notNull = true)
    private Integer totalCount;

    /**
     * 已经处理数量
     */
    @KlDbField(type = DataType.INTEGER, size = 8, notNull = true)
    private Integer processCount;

    /**
     * 处理成功条数
     */
    @KlDbField(type = DataType.INTEGER, size = 8, notNull = true)
    private Integer processSuccessCount;

    /**
     * 处理失败条数
     */
    @KlDbField(type = DataType.INTEGER, size = 8, notNull = true)
    private Integer processErrorCount;

    /**
     * 上传的文件
     */
    @KlDbField(type = DataType.CLOB)
    // TODO 数据脱敏
    private String requestFile;

    /**
     * 上传文件内容处理结果
     */
    @KlDbField(type = DataType.CLOB)
    // TODO 数据脱敏
    private String resultFile;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT, update = "now()")
    @KlDbField(type = DataType.DATE, notNull = true)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, update = "now()")
    @KlDbField(type = DataType.DATE, notNull = true)
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除标志
     */
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "0")
    private Integer isDelete;

    public Long getId() {
        return id;
    }

    public UploadFileDO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getInterfaceName() {
        return interfaceName;
    }

    public UploadFileDO setInterfaceName(String interfaceName) {
        this.interfaceName = interfaceName;
        return this;
    }

    public String getFileName() {
        return fileName;
    }

    public UploadFileDO setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public Integer getProcessStatus() {
        return processStatus;
    }

    public UploadFileDO setProcessStatus(Integer processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public UploadFileDO setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
        return this;
    }

    public Integer getProcessCount() {
        return processCount;
    }

    public UploadFileDO setProcessCount(Integer processCount) {
        this.processCount = processCount;
        return this;
    }

    public Integer getProcessSuccessCount() {
        return processSuccessCount;
    }

    public UploadFileDO setProcessSuccessCount(Integer processSuccessCount) {
        this.processSuccessCount = processSuccessCount;
        return this;
    }

    public Integer getProcessErrorCount() {
        return processErrorCount;
    }

    public UploadFileDO setProcessErrorCount(Integer processErrorCount) {
        this.processErrorCount = processErrorCount;
        return this;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public UploadFileDO setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public UploadFileDO setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
        return this;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public UploadFileDO setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    public String getRequestFile() {
        return requestFile;
    }

    public UploadFileDO setRequestFile(String requestFile) {
        this.requestFile = requestFile;
        return this;
    }

    public String getResultFile() {
        return resultFile;
    }

    public UploadFileDO setResultFile(String resultFile) {
        this.resultFile = resultFile;
        return this;
    }
}