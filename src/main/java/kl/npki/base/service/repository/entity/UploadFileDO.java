package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.tools.dbtool.constant.DataType;


/**
 * 用于表示上传文件信息的实体类
 *
 * <AUTHOR>
 * @create 2024/5/8 上午9:38
 */
@TableName("T_UPLOAD_FILE")
@KlDbTable(tbName = "T_UPLOAD_FILE", indexes = {@Index(name = "IDX_INTERFACE_NAME", columnList = "INTERFACE_NAME")})
public class UploadFileDO extends BaseDO {

    /**
     * 上传文件业务接口名称
     */
    @KlDbField(type = DataType.VARCHAR, size = 128, notNull = true)
    private String interfaceName;

    /**
     * 文件名称
     */
    @KlDbField(type = DataType.VARCHAR, size = 128)
    private String fileName;

    /**
     * Excel文件中的数据处理状态
     * <li>-1 尚未开始处理</li>
     * <li>0 正在处理</li>
     * <li>1 处理完成</li>
     */
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "-1")
    private Integer processStatus;

    /**
     * 总计需要处理数量
     */
    @KlDbField(type = DataType.INTEGER, size = 8, notNull = true)
    private Integer totalCount;

    /**
     * 已经处理数量
     */
    @KlDbField(type = DataType.INTEGER, size = 8, notNull = true)
    private Integer processCount;

    /**
     * 处理成功条数
     */
    @KlDbField(type = DataType.INTEGER, size = 8, notNull = true)
    private Integer processSuccessCount;

    /**
     * 处理失败条数
     */
    @KlDbField(type = DataType.INTEGER, size = 8, notNull = true)
    private Integer processErrorCount;

    /**
     * 上传的文件
     */
    @KlDbField(type = DataType.CLOB)
    // TODO 数据脱敏
    private String requestFile;

    /**
     * 上传文件内容处理结果
     */
    @KlDbField(type = DataType.CLOB)
    // TODO 数据脱敏
    private String resultFile;

    public String getInterfaceName() {
        return interfaceName;
    }

    public UploadFileDO setInterfaceName(String interfaceName) {
        this.interfaceName = interfaceName;
        return this;
    }

    public String getFileName() {
        return fileName;
    }

    public UploadFileDO setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public Integer getProcessStatus() {
        return processStatus;
    }

    public UploadFileDO setProcessStatus(Integer processStatus) {
        this.processStatus = processStatus;
        return this;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public UploadFileDO setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
        return this;
    }

    public Integer getProcessCount() {
        return processCount;
    }

    public UploadFileDO setProcessCount(Integer processCount) {
        this.processCount = processCount;
        return this;
    }

    public Integer getProcessSuccessCount() {
        return processSuccessCount;
    }

    public UploadFileDO setProcessSuccessCount(Integer processSuccessCount) {
        this.processSuccessCount = processSuccessCount;
        return this;
    }

    public Integer getProcessErrorCount() {
        return processErrorCount;
    }

    public UploadFileDO setProcessErrorCount(Integer processErrorCount) {
        this.processErrorCount = processErrorCount;
        return this;
    }

    public String getRequestFile() {
        return requestFile;
    }

    public UploadFileDO setRequestFile(String requestFile) {
        this.requestFile = requestFile;
        return this;
    }

    public String getResultFile() {
        return resultFile;
    }

    public UploadFileDO setResultFile(String resultFile) {
        this.resultFile = resultFile;
        return this;
    }
}