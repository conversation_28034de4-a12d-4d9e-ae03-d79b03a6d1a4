package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.tools.dbtool.constant.DataType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: guoq
 * @Date: 2023/12/28
 * @description: 分布式id生成，用于自动建表
 */
@TableName("t_worker_node")
@KlDbTable(tbName = "t_worker_node")
public class WorkerNodeDO implements Serializable {

    private static final long serialVersionUID = 6596474875615135590L;
    /**
     * Entity unique id (table unique)
     */
    @TableId(type = IdType.AUTO)
    @KlDbField(type = DataType.BIGINT, size = 20, isPrimary = true, autoIncrement = true)
    private Long workId;

    /**
     * Type of CONTAINER: HostName, ACTUAL : IP.
     */
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String hostName;

    /**
     * Type of CONTAINER: Port, ACTUAL : Port
     */
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String port;

    /**
     * service 唯一标识 或者 机器码
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String serviceId;

    /**
     * 记录唯一标识
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String recordId;


    /**
     * Created time
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @KlDbField(type = DataType.DATE, size = 6)
    private LocalDateTime createTime;

    /**
     * Last modified
     */
    @KlDbField(type = DataType.DATE, size = 6)
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
