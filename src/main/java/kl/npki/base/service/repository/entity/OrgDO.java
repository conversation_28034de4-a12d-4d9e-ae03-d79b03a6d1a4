package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.tools.dbtool.constant.DataType;


/**
 * <AUTHOR>
 * @create 2024/3/19 13:42
 */
@TableName("T_ORG_MGR")
@KlDbTable(tbName = "T_ORG_MGR", indexes = {
        @Index(name = "UK_ORG_CODE", columnList = {"ORG_CODE"}, unique = true),
        @Index(name = "IDX_PARENT_CODE", columnList = {"PARENT_CODE"}),
        @Index(name = "IDX_ORG_NAME", columnList = {"ORG_NAME"})})
public class OrgDO extends IntegrityProtectedDO {

    private static final long serialVersionUID = 260003845637993407L;

    /**
     * 机构名称
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256, notNull = true)
    private String orgName;

    /**
     * 机构全称
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String fullName;

    /**
     * 机构名称拼音
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String namePinyin;

    /**
     * 机构状态
     */
    @DataFullField
    @KlDbField(type = DataType.TINYINT, notNull = true)
    private Integer orgStatus;

    /**
     * 机构编码
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String orgCode;

    /**
     * 上级机构
     */
    @DataFullField
    @KlDbField(type = DataType.BIGINT, size = 20, notNull = true)
    private Long parentId;

    /**
     * 上级机构编码
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String parentCode;

    /**
     * 机构类型
     */
    @DataFullField
    @KlDbField(type = DataType.INTEGER)
    private Integer orgType;

    /**
     * 机构完整编码
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String fullCode;

    /**
     * 机构完整ID
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String fullId;

    /**
     * 机构排序码
     */
    @DataFullField
    @KlDbField(type = DataType.INTEGER)
    private int orderIndex;

    /**
     * 机构排序完整编码
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String orderFullIndex;

    /**
     * 机构级别
     */
    @DataFullField
    @KlDbField(type = DataType.INTEGER)
    private Integer orgLevel;

    /**
     * 联系人
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String linkman;

    /**
     * 联系电话
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String telephone;

    /**
     * 邮政编码
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String postcode;

    /**
     * 联系地址
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String address;

    /**
     * 机构描述
     */
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 256)
    private String orgDesc;

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getNamePinyin() {
        return namePinyin;
    }

    public void setNamePinyin(String namePinyin) {
        this.namePinyin = namePinyin;
    }

    public Integer getOrgStatus() {
        return orgStatus;
    }

    public void setOrgStatus(Integer orgStatus) {
        this.orgStatus = orgStatus;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public String getFullCode() {
        return fullCode;
    }

    public void setFullCode(String fullCode) {
        this.fullCode = fullCode;
    }

    public int getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(int orderIndex) {
        this.orderIndex = orderIndex;
    }

    public String getOrderFullIndex() {
        return orderFullIndex;
    }

    public void setOrderFullIndex(String orderFullIndex) {
        this.orderFullIndex = orderFullIndex;
    }

    public Integer getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(Integer orgLevel) {
        this.orgLevel = orgLevel;
    }

    public String getLinkman() {
        return linkman;
    }

    public void setLinkman(String linkman) {
        this.linkman = linkman;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getOrgDesc() {
        return orgDesc;
    }

    public void setOrgDesc(String orgDesc) {
        this.orgDesc = orgDesc;
    }

    public String getFullId() {
        return fullId;
    }

    public OrgDO setFullId(String fullId) {
        this.fullId = fullId;
        return this;
    }

    @Override
    public String toString() {
        return "OrgDO{" +
            "orgName='" + orgName + '\'' +
            ", fullName='" + fullName + '\'' +
            ", namePinyin='" + namePinyin + '\'' +
            ", orgStatus=" + orgStatus +
            ", orgCode='" + orgCode + '\'' +
            ", parentId=" + parentId +
            ", parentCode='" + parentCode + '\'' +
            ", orgType=" + orgType +
            ", fullCode='" + fullCode + '\'' +
            ", fullId='" + fullId + '\'' +
            ", orderIndex=" + orderIndex +
            ", orderFullIndex='" + orderFullIndex + '\'' +
            ", orgLevel=" + orgLevel +
            ", linkman='" + linkman + '\'' +
            ", telephone='" + telephone + '\'' +
            ", postcode='" + postcode + '\'' +
            ", address='" + address + '\'' +
            ", orgDesc='" + orgDesc + '\'' +
            ", fullDataHash='" + fullDataHash + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}