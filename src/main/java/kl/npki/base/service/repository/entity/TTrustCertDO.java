package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.tools.dbtool.constant.DataType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/9/7
 * @desc
 */
@TableName("T_TRUST_CERT")
@KlDbTable(tbName = "T_TRUST_CERT")
public class TTrustCertDO implements Serializable {

    private static final long serialVersionUID = 6917881311957736935L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID, value = "ID")
    @KlDbField(type = DataType.BIGINT, size = 20, isPrimary = true)
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "TENANT_ID")
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String tenantId;

    /**
     * 证书序列号
     */
    @TableField(value = "HEX_SN")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String hexSn;

    /**
     * 使用者CN
     */
    @TableField(value = "SUBJECT_CN")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String subjectCn;

    /**
     * 发行者CN
     */
    @TableField(value = "ISSUER_CN")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String issuerCn;

    /**
     * 证书DN
     * （长度限制需大于页面的省市等长度限制总和）
     */
    @TableField(value = "SUBJECT_DN")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 600)
    private String subjectDn;

    /**
     * 发行者密钥标识符
     */
    @TableField(value = "ISSUER_KEY_ID")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String issuerKeyId;

    /**
     * MUL 使用者密钥标识符
     */
    @TableField(value = "KEY_ID")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String keyId;

    /**
     * 生效时间
     */
    @TableField(value = "VALID_START")
    @KlDbField(type = DataType.DATE)
    private LocalDateTime validStart;

    /**
     * 失效时间
     */
    @TableField(value = "VALID_END")
    @KlDbField(type = DataType.DATE)
    private LocalDateTime validEnd;

    /**
     * 证书
     */
    @TableField(value = "CERT_VALUE")
    @DataFullField
    @KlDbField(type = DataType.CLOB)
    private String certValue;

    /**
     * 加密证书
     */
    @TableField(value = "ENC_CERT_VALUE")
    @DataFullField
    @KlDbField(type = DataType.CLOB)
    private String encCertValue;

    /**
     * 证书请求值
     */
    @TableField(value = "CERT_REQ")
    @DataFullField
    @KlDbField(type = DataType.CLOB)
    private String certReq;

    /**
     * 注册信息
     */
    @TableField(value = "REGISTER_INFO")
    @DataFullField
    @KlDbField(type = DataType.CLOB)
    private String registerInfo;

    /**
     * 证书级别（说明证书是根CA证书或下级CA证书）
     */
    @TableField(value = "CERT_LEVEL")
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "0")
    private Integer certLevel;

    /**
     * 证书类型，0：管理证书，1：SSL证书，2：身份证书
     */
    @TableField(value = "CERT_TYPE")
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "0")
    private Integer certType;

    /**
     * 完整性值
     */
    @TableField(value = "FULL_DATA_HASH", fill = FieldFill.INSERT_UPDATE)
    @KlDbField(type = DataType.VARCHAR, size = 1024)
    private String fullDataHash;

    /**
     * 是否已删除（值: 0. 未删除, 1. 已删除）
     */
    @TableField(value = "IS_DELETE")
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "0")
    private Boolean isDelete;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @KlDbField(type = DataType.DATE, notNull = true, defaultValue = "CURRENT_TIMESTAMP")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @KlDbField(type = DataType.DATE)
    private LocalDateTime updateTime;

    /**
     * 扩展项1
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String ext1;

    /**
     * 扩展项2
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String ext2;

    /**
     * 扩展项3
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String ext3;

    public String getIssuerCn() {
        return issuerCn;
    }

    public TTrustCertDO setIssuerCn(String issuerCn) {
        this.issuerCn = issuerCn;
        return this;
    }

    public String getCertReq() {
        return certReq;
    }

    public void setCertReq(String certReq) {
        this.certReq = certReq;
    }

    public String getIssuerKeyId() {
        return issuerKeyId;
    }

    public TTrustCertDO setIssuerKeyId(String issuerKeyId) {
        this.issuerKeyId = issuerKeyId;
        return this;
    }

    public String getHexSn() {
        return hexSn;
    }

    public void setHexSn(String hexSn) {
        this.hexSn = hexSn;
    }

    public String getKeyId() {
        return keyId;
    }

    public TTrustCertDO setKeyId(String keyId) {
        this.keyId = keyId;
        return this;
    }

    public String getSubjectCn() {
        return subjectCn;
    }

    public TTrustCertDO setSubjectCn(String subjectCn) {
        this.subjectCn = subjectCn;
        return this;
    }

    public String getSubjectDn() {
        return subjectDn;
    }

    public TTrustCertDO setSubjectDn(String subjectDn) {
        this.subjectDn = subjectDn;
        return this;
    }

    public LocalDateTime getValidStart() {
        return validStart;
    }

    public void setValidStart(LocalDateTime validStart) {
        this.validStart = validStart;
    }

    public LocalDateTime getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(LocalDateTime validEnd) {
        this.validEnd = validEnd;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCertValue() {
        return certValue;
    }

    public TTrustCertDO setCertValue(String certValue) {
        this.certValue = certValue;
        return this;
    }

    public String getEncCertValue() {
        return encCertValue;
    }

    public void setEncCertValue(String encCertValue) {
        this.encCertValue = encCertValue;
    }

    public Integer getCertLevel() {
        return certLevel;
    }

    public TTrustCertDO setCertLevel(Integer certLevel) {
        this.certLevel = certLevel;
        return this;
    }

    public Integer getCertType() {
        return certType;
    }

    public void setCertType(Integer certType) {
        this.certType = certType;
    }

    public Boolean getIsDelete() {
        return isDelete;
    }

    public TTrustCertDO setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    public String getFullDataHash() {
        return fullDataHash;
    }

    public void setFullDataHash(String fullDataHash) {
        this.fullDataHash = fullDataHash;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public void setDelete(Boolean delete) {
        isDelete = delete;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    public String getRegisterInfo() {
        return registerInfo;
    }

    public void setRegisterInfo(String registerInfo) {
        this.registerInfo = registerInfo;
    }

    @Override
    public String toString() {
        return "TTrustCertDO{" +
            "id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", hexSn='" + hexSn + '\'' +
            ", subjectCn='" + subjectCn + '\'' +
            ", issuerCn='" + issuerCn + '\'' +
            ", subjectDn='" + subjectDn + '\'' +
            ", issuerKeyId='" + issuerKeyId + '\'' +
            ", keyId='" + keyId + '\'' +
            ", validStart=" + validStart +
            ", validEnd=" + validEnd +
            ", certValue='" + certValue + '\'' +
            ", encCertValue='" + encCertValue + '\'' +
            ", certReq='" + certReq + '\'' +
            ", registerInfo='" + registerInfo + '\'' +
            ", certLevel=" + certLevel +
            ", certType=" + certType +
            ", fullDataHash='" + fullDataHash + '\'' +
            ", isDelete=" + isDelete +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", ext1='" + ext1 + '\'' +
            ", ext2='" + ext2 + '\'' +
            ", ext3='" + ext3 + '\'' +
            '}';
    }
}
