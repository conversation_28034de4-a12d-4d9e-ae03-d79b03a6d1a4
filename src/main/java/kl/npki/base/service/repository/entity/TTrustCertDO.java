package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.tools.dbtool.constant.DataType;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/9/7
 * @desc
 */
@TableName("t_trust_cert")
@KlDbTable(tbName = "t_trust_cert")
public class TTrustCertDO extends IntegrityProtectedDO {

    private static final long serialVersionUID = 6917881311957736935L;

    /**
     * 证书序列号
     */
    @TableField(value = "HEX_SN")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String hexSn;

    /**
     * 使用者CN
     */
    @TableField(value = "SUBJECT_CN")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String subjectCn;

    /**
     * 发行者CN
     */
    @TableField(value = "ISSUER_CN")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String issuerCn;

    /**
     * 证书DN
     * （长度限制需大于页面的省市等长度限制总和）
     */
    @TableField(value = "SUBJECT_DN")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 600)
    private String subjectDn;

    /**
     * 发行者密钥标识符
     */
    @TableField(value = "ISSUER_KEY_ID")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String issuerKeyId;

    /**
     * MUL 使用者密钥标识符
     */
    @TableField(value = "KEY_ID")
    @DataFullField
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String keyId;

    /**
     * 生效时间
     */
    @TableField(value = "VALID_START")
    @KlDbField(type = DataType.DATE, size = 6)
    private LocalDateTime validStart;

    /**
     * 失效时间
     */
    @TableField(value = "VALID_END")
    @KlDbField(type = DataType.DATE, size = 6)
    private LocalDateTime validEnd;

    /**
     * 证书
     */
    @TableField(value = "CERT_VALUE")
    @DataFullField
    @KlDbField(type = DataType.CLOB)
    private String certValue;

    /**
     * 加密证书
     */
    @TableField(value = "ENC_CERT_VALUE")
    @DataFullField
    @KlDbField(type = DataType.CLOB)
    private String encCertValue;

    /**
     * 证书请求值
     */
    @TableField(value = "CERT_REQ")
    @DataFullField
    @KlDbField(type = DataType.CLOB)
    private String certReq;

    /**
     * 注册信息
     */
    @TableField(value = "REGISTER_INFO")
    @DataFullField
    @KlDbField(type = DataType.CLOB)
    private String registerInfo;

    /**
     * 证书级别（说明证书是根CA证书或下级CA证书）
     */
    @TableField(value = "CERT_LEVEL")
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "0")
    private Integer certLevel;

    /**
     * 证书类型，0：管理证书，1：SSL证书，2：身份证书
     */
    @TableField(value = "CERT_TYPE")
    @DataFullField
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "0")
    private Integer certType;

    /**
     * 扩展项1
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String ext1;

    /**
     * 扩展项2
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String ext2;

    /**
     * 扩展项3
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String ext3;

    public String getIssuerCn() {
        return issuerCn;
    }

    public TTrustCertDO setIssuerCn(String issuerCn) {
        this.issuerCn = issuerCn;
        return this;
    }

    public String getCertReq() {
        return certReq;
    }

    public void setCertReq(String certReq) {
        this.certReq = certReq;
    }

    public String getIssuerKeyId() {
        return issuerKeyId;
    }

    public TTrustCertDO setIssuerKeyId(String issuerKeyId) {
        this.issuerKeyId = issuerKeyId;
        return this;
    }

    public String getHexSn() {
        return hexSn;
    }

    public void setHexSn(String hexSn) {
        this.hexSn = hexSn;
    }

    public String getKeyId() {
        return keyId;
    }

    public TTrustCertDO setKeyId(String keyId) {
        this.keyId = keyId;
        return this;
    }

    public String getSubjectCn() {
        return subjectCn;
    }

    public TTrustCertDO setSubjectCn(String subjectCn) {
        this.subjectCn = subjectCn;
        return this;
    }

    public String getSubjectDn() {
        return subjectDn;
    }

    public TTrustCertDO setSubjectDn(String subjectDn) {
        this.subjectDn = subjectDn;
        return this;
    }

    public LocalDateTime getValidStart() {
        return validStart;
    }

    public void setValidStart(LocalDateTime validStart) {
        this.validStart = validStart;
    }

    public LocalDateTime getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(LocalDateTime validEnd) {
        this.validEnd = validEnd;
    }

    public String getCertValue() {
        return certValue;
    }

    public TTrustCertDO setCertValue(String certValue) {
        this.certValue = certValue;
        return this;
    }

    public String getEncCertValue() {
        return encCertValue;
    }

    public void setEncCertValue(String encCertValue) {
        this.encCertValue = encCertValue;
    }

    public Integer getCertLevel() {
        return certLevel;
    }

    public TTrustCertDO setCertLevel(Integer certLevel) {
        this.certLevel = certLevel;
        return this;
    }

    public Integer getCertType() {
        return certType;
    }

    public void setCertType(Integer certType) {
        this.certType = certType;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    public String getRegisterInfo() {
        return registerInfo;
    }

    public void setRegisterInfo(String registerInfo) {
        this.registerInfo = registerInfo;
    }

    @Override
    public String toString() {
        return "TTrustCertDO{" +
            "hexSn='" + hexSn + '\'' +
            ", subjectCn='" + subjectCn + '\'' +
            ", issuerCn='" + issuerCn + '\'' +
            ", subjectDn='" + subjectDn + '\'' +
            ", issuerKeyId='" + issuerKeyId + '\'' +
            ", keyId='" + keyId + '\'' +
            ", validStart=" + validStart +
            ", validEnd=" + validEnd +
            ", certValue='" + certValue + '\'' +
            ", encCertValue='" + encCertValue + '\'' +
            ", certReq='" + certReq + '\'' +
            ", registerInfo='" + registerInfo + '\'' +
            ", certLevel=" + certLevel +
            ", certType=" + certType +
            ", ext1='" + ext1 + '\'' +
            ", ext2='" + ext2 + '\'' +
            ", ext3='" + ext3 + '\'' +
            ", fullDataHash='" + fullDataHash + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
