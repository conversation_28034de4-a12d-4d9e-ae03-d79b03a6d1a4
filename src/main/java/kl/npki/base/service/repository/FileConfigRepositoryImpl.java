package kl.npki.base.service.repository;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.nbase.helper.utils.ResourceUtils;
import kl.npki.base.core.biz.config.model.FileConfigEntity;
import kl.npki.base.core.repository.IFileConfigRepository;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.exception.BaseServiceInternalError;
import kl.npki.base.service.repository.entity.TFileConfigDO;
import kl.npki.base.service.repository.mapper.FileConfigWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static kl.npki.base.core.constant.BaseConstant.WEB_ROOT;

/**
 * <AUTHOR>
 * @since 2023/12/21
 */
@Repository
public class FileConfigRepositoryImpl extends KlServiceImpl<FileConfigWrapper, TFileConfigDO> implements IFileConfigRepository {
    /**
     * logger
     **/
    private static final Logger logger = LoggerFactory.getLogger(FileConfigRepositoryImpl.class);
    private final ConvertService convertService;

    public FileConfigRepositoryImpl(ConvertService convertService) {
        this.convertService = convertService;
    }

    private TFileConfigDO getFileConfigInDb(TFileConfigDO tFileConfigDO) {
        LambdaQueryWrapper<TFileConfigDO> queryWrapper = Wrappers.lambdaQuery(TFileConfigDO.class)
            .eq(TFileConfigDO::getFileName, tFileConfigDO.getFileName())
            .eq(TFileConfigDO::getFilePath, tFileConfigDO.getFilePath())
            .eq(TFileConfigDO::getIsDelete, 0);
        return getOne(queryWrapper, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAndRefreshFile(FileConfigEntity fileConfigEntity) {
        TFileConfigDO tFileConfigDO = convertService.convert(fileConfigEntity, TFileConfigDO.class);
        TFileConfigDO fileConfigInDb = getFileConfigInDb(tFileConfigDO);
        boolean fileExists = Objects.nonNull(fileConfigInDb);
        if (fileExists) {
            // 如果文件存在，更新文件内容
            tFileConfigDO.setId(fileConfigInDb.getId());
            tFileConfigDO.setUpdateBy(Long.parseLong(RequestContextHolder.getLoginUserId()));
            tFileConfigDO.setUpdateTime(LocalDateTime.now());
        } else {
            // 如果文件不存在，新增文件
            tFileConfigDO.setCreateTime(LocalDateTime.now());
        }
        boolean success = saveOrUpdate(tFileConfigDO);
        if (success) {
            try {
                String filePath = fileConfigEntity.getFilePath();
                if (!new File(filePath).isAbsolute()) {
                    // 系统中的所有相对路径必须以WEB-INF目录为基准进行解析
                    filePath = WEB_ROOT+ File.separator + filePath;
                }
                ResourceUtils.writeToFile(Base64Util.base64Decode(fileConfigEntity.getFileContentBase64()), filePath);
            } catch (Exception e) {
                logger.error("write file to {} failed!", fileConfigEntity.getFilePath(), e);
            }
        } else {
            throw BaseServiceInternalError.FILE_CONFIG_ENTITY_SAVE_FAILED.toException();
        }
    }

    @Override
    public List<FileConfigEntity> getFileConfigList() {
        List<TFileConfigDO> fileConfigList =
            list(Wrappers.lambdaQuery(TFileConfigDO.class).eq(TFileConfigDO::getIsDelete, 0));
        return convertService.convert(fileConfigList, FileConfigEntity.class);
    }

}
