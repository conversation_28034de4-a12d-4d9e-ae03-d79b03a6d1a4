package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.tools.dbtool.constant.DataType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: guoq
 * @Date: 2023/12/28
 * @description: 分布式定时任务，用于自动建表
 */
@TableName("T_SCHEDULED_TASK_LOCK")
@KlDbTable(tbName = "T_SCHEDULED_TASK_LOCK")
public class TaskLockDO implements Serializable {

    private static final long serialVersionUID = 2858309322478040567L;
    @TableId(type = IdType.INPUT)
    @KlDbField(type = DataType.VARCHAR, size = 128, isPrimary = true)
    private String taskId;

    @KlDbField(type = DataType.VARCHAR, size = 128)
    private String taskName;

    @KlDbField(type = DataType.VARCHAR, size = 128)
    private String serviceId;

    @KlDbField(type = DataType.INTEGER, size = 1, defaultValue = "0")
    private int version;

    /**
     * 过期时间
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private LocalDateTime expiredTime;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public LocalDateTime getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(LocalDateTime expiredTime) {
        this.expiredTime = expiredTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
