package kl.npki.base.service.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.npki.base.core.biz.cert.model.TrustCertEntity;
import kl.npki.base.core.constant.TrustCertLevel;
import kl.npki.base.core.constant.TrustCertType;
import kl.npki.base.core.repository.ITrustCertRepository;
import kl.npki.base.service.repository.entity.TTrustCertDO;
import kl.npki.base.service.repository.mapper.TTrustCertMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/10 11:24
 * @desc 系统管理员证书仓库接口实现
 */
@Service
public class TrustCertRepositoryImpl extends KlServiceImpl<TTrustCertMapper, TTrustCertDO> implements ITrustCertRepository {

    @Resource
    private ConvertService convertService;

    @Override
    public List<TrustCertEntity> getAllSuperCert() {
        return getAllSuperCert(TrustCertType.MANAGE);
    }

    @Override
    public List<TrustCertEntity> getAllSuperCert(TrustCertType certType) {
        LambdaQueryWrapper<TTrustCertDO> queryWrapper = Wrappers
            .lambdaQuery(TTrustCertDO.class)
            .eq(TTrustCertDO::getCertType, certType.getCode())
            .eq(TTrustCertDO::getCertLevel, TrustCertLevel.SUPER.getCode());
        List<TTrustCertDO> trustCertDOList = list(queryWrapper);

        return trustCertDOList
            .stream()
            .map(v -> convertService.convert(v, TrustCertEntity.class))
            .collect(Collectors.toList());
    }


    @Override
    public void batchSaveTrustCert(Collection<TrustCertEntity> entityList) {
        List<TTrustCertDO> certDOList = entityList
            .stream()
            .map(v -> convertService.convert(v, TTrustCertDO.class))
            .collect(Collectors.toList());
            super.saveBatch(certDOList);
    }

    @Override
    public TrustCertEntity getManageCert() {
        return getCert(TrustCertType.MANAGE);
    }

    @Override
    public TrustCertEntity getCert(TrustCertType certType) {
        LambdaQueryWrapper<TTrustCertDO> queryWrapper = Wrappers
            .lambdaQuery(TTrustCertDO.class)
            .eq(TTrustCertDO::getCertType, certType.getCode())
            .eq(TTrustCertDO::getCertLevel, TrustCertLevel.LOCAL.getCode());

        TTrustCertDO trustCertDO = getOne(queryWrapper);
        if (ObjectUtils.isEmpty(trustCertDO)) {
            return null;
        }
        return convertService.convert(trustCertDO, TrustCertEntity.class);
    }

    @Override
    public Long save(TrustCertEntity trustCertEntity) {
        TTrustCertDO adminTrustCertDO = convertService.convert(trustCertEntity, TTrustCertDO.class);
        this.save(adminTrustCertDO);
        return adminTrustCertDO.getId();
    }

    @Override
    public boolean deleteTrustCert(Long id) {
        LambdaQueryWrapper<TTrustCertDO> queryWrapper = Wrappers
            .lambdaQuery(TTrustCertDO.class)
            .eq(TTrustCertDO::getId, id)
            .eq(TTrustCertDO::getCertLevel, TrustCertLevel.SUPER.getCode());
        return this.baseMapper.delete(queryWrapper) > 0;
    }

    @Override
    public boolean delete(Long id) {
        return this.baseMapper.deleteById(id) > 0;
    }

    @Override
    public TrustCertEntity getCert(Long id) {
        TTrustCertDO trustCertDO = this.baseMapper.selectById(id);
        if (ObjectUtils.isEmpty(trustCertDO)) {
            return null;
        }
        return convertService.convert(trustCertDO, TrustCertEntity.class);
    }

    @Override
    public TrustCertEntity getCert(String hexSn) {
        LambdaQueryWrapper<TTrustCertDO> queryWrapper = Wrappers
                .lambdaQuery(TTrustCertDO.class)
                .eq(TTrustCertDO::getHexSn, hexSn);
        TTrustCertDO trustCertDO = getOne(queryWrapper);
        if (ObjectUtils.isEmpty(trustCertDO)) {
            return null;
        }
        return convertService.convert(trustCertDO, TrustCertEntity.class);
    }

    @Override
    public List<TrustCertEntity> getAll() {
        List<TTrustCertDO> trustCertDOList = lambdaQuery().list();
        if (trustCertDOList.isEmpty()) {
            return Collections.emptyList();
        }
        return convertService.convert(trustCertDOList, TrustCertEntity.class);
    }

    @Override
    public List<TrustCertEntity> getAll(TrustCertType certType) {
        LambdaQueryWrapper<TTrustCertDO> queryWrapper = Wrappers
            .lambdaQuery(TTrustCertDO.class)
            .eq(TTrustCertDO::getCertType, certType.getCode());

        List<TTrustCertDO> trustCertDOList = list(queryWrapper);

        return trustCertDOList
            .stream()
            .map(v -> convertService.convert(v, TrustCertEntity.class))
            .collect(Collectors.toList());
    }

    @Override
    public List<TrustCertEntity> queryCertByLevel(TrustCertLevel certLevel) {
        return queryCertByLevelAndType(certLevel, TrustCertType.MANAGE);
    }

    @Override
    public List<TrustCertEntity> queryCertByLevelAndType(TrustCertLevel certLevel, TrustCertType certType) {
        LambdaQueryWrapper<TTrustCertDO> queryWrapper = Wrappers
            .lambdaQuery(TTrustCertDO.class)
            .eq(TTrustCertDO::getCertType, certType.getCode())
            .eq(TTrustCertDO::getCertLevel, certLevel.getCode());

        List<TTrustCertDO> trustCertDOList = list(queryWrapper);

        return trustCertDOList
            .stream()
            .map(v -> convertService.convert(v, TrustCertEntity.class))
            .collect(Collectors.toList());
    }


    @Override
    public boolean checkCertExist(String hexSn) {
        LambdaQueryWrapper<TTrustCertDO> queryWrapper = Wrappers
            .lambdaQuery(TTrustCertDO.class)
            .eq(TTrustCertDO::getHexSn, hexSn);

        long count = count(queryWrapper);
        return count > 0;
    }

    @Override
    public boolean checkCertExist(String hexSn, String issuerCn) {
        LambdaQueryWrapper<TTrustCertDO> queryWrapper = Wrappers
                .lambdaQuery(TTrustCertDO.class)
                .eq(TTrustCertDO::getHexSn, hexSn)
                .eq(TTrustCertDO::getIssuerCn, issuerCn);
        long count = count(queryWrapper);
        return count > 0;
    }

    @Override
    public void saveOrUpdateManageCertReq(String dn, String b64CertReq, String registerInfo) {
        TTrustCertDO tTrustCertDO = new TTrustCertDO();
        tTrustCertDO.setCertLevel(TrustCertLevel.LOCAL.getCode());
        tTrustCertDO.setCertType(TrustCertType.MANAGE.getCode());
        tTrustCertDO.setCertReq(b64CertReq);
        tTrustCertDO.setRegisterInfo(registerInfo);
        tTrustCertDO.setSubjectDn(dn);

        LambdaUpdateWrapper<TTrustCertDO> updateWrapper = Wrappers
            .lambdaUpdate(TTrustCertDO.class)
            .eq(TTrustCertDO::getCertType, TrustCertType.MANAGE.getCode())
            .eq(TTrustCertDO::getCertLevel, TrustCertLevel.LOCAL.getCode())
            .isNotNull(TTrustCertDO::getCertReq);
        // 尝试先更新
        boolean update = update(tTrustCertDO, updateWrapper);
        if (!update) {
            // 新增
            save(tTrustCertDO);
        }
    }

    @Override
    public void updateById(TrustCertEntity trustCertEntity) {
        TTrustCertDO trustCertDO = convertService.convert(trustCertEntity, TTrustCertDO.class);
        updateById(trustCertDO);
    }
}
