package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.tools.dbtool.constant.DataType;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@TableName("t_main_key")
@KlDbTable(tbName = "t_main_key")
public class MainKeyDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主密钥算法
     */
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String keyAlgo;

    /**
     * 密码机公钥id
     */
    @KlDbField(type = DataType.VARCHAR, size = 1024)
    private String pubKeyId;

    /**
     * 密钥值
     */
    @KlDbField(type = DataType.VARCHAR, size = 1024)
    private String keyValue;

    /**
     * base64 加密iv向量
     */
    @KlDbField(type = DataType.VARCHAR, size = 1024)
    private String iv;

    /**
     * 加密私钥与对称密钥拼接
     */
    @KlDbField(type = DataType.CLOB)
    private String refValue;

    /**
     * 密码机id
     */
    @KlDbField(type = DataType.BIGINT, size = 20)
    private Long engineId;

    /**
     * 当前在用
     */
    @KlDbField(type = DataType.TINYINT, size = 1)
    private Boolean beUsing;

    /**
     * 主密钥状态
     */
    @KlDbField(type = DataType.TINYINT, size = 1)
    private Integer keyStatus;

    /**
     * 扩展项1
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String ext1;

    /**
     * 扩展项2
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String ext2;

    /**
     * 扩展项3
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String ext3;

    public String getKeyAlgo() {
        return keyAlgo;
    }

    public void setKeyAlgo(String keyAlgo) {
        this.keyAlgo = keyAlgo;
    }

    public String getPubKeyId() {
        return pubKeyId;
    }

    public void setPubKeyId(String pubKeyId) {
        this.pubKeyId = pubKeyId;
    }

    public String getKeyValue() {
        return keyValue;
    }

    public void setKeyValue(String keyValue) {
        this.keyValue = keyValue;
    }

    public String getIv() {
        return iv;
    }

    public void setIv(String iv) {
        this.iv = iv;
    }

    public String getRefValue() {
        return refValue;
    }

    public void setRefValue(String refValue) {
        this.refValue = refValue;
    }

    public Long getEngineId() {
        return engineId;
    }

    public void setEngineId(Long engineId) {
        this.engineId = engineId;
    }

    public Boolean getBeUsing() {
        return beUsing;
    }

    public void setBeUsing(Boolean beUsing) {
        this.beUsing = beUsing;
    }

    public Integer getKeyStatus() {
        return keyStatus;
    }

    public void setKeyStatus(Integer keyStatus) {
        this.keyStatus = keyStatus;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    @Override
    public String toString() {
        return "MainKeyDO{" +
            "keyAlgo='" + keyAlgo + '\'' +
            ", pubKeyId='" + pubKeyId + '\'' +
            ", keyValue='" + keyValue + '\'' +
            ", iv='" + iv + '\'' +
            ", refValue='" + refValue + '\'' +
            ", engineId=" + engineId +
            ", beUsing=" + beUsing +
            ", keyStatus=" + keyStatus +
            ", ext1='" + ext1 + '\'' +
            ", ext2='" + ext2 + '\'' +
            ", ext3='" + ext3 + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
