package kl.npki.base.service.repository.mapper;

import kl.nbase.db.support.mybatis.mapper.KlBaseMapper;
import kl.npki.base.service.repository.entity.TLicenseCountDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> by niugang on 2025-05-29 16:56
 */
public interface LicenseCountMapper extends KlBaseMapper<TLicenseCountDO> {

    /**
     * 乐观锁更新 CUS_VALUE 字段，若相加后小于 0 则置为 0。
     * 调用方需处理更新失败（影响行数为 0）的情况，可能表示版本冲突。
     *
     * @param id       id
     * @param cusValue cusValue
     * @param version  version
     * @param updateTime 更新时间
     * @return int
     */
    int updateWithOptimisticLock(@Param("id") Long id, @Param("cusValue") Long cusValue,
                                 @Param("version") Long version, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 批量更新数据
     *
     * @param updateList updateList
     */
    void batchUpdate(@Param("list") List<TLicenseCountDO> updateList);
}
