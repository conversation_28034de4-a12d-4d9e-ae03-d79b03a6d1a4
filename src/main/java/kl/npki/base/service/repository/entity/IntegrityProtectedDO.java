package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import kl.cloud.sql.annotation.KlDbField;
import kl.tools.dbtool.constant.DataType;

/**
 * 支持完整性保护的数据对象类，继承自BaseDO
 * 包含完整性保护相关的字段和注解
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 * @date 2025/7/10
 */
public abstract class IntegrityProtectedDO extends BaseDO {

    /**
     * 完整性值
     */
    @TableField(value = "FULL_DATA_HASH", fill = FieldFill.INSERT_UPDATE)
    @KlDbField(type = DataType.VARCHAR, size = 1024)
    protected String fullDataHash;

    public String getFullDataHash() {
        return fullDataHash;
    }

    public void setFullDataHash(String fullDataHash) {
        this.fullDataHash = fullDataHash;
    }
}
