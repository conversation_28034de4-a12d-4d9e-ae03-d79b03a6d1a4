package kl.npki.base.service.repository.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import kl.nbase.db.support.mybatis.service.IKlService;
import kl.npki.base.core.biz.log.model.ApiLogCountServiceResult;
import kl.npki.base.service.common.query.CursorPageInfo;
import kl.npki.base.service.repository.entity.TApiLogDO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/9
 */
public interface IApiLogRepositoryService extends IKlService<TApiLogDO> {

    /**
     * 保存服务日志
     *
     * @param apiLogDO
     * @return
     */
    void insertApiLogEntity(TApiLogDO apiLogDO);

    /**
     * 服务日志列表展示
     * @param queryWrapper
     * @return
     */
    IPage<TApiLogDO> searchApiLogByPage(IPage<TApiLogDO> page, QueryWrapper<TApiLogDO> queryWrapper);

    /**
     * 服务日志列表展示
     * @param pageInfo
     * @param queryWrapper
     * @return
     */
    IPage<TApiLogDO> searchApiLogByCursorPage(CursorPageInfo pageInfo, QueryWrapper<TApiLogDO> queryWrapper);

    /**
     * 服务日志详情展示
     * @param id
     * @return
     */
    TApiLogDO searchApiLogById(Long id, LocalDateTime logWhen);

    /**
     * 以请求者名称，请求结果，业务名称分组进行统计
     * @param callerNames 请求者名称集合
     * @param startTime 业务统计起始时间
     * @param endTime 业务统计截至时间
     * @return
     */
    List<ApiLogCountServiceResult> countGroupByCallerAndResultAndBiz(List<String> callerNames, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 以请求结果，业务名称分组进行统计
     * @param bizNames 业务名称集合
     * @param startTime 业务统计起始时间
     * @param endTime 业务统计截至时间
     * @return
     */
    List<ApiLogCountServiceResult> countGroupByResultAndBiz(List<String> bizNames, LocalDateTime startTime, LocalDateTime endTime);

}
