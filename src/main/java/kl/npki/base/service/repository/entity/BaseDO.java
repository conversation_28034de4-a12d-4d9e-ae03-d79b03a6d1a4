package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import kl.cloud.sql.annotation.KlDbField;
import kl.npki.base.core.annotation.DataFullField;
import kl.tools.dbtool.constant.DataType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 基础数据对象，包含通用字段
 *
 * <AUTHOR> href="mailto:<EMAIL>">dingqi</a>
 * @since 2025/06/18 14:23
 */
public abstract class BaseDO implements Serializable {

    /**
     * 主键ID
     */
    @KlDbField(type = DataType.BIGINT, size = 20, isPrimary = true, remarks = "主键id")
    @TableId(type = IdType.ASSIGN_ID, value = "ID")
    protected Long id;

    /**
     * 租户ID
     */
    @DataFullField
    @TableField(value = "tenant_id")
    @KlDbField(type = DataType.VARCHAR, size = 64)
    protected String tenantId;

    /**
     * 创建时间
     */
    @DataFullField
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @KlDbField(type = DataType.DATE, defaultValue = "CURRENT_TIMESTAMP", size = 6)
    protected LocalDateTime createTime;

    /**
     * 修改时间
     */
    @KlDbField(type = DataType.DATE, size = 6)
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    protected LocalDateTime updateTime;

    /**
     * 删除状态0：正常，1：已删除
     */
    @KlDbField(type = DataType.TINYINT, size = 1, notNull = true, defaultValue = "0", remarks = "删除状态0：正常，1：已删除")
    protected Integer isDelete;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }
}
