package kl.npki.base.service.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.npki.base.core.biz.log.model.SystemLogFileEntity;
import kl.npki.base.core.constant.UploadFileProcessStatusEnum;
import kl.npki.base.core.repository.ISystemLogFileRepository;
import kl.npki.base.service.repository.entity.TSystemLogFileDO;
import kl.npki.base.service.repository.mapper.SystemLogFileMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 日志文件Repository
 *
 * <AUTHOR>
 * @since 2025/7/10 14:03
 */
@Component
public class SystemLogFileRepositoryImpl extends KlServiceImpl<SystemLogFileMapper, TSystemLogFileDO> implements ISystemLogFileRepository {

    @Resource
    private ConvertService convertService;

    @Resource
    private SystemLogFileMapper systemLogFileMapper;

    @Override
    public Long importFile(SystemLogFileEntity systemLogFileEntity) {
        // 待保存对象为空，则返回空
        if (systemLogFileEntity == null) {
            return null;
        }
        TSystemLogFileDO systemLogFileDo = convertEntity2DO(systemLogFileEntity);
        // 保存并返回巡检记录ID
        return save(systemLogFileDo) ? systemLogFileDo.getId() : null;
    }


    @Override
    public void deleteOverLimitRecord(Integer fileType, Integer limitCount) {
        LambdaQueryWrapper<TSystemLogFileDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(TSystemLogFileDO::getId);
        queryWrapper.eq(TSystemLogFileDO::getFileType, fileType);
        queryWrapper.orderByDesc(TSystemLogFileDO::getId);
        List<TSystemLogFileDO> systemLogFileList = systemLogFileMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(systemLogFileList)) {
            return;
        }
        List<Long> removeIds = Lists.newArrayList();
        if (systemLogFileList.size() > limitCount) {
            for (int i = limitCount; i < systemLogFileList.size(); i++) {
                removeIds.add(systemLogFileList.get(i).getId());
            }
        }
        if (CollectionUtils.isEmpty(removeIds)) {
            return;
        }
        systemLogFileMapper.deleteBatchIds(removeIds);
    }

    @Override
    public boolean update(SystemLogFileEntity systemLogFileEntity) {
        TSystemLogFileDO systemLogFileDO = convertEntity2DO(systemLogFileEntity);
        return super.saveOrUpdate(systemLogFileDO);
    }

    @Override
    public void delete(Long id) {
        super.removeById(id);
    }

    @Override
    public SystemLogFileEntity search(Long id) {
        TSystemLogFileDO systemLogFileDO = super.getById(id);
        return convertDO2Entity(systemLogFileDO);
    }

    @Override
    public SystemLogFileEntity searchStatus(Long id) {
        LambdaQueryWrapper<TSystemLogFileDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(TSystemLogFileDO::getId, TSystemLogFileDO::getFileName, TSystemLogFileDO::getProcessStatus);
        queryWrapper.eq(TSystemLogFileDO::getId, id);
        TSystemLogFileDO systemLogFileDO = super.getOne(queryWrapper);
        return convertDO2Entity(systemLogFileDO);
    }

    @Override
    public List<SystemLogFileEntity> searchSystemLogFileList(Integer fileType) {
        List<SystemLogFileEntity> systemLogFileEntityList = Lists.newArrayList();
        LambdaQueryWrapper<TSystemLogFileDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(TSystemLogFileDO::getId, TSystemLogFileDO::getFileName,
                TSystemLogFileDO::getCreateTime, TSystemLogFileDO::getProcessStatus);
        queryWrapper.eq(TSystemLogFileDO::getFileType, fileType);
        queryWrapper.eq(TSystemLogFileDO::getProcessStatus, UploadFileProcessStatusEnum.COMPLETE.getStatus());
        queryWrapper.orderByDesc(TSystemLogFileDO::getId);
        List<TSystemLogFileDO> systemLogFileDoList = super.list(queryWrapper);
        if (CollectionUtils.isEmpty(systemLogFileDoList)) {
            return systemLogFileEntityList;
        }
        return systemLogFileDoList.stream()
                .map(this::convertDO2Entity)
                .collect(Collectors.toList());

    }

    private SystemLogFileEntity convertDO2Entity(TSystemLogFileDO systemLogFileDO) {
        return null == systemLogFileDO ? null : convertService.convert(systemLogFileDO, SystemLogFileEntity.class);
    }

    private TSystemLogFileDO convertEntity2DO(SystemLogFileEntity systemLogFileEntity) {
        return null == systemLogFileEntity ? null : convertService.convert(systemLogFileEntity, TSystemLogFileDO.class);
    }
}

