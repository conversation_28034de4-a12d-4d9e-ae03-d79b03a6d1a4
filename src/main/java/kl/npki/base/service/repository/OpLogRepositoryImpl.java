package kl.npki.base.service.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.npki.base.core.biz.log.model.OpLogInfo;
import kl.npki.base.core.constant.AuditStatusEnum;
import kl.npki.base.core.repository.IOpLogRepository;
import kl.npki.base.service.repository.entity.TOpLogDO;
import kl.npki.base.service.repository.service.IOpLogRepositoryService;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/2/13 下午5:07
 */
@Repository
public class OpLogRepositoryImpl implements IOpLogRepository {
    @Resource
    private IOpLogRepositoryService service;

    @Resource
    private ConvertService convertService;

    /**
     * 存储操作日志
     *
     * @param opLogInfo
     * @return
     */
    @Override
    public void saveOpLog(OpLogInfo opLogInfo) {
        TOpLogDO tOpLogDO = convertService.convert(opLogInfo, TOpLogDO.class);
        service.save(tOpLogDO);
    }

    /**
     * 操作日志详情展示
     *
     * @param logId
     * @return
     */
    @Override
    public OpLogInfo searchOpLogById(Long logId) {
        TOpLogDO tOpLogDO = service.getById(logId);
        if (tOpLogDO != null) {
            // 数据完整性校验
            verifyDataIntegrity(tOpLogDO);
        }
        return convertService.convert(tOpLogDO, OpLogInfo.class);
    }

    /**
     * 修改验签结果
     *
     * @param logId
     * @param auditStatus
     */
    @Override
    public void modifyAuditStatus(Long logId, AuditStatusEnum auditStatus) {
        TOpLogDO tOpLogDO = service.getById(logId);
        if (tOpLogDO != null) {
            tOpLogDO.setAuditStatus(auditStatus.getCode());
            tOpLogDO.setAuditTime(LocalDateTime.now());
            service.updateById(tOpLogDO);
        }
    }

    @Override
    public List<OpLogInfo> page(PageInfo pageInfo, Object o) {
        return service.page(pageInfo.toPage(), (QueryWrapper<TOpLogDO>) o)
                .convert(v -> convertService.convert(v, OpLogInfo.class))
                .getRecords();
    }
}