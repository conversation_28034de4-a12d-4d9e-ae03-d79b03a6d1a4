package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.biz.check.model.SelfCheckResult;
import kl.tools.dbtool.constant.DataType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统巡检记录表
 *
 * <AUTHOR>
 * @date 07/05/2025 20:44
 **/
@TableName("T_INSPECTION_RECORD")
@KlDbTable(tbName = "T_INSPECTION_RECORD")
public class InspectionRecordDO implements Serializable {

    /**
     * 巡检项目ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @KlDbField(type = DataType.BIGINT, size = 20, isPrimary = true, remarks = "巡检项目ID")
    private Long id;

    /**
     * 本次巡检名称
     */
    @KlDbField(type = DataType.VARCHAR, notNull = true, size = 128, remarks = "本次巡检名称")
    private String name;

    /**
     * 本次巡检耗时
     */
    @KlDbField(type = DataType.BIGINT, remarks = "本次巡检耗时")
    private Long costMills;

    /**
     * 本次巡检执行开始时间
     */
    @KlDbField(type = DataType.DATE, remarks = "本次巡检执行开始时间")
    private LocalDateTime startTime;

    /**
     * 本次巡检执行结束时间
     */
    @KlDbField(type = DataType.DATE, remarks = "本次巡检执行结束时间")
    private LocalDateTime endTime;

    /**
     * 巡检项结果，一个{@link SelfCheckResult}集合的JSON字符串
     */
    @KlDbField(type = DataType.CLOB, remarks = "巡检项结果")
    private String itemResults;

    /**
     * 租户ID
     */
    @KlDbField(type = DataType.VARCHAR, size = 64, remarks = "租户ID")
    private String tenantId;

    /**
     * 逻辑删除标志，1：已删除，0：未删除
     */
    @KlDbField(type = DataType.INTEGER, size = 1, notNull = true, defaultValue = "0", remarks = "删除状态。0：正常，1：已删除")
    private Boolean isDelete;

    /**
     * 创建者
     */
    @KlDbField(type = DataType.BIGINT, remarks = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @KlDbField(type = DataType.DATE, remarks = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @KlDbField(type = DataType.BIGINT, remarks = "更新者")
    @TableField(fill = FieldFill.UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @KlDbField(type = DataType.DATE, remarks = "更新时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCostMills() {
        return costMills;
    }

    public void setCostMills(Long costMills) {
        this.costMills = costMills;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getItemResults() {
        return itemResults;
    }

    public void setItemResults(String itemResults) {
        this.itemResults = itemResults;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Boolean getDelete() {
        return isDelete;
    }

    public void setDelete(Boolean delete) {
        isDelete = delete;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
