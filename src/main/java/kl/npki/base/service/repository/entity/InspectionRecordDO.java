package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.biz.check.model.SelfCheckResult;
import kl.tools.dbtool.constant.DataType;

import java.time.LocalDateTime;

/**
 * 系统巡检记录表
 *
 * <AUTHOR>
 * @date 07/05/2025 20:44
 **/
@TableName("T_INSPECTION_RECORD")
@KlDbTable(tbName = "T_INSPECTION_RECORD")
public class InspectionRecordDO extends BaseDO {

    /**
     * 本次巡检名称
     */
    @KlDbField(type = DataType.VARCHAR, notNull = true, size = 128, remarks = "本次巡检名称")
    private String name;

    /**
     * 本次巡检耗时
     */
    @KlDbField(type = DataType.BIGINT, remarks = "本次巡检耗时")
    private Long costMills;

    /**
     * 本次巡检执行开始时间
     */
    @KlDbField(type = DataType.DATE, remarks = "本次巡检执行开始时间", size = 6)
    private LocalDateTime startTime;

    /**
     * 本次巡检执行结束时间
     */
    @KlDbField(type = DataType.DATE, remarks = "本次巡检执行结束时间", size = 6)
    private LocalDateTime endTime;

    /**
     * 巡检项结果，一个{@link SelfCheckResult}集合的JSON字符串
     */
    @KlDbField(type = DataType.CLOB, remarks = "巡检项结果")
    private String itemResults;

    /**
     * 创建者
     */
    @KlDbField(type = DataType.BIGINT, remarks = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 更新者
     */
    @KlDbField(type = DataType.BIGINT, remarks = "更新者")
    @TableField(fill = FieldFill.UPDATE)
    private Long updateBy;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCostMills() {
        return costMills;
    }

    public void setCostMills(Long costMills) {
        this.costMills = costMills;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getItemResults() {
        return itemResults;
    }

    public void setItemResults(String itemResults) {
        this.itemResults = itemResults;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }
}
