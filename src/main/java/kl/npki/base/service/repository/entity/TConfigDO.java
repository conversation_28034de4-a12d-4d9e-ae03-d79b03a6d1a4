package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.tools.dbtool.constant.DataType;


/**
 * 用于自动生成配置表建表语句
 * <AUTHOR>
 * @since 2024/3/8
 */
@TableName("t_config")
@KlDbTable(tbName = "t_config")
public class TConfigDO extends BaseDO {
    private static final long serialVersionUID = 1L;

    /**
     * 配置文件名称
     */
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String configName;

    /**
     * 配置文件内容
     */
    @KlDbField(type = DataType.CLOB)
    private String configValue;

    /**
     * 配置md5值
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String hash;

    /**
     * 站点名称
     */
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String siteName;

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    @Override
    public String toString() {
        return "TConfigDO{" +
            "configName='" + configName + '\'' +
            ", configValue='" + configValue + '\'' +
            ", hash='" + hash + '\'' +
            ", siteName='" + siteName + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
