package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.tools.dbtool.constant.DataType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用于自动生成配置表建表语句
 * <AUTHOR>
 * @since 2024/3/8
 */
@TableName("T_CONFIG")
@KlDbTable(tbName = "T_CONFIG")
public class TConfigDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @KlDbField(type = DataType.BIGINT, size = 20, isPrimary = true)
    private Long id;

    /**
     * 配置文件名称
     */
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String configName;

    /**
     * 配置文件内容
     */
    @KlDbField(type = DataType.CLOB)
    private String configValue;

    /**
     * 配置md5值
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String hash;

    /**
     * 站点名称
     */
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String siteName;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @KlDbField(type = DataType.DATE, defaultValue = "CURRENT_TIMESTAMP")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @KlDbField(type = DataType.DATE)
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "TConfigDO{" +
            "id=" + id +
            ", configName='" + configName + '\'' +
            ", configValue='" + configValue + '\'' +
            ", hash='" + hash + '\'' +
            ", siteName='" + siteName + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            '}';
    }
}
