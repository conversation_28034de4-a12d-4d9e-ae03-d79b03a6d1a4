package kl.npki.base.service.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import kl.nbase.bean.convert.ConvertService;
import kl.nbase.db.support.mybatis.query.PageInfo;
import kl.nbase.db.support.mybatis.service.KlServiceImpl;
import kl.npki.base.core.biz.org.model.OrgEntity;
import kl.npki.base.core.constant.EntityStatus;
import kl.npki.base.core.repository.IOrgRepository;
import kl.npki.base.service.repository.entity.OrgDO;
import kl.npki.base.service.repository.mapper.OrgMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/3/19 18:13
 */
@Repository
public class OrgRepositoryImpl extends KlServiceImpl<OrgMapper, OrgDO> implements IOrgRepository {

    @Resource
    private ConvertService convertService;

    /**
     * 根据机构ID查询机构实体
     *
     * @param id 机构id
     * @return 机构实体对象
     */
    @Override
    public OrgEntity searchById(Long id) {
        OrgDO orgDO = this.getById(id);
        return convert2Entity(orgDO);
    }

    /**
     * 根据机构名称查询机构实体
     *
     * @param orgName 机构名称
     * @return 机构实体对象
     */
    @Override
    public OrgEntity searchByOrgName(String orgName) {
        LambdaQueryWrapper<OrgDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OrgDO::getOrgName, orgName);
        OrgDO orgDO = getOne(queryWrapper);
        return convert2Entity(orgDO);
    }

    /**
     * 根据机构编码查询机构实体
     *
     * @param orgCode 机构编码
     * @return 机构实体对象
     */
    @Override
    public OrgEntity searchByOrgCode(String orgCode) {
        LambdaQueryWrapper<OrgDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OrgDO::getOrgCode, orgCode);
        OrgDO orgDO = getOne(queryWrapper);
        return convert2Entity(orgDO);
    }

    /**
     * 获取所有的子、孙等机构
     *
     * @param fullId   完整机构id
     * @param pageInfo 分页条件
     * @return
     */
    @Override
    public List<OrgEntity> searchAllSubOrgList(String fullId, PageInfo pageInfo) {
        LambdaQueryWrapper<OrgDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.likeRight(OrgDO::getFullId, fullId);
        queryWrapper.orderByAsc(OrgDO::getId);

        IPage<OrgDO> page = this.page(pageInfo.toPage(), queryWrapper);
        List<OrgDO> records = page.getRecords();
        return convert2EntityList(records);
    }

    /**
     * 根据机构名称查询当前机构的所有子机构实体信息
     *
     * @param parentId 父机构id
     * @param pageInfo 分页信息
     * @return 机构实体对象
     */
    @Override
    public List<OrgEntity> searchSubOrgEntityById(Long parentId, PageInfo pageInfo) {
        LambdaQueryWrapper<OrgDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OrgDO::getParentId, parentId);
        queryWrapper.orderByAsc(OrgDO::getFullCode);

        IPage<OrgDO> page = this.page(pageInfo.toPage(), queryWrapper);
        List<OrgDO> records = page.getRecords();
        return convert2EntityList(records);
    }

    /**
     * 根据机构名称查询当前机构的所有子机构实体信息
     *
     * @param parentOrgName 父机构名称
     * @param pageInfo      分页信息
     * @return 机构实体对象
     */
    @Override
    public List<OrgEntity> searchSubOrgEntityByOrgName(String parentOrgName, PageInfo pageInfo) {
        // 先查询一下当前机构的id，此处不会对性能造成大的影响，因为MyBatis内部默认启用了一级缓存
        OrgEntity orgEntity = this.searchByOrgName(parentOrgName);
        return this.searchSubOrgEntityById(orgEntity.getId(), pageInfo);
    }

    /**
     * 根据机构名称查询当前机构的所有子机构实体信息
     *
     * @param parentOrgCode 父机构编码
     * @param pageInfo      分页信息
     * @return 机构实体对象
     */
    @Override
    public List<OrgEntity> searchSubOrgEntityByOrgCode(String parentOrgCode, PageInfo pageInfo) {
        LambdaQueryWrapper<OrgDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OrgDO::getParentCode, parentOrgCode);
        queryWrapper.orderByAsc(OrgDO::getFullCode);

        IPage<OrgDO> page = this.page(pageInfo.toPage(), queryWrapper);
        List<OrgDO> records = page.getRecords();
        return convert2EntityList(records);
    }

    /**
     * 根据更新时间查询机构实体集合
     *
     * @param updatedAt 更新时间
     * @param pageInfo  分页条件
     * @return 满足时间和分页条件的机构集合
     */
    @Override
    public List<OrgEntity> searchSubOrgListByUpdateAt(LocalDateTime updatedAt, PageInfo pageInfo) {
        LambdaQueryWrapper<OrgDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ge(OrgDO::getUpdateTime, updatedAt);
        queryWrapper.orderByAsc(OrgDO::getFullCode);

        IPage<OrgDO> page = this.page(pageInfo.toPage(), queryWrapper);
        return convert2EntityList(page.getRecords());
    }

    /**
     * 获取所有机构信息
     *
     * @param pageInfo 分页信息
     * @return 机构实体对象
     */
    @Override
    public List<OrgEntity> searchAll(PageInfo pageInfo) {
        LambdaQueryWrapper<OrgDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.orderByAsc(OrgDO::getFullCode);

        IPage<OrgDO> page = this.page(pageInfo.toPage(), queryWrapper);
        return convert2EntityList(page.getRecords());
    }

    /**
     * 获取所有机构信息
     *
     * @return
     */
    @Override
    public List<OrgEntity> searchAll() {
        List<OrgDO> orgDOList = super.list();
        return convert2EntityList(orgDOList);
    }

    /**
     * 添加机构实体
     *
     * @param orgEntity 机构实体信息
     */
    @Override
    public Long save(OrgEntity orgEntity) {
        OrgDO orgDO = convertService.convert(orgEntity, OrgDO.class);
        super.save(orgDO);

        return orgDO.getId();
    }

    /**
     * 添加或更新机构实体，内部需要做一次查询，如果存在则更新，不存在则添加，查询可以考虑使用缓存实现
     *
     * @param orgEntity 机构实体集合
     */
    @Override
    public boolean saveOrUpdate(OrgEntity orgEntity) {
        OrgDO orgDO = convertService.convert(orgEntity, OrgDO.class);
        return super.saveOrUpdate(orgDO);
    }

    /**
     * 更新机构实体
     *
     * @param orgEntity 机构实体信息
     */
    @Override
    public boolean update(OrgEntity orgEntity) {
        OrgDO orgDO = convertService.convert(orgEntity, OrgDO.class);
        return super.updateById(orgDO);
    }

    /**
     * 注销机构实体
     *
     * @param id 机构id
     */
    @Override
    public boolean revoke(Long id) {
        LambdaUpdateWrapper<OrgDO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(OrgDO::getId, id);
        updateWrapper.set(OrgDO::getOrgStatus, EntityStatus.REVOKED.getId());
        return update(updateWrapper);
    }

    /**
     * 逻辑删除机构实体
     *
     * @param id 机构id
     */
    @Override
    public boolean delete(Long id) {
        return super.removeById(id);
    }

    /**
     * 批量逻辑删除机构实体
     *
     * @param ids
     * @return
     */
    @Override
    public boolean batchDelete(List<Long> ids) {
        LambdaUpdateWrapper<OrgDO> deleteWrapper = Wrappers.lambdaUpdate();
        deleteWrapper.in(OrgDO::getId, ids);
        return remove(deleteWrapper);
    }

    private OrgEntity convert2Entity(OrgDO orgDO) {
        if (orgDO == null) {
            return null;
        }
        return convertService.convert(orgDO, OrgEntity.class);
    }

    private List<OrgEntity> convert2EntityList(List<OrgDO> orgDOList) {
        if (CollectionUtils.isEmpty(orgDOList)) {
            return Collections.emptyList();
        }
        return convertService.convert(orgDOList, OrgEntity.class);
    }
}