package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.npki.base.core.annotation.DataFullField;
import kl.tools.dbtool.constant.DataType;

/**
 * <AUTHOR>
 * @since 2023/12/21
 */
@TableName("t_file_config")
@KlDbTable(tbName = "t_file_config")
public class TFileConfigDO extends IntegrityProtectedDO {
    private static final long serialVersionUID = 1134554636171397388L;

    /**
     * 文件名
     */
    @TableField(value = "FILE_NAME")
    @KlDbField(type = DataType.VARCHAR, size = 64)
    private String fileName;

    /**
     * 文件路径
     */
    @TableField(value = "FILE_PATH")
    @KlDbField(type = DataType.VARCHAR, size = 255)
    @DataFullField
    private String filePath;

    /**
     * 文件内容
     */
    @TableField(value = "FILE_CONTENT_BASE64")
    @KlDbField(type = DataType.CLOB)
    @DataFullField
    private String fileContentBase64;

    /**
     * 更新者id
     */
    @TableField(value = "UPDATE_BY", fill = FieldFill.INSERT_UPDATE)
    @KlDbField(type = DataType.BIGINT, size = 20)
    private Long updateBy;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileContentBase64() {
        return fileContentBase64;
    }

    public void setFileContentBase64(String fileContentBase64) {
        this.fileContentBase64 = fileContentBase64;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    @Override
    public String toString() {
        return "TFileConfigDO{" +
            "fileName='" + fileName + '\'' +
            ", filePath='" + filePath + '\'' +
            ", fileContentBase64='" + fileContentBase64 + '\'' +
            ", updateBy=" + updateBy +
            ", fullDataHash='" + fullDataHash + '\'' +
            ", id=" + id +
            ", tenantId='" + tenantId + '\'' +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", isDelete=" + isDelete +
            '}';
    }
}
