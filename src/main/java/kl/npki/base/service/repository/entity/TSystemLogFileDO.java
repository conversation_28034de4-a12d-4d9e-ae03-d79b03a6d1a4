package kl.npki.base.service.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbField;
import kl.cloud.sql.annotation.KlDbTable;
import kl.tools.dbtool.constant.DataType;

import java.time.LocalDateTime;

/**
 * 日志文件实体
 *
 * <AUTHOR>
 * @since 2025/7/10 14:03
 */
@TableName("T_SYSTEM_LOG_FILE")
@KlDbTable(tbName = "T_SYSTEM_LOG_FILE", indexes = {@Index(name = "IDX_FILE_TYPE", columnList = "FILE_TYPE")})
public class TSystemLogFileDO {

    /**
     * 系统日志文件标识ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @KlDbField(type = DataType.BIGINT, size = 20, isPrimary = true, notNull = true)
    private Long id;

    /**
     * 文件类型
     */
    @KlDbField(type = DataType.INTEGER, notNull = true)
    private Integer fileType;

    /**
     * 文件名称
     */
    @KlDbField(type = DataType.VARCHAR, size = 128)
    private String fileName;

    /**
     * 上传的文件
     */
    @KlDbField(type = DataType.BLOB)
    private String fileContent;

    /**
     * 日志文件签名标识
     */
    @KlDbField(type = DataType.INTEGER, size = 1)
    private Boolean logSignFlag;

    /**
     * Excel文件中的数据处理状态
     * <li>-1 尚未开始处理</li>
     * <li>0 正在处理</li>
     * <li>1 处理完成</li>
     */
    @KlDbField(type = DataType.INTEGER, size = 1, defaultValue = "0")
    private Integer processStatus;

    /**
     * 租户id
     */
    @KlDbField(type = DataType.VARCHAR, size = 255)
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @KlDbField(type = DataType.DATE, notNull = true)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @KlDbField(type = DataType.DATE, notNull = true)
    private LocalDateTime updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getFileType() {
        return fileType;
    }

    public void setFileType(Integer fileType) {
        this.fileType = fileType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileContent() {
        return fileContent;
    }

    public void setFileContent(String fileContent) {
        this.fileContent = fileContent;
    }

    public Integer getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(Integer processStatus) {
        this.processStatus = processStatus;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getLogSignFlag() {
        return logSignFlag;
    }

    public void setLogSignFlag(Boolean logSignFlag) {
        this.logSignFlag = logSignFlag;
    }
}