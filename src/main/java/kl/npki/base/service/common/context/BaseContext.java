package kl.npki.base.service.common.context;

import kl.nbase.helper.utils.LocalDateTimeUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: guoq
 * @Date: 2023/12/18
 * @description: 上下文基础属性
 */
public abstract class BaseContext implements Serializable {
    private static final long serialVersionUID = 9192760500967953574L;
    /**
     * 请求IP
     */
    protected String ip;

    /**
     * 前端请求开始时间戳，主要用于签名验签
     */
    protected long start;

    /**
     * 后端请求处理请求开始时间，用于操作日志记录
     */
    protected Long backendStart;

    /**
     * 请求结束时间戳
     */
    protected long end;

    /**
     * 耗时
     */
    protected long elapsedTime;

    /**
     * 租户ID
     */
    protected String tenantId;

    /**
     * 业务用户id
     */
    protected String uid;


    /**
     * 业务证书id
     */
    protected String certId;

    /**
     * 调用者id
     */
    private String callerId;

    /**
     * 调用者名称
     */
    private String callerName;

    /**
     * 业务id
     */
    protected String bizId;

    /**
     * 客户端IP端口
     */
    protected String clientIpPort;

    /**
     * 业务名称
     */
    private String biz;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 链路追踪id
     */
    protected String traceId;

    /**
     * 跨度id
     */
    protected String spanId;

    /**
     * 扩展项1
     */
    protected String ext1;

    /**
     * 扩展项2
     */
    protected String ext2;

    /**
     * 扩展项3
     */
    protected String ext3;

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public long getStart() {
        return start;
    }

    public void setStart(long start) {
        this.start = start;
    }

    public long getEnd() {
        return end;
    }

    public void setEnd(long end) {
        this.end = end;
    }

    public long getElapsedTime() {
        return elapsedTime;
    }

    public void setElapsedTime(long elapsedTime) {
        this.elapsedTime = elapsedTime;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getCertId() {
        return certId;
    }

    public void setCertId(String certId) {
        this.certId = certId;
    }

    public String getCallerId() {
        return callerId;
    }

    public void setCallerId(String callerId) {
        this.callerId = callerId;
    }

    public String getCallerName() {
        return callerName;
    }

    public void setCallerName(String callerName) {
        this.callerName = callerName;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getBiz() {
        return biz;
    }

    public void setBiz(String biz) {
        this.biz = biz;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getClientIpPort() {
        return clientIpPort;
    }

    public void setClientIpPort(String clientIpPort) {
        this.clientIpPort = clientIpPort;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getSpanId() {
        return spanId;
    }

    public void setSpanId(String spanId) {
        this.spanId = spanId;
    }

    public Long getBackendStart() {
        return backendStart;
    }

    public void setBackendStart(Long backendStart) {
        this.backendStart = backendStart;
    }

    /**
     * 获取请求时间，将时间戳转换为LocalDateTime类型
     *
     * @return
     */
    public LocalDateTime getStartDate() {
        return LocalDateTimeUtils.milliSecondParseLocalDateTime(this.start);
    }

    /**
     * 获取后端请求处理开始时间，将时间戳转换为LocalDateTime类型
     *
     * @return LocalDateTime
     */
    public LocalDateTime getBackendStartDate() {
        return LocalDateTimeUtils.milliSecondParseLocalDateTime(this.backendStart);
    }

}
