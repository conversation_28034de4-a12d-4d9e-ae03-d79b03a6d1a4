package kl.npki.base.service.common.date;

import kl.npki.base.core.utils.RegionAndLanguageUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.InstantiationAwareBeanPostProcessor;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 日期格式化加载配置, 动态赋值
 * 启动时候提高优先级,bean实例化之后
 * 防止业务类提前调用默认日志序列化，导致后续数据反序列化失败！
 * <AUTHOR>
 * @since 2025/2/21 15:47
 */
@Component("dateFormatAwareBeanPostProcessor")
public class DateFormatPostProcessor implements InstantiationAwareBeanPostProcessor {

    /**
     * 是否已经执行标识
     */
    private static final AtomicBoolean INIT_FLAG = new AtomicBoolean(Boolean.FALSE);


    @Override
    public Object postProcessAfterInitialization(@NotNull Object bean, @NotNull String beanName) throws BeansException {
        if (INIT_FLAG.get()) {
            return bean;
        }
        // 初始化日期格式化
        RegionAndLanguageUtil.setTimeZone();
        RegionAndLanguageUtil.setDateFormat();

        INIT_FLAG.set(Boolean.TRUE);
        return bean;
    }

}

