package kl.npki.base.service.common.license;

import kl.npki.base.core.biz.license.service.LicenseValidator;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.service.common.context.RequestContextHolder;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

/**
 * 用于检查系统许可的切面
 * <p>
 * 当存在 LicenseValidator 的实现时，该切面在 @CheckLicense 注解的方法前执行许可检查
 * 若无实现，则跳过检查
 *
 * <AUTHOR>
 * @date 2023/8/24
 */
@Aspect
@Component
@Conditional(LicenseValidatorCondition.class)
public class LicenseValidatorAspect {

    /**
     * 当且仅当接入KCSP时，NKM TCP业务接口允许跳过License检查的方法签名（这里采用硬编码会增加代码维护成本，但可以减少代码量，该接口定义设计基本不会改变，所以采用硬编码）
     */
    public static final String KCSP_NKM_ALLOWED_TCP_SERVICE_METHOD_SIGNATURE = "public kl.npki.base.core.common.service.MsgResult kl.npki.km.core.service.key.impl.KeyServiceImpl.processReq(byte[])";

    private final LicenseValidator licenseValidator;

    public LicenseValidatorAspect(LicenseValidator licenseValidator) {
        this.licenseValidator = licenseValidator;
    }

    @Before("@annotation(kl.npki.base.core.annotation.CheckLicense)")
    public void checkLicense(JoinPoint joinPoint) {
        if (!shouldAllow(joinPoint)) {
            licenseValidator.check();
        }
    }

    /**
     * 判断是否允许跳过License检查
     *
     * @return true表示允许跳过License检查
     */
    private boolean shouldAllow(JoinPoint joinPoint) {
        // 对于HTTP业务接口来说，当且仅当接入KCSP且为KCSP请求时，才允许跳过License检查
        // 对于TCP业务接口来说，无法确定是否为KCSP请求，目前通过方法签名判断
        return SystemUtil.isAccessKcsp() &&
            (RequestContextHolder.getContext().isKcspRequest() ||
                KCSP_NKM_ALLOWED_TCP_SERVICE_METHOD_SIGNATURE.equals(joinPoint.getSignature().toLongString()));
    }
}
