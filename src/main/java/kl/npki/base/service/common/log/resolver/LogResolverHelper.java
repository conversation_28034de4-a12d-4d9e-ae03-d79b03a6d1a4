package kl.npki.base.service.common.log.resolver;

import io.swagger.v3.oas.annotations.Operation;
import kl.nbase.i18n.i18n.I18nUtil;
import kl.nbase.i18n.locale.LocaleContext;
import kl.nbase.netty.server.http.mock.MockHttpServletRequest;
import kl.nbase.security.entity.algo.HashAlgo;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Enumeration;
import java.util.Map;
import java.util.Objects;

/**
 * 日志解析帮助类
 *
 * <AUTHOR>
 * @date 2023/3/15
 */
public class LogResolverHelper {

    private static final Logger log = LoggerFactory.getLogger(LogResolverHelper.class);

    /**
     * StringBuilder默认初始容量为16，append过程中内部数组大小调整可能会过度使用堆内存空间（来自Fortify报告），因此此处指定一个预期的容量
     */
    private static final int STRING_BUILDER_INIT_CAPACITY = 1024;

    private LogResolverHelper() {
    }

    /**
     * 切面结合 swagger 获取对应的操作名称以及业务名称
     *
     * @param joinPoint
     * @return
     * @throws NoSuchMethodException
     */
    public static String getBizName(ProceedingJoinPoint joinPoint) {
        Class<?> targetClass = joinPoint.getTarget().getClass();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method;
        String bizName;
        try {
            method = targetClass.getDeclaredMethod(signature.getName(), signature.getParameterTypes());
            Operation annotation = method.getAnnotation(Operation.class);
            bizName = annotation.description();
        } catch (Exception e) {
            bizName = joinPoint.getSignature().getName();
        }
        // 获取系统默认语言配置进行国际化
        return I18nUtil.tr(bizName, LocaleContext.getDefaultLocal());
    }

    /**
     * 获取请求参数
     *
     * @param request
     * @return
     */
    public static String getParameterMapAll(HttpServletRequest request) {
        StringBuilder builder = new StringBuilder(STRING_BUILDER_INIT_CAPACITY);
        // 跟前端保持一致: 文件上传只对body进行签名验签计算, param参数为空
        if (request instanceof MultipartHttpServletRequest) {
            return builder.toString();
        }
        Enumeration<String> parameters = request.getParameterNames();
        // param参数结构 key=value&key=value
        boolean first = true;
        while (parameters.hasMoreElements()) {
            String parameter = parameters.nextElement();
            String value = request.getParameter(parameter);
            if (!first) {
                builder.append("&");
            }
            builder.append(parameter)
                .append("=")
                .append(value);
            first = false;
        }
        return builder.toString();
    }

    /**
     * 获取请求体
     *
     * @param request 请求对象
     * @return String
     */
    public static String getBodyString(HttpServletRequest request) {
        if (request instanceof MockHttpServletRequest) {
            try {
                byte[] contentAsByteArray = ((MockHttpServletRequest) request).getContentAsByteArray();
                if (contentAsByteArray == null) {
                    return StringUtils.EMPTY;
                }
                String charset = StringUtils.defaultString(request.getCharacterEncoding(), StandardCharsets.UTF_8.toString());
                return new String(contentAsByteArray, charset);
            } catch (UnsupportedEncodingException e) {
                log.error("Failed to retrieve the request body", e);
            }
        }
        // 文件上传: 对文件sha256后再签名验签，中间件最大支持4k
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;
            // 获取上传的文件
            Map<String, MultipartFile> fileMap = multipartHttpServletRequest.getFileMap();
            for (Map.Entry<String, MultipartFile> entry : fileMap.entrySet()) {
                MultipartFile file = entry.getValue();
                if (Objects.nonNull(file) && !file.isEmpty()) {
                    try {
                        return calculateFileHash(file);
                    } catch (Exception e) {
                        log.error("Failed to calculateFileHash ", e);
                    }
                }
            }
        }
        StringBuilder builder = new StringBuilder(STRING_BUILDER_INIT_CAPACITY);
        try {
            // 这里使用的servlet的流，不需要显式关闭，servlet会进行自动关闭
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            char[] bodyCharBuffer = new char[1024];
            int len;
            while ((len = reader.read(bodyCharBuffer)) != -1) {
                builder.append(new String(bodyCharBuffer, 0, len));
            }
        } catch (Exception e) {
            log.error("Failed to retrieve the request body", e);
        }
        return builder.toString();
    }

    /**
     * 计算文件摘要值
     *
     * @param file 文件对象
     * @return sha256摘要值
     * @throws IOException              IOException
     * @throws NoSuchAlgorithmException NoSuchAlgorithmException
     */
    public static String calculateFileHash(MultipartFile file) throws IOException, NoSuchAlgorithmException {
        if (Objects.isNull(file) || file.isEmpty()) {
            return StringUtils.EMPTY;
        }
        // 复制流以避免消耗原始流
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try (InputStream inputStream = file.getInputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }
        }

        // 对输入流进行sha256摘要计算
        try (InputStream hashStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray())) {
            MessageDigest digest = MessageDigest.getInstance(HashAlgo.SHA256.getAlgoName());

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = hashStream.read(buffer)) != -1) {
                digest.update(buffer, 0, bytesRead);
            }

            byte[] hashBytes = digest.digest();
            // 摘要结果转成16进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        }
    }

}