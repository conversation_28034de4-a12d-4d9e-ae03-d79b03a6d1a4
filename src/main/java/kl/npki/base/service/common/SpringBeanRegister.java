package kl.npki.base.service.common;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/12/19 14:58
 * @Description: 提供手工注册bean功能
 */
@Component
public class SpringBeanRegister implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    private static DefaultListableBeanFactory defaultListableBeanFactory;

    /**
     * 手动注册bean
     *
     * @param beanName  注册容器中使用的beanName
     * @param beanClazz 注册的class
     * @param args      构造器参数，没有可以为空
     */
    public static void registerBean(String beanName, Class<?> beanClazz, Object... args) {

        // 通过BeanDefinitionBuilder创建bean定义
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(beanClazz);
        if (args != null && args.length > 0) {
            for (Object arg : args) {
                builder.addConstructorArgValue(arg);
            }
        }

        builder.setAutowireMode(AbstractBeanDefinition.AUTOWIRE_BY_TYPE);
        builder.setRole(BeanDefinition.ROLE_APPLICATION);
        // 尝试移除之前相同的bean
        if (defaultListableBeanFactory.containsBean(beanName)) {
            defaultListableBeanFactory.removeBeanDefinition(beanName);
        }
        // 注册bean
        defaultListableBeanFactory
            .registerBeanDefinition(beanName, builder.getRawBeanDefinition());
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringBeanRegister.applicationContext = applicationContext;
        //将applicationContext转换为ConfigurableApplicationContext
        ConfigurableApplicationContext configurableApplicationContext = (ConfigurableApplicationContext) applicationContext;
        // 获取bean工厂并转换为DefaultListableBeanFactory
        SpringBeanRegister.defaultListableBeanFactory = (DefaultListableBeanFactory) configurableApplicationContext.getBeanFactory();

    }

    public static <T> T getBean(Class<T> tClass) {
        return applicationContext.getBean(tClass);
    }

    public static Object getBean(String name) {
        return applicationContext.getBean(name);
    }

}
