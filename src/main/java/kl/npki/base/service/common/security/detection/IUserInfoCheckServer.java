package kl.npki.base.service.common.security.detection;

import kl.npki.base.service.common.context.RequestContext;

/**
 * 用户信息校验接口
 * <p>
 * 由于架构分层设计限制，<code>npki-base-service</code> 模块无法直接访问
 * <code>npki-base-management-core</code> 模块中的管理员信息，因此通过 SPI 机制，
 * 由 <code>npki-base-management-core</code> 提供该接口的实现。用于校验用户身份的合法性，防止请求中的用户信息被伪造
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/2 上午10:20
 */

public interface IUserInfoCheckServer {

    /**
     * 校验用户id和用户名的合法性，正常情况下用户id和用户名都是合法的，只有在被攻击的时候用户id或用户名是非法值
     * <p>
     * TODO 后续可支持在攻击检测后触发响应策略，如推送安全告警、执行 IP 封禁、限制账号权限等操作。
     * </p>
     *
     * @param userId   用户 ID {@link RequestContext#getUserId()}
     * @param userName 用户名 {@link RequestContext#getUsername()}
     * @throws kl.nbase.exception.ValidationException 如果用户 ID 或用户名不合法
     */
    void checkUserInfo(Long userId, String userName);

}