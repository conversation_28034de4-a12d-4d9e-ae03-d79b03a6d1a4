package kl.npki.base.service.common.consul;

import com.ecwid.consul.v1.agent.model.NewService;
import kl.nbase.netty.conf.NettyHttpServerConfig;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.utils.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties;
import org.springframework.cloud.consul.discovery.ConsulDiscoveryProperties;
import org.springframework.cloud.consul.discovery.HeartbeatProperties;
import org.springframework.cloud.consul.serviceregistry.ConsulAutoRegistration;
import org.springframework.cloud.consul.serviceregistry.ConsulServiceRegistry;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.Collections;
import java.util.Map;

import static kl.npki.base.core.constant.KcspConstant.*;

/**
 * Consul 业务服务端口注册
 *
 * <AUTHOR>
 * @date 2024/4/30
 */
@Component
@ConditionalOnProperty(value = "spring.cloud.consul.discovery.enabled", havingValue = "true")
public class ConsulNettyServiceRegistrar implements ApplicationListener<ApplicationReadyEvent>, DisposableBean {

    private final Logger log = LoggerFactory.getLogger(ConsulNettyServiceRegistrar.class);

    private final ConsulServiceRegistry consulServiceRegistry;
    private final AutoServiceRegistrationProperties properties;
    private final ConsulDiscoveryProperties consulProperties;
    private final ApplicationContext applicationContext;
    private final HeartbeatProperties heartbeatProperties;
    private final Environment environment;
    private final SecureRandom random = new SecureRandom();

    private ConsulAutoRegistration registration;


    public ConsulNettyServiceRegistrar(ConsulServiceRegistry consulServiceRegistry,
                                       AutoServiceRegistrationProperties properties,
                                       ConsulDiscoveryProperties consulProperties,
                                       ApplicationContext applicationContext,
                                       HeartbeatProperties heartbeatProperties, Environment environment) {
        this.consulServiceRegistry = consulServiceRegistry;
        this.properties = properties;
        this.consulProperties = consulProperties;
        this.applicationContext = applicationContext;
        this.heartbeatProperties = heartbeatProperties;
        this.environment = environment;
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        NettyHttpServerConfig nettyHttpServerConfig = ConfigHolder.get().get(NettyHttpServerConfig.class);
        if (!nettyHttpServerConfig.isEnabled()) {
            return;
        }
        registration = nettyHttpServiceRegistration(nettyHttpServerConfig,
            properties, consulProperties, applicationContext, heartbeatProperties);
        consulServiceRegistry.register(registration);
    }

    @Override
    public void destroy() throws Exception {
        if (registration != null) {
            try {
                consulServiceRegistry.deregister(registration);
            } catch (Exception e) {
                log.error("Failed to deregister {} service: ", registration.getServiceId(), e);
            }
        }
    }


    public ConsulAutoRegistration nettyHttpServiceRegistration(
        NettyHttpServerConfig nettyHttpServerConfig, AutoServiceRegistrationProperties autoServiceRegistrationProperties, ConsulDiscoveryProperties properties,
        ApplicationContext context,
        HeartbeatProperties heartbeatProperties) {
        NewService service = new NewService();
        String appName = ConsulAutoRegistration.getAppName(properties, context.getEnvironment());
        if (appName.endsWith(KCSP_NPKI_MANAGE_SUFFIX)) {
            appName = appName.substring(0, appName.length() - KCSP_NPKI_MANAGE_SUFFIX.length()) + ConsulAutoRegistration.SEPARATOR + KCSP_NPKI_SERVICE_SUFFIX;
        } else {
            appName = appName + ConsulAutoRegistration.SEPARATOR + KCSP_NPKI_SERVICE_SUFFIX;
        }
        String ipAddress = environment.getProperty("spring.cloud.client.ip-address");
        byte[] bytes = new byte[32];
        random.nextBytes(bytes);
        String serviceInstanceId = appName + BaseConstant.COLON + ipAddress + BaseConstant.COLON + nettyHttpServerConfig.getPort() + BaseConstant.COLON + DigestUtils.digestSha256ToHexStr(bytes);
        service.setId(ConsulAutoRegistration.normalizeForDns(serviceInstanceId));
        service.setAddress(properties.getHostname());

        service.setName(ConsulAutoRegistration.normalizeForDns(appName));
        service.setPort(nettyHttpServerConfig.getPort());
        service.setTags(Collections.singletonList(KCSP_NPKI_SERVICE_SUFFIX));
        service.setEnableTagOverride(properties.getManagementEnableTagOverride());
        Map<String, String> metadata = properties.getMetadata();
        metadata.putIfAbsent(KL_NPKI_SERVICE_TYPE, DEFAULT_SERVICE_NAME);
        // 功能类型,0: 计算,1: 管理,2: 计算—+管理
        metadata.put(KL_NPKI_FUNCTION_TYPE, String.valueOf(0));
        service.setMeta(metadata);
        if (nettyHttpServerConfig.isSslEnabled()) {
            metadata.put("secure", "true");
        } else {
            metadata.put("secure", "false");
        }
        if (properties.isRegisterHealthCheck()) {
            service.setCheck(ConsulAutoRegistration.createCheck(nettyHttpServerConfig.getPort(), heartbeatProperties, properties));
        }
        return new ConsulAutoRegistration(service, autoServiceRegistrationProperties,
            properties, context, heartbeatProperties, null);
    }
}
