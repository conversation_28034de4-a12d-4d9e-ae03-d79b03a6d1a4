package kl.npki.base.service.common;

import kl.npki.base.core.biz.check.handler.SelfCheckItemCollector;
import kl.npki.base.core.biz.check.SelfCheckManager;
import kl.npki.base.core.biz.check.ISelfCheckItem;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 基于Spring容器的服务自检实例收集器
 *
 * <AUTHOR>
 * @date 2023/10/11
 */
@Component
public class SpringCheckItemCollector implements SelfCheckItemCollector, ApplicationContextAware, InitializingBean {

    private ApplicationContext appContext;

    @Override
    public Map<Class<?>, ISelfCheckItem> collect() {
        Map<String, ISelfCheckItem> checkItemMap = appContext.getBeansOfType(ISelfCheckItem.class);
        if (!CollectionUtils.isEmpty(checkItemMap)) {
            Map<Class<?>, ISelfCheckItem> result = new HashMap<>(checkItemMap.size());
            checkItemMap.values().forEach(r -> result.put(r.getClass(), r));
            return result;
        }
        return Collections.emptyMap();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        SelfCheckManager.getInstance().init(this);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        appContext = applicationContext;
    }
}
