package kl.npki.base.service.common.context;


/**
 * @Author: guoq
 * @Date: 2023/12/18
 * @description:
 */
public class MsgContextHolder {
    private static final ThreadLocal<MsgContext> CONTEXT = new ThreadLocal<>();

    private MsgContextHolder() {
    }

    /**
     * 获取MsgContext
     *
     * @return
     */
    public static MsgContext getContext() {
        MsgContext ctx = CONTEXT.get();
        if (ctx == null) {
            ctx = new MsgContext();
            CONTEXT.set(ctx);
        }
        return ctx;
    }

    /**
     * 移除MsgContext
     */
    public static void remove() {
        CONTEXT.remove();
    }

}
