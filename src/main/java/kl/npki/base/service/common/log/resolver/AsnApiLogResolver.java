package kl.npki.base.service.common.log.resolver;

import kl.nbase.helper.utils.JsonUtils;
import kl.nbase.i18n.i18n.I18nUtil;
import kl.nbase.log.collect.LogCollector;
import kl.nbase.log.resolver.ILogResolver;
import kl.npki.base.core.biz.log.model.ApiLogInfo;
import kl.npki.base.core.constant.LogTypeEnum;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.base.service.common.context.MsgContext;
import kl.npki.base.service.common.context.MsgContextHolder;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;

import static kl.npki.base.service.constants.I18nConstant.*;

/**
 * @Author: guoq
 * @Date: 2023/12/18
 * @description: ASN结构的api日志解析器
 */
public class AsnApiLogResolver implements ILogResolver {
    @Override
    public String getLogType() {
        return LogTypeEnum.API_LOG.getLogType();
    }

    /**
     * 解析和处理日志，正常流
     *
     * @param joinPoint
     * @param returnValue
     * @param logCollector
     * @return 要记录的日志
     */
    @Override
    public String resolve(ProceedingJoinPoint joinPoint, Object returnValue, LogCollector logCollector) {
        ApiLogInfo apiLogInfo = new ApiLogInfo();
        populateApiLog(apiLogInfo, joinPoint);

        // 填充响应
        try {
            // 响应内容
            apiLogInfo.setResponse(Base64Util.base64Encode((byte[]) returnValue));
        } catch (Exception e) {
            apiLogInfo.setResponse(I18nUtil.tr(PARSE_RESPONSE_TO_ASN_ERROR_I18N_KEY) + ":" + e.getMessage());
        }
        String apiLogJsonStr = JsonUtils.toJson(apiLogInfo);
        return JsonUtils.toJson(LogResolveInfo.buildMsgResolverResult(LogTypeEnum.API_LOG.getLogType(), apiLogJsonStr));
    }

    /**
     * 解析和处理日志，异常流
     *
     * @param joinPoint
     * @param t
     * @param logCollector
     * @return 要记录的日志
     */
    @Override
    public String exceptionResolve(ProceedingJoinPoint joinPoint, Throwable t, LogCollector logCollector) {
        ApiLogInfo exceptionApiLogInfo = new ApiLogInfo();
        populateApiLog(exceptionApiLogInfo, joinPoint);
        exceptionApiLogInfo.setResult(Boolean.FALSE);
        exceptionApiLogInfo.setDetail(t.getMessage());
        // 设置错误信息
        MsgContextHolder.getContext().setErrorMessage(t.getMessage());
        String toJson = JsonUtils.toJson(exceptionApiLogInfo);
        return JsonUtils.toJson(LogResolveInfo.buildMsgResolverResult(LogTypeEnum.API_LOG.getLogType(), toJson));
    }

    /**
     * 填充正常流和异常流的公共字段
     *
     * @param apiLogInfo
     * @param joinPoint
     */
    private void populateApiLog(ApiLogInfo apiLogInfo, ProceedingJoinPoint joinPoint) {

        // 填充请求
        try {
            // 请求内容
            Object[] requestArgs = joinPoint.getArgs();
            if (requestArgs.length == 0) {
                return;
            }
            byte[] request = null;
            // 判断那个参数是byte[]
            for (Object requestArg : requestArgs) {
                if (requestArg instanceof byte[]) {
                    request = (byte[]) requestArg;
                    break;
                }
            }
            // 请求内容不为空的时候才进行设置
            apiLogInfo.setRequest(Base64Util.base64Encode(request));
        } catch (Exception e) {
            apiLogInfo.setRequest(I18nUtil.tr(PARSE_REQUEST_TO_ASN_ERROR_I18N_KEY) + ":" + e.getMessage());
        }
        // 填充从上下文中获取的数据
        populateApiLogFormContext(apiLogInfo);
    }

    /**
     * 从请求上下文中获取对应的日志属性进行填充
     *
     * @param apiLogInfo
     */
    private void populateApiLogFormContext(ApiLogInfo apiLogInfo) {
        MsgContext context = MsgContextHolder.getContext();
        String errorMessage = context.getErrorMessage();
        if (StringUtils.isEmpty(errorMessage)) {
            apiLogInfo.setResult(Boolean.TRUE);
        } else {
            apiLogInfo.setResult(Boolean.FALSE);
            apiLogInfo.setDetail(errorMessage);
        }
        // 设置接口耗时
        long elapsedTime = System.currentTimeMillis() - context.getBackendStart();
        context.setElapsedTime(elapsedTime);
        apiLogInfo.setElapsedTime(elapsedTime);
        apiLogInfo.setLogWhen(context.getBackendStartDate());
        apiLogInfo.setBiz(context.getBiz());
        apiLogInfo.setClientIp(context.getIp());

        apiLogInfo.setCallerId(context.getCallerId());
        apiLogInfo.setCallerName(context.getCallerName());
        apiLogInfo.setCertId(context.getCertId());
        apiLogInfo.setEntityId(context.getUid());
        apiLogInfo.setExt1(context.getExt1());
        apiLogInfo.setExt2(context.getExt2());
        apiLogInfo.setExt3(context.getExt3());
        apiLogInfo.setBizId(context.getBizId());
        apiLogInfo.setTraceId(context.getTraceId());
        apiLogInfo.setSpanId(context.getSpanId());
        apiLogInfo.setTenantId(context.getTenantId());
        // 如果证书id为空，设置为空字符，避免转json字段丢失
        if (StringUtils.isBlank(apiLogInfo.getCertId())) {
            apiLogInfo.setCertId(StringUtils.EMPTY);
        }
    }
}
