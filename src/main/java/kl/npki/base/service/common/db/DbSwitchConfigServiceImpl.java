package kl.npki.base.service.common.db;

import kl.nbase.config.constant.ConfigConstantsEnum;
import kl.nbase.config.store.db.DBConfigStore;
import kl.nbase.config.utils.BeanCopyUtils;
import kl.nbase.db.core.SwitchableDataSource;
import kl.nbase.db.support.druid.DruidDataSourceProperties;
import kl.nbase.db.support.druid.HealthCheckProperties;
import kl.nbase.db.support.druid.SlotDruidDataSourceProperties;
import kl.nbase.db.support.sharding.config.ReadWriteSplittingDataSourceProperties;
import kl.nbase.db.support.sharding.config.ShardingConfig;
import kl.nbase.db.support.sharding.config.ShardingSphereAlgorithmProperties;
import kl.nbase.db.support.slot.SlotDataSourceConfig;
import kl.nbase.db.support.slot.SwitchableDataSourceBuilder;
import kl.nbase.db.support.slot.SwitchableDataSourceConfig;
import kl.nbase.helper.utils.ResourceUtils;
import kl.nbase.helper.utils.YamlUtil;
import kl.npki.base.core.biz.db.service.IDbSwitchConfigService;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.tenantholders.TenantContextHolder;
import kl.npki.base.service.common.SpringBeanRegister;
import kl.npki.base.service.exception.BaseServiceInternalError;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Paths;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/1/4 9:24
 * @Description: npki 保存数据库配置服务类
 */
@Component
public class DbSwitchConfigServiceImpl implements IDbSwitchConfigService {

    private static final Logger log = LoggerFactory.getLogger(DbSwitchConfigServiceImpl.class);

    /**
     * 保存druid配置到switchConfig
     *
     * @param dbPropertiesList 前端保存的数据库配置列表
     * @return DruidDataSource
     */
    @Override
    public DataSource saveOrUpdateDataSourceConfig(List<DruidDataSourceProperties> dbPropertiesList,
                                                   SlotDruidDataSourceProperties druidDataSourceProperties,
                                                   Boolean enableReadWrite) {
        // 1. 更新switchConfig
        SwitchableDataSourceConfig switchableDataSourceConfig = SpringBeanRegister.getBean(SwitchableDataSourceConfig.class);
        String tenantId = TenantContextHolder.getTenantId();
        SlotDataSourceConfig slotConfig = switchableDataSourceConfig.updateSlotConfig(dbPropertiesList, tenantId);
        SlotDruidDataSourceProperties druid = slotConfig.getDruid();
        druid.setMaxActive(druidDataSourceProperties.getMaxActive());
        druid.setDriverClassName(druidDataSourceProperties.getDriverClassName());
        // 去除默认的数据据库校验语句，这里根据实际数据库设置，扩展数据库适配性
        druid.setValidationQuery(druidDataSourceProperties.getValidationQuery());

        if (ObjectUtils.isEmpty(slotConfig.getSharding())) {
            ShardingConfig shardingConfig = ConfigHolder.get()
                .get(ShardingConfig.class);
            ShardingConfig shardingConfigClone = (ShardingConfig) BeanCopyUtils.deepClone(shardingConfig);
            slotConfig.setSharding(shardingConfigClone);
        }

        ShardingConfig shardingConfig = slotConfig.getSharding();

        // 初始化读写分离配置
        initReadWriteConfig(enableReadWrite, shardingConfig);

        // 初始化健康检查配置
        initHealthCheckConfig(enableReadWrite, shardingConfig);

        // 2. 保存switchConfig
        ConfigHolder.get().save(slotConfig, false);

        // 3. 无需在此次刷新数据源，配置变更后config组件会触发配置变更后事件进而触发内存中的数据源刷新

        // 4. 返回数据源
        return SpringBeanRegister.getBean(SwitchableDataSource.class);

    }

    private static void initHealthCheckConfig(boolean enableAutoSwitch, ShardingConfig shardingConfig) {
        HealthCheckProperties healthCheck = shardingConfig.getHealthCheck();
        if (Objects.isNull(healthCheck)) {
            ShardingConfig globalShardingConfig = ConfigHolder.get()
                .get(ShardingConfig.class);
            if (Objects.isNull(globalShardingConfig) || Objects.isNull(globalShardingConfig.getHealthCheck())) {
                healthCheck = new HealthCheckProperties();
                healthCheck.setEnableAutoSwitch(enableAutoSwitch);
            } else {
                healthCheck = (HealthCheckProperties) BeanCopyUtils.deepClone(globalShardingConfig.getHealthCheck());
            }
        }
        shardingConfig.setHealthCheck(healthCheck);

        healthCheck.setEnableAutoSwitch(enableAutoSwitch);
    }

    private static void initReadWriteConfig(Boolean enableReadWrite, ShardingConfig shardingConfig) {
        shardingConfig.setReadWriteEnabled(enableReadWrite);
        if (!Boolean.TRUE.equals(enableReadWrite)) {
            return;
        }

        List<ReadWriteSplittingDataSourceProperties> readWrite = shardingConfig.getReadWrite();
        if (CollectionUtils.isEmpty(readWrite)) {
            readWrite = new ArrayList<>();
            shardingConfig.setReadWrite(readWrite);
            ReadWriteSplittingDataSourceProperties readWriteSplittingDataSourceProperties = new ReadWriteSplittingDataSourceProperties();
            readWriteSplittingDataSourceProperties.initDefaultConfig();
            readWrite.add(readWriteSplittingDataSourceProperties);
        }

        Map<String, ShardingSphereAlgorithmProperties> loadBalancers = shardingConfig.getLoadBalancers();
        if (loadBalancers == null) {
            loadBalancers = new LinkedHashMap<>();
            shardingConfig.setLoadBalancers(loadBalancers);
        }
        loadBalancers.computeIfAbsent("round_robin", k -> {
            ShardingSphereAlgorithmProperties shardingSphereAlgorithmProperties = new ShardingSphereAlgorithmProperties();
            shardingSphereAlgorithmProperties.setType("ROUND_ROBIN");
            shardingSphereAlgorithmProperties.setProps(new Properties());
            return shardingSphereAlgorithmProperties;
        });
    }

    @Override
    public void refreshDatasource(SlotDataSourceConfig slotConfig) {
        if (!isValidConfig(slotConfig)) {
            // 配置无效(数据库url未配置)，跳过更新
            return;
        }
        SwitchableDataSource switchableDataSource = SpringBeanRegister.getBean(SwitchableDataSource.class);
        ShardingConfig shardingConfig = null;
        try {
            shardingConfig = SpringBeanRegister.getBean(ShardingConfig.class);
        } catch (NoSuchBeanDefinitionException e) {
            log.warn("get bean failed!,class name:[ShardingConfig]", e);
        }
        try {
            SwitchableDataSourceBuilder.refreshBySlot(switchableDataSource, slotConfig, shardingConfig);
        } catch (Exception e) {
            throw BaseServiceInternalError.DB_REFRESH_ERROR.toException(e);
        }
    }


    @Override
    public void updateDbConfigStoreIfNecessary(SlotDataSourceConfig slotConfig) {
        if (!System.getProperty(ConfigConstantsEnum.CONFIG_STORE_TYPE.getKey()).equalsIgnoreCase(DBConfigStore.STORE_TYPE)) {
            return;
        }
        // 修改bootstrap.yml中的数据源配置
        Map<String, Object> map;
        try (InputStream bootstrapInputStream = ResourceUtils.getResourceAsStream(BaseConstant.BOOTSTRAP_YML)){
            map = YamlUtil.parseYamlFile(bootstrapInputStream);
        } catch (IOException e) {
            throw BaseServiceInternalError.FILE_READ_ERROR.toException(e);
        }
        Map<String, Object> configDbMap = (Map<String, Object>) map.get("config");
        Map<String, Object> dbMap = (Map<String, Object>) configDbMap.get("db");
        DruidDataSourceProperties druidDataSourceProperties = slotConfig.getDatasource().get(0);
        if (Objects.isNull(druidDataSourceProperties)) {
            return;
        }
        // 更新bootstrap.yml中的数据源配置
        dbMap.put("url", druidDataSourceProperties.getUrl());
        dbMap.put("user", druidDataSourceProperties.getUsername());
        dbMap.put("password", druidDataSourceProperties.getPassword());
        dbMap.put("switchDataSource", System.getProperty(ConfigConstantsEnum.CONFIG_DB_SWITCH_DATA_SOURCE.getKey()));
        URL bootstrapURL = ResourceUtils.getResource(BaseConstant.BOOTSTRAP_YML);
        try (Writer writer = new BufferedWriter(new FileWriter(Paths.get(bootstrapURL.toURI()).toFile()))){
            YamlUtil.writeYamlFile(map, writer);
        } catch (IOException | URISyntaxException e) {
            throw BaseServiceInternalError.FILE_WRITE_ERROR.toException(e);
        }
    }

    /**
     * 是否为有效的数据库配置
     *
     * @return
     */
    private boolean isValidConfig(SlotDataSourceConfig slotConfig) {
        List<DruidDataSourceProperties> datasource = slotConfig.getDatasource();

        for (DruidDataSourceProperties properties : datasource) {
            // 配置中至少有一个有效
            if (StringUtils.isNotBlank(properties.getUrl())) {
                return true;
            }
        }
        return false;
    }
}
