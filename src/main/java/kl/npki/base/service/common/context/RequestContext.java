package kl.npki.base.service.common.context;

import kl.nbase.helper.utils.WebUtils;
import kl.nbase.security.asn1.x509.Certificate;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.I18nConstant;
import kl.npki.base.core.utils.URLCoder;
import kl.npki.base.service.common.SpringBeanRegister;
import kl.npki.base.service.constants.Constants;
import kl.npki.base.service.exception.BaseServiceValidationError;
import kl.npki.base.service.common.security.detection.IUserInfoCheckServer;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.http.HttpHeaders;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.UUID;

import static kl.npki.base.core.constant.I18nConstant.DATE_TIME_PATTERN_MESSAGE_I18N_KEY;
import static kl.npki.base.service.constants.I18nExceptionInfoConstant.SIGNATURE_TIMESTAMP_FORMAT_ERROR_DESC_I18N_KEY;

/**
 *
 * <AUTHOR>
 * Created on 2022/08/23 15:17
 * @description: HTTP协议请求上下文
 */
public class RequestContext extends BaseContext {

    private static final long serialVersionUID = 2372053647802654791L;
    /**
     * 请求内容
     */
    private transient HttpServletRequest request;

    /**
     * 请求URL地址
     */
    private String requestUrl;

    /**
     * 请求方法
     */
    private String requestMethod;


    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 签名值
     */
    private String signData;

    /**
     * 签名原文
     */
    private String originData;

    /**
     * 角色id
     */
    private List<Long> roleIds;

    /**
     * token令牌
     */
    private String token;

    /**
     * 是否为KCSP平台请求
     */
    private boolean isKcspRequest;

    /**
     * 用户证书
     */
    private Certificate userCert;

    /**
     * 随机数，解决前端脚本白名单绕过漏洞，由{@link kl.npki.base.service.filter.SecurityResponseFilter}生成
     */
    private String nonce;

    /**
     * 封装request中内容至RequestContext中
     */
    public void assembleContext(HttpServletRequest request) {
        setRequest(request);
        setStart(getStartTimeFromRequest(request));
        setBackendStart(System.currentTimeMillis());
        String ip = WebUtils.getIpAddr(request,
                BaseConfigWrapper.getWebConfig().getClientIpv4whitelist(),
                BaseConfigWrapper.getWebConfig().getClientIpv6whitelist());
        setIp(ip);
        setRequestUrl(WebUtils.getOriginatingRequestUri(request));
        setRequestMethod(request.getMethod());
        String tenantIdFromReq = request.getHeader(Constants.TENANT_ID);

        setTenantId(StringUtils.isEmpty(tenantIdFromReq) ?
            BaseConfigWrapper.getSysInfoConfig().getEnvironmentSwitchId() : tenantIdFromReq);

        // 处理链路追踪id，没有则自动随机生成
        String traceIdInHeader = request.getHeader(Constants.TRACE_ID);
        if (StringUtils.isEmpty(traceIdInHeader)) {
            traceIdInHeader = UUID.randomUUID().toString();
        }
        setTraceId(traceIdInHeader);
        // 处理签名原文
        setOriginData(request.getHeader(Constants.ORIGIN_DATA));
        // 处理签名值
        setSignData(request.getHeader(Constants.SIGN_DATA));
        // 处理扩展项
        setExt1(request.getHeader(Constants.EXT1));
        setExt2(request.getHeader(Constants.EXT2));
        setExt3(request.getHeader(Constants.EXT3));
        // 处理spanId
        setSpanId(request.getHeader(Constants.SPAN_ID));
        // 处理用户标识以及adminName
        String userIdFromReq = request.getHeader(Constants.USER_ID);
        if (StringUtils.isNotEmpty(userIdFromReq)) {
            setUserId(Long.valueOf(userIdFromReq));
        }
        setUsername(getUserNameFromRequest(request));
        // 校验用户ID和用户名合法性
        checkUserIdAndUsername(getUserId(), getUsername());
        // 处理终端标识以及其他标识
        setUid(request.getHeader(Constants.UID));
        setCertId(request.getHeader(Constants.CID));
        setCallerId(request.getHeader(Constants.CALLER_ID));
        setBizId(UUID.randomUUID().toString());
        String clientIpPortHeader = request.getHeader(Constants.CLIENT_IP_PORT);
        setClientIpPort(clientIpPortHeader == null ? getIp() : clientIpPortHeader);
        setToken(getTokenFromRequest(request));
        // 处理KCSP平台传输过来的信息
        determineIsKcspRequest(request);
    }

    /**
     * 校验用户ID和用户名合法性
     *
     * @param userId   用户id
     * @param username 用户名称
     */
    private static void checkUserIdAndUsername(Long userId, String username) {
        IUserInfoCheckServer infoCheckServer = SpringBeanRegister.getBean(IUserInfoCheckServer.class);
        infoCheckServer.checkUserInfo(userId, username);
    }

    private static Long getStartTimeFromRequest(HttpServletRequest request) {
        long startTime;
        try {
            // 获取请求头中的客户端时间戳
            String headerValue = request.getHeader(Constants.START);
            // 如果请求头中没有时间戳，则使用当前系统时间
            startTime = StringUtils.isNotBlank(headerValue) ? Long.parseLong(headerValue) : System.currentTimeMillis();
        } catch (NumberFormatException e) {
            throw BaseServiceValidationError.SIGNATURE_TIMESTAMP_FORMAT_ERROR.toException(e);
        }

        // 检查客户端提交的时间与服务器时间相差是否超出配置时间，如果超出则抛出异常
        long currentTime = System.currentTimeMillis();
        long maxAllowedDiffMs = BaseConfigWrapper.getWebConfig().getMaxAllowedTimeDriftMs();
        long diffMs = Math.abs(currentTime - startTime);
        if (diffMs > maxAllowedDiffMs) {
            String dateTimePattern = I18nConstant.getBaseCoreI18nMessage(DATE_TIME_PATTERN_MESSAGE_I18N_KEY);
            String formattedStartTime = DateFormatUtils.format(startTime, dateTimePattern);
            String formattedCurrentTime = DateFormatUtils.format(currentTime, dateTimePattern);
            String diffInMinutes  = String.valueOf(diffMs / (1000 * 60));

            throw BaseServiceValidationError.SIGNATURE_TIMESTAMP_CHECK_ERROR.toException(
                    SIGNATURE_TIMESTAMP_FORMAT_ERROR_DESC_I18N_KEY,
                    formattedCurrentTime,
                    formattedStartTime,
                    diffInMinutes
            );
        }

        return startTime;
    }


    private static String getTokenFromRequest(HttpServletRequest request) {
        return request.getHeader(HttpHeaders.AUTHORIZATION);
    }

    private static String getUserNameFromRequest(HttpServletRequest request) {
        String userName = request.getHeader(Constants.USERNAME);
        if (StringUtils.isNotBlank(userName)) {
            return URLCoder.decode(userName);
        }
        return userName;
    }

    public String getRequestUrl() {
        return requestUrl;
    }

    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }

    public String getRequestMethod() {
        return requestMethod;
    }

    public void setRequestMethod(String requestMethod) {
        this.requestMethod = requestMethod;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public List<Long> getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(List<Long> roleIds) {
        this.roleIds = roleIds;
    }

    public String getSignData() {
        return signData;
    }

    public void setSignData(String signData) {
        this.signData = signData;
    }

    public String getOriginData() {
        return originData;
    }

    public void setOriginData(String originData) {
        this.originData = originData;
    }

    public HttpServletRequest getRequest() {
        return request;
    }

    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public boolean isKcspRequest() {
        return isKcspRequest;
    }

    public void setKcspRequest(boolean kcspRequest) {
        isKcspRequest = kcspRequest;
    }

    public Certificate getUserCert() {
        return userCert;
    }

    public RequestContext setUserCert(Certificate userCert) {
        this.userCert = userCert;
        return this;
    }

    /**
     * 判断是否为KCSP请求
     *
     * @param request
     */
    private void determineIsKcspRequest(HttpServletRequest request) {
        String xforwardedPrefixHeader = request.getHeader(Constants.X_FORWARDED_PREFIX_HEADER);
        String mgrGatewayIdentifier = BaseConfigWrapper.getKcspConfig().getMgrGatewayIdentifier();
        String svcGatewayIdentifier = BaseConfigWrapper.getKcspConfig().getSvcGatewayIdentifier();
        if (xforwardedPrefixHeader != null && (xforwardedPrefixHeader.equals(mgrGatewayIdentifier) || xforwardedPrefixHeader.equals(svcGatewayIdentifier))) {
            setKcspRequest(true);
        } else {
            setKcspRequest(false);
        }
    }

    public String getNonce() {
        return nonce;
    }

    public RequestContext setNonce(String nonce) {
        this.nonce = nonce;
        return this;
    }
}
