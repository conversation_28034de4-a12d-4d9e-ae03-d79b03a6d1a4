package kl.npki.base.service.common.log.store;

import kl.nbase.helper.utils.JsonUtils;
import kl.nbase.helper.utils.LocalDateTimeUtils;
import kl.npki.base.core.biz.kcsp.model.IKcspLog;
import kl.npki.base.core.biz.kcsp.model.request.KcspAdminOperLogInfo;
import kl.npki.base.core.biz.kcsp.service.KcspLogUploadService;
import kl.npki.base.core.biz.log.model.OpLogInfo;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.LogTypeEnum;
import kl.npki.base.core.utils.DateUtil;
import kl.npki.base.service.common.SpringBeanRegister;
import kl.npki.base.service.common.log.resolver.LogResolveInfo;
import kl.npki.base.service.exception.BaseServiceInternalError;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import static kl.npki.base.core.constant.KcspConstant.KCSP_LOG_STORE_TYPE;

/**
 * KCSP 日志上报存储服务
 *
 * <AUTHOR>
 * @date 2024/4/29
 */
public class KcspLogStoreService extends SwitchableLogStore {

    private final KcspLogUploadService uploadService;

    /**
     * KCSP 识别的应用名
     */
    private final String kcspAppName;

    public KcspLogStoreService() {
        this.uploadService = SpringBeanRegister.getBean(KcspLogUploadService.class);
        kcspAppName = BaseConfigWrapper.getSysInfoConfig().getShortName();
    }

    @Override
    public void doSave(String msg) {
        if (StringUtils.isEmpty(msg)) {
            return;
        }
        // 解析msg
        LogResolveInfo resolverResult = JsonUtils.from(msg, LogResolveInfo.class);
        // 根据环境判断是否需要进行上报,如果此时已经开始部署，但是环境标识为demo,此时不让日志进行上报
        // 获取环境标识
        SysInfoConfig sysInfoConfig = BaseConfigWrapper.getSysInfoConfig();
        String environmentSwitchId = sysInfoConfig.getEnvironmentSwitchId();
        // 从request获取是否已经开始部署
        String tenantId = resolverResult.getTenantId();
        // 只有当 environmentSwitchId 和  tenantId 同时为 demo 或者 default 的时候才需要进行上报
        if (!tenantId.equals(environmentSwitchId)) {
            return;
        }
        // 判断属于哪种类型的日志
        String logType = resolverResult.getLogType();
        String resolverResultValue = resolverResult.getValue();
        LogTypeEnum logTypeEnum = LogTypeEnum.getLogType(logType);
        IKcspLog log = null;
        switch (logTypeEnum) {
            case OP_LOG:
                // 判断 resolverResultValue 是否为空，对于GET请求，解析器返回的是个null, 如果为空，直接return ，不在进行存储
                if (ObjectUtils.isEmpty(resolverResultValue)) {
                    return;
                }
                OpLogInfo opLogInfo = JsonUtils.from(resolverResultValue, OpLogInfo.class);
                log = convertToKcspAdminLog(opLogInfo);
                break;
            case API_LOG:
                // 服务日志暂不上报,由服务网关上报到KCSP
                break;
            default:
                throw BaseServiceInternalError.INVALID_LOG_TYPE.toException();
        }
        uploadService.upload(log);
    }

    private KcspAdminOperLogInfo convertToKcspAdminLog(OpLogInfo opLogInfo) {
        KcspAdminOperLogInfo kcspAdminOperLogInfo = new KcspAdminOperLogInfo();
        kcspAdminOperLogInfo.setServiceSystem(this.kcspAppName);
        kcspAdminOperLogInfo.setUserId(opLogInfo.getLogWho());
        kcspAdminOperLogInfo.setLogName(opLogInfo.getLogDo());
        kcspAdminOperLogInfo.setLogContent(opLogInfo.getLogWhat());
        kcspAdminOperLogInfo.setLogResult(Boolean.TRUE.equals(opLogInfo.getResult()) ? 0 : 1);
        kcspAdminOperLogInfo.setLogResultDesc(Boolean.TRUE.equals(opLogInfo.getResult()) ? "成功" : "失败");
        kcspAdminOperLogInfo.setClientIp(opLogInfo.getClientIp());
        kcspAdminOperLogInfo.setServerIp(opLogInfo.getServerIp());
        kcspAdminOperLogInfo.setBeginDate(DateUtil.formatKcspDate(LocalDateTimeUtils.parseDate(opLogInfo.getLogWhen())));
        kcspAdminOperLogInfo.setEndDate(DateUtil.formatKcspDate(LocalDateTimeUtils.parseDate(opLogInfo.getLogEnd())));
        kcspAdminOperLogInfo.setElapsedTime(opLogInfo.getElapsedTime());
        kcspAdminOperLogInfo.setLogTime(DateUtil.formatKcspDate(LocalDateTimeUtils.parseDate(opLogInfo.getLogWhen())));
        return kcspAdminOperLogInfo;
    }

    @Override
    public String getStoreType() {
        return KCSP_LOG_STORE_TYPE;
    }
}
