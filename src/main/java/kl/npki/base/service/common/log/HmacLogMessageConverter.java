package kl.npki.base.service.common.log;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.pattern.MessageConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import kl.nbase.db.core.DataSourceContext;
import kl.nbase.db.core.SwitchableDataSource;
import kl.nbase.helper.utils.LocalDateTimeUtils;
import kl.nbase.security.crypto.provider.digest.HMacProvider;
import kl.nbase.security.entity.algo.HashAlgo;
import kl.npki.base.core.biz.mainkey.MainKeyLoadFlag;
import kl.npki.base.core.biz.mainkey.model.NakedMainKeyEntity;
import kl.npki.base.core.biz.mainkey.service.MainKeyManager;
import kl.npki.base.core.utils.DateUtil;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.service.constants.Constants;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import javax.sql.DataSource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Base64;
import java.util.Map;

/**
 * Hmac日志消息转换器，这是一个自定义的MessageConverter，它将日志事件转换为特定格式的字符串，
 * 并使用SM3算法生成一个HMAC哈希值
 *
 * <AUTHOR>
 * @date 2023/7/4
 */
public class HmacLogMessageConverter extends MessageConverter {

    private static final Logger log = LoggerFactory.getLogger(HmacLogMessageConverter.class);

    private static final String CLASS_NAME = HmacLogMessageConverter.class.getName();

    LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();

    @Override
    public String convert(ILoggingEvent event) {
        String convert = super.convert(event);
        // 1. 本类报错不计算HAMC
        // 2. 如果日志级别为DEBUG或者TRACE，为保证性能则不做HMAC计算
        if (event.getLoggerName().contains(CLASS_NAME) || Level.TRACE == event.getLevel() || Level.DEBUG == event.getLevel()) {
            return convert;
        }
        if (MainKeyLoadFlag.getInstance().isReady() && SystemUtil.isDataSourceConfigured() && isExistsDataSource()) {
            try {
                Long currentMainKeyId = MainKeyManager.getInstance().getCurrentMainKeyId();
                if (ObjectUtils.isNotEmpty(currentMainKeyId)) {
                    NakedMainKeyEntity currentMainKey = MainKeyManager.getInstance().getMainKey(currentMainKeyId);
                    byte[] hmac = HMacProvider.hMac(currentMainKey.getSecretKey(),
                        buildHmacSignString(event).getBytes(), HashAlgo.SM3);
                    // 将 hmac 值添加到日志消息中
                    convert += " [hmac=" + Base64.getEncoder().encodeToString(hmac) + "]";
                }
            } catch (Exception e) {
                log.error("Calculation of log message Hmac failed due to: ", e);
            }
        }
        return convert;
    }

    private static boolean isExistsDataSource() {
        SwitchableDataSource switchableDataSource = DataSourceContext.getSwitchableDataSource();
        if (switchableDataSource != null) {
            Map<String, DataSource> dataSources = switchableDataSource.getDataSources();
            return MapUtils.isNotEmpty(dataSources) && dataSources.containsKey(DataSourceContext.getTenantId());
        }
        return false;
    }

    /**
     * 构建Hmac签名字符串
     *
     * @param event 日志事件
     * @return Hmac签名字符串
     */
    private String buildHmacSignString(ILoggingEvent event) {
        LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(event.getTimeStamp()),
            ZoneId.systemDefault());
        String format = DateUtil.formatDateByPattern(LocalDateTimeUtils.parseDate(dateTime), DateUtil.dateTimeMsPattern);
        String traceId = MDC.get(Constants.TRACE_ID) == null ? "" : MDC.get(Constants.TRACE_ID);
        String spanId = MDC.get(Constants.SPAN_ID) == null ? "" : MDC.get(Constants.SPAN_ID);
        String serverName = loggerContext.getProperty("server_name");
        return String.format("%s [%s:%s] [%s] [traceId=%s] [spanId=%s] %s %s - %s", format, serverName,
            ServerIpConvert.LOCAL_IP_SM3_DIGEST,
            event.getThreadName(), traceId, spanId, event.getLevel(), event.getLoggerName(),
            event.getFormattedMessage());
    }
}
