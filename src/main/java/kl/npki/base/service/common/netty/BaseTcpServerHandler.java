package kl.npki.base.service.common.netty;

import io.netty.channel.ChannelHandlerContext;
import kl.nbase.db.core.DataSourceContext;
import kl.nbase.helper.utils.IpValidator;
import kl.nbase.netty.server.tcp.NettyTcpServerHandler;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.service.common.context.MsgContext;
import kl.npki.base.service.common.context.MsgContextHolder;
import kl.npki.base.service.common.metrics.MeasureExecutionTime;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.util.Base64;
import java.util.UUID;

/**
 * @Author: guoq
 * @Date: 2023/12/18
 * @description: tcp服务基础类
 */
public abstract class BaseTcpServerHandler extends NettyTcpServerHandler {

    private static final Logger log = LoggerFactory.getLogger(BaseTcpServerHandler.class);

    @Override
    protected void beforeProcess(ChannelHandlerContext ctx, byte[] msg) {
        if (log.isDebugEnabled()) {
            log.debug("Request data :【{}】", Base64.getEncoder().encodeToString(msg));
        }
        // 获取客户端ip
        InetSocketAddress ipSocket = (InetSocketAddress) ctx.channel().remoteAddress();
        String clientIp = ipSocket.getAddress().getHostAddress();
        IpValidator.validateIpInWhitelist(clientIp,
                BaseConfigWrapper.getWebConfig().getClientIpv4whitelist(),
                BaseConfigWrapper.getWebConfig().getClientIpv6whitelist());
        // 构造消息上下文
        MsgContext context = MsgContextHolder.getContext();
        context.setIp(clientIp);
        long currentTime = System.currentTimeMillis();
        context.setStart(currentTime);
        context.setBackendStart(currentTime);
        context.setTenantId(BaseConfigWrapper.getSysInfoConfig().getEnvironmentSwitchId());
        context.setTraceId(UUID.randomUUID().toString());
        // 当从库有问题是，开启强制主库路由
        if (Boolean.TRUE.equals(DataSourceContext.getWriteRouteOnly())) {
            HintManager.getInstance().setWriteRouteOnly();
        }

    }

    @Override
    protected void afterProcess(ChannelHandlerContext ctx, byte[] result) {
        if (log.isDebugEnabled()) {
            log.debug("Response data: 【{}】", Base64.getEncoder().encodeToString(result));
        }
        // 指标耗时记录
        if (BaseConfigWrapper.getHealthMetricsConfig().getExecutionTime().isEnabled()) {
            MeasureExecutionTime.getInstance().measure();
        }
        MsgContextHolder.remove();
        HintManager.clear();
    }
}
