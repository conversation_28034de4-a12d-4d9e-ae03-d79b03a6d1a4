package kl.npki.base.service.common.log.resolver;

import kl.nbase.helper.utils.JsonUtils;
import kl.nbase.helper.utils.LocalDateTimeUtils;
import kl.nbase.i18n.i18n.I18nUtil;
import kl.nbase.log.collect.LogCollector;
import kl.nbase.log.resolver.ILogResolver;
import kl.nbase.log.utils.JacksonFiltersUtil;
import kl.npki.base.core.biz.log.model.OpLogInfo;
import kl.npki.base.core.constant.AuditStatusEnum;
import kl.npki.base.core.constant.LogTypeEnum;
import kl.npki.base.service.common.context.RequestContext;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.util.OperationLogUtil;
import org.apache.commons.lang3.ClassUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.instance.util.IpUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

import static kl.npki.base.service.constants.I18nConstant.PARSE_REQUEST_TO_JSON_ERROR_I18N_KEY;


/**
 * <AUTHOR>
 * @Date 2023/2/16 17:11
 */
public class OperationLogResolver implements ILogResolver {

    private static final String GET_METHOD = "GET";

    @Override
    public String getLogType() {
        return LogTypeEnum.OP_LOG.getLogType();
    }

    /**
     * 解析和处理日志，正常流
     *
     * @param joinPoint
     * @param returnValue
     * @param logCollector
     * @return 要记录的日志
     */
    @Override
    public String resolve(ProceedingJoinPoint joinPoint, Object returnValue, LogCollector logCollector) {
        RequestContext requestContext = RequestContextHolder.getContext();
        // 检查是否为GET请求且没有LogCollector注解，如果是则直接返回null
        if (isGetRequestWithoutLogCollector(requestContext, joinPoint)) {
            return null;
        }
        OpLogInfo opLogInfo = new OpLogInfo();
        // 填充请求
        populateRequest(joinPoint, opLogInfo, logCollector);
        populateOpLogInfo(opLogInfo, joinPoint);
        populateOriginData(opLogInfo, requestContext);
        opLogInfo.setServerIp(IpUtils.getIp());
        opLogInfo.setResult(Boolean.TRUE);
        String opLogJsonStr = JsonUtils.toJson(opLogInfo);
        return JsonUtils.toJson(LogResolveInfo.buildRequestResolverResult(LogTypeEnum.OP_LOG.getLogType(), opLogJsonStr));
    }

    /**
     * 解析和处理日志，异常流
     *
     * @param joinPoint
     * @param t
     * @param logCollector
     * @return 要记录的日志
     */
    @Override
    public String exceptionResolve(ProceedingJoinPoint joinPoint, Throwable t, LogCollector logCollector) {
        RequestContext requestContext = RequestContextHolder.getContext();
        OpLogInfo exceptionOpLogInfo = new OpLogInfo();
        // 填充请求
        populateRequest(joinPoint, exceptionOpLogInfo, logCollector);
        populateOpLogInfo(exceptionOpLogInfo, joinPoint);
        populateOriginData(exceptionOpLogInfo, requestContext);
        exceptionOpLogInfo.setServerIp(IpUtils.getIp());
        String logWhat = t.getMessage();
        exceptionOpLogInfo.setLogWhat(logWhat);
        exceptionOpLogInfo.setResult(Boolean.FALSE);
        String exceptionOpLogStr = JsonUtils.toJson(exceptionOpLogInfo);
        return JsonUtils.toJson(LogResolveInfo.buildRequestResolverResult(LogTypeEnum.OP_LOG.getLogType(), exceptionOpLogStr));
    }

    /**
     * 填充操作日志对应的字段
     *
     * @param opLogInfo
     */
    protected void populateOpLogInfo(OpLogInfo opLogInfo, ProceedingJoinPoint joinPoint) {
        // 结束时间
        long endTime = System.currentTimeMillis();
        RequestContext requestContext = RequestContextHolder.getContext();
        opLogInfo.setClientIp(requestContext.getIp());
        String bizName = LogResolverHelper.getBizName(joinPoint);
        opLogInfo.setLogDo(bizName);
        opLogInfo.setLogWhat(bizName);
        opLogInfo.setLogWhen(requestContext.getBackendStartDate());
        opLogInfo.setLogEnd(LocalDateTimeUtils.milliSecondParseLocalDateTime(endTime));
        opLogInfo.setElapsedTime((int) (endTime - requestContext.getBackendStart()));
        opLogInfo.setAuditStatus(AuditStatusEnum.PENDING);
        opLogInfo.setSecurityLog(Boolean.FALSE);
        populateOpLogFormContext(opLogInfo, requestContext);
    }

    /**
     * 填充请求内容
     *
     * @param proceedingJoinPoint 切入点
     * @param opLogInfo           需要填充的服务日志模型
     * @param logCollector        日志收集器
     */
    private void populateRequest(ProceedingJoinPoint proceedingJoinPoint, OpLogInfo opLogInfo, LogCollector logCollector) {
        try {
            // 请求内容
            Object[] requestArgs = proceedingJoinPoint.getArgs();
            if (requestArgs.length == 0) {
                opLogInfo.setRequest(null);
                return;
            }
            // 请求参数不为空的时候才进行赋值操作
            Object request = proceedingJoinPoint.getArgs()[0];
            String requestContent;
            if (request instanceof HttpServletRequest || request instanceof HttpServletResponse) {
                requestContent = StringUtils.EMPTY;
            } else if (request instanceof MultipartFile) {
                requestContent = ((MultipartFile) request).getOriginalFilename();
            } else {
                if (ObjectUtils.isNotEmpty(logCollector.maskFields())) {
                    requestContent = doMaskFields(proceedingJoinPoint, request, logCollector.maskFields());
                } else {
                    requestContent = JsonUtils.toJson(request);
                }
            }
            opLogInfo.setRequest(requestContent);
        } catch (Exception e) {
            opLogInfo.setRequest(I18nUtil.tr(PARSE_REQUEST_TO_JSON_ERROR_I18N_KEY) + ":" + e.getMessage());
        }
    }

    private static String doMaskFields(ProceedingJoinPoint proceedingJoinPoint, Object request, String[] maskFields) {
        String requestContent;
        // 如果包含敏感字段，则判断请求参数是否为原始类型或包装类型
        if (request instanceof String || ClassUtils.isPrimitiveOrWrapper(request.getClass())) {
            MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
            String[] parameterNames = signature.getParameterNames();
            List<String> parameterNamesList = Arrays.asList(parameterNames);

            boolean containsSensitiveField = Arrays.stream(maskFields)
                .anyMatch(parameterNamesList::contains);
            if (containsSensitiveField) {
                requestContent = StringUtils.EMPTY;
            } else {
                requestContent = request.toString();
            }
        } else {
            // 对非原始类型或包装类型的请求参数进行脱敏处理
            requestContent = JacksonFiltersUtil.transJsonFilter(request, maskFields);
        }
        return requestContent;
    }

    /**
     * 从请求中获取对应的信息填充操作日志
     *
     * @param opLogInfo
     * @param requestContext
     */
    private void populateOpLogFormContext(OpLogInfo opLogInfo, RequestContext requestContext) {
        opLogInfo.setTenantId(requestContext.getTenantId());
        opLogInfo.setSignData(requestContext.getSignData());
        opLogInfo.setSpanId(requestContext.getSpanId());
        opLogInfo.setTraceId(requestContext.getTraceId());
        opLogInfo.setLogWho(ObjectUtils.isEmpty(requestContext.getUserId()) ? StringUtils.EMPTY : String.valueOf(requestContext.getUserId()));
        opLogInfo.setUsername(requestContext.getUsername());

    }

    /**
     * 后端自己填充签名原文
     *
     * @param opLogInfo
     * @param requestContext
     */
    private void populateOriginData(OpLogInfo opLogInfo, RequestContext requestContext) {
        String originData = OperationLogUtil.buildSignOriginData(requestContext);
        opLogInfo.setOriginData(originData);
    }

    private boolean isGetRequestWithoutLogCollector(RequestContext requestContext, ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        return GET_METHOD.equalsIgnoreCase(requestContext.getRequestMethod()) && !signature.getMethod().isAnnotationPresent(LogCollector.class);
    }

}
