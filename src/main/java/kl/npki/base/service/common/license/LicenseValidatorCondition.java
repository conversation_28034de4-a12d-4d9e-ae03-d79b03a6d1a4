package kl.npki.base.service.common.license;

import kl.npki.base.core.biz.license.service.LicenseValidator;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * 判断Spring上下文中是否存在LicenseValidator的实现
 *
 * <AUTHOR>
 * @date 2023/8/24
 */
public class LicenseValidatorCondition implements Condition {

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        ConfigurableListableBeanFactory beanFactory = context.getBeanFactory();
        return beanFactory != null && beanFactory.getBeanNamesForType(LicenseValidator.class, false, false).length > 0;
    }
}