package kl.npki.base.service.common.db.impl;

import com.alibaba.druid.pool.DruidDataSource;
import kl.cloud.sql.util.SqlGanUtils;
import kl.nbase.db.support.slot.SlotDataSourceConfig;
import kl.npki.base.core.constant.EnvironmentEnum;
import kl.npki.base.core.tenantholders.ConfigHolder;
import kl.npki.base.core.utils.SystemUtil;
import kl.npki.base.service.common.db.ITableShardingService;
import kl.npki.base.service.sharding.impl.ApiLogShardingTableRefresherImpl;
import kl.npki.base.service.util.MonthShardingUtil;
import kl.tools.dbtool.task.bean.TableIndex;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.jdbc.ScriptRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.StringReader;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 月份分表服务
 *
 * <AUTHOR> Guiyu
 * @Date 2023/12/25
 */
@Service
public class MonthShardingServiceImpl implements ITableShardingService {

    private final Logger log = LoggerFactory.getLogger(MonthShardingServiceImpl.class);

    @Autowired
    private ApiLogShardingTableRefresherImpl apiLogShardingTableRefresher;

    @PostConstruct
    public void init() {
        createTables();
    }

    @Override
    public void createTables() {
        // demo环境不分表，如果default环境未配置数据源直接返回
        if (!SystemUtil.isDataSourceConfigured(EnvironmentEnum.DEFAULT.getId())) {
            return;
        }
        // 获取数据源配置
        SlotDataSourceConfig slotDataSourceConfig = ConfigHolder.get(EnvironmentEnum.DEFAULT.getId()).get(SlotDataSourceConfig.class);
        // 获取预创建月份分表数量
        int createNum = MonthShardingUtil.getCreateNum(slotDataSourceConfig);
        // 获取当前数据源类型
        String dbType = MonthShardingUtil.getDbType(slotDataSourceConfig);
        // 获取最早分表时间
        LocalDateTime earliestDate = MonthShardingUtil.getEarliestTableShardingDate();

        // 逻辑表名到对应DO类的映射，DO类用于生成创建逻辑表的SQL
        Map<String, Class<?>> logicTableToClassMap = MonthShardingUtil.getLogicTableToClassMap(slotDataSourceConfig);
        // 逻辑表名到分表列表的映射，分表列表用于生成创建分表的SQL
        Map<String, List<String>> logicTableToAllTableMap = new HashMap<>();
        for (String logicTableName : logicTableToClassMap.keySet()) {
            // 获取预期时间范围内的所有分表名称
            List<String> allTableName = MonthShardingUtil.getAllTableName(logicTableName, earliestDate, LocalDateTime.now().plusMonths(createNum));
            allTableName.add(logicTableName);
            logicTableToAllTableMap.put(logicTableName, allTableName);
        }
        // 获取主数据源，用于创建分表
        try (DruidDataSource dataSource = MonthShardingUtil.getMasterDataSource(slotDataSourceConfig)) {
            try (Connection connection = dataSource.getConnection()) {
                // 移除已创建的表
                removeExistingTable(logicTableToAllTableMap, logicTableToClassMap, connection);
                // 执行建表语句
                executeCreateTableSql(logicTableToAllTableMap, logicTableToClassMap, connection, dbType);
            } catch (SQLException e) {
                log.error("Table creation failed", e);
            }
        }

        if (MapUtils.isNotEmpty(logicTableToClassMap)) {
            // 没有新创建表，不用刷新
            return;
        }

        // 刷新API日志分表
        if (logicTableToAllTableMap.containsKey(ApiLogShardingTableRefresherImpl.TB_API_LOG_NAME)) {
            apiLogShardingTableRefresher.refreshShardingTable();
        }
    }

    /**
     * 移除logicTableToAllTableMap中已创建的表
     */
    private void removeExistingTable(Map<String, List<String>> logicTableToAllTableMap,
                                     Map<String, Class<?>> logicTableToClassMap, Connection connection) throws SQLException {
        for (String logicTable : logicTableToClassMap.keySet()) {
            // 已创建的分表集合
            Set<String> createdTableSet = getExistingTableFromDb(connection, logicTable);
            // 待去重的分表列表
            List<String> allTable = logicTableToAllTableMap.get(logicTable);
            // 遍历判断并移除已创建的分表
            // 建表时部分数据库可能会将表名统一转成大写或小写，此处判断也需要兼容大写小写2种情况。Locale.ROOT表示语言环境不敏感，避免大小写转换出现乱码。
            allTable.removeIf(tableName -> createdTableSet.contains(tableName.toLowerCase(Locale.ROOT)) || createdTableSet.contains(tableName.toUpperCase(Locale.ROOT)));
            // 逻辑表对应的所有分表均已创建，则从logicTableToAllTableMap移除
            if (CollectionUtils.isEmpty(allTable)) {
                logicTableToAllTableMap.remove(logicTable);
            }
        }
    }

    /**
     * 根据逻辑表名从数据库获取所有已创建的分表名称
     */
    private Set<String> getExistingTableFromDb(Connection connection, String logicTableName) throws SQLException {
        Set<String> tableNameSet = new HashSet<>();
        try (ResultSet resultSet = connection.getMetaData().getTables(connection.getCatalog(), connection.getSchema(), null, new String[]{"TABLE"})) {
            while (resultSet.next()) {
                tableNameSet.add(resultSet.getString("TABLE_NAME").toUpperCase(Locale.ROOT));
            }
        }

        // 使用逻辑表后置筛选，否则可能因为大小写敏感导致无法查到分表
        return tableNameSet.stream().filter(
            tableName -> tableName.startsWith(logicTableName.toUpperCase(Locale.ROOT))
        ).collect(Collectors.toSet());
    }

    /**
     * 执行建表语句
     */
    private void executeCreateTableSql(Map<String, List<String>> logicTableToAllTableMap,
                                       Map<String, Class<?>> logicTableToClassMap, Connection connection, String dbType) {
        // 创建成功的表
        List<String> successful = new ArrayList<>();
        // 创建失败的表
        List<String> unsuccessful = new ArrayList<>();
        for (Map.Entry<String, List<String>> logicTableToAllTableEntry : logicTableToAllTableMap.entrySet()) {
            // 逻辑表
            String logicTable = logicTableToAllTableEntry.getKey();
            // 逻辑表对应的待创建的所有分表
            List<String> allTable = logicTableToAllTableEntry.getValue();
            // 生成创建逻辑表的SQL语句
            Optional<String> createLogicTableSql = getCreateLogicTableSql(logicTableToClassMap.get(logicTable), dbType);
            if (!createLogicTableSql.isPresent()) {
                continue;
            }
            // 读取索引信息
            List<TableIndex> tableIndices = MonthShardingUtil.parseTableIndex(logicTableToClassMap.get(logicTable));

            ScriptRunner scriptRunner = new ScriptRunner(connection);
            for (String toBeCreated : allTable) {
                // 生成创建实际分表的SQL语句
                String createActualTableSql = createLogicTableSql.get().replace(logicTable, toBeCreated);
                // 修改索引名称
                for (TableIndex tableIndex : tableIndices) {
                    String indexName = tableIndex.getName();
                    // 获取表名后缀，也就是月份信息
                    String monthInfo = StringUtils.remove(toBeCreated, logicTable);
                    createActualTableSql = createActualTableSql.replace(indexName, indexName.concat(monthInfo));
                }

                try {
                    // 执行建表语句
                    scriptRunner.runScript(new StringReader(createActualTableSql));
                    successful.add(toBeCreated);
                } catch (Exception e) {
                    log.error("{} Table creation failed. If the table has already been created, please ignore it", toBeCreated, e);
                    unsuccessful.add(toBeCreated);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(successful)) {
            log.info("{} Table created successfully", successful);
        }
        if (CollectionUtils.isNotEmpty(unsuccessful)) {
            log.info("{} Table creation failed. If the table has already been created, please ignore it", unsuccessful);
        }
    }

    /**
     * 获取创建逻辑表SQL语句
     */
    private Optional<String> getCreateLogicTableSql(Class<?> clazz, String dbType) {
        try {
            return SqlGanUtils.genSql(clazz, dbType);
        } catch (Exception e) {
            log.error("Failed to generate table building SQL statement based on entity class: {}, database type: {}", clazz, dbType, e);
        }
        return Optional.empty();
    }
}
