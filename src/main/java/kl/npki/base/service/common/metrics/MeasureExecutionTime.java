package kl.npki.base.service.common.metrics;

import io.opentelemetry.api.common.Attributes;
import kl.nbase.health.metrics.CustomMetricsManager;
import kl.nbase.health.metrics.base.MetricsInfo;
import kl.nbase.health.metrics.base.sync.LongSyncMetrics;
import kl.nbase.health.metrics.base.sync.SyncMetricsType;
import kl.nbase.health.utils.AttributesUtil;
import kl.npki.base.service.common.context.MsgContext;
import kl.npki.base.service.common.context.MsgContextHolder;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: guoq
 * @Date: 2023/12/20
 * @description: 手动记录接口耗时
 */
public class MeasureExecutionTime {

    private static final MeasureExecutionTime INSTANCE = new MeasureExecutionTime();
    private static final String DEFAULT_REQUEST_METRICS_NAME = "request_metrics";
    private static final Attributes successfulResultAttr = AttributesUtil.createStringAttributes("status", "successful");
    private static final Attributes failedResultAttr = AttributesUtil.createStringAttributes("status", "failed");
    private final LongSyncMetrics requestMetrics;

    private MeasureExecutionTime() {
        CustomMetricsManager instance = CustomMetricsManager.getInstance();
        // 获取指标，如果不存在则创建
        LongSyncMetrics longSyncMetrics = instance.getLongSyncMetrics(DEFAULT_REQUEST_METRICS_NAME);
        if (longSyncMetrics == null) {
            longSyncMetrics = instance.registerLongSyncMetrics(new MetricsInfo.Builder(DEFAULT_REQUEST_METRICS_NAME).description("method execution time").unit("ms").build(), SyncMetricsType.HISTOGRAM);
        }
        this.requestMetrics = longSyncMetrics;
    }

    public static MeasureExecutionTime getInstance() {
        return INSTANCE;
    }

    /**
     * 记录tcp 方法执行耗时
     */
    public void measure() {
        MsgContext context = MsgContextHolder.getContext();
        measure(context.getBiz(), context.getElapsedTime(), StringUtils.isEmpty(context.getErrorMessage()));
    }

    /**
     * 记录方法执行耗时
     * @param name 业务名称
     * @param elapsedTime 耗时
     * @param isSuccessful 服务是否成功
     */
    public void measure(String name, long elapsedTime, boolean isSuccessful) {
        Attributes resultAttr = successfulResultAttr;
        // 指标的标签
        if (!isSuccessful) {
            resultAttr = failedResultAttr;
        }
        Attributes nameAttr = AttributesUtil.createStringAttributes("type", name);
        // 记录指标
        requestMetrics.record(elapsedTime, AttributesUtil.mergeAttributes(nameAttr, resultAttr));

    }
}
