package kl.npki.base.service.common.log.store;

import kl.nbase.log.store.LogStoreAdapter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 日志 fileStore
 *
 * <AUTHOR>
 * @date 2023/4/14
 */
public class FileLogStoreService extends LogStoreAdapter {

    /**
     * 文件存储相关store
     */
    private static final String FILE_LOG_STORE = "fileLogStore";

    private static final Logger log = LoggerFactory.getLogger(FileLogStoreService.class);

    @Override
    public void doSave(String msg) {
        if (StringUtils.isEmpty(msg)) {
            return;
        }
        // 打印对应的日志信息
        log.info(msg);
    }

    @Override
    public String getStoreType() {
        return FILE_LOG_STORE;
    }
}
