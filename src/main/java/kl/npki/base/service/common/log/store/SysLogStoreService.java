package kl.npki.base.service.common.log.store;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import kl.nbase.helper.utils.JsonUtils;
import kl.npki.base.core.biz.log.model.ApiLogInfo;
import kl.npki.base.core.biz.log.model.OpLogInfo;
import kl.npki.base.core.configs.LogConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.LogTypeEnum;
import kl.npki.base.core.utils.SysLogSendUtil;
import kl.npki.base.service.common.log.resolver.LogResolveInfo;
import kl.npki.base.service.exception.BaseServiceInternalError;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * sysLog操作类
 *
 * <AUTHOR>
 * @since 2025/1/21 13:40
 */
public class SysLogStoreService extends SwitchableLogStore {

    private static final Logger log = LoggerFactory.getLogger(SysLogStoreService.class);


    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * sysLog转发相关store
     */
    private static final String SYS_LOG_STORE = "sysLogStore";

    /**
     * 安全操作日志
     */
    private static final String SECURITY_LOG = "securityLog";

    /**
     * 日志类型
     */
    private static final String LOG_TYPE = "logType";


    @Override
    public void doSave(String msg) {
        LogConfig logConfig = BaseConfigWrapper.getLogConfig();
        boolean sysLogEnable = Boolean.TRUE.toString().equalsIgnoreCase(logConfig.getSyslogEnable());
        if (StringUtils.isEmpty(msg) || !sysLogEnable) {
            return;
        }
        // 解析msg
        LogResolveInfo resolverResult = JsonUtils.from(msg, LogResolveInfo.class);
        // 判断属于哪种类型的日志
        String logType = resolverResult.getLogType();
        String resolverResultValue = resolverResult.getValue();
        if (ObjectUtils.isEmpty(resolverResultValue)) {
            return;
        }
        LogTypeEnum logTypeEnum = LogTypeEnum.getLogType(logType);
        switch (logTypeEnum) {
            case OP_LOG:
                OpLogInfo opLogInfo = JsonUtils.from(resolverResultValue, OpLogInfo.class);
                Boolean securityLog = opLogInfo.getSecurityLog();
                String logTypeResult = Boolean.TRUE.equals(securityLog) ? SECURITY_LOG : logType;
                // 因为签名原文中包含有些敏感数据无法排除，所以发送审计日志之前将签名原文排除
                opLogInfo.setOriginData(null);
                // 处理数据并发送
                dealDataAndSend(JsonUtils.toJson(opLogInfo), logTypeResult);
                break;
            case API_LOG:
                // 如果是性能测试模式，不转发日志
                if (BaseConfigWrapper.getSysConfig().isPerformanceTest()) {
                    return;
                }
                ApiLogInfo apiLogInfo = JsonUtils.from(resolverResultValue, ApiLogInfo.class);
                // 因为签名原文中包含有些敏感数据无法排除，所以发送审计日志之前将签名原文排除
                apiLogInfo.setSignData(null);
                // 处理数据并发送
                dealDataAndSend(JsonUtils.toJson(apiLogInfo), logType);
                break;
            default:
                throw BaseServiceInternalError.INVALID_LOG_TYPE.toException();
        }
    }

    /**
     * 处理日志内容，审计无法提取，解决如下问题
     * 1、数字或者布尔类型转String
     * 2、去除json字符串带反斜杠问题
     * 3、细化操作类型，便于日志提取
     *
     * @param jsonData      json数据
     * @param logTypeResult 日志类型结果
     */

    @SuppressWarnings({"deprecation", "unchecked"})
    private void dealDataAndSend(String jsonData, String logTypeResult) {
        Map<String, Object> dataMap = JsonUtils.from(jsonData, LinkedHashMap.class);
        if (MapUtils.isEmpty(dataMap)) {
            return;
        }
        LinkedHashMap<String, Object> newDataMap = new LinkedHashMap<>(dataMap.size() + 1);
        // 将新元素放在第一位, securityLog-安全操作日志，opLog-业务操作日志, apiLog-服务日志
        newDataMap.put(LOG_TYPE, logTypeResult);
        newDataMap.putAll(dataMap);
        String jsonStr = JsonUtils.toJson(newDataMap);
        JsonNode jsonNode = null;
        try {
            jsonNode = OBJECT_MAPPER.readTree(jsonStr);
            JsonNodeFactory nodeFactory = JsonNodeFactory.instance;
            JsonNode convertedNode = convertJsonNode(jsonNode, nodeFactory);
            // 将转换后的 JsonNode 重新序列化为 JSON 字符串
            String convertJson = OBJECT_MAPPER.writeValueAsString(convertedNode);
            String unescapeStr = StringEscapeUtils.unescapeJava(convertJson);
            // 然后使用正则表达式去除多余的反斜杠
            String resultStr = unescapeStr.replace("\\\\", "");
            SysLogSendUtil.syslogSend(resultStr);
        } catch (JsonProcessingException e) {
            log.error("syslogSend jsonParse error", e);
        }
    }

    private JsonNode convertJsonNode(JsonNode node, JsonNodeFactory nodeFactory) {
        if (node.isObject()) {
            ObjectNode objectNode = nodeFactory.objectNode();
            // 遍历对象的每个字段
            node.fields().forEachRemaining(entry -> {
                JsonNode value = entry.getValue();
                // 递归调用转换方法处理子节点
                objectNode.set(entry.getKey(), convertJsonNode(value, nodeFactory));
            });
            return objectNode;
        } else if (node.isArray()) {
            // 处理数组节点
            JsonNodeFactory arrayNodeFactory = JsonNodeFactory.instance;
            ArrayNode arrayNode = arrayNodeFactory.arrayNode();
            node.elements().forEachRemaining(element -> arrayNode.add(convertJsonNode(element, nodeFactory)));
            return arrayNode;
        } else if (node.isNumber()) {
            // 将数字类型转换为字符串
            return nodeFactory.textNode(node.asText());
        } else if (node.isBoolean()) {
            // 将布尔类型转换为字符串
            return nodeFactory.textNode(node.asText());
        } else {
            return node;
        }
    }

    @Override
    public String getStoreType() {
        return SYS_LOG_STORE;
    }

}

