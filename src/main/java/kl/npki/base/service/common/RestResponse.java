package kl.npki.base.service.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import kl.nbase.exception.BaseException;
import kl.npki.base.service.common.context.RequestContextHolder;
import kl.npki.base.service.util.BaseExceptionUtils;

/**
 * <AUTHOR>
 * Created on 2022/08/24 10:49
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RestResponse<T> {

    public static final String SUCCESS_CODE = "0";

    private String code;
    private String message;
    private String traceId;
    private long timestamp;
    private T data;

    public static <T> RestResponse<T> success() {
        RestResponse<T> response = new RestResponse<>();
        response.code = SUCCESS_CODE;
        response.traceId = RequestContextHolder.getContext().getTraceId();
        response.timestamp = System.currentTimeMillis();
        return response;
    }

    public static <T> RestResponse<T> success(T t) {
        RestResponse<T> response = new RestResponse<>();
        response.code = SUCCESS_CODE;
        response.traceId = RequestContextHolder.getContext().getTraceId();
        response.timestamp = System.currentTimeMillis();
        response.data = t;
        return response;
    }

    public static <T> RestResponse<T> fail(BaseException baseException) {
        RestResponse<T> response = new RestResponse<>();
        response.code = baseException.getCode();
        response.traceId = RequestContextHolder.getContext().getTraceId();
        response.timestamp = System.currentTimeMillis();
        response.message = BaseExceptionUtils.buildCombinedMessage(baseException);
        return response;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getTraceId() {
        return traceId;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public T getData() {
        return data;
    }
}
