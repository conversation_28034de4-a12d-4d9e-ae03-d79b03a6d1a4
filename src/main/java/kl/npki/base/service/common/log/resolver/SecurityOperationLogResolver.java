package kl.npki.base.service.common.log.resolver;

import kl.npki.base.core.biz.log.model.OpLogInfo;
import org.aspectj.lang.ProceedingJoinPoint;

/**
 * 安全操作日志解析器
 *
 * <AUTHOR>
 * @date 2024/6/6
 */
public class SecurityOperationLogResolver extends OperationLogResolver {

    @Override
    protected void populateOpLogInfo(OpLogInfo opLogInfo, ProceedingJoinPoint joinPoint) {
        super.populateOpLogInfo(opLogInfo, joinPoint);
        opLogInfo.setSecurityLog(Boolean.TRUE);
    }
}
