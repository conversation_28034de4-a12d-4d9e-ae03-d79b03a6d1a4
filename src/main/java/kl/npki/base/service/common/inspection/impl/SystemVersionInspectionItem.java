package kl.npki.base.service.common.inspection.impl;

import kl.npki.base.core.biz.inspection.IInspectionItem;
import kl.npki.base.core.biz.inspection.InspectionItemTypeEnum;
import kl.npki.base.core.biz.inspection.model.InspectionItemResult;
import kl.npki.base.core.configs.SysInfoConfig;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.service.constants.I18nConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import static kl.nbase.i18n.i18n.I18nUtil.tr;

/**
 * 系统版本号检查
 *
 * <AUTHOR>
 * @date 2025/5/21 19:22
 **/
@Component
public class SystemVersionInspectionItem implements IInspectionItem {

    @Override
    public InspectionItemResult execute() {

        SysInfoConfig sysInfoConfig = BaseConfigWrapper.getSysInfoConfig();

        String version = StringUtils.isBlank(sysInfoConfig.getVersion()) ? "-" : sysInfoConfig.getVersion();
        String kernelVersion = StringUtils.isBlank(sysInfoConfig.getKernelVersion()) ? "-" : sysInfoConfig.getKernelVersion();
        String customizedVersion = StringUtils.isBlank(sysInfoConfig.getCustomizedVersion()) ? "-" : sysInfoConfig.getCustomizedVersion();
        String versionMessage = tr(I18nConstant.SYSTEM_RELEASE_VERSION_I18N_KEY) + ": " + version + "; " +
            tr(I18nConstant.SYSTEM_KERNEL_VERSION_I18N_KEY) + ": " + kernelVersion + "; " +
            tr(I18nConstant.SYSTEM_CUSTOMIZED_VERSION_I18N_KEY) + ": " + customizedVersion;

        return InspectionItemResult.success(getName(), getInspectionItemType(), versionMessage, null);
    }

    @Override
    public String getName() {
        return tr(I18nConstant.SYSTEM_VERSION_INSPECTION_I18N_KEY);
    }

    @Override
    public InspectionItemTypeEnum getInspectionItemType() {
        return InspectionItemTypeEnum.INSPECTION_SYSTEM_RESOURCE;
    }
}
