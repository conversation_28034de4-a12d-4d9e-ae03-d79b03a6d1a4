package kl.npki.base.service.common.db;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import kl.nbase.db.support.id.UidGenerator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: guoq
 * @Date: 2023/12/28
 * @description: 自定义mybatis-plus 主键生成器
 */
@Component
public class UidIdentifierGenerator implements IdentifierGenerator {

    @Resource
    private UidGenerator uidGenerator;

    @Override
    public Number nextId(Object entity) {
        return uidGenerator.getUID();
    }

}

