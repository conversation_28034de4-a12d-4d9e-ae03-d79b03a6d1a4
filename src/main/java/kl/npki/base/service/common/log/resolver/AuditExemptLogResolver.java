package kl.npki.base.service.common.log.resolver;

import kl.npki.base.core.biz.log.model.OpLogInfo;
import kl.npki.base.core.constant.AuditStatusEnum;
import org.aspectj.lang.ProceedingJoinPoint;

/**
 * 审计豁免解析器，用于不需要审计但需要记录的操作日志
 *
 * <AUTHOR>
 * @date 2024/6/6
 */
public class AuditExemptLogResolver extends SecurityOperationLogResolver {

    @Override
    protected void populateOpLogInfo(OpLogInfo opLogInfo, ProceedingJoinPoint joinPoint) {
        super.populateOpLogInfo(opLogInfo, joinPoint);

        // 标记为无需审计
        opLogInfo.setAuditStatus(AuditStatusEnum.NO_AUDIT);
    }
}