/**
 * @projectName npki-base-management
 * @package kl.npki.base.service.constants
 * @className kl.npki.base.service.constants.I18nExceptionInfoConstant
 */
package kl.npki.base.service.constants;

import static kl.npki.base.service.constants.I18nConstant.BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX;

/**
 * <AUTHOR>
 * @date 2025/1/22 16:08
 */
public class I18nExceptionInfoConstant {

    /**
     * 未从配置中心读取系统配置国际化键
     */
    public static final String SYSTEM_CONFIGURATION_NOT_READ_FROM_CONFIGURATION_CENTER_I18N_KEY = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "system_configuration_not_read_from_configuration_center";
    /**
     * 未从系统配置中读取默认租户id国际化键
     */
    public static final String DEFAULT_TENANT_ID_NOT_READ_FROM_SYSTEM_CONFIGURATION_I18N_KEY = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "default_tenant_id_not_read_from_system_configuration";

    /**
     * 请重新登录
     */
    public static final String PLEASE_LOG_IN_AGAIN_I18N_KEY = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "please_log_in_again";

    /**
     * 超过license最大授权限制
     */
    public static final String EXCEEDS_LICENSE_LIMIT = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "exceeds_license_limit";

    /**
     * license更新异常
     */
    public static final String LICENSE_UPDATE_ERROR = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "license_update_error";

    /**
     * 缺失请求参数字段
     */
    public static final String MISSING_REQUEST_PARAM_FIELD_I18N_KEY = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "missing_request_param_field";


    /**
     * 缺失请求header参数字段
     */
    public static final String MISSING_REQUEST_HEADER_PARAM_FIELD_I18N_KEY = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "missing_request_header_param_field";
    /**
     * 缺失请求header参数字段
     */
    public static final String SIGNATURE_TIMESTAMP_FORMAT_ERROR_DESC_I18N_KEY = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "signature_timestamp_format_error_desc";

    /**
     * 请求参数类型转换异常
     */
    public static final String PARAM_CONVERT_ERROR_DESC_DESC_I18N_KEY = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "param_convert_error_desc";
}
