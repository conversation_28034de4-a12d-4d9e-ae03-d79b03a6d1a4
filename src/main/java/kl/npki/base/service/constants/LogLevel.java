package kl.npki.base.service.constants;

import ch.qos.logback.classic.Level;
import kl.nbase.i18n.i18n.EnumI18n;
import kl.npki.base.service.exception.BaseServiceValidationError;

import java.util.HashMap;
import java.util.Map;

import static kl.npki.base.service.constants.I18nConstant.BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX;

/**
 * 日志输出级别
 * <AUTHOR>
 * @date 2022/9/22 14:43
 */
public enum LogLevel implements EnumI18n {
    OFF("OFF", "关闭"),
    ERROR("ERROR", "错误"),
    WARN("WARN", "警告"),
    INFO("INFO", "一般"),
    DEBUG("DEBUG", "调试"),
    TRACE("TRACE", "跟踪"),
    ALL("ALL", "所有")
    ;

    private String level;

    private String desc;

    LogLevel(String level, String desc) {
        this.level = level;
        this.desc =desc;
    }

    public String getLevel() {
        return level;
    }

    public String getDesc() {
        return tr();
    }

    public static Level getLogLevel(String level) {
        LogLevel logLevel = null;
        for (LogLevel value : LogLevel.values()) {
            if (level.equals(value.getLevel())) {
                logLevel = value;
                break;
            }
        }
        if (null == logLevel) {
            throw BaseServiceValidationError.NO_SUCH_LOG_LEVEL.toException();
        }
        switch (logLevel) {
            case OFF:
                return Level.OFF;
            case ERROR:
                return Level.ERROR;
            case WARN:
                return Level.WARN;
            case INFO:
                return Level.INFO;
            case DEBUG:
                return Level.DEBUG;
            case TRACE:
                return Level.TRACE;
            case ALL:
                return Level.ALL;
            default:
                throw BaseServiceValidationError.NO_SUCH_LOG_LEVEL.toException();
        }
    }

    public static Map<String, String> getValuesMap() {
        Map<String, String> valueMap = new HashMap<>();
        for (LogLevel logLevel : LogLevel.values()) {
            valueMap.put(logLevel.getLevel(), logLevel.getDesc());
        }
        return valueMap;
    }

    @Override
    public String getMessageKey() {
        // 下划线前为国际化资源路径，后面为枚举对象的name
        return BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + this.name().toLowerCase();
    }
}
