package kl.npki.base.service.constants;

import kl.nbase.i18n.i18n.I18nUtil;

/**
 * <AUTHOR>
 * @date 2025/1/17 10:05
 */
public class I18nConstant {
    private I18nConstant() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 国际化资源路径
     */
    public static final String BASE_SERVICE_I18N_RESOURCE_PATH = "kl.npki.base.service.i18n";

    /**
     * 国际化资源路径前缀
     */
    public static final String BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX = BASE_SERVICE_I18N_RESOURCE_PATH + I18nUtil.SEPARATOR;

    // region kl.npki.base.service.common.log.resolver.ApiLogResolver
    public static final String PARSE_REQUEST_TO_JSON_ERROR_I18N_KEY = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "parse_request_to_json_error";
    public static final String PARSE_RESPONSE_TO_JSON_ERROR_I18N_KEY = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "parse_response_to_json_error";
    // endregion

    // region kl.npki.base.service.common.log.resolver.AsnApiLogResolver
    public static final String PARSE_REQUEST_TO_ASN_ERROR_I18N_KEY = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "parse_request_to_asn_error";
    public static final String PARSE_RESPONSE_TO_ASN_ERROR_I18N_KEY = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "parse_response_to_asn_error";
    // endregion

    // region kl.npki.base.service.common.inspection.impl.SystemVersionInspectionItem
    public static final String SYSTEM_RELEASE_VERSION_I18N_KEY = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "system_release_version";
    public static final String SYSTEM_VERSION_INSPECTION_I18N_KEY = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "system_version_inspection";
    public static final String SYSTEM_KERNEL_VERSION_I18N_KEY = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "system_kernel_version";
    public static final String SYSTEM_CUSTOMIZED_VERSION_I18N_KEY = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "system_customized_version";
    // end region

    public static final String SYSTEM_OPERATOR = BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + "system_operator";

    /**
     * 获取国际化后的信息
     *
     * @param key  国际化资源的键（不包含资源路径）
     * @param args 占位填充参数
     * @return 国际化结果
     */
    public static String getBaseServiceI18nMessage(String key, Object... args) {
        return I18nUtil.tr(BASE_SERVICE_I18N_MESSAGE_KEY_PREFIX + key, args);
    }
}
