package kl.npki.base.service.constants;

/**
 * <AUTHOR>
 * Created on 2022/08/23 15:48
 */
public class Constants {

    private Constants() {
    }

    public static final String TRACE_ID = "trace_id";

    public static final String START = "start";

    /**
     * TID
     */
    public static final String CALLER_ID = "caller_id";

    /**
     * CID
     */
    public static final String CID = "cid";

    /**
     * 扩展项1
     */
    public static final String EXT1 = "ext1";

    /**
     * 扩展项2
     */
    public static final String EXT2 = "ext2";

    /**
     * 扩展项3
     */
    public static final String EXT3 = "ext3";


    public static final String TENANT_ID = "tenantId";

    public static final String SPAN_ID = "spanId";

    public static final String USERNAME = "username";

    public static final String USER_ID = "userId";

    /**
     * 业务ID
     */
    public static final String BIZ_ID = "biz_id";

    /**
     * 未知业务ID
     */
    public static final String UN_KNOW_BIZ_ID = "unKnowBizId";

    /**
     * 客户端IP端口
     */
    public static final String CLIENT_IP_PORT = "client_ip_port";

    /**
     * 未知客户端IP端口
     */
    public static final String UN_KNOWN_CLIENT_IP_PORT = "unKnowClientIp";

    /**
     * 终端标识
     */
    public static final String TERM_RID = "termRid";

    /**
     * UID
     */
    public static final String UID = "uid";

    /**
     * 签名数据
     */
    public static final String SIGN_DATA = "signData";

    /**
     * 签名原文
     */
    public static final String ORIGIN_DATA = "originData";

    /**
     * 加解密请求体使用的iv
     */
    public static final String IV = "iv";

    /**
     * X-Forwarded-Prefix 是一个 HTTP 请求头，用于在反向代理服务器或负载均衡器前面传递请求路径的前缀信息
     */
    public static final String X_FORWARDED_PREFIX_HEADER = "X-Forwarded-Prefix";

    public static final String METRICS_UNIT = "long";

}
