package kl.npki.base.service.util;

import kl.npki.base.service.common.context.RequestContext;
import kl.npki.base.service.common.log.resolver.LogResolverHelper;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2025/5/15 11:11
 */
public class OperationLogUtil {
    private OperationLogUtil() {
    }

    /**
     * 根据请求信息构建操作签名原始数据
     *
     * @param requestContext 请求上下文
     * @return {@link String }
     */
    public static String buildSignOriginData(RequestContext requestContext) {
        String uri = requestContext.getRequestUrl();
        HttpServletRequest request = requestContext.getRequest();
        // 请求的param参数
        String requestParam = LogResolverHelper.getParameterMapAll(request);
        // 请求的body参数
        String requestBody = LogResolverHelper.getBodyString(request);
        StringBuilder signedOriginal = getStringBuilder(requestContext, requestParam, requestBody, uri);
        return signedOriginal.toString();
    }

    @NotNull
    private static StringBuilder getStringBuilder(RequestContext requestContext, String requestParam, String requestBody, String uri) {
        String startAt = Long.toString(requestContext.getStart());
        // StringBuilder默认初始容量为16，append过程中内部数组大小调整可能会过度使用堆内存空间（来自Fortify报告），因此此处直接计算出具体容量
        //签名原文格式： {url}{param}{body}{time}
        StringBuilder signedOriginal = new StringBuilder(16 + requestParam.length() +  requestBody.length()+ uri.length() + startAt.length());
        signedOriginal.append("{");
        signedOriginal.append(uri);
        signedOriginal.append("}{");
        signedOriginal.append(requestParam);
        signedOriginal.append("}{");
        signedOriginal.append(requestBody);
        signedOriginal.append("}{");
        signedOriginal.append(startAt);
        signedOriginal.append("}");
        return signedOriginal;
    }

}
