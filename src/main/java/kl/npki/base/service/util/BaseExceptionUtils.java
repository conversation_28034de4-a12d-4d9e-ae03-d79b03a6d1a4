package kl.npki.base.service.util;

import kl.nbase.exception.BaseException;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @since 2025/7/8 下午6:34
 */
public class BaseExceptionUtils {

    private BaseExceptionUtils() {
    }

    /**
     * 构建业务异常的完整信息，递归拼接各级业务描述，剔除非业务相关内容。
     *
     * @param ex 业务异常对象
     * @return 拼接后的业务异常信息字符串
     */
    public static String buildCombinedMessage(BaseException ex) {
        StringBuilder fullMessage = new StringBuilder();
        String desc = ex.getDesc();

        if (StringUtils.hasText(desc)) {
            fullMessage.append(desc);
        }

        String appendMessage = getAppendMessage(ex);
        if (StringUtils.hasText(appendMessage)) {
            if (fullMessage.length() > 0) {
                fullMessage.append(":");
            }
            fullMessage.append(appendMessage);
        }

        Throwable cause = ex.getCause();
        if (cause instanceof BaseException) {
            BaseException baseCause = (BaseException) cause;
            String nestedMessage = buildCombinedMessage(baseCause);
            if (StringUtils.hasText(nestedMessage)) {
                if (fullMessage.length() > 0) {
                    fullMessage.append("||");
                }
                fullMessage.append(nestedMessage);
            }
        }

        return fullMessage.toString();
    }


    private static String getAppendMessage(BaseException ex) {
        try {
            Field field = ReflectionUtils.findField(ex.getClass(), "appendMessage", String.class);
            if (field == null) {
                return "";
            }
            ReflectionUtils.makeAccessible(field);
            return (String) ReflectionUtils.getField(field, ex);
        } catch (Exception e) {
            return "";
        }
    }

}