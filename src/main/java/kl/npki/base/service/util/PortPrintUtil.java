package kl.npki.base.service.util;

import kl.nbase.netty.conf.NettyHttpServerConfig;
import kl.nbase.netty.conf.NettyTcpServerConfig;
import kl.npki.base.core.configs.ManagementSslConfig;
import kl.npki.base.core.configs.NpkiServerConfig;
import kl.npki.base.core.tenantholders.ConfigHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/11/30
 */
public class PortPrintUtil {

    private PortPrintUtil() {
    }

    /**
     * Text Reset
     */
    public static final String RESET = "\033[0m";

    public static final String GREEN = "\033[0;32m";

    public static final String DEFAULT_COLOR = GREEN;

    /**
     * logger
     **/
    private static final Logger logger = LoggerFactory.getLogger(PortPrintUtil.class);

    public static StringBuilder getPortOutPutString() {
        StringBuilder sb = new StringBuilder();
        sb.append("NGPKI Service started:\n");
        sb.append(getCommonPortOutPutString());
        return sb;
    }

    /**
     * 获取常用的端口的输出，后续有需要可以继续追加
     *
     * @return 输出的字符串
     */
    public static StringBuilder getCommonPortOutPutString() {
        StringBuilder sb = new StringBuilder();
        NettyHttpServerConfig nettyHttpServerConfig = ConfigHolder.get().get(NettyHttpServerConfig.class);
        if (Objects.nonNull(nettyHttpServerConfig) && nettyHttpServerConfig.isEnabled() && nettyHttpServerConfig.getPort() != 0) {
            if (nettyHttpServerConfig.isSslEnabled()) {
                appendPort(sb, "Service\t HTTPS:\t\t", nettyHttpServerConfig.getPort());
            } else {
                appendPort(sb, "Service\t HTTP:\t\t", nettyHttpServerConfig.getPort());
            }
        }

        NettyTcpServerConfig nettyTcpServerConfig = ConfigHolder.get().get(NettyTcpServerConfig.class);
        if (Objects.nonNull(nettyTcpServerConfig) && nettyTcpServerConfig.isEnabled() && nettyTcpServerConfig.getPort() != 0) {
            if (nettyTcpServerConfig.isSslEnabled()) {
                appendPort(sb, "Service\t TCP(SSL):\t", nettyTcpServerConfig.getPort());
            } else {
                appendPort(sb, "Service\t TCP:\t\t", nettyTcpServerConfig.getPort());
            }
        }

        NpkiServerConfig npkiServerConfig = ConfigHolder.get().get(NpkiServerConfig.class);
        ManagementSslConfig managementSslConfig = ConfigHolder.get().get(ManagementSslConfig.class);
        if (Objects.nonNull(npkiServerConfig) && Objects.nonNull(npkiServerConfig.getPort()) && npkiServerConfig.getPort() != 0
                && Objects.nonNull(managementSslConfig)) {
            if (Boolean.TRUE.equals(managementSslConfig.getEnabled())) {
                appendPort(sb, "Mgmt\t HTTPS:\t\t", npkiServerConfig.getPort());
            } else {
                appendPort(sb, "Mgmt\t HTTP:\t\t", npkiServerConfig.getPort());
            }
        }

        return sb;
    }

    /**
     * 追加端口信息
     *
     * @param sb         输出的字符串
     * @param serverName 服务名称
     * @param port       端口
     */
    public static void appendPort(StringBuilder sb, String serverName, int port) {
        sb.append(serverName).append(DEFAULT_COLOR).append("[").append(port).append("]").append(RESET).append("\n");
    }

    /**
     * 打印端口信息
     */
    public static void logPrint() {
        logger.info(getPortOutPutString().toString());
    }
}
