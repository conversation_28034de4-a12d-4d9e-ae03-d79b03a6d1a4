package kl.npki.base.service.util;

import ch.qos.logback.classic.AsyncAppender;
import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.rolling.RollingFileAppender;
import kl.npki.base.service.constants.LogLevel;
import kl.npki.base.service.constants.LogOutType;
import org.slf4j.LoggerFactory;

/**
 * @Author: guoq
 * @Date: 2024/3/4
 * @description: logback日志工具类
 */
public class LogbackHelper {
    private LogbackHelper(){
        throw new UnsupportedOperationException("LogbackHelper is a util class");
    }

    public static RollingFileAppender<ILoggingEvent> getRollingFileAppender() {
        Logger logger = getLogger();
        AsyncAppender asyncAppender = (AsyncAppender)logger.getAppender(LogOutType.ASYNC_ROLLING_FILE.getOutType());
        if(asyncAppender != null){
            return (RollingFileAppender<ILoggingEvent>)asyncAppender.getAppender(LogOutType.ROLLING_FILE.getOutType());
        }else {
            return (RollingFileAppender<ILoggingEvent>)logger.getAppender(LogOutType.ROLLING_FILE.getOutType());
        }
    }

    private static Logger getLogger() {
        // 获取logger上下文
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        // 获取logback root内容
        return loggerContext.getLogger("ROOT");
    }

    /**
     * 修改日志级别
     * @param level
     */
    public static void updateLogLevel(String level) {
        Level logLevel = LogLevel.getLogLevel(level);
        // 设置日志输出级别
        getLogger().setLevel(logLevel);
    }
}
