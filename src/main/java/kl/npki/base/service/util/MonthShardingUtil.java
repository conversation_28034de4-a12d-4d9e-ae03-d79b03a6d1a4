package kl.npki.base.service.util;

import com.alibaba.druid.pool.DruidDataSource;
import kl.cloud.sql.annotation.Index;
import kl.cloud.sql.annotation.KlDbTable;
import kl.nbase.db.core.DataSourceContext;
import kl.nbase.db.support.druid.DruidDataSourceBuilder;
import kl.nbase.db.support.druid.DruidDataSourceProperties;
import kl.nbase.db.support.druid.SlotDruidDataSourceProperties;
import kl.nbase.db.support.slot.SlotDataSourceConfig;
import kl.nbase.helper.utils.LocalDateTimeUtils;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.constant.DbConfigType;
import kl.tools.dbtool.task.bean.TableIndex;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 月份分表工具类
 *
 * <AUTHOR> Guiyu
 * @Date 2023/12/19
 */
public class MonthShardingUtil {

    private static final Logger log = LoggerFactory.getLogger(MonthShardingUtil.class);

    public static final String NUM_CONFIG_KEY = "month-sharding-num";

    public static final String CLASSES_CONFIG_KEY = "month-sharding-classes";

    public static final String CRON_CONFIG_KEY = "month-sharding-cron";

    /**
     * 分表名后缀格式
     */
    private static final String TABLE_SUFFIX_FORMAT = "_yyyy_MM";

    private MonthShardingUtil() {
    }

    /**
     * 根据时间获取分表名称
     */
    public static String getTableName(String logicTableName, LocalDateTime dateTime) {
        return logicTableName + getTableSuffix(dateTime);
    }

    /**
     * 获根据时间范围获取所有分表名称，Date入参顺序无要求
     */
    public static List<String> getAllTableName(String logicTableName, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        LocalDateTime lower = startDateTime;
        LocalDateTime upper = endDateTime;
        // 确保lower的时间不会晚于upper的
        if (endDateTime.isBefore(startDateTime)) {
            lower = endDateTime;
            upper = startDateTime;
        }

        List<String> allTableName = new ArrayList<>();
        // 按月从lower遍历到upper，解析对应月份分表后缀
        while (lower.isBefore(upper) || lower.isEqual(upper)) {
            allTableName.add(logicTableName + getTableSuffix(lower));
            // 置为下个月1号0时0分0秒
            lower = lower.plusMonths(1);
            lower = LocalDateTime.of(lower.getYear(), lower.getMonth(), 1, 0, 0, 0, 0);
        }
        return allTableName;
    }

    /**
     * 获取分表后缀，后缀格式为：_yyyy_MM，例如时间是2000年1月，则后缀为：_2000_01
     */
    public static String getTableSuffix(LocalDateTime dateTime) {
        return dateTime.format(DateTimeFormatter.ofPattern(TABLE_SUFFIX_FORMAT));
    }

    /**
     * 获取最早的分表时间，将部署时间作为最早分表的时间，如果为空则用当前时间作为最早分表时间
     */
    public static LocalDateTime getEarliestTableShardingDate() {
        Date deploymentDate = BaseConfigWrapper.getSysInfoConfig().getDeploymentTime();
        return Objects.nonNull(deploymentDate) ? LocalDateTimeUtils.parseLocalDateTime(deploymentDate) :
                LocalDateTime.now();
    }

    /**
     * 获取主数据源，用于创建分表
     */
    public static DruidDataSource getMasterDataSource(SlotDataSourceConfig slotDataSourceConfig) {
        DruidDataSource druidDataSource;
        SlotDruidDataSourceProperties druid = new SlotDruidDataSourceProperties();
        druid.setMinIdle(1);
        druid.setMaxActive(1);
        druid.setInitialSize(1);
        // druid复用 避免JdbcConstants常量里定义的className与配置文件中的不一致
        SlotDruidDataSourceProperties oldDruid = slotDataSourceConfig.getDruid();
        if (StringUtils.isNotBlank(oldDruid.getDriverClassName())) {
            druid.setDriverClassName(oldDruid.getDriverClassName());
            druid.setValidationQuery(oldDruid.getValidationQuery());
        }
        List<DruidDataSourceProperties> dataSourceProperties = slotDataSourceConfig.getDatasource();
        DruidDataSourceProperties druidDataSourceProperties = dataSourceProperties.get(DataSourceContext.getMasterIndex(slotDataSourceConfig.getSlotName()));
        // 使用UUID作为数据源名称，避免与已有的数据源冲突
        druidDataSource = DruidDataSourceBuilder.getDruidDataSource(druidDataSourceProperties, druid, UUID.randomUUID().toString());
        return druidDataSource;
    }

    /**
     * 获取数据库类型
     */
    public static String getDbType(SlotDataSourceConfig slotDataSourceConfig) {
        String driverClassName = slotDataSourceConfig.getDruid().getDriverClassName();
        return DbConfigType.getDbConfigTypeByDriver(driverClassName).getDbTypeName().toUpperCase(Locale.ROOT);
    }

    /**
     * 从配置中获取预创建月份分表的数量
     */
    public static int getCreateNum(SlotDataSourceConfig slotDataSourceConfig) {
        Properties shardingProperties = slotDataSourceConfig.getSharding().getProps();
        return Integer.parseInt(shardingProperties.get(MonthShardingUtil.NUM_CONFIG_KEY).toString());
    }

    /**
     * 获取逻辑表到其对应DO类Class对象的映射，先从配置中获取需要创建月份分表的DO类全限定名称，再从DO类的注解中解析可得到对应表名
     */
    public static Map<String, Class<?>> getLogicTableToClassMap(SlotDataSourceConfig slotDataSourceConfig) {
        Properties shardingProperties = slotDataSourceConfig.getSharding().getProps();
        // DO类全限定名称，允许配置多个
        String classes = shardingProperties.get(MonthShardingUtil.CLASSES_CONFIG_KEY).toString();
        // 多个类则使用英文逗号隔开
        String separator = ",";

        String[] classNameArr;
        if (classes.contains(separator)) {
            classNameArr = classes.split(separator);
        } else {
            classNameArr = new String[]{classes};
        }

        Map<String, Class<?>> logicTableToClassMap = new HashMap<>();
        for (String className : classNameArr) {
            try {
                Class<?> claszz = Class.forName(className.trim());
                // 从注解中获取tableName
                KlDbTable klDbTable = claszz.getAnnotation(KlDbTable.class);
                String tableName = klDbTable.tbName();
                logicTableToClassMap.put(tableName, claszz);
            } catch (ClassNotFoundException e) {
                log.error("Failed to retrieve table name based on entity class ({})", className, e);
            }
        }
        return logicTableToClassMap;
    }

    public static List<TableIndex> parseTableIndex(Class entityClass) {
        KlDbTable annotation = (KlDbTable) entityClass.getAnnotation(KlDbTable.class);

        Index[] indexes = annotation.indexes();
        if (indexes.length == 0) {
            return Collections.emptyList();
        }
        List<TableIndex> tableIndexList = new ArrayList<>(indexes.length);
        for (Index index : indexes) {
            TableIndex tableIndex = new TableIndex(index.name(), index.columnList(), index.unique());
            tableIndexList.add(tableIndex);
        }
        return tableIndexList;
    }

}
