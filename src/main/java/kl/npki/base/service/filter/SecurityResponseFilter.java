package kl.npki.base.service.filter;

import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.service.common.context.RequestContextHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;

/**
 * 设置安全响应头的过滤器，参考：https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/HTTP_Headers_Cheat_Sheet.md
 *
 * <AUTHOR> <PERSON>yu
 * @Date 2023/11/17
 */
@Component
public class SecurityResponseFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        setSecurityResponseHeader((HttpServletRequest) request, (HttpServletResponse) response);
        chain.doFilter(request, response);
    }

    /**
     * 设置安全响应头
     */
    private void setSecurityResponseHeader(HttpServletRequest request, HttpServletResponse response) {
        /*
         * X-Frame-Options可以用来指示浏览器是否被允许在<frame>、<iframe>、<embed>或<object>中呈现页面。网站可以利用这一点来避免点击劫持攻击，确保内容没有嵌入到其他网站。
         * X-Frame-Options标头只有在包含它的HTTP响应中有需要交互的东西(如链接，按钮)时才有用，如果HTTP响应是重定向或返回JSON数据的API，则X-Frame-Options不提供任何安全性。
         * SAMEORIGIN：表示该页面只可以在相同域名页面的<frame>中展示。
         */
        response.addHeader("X-Frame-Options", "SAMEORIGIN");

        /*
         * Access-Control-Allow-Origin是一个CORS(跨域资源共享)头，指示与之相关的响应是否可以与来自给定源的请求代码共享。如果不使用此标头，则默认受到SOP(同源策略)的保护。
         * Access-Control-Allow-Origin: http://mysite.com，表示只允许http://mysite.com主机访问该资源。如果确定要使用该标头，应当设置特定值，则不是设置为*。
         */
        response.addHeader("Access-Control-Allow-Origin", request.getRemoteAddr());

        /*
         * X-XSS-Protection响应头是IE、Chrome和Safari的一个功能，当它们检测到反射的跨站点脚本(XSS)攻击时，会阻止页面加载。
         * 1; mode=block：表示启用XSS过滤，如果检测到攻击，浏览器将阻止呈现页面，而不是清除页面。
         */
        response.addHeader("X-XSS-Protection", "1; mode=block");

        /*
         * X-Content-Type-Options响应头来指示浏览器应该遵循Content-Type中设定的MIME类型，而不能对其进行修改。用于阻止浏览器的MIME类型嗅探从而避免MIME混淆攻击。
         * nosniff：表示启用该规则
         */
        response.addHeader("X-Content-Type-Options", "nosniff");

        /*
         * Strict-Transport-Security响应头通知浏览器只能使用HTTPS访问该站点，并且将来在指定的时间内任何使用HTTP访问该站点的尝试都应自动转换为HTTPS。
         * 如果站点仅能通过HTTP访问，则浏览器会忽略Strict-Transport-Security头。
         * max-age=63072000; includeSubDomains; preload：表示63072000秒(即2年)有效，该规则适用于子域名，preload让浏览器加入HSTS预加载列表
         */
        response.addHeader("Strict-Transport-Security", "max-age=63072000; includeSubDomains; preload");

        /*
         * 内容安全策略（CSP）是一个附加的安全层，用于指定允许在网站或 Web 应用中加载的内容来源，CSP策略只能用于 HTML 页面。
         * 有助于检测和缓解某些类型的攻击，包括跨站脚本（XSS）和数据注入攻击。
         * 策略由一系列指令组成，每条指令定义某类资源的加载来源与作用范围，以下是当前配置：
         *
         * default-src 'self'：
         *   所有类型资源的默认加载策略，若某类资源未单独定义策略，则使用该项；
         *   'self' 表示仅允许来自相同协议、域名和端口的内容。
         *
         * object-src 'none'：
         *   针对 <object>、<embed>、<applet> 等标签加载的资源，通常用于阻止 Flash 等插件内容；
         *   'none' 表示禁止加载任何该类对象资源。
         *
         * base-uri 'self'：
         *   限制 <base> 标签的可设置来源，仅允许当前页面自身；
         *   可防止攻击者修改基础 URL，进而影响相对链接跳转行为。
         *
         * style-src 'self' 'nonce-...'：
         *   限制页面中样式资源（CSS）的加载来源，仅允许以下两类：
         *   来自与页面同源（协议、域名、端口相同）的外部样式文件（通过 <link href="..."> 引入）
         *   通过 <style>标签定义的样式，但必须带有匹配的 nonce值
         *
         * img-src 'self' data:：
         *   允许加载来自当前站点的图片资源，以及以 data URI 格式嵌入的图片（如 base64）。
         *
         * frame-ancestors 'self'：
         *   只允许页面被自身嵌入 iframe 中，防止被其他页面嵌套（防点击劫持攻击）。
         *
         * script-src 'strict-dynamic' 'self' 'nonce-...'：
         *   仅允许同源脚本和带指定 nonce 的 <script> 标签执行；
         *   如果使用 'strict-dynamic'，则仅信任带 nonce 的脚本及其动态加载的脚本。
         *
         * connect-src 'self' https://127.0.0.1:16080：
         *   允许前端与本地 PKI 安全中间件建立连接；
         *   注意：此处地址不宜过多，否则 AppScan 可能提示“过多的回退机制”。
         */
        if (isHtmlOrRoot(request.getServletPath())) {
            // 仅对 html首页设置 CSP 策略
            // 因为 CSP 策略主要用于限制页面内资源加载，非 HTML 页面不需要设置
            String nonce = UUID.randomUUID().toString();
            RequestContextHolder.getContext().setNonce(nonce);
            String pkiMiddlewareUrls = BaseConfigWrapper.getWebConfig().getPkiMiddlewareUrls();

            // 设置CSP响应头，控制页面资源的加载策略
            String csp = "default-src 'self';" +
                    "object-src 'none';" +
                    "base-uri 'self';" +
                    "style-src 'self' 'nonce-" + nonce + "';" +
                    "img-src 'self' data:;" +
                    "frame-ancestors 'self';" +
                    "script-src 'strict-dynamic' 'nonce-" + nonce + "';" +
                    "connect-src 'self' " + pkiMiddlewareUrls;

            response.addHeader("Content-Security-Policy", csp);
        }
    }

    private boolean isHtmlOrRoot(String uri) {
        if (StringUtils.isEmpty(uri)) {
            return false;
        }

        // 首页
        return "/".equals(uri) || uri.endsWith(".html");
    }

}
