package kl.npki.base.service.filter;

import kl.npki.base.service.common.context.RequestContextHolder;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 设置安全响应头的过滤器，参考：https://github.com/OWASP/CheatSheetSeries/blob/master/cheatsheets/HTTP_Headers_Cheat_Sheet.md
 *
 * <AUTHOR>
 * @Date 2023/11/17
 */
@Component
public class SecurityResponseFilter implements Filter {

    /**
     * PKI安全中间件接口地址列表
     */
    private static final String PKI_MIDDLEWARE_URLS = generatePkiMiddlewareUrls();

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        setSecurityResponseHeader((HttpServletRequest) request, (HttpServletResponse) response);
        chain.doFilter(request, response);
    }

    /**
     * 设置安全响应头
     */
    private void setSecurityResponseHeader(HttpServletRequest request, HttpServletResponse response) {
        /*
         * X-Frame-Options可以用来指示浏览器是否被允许在<frame>、<iframe>、<embed>或<object>中呈现页面。网站可以利用这一点来避免点击劫持攻击，确保内容没有嵌入到其他网站。
         * X-Frame-Options标头只有在包含它的HTTP响应中有需要交互的东西(如链接，按钮)时才有用，如果HTTP响应是重定向或返回JSON数据的API，则X-Frame-Options不提供任何安全性。
         * SAMEORIGIN：表示该页面只可以在相同域名页面的<frame>中展示。
         */
        response.addHeader("X-Frame-Options", "SAMEORIGIN");

        /*
         * Access-Control-Allow-Origin是一个CORS(跨域资源共享)头，指示与之相关的响应是否可以与来自给定源的请求代码共享。如果不使用此标头，则默认受到SOP(同源策略)的保护。
         * Access-Control-Allow-Origin: http://mysite.com，表示只允许http://mysite.com主机访问该资源。如果确定要使用该标头，应当设置特定值，则不是设置为*。
         */
        response.addHeader("Access-Control-Allow-Origin", request.getRemoteAddr());

        /*
         * X-XSS-Protection响应头是IE、Chrome和Safari的一个功能，当它们检测到反射的跨站点脚本(XSS)攻击时，会阻止页面加载。
         * 1; mode=block：表示启用XSS过滤，如果检测到攻击，浏览器将阻止呈现页面，而不是清除页面。
         */
        response.addHeader("X-XSS-Protection", "1; mode=block");

        /*
         * X-Content-Type-Options响应头来指示浏览器应该遵循Content-Type中设定的MIME类型，而不能对其进行修改。用于阻止浏览器的MIME类型嗅探从而避免MIME混淆攻击。
         * nosniff：表示启用该规则
         */
        response.addHeader("X-Content-Type-Options", "nosniff");

        /*
         * Strict-Transport-Security响应头通知浏览器只能使用HTTPS访问该站点，并且将来在指定的时间内任何使用HTTP访问该站点的尝试都应自动转换为HTTPS。
         * 如果站点仅能通过HTTP访问，则浏览器会忽略Strict-Transport-Security头。
         * max-age=63072000; includeSubDomains; preload：表示63072000秒(即2年)有效，该规则适用于子域名，preload让浏览器加入HSTS预加载列表
         */
        response.addHeader("Strict-Transport-Security", "max-age=63072000; includeSubDomains; preload");

        /*
         * 内容安全策略(CSP)是一个附加的安全层，用于指定允许在网站或web应用程序中加载的内容的来源，有助于检测和减轻某些类型的攻击，包括跨站点脚本(XSS)和数据注入攻击。
         * 策略由一系列策略指令组成，每个策略指令都描述了一个针对某个特定类型资源以及生效范围的策略，如：
         * default-src 'self'; ：表示所有类型资源的默认加载策略，某类型资源如果没有单独定义策略，就使用默认的，self表示允许来自相同的协议、域名和端口的内容
         * object-src 'none'; ：表示针对的是<object>、<embed>、<applet> 等标签引入的 flash 等资源的加载策略，none表示不允许任何内容
         * script-src 'nonce-...' 'strict-dynamic';：只允许带指定 nonce 的 <script> 执行，同时允许这些脚本动态加载其他脚本
         * frame-ancestors 'self'：只允许自己嵌入自己（防止被 iframe 嵌套，如防点击劫持）
         * connect-src 'self' http://127.0.0.1:18080 允许访问PKI安全中间件地址
         * img-src 'self' data: 允许data图片加载
         */
        // 生成一个随机的nonce值，用于Content-Security-Policy
        String nonce = UUID.randomUUID().toString();
        RequestContextHolder.getContext().setNonce(nonce);
        response.addHeader("Content-Security-Policy",
                "default-src 'self'; " +
                        "object-src 'none'; " +
                        "script-src 'nonce-" + nonce + "' 'strict-dynamic'; " +
                        "frame-ancestors 'self';" +
                        "img-src 'self' data:;" +
                        "connect-src 'self' " + PKI_MIDDLEWARE_URLS);
    }

    /**
     * 生成固定的 PKI 中间件地址字符串
     * <li>http：11080~11089</li>
     * <li>https：16080~16089</li>
     *
     * 上面的端口逻辑是固定的不会变化
     *
     * @return 拼接好的地址字符串
     */
    public static String generatePkiMiddlewareUrls() {
        List<String> urls = new ArrayList<>();

        for (int port = 18080; port <= 18089; port++) {
            urls.add("http://127.0.0.1:" + port);
        }

        for (int port = 16080; port <= 16089; port++) {
            urls.add("http://127.0.0.1:" + port);
        }

        return String.join(" ", urls);
    }

}
