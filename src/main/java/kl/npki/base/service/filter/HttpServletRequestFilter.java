package kl.npki.base.service.filter;

import kl.nbase.helper.utils.CheckUtils;
import kl.nbase.helper.utils.JsonUtils;
import kl.nbase.security.entity.algo.BlockSymAlgo;
import kl.nbase.security.util.Arrays;
import kl.nbase.security.util.encoders.Hex;
import kl.npki.base.service.common.RestResponse;
import kl.npki.base.service.constants.Constants;
import kl.npki.base.service.exception.BaseServiceValidationError;
import kl.npki.base.core.configs.wrapper.BaseConfigWrapper;
import kl.npki.base.core.tenantholders.EngineHolder;
import kl.npki.base.core.utils.Base64Util;
import kl.npki.base.core.utils.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Locale;
import java.util.Objects;

import static org.springframework.http.HttpMethod.GET;

/**
 * 过滤器包装ServletRequest 使body可以重复获取,
 * 拦截器无法以代码方式进行 HttpServletRequest 对象替换。
 *
 * <AUTHOR>
 * @date 2023/3/22
 */
@Component
public class HttpServletRequestFilter implements Filter {

    private static final Logger log = LoggerFactory.getLogger(HttpServletRequestFilter.class);

    private static final String APPLICATION_JSON = "application/json";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String methodType = httpRequest.getMethod().toUpperCase(Locale.ROOT);
        RequestWrapper requestWrapper = null;
        // 遇到非get请求才对request进行包装
        if (!GET.toString().equals(methodType)) {
            requestWrapper = new RequestWrapper(httpRequest);
            // 判断是否需要解密请求体
            if (needToDecrypt(requestWrapper)) {
                try {
                    // 解密请求体
                    decryptRequestBody(requestWrapper);
                } catch (Exception e) {
                    log.error("Decryption request body failed: {}", new String(requestWrapper.getBody()), e);
                    // Filter抛出的异常无法被@ControllerAdvice进行统一处理，所以此处直接设置错误响应并返回
                    setErrorResponseBody((HttpServletResponse) response);
                    return;
                }
            }
        }
        if (null == requestWrapper) {
            chain.doFilter(request, response);
        } else {
            chain.doFilter(requestWrapper, response);
        }
    }

    /**
     * 根据配置及请求情况断是否需要进行解密操作，非application/json类型的请求（如：multipart/form-data）不用进行加解密
     */
    private boolean needToDecrypt(HttpServletRequest request) {
        return BaseConfigWrapper.getSysConfig().isEncryptRequestBody()
                && Objects.nonNull(request.getContentType())
                && request.getContentType().contains(APPLICATION_JSON);
    }

    /**
     * 解密请求体
     */
    private void decryptRequestBody(RequestWrapper requestWrapper) throws Exception {
        // 没有请求体则无需解密
        if (Arrays.isNullOrEmpty(requestWrapper.getBody())) {
            return;
        }

        // 获取加密数据，约定加密后的数据先转成base64字符串再传给后端
        byte[] encryptedBody = Base64Util.base64Decode(requestWrapper.getBody());

        // 使用当前用户username的SM3摘要作为key
        String username = requestWrapper.getHeader(Constants.USERNAME);
        CheckUtils.isTrue(StringUtils.isNotBlank(username), BaseServiceValidationError.USERNAME_FIELD_IN_HEADER_IS_EMPTY.toException());
        byte[] usernameSm3Digest = DigestUtils.digestBySm3(URLDecoder.decode(username, StandardCharsets.UTF_8.name()).getBytes(StandardCharsets.UTF_8));
        byte[] key = Arrays.copyOfRange(usernameSm3Digest, 0, usernameSm3Digest.length / 2);

        // 获取前端生成的iv，前端约定使用Hex编码iv
        String hexIv = requestWrapper.getHeader(Constants.IV);
        CheckUtils.isTrue(StringUtils.isNotBlank(hexIv), BaseServiceValidationError.IV_FIELD_IN_HEADER_IS_EMPTY.toException());
        byte[] iv = Hex.decode(hexIv);

        // 使用SM4/GCM/NoPadding算法解密
        byte[] decryptedBody = EngineHolder.get().symmetricDec(BlockSymAlgo.SM4_GCM_NOPADDING, key, iv, encryptedBody);
        requestWrapper.setBody(decryptedBody);
    }

    /**
     * 设置错误响应
     */
    private void setErrorResponseBody(HttpServletResponse response) {
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        response.setContentType(APPLICATION_JSON);
        RestResponse<Object> responseBody = RestResponse.fail(BaseServiceValidationError.IRREGULAR_REQUEST.toException());
        try {
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(JsonUtils.toJson(responseBody).getBytes());
            outputStream.flush();
        } catch (IOException e) {
            log.error("Setting error response exception", e);
        }
    }

}
