package kl.npki.base.service.filter;

import org.apache.commons.io.IOUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;

/**
 * 包装HttpRequest，使其能够重复获取body
 *
 * <AUTHOR>
 * @date 2023/3/22
 */
public class RequestWrapper extends HttpServletRequestWrapper {

    /**
     * 存放body内容
     */
    private byte[] body;

    /**
     * 标记body是否已加载过
     */
    private boolean isBodyLoaded = false;

    public RequestWrapper(HttpServletRequest request) {
        super(request);
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(this.getInputStream()));
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        loadBody();
        final ByteArrayInputStream bais = new ByteArrayInputStream(body);
        return new ServletInputStream() {

            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener listener) {
            }

            @Override
            public int read() {
                return bais.read();
            }
        };
    }

    private void loadBody() throws IOException {
        if (!isBodyLoaded) {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            IOUtils.copy(this.getRequest().getInputStream(), baos);
            setBody(baos.toByteArray());
            this.isBodyLoaded = true;
        }
    }

    public byte[] getBody() throws IOException {
        loadBody();
        return this.body;
    }

    public void setBody(byte[] body) {
        this.body = body;
    }

}