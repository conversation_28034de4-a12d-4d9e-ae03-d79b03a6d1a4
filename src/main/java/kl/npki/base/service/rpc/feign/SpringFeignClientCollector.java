package kl.npki.base.service.rpc.feign;

import kl.npki.base.core.biz.api.BaseApi;
import kl.npki.base.core.rpc.RpcClientCollector;
import kl.npki.base.core.rpc.RpcClientFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Spring FeignClient收集器
 *
 * <AUTHOR> Shi
 * @date 2024/12/18
 */
@Component
public class SpringFeignClientCollector implements RpcClientCollector, ApplicationContextAware, InitializingBean {

    private ApplicationContext appContext;

    @Override
    public Map<Class<?>, BaseApi> collect() {
        Map<String, BaseApi> clientMap = appContext.getBeansOfType(BaseApi.class);
        if (CollectionUtils.isEmpty(clientMap)) {
            return Collections.emptyMap();
        }

        Map<Class<?>, BaseApi> result = new HashMap<>();
        clientMap.values().forEach(client -> collectClientInterfaces(client, result));
        return result;
    }

    private void collectClientInterfaces(BaseApi client, Map<Class<?>, BaseApi> result) {
        Class<?> targetClass = AopUtils.getTargetClass(client);
        // 获取目标类的所有接口
        Class<?>[] targetInterfaces = targetClass.getInterfaces();

        if (!isFeignProxy(targetInterfaces)) {
            return;
        }

        for (Class<?> iface : targetInterfaces) {
            // FeignClient注解的接口,则直接放入结果集
            result.put(iface, client);
            // 找到实现了 BaseApi 的接口的父接口，也放入结果集
            collectBaseApiParentInterface(iface, client, result);
        }
    }

    private boolean isFeignProxy(Class<?>[] interfaces) {
        return Arrays.stream(interfaces)
            .anyMatch(iface -> iface.isAnnotationPresent(FeignClient.class));
    }

    private void collectBaseApiParentInterface(Class<?> iface, BaseApi client, Map<Class<?>, BaseApi> result) {
        // 找到实现了 BaseApi 的接口的父接口
        Class<?>[] parentInterfaces = iface.getInterfaces();
        for (Class<?> parentIface : parentInterfaces) {
            if (BaseApi.class.isAssignableFrom(parentIface)) {
                result.put(parentIface, client);
                break;
            }
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.appContext = applicationContext;
    }

    @Override
    public void afterPropertiesSet() {
        RpcClientFactory.init(this);
    }
}
