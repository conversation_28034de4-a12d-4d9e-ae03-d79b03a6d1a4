package kl.npki;

import kl.nbase.exception.context.ExceptionContext;
import kl.npki.base.core.constant.BaseConstant;
import kl.npki.base.management.exception.ErrorCode;
import kl.npki.base.service.util.PortPrintUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.ApplicationPidFileWriter;

/**
 * <AUTHOR>
 * Created on 2022/07/27 17:49
 */
@SpringBootApplication(scanBasePackages = BaseConstant.BASE_PACKAGE)
public class BaseManagementApplication {

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(BaseManagementApplication.class);
        app.addListeners(new ApplicationPidFileWriter());
        // base-management没有服务码，不会单独启动base-management
        // app.addInitializers(applicationContext -> ExceptionContext.getInstance().init(ErrorCode.SYSTEM_CODE, ErrorCode.SERVICE_CODE));
        app.run();

        PortPrintUtil.logPrint();
    }
}
